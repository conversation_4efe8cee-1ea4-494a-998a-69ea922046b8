🚀 The ISAAC Best Practices Guide

  1. Start Every Session with ISAAC Context

  # ALWAYS do this sequence when starting work:
  /mcp                    # Verify ISAAC is connected
  /isaac-start "Working on [specific task]"  # Start a development session
  /isaac-context          # Get current project state

  2. The Anti-Hallucination Workflow

  ISAAC's primary purpose is to prevent AI hallucinations by providing verified, real-time knowledge about your codebase. Follow
  this workflow:

  graph TD
      A[Start Coding Task] --> B[/isaac-context]
      B --> C{Need Function Info?}
      C -->|Yes| D[/isaac-functions]
      C -->|No| E{Need API Info?}
      E -->|Yes| F[/isaac-api]
      E -->|No| G[Write Code]
      G --> H[/isaac-validate]
      H -->|Missing| I[State: MISSING_DEPENDENCY]
      H -->|Exists| J[Continue Development]

  3. Key Commands by Use Case

  Starting New Feature Development

  1. /isaac-start "Building user authentication"
  2. /isaac-context                    # Understand current state
  3. /isaac-arch                       # Query architecture
  4. /isaac-functions                  # List available functions
  5. /isaac-task "Implement JWT auth"  # Create tracked task

  Debugging Issues

  1. /isaac-debug                      # Get recent commits & issues
  2. /isaac-context                    # Current system state
  3. /isaac-hotspots                   # Find frequently changed files
  4. /isaac-impact "component_name"    # Analyze dependencies

  Code Review/Refactoring

  1. /isaac-complexity                 # Find complex code
  2. /isaac-debt                       # Check technical debt
  3. /isaac-quality                    # Overall code quality
  4. /isaac-dependencies "module"      # Check what depends on this

  4. The ISAAC Development Loop

  ┌─────────────────────────────────────────────┐
  │  1. START SESSION with clear focus          │
  │     /isaac-start "Specific goal"            │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  2. GET CONTEXT before any work             │
  │     /isaac-context                          │
  │     /isaac-functions (if coding)            │
  │     /isaac-api (if API work)                │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  3. CREATE TASKS for tracking               │
  │     /isaac-task "Clear task description"    │
  │     /isaac-priority (to focus on important) │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  4. VALIDATE all code proposals             │
  │     /isaac-validate ["func1", "func2"]      │
  │     State "MISSING_DEPENDENCY" if needed    │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  5. UPDATE SESSION as you work              │
  │     /isaac-update "progress notes"          │
  │     /isaac-task-update (mark completed)     │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  6. END SESSION with summary                │
  │     /isaac-session-end                      │
  │     /isaac-stats (review work done)         │
  └─────────────────────────────────────────────┘

  5. Critical Anti-Hallucination Rules

  1. NEVER generate code without checking ISAAC first
    - Always run /isaac-functions before writing functions
    - Always run /isaac-api before creating endpoints
    - Always run /isaac-context before starting work
  2. ALWAYS validate before implementing
  /isaac-validate ["new_function", "helper_function", "api_endpoint"]
  3. EXPLICITLY state when something is missing
  MISSING_DEPENDENCY: EmailService - not found in ISAAC
  MISSING_DEPENDENCY: AuthMiddleware - needs to be created

  6. Power User Tips

  Batch Operations for Efficiency

  # Instead of multiple queries, use comprehensive ones:
  /isaac-context          # Gets session, tasks, and current work
  /isaac-stats           # Gets full system overview
  /isaac-priority        # Gets all important tasks at once

  Use ISAAC for Code Discovery

  /isaac-search "authentication"     # Find all auth-related code
  /isaac-similar "function_name"     # Find similar implementations
  /isaac-examples "pattern"          # Find usage examples

  Track Decision Making

  /isaac-decision "Switch to Redis for caching" "Performance reasons"
  /isaac-investigation "Bug in payment flow" "findings here"

  Feature Requests and Bug Reports

  /isaac-feature "Enhanced action cards" "Show appointment details in chat" "medium"
  /isaac-bug "Chat loses context" "Parameter collection fails randomly" "high"

  7. Common Workflows

  Morning Standup

  /isaac-standup         # What I did, what I'm doing, blockers
  /isaac-priority        # Focus for today
  /isaac-health         # System status check

  Before Creating PR

  /isaac-impact "my_changes"    # What will be affected
  /isaac-validate [...]         # Ensure all functions exist
  /isaac-quality               # Check code quality
  /isaac-session-end           # Close out the work session

  Learning New Codebase

  /isaac-ingest               # Import entire codebase
  /isaac-arch                 # Understand architecture
  /isaac-top-components       # Most important parts
  /isaac-flow "user_login"    # Trace specific flows

  8. Feature Requests and Bug Tracking

  ISAAC tracks features and bugs in the knowledge graph AND creates GitHub issues for team visibility:

  Feature Requests - Two-Step Process
  
  Step 1: Track in ISAAC
  /isaac-feature "title" "description" "priority"
  # Example:
  /isaac-feature "Dark mode support" "Add theme switching to all UIs" "low"

  # With more details:
  /isaac-feature-detailed
    title: "Enhanced action completion cards"
    description: "Show specific details like appointment times"
    priority: "medium"
    components: ["chat", "ui"]
    effort: "2-4 hours"

  Step 2: Create GitHub Issue
  # Use the GitHub MCP tool to create the issue
  # The tool will link it to the ISAAC entry automatically
  
  # For features:
  gh issue create \
    --title "Feature: Enhanced action completion cards" \
    --body "## Description\nShow specific details like appointment times in chat\n\n## Priority\nMedium\n\n## Estimated Effort\n2-4 hours\n\n## Components\n- chat\n- ui\n\n## ISAAC Reference\nTracked in knowledge graph as feature request" \
    --label "enhancement,chat,ui"

  Bug Reports - Two-Step Process
  
  Step 1: Track in ISAAC
  /isaac-bug "title" "description" "severity"
  # Example:
  /isaac-bug "Chat context loss" "Parameters lost between messages" "high"

  # With reproduction steps:
  /isaac-bug-detailed
    title: "Side effect form validation error"
    description: "Form submits with empty medication field"
    severity: "critical"
    steps: ["Open side effect form", "Leave medication blank", "Submit"]
    affected_users: "all patients"

  Step 2: Create GitHub Issue
  # For bugs:
  gh issue create \
    --title "Bug: Chat context loss during parameter collection" \
    --body "## Description\nParameters are lost between messages during multi-step actions\n\n## Severity\nHigh\n\n## Steps to Reproduce\n1. Start side effect report\n2. System asks for medication\n3. Provide medication name\n4. System forgets context and shows guidelines\n\n## Expected Behavior\nSystem should remember it was collecting parameters\n\n## Affected Users\nAll users\n\n## ISAAC Reference\nTracked in knowledge graph for context" \
    --label "bug,high-priority,chat"

  Best Practice: Always Do Both!
  1. First create the ISAAC entry for knowledge graph tracking
  2. Then create the GitHub issue for team visibility
  3. Link them by mentioning the ISAAC ID in the GitHub issue
  4. Update ISAAC with the GitHub issue number:
     /isaac-update-feature "feature-id" "github_issue: #123"

  Query Feature/Bug Status
  /isaac-features          # List all feature requests
  /isaac-bugs             # List all open bugs
  /isaac-features "chat"  # Features related to chat
  /isaac-bugs "critical"  # Critical severity bugs
  
  # Check GitHub issues status
  gh issue list --label "enhancement"
  gh issue list --label "bug"

  9. Best Practices Summary

  1. Start every session with /isaac-start and /isaac-context
  2. Create tasks for everything you work on
  3. Validate all code before implementing
  4. Update session progress regularly
  5. End sessions properly to maintain good data
  6. Use shortcuts instead of writing full Cypher queries
  7. Trust ISAAC data over assumptions
  8. State missing dependencies explicitly
  9. Track features and bugs for complete project history

  10. What NOT to Do

  ❌ Don't write code without checking ISAAC first
  ❌ Don't assume functions exist - validate them
  ❌ Don't skip session management - it provides continuity
  ❌ Don't ignore MISSING_DEPENDENCY warnings
  ❌ Don't create orphaned entities - link everything properly
  ❌ Don't track features/bugs in ISAAC only - always create GitHub issues too
  ❌ Don't create GitHub issues without ISAAC entries - you'll lose context

  By following these practices, ISAAC becomes your "second brain" that prevents mistakes, maintains context across sessions, and
  ensures your AI assistant never hallucinates about code that doesn't exist.