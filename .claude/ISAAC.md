# ISAAC: Intelligent System for AI Augmented Coding
## Complete System Guide & Reference

### Table of Contents
1. [System Overview](#system-overview)
2. [Quick Reference Card](#quick-reference-card)
3. [Core Architecture](#core-architecture)
4. [Essential Shortcuts](#essential-shortcuts)
5. [Development Workflows](#development-workflows)
6. [Codebase Ingestion](#codebase-ingestion)
7. [Session Management](#session-management)
8. [Task & Issue Management](#task--issue-management)
9. [Architecture Queries](#architecture-queries)
10. [AI Integration Patterns](#ai-integration-patterns)
11. [Advanced Query Library](#advanced-query-library)
12. [Claude Code Integration](#claude-code-integration)
13. [Troubleshooting & Maintenance](#troubleshooting--maintenance)

---

## System Overview

ISAAC (Intelligent System for AI Augmented Coding) is a Neo4j-powered knowledge graph that maintains persistent memory for AI-assisted development. It tracks code structure, development sessions, tasks, issues, and relationships to provide rich context for coding assistance tools.

### Key Capabilities
- **Persistent Memory**: Maintains context across development sessions
- **Code Intelligence**: Tracks functions, components, and architecture
- **Session Continuity**: Preserves development state and focus
- **Issue Integration**: Links GitHub issues to code and tasks
- **Decision Tracking**: Logs technical decisions with rationale
- **AI Context**: Provides rich context for AI coding assistants

### System Configuration
```json
"mcpServers": {
    "isaac": {
      "command": "/Users/<USER>/.local/bin/uvx",
      "args": ["mcp-neo4j-cypher@0.2.1"],
      "env": {
        "NEO4J_URI": "bolt://edinburgh:7687",
        "NEO4J_USERNAME": "neo4j",
        "NEO4J_PASSWORD": "YourStrongPasswordHere",
        "NEO4J_DATABASE": "neo4j"
      }
    }
```

---

## Quick Reference Card

### 🚀 Essential Commands

| Shortcut | Purpose | Usage |
|----------|---------|-------|
| `/isaac-start` | Initialize new session | Start development work |
| `/isaac-context` | Get current context | Before asking AI questions |
| `/isaac-task` | Create task with context | Create new development task |
| `/isaac-priority` | Get priority matrix | Focus on important work |
| `/isaac-arch` | Query architecture | Understand system structure |
| `/isaac-debt` | Check technical debt | Identify improvement areas |
| `/isaac-session-end` | Complete session | End development session |

### 📊 Status Checks

| Shortcut | Purpose | Usage |
|----------|---------|-------|
| `/isaac-health` | System health check | Monitor ISAAC status |
| `/isaac-stats` | Entity statistics | See data overview |
| `/isaac-validate` | Validate data integrity | Check data quality |
| `/isaac-orphans` | Find orphaned entities | Cleanup disconnected data |

### 🔧 Maintenance

| Shortcut | Purpose | Usage |
|----------|---------|-------|
| `/isaac-ingest` | Full codebase ingestion | Initial setup |
| `/isaac-update` | Incremental updates | Regular maintenance |
| `/isaac-relationships` | Auto-generate links | Fix missing connections |
| `/isaac-cleanup` | Clean orphaned data | Maintenance |

---

## Core Architecture

### Entity Types in ISAAC

```
📂 Project Management
├── Project - Main project container
├── Task - Development tasks
├── Epic - Large feature groups  
├── Milestone - Project milestones
└── GitHub_Issue - Linked issues

🏗️ Code Structure  
├── Entity - Generic entities
├── Function - Code functions
├── Component - UI/System components
├── Service - Business services
├── Database_Model - Data models
└── APIEndpoint - API routes

🧠 Development Intelligence
├── DevelopmentSession - Active sessions
├── Decision - Technical decisions  
├── TechnicalDebt - Code debt items
├── Testing_Category - Test suites
└── Configuration - Config files

👤 Collaboration
├── Developer - Team members
├── Commit - Git commits
├── BugFix - Bug resolutions  
└── Knowledge - Captured learnings
```

### Key Relationships

```
Project → CONTAINS → Entity
Task → MODIFIES_COMPONENT → Component  
Function → CALLS → Function
Component → USES → Service
GitHub_Issue → RESOLVED_BY → Task
DevelopmentSession → WORKING_ON → Entity
Developer → AUTHORED → Commit
```

---

## Essential Shortcuts

### Session Management

#### `/isaac-start` - Initialize Development Session
```cypher
CREATE (ds:DevelopmentSession {
  session_id: randomUUID(),
  status: 'active',
  start_time: datetime(),
  last_updated: datetime(),
  primary_focus: $focus,
  ai_conversation_active: true
})
CREATE (ss:Session_State {
  name: 'current_state',
  flow_state: 'initializing',
  current_context: $context
})
CREATE (ds)-[:HAS_STATE]->(ss)
RETURN ds.session_id, ds.primary_focus
```

#### `/isaac-context` - Get Current Development Context  
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
OPTIONAL MATCH (ds)-[:WORKING_ON]->(current)
OPTIONAL MATCH (ds)-[:HAS_TASK]->(tasks:Task {status: 'in_progress'})
OPTIONAL MATCH (current)-[r]-(related)
OPTIONAL MATCH (ds)-[:HAS_FOCUS_CONTEXT]->(fc:Focus_Context)
RETURN {
  session_focus: ds.primary_focus,
  current_work: current.name,
  active_tasks: collect(DISTINCT tasks.name),
  related_entities: collect(DISTINCT related.name),
  flow_state: fc.flow_state,
  energy_level: fc.energy_level
} as context
```

#### `/isaac-session-end` - Complete Development Session
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
SET ds.status = 'completed',
    ds.end_time = datetime()
CREATE (sc:SessionCompletion {
  id: randomUUID(),
  date: date(),
  focus: ds.primary_focus,
  duration_estimate: duration.between(ds.start_time, ds.end_time).minutes + " minutes"
})
CREATE (ds)-[:HAS_COMPLETION]->(sc)
RETURN "Session completed" as status, sc.duration_estimate
```

### Task Management

#### `/isaac-task` - Create Task with Full Context
```cypher
CREATE (t:Task {
  task_id: randomUUID(),
  name: $name,
  description: $description,
  priority: $priority,
  status: 'open',
  task_type: $type,
  created_date: datetime(),
  estimated_effort: $effort,
  business_value: $business_value,
  current_session_focus: true
})
WITH t
MATCH (ds:DevelopmentSession {status: 'active'})
CREATE (ds)-[:HAS_TASK]->(t)
RETURN t.task_id, t.name, t.priority
```

#### `/isaac-priority` - Get Priority Matrix
```cypher
MATCH (t:Task)
WHERE t.status IN ['open', 'in_progress']
WITH t,
  CASE 
    WHEN t.priority = 'critical' THEN 4
    WHEN t.priority = 'high' THEN 3
    WHEN t.priority = 'medium' THEN 2
    ELSE 1
  END as priority_weight,
  CASE
    WHEN t.business_value = 'high' THEN 3
    WHEN t.business_value = 'medium' THEN 2
    ELSE 1
  END as value_weight
ORDER BY (priority_weight + value_weight) DESC
RETURN t.name, t.priority, t.business_value, t.estimated_effort, 
       (priority_weight + value_weight) as total_score
LIMIT 10
```

### Architecture Analysis

#### `/isaac-arch` - Architecture Overview
```cypher
MATCH (ac:Architecture_Component)
OPTIONAL MATCH (ac)-[:USES|DEPENDS_ON|IMPLEMENTS]->(deps)
OPTIONAL MATCH (dependents)-[:USES|DEPENDS_ON|IMPLEMENTS]->(ac)
RETURN ac.name, ac.description, ac.status, 
       collect(DISTINCT deps.name) as dependencies,
       collect(DISTINCT dependents.name) as dependents
ORDER BY ac.name
```

#### `/isaac-debt` - Technical Debt Analysis
```cypher
MATCH (td:Technical_Debt)
WHERE td.severity IN ['high', 'critical']
OPTIONAL MATCH (td)-[:AFFECTS]->(component)
RETURN td.description, td.severity, td.effort_estimate,
       td.impact, collect(component.name) as affected_components
ORDER BY 
  CASE td.severity 
    WHEN 'critical' THEN 3
    WHEN 'high' THEN 2
    ELSE 1
  END DESC
```

### System Health

#### `/isaac-health` - System Health Check
```cypher
MATCH (n) 
WITH labels(n)[0] as entity_type, count(n) as count
RETURN entity_type, count
ORDER BY count DESC
UNION ALL
MATCH ()-[r]-()
WITH type(r) as relationship_type, count(r) as count
RETURN "REL: " + relationship_type as entity_type, count
ORDER BY count DESC
```

#### `/isaac-stats` - Comprehensive Statistics  
```cypher
MATCH (p:Project)
OPTIONAL MATCH (p)-[*1..2]-(entities)
WITH p, collect(DISTINCT labels(entities)[0]) as entity_types, count(DISTINCT entities) as total_entities
RETURN {
  project: p.name,
  total_entities: total_entities,
  entity_types: entity_types,
  last_update: p.last_major_update,
  status: p.status
} as project_stats
```

---

## Development Workflows

### Morning Standup Workflow

1. **Get Yesterday's Context**
```cypher
// /isaac-yesterday
MATCH (ds:DevelopmentSession)
WHERE date(ds.start_time) = date() - duration('P1D')
OPTIONAL MATCH (ds)-[:HAS_TASK]->(completed:Task {status: 'completed'})
RETURN ds.primary_focus, collect(completed.name) as completed_tasks
```

2. **Check Current Priorities**
```cypher
// /isaac-today-priorities  
MATCH (t:Task {status: 'in_progress'})
OPTIONAL MATCH (gi:GitHub_Issue {state: 'open'})-[:RESOLVED_BY]->(t)
RETURN t.name, t.priority, gi.number as github_issue
ORDER BY 
  CASE t.priority 
    WHEN 'critical' THEN 1
    WHEN 'high' THEN 2 
    ELSE 3 
  END
```

3. **Initialize Today's Session**
```cypher
// Use /isaac-start with today's focus
```

### Feature Development Workflow

1. **Analyze Requirements**
```cypher
// /isaac-feature-context
MATCH (f:Feature {name: $feature_name})
OPTIONAL MATCH (f)-[:PART_OF]->(epic:Epic)
OPTIONAL MATCH (f)-[:HAS_TASK]->(tasks:Task)
OPTIONAL MATCH (f)-[:IMPLEMENTED_BY]->(components)
RETURN f, epic, collect(tasks) as related_tasks, collect(components) as implementing_components
```

2. **Check Dependencies**
```cypher
// /isaac-dependencies
MATCH (component:Component {name: $component_name})
MATCH (component)-[:DEPENDS_ON*1..2]->(deps)
RETURN component.name, collect(DISTINCT deps.name) as all_dependencies
```

3. **Create Implementation Tasks**
```cypher
// Use /isaac-task for each implementation step
```

### Bug Investigation Workflow

1. **Analyze GitHub Issue**
```cypher
// /isaac-bug-context
MATCH (gi:GitHub_Issue {number: $issue_number})
OPTIONAL MATCH (gi)-[:AFFECTS_COMPONENT]->(affected)
OPTIONAL MATCH (gi)-[:RELATED_TO]->(related_issues)
RETURN gi, collect(affected) as affected_components, collect(related_issues) as related_issues
```

2. **Find Similar Past Issues**
```cypher
// /isaac-similar-bugs
MATCH (gi:GitHub_Issue {number: $issue_number})
MATCH (similar:GitHub_Issue)
WHERE similar <> gi 
  AND (similar.labels = gi.labels OR any(word IN split(gi.title, ' ') WHERE similar.title CONTAINS word))
OPTIONAL MATCH (similar)-[:RESOLVED_BY]->(resolution:Task)
RETURN similar.number, similar.title, similar.state, resolution.solution
LIMIT 5
```

3. **Track Investigation**
```cypher
// /isaac-investigation
CREATE (inv:Investigation_Notes {
  name: "Investigation: Issue #" + $issue_number,
  research_findings: $findings,
  patterns_discovered: $patterns,
  lessons_learned: $lessons
})
WITH inv
MATCH (ds:DevelopmentSession {status: 'active'})
CREATE (ds)-[:HAS_INVESTIGATION_NOTES]->(inv)
RETURN inv
```

---

## Codebase Ingestion

### Phase 1: Project Initialization

#### LLM Prompt: "Initialize ISAAC Project"
```
Analyze the current repository and create the base project structure in ISAAC:

1. Examine package.json, README.md, or similar files to understand the project
2. Execute this query with the discovered information:
```

```cypher
CREATE (project:Project {
  name: "DISCOVERED_PROJECT_NAME",
  description: "EXTRACTED_FROM_README_OR_PACKAGE_JSON",
  created_date: date(),
  last_major_update: datetime(),
  status: 'active',
  architecture: 'DETECTED_ARCHITECTURE_TYPE',
  primary_language: 'PRIMARY_LANGUAGE_DETECTED',
  entityType: 'Project'
})
RETURN project.name, project.description
```

### Phase 2: File Structure Analysis

#### LLM Prompt: "Catalog Source Files"
```
Examine all source files in the repository and create entities:

For each source file discovered, execute:
```

```cypher
MERGE (file:Entity {
  name: $file_name,
  entityType: 'CodeFile',
  file_path: $full_file_path,
  type: $file_extension,
  last_updated: datetime(),
  description: $inferred_file_purpose
})
WITH file
MATCH (project:Project)
CREATE (project)-[:CONTAINS]->(file)
RETURN file.name, file.entityType
```

### Phase 3: Code Structure Extraction

#### LLM Prompt: "Extract Functions and Components"
```
For each source file, analyze its contents and extract code structures:

1. Identify functions, classes, React components, API endpoints
2. For functions, execute:
```

```cypher
CREATE (func:Function {
  name: $function_name,
  file_path: $file_path,
  description: $function_purpose_or_docstring,
  parameters: $parameter_list,
  return_type: $return_type_if_available,
  visibility: $public_or_private,
  created_at: datetime(),
  purpose: $function_purpose
})
WITH func
MATCH (file:Entity {file_path: $file_path})
CREATE (file)-[:CONTAINS_FUNCTION]->(func)
RETURN func.name, func.file_path
```

#### LLM Prompt: "Identify Components and Services"
```
Analyze the codebase to identify architectural components:

For React components:
```

```cypher
CREATE (comp:Component {
  name: $component_name,
  type: 'React',
  file_path: $component_file_path,
  description: $component_purpose,
  features: $component_features,
  created_at: datetime()
})
WITH comp
MATCH (file:Entity {file_path: $component_file_path})
CREATE (file)-[:CONTAINS]->(comp)
RETURN comp.name, comp.type
```

```
For API endpoints:
```

```cypher
CREATE (api:APIEndpoint {
  route: $endpoint_path,
  method: $http_method,
  description: $endpoint_purpose,
  file_path: $file_location,
  authentication: $auth_requirements_if_any,
  created_at: datetime()
})
WITH api
MATCH (file:Entity {file_path: $file_location})
CREATE (file)-[:CONTAINS]->(api)
RETURN api.route, api.method
```

### Phase 4: Git History Integration

#### LLM Prompt: "Import Recent Git History"
```
Analyze recent git commits to understand development patterns:

For each recent commit, execute:
```

```cypher
CREATE (commit:Commit {
  hash: $commit_sha,
  message: $commit_message,
  date: datetime($commit_date),
  branch: $branch_name,
  files_changed: $files_modified_count,
  description: $commit_summary
})
WITH commit
MERGE (dev:Developer {
  name: $author_name,
  email: $author_email_if_available
})
CREATE (dev)-[:AUTHORED]->(commit)
RETURN commit.hash, commit.message
```

#### LLM Prompt: "Link Commits to Files"
```
For each commit, create relationships with modified files:
```

```cypher
MATCH (commit:Commit {hash: $commit_hash})
UNWIND $modified_file_paths as file_path
MATCH (file:Entity {file_path: file_path})
CREATE (commit)-[:MODIFIED_FILE]->(file)
SET file.last_updated = datetime($commit_date)
RETURN commit.hash, file.name
```

### Phase 5: Issues and Tasks Integration

#### LLM Prompt: "Import GitHub Issues"
```
Analyze GitHub issues and convert to ISAAC entities:

For each issue:
```

```cypher
CREATE (issue:GitHub_Issue {
  number: $issue_number,
  title: $issue_title,
  description: $issue_body,
  state: $issue_state,
  priority: $inferred_priority_based_on_labels,
  labels: $issue_labels,
  created_date: date($created_at),
  updated_date: date($updated_at),
  github_id: $issue_id,
  user_impact: $assessed_user_impact,
  business_impact: $assessed_business_impact,
  severity: $inferred_severity
})
RETURN issue.number, issue.title, issue.priority
```

#### LLM Prompt: "Convert Issues to Tasks"
```
Transform open GitHub issues into actionable tasks:
```

```cypher
MATCH (gi:GitHub_Issue {state: 'open'})
CREATE (task:Task {
  task_id: randomUUID(),
  name: gi.title,
  description: gi.description,
  priority: gi.priority,
  status: 'open',
  task_type: $inferred_task_type,
  created_date: gi.created_date,
  business_value: $assessed_business_value,
  estimated_effort: $estimated_effort_hours,
  github_issue: gi.number
})
CREATE (task)-[:RESOLVES_ISSUE]->(gi)
RETURN task.name, task.priority, gi.number
```

### Phase 6: Documentation Integration

#### LLM Prompt: "Process Documentation"
```
Find and process all documentation files:

For each documentation file:
```

```cypher
CREATE (doc:Entity {
  name: $doc_filename,
  entityType: 'Documentation',
  file_path: $doc_path,
  type: 'markdown',
  description: $doc_summary_from_content,
  key_sections: $major_section_headings,
  last_updated: datetime()
})
WITH doc
MATCH (project:Project)
CREATE (project)-[:HAS_DOCUMENTATION]->(doc)
RETURN doc.name, doc.description
```

### Phase 7: Relationship Building

#### LLM Prompt: "Establish Code Relationships"
```
Analyze code to identify relationships between components:

Execute these relationship-building queries:
```

```cypher
// Link functions that call other functions
MATCH (caller:Function), (called:Function)
WHERE caller.file_path <> called.file_path 
  AND $analysis_shows_caller_invokes_called
CREATE (caller)-[:CALLS]->(called)

// Link components that use services  
MATCH (comp:Component), (service:Service)
WHERE $component_imports_or_uses_service
CREATE (comp)-[:USES]->(service)

// Link API endpoints to implementing functions
MATCH (api:APIEndpoint), (func:Function)
WHERE api.file_path = func.file_path 
  AND $function_implements_endpoint
CREATE (api)-[:IMPLEMENTED_BY]->(func)

RETURN "Relationships created" as status
```

### Validation and Cleanup

#### `/isaac-validate` - Validate Ingestion
```cypher
MATCH (n) 
RETURN labels(n)[0] as entity_type, count(n) as count 
ORDER BY count DESC
UNION ALL
MATCH (project:Project)
OPTIONAL MATCH (project)-[*1..3]-(related)
RETURN "Connected to Project" as entity_type, count(DISTINCT related) as count
UNION ALL  
MATCH (n)
WHERE NOT (n)--()
RETURN "Orphaned: " + labels(n)[0] as entity_type, count(n) as count
```

#### `/isaac-relationships` - Auto-Generate Missing Links
```cypher
// Auto-link functions to files
MATCH (f:Function), (file:Entity {entityType: 'CodeFile'})
WHERE f.file_path = file.file_path AND NOT (file)-[:CONTAINS_FUNCTION]->(f)
CREATE (file)-[:CONTAINS_FUNCTION]->(f)

// Link tests to components based on naming
MATCH (t:Testing_Category), (c:Component)
WHERE t.name CONTAINS c.name AND NOT (t)-[:TESTS]->(c)
CREATE (t)-[:TESTS]->(c)

// Connect issues to tasks
MATCH (issue:GitHub_Issue), (task:Task)
WHERE task.github_issue = issue.number AND NOT (task)-[:RESOLVES_ISSUE]->(issue)
CREATE (task)-[:RESOLVES_ISSUE]->(issue)

RETURN "Auto-relationships created" as status
```

---

## Session Management

### Active Session Monitoring

#### `/isaac-session-status` - Current Session Status
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
OPTIONAL MATCH (ds)-[:HAS_STATE]->(ss:Session_State)
OPTIONAL MATCH (ds)-[:HAS_FOCUS_CONTEXT]->(fc:Focus_Context)
OPTIONAL MATCH (ds)-[:HAS_TASK]->(tasks:Task)
WHERE tasks.status = 'in_progress'
RETURN {
  session_id: ds.session_id,
  focus: ds.primary_focus,
  start_time: ds.start_time,
  flow_state: ss.flow_state,
  energy_level: fc.energy_level,
  active_tasks: count(tasks),
  duration: duration.between(ds.start_time, datetime()).minutes + " minutes"
} as session_status
```

#### `/isaac-session-progress` - Update Session Progress
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
SET ds.last_updated = datetime(),
    ds.recent_progress = $progress_description
WITH ds
OPTIONAL MATCH (ds)-[:HAS_STATE]->(ss:Session_State)
SET ss.flow_state = $current_flow_state,
    ss.current_context = $current_context
RETURN ds.recent_progress, ss.flow_state
```

### Session Analytics

#### `/isaac-session-history` - Recent Session History
```cypher
MATCH (ds:DevelopmentSession)
WHERE ds.start_time > datetime() - duration('P7D')
OPTIONAL MATCH (ds)-[:HAS_COMPLETION]->(sc:SessionCompletion)
RETURN ds.primary_focus, ds.start_time, ds.status, 
       sc.duration_estimate, sc.key_outcomes
ORDER BY ds.start_time DESC
LIMIT 10
```

#### `/isaac-productivity` - Productivity Analysis
```cypher
MATCH (ds:DevelopmentSession)
WHERE ds.start_time > datetime() - duration('P30D')
OPTIONAL MATCH (ds)-[:HAS_TASK]->(completed:Task {status: 'completed'})
WITH ds, count(completed) as tasks_completed
RETURN date(ds.start_time) as date, 
       ds.primary_focus as focus,
       tasks_completed,
       duration.between(ds.start_time, ds.end_time).hours as session_hours
ORDER BY date DESC
```

---

## Task & Issue Management

### Task Lifecycle Management

#### `/isaac-task-update` - Update Task Status
```cypher
MATCH (t:Task {task_id: $task_id})
SET t.status = $new_status,
    t.last_updated = datetime(),
    t.progress_notes = $progress_notes
RETURN t.name, t.status, t.progress_notes
```

#### `/isaac-task-complete` - Complete Task
```cypher
MATCH (t:Task {task_id: $task_id})
SET t.status = 'completed',
    t.completed_at = datetime(),
    t.completion_notes = $completion_notes,
    t.actual_effort = $actual_hours_spent
WITH t
OPTIONAL MATCH (t)-[:RESOLVES_ISSUE]->(gi:GitHub_Issue)
SET gi.resolution_status = 'resolved'
RETURN t.name, t.completion_notes, gi.number as resolved_issue
```

### Issue Analysis

#### `/isaac-issue-impact` - Analyze Issue Impact
```cypher
MATCH (gi:GitHub_Issue {number: $issue_number})
OPTIONAL MATCH (gi)-[:AFFECTS_COMPONENT]->(affected)
OPTIONAL MATCH path = (affected)-[*1..2]-(related)
WITH gi, affected, collect(DISTINCT related) as impact_chain
RETURN gi.title, gi.severity, gi.business_impact,
       affected.name as directly_affected,
       [item IN impact_chain | item.name] as impact_chain
```

#### `/isaac-blocking-issues` - Find Blocking Issues
```cypher
MATCH (t:Task {status: 'open'})
WHERE t.blockedBy IS NOT NULL
OPTIONAL MATCH (blocking:Task {task_id: t.blockedBy})
OPTIONAL MATCH (t)-[:RESOLVES_ISSUE]->(gi:GitHub_Issue)
RETURN t.name, t.priority, blocking.name as blocked_by, gi.number as github_issue
ORDER BY 
  CASE t.priority 
    WHEN 'critical' THEN 1
    WHEN 'high' THEN 2
    ELSE 3
  END
```

### Sprint Planning

#### `/isaac-sprint-ready` - Sprint-Ready Tasks
```cypher
MATCH (t:Task {status: 'open'})
WHERE t.estimated_effort IS NOT NULL
  AND t.business_value IS NOT NULL
  AND t.blockedBy IS NULL
WITH t,
  CASE t.priority 
    WHEN 'critical' THEN 4
    WHEN 'high' THEN 3
    WHEN 'medium' THEN 2
    ELSE 1
  END as priority_score,
  CASE t.business_value
    WHEN 'high' THEN 3
    WHEN 'medium' THEN 2
    ELSE 1
  END as value_score
RETURN t.name, t.estimated_effort, t.priority, t.business_value,
       (priority_score + value_score) as sprint_score
ORDER BY sprint_score DESC, t.estimated_effort ASC
LIMIT 15
```

---

## Architecture Queries

### Component Analysis

#### `/isaac-component-deps` - Component Dependencies  
```cypher
MATCH (c:Component {name: $component_name})
OPTIONAL MATCH (c)-[:DEPENDS_ON|USES]->(deps)
OPTIONAL MATCH (dependents)-[:DEPENDS_ON|USES]->(c)
OPTIONAL MATCH (c)-[:CONTAINS_FUNCTION]->(funcs:Function)
RETURN {
  component: c.name,
  type: c.type,
  dependencies: collect(DISTINCT deps.name),
  dependents: collect(DISTINCT dependents.name),
  functions: collect(DISTINCT funcs.name),
  last_updated: c.last_updated
} as component_info
```

#### `/isaac-component-health` - Component Health Assessment
```cypher
MATCH (c:Component)
OPTIONAL MATCH (c)<-[:TESTS]-(tests:Testing_Category)
OPTIONAL MATCH (c)<-[:AFFECTS]-(debt:Technical_Debt)
OPTIONAL MATCH (c)<-[:MODIFIES_COMPONENT]-(tasks:Task {status: 'open'})
WITH c, count(tests) as test_coverage, count(debt) as debt_items, count(tasks) as pending_changes
RETURN c.name, c.type, c.status,
       CASE 
         WHEN test_coverage > 0 THEN 'tested'
         ELSE 'needs_tests'
       END as test_status,
       debt_items as technical_debt_count,
       pending_changes as pending_work
ORDER BY debt_items DESC, pending_changes DESC
```

### System Architecture

#### `/isaac-system-map` - System Architecture Map
```cypher
MATCH (ac:Architecture_Component)
OPTIONAL MATCH (ac)-[:ORCHESTRATES|IMPLEMENTS|PROVIDES]->(services)
OPTIONAL MATCH (ac)-[:DEPENDS_ON]->(deps)
RETURN {
  component: ac.name,
  description: ac.description,
  type: labels(ac)[0],
  services_provided: collect(DISTINCT services.name),
  dependencies: collect(DISTINCT deps.name),
  status: ac.status
} as architecture_map
ORDER BY ac.name
```

#### `/isaac-data-flow` - Data Flow Analysis
```cypher
MATCH (api:APIEndpoint)-[:IMPLEMENTED_BY]->(func:Function)
OPTIONAL MATCH (func)-[:USES]->(model:Database_Model)
OPTIONAL MATCH (func)-[:CALLS]->(service_func:Function)
OPTIONAL MATCH (service_func)-[:USES]->(service_model:Database_Model)
RETURN {
  endpoint: api.route + " [" + api.method + "]",
  handler: func.name,
  direct_models: collect(DISTINCT model.name),
  service_calls: collect(DISTINCT service_func.name),
  service_models: collect(DISTINCT service_model.name)
} as data_flow
ORDER BY api.route
```

---

## AI Integration Patterns

### Context Preparation

#### `/isaac-ai-context` - Comprehensive AI Context
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
OPTIONAL MATCH (ds)-[:WORKING_ON]->(current)
OPTIONAL MATCH (ds)-[:HAS_TASK]->(active_tasks:Task)
WHERE active_tasks.status IN ['in_progress', 'open']
OPTIONAL MATCH (current)-[r]-(related)
OPTIONAL MATCH (ds)-[:HAS_AI_CONVERSATION]->(aic:AI_Conversation_Context)
OPTIONAL MATCH (recent_commits:Commit)
WHERE recent_commits.date > datetime() - duration('P1D')
RETURN {
  session: {
    focus: ds.primary_focus,
    progress: ds.recent_progress,
    start_time: ds.start_time
  },
  current_work: {
    entity: current.name,
    type: current.entityType,
    description: current.description
  },
  active_tasks: [task IN collect(DISTINCT active_tasks) | {
    name: task.name,
    priority: task.priority,
    estimated_effort: task.estimated_effort
  }],
  related_context: collect(DISTINCT related.name),
  recent_activity: collect(DISTINCT recent_commits.message),
  ai_context: aic.shared_understanding
} as comprehensive_context
```

#### `/isaac-decision-context` - Decision Support Context
```cypher
MATCH (ds:DevelopmentSession {status: 'active'})
OPTIONAL MATCH (recent_decisions:Decision)
WHERE recent_decisions.created_at > datetime() - duration('P7D')  
OPTIONAL MATCH (current_debt:Technical_Debt)
WHERE current_debt.severity IN ['high', 'critical']
OPTIONAL MATCH (ds)-[:HAS_DECISION_TRAIL]->(dt:Decision_Trail)
RETURN {
  recent_decisions: [dec IN collect(recent_decisions) | {
    decision: dec.decision,
    rationale: dec.rationale,
    date: dec.created_at
  }],
  critical_debt: [debt IN collect(current_debt) | {
    description: debt.description,
    impact: debt.impact,
    effort: debt.effort_estimate
  }],
  decision_trail: dt.decisions_made,
  trade_offs_considered: dt.trade_offs
} as decision_context
```

### Learning Integration

#### `/isaac-capture-insight` - Capture AI Insights
```cypher
CREATE (insight:Knowledge {
  id: randomUUID(),
  title: $insight_title,
  content: $insight_content,
  category: $insight_category,
  importance: $importance_level,
  learnedAt: datetime()
})
WITH insight
MATCH (ds:DevelopmentSession {status: 'active'})
CREATE (ds)-[:CAPTURED_KNOWLEDGE]->(insight)
RETURN insight.title, insight.category
```

#### `/isaac-similar-context` - Find Similar Past Context
```cypher
MATCH (current_session:DevelopmentSession {status: 'active'})
MATCH (past_session:DevelopmentSession)
WHERE past_session <> current_session
  AND (past_session.primary_focus CONTAINS current_session.primary_focus
       OR current_session.primary_focus CONTAINS past_session.primary_focus)
OPTIONAL MATCH (past_session)-[:CAPTURED_KNOWLEDGE]->(insights:Knowledge)
OPTIONAL MATCH (past_session)-[:HAS_COMPLETION]->(completion:SessionCompletion)
RETURN past_session.primary_focus, past_session.start_time,
       collect(insights.title) as relevant_insights,
       completion.key_outcomes as past_outcomes
ORDER BY past_session.start_time DESC
LIMIT 5
```

---

## Advanced Query Library

### Performance Analysis

#### `/isaac-hotspots` - Code Hotspots Analysis
```cypher
MATCH (file:Entity {entityType: 'CodeFile'})<-[:MODIFIED_FILE]-(commits:Commit)
WHERE commits.date > datetime() - duration('P30D')
WITH file, count(commits) as change_frequency
OPTIONAL MATCH (file)<-[:AFFECTS]-(debt:Technical_Debt)
OPTIONAL MATCH (file)-[:CONTAINS_FUNCTION]->(funcs:Function)
RETURN file.name, file.file_path, change_frequency,
       count(debt) as debt_items,
       count(funcs) as function_count,
       (change_frequency + count(debt)) as hotspot_score
ORDER BY hotspot_score DESC
LIMIT 10
```

#### `/isaac-code-complexity` - Code Complexity Assessment  
```cypher
MATCH (func:Function)
WITH func, size(func.parameters) as param_count,
     CASE 
       WHEN func.line_numbers IS NOT NULL 
       THEN toInteger(split(func.line_numbers, '-')[1]) - toInteger(split(func.line_numbers, '-')[0])
       ELSE 0
     END as estimated_lines
OPTIONAL MATCH (func)-[:CALLS]->(called_funcs:Function)
WITH func, param_count, estimated_lines, count(called_funcs) as calls_made,
     (param_count + (estimated_lines / 10) + count(called_funcs)) as complexity_score
WHERE complexity_score > 10
RETURN func.name, func.file_path, param_count, estimated_lines, calls_made, complexity_score
ORDER BY complexity_score DESC
LIMIT 15
```

### Dependency Analysis

#### `/isaac-circular-deps` - Find Circular Dependencies
```cypher
MATCH path = (a:Component)-[:DEPENDS_ON*2..4]->(a)
WHERE length(path) > 2
UNWIND nodes(path) as n
WITH collect(DISTINCT n.name) as cycle_components, length(path) as cycle_length
RETURN cycle_components, cycle_length
ORDER BY cycle_length DESC
```

#### `/isaac-dependency-impact` - Dependency Impact Analysis
```cypher
MATCH (component:Component {name: $component_name})
MATCH path = (dependents)-[:DEPENDS_ON*1..3]->(component)
WITH dependents, length(path) as dependency_depth
RETURN dependents.name, dependents.type, dependency_depth,
       CASE dependency_depth
         WHEN 1 THEN 'direct'
         WHEN 2 THEN 'secondary'
         ELSE 'tertiary'
       END as impact_level
ORDER BY dependency_depth, dependents.name
```

### Quality Metrics

#### `/isaac-test-coverage` - Test Coverage Analysis
```cypher
MATCH (c:Component)
OPTIONAL MATCH (c)<-[:TESTS]-(tests:Testing_Category)
OPTIONAL MATCH (c)-[:CONTAINS_FUNCTION]->(funcs:Function)
WITH c, count(tests) as test_suites, count(funcs) as function_count
RETURN c.name, c.type, test_suites, function_count,
       CASE 
         WHEN function_count = 0 THEN 'N/A'
         WHEN test_suites = 0 THEN 'No Tests'
         WHEN test_suites >= function_count THEN 'Good Coverage'
         ELSE 'Partial Coverage'
       END as coverage_status
ORDER BY 
  CASE coverage_status
    WHEN 'No Tests' THEN 1
    WHEN 'Partial Coverage' THEN 2
    WHEN 'Good Coverage' THEN 3
    ELSE 4
  END, c.name
```

#### `/isaac-quality-score` - Overall Quality Score
```cypher
MATCH (c:Component)
OPTIONAL MATCH (c)<-[:TESTS]-(tests)
OPTIONAL MATCH (c)<-[:AFFECTS]-(debt:Technical_Debt)
OPTIONAL MATCH (c)-[:CONTAINS_FUNCTION]->(funcs:Function)
WITH c, count(tests) as test_count, count(debt) as debt_count, count(funcs) as func_count
WITH c, test_count, debt_count, func_count,
  CASE 
    WHEN func_count = 0 THEN 0
    ELSE (test_count * 10) - (debt_count * 5) + (func_count * 2)
  END as quality_score
RETURN c.name, c.type, quality_score, test_count, debt_count, func_count
ORDER BY quality_score DESC
```

---

## Claude Code Integration

### Claude Code Shortcuts for ISAAC

When using Claude Code or other AI coding assistants, use these shortcuts to quickly access ISAAC context:

#### Development Session Commands

```bash
# Initialize ISAAC session for current work
@isaac session start "Feature: User Authentication"

# Get current development context  
@isaac context

# Update session progress
@isaac progress "Implemented login validation"

# Complete session
@isaac session end
```

#### Task Management Commands

```bash
# Create new task with context
@isaac task create "Fix login button styling" --priority=medium --effort=2h

# Get priority matrix for current work
@isaac priorities

# Update task status
@isaac task update TASK_ID --status=in_progress --notes="Started implementation"

# Complete task
@isaac task complete TASK_ID --notes="Fixed styling, added hover effects"
```

#### Architecture Analysis Commands

```bash
# Analyze component dependencies
@isaac deps ComponentName

# Check system architecture
@isaac arch overview

# Find technical debt
@isaac debt critical

# Analyze code hotspots
@isaac hotspots
```

#### AI Context Commands

```bash
# Get comprehensive context for AI assistance  
@isaac ai-context

# Find similar past work
@isaac similar-context "authentication"

# Capture insights from AI interaction
@isaac capture-insight "Password validation patterns" --category=security
```

### Integration with Development Tools

#### VS Code Integration
```json
// .vscode/settings.json
{
  "isaac.shortcuts": {
    "session.start": "Ctrl+Shift+I S",
    "context.get": "Ctrl+Shift+I C", 
    "task.create": "Ctrl+Shift+I T",
    "priorities.show": "Ctrl+Shift+I P"
  }
}
```

#### Terminal Aliases
```bash
# Add to ~/.bashrc or ~/.zshrc
alias is='isaac session'
alias ic='isaac context'  
alias it='isaac task'
alias ip='isaac priorities'
alias ia='isaac arch'
alias id='isaac debt'
```

### AI Prompt Templates

#### Code Review Context
```
Before reviewing this code, let me get the current ISAAC context:

[Use /isaac-context to get current session context]
[Use /isaac-component-deps for component being reviewed]
[Use /isaac-debt for related technical debt]

Based on this context, please review the following code...
```

#### Feature Implementation Context
```
I'm implementing a new feature. Let me gather the relevant context:

[Use /isaac-feature-context for feature requirements]
[Use /isaac-dependencies for component dependencies]
[Use /isaac-similar-context for similar past implementations]

Here's what I need to implement...
```

#### Bug Investigation Context
```
I'm investigating a bug. Let me get the relevant information:

[Use /isaac-bug-context for issue details]
[Use /isaac-similar-bugs for similar past issues]  
[Use /isaac-component-health for affected components]

The bug I'm investigating is...
```

---

## Troubleshooting & Maintenance

### System Health Monitoring

#### `/isaac-orphans` - Find Orphaned Entities
```cypher
MATCH (n)
WHERE NOT (n)--() AND NOT n:Project
RETURN labels(n)[0] as entity_type, collect(n.name) as orphaned_entities
```

#### `/isaac-missing-relationships` - Identify Missing Relationships
```cypher
// Functions not linked to files
MATCH (f:Function)
WHERE NOT (:Entity {entityType: 'CodeFile'})-[:CONTAINS_FUNCTION]->(f)
RETURN 'Function without file' as issue, f.name, f.file_path

UNION ALL

// Components without tests
MATCH (c:Component)
WHERE NOT (:Testing_Category)-[:TESTS]->(c)
RETURN 'Component without tests' as issue, c.name, c.type

UNION ALL

// Tasks without issues
MATCH (t:Task)
WHERE t.github_issue IS NOT NULL AND NOT (t)-[:RESOLVES_ISSUE]->(:GitHub_Issue)
RETURN 'Task without linked issue' as issue, t.name, t.github_issue
```

### Data Cleanup

#### `/isaac-cleanup` - Clean Orphaned Data
```cypher
// Remove orphaned entities (be careful with this!)
MATCH (n)
WHERE NOT (n)--() AND NOT n:Project AND NOT n:DevelopmentSession
WITH n LIMIT 100
DETACH DELETE n
RETURN count(n) as deleted_entities
```

#### `/isaac-deduplicate` - Remove Duplicate Entities
```cypher
// Find duplicate components by name and file_path
MATCH (c1:Component), (c2:Component)
WHERE c1.name = c2.name AND c1.file_path = c2.file_path AND id(c1) < id(c2)
WITH c2
DETACH DELETE c2
RETURN count(c2) as deduplicated_components
```

### Performance Optimization

#### `/isaac-index-suggestions` - Suggest Database Indexes
```cypher
// This is informational - actual index creation requires database admin
RETURN [
  "CREATE INDEX FOR (n:Entity) ON (n.name)",
  "CREATE INDEX FOR (n:Entity) ON (n.file_path)", 
  "CREATE INDEX FOR (t:Task) ON (t.status)",
  "CREATE INDEX FOR (ds:DevelopmentSession) ON (ds.status)",
  "CREATE INDEX FOR (gi:GitHub_Issue) ON (gi.number)",
  "CREATE INDEX FOR (c:Commit) ON (c.date)"
] as suggested_indexes
```

#### `/isaac-query-performance` - Analyze Query Performance
```cypher
// Check relationship density
MATCH (n)
WITH labels(n)[0] as node_type, count(n) as node_count
MATCH (n)--()
WITH node_type, node_count, count(*) as relationship_count
RETURN node_type, node_count, relationship_count, 
       (relationship_count * 1.0 / node_count) as avg_connections
ORDER BY avg_connections DESC
```

### Backup and Recovery

#### `/isaac-export-schema` - Export Schema Information
```cypher
CALL db.schema.visualization() YIELD nodes, relationships
RETURN nodes, relationships
```

#### `/isaac-backup-metadata` - Backup System Metadata
```cypher
MATCH (meta:Entity {entityType: 'SystemMetadata'})
RETURN meta
UNION ALL
MATCH (project:Project)
RETURN project
UNION ALL
MATCH (ds:DevelopmentSession {status: 'active'})
RETURN ds
```

### Error Recovery

#### `/isaac-recover-session` - Recover Incomplete Session
```cypher
// If session is stuck in 'active' state
MATCH (ds:DevelopmentSession {status: 'active'})
WHERE ds.start_time < datetime() - duration('P1D')
SET ds.status = 'interrupted',
    ds.end_time = datetime()
CREATE (recovery_note:Entity {
  name: 'Session Recovery: ' + ds.session_id,
  entityType: 'SystemNote',
  description: 'Session was automatically recovered due to extended duration',
  created_date: datetime()
})
RETURN ds.session_id, 'Session recovered' as status
```

#### `/isaac-validate-integrity` - Full Integrity Check
```cypher
// Check for broken relationships
MATCH (n)-[r]->(m)
WHERE n IS NULL OR m IS NULL
RETURN 'Broken relationship found' as issue, type(r) as relationship_type

UNION ALL

// Check for required properties
MATCH (t:Task)
WHERE t.name IS NULL OR t.status IS NULL
RETURN 'Task with missing required properties' as issue, t.task_id as entity_id

UNION ALL

// Check for valid statuses
MATCH (t:Task)
WHERE NOT t.status IN ['open', 'in_progress', 'completed', 'cancelled']
RETURN 'Task with invalid status' as issue, t.name + ': ' + t.status as details
```

---

## Usage Examples

### Daily Development Workflow

**Morning Startup:**
```bash
@isaac session start "Sprint 3: User Dashboard Features"
@isaac priorities
@isaac context
```

**Mid-Development:**
```bash
@isaac progress "Completed user profile component, starting dashboard layout"
@isaac task update TASK_ID --status=in_progress
```

**Before AI Assistance:**
```bash
@isaac ai-context
# Then ask AI: "Based on this context, help me implement the user dashboard component"
```

**End of Day:**
```bash
@isaac task complete TASK_ID --notes="Dashboard layout complete, responsive design implemented"
@isaac session end
```

### Feature Development Example

**Planning Phase:**
```bash
@isaac feature-context "User Authentication System"
@isaac deps AuthComponent
@isaac similar-context "authentication"
```

**Implementation Phase:**  
```bash
@isaac task create "Implement login form validation" --priority=high --effort=4h
@isaac arch overview
@isaac ai-context
```

**Review Phase:**
```bash
@isaac component-health AuthComponent
@isaac test-coverage
@isaac capture-insight "JWT token validation patterns" --category=security
```

### Bug Investigation Example

**Initial Analysis:**
```bash
@isaac bug-context 123
@isaac similar-bugs
@isaac component-health ComponentName
```

**Investigation:**
```bash
@isaac investigation "Root cause appears to be race condition in async validation"
@isaac hotspots
```

**Resolution:**
```bash
@isaac task create "Fix race condition in login validation" --priority=critical
@isaac task complete TASK_ID --notes="Added proper async/await handling"
```

This comprehensive guide provides everything needed to effectively use ISAAC for AI-augmented development. The system grows more valuable as it accumulates more data about your development patterns, decisions, and codebase evolution.