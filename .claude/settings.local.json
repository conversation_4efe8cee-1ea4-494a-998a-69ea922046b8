{"permissions": {"allow": ["Bash(grep:*)", "mcp__github-projects__create_draft_issue", "mcp__github-projects__update_project_item_field", "mcp__github-projects__get_project_fields", "mcp__github-projects__get_project_items", "<PERSON><PERSON>(touch:*)", "mcp__github-projects__list_projects", "mcp__obsidian__obsidian_list_files_in_dir", "mcp__obsidian__obsidian_get_file_contents", "mcp__memory__create_relations", "mcp__memory__read_graph", "mcp__memory__search_nodes", "mcp__memory__create_entities", "Bash(ls:*)", "mcp__fetch__fetch", "mcp__memory__add_observations", "mcp__mpi-memory__read_neo4j_cypher", "mcp__mpi-memory__write_neo4j_cypher", "mcp__mpi-memory__get_neo4j_schema", "Bash(docker-compose logs:*)", "Bash(timeout 10 yarn dev --port 5173)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(docker-compose exec:*)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(test:*)", "<PERSON><PERSON>(source:*)", "mcp__github-official__list_issues", "mcp__github-official__create_issue", "mcp__github-official__update_issue", "mcp__github-official__get_issue", "Bash(git add:*)", "Bash(git commit:*)", "mcp__github-official__search_repositories", "Bash(rg:*)", "<PERSON><PERSON>(gh issue view:*)", "mcp__isaac__read_neo4j_cypher", "mcp__isaac__write_neo4j_cypher", "mcp__isaac__get_neo4j_schema", "Bash(cp:*)", "mcp__github-official__add_issue_comment", "Bash(find:*)", "mcp__github-official__search_issues", "mcp__github-official__get_issue_comments", "<PERSON><PERSON>(echo:*)", "WebFetch(domain:context7.com)"], "deny": []}}