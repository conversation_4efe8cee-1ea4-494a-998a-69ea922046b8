🚀 The ISAAC Best Practices Guide

  1. Start Every Session with ISAAC Context

  # ALWAYS do this sequence when starting work:
  /mcp                    # Verify ISAAC is connected
  /isaac-start "Working on [specific task]"  # Start a development session
  /isaac-context          # Get current project state

  2. The Anti-Hallucination Workflow

  ISAAC's primary purpose is to prevent AI hallucinations by providing verified, real-time knowledge about your codebase. Follow
  this workflow:

  graph TD
      A[Start Coding Task] --> B[/isaac-context]
      B --> C{Need Function Info?}
      C -->|Yes| D[/isaac-functions]
      C -->|No| E{Need API Info?}
      E -->|Yes| F[/isaac-api]
      E -->|No| G[Write Code]
      G --> H[/isaac-validate]
      H -->|Missing| I[State: MISSING_DEPENDENCY]
      H -->|Exists| J[Continue Development]

  3. Key Commands by Use Case

  Starting New Feature Development

  1. /isaac-start "Building user authentication"
  2. /isaac-context                    # Understand current state
  3. /isaac-arch                       # Query architecture
  4. /isaac-functions                  # List available functions
  5. /isaac-task "Implement JWT auth"  # Create tracked task

  Debugging Issues

  1. /isaac-debug                      # Get recent commits & issues
  2. /isaac-context                    # Current system state
  3. /isaac-hotspots                   # Find frequently changed files
  4. /isaac-impact "component_name"    # Analyze dependencies

  Code Review/Refactoring

  1. /isaac-complexity                 # Find complex code
  2. /isaac-debt                       # Check technical debt
  3. /isaac-quality                    # Overall code quality
  4. /isaac-dependencies "module"      # Check what depends on this

  4. The ISAAC Development Loop

  ┌─────────────────────────────────────────────┐
  │  1. START SESSION with clear focus          │
  │     /isaac-start "Specific goal"            │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  2. GET CONTEXT before any work             │
  │     /isaac-context                          │
  │     /isaac-functions (if coding)            │
  │     /isaac-api (if API work)                │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  3. CREATE TASKS for tracking               │
  │     /isaac-task "Clear task description"    │
  │     /isaac-priority (to focus on important) │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  4. VALIDATE all code proposals             │
  │     /isaac-validate ["func1", "func2"]      │
  │     State "MISSING_DEPENDENCY" if needed    │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  5. UPDATE SESSION as you work              │
  │     /isaac-update "progress notes"          │
  │     /isaac-task-update (mark completed)     │
  └─────────────────────────┬───────────────────┘
                            │
  ┌─────────────────────────▼───────────────────┐
  │  6. END SESSION with summary                │
  │     /isaac-session-end                      │
  │     /isaac-stats (review work done)         │
  └─────────────────────────────────────────────┘

  5. Critical Anti-Hallucination Rules

  1. NEVER generate code without checking ISAAC first
    - Always run /isaac-functions before writing functions
    - Always run /isaac-api before creating endpoints
    - Always run /isaac-context before starting work
  2. ALWAYS validate before implementing
  /isaac-validate ["new_function", "helper_function", "api_endpoint"]
  3. EXPLICITLY state when something is missing
  MISSING_DEPENDENCY: EmailService - not found in ISAAC
  MISSING_DEPENDENCY: AuthMiddleware - needs to be created

  6. Power User Tips

  Batch Operations for Efficiency

  # Instead of multiple queries, use comprehensive ones:
  /isaac-context          # Gets session, tasks, and current work
  /isaac-stats           # Gets full system overview
  /isaac-priority        # Gets all important tasks at once

  Use ISAAC for Code Discovery

  /isaac-search "authentication"     # Find all auth-related code
  /isaac-similar "function_name"     # Find similar implementations
  /isaac-examples "pattern"          # Find usage examples

  Track Decision Making

  /isaac-decision "Switch to Redis for caching" "Performance reasons"
  /isaac-investigation "Bug in payment flow" "findings here"

  7. Common Workflows

  Morning Standup

  /isaac-standup         # What I did, what I'm doing, blockers
  /isaac-priority        # Focus for today
  /isaac-health         # System status check

  Before Creating PR

  /isaac-impact "my_changes"    # What will be affected
  /isaac-validate [...]         # Ensure all functions exist
  /isaac-quality               # Check code quality
  /isaac-session-end           # Close out the work session

  Learning New Codebase

  /isaac-ingest               # Import entire codebase
  /isaac-arch                 # Understand architecture
  /isaac-top-components       # Most important parts
  /isaac-flow "user_login"    # Trace specific flows

  8. Best Practices Summary

  1. Start every session with /isaac-start and /isaac-context
  2. Create tasks for everything you work on
  3. Validate all code before implementing
  4. Update session progress regularly
  5. End sessions properly to maintain good data
  6. Use shortcuts instead of writing full Cypher queries
  7. Trust ISAAC data over assumptions
  8. State missing dependencies explicitly

  9. What NOT to Do

  ❌ Don't write code without checking ISAAC first
  ❌ Don't assume functions exist - validate them
  ❌ Don't skip session management - it provides continuity
  ❌ Don't ignore MISSING_DEPENDENCY warnings
  ❌ Don't create orphaned entities - link everything properly

  By following these practices, ISAAC becomes your "second brain" that prevents mistakes, maintains context across sessions, and
  ensures your AI assistant never hallucinates about code that doesn't exist.