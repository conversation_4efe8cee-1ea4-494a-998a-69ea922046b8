# Debugging & Analysis Workflows

## Crisis Mode
URGENT: I have a critical issue that needs immediate attention:

CRISIS CONTEXT:
- Get all blocking critical/high priority issues
- Get current system state and recent changes
- Get stakeholder impact and escalation context
- Get available resources and expertise

Crisis: [Describe the urgent situation]
Impact: [Who/what is affected]
Timeline: [When does this need resolution]

Help me:
1. Triage the situation based on full context
2. Identify immediate actions vs. longer-term solutions
3. Plan resource allocation and escalation if needed
4. Coordinate response while maintaining other priorities

## Context Switch
I need to switch contexts from [Current Work] to [New Work]:

TRANSITION CONTEXT:
- Capture current state and decision points for [Current Work]
- Load full context for [New Work] including dependencies and blockers
- Identify what needs immediate attention vs. what can wait

Help me:
1. Safely bookmark current work for easy resumption
2. Understand the new context quickly and completely
3. Identify the most effective starting point
4. Minimize context switching overhead

## Impact Analysis
Before I make changes to [specific component/feature], help me understand the full impact:
- Get all dependencies and relationships for this component
- Identify tasks that would be affected by changes
- Find any related issues or past decisions
- Analyze potential risks and cascading effects

## Quick Context Check
Quick context check: What's my current development focus, active high-priority tasks, and any immediate blockers?

## Deep Context Analysis
I need a comprehensive context analysis. Please query:
1. My current session state and mental model
2. All active tasks with priorities and completion status
3. Blocking issues and their impact analysis
4. Recent decisions and their outcomes
5. Architecture components I'm actively modifying

Synthesize this into a clear picture of where I am and what needs attention.