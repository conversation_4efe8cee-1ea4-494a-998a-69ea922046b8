# Development Session Workflows

## Session Start
I'm starting a development session. Please query my knowledge graph to understand my current context:

CURRENT SESSION CONTEXT:
- Run: Get my active development session, current focus, and flow state
- Run: Get my high-priority tasks (critical/high priority, in-progress/pending)
- Run: Get any blocking issues currently open
- Run: Get the architecture components I'm currently working on

Based on this context, help me:
1. Understand where I left off
2. Identify the most important next actions
3. Spot any potential blockers or dependencies
4. Optimize my work approach for current energy/focus level

My goal: [Describe current session goal]
Time available: [Time constraint]
Energy level: [High/Medium/Low]

## Task Focus
I need help with a specific task. Please gather comprehensive context:

TASK CONTEXT NEEDED:
- Get details for task: "[Task Name]"
- Get related issues, dependencies, and architectural impact
- Get current completion status and any investigation notes
- Get related code context and recent decisions

Help me:
1. Understand the full scope and current state
2. Identify dependencies and potential blockers
3. Generate a step-by-step approach
4. Estimate effort and complexity
5. Suggest the optimal work approach

Current constraints: [Time, energy, focus level, other priorities]
Specific question: [Your specific question about the task]

## Problem Solving
I'm encountering an issue and need debugging assistance:

ISSUE INVESTIGATION CONTEXT:
- Get issue details for: "[Issue Name]"
- Get investigation trail and related decisions
- Get affected architecture components and dependencies
- Get similar past issues and their resolutions

Problem description: [Describe the problem you're facing]
What I've tried: [List attempted solutions]
Current hypothesis: [Your current thinking]

Help me:
1. Analyze the investigation trail and context
2. Identify potential root causes I might have missed
3. Suggest additional debugging approaches
4. Recommend solution strategies based on architecture context
5. Plan verification and testing approach

## Session Wrap-up
Help me wrap up my development session effectively:

SESSION COMPLETION CONTEXT:
- Get what I accomplished today vs. planned goals
- Get current state of in-progress tasks
- Get any new insights or decisions made
- Get preparation needed for next session

Help me:
1. Record session outcomes and learnings
2. Update task completion and progress
3. Capture decision points and mental model state
4. Plan optimal resumption strategy for next session
5. Identify what to communicate to stakeholders