# Knowledge Graph Update Workflows

## Session Progress Update
Update the knowledge graph with my current development session progress:

SESSION PROGRESS CONTEXT:
- Update my current development session with progress made
- Record completed tasks and mark them as finished
- Update task status changes (pending → in_progress → completed)
- Capture new decisions made and their rationale
- Record any new issues discovered or resolved
- Update architecture component modifications and changes
- Log new dependencies or relationships discovered
- Update my current focus and flow state

Progress to record:
- Tasks completed: [List what was finished]
- Tasks started: [List what was moved to in_progress]
- Decisions made: [Key decisions and reasoning]
- Code changes: [Major structural changes made]
- Issues found/resolved: [Problems discovered or fixed]
- Next focus: [What I'm working on next]

## Structural Code Changes Update
Record significant code architecture changes in the knowledge graph:

CODE ARCHITECTURE UPDATES:
- Document new components, modules, or services created
- Update relationships between existing components
- Record API changes, schema modifications, or interface updates
- Log new dependencies added or removed
- Update technology stack changes
- Record performance or security improvements
- Document refactoring impacts and scope
- Update testing coverage and strategy changes

Changes to record:
- New components: [Components/modules created]
- Modified components: [Components changed and how]
- Deleted components: [Components removed and why]
- New dependencies: [Libraries/services added]
- API changes: [Endpoint/schema modifications]
- Database changes: [Schema or data model updates]

## Task and TODO Synchronization
Synchronize current task list and TODOs with the knowledge graph:

TASK SYNCHRONIZATION:
- Update all task statuses to match current reality
- Add new tasks discovered during development
- Update task priorities based on current context
- Record task dependencies and blockers discovered
- Update estimated effort based on actual work
- Log task completion notes and outcomes
- Create subtasks for complex work items
- Update task assignments and ownership

Current state to sync:
- TODO list status: [Current todos and their states]
- New tasks identified: [Tasks discovered during work]
- Priority changes: [Tasks that changed priority]
- Blockers found: [New blockers or dependencies]
- Completion notes: [What was learned completing tasks]

## Decision Trail Update
Record important decisions made during the session:

DECISION DOCUMENTATION:
- Log technical decisions and their rationale
- Record trade-offs considered and chosen approach
- Document alternative options evaluated
- Update decision outcomes and effectiveness
- Record stakeholder input or constraints
- Log timeline and resource decisions
- Document risk assessments and mitigations

Decisions to record:
- Technical decisions: [Architecture, tool, or approach choices]
- Process decisions: [Workflow or methodology changes]
- Priority decisions: [What to work on and why]
- Resource decisions: [Time, people, or tool allocation]
- Risk decisions: [Risk acceptance or mitigation strategies]

## Investigation and Learning Update
Update the knowledge graph with investigation findings and learning:

INVESTIGATION UPDATES:
- Record debugging session findings and solutions
- Document research outcomes and useful resources
- Update understanding of system behavior or constraints
- Log performance testing results and insights
- Record security analysis findings
- Document integration testing discoveries
- Update understanding of third-party dependencies

Learning to capture:
- Root causes found: [Problems diagnosed and solutions]
- System insights: [New understanding of how things work]
- Tool learnings: [New techniques or tools discovered]
- Performance insights: [Bottlenecks or optimizations found]
- Security learnings: [Vulnerabilities or protections discovered]

## Relationship and Dependency Update
Update component relationships and dependencies in the knowledge graph:

RELATIONSHIP UPDATES:
- Map new dependencies between components
- Update data flow relationships
- Record service communication patterns
- Document shared resource dependencies
- Update deployment dependencies
- Record testing dependencies and order
- Map business process to technical component relationships

Relationships to update:
- Component dependencies: [A depends on B relationships]
- Data dependencies: [Data flow between components]
- Service dependencies: [Service call relationships]
- Infrastructure dependencies: [Deployment/runtime dependencies]
- Test dependencies: [Test execution order or shared fixtures]

## Issue and Risk Update
Update the knowledge graph with current issues and risk status:

ISSUE AND RISK UPDATES:
- Create new issues discovered during development
- Update status of existing issues (open/investigating/resolved)
- Record risk assessments and mitigation status
- Document technical debt identified
- Log security concerns or vulnerabilities found
- Update performance issues and optimization opportunities
- Record compliance or regulatory considerations

Issues and risks to update:
- New issues: [Problems discovered during work]
- Resolved issues: [Problems fixed and how]
- Risk status: [Risk likelihood or impact changes]
- Technical debt: [New debt created or paid down]
- Performance issues: [Bottlenecks or optimizations needed]

## Development Environment Update
Update the knowledge graph with development environment changes:

ENVIRONMENT UPDATES:
- Record tool updates or configuration changes
- Document new development dependencies
- Update build or deployment process changes
- Record environment setup or troubleshooting notes
- Log integration with new external services
- Document local development workflow improvements
- Update team development standards or conventions

Environment changes to record:
- Tool updates: [Version changes or new tools added]
- Configuration changes: [Settings or environment updates]
- Process improvements: [Workflow or automation improvements]
- Integration updates: [New external service connections]
- Standards updates: [Coding or process standard changes]

## Bulk Session Update
Comprehensive update of all session-related information in the knowledge graph:

COMPREHENSIVE SESSION UPDATE:
Execute all of the above update patterns in sequence:
1. Update session progress and current state
2. Record all structural code changes made
3. Synchronize task and TODO status
4. Document decisions made during session
5. Capture investigation findings and learnings
6. Update component relationships and dependencies
7. Record new issues and update risk status
8. Document environment or process changes

Use this for end-of-session knowledge graph synchronization to ensure nothing is lost between sessions.

## Quick Status Sync
Fast update for minor progress without full session update:

QUICK SYNC:
- Update current task status only
- Record immediate progress made
- Log any blocking issues encountered
- Update current focus area

Quick updates:
- Current task: [What I'm working on now]
- Progress made: [What was accomplished]
- Blockers: [Immediate obstacles encountered]
- Next action: [What I'll do next]