name: CI Pipeline

on:
  push:
    branches: [ main, develop, feature/** ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint-backend:
    name: <PERSON><PERSON> Backend (Python/Ruff)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install backend dependencies and Ruff
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt ruff

      - name: Run Ruff linter
        run: ruff check ./backend

      - name: Run Ruff formatter check
        run: ruff format ./backend --check

  lint-frontend-patient:
    name: Lint Frontend Patient App (Node/ESLint)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend-patient/package-lock.json

      - name: Install frontend dependencies
        run: npm ci --prefix ./frontend-patient

      - name: Run ESLint
        run: npm run lint --prefix ./frontend-patient

  lint-frontend-clinician:
    name: Lint Frontend Clinician Portal (Node/ESLint)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend-clinician/package-lock.json
      - name: Install frontend dependencies
        run: npm ci --prefix ./frontend-clinician
      - name: Run ESLint
        run: npm run lint --prefix ./frontend-clinician

  lint-frontend-admin:
    name: Lint Frontend Admin Portal (Node/ESLint)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend-admin/package-lock.json
      - name: Install frontend dependencies
        run: npm ci --prefix ./frontend-admin
      - name: Run ESLint
        run: npm run lint --prefix ./frontend-admin

  sync-knowledge:
    needs: [lint-backend, lint-frontend-patient, lint-frontend-clinician, lint-frontend-admin]
    if: ${{ always() && !contains(needs.*.result, 'failure') }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt
          # Add any additional dependencies needed for knowledge sync scripts
          pip install repomix

      - name: Initialize knowledge structure
        run: |
          mkdir -p knowledge/digests
          mkdir -p knowledge/snapshots
          mkdir -p knowledge/progress
          mkdir -p knowledge/git-context

      - name: Sync progress from PRDs
        run: |
          if [ -f "scripts/update_progress_from_prds.py" ]; then
            python scripts/update_progress_from_prds.py
          fi

      - name: Generate repo snapshot
        run: |
          python -m repomix --verbose \
            --style xml \
            -o pulsetrack-output.xml
          mkdir -p knowledge/snapshots
          cp pulsetrack-output.xml knowledge/snapshots/

      - name: Generate git context report
        run: |
          if [ -f "scripts/git_context.py" ]; then
            python scripts/git_context.py --output knowledge/git-context/latest.md --format md --depth medium
          fi

      - name: Commit & push knowledge updates
        env:
          GIT_AUTHOR_NAME: pulsetrack-bot
          GIT_AUTHOR_EMAIL: bot@pulsetrack
          GIT_COMMITTER_NAME: pulsetrack-bot
          GIT_COMMITTER_EMAIL: bot@pulsetrack
        run: |
          git diff --quiet || (
            git add knowledge/digests/ \
                   knowledge/snapshots/ \
                   knowledge/progress/ \
                   knowledge/git-context/ &&
            git commit -m "chore(docs): refresh knowledge digests & git context [ci skip]" &&
            git push origin HEAD
          )
