# Environment variables
.env*
!.env.example
.env.db

.yarn/

# Python cache
__pycache__/
*.py[cod]
*$py.class

# Python virtual environment
.venv/
venv/
ENV/

# Node dependencies
/node_modules
/frontend-patient/node_modules
/frontend-clinician/node_modules
/frontend-admin/node_modules
/packages/shared-frontend/node_modules

# Node build outputs
/frontend-patient/dist
/frontend-clinician/dist
/frontend-admin/dist
/packages/shared-frontend/dist

/frontend-patient/build
/frontend-clinician/build
/frontend-admin/build
/packages/shared-frontend/build

# Node logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS generated files
.DS_Store
Thumbs.db

# IDE / Editor directories
.vscode/
.idea/
*.swp
*~

# Package artifacts (within packages/*/)
packages/*/node_modules/
packages/*/dist/
packages/*/build/

# Docker
docker-compose.override.yml

# Logs
logs/
*.log
!.xgen/logs/

# Coverage output
.coverage
coverage.*
htmlcov/
.pytest_cache/

# Alembic (keep versions)
!backend/alembic/versions/

# Repomix output
repomix-output.xml
pulsetrack-output.xml

# Legacy documentation structure
knowledge/.xgen

# AI digests
knowledge/digests/ai.md
