# Environment variables
.env*
!.env.example
.env.db

# Python cache
__pycache__/
*.py[cod]
*$py.class

# Python virtual environment
.venv/
venv/
ENV/

# Node dependencies
/node_modules
/frontend-patient/node_modules
/frontend-clinician/node_modules
/frontend-admin/node_modules
/packages/shared-frontend/dist
/packages/shared-frontend/node_modules

# Node build outputs
/frontend-patient/dist
/frontend-clinician/dist
/frontend-patient/build
/frontend-clinician/build

# Node logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS generated files
.DS_Store
Thumbs.db

# IDE / Editor directories
.vscode/
.idea/
*.swp
*~

# Docker
docker-compose.override.yml

# Logs
logs/
*.log
!.xgen/logs/

# Coverage output
.coverage
coverage.*
htmlcov/
.pytest_cache/

# Alembic (keep versions)
!backend/alembic/versions/

pulsetrack-output.xml
knowledge/digests/ai.md