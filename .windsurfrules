# Rules, Knowledge Repository, and Documentation Instructions

## Rules
- DO NOT ask for permission to search for, investigate, or modify files.

## Knowledge Repository Structure
The Knowledge Repository is a set of directories and files stored in the `knowledge/` directory to maintain project context across sessions. It includes:

- `knowledge/digests/ai.md`: AI-generated insights about the project
- `knowledge/digests/project_history.md`: Chronological history of project development
- `knowledge/git-context/latest.md`: Git context information and recent changes
- `knowledge/progress.md`: Task tracking and implementation status
- `knowledge/snapshots/`: Repository snapshots for reference

## Documentation Structure
The Documentation is organized in the `docs/` directory with a clear structure:

- `docs/architecture/`: System architecture diagrams and descriptions
- `docs/planning/`: Project planning documents, roadmaps, and timelines
- `docs/prds/`: Product Requirements Documents for different features

---

## Knowledge Status Display
Always begin EVERY response with one of the following:
- `[KNOWLEDGE: ACTIVE]` – if the Knowledge Repository is present and in use
- `[KNOWLEDGE: INACTIVE]` – if the Knowledge Repository is not initialized

---

## Knowledge Repository Initialization
When starting a session, check if the Knowledge Repository exists:
```yaml
- intent: CHECK_FOR_FILES
  directory: knowledge/digests
```

If present, read key files:
```yaml
- intent: READ_FILE
  path: knowledge/digests/ai.md

- intent: READ_FILE
  path: knowledge/digests/project_history.md

- intent: READ_FILE
  path: knowledge/progress.md

- intent: READ_FILE
  path: knowledge/git-context/latest.md
```

If not present:
```yaml
- intent: ASK_CONFIRMATION
  message: "No Knowledge Repository was found. I recommend initializing one using the scripts in the scripts/ directory. Proceed?"
```

---

## Tool Interface Map

| Intent             | Description                          | Must be implemented as                |
|--------------------|--------------------------------------|----------------------------------------|
| `CHECK_FOR_FILES`  | List files in a given directory       | IDE-native list function               |
| `READ_FILE`        | Read file contents                    | Bound to Roo's or LLM IDE reader       |
| `WRITE_FILE_APPEND`| Append content to an existing file    | Idempotent insert, preserve history    |
| `ASK_CONFIRMATION` | Prompt user before making changes     | Textual or modal interface             |

---

## General Update Guidelines
- Use timestamp format `YYYY-MM-DD HH:MM:SS`
- Use `WRITE_FILE_APPEND` to insert new entries (do not overwrite)
- Avoid duplicates: Check recent entries before inserting
- Use the scripts in `scripts/` directory to update knowledge files automatically

---

## Update Command
Every mode must respond to the command `Update Knowledge` or `UK` by:
1. Halting the current task
2. Acknowledging with `[KNOWLEDGE: UPDATING]`
3. Reviewing the current conversation
4. Running the appropriate scripts to update the Knowledge Repository
5. Confirming with `[KNOWLEDGE: UPDATED]`

Example:
```yaml
- intent: EXECUTE_SCRIPT
  path: scripts/render_digests.py
```

---

## File Update Scripts

### render_digests.py
- Updates `knowledge/digests/ai.md` and `knowledge/digests/project_history.md`
- Run after significant changes to document progress and insights

### update_progress_from_prds.py
- Updates `knowledge/progress.md` based on PRD task completion status
- Run after updating PRD files to track implementation progress

### git_context.py
- Updates `knowledge/git-context/latest.md` with current git information
- Run to capture the current state of the repository

### init_knowledge_repo.py
- Initializes the knowledge repository structure
- Run when setting up a new environment

---

## Documentation Guidelines

### Architecture Documentation
- Place in `docs/architecture/`
- Include system diagrams, component relationships, and design patterns
- Use Mermaid syntax for diagrams when possible

### Planning Documentation
- Place in `docs/planning/`
- Include roadmaps, timelines, and project context
- Track high-level progress and strategic goals

### PRD Documentation
- Place in `docs/prds/`
- Use checkboxes (`- [ ]`, `- [x]`) to track implementation status
- Organize by feature area with clear acceptance criteria

---

## Development Constraints
Always follow the constraints defined in the architecture documentation:

### Key Constraints
- **Architecture:** API-first, decoupled design; frontend is consumer only
- **CRUD:** Consistent use of class-based `CRUDBase`, unified import pattern
- **Performance:**
  - Avoid N+1 queries (use eager loading with selectinload)
  - Use indexed queries
  - Batch operations when needed
  - Paginate list endpoints
- **Security:**
  - RBAC and Clerk role checks enforced at endpoint and CRUD layers
  - Avoid exposing unprotected endpoints
- **Scalability:**
  - Use containerization
  - Design for horizontal scaling post-MVP
- **Naming Conventions:**
  - Python: snake_case for files, PascalCase for classes
  - JSON: camelCase preferred for frontend compatibility
- **Audit Logging:**
  - Required for status updates, deletions, and sensitive operations

---

## Fallback Behavior for Unknown Tools
If an intent cannot be resolved by the environment:
- Acknowledge with `[TOOL MAPPING ERROR: {INTENT}]`
- Suggest fallback command (e.g., manual script execution)
- Document the issue in a comment for future reference