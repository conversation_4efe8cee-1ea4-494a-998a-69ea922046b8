# PulseTrack - Claude Code Instructions

PulseTrack is a multi-tenant medical platform with AI/RAG integration for patient care management.

## 🚨 CRITICAL PYTHON RULE
**ALWAYS run Python commands inside the backend Docker container!**

```bash
# For PulseTrack backend:
docker-compose exec backend python script.py

# Examples:
docker-compose exec backend python scripts/seed_demo_data.py
docker-compose exec backend pytest tests/unit/ -v
docker-compose exec backend alembic upgrade head

# NEVER run Python directly on host machine!
# ❌ python script.py           # WRONG - uses host Python
# ❌ cd backend && python script.py  # WRONG - uses host Python
# ✅ docker-compose exec backend python script.py # CORRECT - uses container Python
```

## Quick Start Commands

```bash
# Start all services
docker-compose up -d

# Backend development
docker-compose exec backend uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Frontend development (separate terminals)
cd frontend-patient && yarn dev     # Port 5174
cd frontend-clinician && yarn dev   # Port 5173
cd frontend-admin && yarn dev       # Port 5175

# Run tests
docker-compose exec backend pytest tests/unit/ -v
cd frontend-clinician && yarn test

# Seed demo data
docker-compose exec backend python scripts/seed_medications.py
docker-compose exec backend python scripts/seed_demo_data.py
```

---

## 🤖 ISAAC AI Coding Assistant

### Overview
ISAAC is a two-command development system that eliminates hallucinations by continuously validating against a Neo4j knowledge graph. You describe what you want, Claude does everything else.

**Prerequisites**: 
- ISAAC MCP server must be connected (provides `mcp__isaac__` commands)
- GitHub MCP server must be connected (provides `mcp__github-official__` commands)  
- Neo4j database must be running with PulseTrack knowledge graph
- GitHub authentication configured for MPIsaac-Syn/pulsetrack repository

### The Three Commands

#### 0️⃣ `/isaac-init`
**User provides**: Command to initialize ISAAC system
**Claude executes**: Reads CLAUDE.md, confirms understanding of all commands and context, reports readiness

#### 1️⃣ `/isaac-begin-session "task description"`
**User provides**: One clear sentence describing the desired outcome
**Claude executes**: Complete development cycle with continuous KG validation via ISAAC MCP

#### 2️⃣ `/isaac-end-session`
**User provides**: Simple confirmation (e.g., "done", "ship it", "looks good")
**Claude executes**: Final validation, KG updates, git commit via ISAAC MCP, and closes all relevant GitHub issues

---

### 🔧 CLAUDE'S ISAAC EXECUTION INSTRUCTIONS

**IMPORTANT**: All Neo4j knowledge graph operations are executed via the ISAAC MCP tool commands:
- `mcp__isaac__read_neo4j_cypher` - For all read queries
- `mcp__isaac__write_neo4j_cypher` - For all write operations
- `mcp__isaac__get_neo4j_schema` - To understand the KG structure

When user types `/isaac-init`, you MUST:
1. Read and parse the entire CLAUDE.md file
2. Confirm understanding of:
   - PulseTrack project structure and tech stack
   - Python Docker container requirements
   - ISAAC AI Coding Assistant commands and workflow
   - Neo4j knowledge graph integration via MCP tools
   - GitHub issue tracking requirements
   - All validation and KG sync procedures
3. Respond with:
   ```
   🤖 ISAAC System Initialized
   
   ✅ Context loaded:
   - PulseTrack medical platform architecture understood
   - Docker Python execution rules acknowledged
   - Neo4j MCP commands ready (read/write/schema)
   - GitHub MCP integration verified
   - Three-command workflow ready (/isaac-init, /isaac-begin-session, /isaac-end-session)
   
   Standing by to begin development session. Use /isaac-begin-session "task" to start.
   ```

When user types `/isaac-begin-session "task"`, you MUST follow this exact sequence:

#### PHASE 1: Session Initialization (0-10 seconds)
```bash
# 0. Parse task for any GitHub issue references (e.g., #123, fixes #456, closes #789)
import re
issue_pattern = r'#(\d+)|(?:fixes?|closes?|resolves?)\s+#(\d+)'
referenced_issues = []
for match in re.finditer(issue_pattern, task_description, re.IGNORECASE):
    issue_num = match.group(1) or match.group(2)
    if issue_num:
        referenced_issues.append(int(issue_num))

# 1. Create GitHub issue for tracking
github_issue = mcp__github-official__create_issue(
  owner="MPIsaac-Syn",  # or appropriate org
  repo="pulsetrack",
  title=f"🤖 ISAAC: {task_description[:60]}...",
  body=f"""## ISAAC Development Session

**Task**: {task_description}
**Session ID**: {session_id}
**Started**: {datetime.now()}

### Progress Tracking
- [ ] Analyze requirements
- [ ] Find existing components  
- [ ] Implement solution
- [ ] Test implementation
- [ ] Update knowledge graph

### Implementation Plan
_Will be updated as session progresses..._

---
*This issue is automatically managed by ISAAC AI Assistant*
""",
  labels=["isaac-session", "in-progress", "automated"]
)

# 2. Create session in KG using ISAAC MCP (with GitHub issue link and referenced issues)
mcp__isaac__write_neo4j_cypher(query="""
  MATCH (p:Project {name: 'PulseTrack'})
  CREATE (s:Session {
    id: timestamp(),
    task: $task_description,
    status: 'active',
    github_issue_number: $issue_number,
    github_issue_url: $issue_url,
    referenced_issues: $referenced_issues,
    created_at: datetime()
  })
  CREATE (p)-[:HAS_SESSION]->(s)
  RETURN s.id as session_id
""", params={
  "task_description": task,
  "issue_number": github_issue.number,
  "issue_url": github_issue.html_url,
  "referenced_issues": referenced_issues
})

# 3. Query existing components relevant to task
mcp__isaac__read_neo4j_cypher(query="""
  MATCH (c:Component)-[:BELONGS_TO]->(p:Project {name: 'PulseTrack'})
  WHERE c.name CONTAINS $keyword OR c.description CONTAINS $keyword
  RETURN c.name, c.path, c.type, c.dependencies
""", params={"keyword": extracted_keyword})

# 4. Create task breakdown and update GitHub issue
task_breakdown = mcp__isaac__write_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  CREATE (t:TodoList)-[:FOR_SESSION]->(s)
  CREATE (t1:Todo {title: 'Analyze requirements', status: 'pending'}),
         (t2:Todo {title: 'Find existing components', status: 'pending'}),
         (t3:Todo {title: 'Implement solution', status: 'pending'}),
         (t4:Todo {title: 'Test implementation', status: 'pending'})
  CREATE (t)-[:HAS_TODO]->(t1), (t)-[:HAS_TODO]->(t2),
         (t)-[:HAS_TODO]->(t3), (t)-[:HAS_TODO]->(t4)
  RETURN t
""", params={"session_id": session_id})

# 5. Post initial plan to GitHub
mcp__github-official__add_issue_comment(
  owner="MPIsaac-Syn",
  repo="pulsetrack",
  issue_number=github_issue.number,
  body=f"""### 🚀 Session Started

**Components Found**: {len(components)} relevant components
**Implementation Approach**: {approach_summary}

I'll now begin implementing the solution and will update this issue with progress.
"""
)
```

#### PHASE 2: Continuous Development Loop (10 seconds - completion)
**Every 30 seconds or before any file operation:**
```bash
# Validate component exists via ISAAC MCP
result = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (c:Component {name: $component_name})
  RETURN c.path, c.exports, c.parameters
""", params={"component_name": component_name})

# If not found, search for similar
if not result:
  alternatives = mcp__isaac__read_neo4j_cypher(query="""
    MATCH (c:Component)
    WHERE c.type = $component_type
    RETURN c.name, c.path 
    ORDER BY apoc.text.distance(c.name, $component_name) ASC 
    LIMIT 5
  """, params={"component_type": type, "component_name": component_name})
```

**Before EVERY function call or import:**
```bash
# Verify function signature using ISAAC MCP
function_info = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (f:Function {name: $function_name})-[:EXPORTED_BY]->(c:Component)
  RETURN f.signature, f.parameters, c.path
""", params={"function_name": function_name})

# Get proper import path
import_path = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (c:Component)-[:EXPORTS]->(f:Function {name: $function_name})
  RETURN c.import_path
""", params={"function_name": function_name})
```

**After EVERY file modification:**
```bash
# Update component in KG using ISAAC MCP
mcp__isaac__write_neo4j_cypher(query="""
  MATCH (c:Component {path: $file_path})
  SET c.last_modified = datetime(),
      c.functions = $extracted_functions,
      c.exports = $extracted_exports
  RETURN c
""", params={
  "file_path": file_path,
  "extracted_functions": functions_list,
  "extracted_exports": exports_list
})

# Update session progress
mcp__isaac__write_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  SET s.files_modified = coalesce(s.files_modified, 0) + 1,
      s.last_activity = datetime()
  RETURN s
""", params={"session_id": session_id})
```

#### PHASE 3: Task Execution Pattern
For each subtask, Claude MUST:

1. **Query KG for existing solutions using ISAAC MCP**:
```bash
patterns = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern)-[:SOLVES]->(problem:Problem {type: $task_type})
  RETURN p.implementation, p.example_code, p.used_in_files
""", params={"task_type": task_type})
```

2. **Use only verified components via ISAAC MCP**:
```bash
verified_components = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (c:Component {verified: true})-[:USED_BY]->(f:File)
  WHERE f.path IN $similar_files
  RETURN c.name, c.path, count(f) as usage_count
  ORDER BY usage_count DESC
""", params={"similar_files": similar_files})
```

3. **Track every decision using ISAAC MCP**:
```bash
mcp__isaac__write_neo4j_cypher(query="""
  CREATE (d:Decision {
    session_id: $session_id,
    type: 'component_selection',
    chosen: $component_name,
    reason: $why_chosen,
    alternatives: $other_options,
    timestamp: datetime()
  })
  RETURN d
""", params={
  "session_id": session_id,
  "component_name": chosen_component,
  "why_chosen": reason,
  "other_options": alternatives
})
```

#### PHASE 4: Continuous Validation Rules
**EVERY code line must be validated**:
- ❌ `import { Button } from './components'` → Query KG for correct path
- ✅ `import { Button } from '@/components/ui/button'` → Verified via KG
- ❌ `await fetchPatientData()` → Function doesn't exist in KG
- ✅ `await apiClient.get('/patients')` → Verified pattern from KG

**Auto-correction via ISAAC MCP**:
```bash
# When function not found, query alternatives
alternatives = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (f:Function)
  WHERE f.purpose CONTAINS $intended_purpose 
     OR f.name CONTAINS $partial_name
  RETURN f.name, f.signature, f.component_path
  LIMIT 3
""", params={
  "intended_purpose": purpose,
  "partial_name": partial_function_name
})
```

#### PHASE 5: Progress Tracking
**Every 2 minutes, update session and GitHub**:
```bash
# Update session progress in KG
progress_data = mcp__isaac__write_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  MATCH (t:Todo)-[:FOR_SESSION]->(s)
  WITH s, 
       count(CASE WHEN t.status = 'completed' THEN 1 END) as completed,
       count(t) as total,
       collect(CASE WHEN t.status = 'completed' THEN t.title END) as completed_tasks
  SET s.progress = CASE WHEN total > 0 THEN (completed * 100.0) / total ELSE 0 END,
      s.status = CASE WHEN s.progress = 100 THEN 'ready_to_close' ELSE 'active' END,
      s.last_update = datetime()
  RETURN s.progress, s.status, s.github_issue_number, completed_tasks
""", params={"session_id": session_id})

# Update GitHub issue with progress
if progress_data.github_issue_number:
  mcp__github-official__add_issue_comment(
    owner="MPIsaac-Syn",
    repo="pulsetrack",
    issue_number=progress_data.github_issue_number,
    body=f"""### 📊 Progress Update

**Completion**: {progress_data.progress:.0f}%
**Status**: {progress_data.status}

**Completed Tasks**:
{chr(10).join(f"- ✅ {task}" for task in progress_data.completed_tasks)}

**Recent Changes**:
{chr(10).join(f"- Modified: `{file}`" for file in recent_files)}

_Last updated: {datetime.now().strftime("%I:%M %p")}_
"""
  )
```

---

### 📋 Example Execution Flow

**User**: `/isaac-begin-session "Add patient medication history timeline to patient detail page"`

**Claude's Execution**:
```bash
🤖 ISAAC Session Started [Neo4j session: **********]
📊 Querying knowledge graph for existing components...

[KG Query] MATCH (c:Component) WHERE c.type IN ['Timeline', 'History', 'Medication'] RETURN c
✓ Found: MedicationRequestHistory, TimelineComponent, PatientDetailPage

📝 Creating implementation plan:
1. ✓ Locate PatientDetailPage component
2. ✓ Find existing timeline components  
3. ✓ Query medication history data structure
4. ⏳ Implement timeline integration

[KG Validation] Checking import path for TimelineComponent...
✓ Verified: '@/components/shared/Timeline'

[File: frontend-patient/src/pages/PatientDetailPage.tsx]
- Adding Timeline import (verified via KG)
- Querying KG for useMedicationHistory hook... 
  ❌ Not found. Searching for alternatives...
  ✓ Found: useMedicationRequests in @/hooks/useMedicationRequests

[KG Update] Recording new component relationship:
CREATE (:Component {name: 'PatientDetailPage'})-[:USES]->(:Component {name: 'Timeline'})

🧪 Running tests...
✓ Component renders without errors
✓ Timeline displays medication data
✓ Sorting works correctly

[Session Progress: 85%] Updating final TypeScript types...

✅ Implementation complete!
📊 KG Updated: 3 new relationships, 1 pattern recorded
```

**User**: `/isaac-end-session`

**Claude's Execution**:
```bash
🏁 Finalizing ISAAC Session...

# PHASE 1: Comprehensive KG Synchronization
[KG Sync] Scanning all modified files for complete entity extraction...

# 1. Extract ALL functions, classes, and exports from modified files
for file in modified_files:
  file_analysis = mcp__isaac__write_neo4j_cypher(query="""
    MERGE (f:File {path: $file_path})
    SET f.last_analyzed = datetime(),
        f.content_hash = $content_hash
    WITH f
    
    // Delete old relationships to ensure clean state
    MATCH (f)-[r:CONTAINS|EXPORTS|IMPORTS]->()
    DELETE r
    
    // Create new entities and relationships
    UNWIND $functions as func
    CREATE (fn:Function {
      name: func.name,
      signature: func.signature,
      parameters: func.parameters,
      return_type: func.return_type,
      is_async: func.is_async,
      is_exported: func.is_exported
    })
    CREATE (f)-[:CONTAINS]->(fn)
    
    UNWIND $classes as cls
    CREATE (c:Class {
      name: cls.name,
      extends: cls.extends,
      implements: cls.implements,
      methods: cls.methods,
      properties: cls.properties,
      is_exported: cls.is_exported
    })
    CREATE (f)-[:CONTAINS]->(c)
    
    UNWIND $imports as imp
    MERGE (imported:Component {path: imp.from})
    CREATE (f)-[:IMPORTS {items: imp.items}]->(imported)
    
    UNWIND $exports as exp
    CREATE (f)-[:EXPORTS {name: exp.name, type: exp.type}]->()
    
    RETURN f
  """, params={
    "file_path": file.path,
    "content_hash": file.hash,
    "functions": extracted_functions,
    "classes": extracted_classes,
    "imports": extracted_imports,
    "exports": extracted_exports
  })

# 2. Update component relationships and dependencies
mcp__isaac__write_neo4j_cypher(query="""
  // Find all components and update their relationships
  MATCH (c:Component)-[:BELONGS_TO]->(p:Project {name: 'PulseTrack'})
  WHERE c.path IN $modified_paths
  WITH c
  
  // Update component dependencies based on imports
  MATCH (f:File {path: c.path})-[:IMPORTS]->(imported:Component)
  MERGE (c)-[:DEPENDS_ON]->(imported)
  
  // Update function relationships
  MATCH (f:File {path: c.path})-[:CONTAINS]->(fn:Function)
  WHERE fn.is_exported = true
  MERGE (c)-[:EXPORTS_FUNCTION]->(fn)
  
  // Update class relationships  
  MATCH (f:File {path: c.path})-[:CONTAINS]->(cls:Class)
  WHERE cls.is_exported = true
  MERGE (c)-[:EXPORTS_CLASS]->(cls)
  
  // Track usage patterns
  MATCH (other:Component)-[:IMPORTS]->(c)
  MERGE (c)-[:USED_BY]->(other)
  
  SET c.last_sync = datetime(),
      c.dependency_count = SIZE([(c)-[:DEPENDS_ON]->() | 1]),
      c.usage_count = SIZE([(c)<-[:USED_BY]-() | 1])
  
  RETURN c.path, c.dependency_count, c.usage_count
""", params={"modified_paths": [f.path for f in modified_files]})

# 3. Detect and record new patterns
pattern_analysis = mcp__isaac__write_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  
  // Analyze code patterns from this session
  WITH s, $code_patterns as patterns
  UNWIND patterns as pattern
  
  MERGE (p:Pattern {
    name: pattern.name,
    type: pattern.type
  })
  SET p.implementation = pattern.implementation,
      p.description = pattern.description,
      p.example_usage = pattern.example,
      p.last_used = datetime()
  
  CREATE (s)-[:DISCOVERED_PATTERN]->(p)
  
  // Link pattern to files where it's used
  WITH p, pattern
  UNWIND pattern.used_in_files as file_path
  MATCH (f:File {path: file_path})
  CREATE (p)-[:IMPLEMENTED_IN]->(f)
  
  RETURN collect(p.name) as discovered_patterns
""", params={
  "session_id": session_id,
  "code_patterns": extracted_patterns
})

# 4. Update cross-references and type definitions
mcp__isaac__write_neo4j_cypher(query="""
  // Update TypeScript interfaces and type relationships
  MATCH (f:File)-[:CONTAINS]->(t:TypeDefinition)
  WHERE f.path IN $modified_paths
  WITH t, f
  
  // Find where types are used
  MATCH (other:File)-[:IMPORTS]->(f)
  WHERE other.content CONTAINS t.name
  CREATE (other)-[:USES_TYPE]->(t)
  
  // Update interface implementations
  MATCH (c:Class)-[:IMPLEMENTS]->(i:Interface)
  WHERE c.name = t.name OR i.name = t.name
  SET c.last_validated = datetime()
  
  RETURN count(t) as types_updated
""", params={"modified_paths": [f.path for f in modified_files]})

# 5. Validate complete graph consistency
validation_results = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  
  // Check for orphaned functions (functions not in any file)
  OPTIONAL MATCH (orphan:Function)
  WHERE NOT (()-[:CONTAINS]->(orphan))
  
  // Check for broken imports
  OPTIONAL MATCH (f:File)-[imp:IMPORTS]->(missing:Component)
  WHERE NOT EXISTS(missing.path)
  
  // Check for circular dependencies
  OPTIONAL MATCH path = (c1:Component)-[:DEPENDS_ON*]->(c1)
  
  // Check for unused exports
  OPTIONAL MATCH (c:Component)-[:EXPORTS_FUNCTION]->(unused:Function)
  WHERE NOT (()-[:IMPORTS]->()<-[:EXPORTS_FUNCTION]-(unused))
  
  RETURN {
    orphaned_functions: count(DISTINCT orphan),
    broken_imports: count(DISTINCT imp),
    circular_dependencies: count(DISTINCT path),
    unused_exports: count(DISTINCT unused),
    session_id: s.id
  } as validation
""", params={"session_id": session_id})

# PHASE 2: Final Validation and Reporting
[KG Query] Validating all modifications...
✓ All imports verified against KG
✓ No orphaned functions (0 found)
✓ Type definitions match interfaces
✓ No circular dependencies detected
✓ All function signatures validated
✓ Cross-references updated

# PHASE 3: Generate Comprehensive Session Report
[GitHub] Posting final summary with complete KG sync details...
session_data = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  OPTIONAL MATCH (s)-[:DISCOVERED_PATTERN]->(p:Pattern)
  OPTIONAL MATCH (s)-[:MADE_DECISION]->(d:Decision)
  OPTIONAL MATCH (modified:File)<-[:MODIFIED_IN]-(s)
  RETURN {
    github_issue_number: s.github_issue_number,
    referenced_issues: s.referenced_issues,
    task: s.task,
    files_modified: count(DISTINCT modified),
    patterns_discovered: collect(DISTINCT p.name),
    decisions_made: count(DISTINCT d),
    components_updated: SIZE([(c:Component {last_sync: s.created_at}) | c]),
    functions_added: SIZE([(f:Function)<-[:CONTAINS]-(file:File) WHERE file.last_analyzed >= s.created_at | f]),
    relationships_created: SIZE([(s)-[r]->() | r])
  } as report
""", params={"session_id": session_id})

mcp__github-official__add_issue_comment(
  owner="MPIsaac-Syn",
  repo="pulsetrack", 
  issue_number=session_data.github_issue_number,
  body=f"""### ✅ Session Complete!

**Task**: {session_data.task}
**Files Modified**: {session_data.files_modified}

### Knowledge Graph Synchronization Report

**📊 Entity Updates**:
- Components updated: {session_data.components_updated}
- Functions added/modified: {session_data.functions_added}
- Relationships created: {session_data.relationships_created}
- Patterns discovered: {', '.join(session_data.patterns_discovered) or 'None'}

**✅ Validation Results**:
- All imports verified against KG
- No orphaned functions or entities
- Type definitions synchronized
- Cross-references updated
- No circular dependencies detected
- All function signatures validated

**🔗 Graph Integrity**:
- Broken imports: 0
- Unused exports cleaned: {validation_results.unused_exports}
- Type mismatches resolved: 0

**Summary of Changes**:
- Added Timeline to PatientDetailPage
- Created pattern: "Medication History Display"  
- Linked 3 components in knowledge graph
- Updated 15 function signatures
- Synchronized 8 type definitions

**Git Commit**: `feat: add medication history timeline to patient detail page`

The implementation is complete, knowledge graph is fully synchronized, and all changes have been committed to the repository.
"""
)

# PHASE 4: Record Final Session State
[KG Update] Recording complete session results with entity mappings:
mcp__isaac__write_neo4j_cypher(query="""
  MATCH (s:Session {id: $session_id})
  SET s.status = 'completed',
      s.completed_at = datetime(),
      s.final_commit_sha = $commit_sha,
      s.kg_sync_completed = true,
      s.validation_passed = true,
      s.entities_created = $entity_summary,
      s.relationships_mapped = $relationship_summary
  
  // Create audit trail
  CREATE (audit:AuditLog {
    session_id: s.id,
    timestamp: datetime(),
    action: 'session_completed',
    kg_state: $kg_state_snapshot,
    validation_results: $validation_results
  })
  CREATE (s)-[:HAS_AUDIT]->(audit)
  
  RETURN s
""", params={
  "session_id": session_id, 
  "commit_sha": commit_sha,
  "entity_summary": entity_counts,
  "relationship_summary": relationship_counts,
  "kg_state_snapshot": graph_snapshot,
  "validation_results": validation_results
})

[Git Operations]
✓ All changes staged  
✓ Commit: "feat: add medication history timeline to patient detail page"
✓ Commit includes KG sync metadata

[GitHub] Closing all related issues...

# Close the ISAAC-created issue
mcp__github-official__update_issue(
  owner="MPIsaac-Syn",
  repo="pulsetrack",
  issue_number=session_data.github_issue_number,
  state="closed",
  body=original_body + f"\n\n**Status**: ✅ Completed successfully\n**Commit**: {commit_sha}\n**KG Sync**: ✅ Fully synchronized"
)

# Close any user-referenced issues
for issue_num in session_data.referenced_issues:
    try:
        # Add completion comment to referenced issue
        mcp__github-official__add_issue_comment(
            owner="MPIsaac-Syn",
            repo="pulsetrack",
            issue_number=issue_num,
            body=f"""### ✅ Resolved by ISAAC Session

This issue was resolved in ISAAC session #{session_data.github_issue_number}

**Commit**: {commit_sha}
**Summary**: {session_data.task}

See full implementation details: #{session_data.github_issue_number}
"""
        )
        
        # Close the referenced issue
        mcp__github-official__update_issue(
            owner="MPIsaac-Syn",
            repo="pulsetrack",
            issue_number=issue_num,
            state="closed"
        )
        print(f"✓ Closed referenced issue #{issue_num}")
    except Exception as e:
        print(f"⚠️ Could not close issue #{issue_num}: {e}")

🎉 Session complete! Knowledge graph fully synchronized and all GitHub issues updated.
```

---

### 🚫 Critical Rules for Claude

1. **NEVER write code without KG validation**
2. **NEVER guess function names or paths**  
3. **ALWAYS query KG before imports**
4. **ALWAYS update KG after changes**
5. **ALWAYS create GitHub issue at session start**
6. **ALWAYS parse task for referenced issues (e.g., #123, fixes #456)**
7. **ALWAYS update GitHub issue with progress**
8. **ALWAYS close ALL relevant GitHub issues at session end (created + referenced)**
9. **STOP and ask if KG returns no results**

### 🎯 Success Metrics
- Zero hallucinated functions
- 100% import path accuracy  
- All patterns recorded for reuse
- Complete audit trail in KG
- GitHub issue properly tracked and closed
- Public visibility of progress for team
- No manual corrections needed

---

## Architecture Overview

**Tech Stack:**
- **Backend**: FastAPI, SQLAlchemy, PostgreSQL (with pgvector), Redis, Poetry
- **Frontend**: React 19.0.0, TypeScript ~5.7.2, Vite 6.2.0, TailwindCSS 4.0.17
- **AI/ML**: OpenAI API, RAG system, Intent recognition
- **DevOps**: Docker Compose, Yarn workspaces (monorepo)

**Key Components:**
- **Three Frontend Apps**: Patient (5174), Clinician (5173), Admin (5175)  
- **Authentication**: Clerk-based JWT
- **AI Chat System**: Multi-modal conversations with RAG
- **Intent Pipeline**: Natural language → API operations
- **Action Framework**: Executes scheduling, logging, requests

## Essential Backend Commands
```bash
# Database migrations
docker-compose exec backend alembic upgrade head  # Apply migrations
docker-compose exec backend alembic revision --autogenerate -m "Description"  # Create migration

# Testing
docker-compose exec backend pytest  # Run all tests
docker-compose exec backend pytest tests/unit/  # Unit tests only
docker-compose exec backend pytest -v  # Verbose output

# Linting and formatting
docker-compose exec backend black app/ tests/  # Format code
docker-compose exec backend ruff check app/ tests/  # Check linting
docker-compose exec backend mypy app/  # Type checking
```

## 🚨 API Testing Note
**IMPORTANT**: `curl` commands do not work in the development environment due to JWT token authentication required on all API endpoints. 

**Alternatives for API testing**:
- Use the frontend applications (patient/clinician/admin) for authenticated requests
- Write pytest test scripts that handle authentication properly
- Use the existing test scripts in `backend/scripts/` directory
- Access API documentation via the running FastAPI docs at `http://localhost:8000/docs`

**Example test script approach**:
```bash
# Instead of: curl http://localhost:8000/api/v1/patients
# Use: docker-compose exec backend python scripts/test_patients_api.py
```

## Essential Frontend Commands
```bash
# Monorepo root commands:
yarn install  # Install all workspace dependencies

# Individual frontend app commands:
cd frontend-patient  # or frontend-clinician, frontend-admin
yarn dev  # Development server
yarn build  # Production build
yarn lint  # Linting
```

## Docker Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Database access
docker-compose exec db psql -U pulsetrack_user -d pulsetrack_dev
# Password: supersecretlocalpassword

# Rebuild specific service
docker-compose build backend
docker-compose up -d backend
```

## Environment Variables

### Backend (.env)
```
DATABASE_URL=postgresql://user:pass@localhost/pulsetrack
CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
OPENAI_API_KEY=sk-...
REDIS_URL=redis://localhost:6379/0
```

### Frontend (.env.local)
```
VITE_CLERK_PUBLISHABLE_KEY=pk_test_...
VITE_API_BASE_URL=http://localhost:8000/api/v1
```

---

## Development Workflow with ISAAC

1. **Start working**: `/isaac-begin-session "your task description"`
2. **Let Claude Code do everything**: Code, test, validate, iterate
3. **End when satisfied**: `/isaac-end-session`
4. **All changes are verified and committed automatically**

This creates a completely automated, hallucination-free development experience where Claude Code works within verified constraints and maintains perfect accuracy.
