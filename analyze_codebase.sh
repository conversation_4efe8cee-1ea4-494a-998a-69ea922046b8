#!/bin/bash

# Script to analyze PulseTrack codebase by file type
# Excludes: node_modules, .git, __pycache__, dist, build directories

PROJECT_ROOT="/Users/<USER>/Documents/projects/pulsetrack"
cd "$PROJECT_ROOT"

echo "=== PulseTrack Codebase Analysis ==="
echo "Date: $(date)"
echo "=================================="
echo

# Function to count lines for a specific file pattern
count_lines() {
    local pattern="$1"
    local description="$2"
    
    # Find files matching pattern, excluding specified directories
    local files=$(find . \
        -name "$pattern" \
        -not -path "*/node_modules/*" \
        -not -path "*/.git/*" \
        -not -path "*/__pycache__/*" \
        -not -path "*/dist/*" \
        -not -path "*/build/*" \
        -not -path "*/packages/*" \
        -not -path "*/.next/*" \
        -not -path "*/coverage/*" \
        -not -path "*/.cache/*" \
        -not -path "*/.venv/*" \
        -not -path "*/venv/*" \
        -not -path "*/env/*" \
        -not -path "*/.env/*" \
        -not -path "*/.pytest_cache/*" \
        -not -path "*/.mypy_cache/*" \
        -not -path "*/.ruff_cache/*" \
        -type f)
    
    if [ -z "$files" ]; then
        echo "$description: 0 files, 0 lines"
        return
    fi
    
    local file_count=$(echo "$files" | wc -l | tr -d ' ')
    local line_count=$(echo "$files" | xargs wc -l 2>/dev/null | tail -n 1 | awk '{print $1}')
    
    if [ -z "$line_count" ]; then
        line_count=0
    fi
    
    printf "%-30s %6d files, %8d lines\n" "$description:" "$file_count" "$line_count"
}

# Python files
echo "Backend (Python):"
echo "-----------------"
count_lines "*.py" "Python (.py)"
echo

# TypeScript/JavaScript files
echo "Frontend (TypeScript/JavaScript):"
echo "---------------------------------"
count_lines "*.ts" "TypeScript (.ts)"
count_lines "*.tsx" "TypeScript React (.tsx)"
count_lines "*.js" "JavaScript (.js)"
count_lines "*.jsx" "JavaScript React (.jsx)"
echo

# Style files
echo "Styling:"
echo "--------"
count_lines "*.css" "CSS (.css)"
count_lines "*.scss" "SCSS (.scss)"
count_lines "*.sass" "SASS (.sass)"
echo

# Configuration and data files
echo "Configuration/Data:"
echo "-------------------"
count_lines "*.json" "JSON (.json)"
count_lines "*.yaml" "YAML (.yaml)"
count_lines "*.yml" "YAML (.yml)"
count_lines "*.toml" "TOML (.toml)"
echo

# Documentation
echo "Documentation:"
echo "--------------"
count_lines "*.md" "Markdown (.md)"
count_lines "*.txt" "Text (.txt)"
count_lines "*.rst" "reStructuredText (.rst)"
echo

# Other important files
echo "Other Files:"
echo "------------"
count_lines "Dockerfile*" "Dockerfiles"
count_lines "*.sh" "Shell scripts (.sh)"
count_lines "*.sql" "SQL (.sql)"
count_lines "*.tex" "LaTeX (.tex)"
echo

# Summary by directory
echo "=================================="
echo "Top-level directory breakdown:"
echo "=================================="

# Backend
if [ -d "backend" ]; then
    backend_lines=$(find backend \
        -type f \( -name "*.py" \) \
        -not -path "*/__pycache__/*" \
        -not -path "*/venv/*" \
        -not -path "*/.venv/*" \
        -not -path "*/.pytest_cache/*" \
        -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    echo "backend/:                    $backend_lines lines (Python)"
fi

# Frontend apps
for frontend in frontend-patient frontend-clinician frontend-admin; do
    if [ -d "$frontend" ]; then
        frontend_lines=$(find "$frontend" \
            -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) \
            -not -path "*/node_modules/*" \
            -not -path "*/dist/*" \
            -not -path "*/build/*" \
            -not -path "*/.next/*" \
            -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
        printf "%-28s %d lines (TS/TSX/JS/JSX)\n" "$frontend/:" "$frontend_lines"
    fi
done

# Documentation
if [ -d "docs" ]; then
    docs_lines=$(find docs -name "*.md" -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    echo "docs/:                       $docs_lines lines (Markdown)"
fi

# Knowledge base
if [ -d "knowledge" ]; then
    knowledge_lines=$(find knowledge -name "*.md" -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    echo "knowledge/:                  $knowledge_lines lines (Markdown)"
fi

echo
echo "=================================="
echo "Grand Total (excluding dependencies):"
echo "=================================="

# Calculate grand total
total_lines=$(find . \
    -type f \( \
        -name "*.py" -o \
        -name "*.ts" -o -name "*.tsx" -o \
        -name "*.js" -o -name "*.jsx" -o \
        -name "*.css" -o -name "*.scss" -o \
        -name "*.json" -o -name "*.yaml" -o -name "*.yml" -o \
        -name "*.md" -o -name "*.toml" -o \
        -name "Dockerfile*" -o -name "*.sh" -o \
        -name "*.sql" -o -name "*.tex" \
    \) \
    -not -path "*/node_modules/*" \
    -not -path "*/.git/*" \
    -not -path "*/__pycache__/*" \
    -not -path "*/dist/*" \
    -not -path "*/build/*" \
    -not -path "*/packages/*" \
    -not -path "*/.next/*" \
    -not -path "*/coverage/*" \
    -not -path "*/.cache/*" \
    -not -path "*/.venv/*" \
    -not -path "*/venv/*" \
    -not -path "*/env/*" \
    -not -path "*/.env/*" \
    -not -path "*/.pytest_cache/*" \
    -not -path "*/.mypy_cache/*" \
    -not -path "*/.ruff_cache/*" \
    -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')

echo "Total lines of code: $total_lines"