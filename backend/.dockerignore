# Git files
.git
.gitignore

# Environment files
.env*

# Python cache and virtual environment
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/

# Build and distribution artifacts
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Pytest cache and coverage reports
.pytest_cache/
.coverage
coverage.xml
htmlcov/

# IDE files
.idea/
.vscode/

# Log files
*.log

# Test and result directories (assuming they are not needed in production image)
tests/
results/

# Other specific files/dirs if needed
# e.g., local_settings.py