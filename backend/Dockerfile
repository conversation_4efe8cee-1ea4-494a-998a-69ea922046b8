# ---- Builder Stage ----
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set working directory
WORKDIR /app

# Install uv (faster package installer)
RUN pip install uv

# Copy only requirements to leverage Docker cache
COPY requirements.txt .

# Install dependencies using uv
# Use --system to install globally in this stage
RUN uv pip install --system --no-cache-dir -r requirements.txt

# ---- Final Stage ----
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV APP_USER=appuser
ENV APP_HOME=/home/<USER>

# Create a non-root user and group
RUN groupadd -r $APP_USER && useradd --no-log-init -r -g $APP_USER $APP_USER

# Create the application directory owned by the non-root user
RUN mkdir -p $APP_HOME/app && chown -R $APP_USER:$APP_USER $APP_HOME
WORKDIR $APP_HOME/app

# Copy installed packages from the builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy the application code (respecting .dockerignore) into the WORKDIR
# Ensure ownership is set correctly
COPY --chown=$APP_USER:$APP_USER . .

# Switch to the non-root user
USER $APP_USER

# Expose the application port
EXPOSE 8000

# Define environment variable for the port (can be overridden)
ENV PORT 8000
# Define environment variable for number of workers (can be overridden)
ENV WORKERS 4

# Run uvicorn server for production
# Use gunicorn as the process manager for uvicorn workers
# Use --forwarded-allow-ips='*' to trust X-Forwarded-* headers if behind a proxy
# The number of workers can be tuned based on server resources (e.g., 2 * num_cores + 1)
# Use shell form for CMD to allow environment variable substitution ($WORKERS, $PORT)
CMD gunicorn -k uvicorn.workers.UvicornWorker -w "$WORKERS" -b "0.0.0.0:$PORT" main:app --forwarded-allow-ips='*'