# PulseTrack Backend

The PulseTrack backend is a FastAPI-based application that powers the core functionality of the healthcare platform. It provides a RESTful API for patient management, clinician operations, clinic administration, and AI-powered medical information retrieval.

## Architecture

The backend follows a modern, modular architecture:

- **FastAPI Framework**: High-performance API framework with automatic OpenAPI documentation
- **SQLAlchemy ORM**: Object-relational mapping with PostgreSQL
- **Alembic**: Database migration management
- **Pydantic**: Data validation and settings management
- **Redis**: Caching and message queue for embeddings
- **Qdrant**: Vector database for similarity search

## Key Features

- **Authentication System**: Role-based JWT authentication with Clerk integration
- **RAG-powered Chat**: Retrieval-augmented generation for medical context
- **Multi-tenant Data Model**: Clinic-based isolation for healthcare compliance
- **Event System**: Event-driven architecture for asynchronous operations
- **Semantic Caching**: Performance optimization for AI responses
- **Content Processing Pipeline**: Medical document chunking and embedding
- **Audit Logging**: Comprehensive logging for compliance requirements

## Project Structure

```
backend/
├── alembic/              # Database migrations
├── app/                  # Main application code
│   ├── api/              # API route definitions and dependencies
│   ├── core/             # Core utilities and configuration
│   │   ├── constants/    # Application constants
│   │   └── llm/          # LLM provider abstractions
│   ├── crud/             # Database CRUD operations
│   ├── db/               # Database connection and session management
│   ├── models/           # SQLAlchemy ORM models
│   ├── schemas/          # Pydantic schemas for request/response
│   ├── services/         # Business logic services
│   │   └── retrieval/    # RAG system components
│   └── utils/            # Utility functions
├── scripts/              # Management and initialization scripts
└── tests/                # Test suite
```

## Getting Started

### Prerequisites

- Python 3.11+
- PostgreSQL 14+
- Redis
- Qdrant
- Docker and Docker Compose (recommended)

### Environment Setup

1. Clone the repository:
```bash
git clone https://github.com/yourusername/pulsetrack.git
cd pulsetrack/backend
```

2. Set up environment variables (copy `.env.example` to `.env` and edit):
```bash
cp .env.example .env
```

3. Start the required services with Docker Compose:
```bash
docker-compose up -d postgres redis qdrant
```

### Using Poetry (Recommended)

1. Install Poetry if not already installed:
```bash
pip install poetry
```

2. Install dependencies:
```bash
poetry install
```

3. Activate the virtual environment:
```bash
poetry shell
```

### Using pip

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

### Database Setup

1. Run database migrations:
```bash
alembic upgrade head
```

2. Initialize collections in Qdrant:
```bash
python scripts/init_qdrant_collections.py
```

### Running the Application

1. Start the API server:
```bash
uvicorn app.main:app --reload
```

2. Start the embedding worker in a separate terminal:
```bash
python -m app.cli.cli embedding worker
```

3. Access the API documentation at: http://localhost:8000/docs

## Development

### Creating Migrations

After modifying SQLAlchemy models, create a new migration:

```bash
alembic revision --autogenerate -m "Description of changes"
```

### Running Tests

```bash
pytest
```

### Managing Embeddings

See [README_EMBEDDING.md](README_EMBEDDING.md) for detailed information on the embedding pipeline.

## API Documentation

The API documentation is available at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Related Repositories

- [Frontend - Patient](../frontend-patient/README.md)
- [Frontend - Clinician](../frontend-clinician/README.md)
- [Frontend - Admin](../frontend-admin/README.md)

## License

Proprietary - All rights reserved.