import os
from logging.config import fileConfig

# --- Load .env file ---
from dotenv import load_dotenv
from sqlalchemy import engine_from_config, pool

from alembic import context

dotenv_path = os.path.join(os.path.dirname(__file__), "..", "..", ".env")
print(f"Attempting to load .env file from: {dotenv_path}")
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
    print(".env file loaded successfully.")
else:
    print(
        f"Warning: .env file not found at {dotenv_path}. DATABASE_URL might not be set."
    )
# --- End .env loading ---


# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# --- Application models ---
# Import Base first
from app.db.base import Base  # noqa: E402
from app.models.access_code import AccessCode  # noqa: F401, E402
from app.models.appointment import Appointment  # noqa: F401, E402

# Import all models here so Base.metadata is populated
from app.models.audit_log import AuditLog  # noqa: F401, E402
from app.models.chat_message import ChatMessage  # noqa: F401, E402
from app.models.clinic import Clinic  # noqa: F401, E402
from app.models.clinician import Clinician  # noqa: F401, E402
from app.models.content import Content  # noqa: F401, E402
from app.models.content_chunk import ContentChunk  # noqa: F401, E402
from app.models.event_log import EventLog  # noqa: F401, E402
from app.models.medication_request import MedicationRequest  # noqa: F401, E402
from app.models.patient import Patient  # noqa: F401, E402
from app.models.scraped_page import ScrapedPage  # noqa: F401, E402
from app.models.side_effect_report import SideEffectReport  # noqa: F401, E402
from app.models.template import Template, TemplateRole  # noqa: F401, E402
from app.models.weight_log import WeightLog  # noqa: F401, E402

target_metadata = Base.metadata
# target_metadata = None # Use this if you don't want autogenerate based on models

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = os.getenv("DATABASE_URL")
    if not url:
        raise ValueError(
            "DATABASE_URL environment variable not set or .env file not loaded."
        )

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        raise ValueError(
            "DATABASE_URL environment variable not set or .env file not loaded for online migration."
        )

    config.set_main_option("sqlalchemy.url", db_url)

    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
