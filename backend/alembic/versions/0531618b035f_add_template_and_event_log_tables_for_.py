"""Add template and event log tables for LLM-driven API actions

Revision ID: 0531618b035f
Revises: 20250415_105000
Create Date: 2025-04-20 16:21:40.055106

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0531618b035f"
down_revision: Union[str, None] = "20250415_105000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "event_logs",
        sa.Column("actor_user_id", sa.String(), nullable=False),
        sa.Column("actor_role", sa.String(length=50), nullable=False),
        sa.Column("action", sa.String(length=255), nullable=False),
        sa.Column("target_resource_type", sa.String(length=100), nullable=True),
        sa.Column("target_resource_id", sa.String(), nullable=True),
        sa.Column("outcome", sa.String(length=50), nullable=False),
        sa.Column("details", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("source_ip", sa.String(length=100), nullable=True),
        sa.Column("original_input", sa.String(), nullable=True),
        sa.Column("input_type", sa.String(length=50), nullable=False),
        sa.Column("extracted_intent", sa.String(length=255), nullable=True),
        sa.Column(
            "extracted_parameters",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
        sa.Column("executed_api_action", sa.String(length=255), nullable=True),
        sa.Column("clinic_id", sa.UUID(), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_event_logs_action"), "event_logs", ["action"], unique=False
    )
    op.create_index(
        "ix_event_logs_actor_created",
        "event_logs",
        ["actor_user_id", "created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_event_logs_actor_role"), "event_logs", ["actor_role"], unique=False
    )
    op.create_index(
        op.f("ix_event_logs_actor_user_id"),
        "event_logs",
        ["actor_user_id"],
        unique=False,
    )
    op.create_index(
        "ix_event_logs_clinic_created",
        "event_logs",
        ["clinic_id", "created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_event_logs_clinic_id"), "event_logs", ["clinic_id"], unique=False
    )
    op.create_index(op.f("ix_event_logs_id"), "event_logs", ["id"], unique=False)
    op.create_index(
        op.f("ix_event_logs_outcome"), "event_logs", ["outcome"], unique=False
    )
    op.create_index(
        op.f("ix_event_logs_target_resource_id"),
        "event_logs",
        ["target_resource_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_event_logs_target_resource_type"),
        "event_logs",
        ["target_resource_type"],
        unique=False,
    )
    op.create_table(
        "templates",
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("version", sa.String(length=50), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("system_prompt", sa.Text(), nullable=False),
        sa.Column(
            "default_settings", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column("actions", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("clinic_id", sa.UUID(), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["clinic_id"], ["clinics.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_templates_clinic_id"), "templates", ["clinic_id"], unique=False
    )
    op.create_index(op.f("ix_templates_id"), "templates", ["id"], unique=False)
    op.create_index(
        op.f("ix_templates_is_active"), "templates", ["is_active"], unique=False
    )
    op.create_index(op.f("ix_templates_name"), "templates", ["name"], unique=False)
    op.create_table(
        "template_roles",
        sa.Column("template_id", sa.UUID(), nullable=False),
        sa.Column("role", sa.String(length=50), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["template_id"], ["templates.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("template_id", "role", name="uq_template_role"),
    )
    op.create_index(
        op.f("ix_template_roles_id"), "template_roles", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_template_roles_template_id"),
        "template_roles",
        ["template_id"],
        unique=False,
    )
    op.drop_index("ix_user_settings_key", table_name="user_settings")
    op.drop_index("ix_user_settings_user_id", table_name="user_settings")
    op.drop_table("user_settings")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user_settings",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("user_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("key", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("value", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["patients.id"], name="user_settings_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="user_settings_pkey"),
        sa.UniqueConstraint("user_id", "key", name="uq_user_setting"),
    )
    op.create_index(
        "ix_user_settings_user_id", "user_settings", ["user_id"], unique=False
    )
    op.create_index("ix_user_settings_key", "user_settings", ["key"], unique=False)
    op.drop_index(op.f("ix_template_roles_template_id"), table_name="template_roles")
    op.drop_index(op.f("ix_template_roles_id"), table_name="template_roles")
    op.drop_table("template_roles")
    op.drop_index(op.f("ix_templates_name"), table_name="templates")
    op.drop_index(op.f("ix_templates_is_active"), table_name="templates")
    op.drop_index(op.f("ix_templates_id"), table_name="templates")
    op.drop_index(op.f("ix_templates_clinic_id"), table_name="templates")
    op.drop_table("templates")
    op.drop_index(op.f("ix_event_logs_target_resource_type"), table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_target_resource_id"), table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_outcome"), table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_id"), table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_clinic_id"), table_name="event_logs")
    op.drop_index("ix_event_logs_clinic_created", table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_actor_user_id"), table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_actor_role"), table_name="event_logs")
    op.drop_index("ix_event_logs_actor_created", table_name="event_logs")
    op.drop_index(op.f("ix_event_logs_action"), table_name="event_logs")
    op.drop_table("event_logs")
    # ### end Alembic commands ###
