"""Add notes column to clinic_medication_association

Revision ID: 089db6fbf124
Revises: 83bf5158bdf8
Create Date: 2025-04-14 14:01:15.736711

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "089db6fbf124"
down_revision: Union[str, None] = "83bf5158bdf8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "clinic_medication_association",
        sa.Column("notes", sa.String(length=500), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("clinic_medication_association", "notes")
    # ### end Alembic commands ###
