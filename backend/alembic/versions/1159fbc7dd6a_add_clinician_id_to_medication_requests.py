"""add_clinician_id_to_medication_requests

Revision ID: 1159fbc7dd6a
Revises: 0d6a3b65beb6
Create Date: 2024-04-03 18:40:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1159fbc7dd6a"
down_revision: Union[str, None] = "0d6a3b65beb6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add clinician_id column
    op.add_column(
        "medication_requests",
        sa.Column("clinician_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.create_index(
        op.f("ix_medication_requests_clinician_id"),
        "medication_requests",
        ["clinician_id"],
        unique=False,
    )
    op.create_foreign_key(
        "fk_medication_requests_clinician_id",
        "medication_requests",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )

    # Add resolved_at column
    op.add_column(
        "medication_requests",
        sa.Column("resolved_at", sa.DateTime(timezone=True), nullable=True),
    )


def downgrade() -> None:
    # Remove resolved_at column
    op.drop_column("medication_requests", "resolved_at")

    # Remove clinician_id column and its constraints
    op.drop_constraint(
        "fk_medication_requests_clinician_id", "medication_requests", type_="foreignkey"
    )
    op.drop_index(
        op.f("ix_medication_requests_clinician_id"), table_name="medication_requests"
    )
    op.drop_column("medication_requests", "clinician_id")
