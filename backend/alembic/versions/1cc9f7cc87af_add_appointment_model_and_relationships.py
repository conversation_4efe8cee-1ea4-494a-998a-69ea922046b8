"""Add Appointment model and relationships

Revision ID: 1cc9f7cc87af
Revises: 898dfc99d075
Create Date: 2025-04-04 12:31:52.261715

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1cc9f7cc87af"
down_revision: Union[str, None] = "898dfc99d075"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "appointments",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("clinician_id", sa.UUID(), nullable=False),
        sa.Column("appointment_datetime", sa.DateTime(timezone=True), nullable=False),
        sa.Column("clinician_notes", sa.Text(), nullable=True),
        sa.Column("patient_notes", sa.Text(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("appointment_type", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinician_id"],
            ["clinicians.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_appointments_appointment_datetime"),
        "appointments",
        ["appointment_datetime"],
        unique=False,
    )
    op.create_index(
        op.f("ix_appointments_clinician_id"),
        "appointments",
        ["clinician_id"],
        unique=False,
    )
    op.create_index(op.f("ix_appointments_id"), "appointments", ["id"], unique=False)
    op.create_index(
        op.f("ix_appointments_patient_id"), "appointments", ["patient_id"], unique=False
    )
    op.create_index(
        op.f("ix_appointments_status"), "appointments", ["status"], unique=False
    )
    op.create_table(
        "lab_results",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("clinician_id", sa.UUID(), nullable=False),
        sa.Column("test_name", sa.String(), nullable=False),
        sa.Column("result_date", sa.Date(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("result_summary", sa.Text(), nullable=True),
        sa.Column("reviewed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("reviewed_by_clinician_id", sa.UUID(), nullable=True),
        sa.Column("source_document_url", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinician_id"],
            ["clinicians.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.ForeignKeyConstraint(
            ["reviewed_by_clinician_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_lab_results_clinician_id", "lab_results", ["clinician_id"], unique=False
    )
    op.create_index(
        op.f("ix_lab_results_patient_id"), "lab_results", ["patient_id"], unique=False
    )
    op.create_index(
        "ix_lab_results_result_date", "lab_results", ["result_date"], unique=False
    )
    op.create_index("ix_lab_results_status", "lab_results", ["status"], unique=False)
    op.create_table(
        "notes",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("clinician_id", sa.UUID(), nullable=False),
        sa.Column("title", sa.String(), nullable=True),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column("note_type", sa.String(), nullable=True),
        sa.Column("associated_entity_type", sa.String(), nullable=True),
        sa.Column("associated_entity_id", sa.UUID(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinician_id"],
            ["clinicians.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_notes_clinician_id"), "notes", ["clinician_id"], unique=False
    )
    op.create_index(op.f("ix_notes_note_type"), "notes", ["note_type"], unique=False)
    op.create_index("ix_notes_patient_id", "notes", ["patient_id"], unique=False)
    op.create_table(
        "notifications",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("recipient_clinician_id", sa.UUID(), nullable=False),
        sa.Column("notification_type", sa.String(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("message", sa.Text(), nullable=True),
        sa.Column("related_entity_type", sa.String(), nullable=True),
        sa.Column("related_entity_id", sa.UUID(), nullable=True),
        sa.Column("is_read", sa.Boolean(), nullable=False),
        sa.Column("read_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["recipient_clinician_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_notifications_is_read"), "notifications", ["is_read"], unique=False
    )
    op.create_index(
        "ix_notifications_notification_type",
        "notifications",
        ["notification_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_notifications_recipient_clinician_id"),
        "notifications",
        ["recipient_clinician_id"],
        unique=False,
    )
    op.create_table(
        "patient_alerts",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("alert_type", sa.String(), nullable=False),
        sa.Column("severity", sa.String(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("source", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("resolved_at", sa.DateTime(), nullable=True),
        sa.Column("resolved_by_clinician_id", sa.UUID(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.ForeignKeyConstraint(
            ["resolved_by_clinician_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_patient_alerts_alert_type", "patient_alerts", ["alert_type"], unique=False
    )
    op.create_index(
        op.f("ix_patient_alerts_patient_id"),
        "patient_alerts",
        ["patient_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_patient_alerts_severity"), "patient_alerts", ["severity"], unique=False
    )
    op.create_index(
        "ix_patient_alerts_status", "patient_alerts", ["status"], unique=False
    )
    op.alter_column(
        "access_codes", "clinician_id", existing_type=sa.UUID(), nullable=False
    )
    op.create_index(
        "ix_access_codes_clinician_id_created_at",
        "access_codes",
        ["clinician_id", "created_at"],
        unique=False,
    )
    op.add_column(
        "chat_messages", sa.Column("is_read_by_clinician", sa.Boolean(), nullable=False)
    )
    op.add_column(
        "chat_messages", sa.Column("read_by_clinician_at", sa.DateTime(), nullable=True)
    )
    op.create_index(
        op.f("ix_chat_messages_is_read_by_clinician"),
        "chat_messages",
        ["is_read_by_clinician"],
        unique=False,
    )
    op.create_index(
        "ix_chat_messages_unread_patient",
        "chat_messages",
        ["patient_id", "sender_type", "is_read_by_clinician"],
        unique=False,
        postgresql_where=sa.text(
            "sender_type = 'PATIENT' AND is_read_by_clinician = false"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_chat_messages_unread_patient",
        table_name="chat_messages",
        postgresql_where=sa.text(
            "sender_type = 'PATIENT' AND is_read_by_clinician = false"
        ),
    )
    op.drop_index(
        op.f("ix_chat_messages_is_read_by_clinician"), table_name="chat_messages"
    )
    op.drop_column("chat_messages", "read_by_clinician_at")
    op.drop_column("chat_messages", "is_read_by_clinician")
    op.drop_index("ix_access_codes_clinician_id_created_at", table_name="access_codes")
    op.alter_column(
        "access_codes", "clinician_id", existing_type=sa.UUID(), nullable=True
    )
    op.drop_index("ix_patient_alerts_status", table_name="patient_alerts")
    op.drop_index(op.f("ix_patient_alerts_severity"), table_name="patient_alerts")
    op.drop_index(op.f("ix_patient_alerts_patient_id"), table_name="patient_alerts")
    op.drop_index("ix_patient_alerts_alert_type", table_name="patient_alerts")
    op.drop_table("patient_alerts")
    op.drop_index(
        op.f("ix_notifications_recipient_clinician_id"), table_name="notifications"
    )
    op.drop_index("ix_notifications_notification_type", table_name="notifications")
    op.drop_index(op.f("ix_notifications_is_read"), table_name="notifications")
    op.drop_table("notifications")
    op.drop_index("ix_notes_patient_id", table_name="notes")
    op.drop_index(op.f("ix_notes_note_type"), table_name="notes")
    op.drop_index(op.f("ix_notes_clinician_id"), table_name="notes")
    op.drop_table("notes")
    op.drop_index("ix_lab_results_status", table_name="lab_results")
    op.drop_index("ix_lab_results_result_date", table_name="lab_results")
    op.drop_index(op.f("ix_lab_results_patient_id"), table_name="lab_results")
    op.drop_index("ix_lab_results_clinician_id", table_name="lab_results")
    op.drop_table("lab_results")
    op.drop_index(op.f("ix_appointments_status"), table_name="appointments")
    op.drop_index(op.f("ix_appointments_patient_id"), table_name="appointments")
    op.drop_index(op.f("ix_appointments_id"), table_name="appointments")
    op.drop_index(op.f("ix_appointments_clinician_id"), table_name="appointments")
    op.drop_index(
        op.f("ix_appointments_appointment_datetime"), table_name="appointments"
    )
    op.drop_table("appointments")
    # ### end Alembic commands ###
