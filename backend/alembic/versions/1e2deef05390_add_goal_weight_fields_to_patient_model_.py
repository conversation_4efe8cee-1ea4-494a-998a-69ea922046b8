"""Add goal weight fields to patient model for tracking weight goals

Revision ID: 1e2deef05390
Revises: 28ac14b8f911
Create Date: 2025-05-30 02:43:31.715382

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1e2deef05390'
down_revision: Union[str, None] = '28ac14b8f911'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('patients', sa.Column('goal_weight_kg', sa.Float(), nullable=True))
    op.add_column('patients', sa.Column('goal_weight_date', sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('patients', 'goal_weight_date')
    op.drop_column('patients', 'goal_weight_kg')
    # ### end Alembic commands ###
