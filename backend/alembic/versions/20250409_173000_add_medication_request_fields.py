"""Add medication request fields

Revision ID: 20250409_173000
Revises: 9038a45d0e37
Create Date: 2025-04-09 17:30:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250409_173000"
down_revision: Union[str, None] = "9038a45d0e37"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add new columns to medication_requests table
    op.add_column(
        "medication_requests", sa.Column("dosage", sa.String(), nullable=True)
    )
    op.add_column(
        "medication_requests", sa.Column("frequency", sa.String(), nullable=True)
    )
    op.add_column(
        "medication_requests", sa.Column("duration", sa.String(), nullable=True)
    )

    # Add indexes for the new columns
    op.create_index("ix_medication_requests_dosage", "medication_requests", ["dosage"])
    op.create_index(
        "ix_medication_requests_frequency", "medication_requests", ["frequency"]
    )
    op.create_index(
        "ix_medication_requests_duration", "medication_requests", ["duration"]
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Remove indexes first
    op.drop_index("ix_medication_requests_duration")
    op.drop_index("ix_medication_requests_frequency")
    op.drop_index("ix_medication_requests_dosage")

    # Remove columns
    op.drop_column("medication_requests", "duration")
    op.drop_column("medication_requests", "frequency")
    op.drop_column("medication_requests", "dosage")
