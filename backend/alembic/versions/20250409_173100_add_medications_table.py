"""Add medications table

Revision ID: 20250409_173100
Revises: 20250409_173000
Create Date: 2025-04-09 17:31:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250409_173100"
down_revision: Union[str, None] = "20250409_173000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create medications table
    op.create_table(
        "medications",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("dosage_guidelines", sa.Text(), nullable=True),
        sa.Column("common_side_effects", sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_medications_id", "medications", ["id"])
    op.create_index("ix_medications_name", "medications", ["name"], unique=True)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index("ix_medications_name")
    op.drop_index("ix_medications_id")
    op.drop_table("medications")
