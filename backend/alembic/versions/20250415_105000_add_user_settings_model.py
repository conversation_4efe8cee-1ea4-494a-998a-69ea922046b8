"""Add UserSetting model

Revision ID: 20250415_105000
Revises: 1ec992ec9d23
Create Date: 2025-04-15 10:50:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250415_105000"
down_revision: Union[str, None] = "1ec992ec9d23"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "user_settings",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("user_type", sa.String(), nullable=False),
        sa.Column("key", sa.String(), nullable=False),
        sa.Column("value", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "user_type", "key", name="uq_user_setting"),
    )

    op.create_index(
        op.f("ix_user_settings_key"), "user_settings", ["key"], unique=False
    )
    op.create_index(
        op.f("ix_user_settings_user_id"), "user_settings", ["user_id"], unique=False
    )
    op.create_index(
        op.f("ix_user_settings_user_type"), "user_settings", ["user_type"], unique=False
    )
    op.create_index(
        op.f("ix_user_settings_user_id_type"),
        "user_settings",
        ["user_id", "user_type"],
        unique=False,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_user_settings_user_id_type"), table_name="user_settings")
    op.drop_index(op.f("ix_user_settings_user_type"), table_name="user_settings")
    op.drop_index(op.f("ix_user_settings_user_id"), table_name="user_settings")
    op.drop_index(op.f("ix_user_settings_key"), table_name="user_settings")
    op.drop_table("user_settings")
