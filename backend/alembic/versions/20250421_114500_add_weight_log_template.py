"""add_weight_log_template

Revision ID: 20250421_114500
Revises: 20250415_105000
Create Date: 2025-04-21 11:45:00.000000

"""

import uuid
from datetime import datetime

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "20250421_114500"
down_revision = "20250415_105000"  # Update to the appropriate previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create table references (no actual tables are created, just references for the data insertion)
    templates_table = table(
        "templates",
        column("id", postgresql.UUID),
        column("name", sa.String),
        column("description", sa.Text),
        column("system_prompt", sa.Text),
        column("is_active", sa.<PERSON>),
        column("version", sa.Integer),
        column("default_settings", postgresql.JSONB),
        column("clinic_id", postgresql.UUID),
        column("actions", postgresql.JSONB),
        column("created_at", sa.DateTime),
        column("updated_at", sa.DateTime),
    )

    template_roles_table = table(
        "template_roles",
        column("id", postgresql.UUID),
        column("template_id", postgresql.UUID),
        column("role", sa.String),
        column("created_at", sa.DateTime),
        column("updated_at", sa.DateTime),
    )

    # Current timestamp for created_at and updated_at
    current_time = datetime.utcnow()

    # Insert weight log template
    template_id = uuid.uuid4()
    weight_log_template = {
        "id": template_id,
        "name": "Patient Weight Logging",
        "description": "Template for patients to log their weight measurements through natural language",
        "system_prompt": """You are a medical assistant helping patients track their weight measurements.
Extract the weight value, unit (kg/lbs), and the date from the patient's input.
If the date is not specified, use today's date.

When extracting dates, convert any natural language descriptions (like 'yesterday', 'last Monday', etc.)
into a standardized YYYY-MM-DD format based on the current date.

Acceptable weight units include:
- Kilograms: kg, kgs, kilos, kilograms
- Pounds: lb, lbs, pounds

Extract any additional notes the patient provides about their weight measurement.""",
        "is_active": True,
        "version": 1,
        "default_settings": {"temperature": 0.2, "max_tokens": 512},
        "clinic_id": None,  # No clinic restriction - available to all
        "actions": [
            {
                "action_type": "weight_log_create",
                "description": "Log a new weight measurement",
                "parameters": [
                    {
                        "name": "weight_value",
                        "type": "float",
                        "required": True,
                        "description": "Weight value (numeric)",
                    },
                    {
                        "name": "unit",
                        "type": "string",
                        "required": True,
                        "description": "Weight unit (kg or lbs)",
                    },
                    {
                        "name": "log_date",
                        "type": "string",
                        "required": True,
                        "description": "Date of the weight measurement in YYYY-MM-DD format. If no date is specified by the user, use today's date.",
                    },
                    {
                        "name": "notes",
                        "type": "string",
                        "required": False,
                        "description": "Any additional notes about the weight measurement",
                    },
                ],
            }
        ],
        "created_at": current_time,
        "updated_at": current_time,
    }

    # Insert template
    op.bulk_insert(templates_table, [weight_log_template])

    # Associate template with patient role
    patient_role = {
        "id": uuid.uuid4(),
        "template_id": template_id,
        "role": "patient",
        "created_at": current_time,
        "updated_at": current_time,
    }

    # Insert template role
    op.bulk_insert(template_roles_table, [patient_role])


def downgrade() -> None:
    # Use data values to delete specific records
    op.execute(
        "DELETE FROM template_roles WHERE template_id IN (SELECT id FROM templates WHERE name = 'Patient Weight Logging')"
    )
    op.execute("DELETE FROM templates WHERE name = 'Patient Weight Logging'")
