"""Add message_route to chat_message table

Revision ID: 20250520_add_message_route
Revises: c2607f8641af
Create Date: 2025-05-20 12:00:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520_add_message_route"
down_revision: str = "c2607f8641af"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the enum type first
    op.execute("CREATE TYPE msg_route_type_enum AS ENUM ('patient', 'ai')")

    # Add the column using the new enum type
    op.add_column(
        "chat_messages",
        sa.Column(
            "message_route",
            sa.Enum("patient", "ai", name="msg_route_type_enum"),
            nullable=True,
        ),
    )


def downgrade() -> None:
    # Drop the column
    op.drop_column("chat_messages", "message_route")

    # Drop the enum type
    op.execute("DROP TYPE msg_route_type_enum")
