"""merge message_route with appointment_requests

Revision ID: 20250520_merge_heads
Revises: 20250520_add_message_route, a2ab82016c0f
Create Date: 2025-05-20 13:00:00.000000

"""

from collections.abc import Sequence
from typing import Union

# revision identifiers, used by Alembic.
revision: str = "20250520_merge_heads"
down_revision: Union[str, Sequence[str]] = (
    "20250520_add_message_route",
    "a2ab82016c0f",
)
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Merge branches."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
