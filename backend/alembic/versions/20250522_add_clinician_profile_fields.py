"""add bio credentials and years_experience to clinician model

Revision ID: 20250522_add_clinician_profile_fields
Revises: f8fcaca78508
Create Date: 2025-05-22 14:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_profile_fields_2025'
down_revision = '20250520_merge_heads'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('clinicians', sa.Column('bio', sa.Text(), nullable=True))
    op.add_column('clinicians', sa.Column('credentials', postgresql.ARRAY(sa.String()), nullable=True))
    op.add_column('clinicians', sa.Column('years_experience', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('clinicians', 'years_experience')
    op.drop_column('clinicians', 'credentials')
    op.drop_column('clinicians', 'bio')
    # ### end Alembic commands ###