"""Add clinician value to MessageRouteType enum

Revision ID: add_clinician_route_2025
Revises: add_profile_fields_2025
Create Date: 2025-05-22 15:00:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "add_clinician_route_2025"
down_revision: str = "add_profile_fields_2025"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add 'clinician' value to existing msg_route_type_enum
    op.execute("ALTER TYPE msg_route_type_enum ADD VALUE 'clinician'")


def downgrade() -> None:
    # Note: PostgreSQL doesn't support removing enum values directly
    # We would need to recreate the enum type and update the column
    # For simplicity, this downgrade recreates the enum without 'clinician'
    
    # Create a new enum type without 'clinician'
    op.execute("CREATE TYPE msg_route_type_enum_new AS ENUM ('patient', 'ai')")
    
    # Update the column to use the new enum type
    op.execute("""
        ALTER TABLE chat_messages 
        ALTER COLUMN message_route TYPE msg_route_type_enum_new 
        USING message_route::text::msg_route_type_enum_new
    """)
    
    # Drop the old enum type and rename the new one
    op.execute("DROP TYPE msg_route_type_enum")
    op.execute("ALTER TYPE msg_route_type_enum_new RENAME TO msg_route_type_enum")