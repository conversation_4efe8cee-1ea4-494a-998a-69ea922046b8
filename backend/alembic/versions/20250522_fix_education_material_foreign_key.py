"""fix education material foreign key

Revision ID: 20250522_fix_education_material_foreign_key
Revises: 20250520_merge_heads
Create Date: 2025-05-22 17:44:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_edu_fk_20250522'
down_revision = 'a8bcb9452f64'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Convert education_materials.created_by from UUID to String and add foreign key
    
    # Step 1: Add a temporary column
    op.add_column('education_materials', 
                 sa.Column('created_by_temp', sa.String(), nullable=True))
    
    # Step 2: Update the temporary column with converted values (this will be empty for now)
    # In a real migration, you would need to populate this with actual clinician IDs
    # For now, we'll set a default value that can be updated later
    op.execute("UPDATE education_materials SET created_by_temp = 'temp_value'")
    
    # Step 3: Drop the old UUID column
    op.drop_column('education_materials', 'created_by')
    
    # Step 4: Rename the temporary column
    op.alter_column('education_materials', 'created_by_temp', new_column_name='created_by')
    
    # Step 5: Make the column not null
    op.alter_column('education_materials', 'created_by', nullable=False)
    
    # Step 6: Add the foreign key constraint
    op.create_foreign_key('fk_education_materials_created_by_clinicians',
                         'education_materials', 'clinicians',
                         ['created_by'], ['id'])
    
    # Step 7: Recreate the index
    op.create_index('ix_education_materials_created_by', 'education_materials', ['created_by'])


def downgrade() -> None:
    # Remove foreign key constraint and convert back to UUID
    try:
        op.drop_constraint('fk_education_materials_created_by_clinicians', 'education_materials', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_index('ix_education_materials_created_by', 'education_materials')
    except:
        pass
    
    # Drop and recreate as UUID
    op.drop_column('education_materials', 'created_by')
    op.add_column('education_materials', 
                 sa.Column('created_by', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False))
    op.create_index('ix_education_materials_created_by', 'education_materials', ['created_by'])