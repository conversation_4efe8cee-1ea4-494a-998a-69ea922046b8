"""make scraped_page_id nullable for education materials

Revision ID: make_scraped_page_id_nullable
Revises: fix_clinic_fk_20250522
Create Date: 2025-05-23 15:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'make_scraped_page_id_nullable'
down_revision = 'fix_clinic_fk_20250522'
branch_labels = None
depends_on = None


def upgrade():
    # Make scraped_page_id nullable to support education material chunks
    op.alter_column('content_chunks', 'scraped_page_id',
                    existing_type=sa.UUID(),
                    nullable=True)


def downgrade():
    # Make scraped_page_id non-nullable again
    # Note: This will fail if there are any NULL values in the column
    op.alter_column('content_chunks', 'scraped_page_id',
                    existing_type=sa.UUID(),
                    nullable=False)