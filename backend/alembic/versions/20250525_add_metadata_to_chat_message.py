"""Add message_metadata field to ChatMessage model

Revision ID: add_metadata_chat_msg
Revises: fix_clinic_fk_20250522
Create Date: 2025-05-25 15:47:48

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_metadata_chat_msg'
down_revision = 'fix_clinic_fk_20250522'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add message_metadata column to chat_messages table
    op.add_column('chat_messages', 
        sa.Column('message_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True)
    )


def downgrade() -> None:
    # Remove message_metadata column from chat_messages table
    op.drop_column('chat_messages', 'message_metadata')