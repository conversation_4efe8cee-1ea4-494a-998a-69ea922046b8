"""Add address and scraped_data to Clinic model

Revision ID: 2136f16acfa1
Revises: 79d13ccdaddb
Create Date: 2025-04-06 12:30:21.866622

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2136f16acfa1"
down_revision: Union[str, None] = "79d13ccdaddb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("clinics", sa.Column("address", sa.Text(), nullable=True))
    op.add_column("clinics", sa.Column("scraped_data", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("clinics", "scraped_data")
    op.drop_column("clinics", "address")
    # ### end Alembic commands ###
