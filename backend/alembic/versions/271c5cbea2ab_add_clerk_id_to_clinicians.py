"""add clerk_id to clinicians

Revision ID: 271c5cbea2ab
Revises: 95da076f7a11
Create Date: 2025-04-03 13:29:13.151909

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "271c5cbea2ab"
down_revision: Union[str, None] = "95da076f7a11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("access_codes", sa.Column("email", sa.String(), nullable=False))
    op.add_column("access_codes", sa.Column("clinician_id", sa.UUID(), nullable=True))
    op.create_index(
        op.f("ix_access_codes_clinician_id"),
        "access_codes",
        ["clinician_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_access_codes_email"), "access_codes", ["email"], unique=False
    )
    op.create_foreign_key(None, "access_codes", "clinicians", ["clinician_id"], ["id"])
    op.add_column("clinicians", sa.Column("clerk_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_clinicians_clerk_id"), "clinicians", ["clerk_id"], unique=True
    )
    op.add_column("contents", sa.Column("category", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_contents_category"), "contents", ["category"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_contents_category"), table_name="contents")
    op.drop_column("contents", "category")
    op.drop_index(op.f("ix_clinicians_clerk_id"), table_name="clinicians")
    op.drop_column("clinicians", "clerk_id")
    op.drop_constraint(None, "access_codes", type_="foreignkey")
    op.drop_index(op.f("ix_access_codes_email"), table_name="access_codes")
    op.drop_index(op.f("ix_access_codes_clinician_id"), table_name="access_codes")
    op.drop_column("access_codes", "clinician_id")
    op.drop_column("access_codes", "email")
    # ### end Alembic commands ###
