"""Change UUID fields to String for Clerk user IDs

Revision ID: 3315adcc562b
Revises: 1cc9f7cc87af
Create Date: 2025-04-04 13:08:49.166429

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3315adcc562b"
down_revision: Union[str, None] = "1cc9f7cc87af"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema (UUID → String)."""
    # Drop foreign key constraints before altering column types
    op.drop_constraint(
        "access_codes_clinician_id_fkey", "access_codes", type_="foreignkey"
    )
    op.drop_constraint(
        "access_codes_patient_id_fkey", "access_codes", type_="foreignkey"
    )
    op.drop_constraint(
        "appointments_patient_id_fkey", "appointments", type_="foreignkey"
    )
    op.drop_constraint(
        "appointments_clinician_id_fkey", "appointments", type_="foreignkey"
    )
    op.drop_constraint(
        "chat_messages_patient_id_fkey", "chat_messages", type_="foreignkey"
    )
    op.drop_constraint(
        "clinician_patient_association_clinician_id_fkey",
        "clinician_patient_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "clinician_patient_association_patient_id_fkey",
        "clinician_patient_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "medication_requests_patient_id_fkey", "medication_requests", type_="foreignkey"
    )
    op.drop_constraint(
        "fk_medication_requests_clinician_id", "medication_requests", type_="foreignkey"
    )
    op.drop_constraint(
        "patient_alerts_patient_id_fkey", "patient_alerts", type_="foreignkey"
    )
    op.drop_constraint(
        "patient_alerts_resolved_by_clinician_id_fkey",
        "patient_alerts",
        type_="foreignkey",
    )
    op.drop_constraint(
        "side_effect_reports_patient_id_fkey", "side_effect_reports", type_="foreignkey"
    )
    op.drop_constraint("weight_logs_patient_id_fkey", "weight_logs", type_="foreignkey")
    # Additional constraints
    op.drop_constraint(
        "lab_results_clinician_id_fkey", "lab_results", type_="foreignkey"
    )
    op.drop_constraint("lab_results_patient_id_fkey", "lab_results", type_="foreignkey")
    op.drop_constraint(
        "lab_results_reviewed_by_clinician_id_fkey", "lab_results", type_="foreignkey"
    )
    op.drop_constraint("notes_clinician_id_fkey", "notes", type_="foreignkey")
    op.drop_constraint("notes_patient_id_fkey", "notes", type_="foreignkey")
    op.drop_constraint(
        "notifications_recipient_clinician_id_fkey", "notifications", type_="foreignkey"
    )

    # Alter column types from UUID to String
    op.alter_column(
        "access_codes",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "access_codes",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=True,
    )
    op.alter_column(
        "appointments",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "appointments",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "chat_messages",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "clinician_patient_association",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "clinician_patient_association",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "clinicians",
        "id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.create_index(op.f("ix_clinicians_id"), "clinicians", ["id"], unique=False)
    op.alter_column(
        "medication_requests",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "medication_requests",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=True,
    )
    op.alter_column(
        "patient_alerts",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "patient_alerts",
        "resolved_by_clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=True,
    )
    op.create_index(
        op.f("ix_patient_alerts_id"), "patient_alerts", ["id"], unique=False
    )
    op.alter_column(
        "patients",
        "id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "side_effect_reports",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "weight_logs",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    # Additional columns
    op.alter_column(
        "lab_results",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "lab_results",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "lab_results",
        "reviewed_by_clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=True,
    )
    op.alter_column(
        "notes",
        "clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "notes",
        "patient_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )
    op.alter_column(
        "notifications",
        "recipient_clinician_id",
        existing_type=sa.UUID(),
        type_=sa.String(),
        existing_nullable=False,
    )

    # Recreate foreign key constraints after altering column types
    op.create_foreign_key(
        "access_codes_clinician_id_fkey",
        "access_codes",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "access_codes_patient_id_fkey",
        "access_codes",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "appointments_patient_id_fkey",
        "appointments",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "appointments_clinician_id_fkey",
        "appointments",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "chat_messages_patient_id_fkey",
        "chat_messages",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "clinician_patient_association_clinician_id_fkey",
        "clinician_patient_association",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "clinician_patient_association_patient_id_fkey",
        "clinician_patient_association",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "medication_requests_patient_id_fkey",
        "medication_requests",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_medication_requests_clinician_id",
        "medication_requests",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "patient_alerts_patient_id_fkey",
        "patient_alerts",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "patient_alerts_resolved_by_clinician_id_fkey",
        "patient_alerts",
        "clinicians",
        ["resolved_by_clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "side_effect_reports_patient_id_fkey",
        "side_effect_reports",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "weight_logs_patient_id_fkey", "weight_logs", "patients", ["patient_id"], ["id"]
    )
    # Additional constraints
    op.create_foreign_key(
        "lab_results_clinician_id_fkey",
        "lab_results",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "lab_results_patient_id_fkey", "lab_results", "patients", ["patient_id"], ["id"]
    )
    op.create_foreign_key(
        "lab_results_reviewed_by_clinician_id_fkey",
        "lab_results",
        "clinicians",
        ["reviewed_by_clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "notes_clinician_id_fkey", "notes", "clinicians", ["clinician_id"], ["id"]
    )
    op.create_foreign_key(
        "notes_patient_id_fkey", "notes", "patients", ["patient_id"], ["id"]
    )
    op.create_foreign_key(
        "notifications_recipient_clinician_id_fkey",
        "notifications",
        "clinicians",
        ["recipient_clinician_id"],
        ["id"],
    )


def downgrade() -> None:
    """Downgrade schema (String → UUID)."""
    # Drop foreign key constraints before altering column types
    op.drop_constraint(
        "access_codes_clinician_id_fkey", "access_codes", type_="foreignkey"
    )
    op.drop_constraint(
        "access_codes_patient_id_fkey", "access_codes", type_="foreignkey"
    )
    op.drop_constraint(
        "appointments_patient_id_fkey", "appointments", type_="foreignkey"
    )
    op.drop_constraint(
        "appointments_clinician_id_fkey", "appointments", type_="foreignkey"
    )
    op.drop_constraint(
        "chat_messages_patient_id_fkey", "chat_messages", type_="foreignkey"
    )
    op.drop_constraint(
        "clinician_patient_association_clinician_id_fkey",
        "clinician_patient_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "clinician_patient_association_patient_id_fkey",
        "clinician_patient_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "medication_requests_patient_id_fkey", "medication_requests", type_="foreignkey"
    )
    op.drop_constraint(
        "fk_medication_requests_clinician_id", "medication_requests", type_="foreignkey"
    )
    op.drop_constraint(
        "patient_alerts_patient_id_fkey", "patient_alerts", type_="foreignkey"
    )
    op.drop_constraint(
        "patient_alerts_resolved_by_clinician_id_fkey",
        "patient_alerts",
        type_="foreignkey",
    )
    op.drop_constraint(
        "side_effect_reports_patient_id_fkey", "side_effect_reports", type_="foreignkey"
    )
    op.drop_constraint("weight_logs_patient_id_fkey", "weight_logs", type_="foreignkey")
    # Additional constraints
    op.drop_constraint(
        "lab_results_clinician_id_fkey", "lab_results", type_="foreignkey"
    )
    op.drop_constraint("lab_results_patient_id_fkey", "lab_results", type_="foreignkey")
    op.drop_constraint(
        "lab_results_reviewed_by_clinician_id_fkey", "lab_results", type_="foreignkey"
    )
    op.drop_constraint("notes_clinician_id_fkey", "notes", type_="foreignkey")
    op.drop_constraint("notes_patient_id_fkey", "notes", type_="foreignkey")
    op.drop_constraint(
        "notifications_recipient_clinician_id_fkey", "notifications", type_="foreignkey"
    )

    # Alter column types from String to UUID
    op.alter_column(
        "weight_logs",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "side_effect_reports",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "patients",
        "id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.drop_index(op.f("ix_patient_alerts_id"), table_name="patient_alerts")
    op.alter_column(
        "patient_alerts",
        "resolved_by_clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=True,
    )
    op.alter_column(
        "patient_alerts",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "medication_requests",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=True,
    )
    op.alter_column(
        "medication_requests",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.drop_index(op.f("ix_clinicians_id"), table_name="clinicians")
    op.alter_column(
        "clinicians",
        "id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "clinician_patient_association",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "clinician_patient_association",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "chat_messages",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "appointments",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "appointments",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "access_codes",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=True,
    )
    op.alter_column(
        "access_codes",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    # Additional columns
    op.alter_column(
        "lab_results",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "lab_results",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "lab_results",
        "reviewed_by_clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=True,
    )
    op.alter_column(
        "notes",
        "clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "notes",
        "patient_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    op.alter_column(
        "notifications",
        "recipient_clinician_id",
        existing_type=sa.String(),
        type_=sa.UUID(),
        existing_nullable=False,
    )

    # Recreate foreign key constraints after altering column types
    op.create_foreign_key(
        "access_codes_clinician_id_fkey",
        "access_codes",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "access_codes_patient_id_fkey",
        "access_codes",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "appointments_patient_id_fkey",
        "appointments",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "appointments_clinician_id_fkey",
        "appointments",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "chat_messages_patient_id_fkey",
        "chat_messages",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "clinician_patient_association_clinician_id_fkey",
        "clinician_patient_association",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "clinician_patient_association_patient_id_fkey",
        "clinician_patient_association",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "medication_requests_patient_id_fkey",
        "medication_requests",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_medication_requests_clinician_id",
        "medication_requests",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "patient_alerts_patient_id_fkey",
        "patient_alerts",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "patient_alerts_resolved_by_clinician_id_fkey",
        "patient_alerts",
        "clinicians",
        ["resolved_by_clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "side_effect_reports_patient_id_fkey",
        "side_effect_reports",
        "patients",
        ["patient_id"],
        ["id"],
    )
    op.create_foreign_key(
        "weight_logs_patient_id_fkey", "weight_logs", "patients", ["patient_id"], ["id"]
    )
    # Additional constraints
    op.create_foreign_key(
        "lab_results_clinician_id_fkey",
        "lab_results",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "lab_results_patient_id_fkey", "lab_results", "patients", ["patient_id"], ["id"]
    )
    op.create_foreign_key(
        "lab_results_reviewed_by_clinician_id_fkey",
        "lab_results",
        "clinicians",
        ["reviewed_by_clinician_id"],
        ["id"],
    )
    op.create_foreign_key(
        "notes_clinician_id_fkey", "notes", "clinicians", ["clinician_id"], ["id"]
    )
    op.create_foreign_key(
        "notes_patient_id_fkey", "notes", "patients", ["patient_id"], ["id"]
    )
    op.create_foreign_key(
        "notifications_recipient_clinician_id_fkey",
        "notifications",
        "clinicians",
        ["recipient_clinician_id"],
        ["id"],
    )
