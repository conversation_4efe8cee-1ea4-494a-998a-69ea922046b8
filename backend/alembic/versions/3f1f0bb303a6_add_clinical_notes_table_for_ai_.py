"""add_clinical_notes_table_for_ai_generation

Revision ID: 3f1f0bb303a6
Revises: d244f6733ac1
Create Date: 2025-05-26 16:06:43.548648

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '3f1f0bb303a6'
down_revision: Union[str, None] = 'd244f6733ac1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create clinical_notes table for AI-generated clinical documentation."""
    # Enable UUID extension if not already enabled
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    # Create clinical_notes table
    op.create_table(
        'clinical_notes',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, server_default=sa.text('uuid_generate_v4()')),
        sa.Column('patient_id', sa.String(), nullable=False),
        sa.Column('clinician_id', sa.String(), nullable=False),
        sa.Column('appointment_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('chat_session_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), nullable=True),
        
        # Note content
        sa.Column('note_type', sa.String(), nullable=False),  # 'SOAP', 'K-SOAP', etc.
        sa.Column('sections', postgresql.JSONB(astext_type=sa.Text()), nullable=False),  # Structured sections
        sa.Column('raw_text', sa.Text(), nullable=True),  # Full text version
        
        # Metadata
        sa.Column('status', postgresql.ENUM('draft', 'reviewed', 'approved', 'amended', name='clinical_note_status'), nullable=False, server_default='draft'),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('now()')),
        sa.Column('approved_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('approved_by', sa.String(), nullable=True),
        
        # AI tracking
        sa.Column('ai_generated', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('ai_confidence_score', sa.Float(), nullable=True),
        sa.Column('human_edits', postgresql.JSONB(astext_type=sa.Text()), nullable=True),  # Track what was changed
        
        # Billing support
        sa.Column('suggested_icd10_codes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('suggested_cpt_codes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('billing_notes', sa.Text(), nullable=True),
        
        # Relationships
        sa.ForeignKeyConstraint(['patient_id'], ['patients.id'], ),
        sa.ForeignKeyConstraint(['clinician_id'], ['clinicians.clerk_id'], ),
        sa.ForeignKeyConstraint(['appointment_id'], ['appointments.id'], ),
        sa.ForeignKeyConstraint(['template_id'], ['templates.id'], ),
        sa.ForeignKeyConstraint(['approved_by'], ['clinicians.clerk_id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index('ix_clinical_notes_patient_id', 'clinical_notes', ['patient_id'])
    op.create_index('ix_clinical_notes_clinician_id', 'clinical_notes', ['clinician_id'])
    op.create_index('ix_clinical_notes_appointment_id', 'clinical_notes', ['appointment_id'])
    op.create_index('ix_clinical_notes_status', 'clinical_notes', ['status'])
    op.create_index('ix_clinical_notes_created_at', 'clinical_notes', ['created_at'])
    op.create_index('ix_clinical_notes_patient_clinician', 'clinical_notes', ['patient_id', 'clinician_id'])


def downgrade() -> None:
    """Drop clinical_notes table and related objects."""
    # Drop indexes
    op.drop_index('ix_clinical_notes_patient_clinician', table_name='clinical_notes')
    op.drop_index('ix_clinical_notes_created_at', table_name='clinical_notes')
    op.drop_index('ix_clinical_notes_status', table_name='clinical_notes')
    op.drop_index('ix_clinical_notes_appointment_id', table_name='clinical_notes')
    op.drop_index('ix_clinical_notes_clinician_id', table_name='clinical_notes')
    op.drop_index('ix_clinical_notes_patient_id', table_name='clinical_notes')
    
    # Drop table
    op.drop_table('clinical_notes')
    
    # Drop enum type if it exists
    op.execute("DROP TYPE IF EXISTS clinical_note_status")
