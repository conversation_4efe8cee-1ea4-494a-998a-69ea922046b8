"""Add clinician_id to SideEffectReport model

Revision ID: 45ecf4b44971
Revises: 20250409_173100
Create Date: 2025-04-10 12:54:28.737600

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "45ecf4b44971"
down_revision: Union[str, None] = "20250409_173100"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_medication_requests_dosage", table_name="medication_requests")
    op.drop_index("ix_medication_requests_duration", table_name="medication_requests")
    op.drop_index("ix_medication_requests_frequency", table_name="medication_requests")
    op.drop_index("ix_medications_name", table_name="medications")
    op.create_unique_constraint("uq_medications_name", "medications", ["name"])
    op.add_column(
        "side_effect_reports", sa.Column("clinician_id", sa.String(), nullable=True)
    )
    op.add_column(
        "side_effect_reports",
        sa.Column(
            "reported_at", sa.DateTime(), nullable=False, server_default=sa.func.now()
        ),
    )
    op.add_column(
        "side_effect_reports", sa.Column("resolved_at", sa.DateTime(), nullable=True)
    )
    op.add_column(
        "side_effect_reports", sa.Column("resolution_notes", sa.Text(), nullable=True)
    )
    op.drop_index("ix_side_effect_reports_id", table_name="side_effect_reports")
    op.drop_index(
        "ix_side_effect_reports_patient_id_created_at", table_name="side_effect_reports"
    )
    op.drop_index(
        "ix_side_effect_reports_patient_id_severity", table_name="side_effect_reports"
    )
    op.drop_index(
        "ix_side_effect_reports_patient_id_status", table_name="side_effect_reports"
    )
    op.create_index(
        op.f("ix_side_effect_reports_clinician_id"),
        "side_effect_reports",
        ["clinician_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_side_effect_reports_severity"),
        "side_effect_reports",
        ["severity"],
        unique=False,
    )
    op.create_index(
        op.f("ix_side_effect_reports_status"),
        "side_effect_reports",
        ["status"],
        unique=False,
    )
    op.create_foreign_key(
        "fk_side_effect_reports_clinician_id",
        "side_effect_reports",
        "clinicians",
        ["clinician_id"],
        ["id"],
    )
    op.drop_column("side_effect_reports", "created_at")
    op.drop_column("side_effect_reports", "updated_at")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "side_effect_reports",
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "side_effect_reports",
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_constraint(
        "fk_side_effect_reports_clinician_id", "side_effect_reports", type_="foreignkey"
    )
    op.drop_index(
        op.f("ix_side_effect_reports_status"), table_name="side_effect_reports"
    )
    op.drop_index(
        op.f("ix_side_effect_reports_severity"), table_name="side_effect_reports"
    )
    op.drop_index(
        op.f("ix_side_effect_reports_clinician_id"), table_name="side_effect_reports"
    )
    op.create_index(
        "ix_side_effect_reports_patient_id_status",
        "side_effect_reports",
        ["patient_id", "status"],
        unique=False,
    )
    op.create_index(
        "ix_side_effect_reports_patient_id_severity",
        "side_effect_reports",
        ["patient_id", "severity"],
        unique=False,
    )
    op.create_index(
        "ix_side_effect_reports_patient_id_created_at",
        "side_effect_reports",
        ["patient_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_side_effect_reports_id", "side_effect_reports", ["id"], unique=False
    )
    op.drop_column("side_effect_reports", "resolution_notes")
    op.drop_column("side_effect_reports", "resolved_at")
    op.drop_column("side_effect_reports", "reported_at")
    op.drop_column("side_effect_reports", "clinician_id")
    op.drop_constraint("uq_medications_name", "medications", type_="unique")
    op.create_index("ix_medications_name", "medications", ["name"], unique=True)
    op.create_index(
        "ix_medication_requests_frequency",
        "medication_requests",
        ["frequency"],
        unique=False,
    )
    op.create_index(
        "ix_medication_requests_duration",
        "medication_requests",
        ["duration"],
        unique=False,
    )
    op.create_index(
        "ix_medication_requests_dosage", "medication_requests", ["dosage"], unique=False
    )
    # ### end Alembic commands ###
