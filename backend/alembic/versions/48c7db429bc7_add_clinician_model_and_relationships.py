"""Add clinician model and relationships

Revision ID: 48c7db429bc7
Revises: 643d65c31714
Create Date: 2025-04-01 08:27:19.240479

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "48c7db429bc7"
down_revision: Union[str, None] = "643d65c31714"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "clinicians",
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("specialty", sa.String(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("firebase_uid", sa.String(), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_clinicians_email"), "clinicians", ["email"], unique=True)
    op.create_index(
        op.f("ix_clinicians_firebase_uid"), "clinicians", ["firebase_uid"], unique=True
    )
    op.create_index(op.f("ix_clinicians_id"), "clinicians", ["id"], unique=False)
    op.create_table(
        "clinician_patient_association",
        sa.Column("clinician_id", sa.UUID(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(
            ["clinician_id"],
            ["clinicians.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("clinician_id", "patient_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("clinician_patient_association")
    op.drop_index(op.f("ix_clinicians_id"), table_name="clinicians")
    op.drop_index(op.f("ix_clinicians_firebase_uid"), table_name="clinicians")
    op.drop_index(op.f("ix_clinicians_email"), table_name="clinicians")
    op.drop_table("clinicians")
    # ### end Alembic commands ###
