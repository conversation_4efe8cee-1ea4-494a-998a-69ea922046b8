"""Initial database schema

Revision ID: 643d65c31714
Revises:
Create Date: 2025-04-01 08:05:44.446846

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "643d65c31714"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "audit_logs",
        sa.Column("actor_user_id", sa.String(), nullable=True),
        sa.Column("actor_role", sa.String(length=50), nullable=True),
        sa.Column("action", sa.String(length=255), nullable=False),
        sa.Column("target_resource_type", sa.String(length=100), nullable=True),
        sa.Column("target_resource_id", sa.String(), nullable=True),
        sa.Column("outcome", sa.String(length=50), nullable=False),
        sa.Column("details", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("source_ip", sa.String(length=100), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_audit_logs_action"), "audit_logs", ["action"], unique=False
    )
    op.create_index(
        op.f("ix_audit_logs_actor_role"), "audit_logs", ["actor_role"], unique=False
    )
    op.create_index(
        op.f("ix_audit_logs_actor_user_id"),
        "audit_logs",
        ["actor_user_id"],
        unique=False,
    )
    op.create_index(op.f("ix_audit_logs_id"), "audit_logs", ["id"], unique=False)
    op.create_index(
        op.f("ix_audit_logs_outcome"), "audit_logs", ["outcome"], unique=False
    )
    op.create_index(
        op.f("ix_audit_logs_target_resource_id"),
        "audit_logs",
        ["target_resource_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_audit_logs_target_resource_type"),
        "audit_logs",
        ["target_resource_type"],
        unique=False,
    )
    op.create_table(
        "contents",
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("body", sa.Text(), nullable=True),
        sa.Column(
            "content_type",
            sa.Enum(
                "ARTICLE", "FAQ", "VIDEO", "RESOURCE_LINK", name="content_type_enum"
            ),
            nullable=False,
        ),
        sa.Column("url", sa.String(), nullable=True),
        sa.Column("is_published", sa.Boolean(), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_contents_content_type"), "contents", ["content_type"], unique=False
    )
    op.create_index(op.f("ix_contents_id"), "contents", ["id"], unique=False)
    op.create_index(
        op.f("ix_contents_is_published"), "contents", ["is_published"], unique=False
    )
    op.create_index(op.f("ix_contents_title"), "contents", ["title"], unique=False)
    op.create_table(
        "patients",
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("date_of_birth", sa.Date(), nullable=True),
        sa.Column("phone_number", sa.String(), nullable=True),
        sa.Column("height_cm", sa.Float(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_patients_email"), "patients", ["email"], unique=True)
    op.create_index(op.f("ix_patients_id"), "patients", ["id"], unique=False)
    op.create_index(
        op.f("ix_patients_phone_number"), "patients", ["phone_number"], unique=True
    )
    op.create_table(
        "access_codes",
        sa.Column("code", sa.String(), nullable=False),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("is_used", sa.Boolean(), nullable=False),
        sa.Column("patient_id", sa.UUID(), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_access_codes_code"), "access_codes", ["code"], unique=True)
    op.create_index(
        op.f("ix_access_codes_expires_at"), "access_codes", ["expires_at"], unique=False
    )
    op.create_index(op.f("ix_access_codes_id"), "access_codes", ["id"], unique=False)
    op.create_index(
        op.f("ix_access_codes_is_used"), "access_codes", ["is_used"], unique=False
    )
    op.create_index(
        op.f("ix_access_codes_patient_id"), "access_codes", ["patient_id"], unique=True
    )
    op.create_table(
        "chat_messages",
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column(
            "sender_type",
            sa.Enum("patient", "agent", "clinician", name="msg_sender_type_enum"),
            nullable=False,
        ),
        sa.Column("message_content", sa.Text(), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_chat_messages_id"), "chat_messages", ["id"], unique=False)
    op.create_index(
        op.f("ix_chat_messages_patient_id"),
        "chat_messages",
        ["patient_id"],
        unique=False,
    )
    op.create_table(
        "medication_requests",
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("medication_name", sa.String(), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column(
            "status",
            sa.Enum("PENDING", "APPROVED", "REJECTED", name="med_req_status_enum"),
            nullable=False,
        ),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_medication_requests_id"), "medication_requests", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_medication_requests_patient_id"),
        "medication_requests",
        ["patient_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_medication_requests_status"),
        "medication_requests",
        ["status"],
        unique=False,
    )
    op.create_table(
        "side_effect_reports",
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column(
            "severity",
            sa.Enum("MINOR", "MAJOR", name="severity_level_enum"),
            nullable=False,
        ),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_side_effect_reports_id"), "side_effect_reports", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_side_effect_reports_patient_id"),
        "side_effect_reports",
        ["patient_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_side_effect_reports_severity"),
        "side_effect_reports",
        ["severity"],
        unique=False,
    )
    op.create_index(
        op.f("ix_side_effect_reports_status"),
        "side_effect_reports",
        ["status"],
        unique=False,
    )
    op.create_table(
        "weight_logs",
        sa.Column("patient_id", sa.UUID(), nullable=False),
        sa.Column("weight_kg", sa.Float(), nullable=False),
        sa.Column("log_date", sa.Date(), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_weight_logs_id"), "weight_logs", ["id"], unique=False)
    op.create_index(
        op.f("ix_weight_logs_log_date"), "weight_logs", ["log_date"], unique=False
    )
    op.create_index(
        op.f("ix_weight_logs_patient_id"), "weight_logs", ["patient_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_weight_logs_patient_id"), table_name="weight_logs")
    op.drop_index(op.f("ix_weight_logs_log_date"), table_name="weight_logs")
    op.drop_index(op.f("ix_weight_logs_id"), table_name="weight_logs")
    op.drop_table("weight_logs")
    op.drop_index(
        op.f("ix_side_effect_reports_status"), table_name="side_effect_reports"
    )
    op.drop_index(
        op.f("ix_side_effect_reports_severity"), table_name="side_effect_reports"
    )
    op.drop_index(
        op.f("ix_side_effect_reports_patient_id"), table_name="side_effect_reports"
    )
    op.drop_index(op.f("ix_side_effect_reports_id"), table_name="side_effect_reports")
    op.drop_table("side_effect_reports")
    op.drop_index(
        op.f("ix_medication_requests_status"), table_name="medication_requests"
    )
    op.drop_index(
        op.f("ix_medication_requests_patient_id"), table_name="medication_requests"
    )
    op.drop_index(op.f("ix_medication_requests_id"), table_name="medication_requests")
    op.drop_table("medication_requests")
    op.drop_index(op.f("ix_chat_messages_patient_id"), table_name="chat_messages")
    op.drop_index(op.f("ix_chat_messages_id"), table_name="chat_messages")
    op.drop_table("chat_messages")
    op.drop_index(op.f("ix_access_codes_patient_id"), table_name="access_codes")
    op.drop_index(op.f("ix_access_codes_is_used"), table_name="access_codes")
    op.drop_index(op.f("ix_access_codes_id"), table_name="access_codes")
    op.drop_index(op.f("ix_access_codes_expires_at"), table_name="access_codes")
    op.drop_index(op.f("ix_access_codes_code"), table_name="access_codes")
    op.drop_table("access_codes")
    op.drop_index(op.f("ix_patients_phone_number"), table_name="patients")
    op.drop_index(op.f("ix_patients_id"), table_name="patients")
    op.drop_index(op.f("ix_patients_email"), table_name="patients")
    op.drop_table("patients")
    op.drop_index(op.f("ix_contents_title"), table_name="contents")
    op.drop_index(op.f("ix_contents_is_published"), table_name="contents")
    op.drop_index(op.f("ix_contents_id"), table_name="contents")
    op.drop_index(op.f("ix_contents_content_type"), table_name="contents")
    op.drop_table("contents")
    op.drop_index(op.f("ix_audit_logs_target_resource_type"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_target_resource_id"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_outcome"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_id"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_actor_user_id"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_actor_role"), table_name="audit_logs")
    op.drop_index(op.f("ix_audit_logs_action"), table_name="audit_logs")
    op.drop_table("audit_logs")
    # ### end Alembic commands ###
