"""Add invitation fields to Patient model

Revision ID: 6a0384f225a2
Revises: 963211e8e44f
Create Date: 2025-04-08 09:36:33.700380

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6a0384f225a2"
down_revision: Union[str, None] = "963211e8e44f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patients", sa.Column("invited_by_clinician_id", sa.String(), nullable=True)
    )
    op.add_column(
        "patients", sa.Column("associated_clinic_id", sa.UUID(), nullable=True)
    )
    op.create_index(
        op.f("ix_patients_associated_clinic_id"),
        "patients",
        ["associated_clinic_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_patients_invited_by_clinician_id"),
        "patients",
        ["invited_by_clinician_id"],
        unique=False,
    )
    op.create_foreign_key(None, "patients", "clinics", ["associated_clinic_id"], ["id"])
    op.create_foreign_key(
        None, "patients", "clinicians", ["invited_by_clinician_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "patients", type_="foreignkey")
    op.drop_constraint(None, "patients", type_="foreignkey")
    op.drop_index(op.f("ix_patients_invited_by_clinician_id"), table_name="patients")
    op.drop_index(op.f("ix_patients_associated_clinic_id"), table_name="patients")
    op.drop_column("patients", "associated_clinic_id")
    op.drop_column("patients", "invited_by_clinician_id")
    # ### end Alembic commands ###
