"""Add photo_url to patient model

Revision ID: 79d13ccdaddb
Revises: a3e31158b767
Create Date: 2025-04-05 19:16:08.686377

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "79d13ccdaddb"
down_revision: Union[str, None] = "a3e31158b767"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("patients", sa.Column("photo_url", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("patients", "photo_url")
    # ### end Alembic commands ###
