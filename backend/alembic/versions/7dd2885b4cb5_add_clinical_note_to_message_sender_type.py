"""add_clinical_note_to_message_sender_type

Revision ID: 7dd2885b4cb5
Revises: 3f1f0bb303a6
Create Date: 2025-05-27 13:38:52.179677

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7dd2885b4cb5'
down_revision: Union[str, None] = '3f1f0bb303a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add CLINICAL_NOTE to the MessageSenderType enum (uppercase to match existing values)
    op.execute("ALTER TYPE msg_sender_type_enum ADD VALUE IF NOT EXISTS 'CLINICAL_NOTE'")


def downgrade() -> None:
    """Downgrade schema."""
    # Note: PostgreSQL doesn't support removing enum values directly
    # You would need to recreate the enum without the value
    # This is left as a pass for safety
    pass
