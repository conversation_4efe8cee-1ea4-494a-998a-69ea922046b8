"""add category column to medications

Revision ID: 83bf5158bdf8
Revises: 96df0601da4c
Create Date: 2025-04-14 13:27:58.275512

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "83bf5158bdf8"
down_revision: Union[str, None] = "96df0601da4c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("medications", sa.Column("category", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_medications_category"), "medications", ["category"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_medications_category"), table_name="medications")
    op.drop_column("medications", "category")
    # ### end Alembic commands ###
