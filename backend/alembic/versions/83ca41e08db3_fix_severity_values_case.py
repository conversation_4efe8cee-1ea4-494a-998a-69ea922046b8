"""fix_severity_values_case

Revision ID: 83ca41e08db3
Revises: ba2f9c7d92bc
Create Date: 2025-04-10 18:09:33.336

"""

from collections.abc import Sequence
from typing import Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "83ca41e08db3"
down_revision: Union[str, None] = "ba2f9c7d92bc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create a temporary column to store the lowercase values
    op.execute(
        """
        ALTER TABLE side_effect_reports
        ADD COLUMN severity_temp text
    """
    )

    # Update the temporary column with lowercase values
    op.execute(
        """
        UPDATE side_effect_reports
        SET severity_temp = LOWER(severity::text)
    """
    )

    # Drop the existing enum type
    op.execute("DROP TYPE severity_level_enum CASCADE")

    # Create the new enum type with lowercase values
    op.execute(
        """
        CREATE TYPE severity_level_enum AS ENUM ('minor', 'moderate', 'major')
    """
    )

    # Update the severity column to use the new enum type
    op.execute(
        """
        ALTER TABLE side_effect_reports
        ALTER COLUMN severity TYPE severity_level_enum
        USING severity_temp::severity_level_enum
    """
    )

    # Drop the temporary column
    op.execute(
        """
        ALTER TABLE side_effect_reports
        DROP COLUMN severity_temp
    """
    )


def downgrade() -> None:
    # Create a temporary column to store the uppercase values
    op.execute(
        """
        ALTER TABLE side_effect_reports
        ADD COLUMN severity_temp text
    """
    )

    # Update the temporary column with uppercase values
    op.execute(
        """
        UPDATE side_effect_reports
        SET severity_temp = UPPER(severity::text)
    """
    )

    # Drop the existing enum type
    op.execute("DROP TYPE severity_level_enum CASCADE")

    # Create the original enum type with uppercase values
    op.execute(
        """
        CREATE TYPE severity_level_enum AS ENUM ('MINOR', 'MAJOR', 'moderate')
    """
    )

    # Update the severity column to use the original enum type
    op.execute(
        """
        ALTER TABLE side_effect_reports
        ALTER COLUMN severity TYPE severity_level_enum
        USING severity_temp::severity_level_enum
    """
    )

    # Drop the temporary column
    op.execute(
        """
        ALTER TABLE side_effect_reports
        DROP COLUMN severity_temp
    """
    )
