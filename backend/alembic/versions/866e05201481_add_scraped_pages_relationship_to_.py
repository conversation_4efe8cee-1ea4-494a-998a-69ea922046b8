"""Add scraped_pages relationship to Clinic model

Revision ID: 866e05201481
Revises: a64b6e80814a
Create Date: 2025-04-06 14:36:44.277602

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "866e05201481"
down_revision: Union[str, None] = "a64b6e80814a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "content_chunks",
        sa.Column("metadata", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )
    op.drop_column("content_chunks", "chunk_metadata")
    op.add_column(
        "scraped_pages",
        sa.Column("metadata", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )
    op.alter_column(
        "scraped_pages",
        "scraped_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=sa.DateTime(),
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.create_index(
        "ix_scraped_pages_clinic_id_scraped_at",
        "scraped_pages",
        ["clinic_id", "scraped_at"],
        unique=False,
    )
    op.drop_column("scraped_pages", "page_metadata")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "scraped_pages",
        sa.Column(
            "page_metadata",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.drop_index("ix_scraped_pages_clinic_id_scraped_at", table_name="scraped_pages")
    op.alter_column(
        "scraped_pages",
        "scraped_at",
        existing_type=sa.DateTime(),
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.drop_column("scraped_pages", "metadata")
    op.add_column(
        "content_chunks",
        sa.Column(
            "chunk_metadata",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.drop_column("content_chunks", "metadata")
    # ### end Alembic commands ###
