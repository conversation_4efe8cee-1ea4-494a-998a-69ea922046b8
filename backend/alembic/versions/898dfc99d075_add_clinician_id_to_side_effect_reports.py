"""add_clinician_id_to_side_effect_reports

Revision ID: 898dfc99d075
Revises: 1159fbc7dd6a
Create Date: 2025-04-03 13:47:15.017632

"""

from collections.abc import Sequence
from typing import Union

# revision identifiers, used by Alembic.
revision: str = "898dfc99d075"
down_revision: Union[str, None] = "1159fbc7dd6a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
