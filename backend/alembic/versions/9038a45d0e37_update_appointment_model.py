"""update_appointment_model

Revision ID: 9038a45d0e37
Revises: 6a0384f225a2
Create Date: 2025-04-09 12:14:34.128720

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9038a45d0e37"
down_revision: Union[str, None] = "6a0384f225a2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "appointments", sa.Column("duration_minutes", sa.Integer(), nullable=False)
    )
    op.add_column("appointments", sa.Column("reason", sa.Text(), nullable=True))
    op.add_column(
        "appointments",
        sa.Column("cancelled_at", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "appointments", sa.Column("cancelled_by_id", sa.String(), nullable=True)
    )
    op.add_column(
        "appointments", sa.Column("cancellation_reason", sa.Text(), nullable=True)
    )
    op.alter_column(
        "appointments", "appointment_type", existing_type=sa.VARCHAR(), nullable=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "appointments", "appointment_type", existing_type=sa.VARCHAR(), nullable=True
    )
    op.drop_column("appointments", "cancellation_reason")
    op.drop_column("appointments", "cancelled_by_id")
    op.drop_column("appointments", "cancelled_at")
    op.drop_column("appointments", "reason")
    op.drop_column("appointments", "duration_minutes")
    # ### end Alembic commands ###
