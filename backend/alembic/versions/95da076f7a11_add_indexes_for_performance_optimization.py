"""Add indexes for performance optimization

Revision ID: 95da076f7a11
Revises: 48c7db429bc7
Create Date: 2025-04-01 09:25:31.982862

"""

from collections.abc import Sequence
from typing import Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "95da076f7a11"
down_revision: Union[str, None] = "48c7db429bc7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_audit_logs_action_created_at",
        "audit_logs",
        ["action", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_audit_logs_actor_user_id_created_at",
        "audit_logs",
        ["actor_user_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_chat_messages_patient_id_created_at",
        "chat_messages",
        ["patient_id", "created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_clinicians_is_active"), "clinicians", ["is_active"], unique=False
    )
    op.create_index(
        "ix_content_published_type",
        "contents",
        ["is_published", "content_type"],
        unique=False,
    )
    op.drop_index("ix_medication_requests_status", table_name="medication_requests")
    op.create_index(
        "ix_med_req_patient_id_created_at",
        "medication_requests",
        ["patient_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_med_req_patient_id_status",
        "medication_requests",
        ["patient_id", "status"],
        unique=False,
    )
    op.create_index(
        op.f("ix_patients_is_active"), "patients", ["is_active"], unique=False
    )
    op.drop_index("ix_side_effect_reports_severity", table_name="side_effect_reports")
    op.drop_index("ix_side_effect_reports_status", table_name="side_effect_reports")
    op.create_index(
        "ix_side_effect_reports_patient_id_created_at",
        "side_effect_reports",
        ["patient_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_side_effect_reports_patient_id_severity",
        "side_effect_reports",
        ["patient_id", "severity"],
        unique=False,
    )
    op.create_index(
        "ix_side_effect_reports_patient_id_status",
        "side_effect_reports",
        ["patient_id", "status"],
        unique=False,
    )
    op.drop_index("ix_weight_logs_log_date", table_name="weight_logs")
    op.create_index(
        "ix_weight_logs_patient_id_log_date",
        "weight_logs",
        ["patient_id", "log_date"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_weight_logs_patient_id_log_date", table_name="weight_logs")
    op.create_index(
        "ix_weight_logs_log_date", "weight_logs", ["log_date"], unique=False
    )
    op.drop_index(
        "ix_side_effect_reports_patient_id_status", table_name="side_effect_reports"
    )
    op.drop_index(
        "ix_side_effect_reports_patient_id_severity", table_name="side_effect_reports"
    )
    op.drop_index(
        "ix_side_effect_reports_patient_id_created_at", table_name="side_effect_reports"
    )
    op.create_index(
        "ix_side_effect_reports_status", "side_effect_reports", ["status"], unique=False
    )
    op.create_index(
        "ix_side_effect_reports_severity",
        "side_effect_reports",
        ["severity"],
        unique=False,
    )
    op.drop_index(op.f("ix_patients_is_active"), table_name="patients")
    op.drop_index("ix_med_req_patient_id_status", table_name="medication_requests")
    op.drop_index("ix_med_req_patient_id_created_at", table_name="medication_requests")
    op.create_index(
        "ix_medication_requests_status", "medication_requests", ["status"], unique=False
    )
    op.drop_index("ix_content_published_type", table_name="contents")
    op.drop_index(op.f("ix_clinicians_is_active"), table_name="clinicians")
    op.drop_index("ix_chat_messages_patient_id_created_at", table_name="chat_messages")
    op.drop_index("ix_audit_logs_actor_user_id_created_at", table_name="audit_logs")
    op.drop_index("ix_audit_logs_action_created_at", table_name="audit_logs")
    # ### end Alembic commands ###
