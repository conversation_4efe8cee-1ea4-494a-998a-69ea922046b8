"""Add last_scraped_at to Clinic model

Revision ID: 963211e8e44f
Revises: 866e05201481
Create Date: 2025-04-07 17:49:27.839592

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "963211e8e44f"
down_revision: Union[str, None] = "866e05201481"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "clinics",
        sa.Column("last_scraped_at", sa.DateTime(timezone=True), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("clinics", "last_scraped_at")
    # ### end Alembic commands ###
