"""Add clinic_medication_association table

Revision ID: 96df0601da4c
Revises: b5d0eeea1376
Create Date: 2025-04-14 12:02:58.329009

"""

from collections.abc import Sequence
from typing import Union

# revision identifiers, used by Alembic.
revision: str = "96df0601da4c"
down_revision: Union[str, None] = "b5d0eeea1376"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
