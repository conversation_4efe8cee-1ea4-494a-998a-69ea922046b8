"""Add appointment_requests table

Revision ID: a2ab82016c0f
Revises: c2607f8641af
Create Date: 2025-04-22 20:04:13.239874

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a2ab82016c0f"
down_revision: Union[str, None] = "c2607f8641af"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "notifications",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("recipient_clinician_id", sa.String(), nullable=False),
        sa.Column("notification_type", sa.String(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("message", sa.Text(), nullable=True),
        sa.Column("related_entity_type", sa.String(), nullable=True),
        sa.Column("related_entity_id", sa.UUID(), nullable=True),
        sa.Column("is_read", sa.Boolean(), nullable=False),
        sa.Column("read_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["recipient_clinician_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_notifications_is_read", "notifications", ["is_read"], unique=False
    )
    op.create_index(
        op.f("ix_notifications_notification_type"),
        "notifications",
        ["notification_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_notifications_recipient_clinician_id"),
        "notifications",
        ["recipient_clinician_id"],
        unique=False,
    )
    op.create_table(
        "appointment_requests",
        sa.Column("patient_id", sa.String(), nullable=False),
        sa.Column("preferred_datetime", sa.DateTime(timezone=True), nullable=False),
        sa.Column("reason", sa.Text(), nullable=False),
        sa.Column("clinician_preference", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("reviewed_by_id", sa.String(), nullable=True),
        sa.Column("reviewed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("review_notes", sa.Text(), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["patient_id"],
            ["patients.id"],
        ),
        sa.ForeignKeyConstraint(
            ["reviewed_by_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_appointment_requests_id"), "appointment_requests", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_appointment_requests_patient_id"),
        "appointment_requests",
        ["patient_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_appointment_requests_preferred_datetime"),
        "appointment_requests",
        ["preferred_datetime"],
        unique=False,
    )
    op.create_index(
        op.f("ix_appointment_requests_reviewed_by_id"),
        "appointment_requests",
        ["reviewed_by_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_appointment_requests_status"),
        "appointment_requests",
        ["status"],
        unique=False,
    )
    op.drop_index("ix_notification_rule_clinic_id", table_name="notification_rule")
    op.drop_index("ix_notification_rule_event_type", table_name="notification_rule")
    op.drop_index("ix_notification_rule_template_id", table_name="notification_rule")
    op.drop_index(
        "uq_notification_rule_active",
        table_name="notification_rule",
        postgresql_where="is_active",
    )
    op.drop_table("notification_rule")
    op.drop_index("ix_notification_clinic_recipient_status", table_name="notification")
    op.drop_index(
        "ix_notification_unread",
        table_name="notification",
        postgresql_where="(status = 'unread'::notificationstatus)",
    )
    op.drop_table("notification")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "notification",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("clinic_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "recipient_id", sa.VARCHAR(length=190), autoincrement=False, nullable=False
        ),
        sa.Column(
            "recipient_role",
            postgresql.ENUM("patient", "clinician", "admin", name="userrole"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "notif_type",
            postgresql.ENUM("appointment", "medication", name="notificationtype"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "channel",
            postgresql.ENUM("in_app", "email", "sms", name="notificationchannel"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("template_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("content", sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column(
            "status",
            postgresql.ENUM("unread", "read", "archived", name="notificationstatus"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "metadata",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "read_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("trigger_event_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("actor_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "recipient_clinician_id", sa.VARCHAR(), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["actor_id"], ["clinicians.id"], name="notification_actor_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
            name="notification_clinic_id_fkey",
            ondelete="RESTRICT",
        ),
        sa.ForeignKeyConstraint(
            ["recipient_clinician_id"],
            ["clinicians.id"],
            name="notification_recipient_clinician_id_fkey",
            ondelete="SET NULL",
        ),
        sa.ForeignKeyConstraint(
            ["template_id"],
            ["templates.id"],
            name="notification_template_id_fkey",
            ondelete="SET NULL",
        ),
        sa.ForeignKeyConstraint(
            ["trigger_event_id"],
            ["event_logs.id"],
            name="notification_trigger_event_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="notification_pkey"),
    )
    op.create_index(
        "ix_notification_unread",
        "notification",
        ["recipient_id"],
        unique=False,
        postgresql_where="(status = 'unread'::notificationstatus)",
    )
    op.create_index(
        "ix_notification_clinic_recipient_status",
        "notification",
        ["clinic_id", "recipient_id", "status"],
        unique=False,
    )
    op.create_table(
        "notification_rule",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("clinic_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("event_type", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("condition_expr", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("template_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("cooldown_minutes", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("recipient_selector", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "channels",
            postgresql.ARRAY(
                postgresql.ENUM("in_app", "email", "sms", name="notificationchannel")
            ),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
            name="notification_rule_clinic_id_fkey",
            ondelete="RESTRICT",
        ),
        sa.ForeignKeyConstraint(
            ["template_id"],
            ["templates.id"],
            name="notification_rule_template_id_fkey",
            ondelete="SET NULL",
        ),
        sa.PrimaryKeyConstraint("id", name="notification_rule_pkey"),
    )
    op.create_index(
        "uq_notification_rule_active",
        "notification_rule",
        ["clinic_id", "event_type", "template_id"],
        unique=True,
        postgresql_where="is_active",
    )
    op.create_index(
        "ix_notification_rule_template_id",
        "notification_rule",
        ["template_id"],
        unique=False,
    )
    op.create_index(
        "ix_notification_rule_event_type",
        "notification_rule",
        ["event_type"],
        unique=False,
    )
    op.create_index(
        "ix_notification_rule_clinic_id",
        "notification_rule",
        ["clinic_id"],
        unique=False,
    )
    op.drop_index(
        op.f("ix_appointment_requests_status"), table_name="appointment_requests"
    )
    op.drop_index(
        op.f("ix_appointment_requests_reviewed_by_id"),
        table_name="appointment_requests",
    )
    op.drop_index(
        op.f("ix_appointment_requests_preferred_datetime"),
        table_name="appointment_requests",
    )
    op.drop_index(
        op.f("ix_appointment_requests_patient_id"), table_name="appointment_requests"
    )
    op.drop_index(op.f("ix_appointment_requests_id"), table_name="appointment_requests")
    op.drop_table("appointment_requests")
    op.drop_index(
        op.f("ix_notifications_recipient_clinician_id"), table_name="notifications"
    )
    op.drop_index(
        op.f("ix_notifications_notification_type"), table_name="notifications"
    )
    op.drop_index("ix_notifications_is_read", table_name="notifications")
    op.drop_table("notifications")
    # ### end Alembic commands ###
