"""Add Clinic model and Clinician-Clinic relationship

Revision ID: a3e31158b767
Revises: b2c3d4e5f6a7
Create Date: 2025-04-05 12:35:45.805092

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a3e31158b767"
down_revision: Union[str, None] = "b2c3d4e5f6a7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "clinics",
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("website_url", sa.String(length=512), nullable=True),
        sa.Column("knowledge_base_id", sa.String(length=255), nullable=True),
        sa.Column("chatbot_instance_id", sa.String(length=255), nullable=True),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_clinics_chatbot_instance_id"),
        "clinics",
        ["chatbot_instance_id"],
        unique=False,
    )
    op.create_index(op.f("ix_clinics_id"), "clinics", ["id"], unique=False)
    op.create_index(
        op.f("ix_clinics_knowledge_base_id"),
        "clinics",
        ["knowledge_base_id"],
        unique=False,
    )
    op.create_index(op.f("ix_clinics_name"), "clinics", ["name"], unique=False)
    op.create_table(
        "clinician_clinic_association",
        sa.Column("clinician_id", sa.String(), nullable=False),
        sa.Column("clinic_id", sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
        ),
        sa.ForeignKeyConstraint(
            ["clinician_id"],
            ["clinicians.id"],
        ),
        sa.PrimaryKeyConstraint("clinician_id", "clinic_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("clinician_clinic_association")
    op.drop_index(op.f("ix_clinics_name"), table_name="clinics")
    op.drop_index(op.f("ix_clinics_knowledge_base_id"), table_name="clinics")
    op.drop_index(op.f("ix_clinics_id"), table_name="clinics")
    op.drop_index(op.f("ix_clinics_chatbot_instance_id"), table_name="clinics")
    op.drop_table("clinics")
    # ### end Alembic commands ###
