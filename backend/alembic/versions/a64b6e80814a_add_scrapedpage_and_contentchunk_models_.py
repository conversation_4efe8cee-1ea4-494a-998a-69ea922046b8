"""Add ScrapedPage and ContentChunk models for RAG

Revision ID: a64b6e80814a
Revises: 2136f16acfa1
Create Date: 2025-04-06 13:52:02.883739

"""

from collections.abc import Sequence
from typing import Union

import pgvector.sqlalchemy  # Add import for pgvector types
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a64b6e80814a"
down_revision: Union[str, None] = "2136f16acfa1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Enable the pgvector extension if it doesn't exist
    op.execute("CREATE EXTENSION IF NOT EXISTS vector;")
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "scraped_pages",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("clinic_id", sa.UUID(), nullable=False),
        sa.Column("source_url", sa.String(), nullable=False),
        sa.Column(
            "scraped_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("cleaned_content", sa.Text(), nullable=True),
        sa.Column("page_metadata", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_scraped_pages_clinic_id"), "scraped_pages", ["clinic_id"], unique=False
    )
    op.create_index(
        op.f("ix_scraped_pages_source_url"),
        "scraped_pages",
        ["source_url"],
        unique=False,
    )
    op.create_table(
        "content_chunks",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("scraped_page_id", sa.UUID(), nullable=False),
        sa.Column("chunk_text", sa.Text(), nullable=False),
        sa.Column("embedding", pgvector.sqlalchemy.Vector(dim=768), nullable=True),
        sa.Column("chunk_metadata", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["scraped_page_id"],
            ["scraped_pages.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_content_chunks_scraped_page_id"),
        "content_chunks",
        ["scraped_page_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_content_chunks_scraped_page_id"), table_name="content_chunks"
    )
    op.drop_table("content_chunks")
    op.drop_index(op.f("ix_scraped_pages_source_url"), table_name="scraped_pages")
    op.drop_index(op.f("ix_scraped_pages_clinic_id"), table_name="scraped_pages")
    op.drop_table("scraped_pages")
    # ### end Alembic commands ###
    # Drop the pgvector extension if it exists (optional, depends on policy)
    # Note: This might fail if other tables still depend on the vector type.
    # Consider leaving it enabled or handling dependencies carefully.
    # For simplicity here, we'll attempt to drop it.
    op.execute("DROP EXTENSION IF EXISTS vector;")
