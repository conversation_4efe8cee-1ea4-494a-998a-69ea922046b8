"""add_education_materials_table

Revision ID: a8bcb9452f64
Revises: aa4fde76c3b3
Create Date: 2025-05-22 17:36:26.940768

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a8bcb9452f64'
down_revision: Union[str, None] = 'aa4fde76c3b3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # First drop constraints that might interfere
    op.drop_constraint('education_materials_clinic_id_fkey', 'education_materials', type_='foreignkey')
    op.drop_constraint('education_materials_created_by_fkey', 'education_materials', type_='foreignkey')
    op.drop_index('ix_education_materials_is_active', table_name='education_materials')
    
    # Add new columns
    op.add_column('education_materials', sa.Column('views_count', sa.Integer(), nullable=False))
    op.add_column('education_materials', sa.Column('assignments_count', sa.Integer(), nullable=False))
    
    # Create new enum type
    materialtype_enum = sa.Enum('PDF', 'VIDEO', 'LINK', 'DOCUMENT', name='materialtype')
    materialtype_enum.create(op.get_bind())
    
    # Change the column types using USING clause for PostgreSQL
    op.execute('ALTER TABLE education_materials ALTER COLUMN type TYPE materialtype USING type::text::materialtype')
    op.execute('ALTER TABLE education_materials ALTER COLUMN created_by TYPE UUID USING created_by::uuid')
    
    # Change content_url to Text
    op.alter_column('education_materials', 'content_url',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
    
    # Drop old columns
    op.drop_column('education_materials', 'tags')
    op.drop_column('education_materials', 'is_active')
    op.drop_column('education_materials', 'file_size')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('education_materials', sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('education_materials', sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.add_column('education_materials', sa.Column('tags', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.create_foreign_key('education_materials_created_by_fkey', 'education_materials', 'clinicians', ['created_by'], ['id'])
    op.create_foreign_key('education_materials_clinic_id_fkey', 'education_materials', 'clinics', ['clinic_id'], ['id'])
    op.create_index('ix_education_materials_is_active', 'education_materials', ['is_active'], unique=False)
    # Change created_by back to VARCHAR
    op.execute('ALTER TABLE education_materials ALTER COLUMN created_by TYPE VARCHAR USING created_by::varchar')
    op.alter_column('education_materials', 'content_url',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    # Change back to original enum type
    op.execute('ALTER TABLE education_materials ALTER COLUMN type TYPE material_type_enum USING type::text::material_type_enum')
    
    # Drop the new enum type
    materialtype_enum = sa.Enum('PDF', 'VIDEO', 'LINK', 'DOCUMENT', name='materialtype')
    materialtype_enum.drop(op.get_bind())
    op.drop_column('education_materials', 'assignments_count')
    op.drop_column('education_materials', 'views_count')
    # ### end Alembic commands ###
