"""Add education material system models

Revision ID: aa4fde76c3b3
Revises: add_clinician_route_2025
Create Date: 2025-05-22 16:40:43.237094

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'aa4fde76c3b3'
down_revision: Union[str, None] = 'add_clinician_route_2025'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # First, drop the foreign key constraint before dropping the table
    op.drop_index('ix_chat_messages_conversation_id', table_name='chat_messages')
    op.drop_constraint('chat_messages_conversation_id_fkey', 'chat_messages', type_='foreignkey')
    op.drop_column('chat_messages', 'conversation_id')
    
    # Now we can safely drop the conversations table and its indexes
    op.drop_index('ix_conversations_clinician_id', table_name='conversations')
    op.drop_index('ix_conversations_clinician_status', table_name='conversations')
    op.drop_index('ix_conversations_id', table_name='conversations')
    op.drop_index('ix_conversations_last_message', table_name='conversations')
    op.drop_index('ix_conversations_patient_clinician', table_name='conversations')
    op.drop_index('ix_conversations_patient_id', table_name='conversations')
    op.drop_index('ix_conversations_status', table_name='conversations')
    op.drop_table('conversations')
    
    # Create the education system tables
    op.create_table('education_materials',
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('type', sa.Enum('PDF', 'VIDEO', 'LINK', 'DOCUMENT', name='material_type_enum'), nullable=False),
    sa.Column('content_url', sa.String(), nullable=True),
    sa.Column('file_path', sa.String(), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('duration_minutes', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=False),
    sa.Column('clinic_id', sa.UUID(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['clinic_id'], ['clinics.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['clinicians.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_education_materials_category'), 'education_materials', ['category'], unique=False)
    op.create_index(op.f('ix_education_materials_clinic_id'), 'education_materials', ['clinic_id'], unique=False)
    op.create_index(op.f('ix_education_materials_created_by'), 'education_materials', ['created_by'], unique=False)
    op.create_index(op.f('ix_education_materials_id'), 'education_materials', ['id'], unique=False)
    op.create_index(op.f('ix_education_materials_is_active'), 'education_materials', ['is_active'], unique=False)
    op.create_index(op.f('ix_education_materials_is_public'), 'education_materials', ['is_public'], unique=False)
    op.create_index(op.f('ix_education_materials_title'), 'education_materials', ['title'], unique=False)
    op.create_index(op.f('ix_education_materials_type'), 'education_materials', ['type'], unique=False)
    op.create_table('patient_education_assignments',
    sa.Column('patient_id', sa.String(), nullable=False),
    sa.Column('material_id', sa.UUID(), nullable=False),
    sa.Column('assigned_by', sa.String(), nullable=False),
    sa.Column('assigned_at', sa.DateTime(), nullable=False),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='assignment_priority_enum'), nullable=False),
    sa.Column('clinician_notes', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('ASSIGNED', 'VIEWED', 'IN_PROGRESS', 'COMPLETED', 'OVERDUE', name='assignment_status_enum'), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['assigned_by'], ['clinicians.id'], ),
    sa.ForeignKeyConstraint(['material_id'], ['education_materials.id'], ),
    sa.ForeignKeyConstraint(['patient_id'], ['patients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_education_assignments_assigned_at'), 'patient_education_assignments', ['assigned_at'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_assigned_by'), 'patient_education_assignments', ['assigned_by'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_due_date'), 'patient_education_assignments', ['due_date'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_id'), 'patient_education_assignments', ['id'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_material_id'), 'patient_education_assignments', ['material_id'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_patient_id'), 'patient_education_assignments', ['patient_id'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_priority'), 'patient_education_assignments', ['priority'], unique=False)
    op.create_index(op.f('ix_patient_education_assignments_status'), 'patient_education_assignments', ['status'], unique=False)
    op.create_table('education_progress',
    sa.Column('patient_id', sa.String(), nullable=False),
    sa.Column('material_id', sa.UUID(), nullable=False),
    sa.Column('assignment_id', sa.UUID(), nullable=False),
    sa.Column('progress_percentage', sa.Float(), nullable=False),
    sa.Column('time_spent_minutes', sa.Integer(), nullable=False),
    sa.Column('last_accessed', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('patient_feedback', sa.Text(), nullable=True),
    sa.Column('patient_rating', sa.Integer(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['assignment_id'], ['patient_education_assignments.id'], ),
    sa.ForeignKeyConstraint(['material_id'], ['education_materials.id'], ),
    sa.ForeignKeyConstraint(['patient_id'], ['patients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_education_progress_assignment_id'), 'education_progress', ['assignment_id'], unique=False)
    op.create_index(op.f('ix_education_progress_completed_at'), 'education_progress', ['completed_at'], unique=False)
    op.create_index(op.f('ix_education_progress_id'), 'education_progress', ['id'], unique=False)
    op.create_index(op.f('ix_education_progress_last_accessed'), 'education_progress', ['last_accessed'], unique=False)
    op.create_index(op.f('ix_education_progress_material_id'), 'education_progress', ['material_id'], unique=False)
    op.create_index(op.f('ix_education_progress_patient_id'), 'education_progress', ['patient_id'], unique=False)
    op.create_index(op.f('ix_education_progress_progress_percentage'), 'education_progress', ['progress_percentage'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('conversation_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key('chat_messages_conversation_id_fkey', 'chat_messages', 'conversations', ['conversation_id'], ['id'])
    op.create_index('ix_chat_messages_conversation_id', 'chat_messages', ['conversation_id'], unique=False)
    op.create_table('conversations',
    sa.Column('patient_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('clinician_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'ARCHIVED', 'RESOLVED', name='conversation_status_enum'), autoincrement=False, nullable=False),
    sa.Column('last_message_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['clinician_id'], ['clinicians.id'], name='conversations_clinician_id_fkey'),
    sa.ForeignKeyConstraint(['patient_id'], ['patients.id'], name='conversations_patient_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='conversations_pkey')
    )
    op.create_index('ix_conversations_status', 'conversations', ['status'], unique=False)
    op.create_index('ix_conversations_patient_id', 'conversations', ['patient_id'], unique=False)
    op.create_index('ix_conversations_patient_clinician', 'conversations', ['patient_id', 'clinician_id'], unique=False)
    op.create_index('ix_conversations_last_message', 'conversations', ['last_message_at'], unique=False)
    op.create_index('ix_conversations_id', 'conversations', ['id'], unique=False)
    op.create_index('ix_conversations_clinician_status', 'conversations', ['clinician_id', 'status'], unique=False)
    op.create_index('ix_conversations_clinician_id', 'conversations', ['clinician_id'], unique=False)
    op.drop_index(op.f('ix_education_progress_progress_percentage'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_patient_id'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_material_id'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_last_accessed'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_id'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_completed_at'), table_name='education_progress')
    op.drop_index(op.f('ix_education_progress_assignment_id'), table_name='education_progress')
    op.drop_table('education_progress')
    op.drop_index(op.f('ix_patient_education_assignments_status'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_priority'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_patient_id'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_material_id'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_id'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_due_date'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_assigned_by'), table_name='patient_education_assignments')
    op.drop_index(op.f('ix_patient_education_assignments_assigned_at'), table_name='patient_education_assignments')
    op.drop_table('patient_education_assignments')
    op.drop_index(op.f('ix_education_materials_type'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_title'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_is_public'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_is_active'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_id'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_created_by'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_clinic_id'), table_name='education_materials')
    op.drop_index(op.f('ix_education_materials_category'), table_name='education_materials')
    op.drop_table('education_materials')
    # ### end Alembic commands ###
