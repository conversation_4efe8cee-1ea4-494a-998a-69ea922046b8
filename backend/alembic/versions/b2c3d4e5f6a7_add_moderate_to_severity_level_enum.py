"""add moderate to severity_level_enum

Revision ID: b2c3d4e5f6a7
Revises: 3315adcc562b
Create Date: 2025-04-04 16:56:00.000000

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b2c3d4e5f6a7"
down_revision = "3315adcc562b"
branch_labels = None
depends_on = None


def upgrade():
    # Add 'moderate' value to the severity_level_enum PostgreSQL enum type
    op.execute("ALTER TYPE severity_level_enum ADD VALUE IF NOT EXISTS 'moderate';")


def downgrade():
    # Downgrading enums by removing values is not directly supported in PostgreSQL
    # This would require recreating the enum type without the 'moderate' value
    # and updating all tables/columns that use it, which is complex and risky.
    # Therefore, we leave this empty and document that downgrade is not supported for this migration.
    pass
