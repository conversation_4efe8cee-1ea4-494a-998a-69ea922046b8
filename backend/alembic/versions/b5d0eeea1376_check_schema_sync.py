"""check_schema_sync

Revision ID: b5d0eeea1376
Revises: e7f717cea780
Create Date: 2025-04-14 11:46:28.108062

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b5d0eeea1376"
down_revision: Union[str, None] = "e7f717cea780"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "clinic_medication_association",
        sa.Column("clinic_id", sa.UUID(), nullable=False),
        sa.Column("medication_id", sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(
            ["clinic_id"],
            ["clinics.id"],
        ),
        sa.ForeignKeyConstraint(
            ["medication_id"],
            ["medications.id"],
        ),
        sa.PrimaryKeyConstraint("clinic_id", "medication_id"),
    )
    op.drop_index("ix_event_relationships_event_id", table_name="event_relationships")
    op.drop_index(
        "ix_event_relationships_event_id_related_event_id",
        table_name="event_relationships",
    )
    op.drop_index("ix_event_relationships_id", table_name="event_relationships")
    op.drop_index(
        "ix_event_relationships_related_event_id", table_name="event_relationships"
    )
    op.drop_index(
        "ix_event_relationships_relationship_type", table_name="event_relationships"
    )
    op.drop_table("event_relationships")
    op.drop_index("ix_base_events_actor_user_id", table_name="base_events")
    op.drop_index("ix_base_events_actor_user_id_created_at", table_name="base_events")
    op.drop_index("ix_base_events_event_category", table_name="base_events")
    op.drop_index("ix_base_events_event_type", table_name="base_events")
    op.drop_index("ix_base_events_event_type_created_at", table_name="base_events")
    op.drop_index("ix_base_events_id", table_name="base_events")
    op.drop_index("ix_base_events_outcome", table_name="base_events")
    op.drop_index("ix_base_events_related_event_id", table_name="base_events")
    op.drop_index("ix_base_events_target_resource_id", table_name="base_events")
    op.drop_index("ix_base_events_target_resource_type", table_name="base_events")
    op.drop_index(
        "ix_base_events_target_resource_type_id_created_at", table_name="base_events"
    )
    op.drop_table("base_events")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "base_events",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "event_type", sa.VARCHAR(length=100), autoincrement=False, nullable=False
        ),
        sa.Column(
            "event_category",
            sa.VARCHAR(length=100),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("actor_user_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "actor_role", sa.VARCHAR(length=50), autoincrement=False, nullable=True
        ),
        sa.Column(
            "target_resource_type",
            sa.VARCHAR(length=100),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "target_resource_id", sa.VARCHAR(), autoincrement=False, nullable=False
        ),
        sa.Column("related_event_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column(
            "outcome", sa.VARCHAR(length=50), autoincrement=False, nullable=False
        ),
        sa.Column(
            "details",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "source_ip", sa.VARCHAR(length=100), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["related_event_id"],
            ["base_events.id"],
            name="base_events_related_event_id_fkey",
            ondelete="SET NULL",
        ),
        sa.PrimaryKeyConstraint("id", name="base_events_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index(
        "ix_base_events_target_resource_type_id_created_at",
        "base_events",
        ["target_resource_type", "target_resource_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_base_events_target_resource_type",
        "base_events",
        ["target_resource_type"],
        unique=False,
    )
    op.create_index(
        "ix_base_events_target_resource_id",
        "base_events",
        ["target_resource_id"],
        unique=False,
    )
    op.create_index(
        "ix_base_events_related_event_id",
        "base_events",
        ["related_event_id"],
        unique=False,
    )
    op.create_index("ix_base_events_outcome", "base_events", ["outcome"], unique=False)
    op.create_index("ix_base_events_id", "base_events", ["id"], unique=False)
    op.create_index(
        "ix_base_events_event_type_created_at",
        "base_events",
        ["event_type", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_base_events_event_type", "base_events", ["event_type"], unique=False
    )
    op.create_index(
        "ix_base_events_event_category", "base_events", ["event_category"], unique=False
    )
    op.create_index(
        "ix_base_events_actor_user_id_created_at",
        "base_events",
        ["actor_user_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "ix_base_events_actor_user_id", "base_events", ["actor_user_id"], unique=False
    )
    op.create_table(
        "event_relationships",
        sa.Column("event_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("related_event_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "relationship_type",
            sa.VARCHAR(length=50),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["event_id"],
            ["base_events.id"],
            name="event_relationships_event_id_fkey",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["related_event_id"],
            ["base_events.id"],
            name="event_relationships_related_event_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name="event_relationships_pkey"),
    )
    op.create_index(
        "ix_event_relationships_relationship_type",
        "event_relationships",
        ["relationship_type"],
        unique=False,
    )
    op.create_index(
        "ix_event_relationships_related_event_id",
        "event_relationships",
        ["related_event_id"],
        unique=False,
    )
    op.create_index(
        "ix_event_relationships_id", "event_relationships", ["id"], unique=False
    )
    op.create_index(
        "ix_event_relationships_event_id_related_event_id",
        "event_relationships",
        ["event_id", "related_event_id"],
        unique=False,
    )
    op.create_index(
        "ix_event_relationships_event_id",
        "event_relationships",
        ["event_id"],
        unique=False,
    )
    op.drop_table("clinic_medication_association")
    # ### end Alembic commands ###
