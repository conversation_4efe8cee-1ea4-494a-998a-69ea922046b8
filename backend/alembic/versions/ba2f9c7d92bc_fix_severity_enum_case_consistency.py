"""fix_severity_enum_case_consistency

Revision ID: ba2f9c7d92bc
Revises: 45ecf4b44971
Create Date: 2025-04-10 13:07:41.144456

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ba2f9c7d92bc"
down_revision: Union[str, None] = "45ecf4b44971"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the existing enum type
    op.execute("DROP TYPE IF EXISTS severitylevel CASCADE")

    # Create a new enum type with lowercase values
    op.execute(
        """
        CREATE TYPE severitylevel AS ENUM ('minor', 'moderate', 'major')
    """
    )

    # Update the column to use the new enum type
    op.alter_column(
        "side_effect_reports",
        "severity",
        type_=sa.Enum("minor", "moderate", "major", name="severitylevel"),
        postgresql_using="severity::text::severitylevel",
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the enum type
    op.execute("DROP TYPE IF EXISTS severitylevel CASCADE")

    # Recreate the original enum type
    op.execute(
        """
        CREATE TYPE severitylevel AS ENUM ('MINOR', 'MODERATE', 'MAJOR')
    """
    )

    # Update the column to use the original enum type
    op.alter_column(
        "side_effect_reports",
        "severity",
        type_=sa.Enum("MINOR", "MODERATE", "MAJOR", name="severitylevel"),
        postgresql_using="severity::text::severitylevel",
    )
