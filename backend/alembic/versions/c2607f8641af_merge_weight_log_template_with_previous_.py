"""merge weight_log_template with previous migrations

Revision ID: c2607f8641af
Revises: 0531618b035f, 20250421_114500
Create Date: 2025-04-21 16:59:42.985936

"""

from collections.abc import Sequence
from typing import Union

# revision identifiers, used by Alembic.
revision: str = "c2607f8641af"
down_revision: Union[str, None] = ("0531618b035f", "20250421_114500")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
