"""Me<PERSON> heads for metadata addition

Revision ID: d244f6733ac1
Revises: add_metadata_chat_msg, 95afe5831646
Create Date: 2025-05-25 20:50:11.293259

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd244f6733ac1'
down_revision: Union[str, None] = ('add_metadata_chat_msg', '95afe5831646')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
