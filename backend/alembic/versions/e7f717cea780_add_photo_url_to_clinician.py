"""add_photo_url_to_clinician

Revision ID: e7f717cea780
Revises: 83ca41e08db3
Create Date: 2025-04-10 13:44:43.099716

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e7f717cea780"
down_revision: Union[str, None] = "83ca41e08db3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column("clinicians", sa.Column("photo_url", sa.String(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column("clinicians", "photo_url")
