"""remove firebase_uid and ensure clerk_id

Revision ID: f4325ef73a47
Revises: 271c5cbea2ab
Create Date: 2025-04-03 13:36:27.663794

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f4325ef73a47"
down_revision: Union[str, None] = "271c5cbea2ab"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "clinicians", "is_active", existing_type=sa.BOOLEAN(), nullable=True
    )
    op.alter_column(
        "clinicians",
        "created_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "clinicians",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),
    )
    op.drop_index("ix_clinicians_firebase_uid", table_name="clinicians")
    op.drop_index("ix_clinicians_id", table_name="clinicians")
    op.drop_index("ix_clinicians_is_active", table_name="clinicians")
    op.drop_column("clinicians", "firebase_uid")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "clinicians",
        sa.Column("firebase_uid", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.create_index(
        "ix_clinicians_is_active", "clinicians", ["is_active"], unique=False
    )
    op.create_index("ix_clinicians_id", "clinicians", ["id"], unique=False)
    op.create_index(
        "ix_clinicians_firebase_uid", "clinicians", ["firebase_uid"], unique=True
    )
    op.alter_column(
        "clinicians",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "clinicians",
        "created_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "clinicians", "is_active", existing_type=sa.BOOLEAN(), nullable=False
    )
    # ### end Alembic commands ###
