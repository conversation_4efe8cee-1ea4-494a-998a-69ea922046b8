"""add clinic foreign key to education materials

Revision ID: fix_clinic_fk_20250522
Revises: fix_edu_fk_20250522
Create Date: 2025-05-22 17:47:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_clinic_fk_20250522'
down_revision = 'fix_edu_fk_20250522'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add foreign key constraint for clinic_id in education_materials
    op.create_foreign_key('fk_education_materials_clinic_id_clinics',
                         'education_materials', 'clinics',
                         ['clinic_id'], ['id'])


def downgrade() -> None:
    # Remove foreign key constraint
    op.drop_constraint('fk_education_materials_clinic_id_clinics', 'education_materials', type_='foreignkey')