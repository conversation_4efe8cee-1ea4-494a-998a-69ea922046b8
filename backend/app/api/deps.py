import logging
import uuid
from collections.abc import Generator
from typing import Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app import crud, models  # Unified CRUD import as per constraints
from app.core.auth_utils import clerk, verify_access_token
from app.db.session import SessionLocal
from app.schemas.auth import TokenPayload
from app.schemas.clinic import ClinicRead
from app.schemas.clinician import ClinicianCreate

logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/clinician-login",  # TODO: Should this point to a Clerk endpoint or remain internal? Review auth flow.
    auto_error=False,
)


# --- get_db remains the same ---
def get_db() -> Generator:
    """Dependency to get a database session."""
    db = SessionLocal()
    try:
        yield db
        # If the endpoint code completes without raising an exception,
        # the transaction could be committed here, but typically commits
        # are handled within the CRUD operations themselves.
        # db.commit()
    except Exception:
        # If any exception occurs during the request handling (after yield db),
        # rollback the transaction to ensure consistency.
        db.rollback()
        # Re-raise the original exception so FastAPI can handle it (e.g., return 4xx/5xx)
        raise
    finally:
        # Always close the session to return it to the pool.
        db.close()


# Modified to accept and pass Request object
async def get_current_user(
    request: Request,  # Added Request dependency
    token: Optional[str] = Depends(oauth2_scheme),
) -> TokenPayload:
    """
    Dependency to get the current user from the token.
    Verifies Clerk (RS256) or internal (HS256) tokens via verify_access_token.
    Returns the token payload with standardized fields.
    Does NOT check for specific roles here.
    """
    logger.info(
        f"Token received in get_current_user: {token[:10] if token else 'None'}..."
    )
    logger.debug(
        f"Attempting to get current user. Token extracted by OAuth2 scheme: {str(token)[:10] if token else 'None'}..."
    )

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if token is None:
        logger.error(
            "OAuth2 scheme returned None token. Authorization header likely missing or invalid."
        )
        raise credentials_exception

    # Pass the request object to verify_access_token
    return await verify_access_token(
        token=token, request=request, credentials_exception=credentials_exception
    )


def get_current_patient(
    current_user: TokenPayload = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """Dependency to get the current user, ensuring they have the 'patient' role."""
    if not isinstance(current_user.role, list) and current_user.role != "patient":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. User is not a patient.",
        )

    try:
        # Get Clerk user data
        clerk_user = clerk.users.get(user_id=current_user.sub)

        # Extract names from Clerk user data
        first_name = clerk_user.first_name if clerk_user else None
        last_name = clerk_user.last_name if clerk_user else None

        logger.info(f"Clerk user data - First: {first_name}, Last: {last_name}")

        create_data = {
            "id": current_user.sub,
            "email": current_user.email,
            "first_name": first_name,
            "last_name": last_name,
            "invited_by_clinician_id": (
                current_user.public_metadata.get("inviter_user_id")
                if hasattr(current_user, "public_metadata")
                else None
            ),
            # --- BEGIN FIX: Extract associated_clinic_id from token metadata ---
            "associated_clinic_id": (
                current_user.public_metadata.get("associated_clinic_id")
                if hasattr(current_user, "public_metadata")
                else None
            ),
            # --- END FIX ---
            "is_active": True,
        }

        logger.info(
            f"Patient create/update data: {create_data}"
        )  # Log the data including clinic_id

        # Using unified CRUD import pattern
        patient_record = crud.patient.get_or_create(
            db=db,
            email=current_user.email,
            create_data=create_data,  # Pass data including clinic_id
        )

        # --- BEGIN FIX: Check and update associated_clinic_id if needed ---
        if patient_record:
            token_clinic_id_str = create_data.get("associated_clinic_id")
            db_clinic_id_str = (
                str(patient_record.associated_clinic_id)
                if patient_record.associated_clinic_id
                else None
            )

            if token_clinic_id_str and token_clinic_id_str != db_clinic_id_str:
                logger.warning(
                    f"Mismatch detected for patient {patient_record.id}: "
                    f"Token clinic_id='{token_clinic_id_str}', DB clinic_id='{db_clinic_id_str}'. Updating DB."
                )
                try:
                    # Attempt to convert token_clinic_id_str to UUID for update
                    clinic_id_uuid = uuid.UUID(token_clinic_id_str)
                    crud.patient.update(
                        db=db,
                        db_obj=patient_record,
                        obj_in={"associated_clinic_id": clinic_id_uuid},
                    )
                    logger.info(
                        f"Successfully updated patient {patient_record.id} associated_clinic_id to {clinic_id_uuid}"
                    )
                except ValueError:
                    logger.error(
                        f"Invalid UUID format for associated_clinic_id in token metadata: '{token_clinic_id_str}'"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to update patient {patient_record.id} associated_clinic_id: {e}",
                        exc_info=True,
                    )
                    # Decide if this failure should raise an error or just be logged

        # --- END FIX ---

        # Handle clinician association if needed (uses inviter_user_id from create_data)
        if patient_record and create_data.get(
            "invited_by_clinician_id"
        ):  # Use .get for safety
            clinician = crud.clinician.get_clinician_by_clerk_id(
                db, clerk_id=create_data["invited_by_clinician_id"]
            )
            if clinician:
                crud.clinician.assign_patient_to_clinician(
                    db=db, clinician_id=clinician.id, patient_id=patient_record.id
                )

        return current_user

    except Exception as e:
        logger.error(f"Failed to process patient data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process patient data",
        )


# --- get_current_clinician remains the same ---
# It depends on get_current_user which now handles the request passing implicitly
def get_current_clinician(
    current_user: TokenPayload = Depends(get_current_user),
    db: Session = Depends(get_db),  # Add db dependency
) -> models.Clinician:
    """
    Dependency to get the current clinician user, ensuring they have the 'clinician' role.
    Returns the Clinician model from the database.
    Raises 403 Forbidden if the role is incorrect.

    For Clerk-authenticated users, the role should be determined during token verification.
    """
    logger.debug("Entering get_current_clinician dependency...")

    if not current_user:
        logger.error("get_current_user returned None in get_current_clinician.")
        # This case should ideally be caught by verify_access_token raising credentials_exception
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials (user payload missing)",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.debug(
        f"Payload received in get_current_clinician: sub={current_user.sub}, role={current_user.role}, email={current_user.email}"
    )

    # Allow both 'clinician' and 'admin' roles (Handles string or list)
    allowed_roles = {"clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        # Check if any role in the list is allowed
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles

    if not has_role:
        logger.warning(
            f"User {current_user.sub} does not have required role(s) "
            f"(e.g., 'clinician' or 'admin') (roles: {current_user.role}). "
            f"Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user does not have the required 'clinician' or 'admin' role",
        )

    logger.debug(f"User {current_user.sub} verified as clinician.")

    # Attempt to fetch the clinician record from the database
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_user.sub
    )

    if not clinician_db:
        # Clinician record not found - potentially first login after invite
        logger.info(
            f"Clinician record not found for Clerk ID {current_user.sub}. Checking invitation metadata for automatic creation."
        )

        if not current_user.public_metadata:
            logger.warning(
                f"Clinician {current_user.sub} logged in but has no public_metadata for clinic association."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Clinician account requires setup or association with a clinic. Please contact an administrator.",
            )

        initial_clinic_id_str = current_user.public_metadata.get("initial_clinic_id")
        if not initial_clinic_id_str:
            logger.warning(
                f"Clinician {current_user.sub} public_metadata missing 'initial_clinic_id'. Metadata: {current_user.public_metadata}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Clinician invitation metadata is missing required clinic information. Please contact an administrator.",
            )

        # Add detailed logging before attempting conversion
        logger.debug(
            f"Attempting UUID conversion for initial_clinic_id: type={type(initial_clinic_id_str)}, value={initial_clinic_id_str}"
        )
        try:
            initial_clinic_id = uuid.UUID(initial_clinic_id_str)
            # Validate the clinic exists
            clinic = crud.clinic.get(db=db, id=initial_clinic_id)
            if not clinic:
                logger.error(
                    f"Invalid initial_clinic_id '{initial_clinic_id_str}' found in metadata "
                    f"for clinician {current_user.sub}. Clinic not found."
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,  # Bad request because metadata is invalid
                    detail="Invalid clinic specified in invitation metadata. Please contact an administrator.",
                )

            # Create the clinician record
            logger.debug(
                f"Using clinic_id={initial_clinic_id_str} for clinician {current_user.sub}"
            )
            clinician_in = ClinicianCreate(
                id=current_user.sub,  # Use Clerk ID as primary key
                clerk_id=current_user.sub,  # Explicitly provide Clerk ID using attribute access
                email=current_user.email,  # Use attribute access for email
                first_name=getattr(
                    current_user, "first_name", "Unknown"
                ),  # Use getattr for optional attribute first_name
                last_name=getattr(
                    current_user, "last_name", "Unknown"
                ),  # Use getattr for optional attribute last_name
                is_active=True,  # Assume active on creation
            )
            # Use a specific creation function or adapt existing one
            # Assuming create_with_clerk_id handles setting the ID correctly
            new_clinician = crud.clinician.create_clinician_from_clerk(
                db=db, obj_in=clinician_in
            )

            # Associate the new clinician with the initial clinic
            # Assuming a function exists to add to the association table
            crud.clinician.add_clinician_to_clinic(
                db=db, clinician_id=new_clinician.id, clinic_id=initial_clinic_id
            )

            db.commit()  # Commit both creation and association
            logger.info(
                f"Successfully created clinician record {new_clinician.id} and associated with clinic {initial_clinic_id}."
            )
            clinician_db = new_clinician  # Use the newly created record

        except ValueError:
            # Make the error log more specific to this conversion failure
            logger.error(
                f"Invalid UUID format during initial_clinic_id conversion: value={initial_clinic_id_str} for clinician {current_user.sub}.",
                exc_info=True,  # Include traceback for context
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid clinic ID format in invitation metadata: '{initial_clinic_id_str}'",
            )
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Database integrity error during clinician creation/association for {current_user.sub}: {e}",
                exc_info=True,
            )
            # Could be a race condition, try fetching again just in case
            clinician_db = crud.clinician.get_clinician_by_clerk_id(
                db, clerk_id=current_user.sub
            )
            if not clinician_db:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create clinician profile due to database conflict.",
                )
        except Exception as e:
            db.rollback()
            logger.error(
                f"Unexpected error during clinician creation/association for {current_user.sub}: {e}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=500, detail="Failed to setup clinician profile."
            )

    # If we reach here, either the clinician existed or was just created successfully
    logger.debug(f"Clinician {current_user.sub} DB record confirmed.")
    
    # Fetch the clinician record again if it was just created
    if not clinician_db:
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
    
    return clinician_db  # Return the Clinician model, not the TokenPayload


# --- get_current_active_user remains the same ---
# It depends on get_current_user which now handles the request passing implicitly
def get_current_active_user(
    current_user: TokenPayload = Depends(get_current_user),
) -> TokenPayload:
    """
    Dependency to get the current user, ensuring they have an active role
    (either 'patient', 'clinician', or 'admin').
    Raises 403 Forbidden if the role is not permitted.

    This dependency is useful for endpoints that can be accessed by patients, clinicians, and admins.
    """
    logger.debug(
        f"Payload received in get_current_active_user: sub={current_user.sub}, role={current_user.role}"
    )

    # Check if user has an active role (patient, clinician, or admin) (Handles string or list)
    allowed_roles = {"patient", "clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        # Check if any role in the list is allowed
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles

    if not has_role:
        logger.warning(
            f"User {current_user.sub} does not have an authorized active role "
            f"(e.g., 'patient', 'clinician', or 'admin') (roles: {current_user.role}). Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"User roles '{current_user.role}' are not authorized for this endpoint",
        )

    logger.debug(
        f"User {current_user.sub} verified with active role: {current_user.role}"
    )

    # Optional: Fetch full user object from DB here if needed based on role
    # if current_user.role == "patient":
    #     user = crud.patient.get(db, id=uuid.UUID(current_user.sub))
    # elif current_user.role == "clinician":
    #     user = crud.clinician.get_by_clerk_id(db, clerk_id=current_user.sub) # Example
    # if not user:
    #     raise HTTPException(status_code=404, detail=f"{current_user.role.capitalize()} not found")
    # return user

    return current_user


# --- RBAC / Multi-Tenancy Dependencies ---


def get_current_active_patient_in_clinic(
    request: Request,  # Changed parameter
    current_user: TokenPayload = Depends(
        get_current_patient
    ),  # Ensures user is a patient
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to ensure the current user is an active patient AND associated with the specified clinic
    (extracted from the request path).
    Raises 403 Forbidden if not associated.
    Raises 400 Bad Request if clinic_id is missing or invalid.
    """
    try:
        # Extract clinic_id from path parameters
        clinic_id_str = request.path_params.get("clinic_id")
        if not clinic_id_str:
            logger.error("RBAC Error: clinic_id missing from path parameters.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Clinic ID missing in request path.",
            )
        clinic_id = uuid.UUID(clinic_id_str)
    except KeyError:
        logger.error("RBAC Error: 'clinic_id' key not found in path parameters.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Clinic ID missing in request path.",
        )
    except ValueError:
        logger.error(f"RBAC Error: Invalid UUID format for clinic_id: {clinic_id_str}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid Clinic ID format."
        )

    logger.debug(
        f"RBAC Check: Verifying patient {current_user.sub} association with clinic {clinic_id}"
    )
    if not crud.patient.is_patient_associated_with_clinic(
        db=db, patient_id=current_user.sub, clinic_id=clinic_id
    ):
        logger.warning(
            f"RBAC Fail: Patient {current_user.sub} not associated with clinic {clinic_id}. Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Patient not authorized for this clinic",
        )
    logger.debug(
        f"RBAC Check: Patient {current_user.sub} association with clinic {clinic_id} VERIFIED."
    )
    return current_user


def get_current_active_clinician_in_clinic(
    clinic_id: uuid.UUID,
    current_user: TokenPayload = Depends(get_current_clinician),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to ensure the current user is an active clinician AND associated with the specified clinic.
    Raises 403 Forbidden if not associated.
    """
    logger.debug(
        f"RBAC Check: Verifying clinician {current_user.sub} association with clinic {clinic_id}"
    )
    # Fetch the clinician's internal DB ID using the Clerk ID from the token
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_user.sub
    )

    if not clinician_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    # Check if the clinician is associated with the specified clinic
    if not crud.clinician.is_clinician_associated_with_clinic(
        db, clinician_id=clinician_db.id, clinic_id=clinic_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Clinician not associated with specified clinic",
        )

    return current_user


def get_current_active_clinician_for_patient(
    request: Request,
    current_user: TokenPayload = Depends(get_current_clinician),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to ensure the current user is an active clinician AND associated with the specified patient
    (extracted from the request path).
    Raises 403 Forbidden if not associated.
    Raises 400 Bad Request if patientId is missing.
    """
    # Extract patientId from path parameters
    patient_id = request.path_params.get("patientId")
    if not patient_id:
        logger.error("RBAC Error: patientId missing from path parameters.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Patient ID missing in request path.",
        )

    # Get clinician's internal DB record
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_user.sub
    )

    if not clinician_db:
        logger.error(f"Clinician DB record not found for Clerk ID {current_user.sub}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    # Check if clinician is assigned to this patient
    if not crud.clinician.is_patient_assigned_to_clinician(
        db=db, clinician_id=clinician_db.id, patient_id=patient_id
    ):
        logger.warning(
            f"RBAC Fail: Clinician {current_user.sub} (DB ID: {clinician_db.id}) not associated with patient {patient_id}. Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Clinician not authorized for this patient",
        )
    logger.debug(
        f"RBAC Check: Clinician {current_user.sub} association with patient {patient_id} VERIFIED."
    )
    return current_user


def get_current_active_admin_in_clinic(
    clinic_id: uuid.UUID,
    current_user: TokenPayload = Depends(get_current_user),  # Get generic user first
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to ensure the current user has an 'admin' role AND is associated with the specified clinic.
    Raises 403 Forbidden if role is wrong or not associated.
    Note: Assumes 'admin' role exists and association is via Clinician model link.
    Adjust if admin role/association logic differs.
    """
    logger.debug(
        f"RBAC Check: Verifying admin {current_user.sub} association with clinic {clinic_id}"
    )
    # 1. Check Role
    # Adjust 'admin' role name if different in your system (e.g., 'clinic_admin')
    # Check Role (Handles string or list)
    required_role = "admin"
    has_role = False
    if isinstance(current_user.role, list):
        has_role = required_role in current_user.role
    elif isinstance(current_user.role, str):
        has_role = required_role == current_user.role

    if not has_role:
        logger.warning(
            f"User {current_user.sub} does not have required role '{required_role}' "
            f"(roles: {current_user.role}). Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have administrator privileges",
        )

    # 2. Check Clinic Association (Assuming Admins are also Clinicians linked to clinics)
    # If Admins are managed differently, this logic needs to change.
    # Fetch the admin's internal DB ID using the Clerk ID from the token
    admin_db = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=current_user.sub)
    if not admin_db:
        logger.error(
            f"RBAC Error: Admin/Clinician DB record not found for Clerk ID {current_user.sub}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Administrator record not found",
        )

    if not crud.clinician.is_clinician_associated_with_clinic(
        db=db, clinician_id=admin_db.id, clinic_id=clinic_id
    ):
        logger.warning(
            f"RBAC Fail: Admin {current_user.sub} (DB ID: {admin_db.id}) not associated with clinic {clinic_id}. Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrator not authorized for this clinic",
        )

    logger.debug(
        f"RBAC Check: Admin {current_user.sub} association with clinic {clinic_id} VERIFIED."
    )
    return current_user


def get_clinician_clinics(
    current_user: TokenPayload = Depends(get_current_clinician),
    db: Session = Depends(get_db),
) -> list[ClinicRead]:
    """
    Dependency to retrieve the list of clinics associated with the current clinician.
    Returns a list of ClinicRead schemas. Used by endpoints needing this list.
    """
    logger.debug(f"Retrieving clinics for clinician {current_user.sub}")

    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_user.sub
    )

    if not clinician_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    clinics = crud.clinician.get_clinics_for_clinician(db, clinician_id=clinician_db.id)

    return clinics


def get_current_admin_user(
    current_user: TokenPayload = Depends(
        get_current_user
    ),  # Depends on the base user verification
) -> TokenPayload:
    """
    Dependency to get the current user, ensuring they have the 'admin' role.
    Raises 403 Forbidden if the role is incorrect.
    """
    logger.debug(f"Checking admin role for user {current_user.sub}")
    required_role = "admin"
    has_role = False
    # Check Role (Handles string or list)
    if isinstance(current_user.role, list):
        has_role = required_role in current_user.role
    elif isinstance(current_user.role, str):
        has_role = required_role == current_user.role

    if not has_role:
        logger.warning(
            f"User {current_user.sub} does not have required role '{required_role}' "
            f"(roles: {current_user.role}). Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have administrator privileges",
        )
    logger.debug(f"User {current_user.sub} verified as admin.")
    return current_user


def get_current_admin(
    current_user: TokenPayload = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to get the current user, ensuring they have the 'admin' role.
    Raises 403 Forbidden if the role is incorrect.
    """
    logger.debug("Entering get_current_admin dependency...")

    if not current_user:
        logger.error("get_current_user returned None in get_current_admin")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials (user payload missing)",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user has admin role (either as string or in list)
    has_admin_role = False
    if isinstance(current_user.role, list):
        has_admin_role = "admin" in current_user.role
    else:
        has_admin_role = current_user.role == "admin"

    if not has_admin_role:
        logger.warning(
            f"User {current_user.sub} does not have 'admin' role (roles: {current_user.role}). Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user does not have the required 'admin' role",
        )

    # Verify admin exists in database - use clinician CRUD as admins are stored in the clinician table
    admin_db = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=current_user.sub)

    if not admin_db:
        logger.warning(f"Admin {current_user.sub} not found in database")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin record not found in database",
        )

    logger.debug(f"User {current_user.sub} verified as admin")
    return current_user


def get_current_admin_for_clinic(
    clinic_id: uuid.UUID,
    current_user: TokenPayload = Depends(get_current_admin),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to ensure the current user is an admin AND has access to the specified clinic.
    Super admins have access to all clinics.
    Raises 403 Forbidden if not authorized.
    """
    logger.debug(
        f"RBAC Check: Verifying admin {current_user.sub} access to clinic {clinic_id}"
    )

    admin_db = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=current_user.sub)

    if not admin_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin record not found",
        )

    # Super admins have access to all clinics
    if crud.clinician.is_super_admin(db, admin_id=admin_db.id):
        logger.debug(
            f"Super admin {current_user.sub} access VERIFIED for clinic {clinic_id}"
        )
        return current_user

    # Regular admins need explicit clinic association
    if not crud.clinician.is_clinician_associated_with_clinic(
        db, clinician_id=admin_db.id, clinic_id=clinic_id
    ):
        logger.warning(
            f"RBAC Fail: Admin {current_user.sub} not associated with clinic {clinic_id}. Raising 403."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin not authorized for this clinic",
        )

    logger.debug(
        f"RBAC Check: Admin {current_user.sub} association with clinic {clinic_id} VERIFIED"
    )
    return current_user


def get_current_admin_or_clinician(
    current_user: TokenPayload = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> TokenPayload:
    """
    Dependency to get the current user, ensuring they have either 'admin' or 'clinician' role.
    Raises 403 Forbidden if the role is neither admin nor clinician.

    This is used for endpoints that should be accessible by both admins and clinicians
    but not by patients or other roles.
    """
    logger.debug(f"Checking if user {current_user.sub} has admin or clinician role")

    if isinstance(current_user.role, list):
        roles = set(current_user.role)
        if not ("admin" in roles or "clinician" in roles):
            logger.warning(
                f"Access denied: User {current_user.sub} with roles {roles} attempted to access admin/clinician endpoint"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. User is neither an admin nor a clinician.",
            )
    else:
        if current_user.role != "admin" and current_user.role != "clinician":
            logger.warning(
                f"Access denied: User {current_user.sub} with role {current_user.role} attempted to access admin/clinician endpoint"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. User is neither an admin nor a clinician.",
            )

    # If the user is a clinician, ensure they have a record in the database
    if current_user.role == "clinician" or (
        isinstance(current_user.role, list) and "clinician" in current_user.role
    ):
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if not clinician_db:
            # Create clinician record if it doesn't exist (similar to get_current_clinician)
            try:
                # Get Clerk user data
                clerk_user = clerk.users.get(user_id=current_user.sub)

                # Extract names from Clerk user data
                first_name = clerk_user.first_name if clerk_user else None
                last_name = clerk_user.last_name if clerk_user else None

                # Create clinician record
                clinician_create = ClinicianCreate(
                    id=current_user.sub,
                    email=current_user.email,
                    first_name=first_name,
                    last_name=last_name,
                )

                clinician_db = crud.clinician.create_clinician_from_clerk(
                    db=db, obj_in=clinician_create
                )

                logger.info(f"Created clinician record for user {current_user.sub}")
            except Exception as e:
                logger.error(f"Error creating clinician record: {str(e)}")
                # Continue without creating the record, as this is not a critical error
                # The user is still authenticated via Clerk

    # If the user is an admin, ensure they have a record in the database
    if current_user.role == "admin" or (
        isinstance(current_user.role, list) and "admin" in current_user.role
    ):
        admin_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )  # Use clinician CRUD for admins
        if not admin_db:
            logger.warning(f"Admin user {current_user.sub} has no database record")
            # For admins, we don't auto-create records as they should be explicitly created
            # This is different from clinicians who can be auto-created

    logger.debug(f"User {current_user.sub} verified as admin or clinician")
    return current_user


def verify_clinic_exists(
    clinic_id: uuid.UUID,
    db: Session = Depends(get_db),
) -> None:
    """
    Dependency to verify a clinic exists.
    Raises 404 Not Found if clinic doesn't exist.
    """
    clinic = crud.clinic.get(db, id=clinic_id)
    if not clinic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinic not found",
        )


def get_clinic_by_domain(
    request: Request,
    db: Session = Depends(get_db),
) -> ClinicRead:
    """
    Dependency to get clinic information based on the request domain.
    Used for patient-facing endpoints where the clinic is determined by the subdomain.
    Raises 404 Not Found if no clinic matches the domain.
    """
    domain = request.headers.get("host", "").split(":")[0]  # Remove port if present

    clinic = crud.clinic.get_by_domain(db, domain=domain)
    if not clinic:
        logger.error(f"No clinic found for domain: {domain}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No clinic found for this domain",
        )

    return ClinicRead.from_orm(clinic)


def get_current_user_clinics(
    current_user: TokenPayload = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> list[ClinicRead]:
    """
    Dependency to get all clinics associated with the current user based on their role.
    Returns different results for patients, clinicians, and admins.
    """
    if isinstance(current_user.role, list):
        roles = set(current_user.role)
    else:
        roles = {current_user.role}

    clinics = []

    if "admin" in roles:
        admin_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if admin_db:
            if crud.clinician.is_super_admin(db, admin_id=admin_db.id):
                # Super admins see all clinics
                clinics = crud.clinic.get_all(db)
            else:
                # Regular admins see their associated clinics
                clinics = crud.clinician.get_clinics_for_clinician(
                    db, clinician_id=admin_db.id
                )

    elif "clinician" in roles:
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if clinician_db:
            clinics = crud.clinician.get_clinics_for_clinician(
                db, clinician_id=clinician_db.id
            )

    elif "patient" in roles:
        clinics = crud.patient.get_patient_clinics(db, patient_id=current_user.sub)

    return [ClinicRead.from_orm(clinic) for clinic in clinics]


def validate_sort_params(
    sort_by: str,
    allowed_fields: set[str],
    default_field: str,
    default_order: str = "desc",
) -> tuple[str, str]:
    """
    Validates sort parameters and returns field and order.

    Args:
        sort_by: The sort parameter string (e.g., "reported_at:desc")
        allowed_fields: Set of allowed field names
        default_field: Default field to sort by if invalid
        default_order: Default sort order if invalid

    Returns:
        Tuple of (field, order)
    """
    try:
        if not sort_by or ":" not in sort_by:
            return default_field, default_order

        field, order = sort_by.split(":")
        if field not in allowed_fields or order not in ("asc", "desc"):
            return default_field, default_order

        return field, order
    except Exception:
        return default_field, default_order
