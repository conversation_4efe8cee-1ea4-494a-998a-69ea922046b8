from fastapi import APIRouter

# Import endpoint modules
from app.api.v1.endpoints import (
    admin,
    analytics,
    appointments,
    audit,
    auth,
    clinical_notes,
    clinicians,
    clinics,
    content,
    dashboard,
    demo,
    education_materials,
    event_logs,
    llm_actions,
    medication_requests,
    medications,
    notes,
    notifications,
    patient_alerts,
    patient_education,
    patients,
    search,
    side_effects,
    stats,
    templates,
)
from app.api.v1.endpoints.chat import router as chat_router

api_router = APIRouter()

# Include the authentication routes
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])

# Include the patient routes
api_router.include_router(patients.router, prefix="/patients", tags=["Patients"])
api_router.include_router(patients.router, prefix="", tags=["Patients"])

# Include the clinician routes
api_router.include_router(clinicians.router, prefix="/clinicians", tags=["Clinicians"])

# Include the content routes
api_router.include_router(content.router, prefix="/content", tags=["Content"])

# Include the dashboard routes
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])

# Include the audit log routes
api_router.include_router(audit.router, prefix="/audit-log", tags=["Audit Log"])

# Include the event log routes
api_router.include_router(event_logs.router, prefix="/event-logs", tags=["Event Logs"])

# Include the template routes
api_router.include_router(templates.router, prefix="/templates", tags=["Templates"])

# Include the LLM actions routes
api_router.include_router(
    llm_actions.router, prefix="/llm-actions", tags=["LLM Actions"]
)

# Include the notification routes
api_router.include_router(
    notifications.router, prefix="/notifications", tags=["Notifications"]
)

# Include the search routes
api_router.include_router(search.router, prefix="/search", tags=["Search"])

# Include the notes routes
api_router.include_router(notes.router, prefix="/notes", tags=["Notes"])

# Include the clinical notes routes
api_router.include_router(
    clinical_notes.router, prefix="/clinical-notes", tags=["Clinical Notes"]
)

# Include the clinics ingestion routes
api_router.include_router(clinics.router, prefix="/admin/clinics", tags=["Clinics"])

# Include the admin routes
api_router.include_router(admin.router, prefix="/admin", tags=["Admin"])

# Include the stats routes
api_router.include_router(stats.router, prefix="/stats", tags=["Stats"])

# Include the appointment routes
api_router.include_router(
    appointments.router, prefix="/appointments", tags=["Appointments"]
)

# Include the medications routes
api_router.include_router(
    medications.router, prefix="/medications", tags=["medications"]
)

# Include the side effects routes
api_router.include_router(
    side_effects.router, prefix="/side-effects", tags=["Side Effects"]
)

# Include the medication request routes
api_router.include_router(
    medication_requests.router,
    prefix="/medication-requests",
    tags=["Medication Requests"],
)

# Register the chat API router
api_router.include_router(chat_router, prefix="/chat", tags=["chat"])

# Include the education materials routes
api_router.include_router(
    education_materials.router,
    prefix="/education-materials",
    tags=["Education Materials"],
)

# Include the patient education routes
api_router.include_router(
    patient_education.router,
    prefix="/patient-education",
    tags=["Patient Education"],
)

# Include the patient alerts routes
api_router.include_router(
    patient_alerts.router,
    prefix="/patient-alerts",
    tags=["Patient Alerts"],
)

# Include the analytics routes
api_router.include_router(
    analytics.router,
    prefix="/analytics",
    tags=["Analytics"],
)

# Include the demo routes (admin only)
api_router.include_router(
    demo.router,
    prefix="/demo",
    tags=["Demo Management"],
)

# Add other routers here as they are created
