import logging
from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.models.appointment import Appointment
from app.schemas.auth import TokenPayload

logger = logging.getLogger(__name__)


def get_user_role(current_user: TokenPayload = Depends(deps.get_current_user)) -> str:
    """
    Determine the user's primary role from the TokenPayload.
    If multiple roles are present, prioritize admin > clinician > patient.

    Args:
        current_user: The current user's token payload

    Returns:
        The user's primary role as a string
    """
    if isinstance(current_user.role, list):
        # If user has multiple roles, prioritize admin > clinician > patient
        if "admin" in current_user.role:
            return "admin"
        elif "clinician" in current_user.role:
            return "clinician"
        elif "patient" in current_user.role:
            return "patient"
        else:
            # If no recognized role is found, raise an exception
            logger.error(
                f"User {current_user.sub} has no valid role in {current_user.role}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="User has no valid role"
            )
    else:
        # If role is a string, return it directly
        return current_user.role


def verify_appointment_access(
    *,
    db: Session,
    appointment_id: UUID,
    current_user: TokenPayload,
    allow_roles: Optional[list[str]] = None,
) -> Appointment:
    """
    Verify that the current user has access to the specified appointment.

    Args:
        db: Database session
        appointment_id: ID of the appointment to check
        current_user: The current user's token payload
        allow_roles: List of roles allowed to access this appointment regardless of ownership
                    (e.g., ["admin"] to allow admins to access any appointment)

    Returns:
        The appointment if access is allowed

    Raises:
        HTTPException: If the appointment doesn't exist or the user doesn't have access
    """
    # Get the appointment
    appointment = crud.appointment.get(db, id=appointment_id)
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Appointment {appointment_id} not found",
        )

    # Get the user's role
    user_role = get_user_role(current_user)
    user_id = current_user.sub

    # If user has a role that allows access to all appointments, grant access
    if allow_roles and user_role in allow_roles:
        return appointment

    # Check if user is the patient or clinician for this appointment
    if user_role == "patient" and appointment.patient_id == user_id:
        return appointment
    elif user_role == "clinician" and appointment.clinician_id == user_id:
        return appointment

    # If we get here, the user doesn't have access
    logger.warning(
        f"User {user_id} with role {user_role} attempted to access appointment {appointment_id} "
        f"belonging to patient {appointment.patient_id} and clinician {appointment.clinician_id}"
    )
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="You don't have permission to access this appointment",
    )
