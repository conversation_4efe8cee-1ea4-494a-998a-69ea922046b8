import datetime
import logging
import uuid
from typing import Optional

# Clerk SDK and models
from clerk_backend_api import models as clerk_models
from fastapi import APIRouter, Depends, HTTPException, Path, Query, Response, status
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session

from app.api import deps
from app.core.auth_utils import clerk
from app.core.config import settings

# App imports
from app.crud import crud_clinician
from app.schemas.auth import TokenPayload
from app.schemas.clinician import (
    Clinician as ClinicianSchema,
)
from app.schemas.clinician import (
    ClinicianListResponse,
    ClinicianUpdate,
)
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)

router = APIRouter()


class ClinicianInvitationRequest(BaseModel):
    email: EmailStr
    clinic_id: uuid.UUID


# Pydantic models for Clerk Invitation responses
class ClerkInvitation(BaseModel):
    id: str
    email_address: EmailStr
    status: str
    public_metadata: Optional[dict] = None  # Added to include metadata
    created_at: datetime.datetime  # Assuming Clerk SDK returns datetime objects

    class Config:
        from_attributes = True  # Updated from orm_mode for Pydantic v2


class ClerkInvitationListResponse(BaseModel):
    invitations: list[ClerkInvitation]
    total_count: Optional[int] = None  # Clerk might provide total count


@router.post(
    "/invitations",
    status_code=status.HTTP_201_CREATED,
    summary="Invite a new clinician",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def invite_clinician(
    *,
    db: Session = Depends(deps.get_db),
    invitation_in: ClinicianInvitationRequest,
    current_user: TokenPayload = Depends(
        deps.get_current_admin_user
    ),  # Get admin user (TokenPayload model) for audit log
):
    """
    Sends a Clerk invitation to a new clinician, assigning them a role
    and associating them with an initial clinic via public metadata.
    Requires admin privileges.
    """
    logger.info(
        f"API: Admin {current_user.sub} attempting to invite clinician {invitation_in.email} to clinic {invitation_in.clinic_id}"
    )

    if not settings.FRONTEND_URL_CLINICIAN:
        logger.error(
            "FRONTEND_URL_CLINICIAN is not set in settings. Cannot send invitation."
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Server configuration error: Clinician frontend URL is missing.",
        )

    public_metadata = {
        "role": ["clinician"],  # Corrected: Role should be a list
        "initial_clinic_id": str(invitation_in.clinic_id),  # Ensure UUID is stringified
    }

    try:
        # Corrected method call based on SDK example and parameter structure
        invitation = clerk.invitations.create(
            request={
                "email_address": invitation_in.email,
                "public_metadata": public_metadata,
                "redirect_url": f"{settings.FRONTEND_URL_CLINICIAN}/accept-invitation",  # Append specific path
                # "ignore_existing": True # Optional: Set to True to avoid errors if an invite already exists for this email
            }
        )
        logger.info(
            f"Successfully created Clerk invitation for {invitation_in.email} with ID {invitation.id}"
        )

        # Audit Log
        # Extract role - handle list or string
        admin_role = (
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        )

        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,  # Corrected param name
            actor_role=admin_role,  # Added missing param
            action="create_clinician_invitation",
            outcome="SUCCESS",  # Added missing param
            target_resource_type="clinician_invitation",  # Corrected param name
            target_resource_id=invitation.id,  # Corrected param name
            details={
                "invited_email": invitation_in.email,
                "assigned_clinic_id": str(invitation_in.clinic_id),
                "clerk_invitation_status": invitation.status,
            },
            # source_ip could be added here if extracted from request headers
        )

        # Return the created invitation details (optional, adjust as needed)
        return {
            "message": "Invitation sent successfully.",
            "invitation_id": invitation.id,
            "status": invitation.status,
        }

    except clerk_models.SDKError as e:  # Corrected exception type
        logger.error(
            f"Clerk API error while creating invitation for {invitation_in.email}: {e}",
            exc_info=True,
        )
        # You might want to map specific Clerk errors to different HTTP statuses
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create invitation via Clerk: {e}",
        )
    except Exception as e:
        logger.error(
            f"Unexpected error while creating invitation for {invitation_in.email}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while sending the invitation.",
        )


@router.get(
    "/invitations",
    response_model=ClerkInvitationListResponse,
    summary="List clinician invitations, optionally filtered by status",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def list_invitations(
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of invitations to return"
    ),
    offset: int = Query(0, ge=0, description="Number of invitations to skip"),
    status: Optional[str] = Query(
        None,
        description="Filter invitations by status (e.g., 'pending', 'accepted', 'revoked')",
    ),
):
    """
    Retrieves a list of clinician invitations from Clerk, optionally filtered by status.
    Requires admin privileges.
    """
    logger.info(
        f"API: Admin attempting to list Clerk invitations (limit={limit}, offset={offset}, status={status})"
    )
    try:
        response = clerk.invitations.list(limit=limit, offset=offset, status=status)

        # Filter invitations to only include those with clinician role
        filtered_invitations = []
        for inv in response:
            metadata = getattr(inv, "public_metadata", {})
            roles = metadata.get("role", []) if isinstance(metadata, dict) else []

            # Check if 'clinician' is in the roles list
            if isinstance(roles, list) and "clinician" in roles:
                created_dt = (
                    datetime.datetime.fromtimestamp(
                        inv.created_at / 1000, tz=datetime.timezone.utc
                    )
                    if inv.created_at
                    else None
                )
                filtered_invitations.append(
                    ClerkInvitation(
                        id=inv.id,
                        email_address=inv.email_address,
                        status=inv.status,
                        public_metadata=inv.public_metadata,
                        created_at=created_dt,
                    )
                )

        logger.info(
            f"Successfully retrieved {len(filtered_invitations)} clinician invitations."
        )
        return ClerkInvitationListResponse(
            invitations=filtered_invitations, total_count=len(filtered_invitations)
        )

    except clerk_models.SDKError as e:
        logger.error(f"Clerk API error while listing invitations: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list invitations via Clerk: {e}",
        )
    except Exception as e:
        logger.error(f"Unexpected error while listing invitations: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while listing invitations.",
        )


@router.delete(
    "/invitations/{invitation_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Revoke a clinician invitation",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def revoke_invitation(
    invitation_id: str,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    Revokes an existing clinician invitation using its Clerk ID.
    Requires admin privileges.
    """
    logger.info(
        f"API: Admin {current_user.sub} attempting to revoke invitation {invitation_id}"
    )
    admin_role = (
        current_user.role[0]
        if isinstance(current_user.role, list)
        else current_user.role
    )
    try:
        revoked_invitation = clerk.invitations.revoke(invitation_id=invitation_id)
        logger.info(f"Successfully revoked Clerk invitation {invitation_id}")

        # Audit Log on Success
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="revoke_clinician_invitation",
            outcome="SUCCESS",  # Use string literal
            target_resource_type="clinician_invitation",
            target_resource_id=invitation_id,
            details={
                "revoked_email": revoked_invitation.email_address,
                "previous_status": "pending",  # Assuming it was pending before revoke
                "new_status": revoked_invitation.status,  # Should be 'revoked'
            },
        )
        # Return No Content on success
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except clerk_models.SDKError as e:
        logger.warning(  # Log as warning, as it might be expected behavior
            f"Clerk SDK error while revoking invitation {invitation_id}: {e}",
            exc_info=False,  # Don't need full traceback for expected errors
        )

        # Default error details
        error_detail = f"Failed to revoke invitation via Clerk: {e}"
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        audit_outcome = "FAILURE"

        # Check for specific Clerk error codes within the SDKError
        if e.errors and isinstance(e.errors, list):
            first_error = e.errors[0]
            if isinstance(first_error, dict):
                error_code = first_error.get("code")
                error_message = first_error.get("message")

                if error_code == "invitation_already_accepted":
                    status_code = status.HTTP_400_BAD_REQUEST
                    error_detail = (
                        error_message
                        or "Invitation has already been accepted and cannot be revoked."
                    )
                    audit_outcome = (
                        "FAILURE_ACCEPTED"  # More specific outcome for audit
                    )
                elif (
                    error_code == "resource_not_found"
                ):  # Check for Clerk's specific not found code
                    status_code = status.HTTP_404_NOT_FOUND
                    error_detail = (
                        error_message
                        or f"Invitation with ID '{invitation_id}' not found."
                    )
                    audit_outcome = "FAILURE_NOT_FOUND"

        # Audit Log on Failure (using determined outcome)
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="revoke_clinician_invitation",
            outcome=audit_outcome,
            target_resource_type="clinician_invitation",
            target_resource_id=invitation_id,
            details={"error": str(e)},  # Log the raw error from Clerk
        )

        raise HTTPException(status_code=status_code, detail=error_detail)
    except Exception as e:
        logger.error(
            f"Unexpected error while revoking invitation {invitation_id}: {e}",
            exc_info=True,
        )
        # Audit Log on Unexpected Failure
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="revoke_clinician_invitation",
            outcome="FAILURE",  # Use string literal
            target_resource_type="clinician_invitation",
            target_resource_id=invitation_id,
            details={"error": f"Unexpected error: {str(e)}"},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while revoking the invitation.",
        )


@router.get(
    "/clinicians",
    response_model=ClinicianListResponse,
    summary="List active clinicians with their assigned clinic",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def list_clinicians(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
):
    """List all active clinicians with their clinic assignments."""
    try:
        clinicians_data = crud_clinician.clinician.get_with_clinic(
            db, skip=skip, limit=limit
        )
        logger.info(
            f"Successfully retrieved {len(clinicians_data.get('items', []))} active clinicians."
        )
        return clinicians_data

    except Exception as e:
        logger.error(
            f"Unexpected error while listing active clinicians: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while listing clinicians.",
        )


@router.put(
    "/clinicians/{clinician_id}",
    response_model=ClinicianSchema,
    summary="Update a clinician by Clerk ID",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def update_clinician(
    *,
    db: Session = Depends(deps.get_db),
    clinician_id: str,  # Clerk ID, not UUID
    clinician_in: ClinicianUpdate,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """Update a clinician by Clerk ID."""
    try:
        existing_clinician = crud_clinician.clinician.get_by_clerk_id(
            db, clerk_id=clinician_id
        )
        if not existing_clinician:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Clinician with Clerk ID {clinician_id} not found",
            )
        updated_clinician = crud_clinician.clinician.update(
            db, db_obj=existing_clinician, obj_in=clinician_in
        )
        # If new clinic assignment requested, update association
        if clinician_in.assigned_clinic_id:
            association_updated = crud_clinician.clinician.update_clinic_association(
                db=db,
                clinician_id=clinician_id,
                new_clinic_id=clinician_in.assigned_clinic_id,
            )

            if not association_updated:
                db.rollback()
                admin_role = (
                    current_user.role[0]
                    if isinstance(current_user.role, list)
                    else current_user.role
                )
                log_audit_event(
                    db=db,
                    actor_user_id=current_user.sub,
                    actor_role=admin_role,
                    action="update_clinician_association",
                    outcome="FAILURE",
                    target_resource_type="clinician",
                    target_resource_id=clinician_id,
                    details={
                        "error": f"Failed to associate with clinic {clinician_in.assigned_clinic_id}"
                    },
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to associate clinician with clinic ID {clinician_in.assigned_clinic_id}. Clinic might not exist.",
                )
            else:
                db.commit()
                db.refresh(updated_clinician)

        return updated_clinician

    except Exception as e:
        db.rollback()
        logger.error(f"Error updating clinician {clinician_id}: {e}", exc_info=True)
        admin_role = (
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        )
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="update_clinician",
            outcome="FAILURE",
            target_resource_type="clinician",
            target_resource_id=clinician_id,
            details={"error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while updating the clinician.",
        )


@router.delete(
    "/clinicians/{clinician_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a clinician",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def delete_clinician_endpoint(
    *,
    db: Session = Depends(deps.get_db),
    clinician_id: str = Path(..., description="The ID of the clinician to delete"),
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    Deletes a clinician record from the database. Requires admin privileges.
    Note: This is a hard delete. It does not delete the user from Clerk.
    """
    logger.info(
        f"API: Admin {current_user.sub} attempting to delete clinician {clinician_id}"
    )
    admin_role = (
        current_user.role[0]
        if isinstance(current_user.role, list)
        else current_user.role
    )

    db_clinician = crud_clinician.clinician.get_by_clerk_id(
        db=db, clerk_id=clinician_id
    )
    if not db_clinician:
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="delete_clinician",
            outcome="FAILURE_NOT_FOUND",
            target_resource_type="clinician",
            target_resource_id=clinician_id,
            details={"error": "Clinician not found"},
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Clinician not found"
        )

    try:
        # TODO: Consider implications - unassign patients? Delete Clerk user?
        # For now, just deleting the DB record.
        deleted_clinician = crud_clinician.clinician.delete_clinician(
            db=db, clinician_id=clinician_id
        )
        if deleted_clinician:
            log_audit_event(
                db=db,
                actor_user_id=current_user.sub,
                actor_role=admin_role,
                action="delete_clinician",
                outcome="SUCCESS",
                target_resource_type="clinician",
                target_resource_id=clinician_id,
                details={"deleted_email": deleted_clinician.email},
            )
        else:  # Should not happen if get_by_clerk_id succeeded, but handle defensively
            log_audit_event(
                db=db,
                actor_user_id=current_user.sub,
                actor_role=admin_role,
                action="delete_clinician",
                outcome="FAILURE",
                target_resource_type="clinician",
                target_resource_id=clinician_id,
                details={"error": "Clinician found initially but failed to delete"},
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete clinician after finding it.",
            )

        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logger.error(f"Error deleting clinician {clinician_id}: {e}", exc_info=True)
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=admin_role,
            action="delete_clinician",
            outcome="FAILURE",
            target_resource_type="clinician",
            target_resource_id=clinician_id,
            details={"error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while deleting the clinician.",
        )
