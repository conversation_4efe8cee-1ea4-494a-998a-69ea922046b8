from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.api.deps import get_current_clinician
from app.models.clinician import Clinician
from app.services.population_analytics_service import PopulationAnalyticsService
from app.schemas.analytics import (
    PopulationHealthDashboard,
    PopulationOverview,
    RiskStratification,
    TreatmentAnalytics,
    PredictiveAnalytics,
    OptimizationInsights
)

router = APIRouter()


@router.get("/population-dashboard", response_model=PopulationHealthDashboard)
def get_population_dashboard(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> PopulationHealthDashboard:
    """
    Get complete population health analytics dashboard
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    return analytics_service.get_dashboard_data(clinic_id)


@router.get("/population-overview", response_model=PopulationOverview)
def get_population_overview(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> PopulationOverview:
    """
    Get population overview metrics
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    dashboard = analytics_service.get_dashboard_data(clinic_id)
    return dashboard.overview


@router.get("/risk-stratification", response_model=RiskStratification)
def get_risk_stratification(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> RiskStratification:
    """
    Get patient risk stratification analysis
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    dashboard = analytics_service.get_dashboard_data(clinic_id)
    return dashboard.risk_stratification


@router.get("/treatment-analytics", response_model=TreatmentAnalytics)
def get_treatment_analytics(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> TreatmentAnalytics:
    """
    Get treatment and medication analytics
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    dashboard = analytics_service.get_dashboard_data(clinic_id)
    return dashboard.treatment_analytics


@router.get("/predictive-analytics", response_model=PredictiveAnalytics)
def get_predictive_analytics(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> PredictiveAnalytics:
    """
    Get predictive analytics and patient outcome predictions
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    dashboard = analytics_service.get_dashboard_data(clinic_id)
    return dashboard.predictive_analytics


@router.get("/optimization-insights", response_model=OptimizationInsights)
def get_optimization_insights(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician)
) -> OptimizationInsights:
    """
    Get workflow optimization insights and recommendations
    """
    if not current_clinician.clinics:
        raise HTTPException(
            status_code=400,
            detail="Clinician not associated with a clinic"
        )
    
    # Use the first clinic for now (in the future, could allow selection)
    clinic_id = str(current_clinician.clinics[0].id)
    
    analytics_service = PopulationAnalyticsService(db)
    dashboard = analytics_service.get_dashboard_data(clinic_id)
    return dashboard.optimization_insights