import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.api.v1.dependencies.appointment_deps import (
    get_user_role,
    verify_appointment_access,
)
from app.crud.exceptions import AppointmentNotFoundError, AppointmentStatusError
from app.schemas.appointment import (
    AppointmentCreate,
    AppointmentResponse,
    AppointmentUpdate,
)
from app.schemas.appointment_request import (
    AppointmentRequestCreate,
    AppointmentRequestResponse,
    AppointmentRequestStatusUpdate,
    AppointmentRequestUpdate,
)
from app.schemas.auth import TokenPayload
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=list[AppointmentResponse])
def list_appointments(
    *,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> list[AppointmentResponse]:
    """
    List appointments based on user role:
    - Patients see their own appointments
    - Clinicians see their scheduled appointments
    - Admins see all appointments
    """
    # Determine user role using our dependency
    role = get_user_role(current_user)
    clerk_user_id = current_user.sub  # This is the Clerk user ID (string)

    if role == "admin":
        return crud.appointment.get_multi(db)
    elif role == "clinician":
        # First get the clinician record using the Clerk ID
        clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=clerk_user_id)
        if not clinician:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )
        # Then get appointments using the clinician's ID
        return crud.appointment.get_by_clinician_id(
            db=db,
            clinician_id=clinician.id,  # Use the clinician's ID from the database
        )
    else:  # patient
        # For patients, we can use the Clerk ID directly since it's their primary key
        return crud.appointment.get_by_patient_id(db=db, patient_id=clerk_user_id)


@router.get("/upcoming", response_model=list[AppointmentResponse])
def list_upcoming_appointments(
    *,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> list[AppointmentResponse]:
    """Get upcoming appointments for the current user"""
    # Determine user role using our dependency
    role = get_user_role(current_user)
    user_id = current_user.sub

    if role == "admin":
        # For admin, we could either show all upcoming appointments or none
        # Let's show none by default and let them use filters if needed
        return []
    elif role == "clinician":
        return crud.appointment.get_upcoming_by_clinician_id(db, clinician_id=user_id)
    else:  # patient
        return crud.appointment.get_upcoming_by_patient_id(db, patient_id=user_id)


@router.post(
    "/", response_model=AppointmentResponse, status_code=status.HTTP_201_CREATED
)
def create_appointment(
    *,
    db: Session = Depends(deps.get_db),
    appointment_in: AppointmentCreate,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentResponse:
    """Create new appointment"""
    # Determine user role
    role = get_user_role(current_user)
    user_id = current_user.sub

    # Authorization checks
    if role == "patient":
        # Patients can only create appointments for themselves
        if appointment_in.patient_id != user_id:
            log_audit_event(
                db=db,
                action="CREATE_APPOINTMENT",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role=role,
                details={
                    "reason": "Unauthorized - patient tried to create appointment for another patient",
                    "requested_patient_id": appointment_in.patient_id,
                },
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Patients can only create appointments for themselves",
            )
    elif role == "clinician":
        # Clinicians can create appointments for any patient assigned to them
        # Check if the patient is assigned to this clinician
        if not crud.clinician.is_patient_assigned_to_clinician(
            db, clinician_id=user_id, patient_id=appointment_in.patient_id
        ):
            log_audit_event(
                db=db,
                action="CREATE_APPOINTMENT",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role=role,
                details={
                    "reason": "Unauthorized - clinician tried to create appointment for unassigned patient",
                    "requested_patient_id": appointment_in.patient_id,
                },
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Clinicians can only create appointments for assigned patients",
            )

    try:
        appointment = crud.appointment.create_with_ids(db=db, obj_in=appointment_in)

        log_audit_event(
            db=db,
            action="CREATE_APPOINTMENT",
            outcome="SUCCESS",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment.id),
            details={
                "patient_id": appointment.patient_id,
                "clinician_id": appointment.clinician_id,
                "appointment_type": appointment.appointment_type,
                "appointment_datetime": str(appointment.appointment_datetime),
                "duration_minutes": appointment.duration_minutes,
            },
        )

        return appointment
    except Exception as e:
        logger.error(f"Error creating appointment: {e}", exc_info=True)
        log_audit_event(
            db=db,
            action="CREATE_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            details={
                "reason": f"Database error: {str(e)}",
                "patient_id": appointment_in.patient_id,
                "clinician_id": appointment_in.clinician_id,
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the appointment",
        )


# ===== APPOINTMENT REQUEST ENDPOINTS =====
# NOTE: These must come BEFORE /{appointment_id} to avoid path conflicts

@router.get("/requests", response_model=list[AppointmentRequestResponse])
def list_appointment_requests(
    *,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> list[AppointmentRequestResponse]:
    """
    List appointment requests based on user role:
    - Patients see their own appointment requests
    - Clinicians see requests for their patients
    - Admins see all appointment requests
    """
    try:
        role = get_user_role(current_user)
        clerk_user_id = current_user.sub

        if role == "admin":
            return crud.appointment_request.get_multi(db)
        elif role == "clinician":
            # Get appointment requests for the clinician's patients
            clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=clerk_user_id)
            if not clinician:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Clinician profile not found"
                )
            return crud.appointment_request.get_by_clinician(db, clinician_id=clinician.id)
        elif role == "patient":
            # Get the patient's appointment requests
            patient = crud.patient.get_patient_by_clerk_id(db, clerk_id=clerk_user_id)
            if not patient:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Patient profile not found"
                )
            return crud.appointment_request.get_by_patient(db, patient_id=patient.id)
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid user role"
            )
    except Exception as e:
        logger.error(f"❌ Error in list_appointment_requests: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/requests", response_model=AppointmentRequestResponse)
def create_appointment_request(
    *,
    db: Session = Depends(deps.get_db),
    appointment_request_in: AppointmentRequestCreate,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentRequestResponse:
    """
    Create a new appointment request.
    Typically used by patients to request appointments.
    """
    role = get_user_role(current_user)
    clerk_user_id = current_user.sub

    # Verify the user can create appointment requests
    if role == "patient":
        patient = crud.patient.get_patient_by_clerk_id(db, clerk_id=clerk_user_id)
        if not patient:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient profile not found"
            )
        # Ensure the request is for the authenticated patient
        if appointment_request_in.patient_id != patient.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create appointment request for another patient"
            )
    elif role in ["clinician", "admin"]:
        # Clinicians and admins can create requests for any patient
        pass
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid user role"
        )

    try:
        appointment_request = crud.appointment_request.create(db, obj_in=appointment_request_in)
        
        log_audit_event(
            db=db,
            action="CREATE_APPOINTMENT_REQUEST",
            outcome="SUCCESS",
            actor_user_id=clerk_user_id,
            actor_role=role,
            target_resource_type="AppointmentRequest",
            target_resource_id=str(appointment_request.id),
            details={
                "patient_id": str(appointment_request.patient_id),
                "preferred_datetime": str(appointment_request.preferred_datetime),
                "reason": appointment_request.reason,
            },
        )
        
        return appointment_request
    except Exception as e:
        logger.error(f"Error creating appointment request: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create appointment request"
        )


@router.get("/requests/{request_id}", response_model=AppointmentRequestResponse)
def get_appointment_request(
    *,
    db: Session = Depends(deps.get_db),
    request_id: UUID,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentRequestResponse:
    """
    Get a specific appointment request by ID.
    """
    appointment_request = crud.appointment_request.get(db, id=request_id)
    if not appointment_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment request not found"
        )
    
    # Verify access permissions
    role = get_user_role(current_user)
    clerk_user_id = current_user.sub
    
    if role == "admin":
        pass  # Admins can access any request
    elif role == "clinician":
        clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=clerk_user_id)
        if not clinician:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Clinician profile not found"
            )
        # Check if this request is for one of the clinician's patients
        # This would require a more complex query - for now, allow access
        pass
    elif role == "patient":
        patient = crud.patient.get_patient_by_clerk_id(db, clerk_id=clerk_user_id)
        if not patient or appointment_request.patient_id != patient.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    return appointment_request


@router.patch("/requests/{request_id}/status", response_model=AppointmentRequestResponse)
def update_appointment_request_status(
    *,
    db: Session = Depends(deps.get_db),
    request_id: UUID,
    status_update: AppointmentRequestStatusUpdate,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentRequestResponse:
    """
    Update the status of an appointment request.
    Typically used by clinicians to approve/reject requests.
    """
    appointment_request = crud.appointment_request.get(db, id=request_id)
    if not appointment_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment request not found"
        )
    
    role = get_user_role(current_user)
    clerk_user_id = current_user.sub
    
    # Only clinicians and admins can update request status
    if role not in ["clinician", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only clinicians and admins can update appointment request status"
        )
    
    try:
        updated_request = crud.appointment_request.update_status_by_id(
            db, request_id=request_id, status_update=status_update
        )
        
        log_audit_event(
            db=db,
            action="UPDATE_APPOINTMENT_REQUEST_STATUS",
            outcome="SUCCESS",
            actor_user_id=clerk_user_id,
            actor_role=role,
            target_resource_type="AppointmentRequest",
            target_resource_id=str(request_id),
            details={
                "new_status": status_update.status,
                "patient_id": str(appointment_request.patient_id),
            },
        )
        
        return updated_request
    except Exception as e:
        logger.error(f"Error updating appointment request status: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update appointment request status"
        )


# ===== APPOINTMENT ENDPOINTS =====

@router.get("/{appointment_id}", response_model=AppointmentResponse)
def get_appointment(
    *,
    db: Session = Depends(deps.get_db),
    appointment_id: UUID,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentResponse:
    """Get appointment by ID"""
    try:
        # Use our verify_appointment_access dependency to check authorization
        # Allow admins to access any appointment
        appointment = verify_appointment_access(
            db=db,
            appointment_id=appointment_id,
            current_user=current_user,
            allow_roles=["admin"],
        )
        return appointment
    except HTTPException:
        # Re-raise HTTP exceptions from verify_appointment_access
        raise
    except AppointmentNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        # Log the error internally
        logger.error(
            f"Unexpected error fetching appointment {appointment_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred.",
        )


@router.post("/{appointment_id}/cancel", response_model=AppointmentResponse)
def cancel_appointment(
    *,
    db: Session = Depends(deps.get_db),
    appointment_id: UUID,
    current_user: TokenPayload = Depends(deps.get_current_user),
    reason: Optional[str] = None,
) -> AppointmentResponse:
    """Cancel an appointment"""
    try:
        # Use our verify_appointment_access dependency to check authorization
        # Allow admins to access any appointment
        appointment = verify_appointment_access(
            db=db,
            appointment_id=appointment_id,
            current_user=current_user,
            allow_roles=["admin"],
        )

        # If we get here, the user is authorized to access this appointment
        user_id = current_user.sub
        role = get_user_role(current_user)

        try:
            cancelled_appointment = crud.appointment.cancel_appointment(
                db,
                appointment_id=appointment_id,
                cancelled_by_id=user_id,
                reason=reason,
            )

            log_audit_event(
                db=db,
                action="CANCEL_APPOINTMENT",
                outcome="SUCCESS",
                actor_user_id=str(user_id),
                actor_role=role,
                target_resource_type="Appointment",
                target_resource_id=str(appointment_id),
                details={
                    "cancelled_by_id": user_id,
                    "cancellation_reason": reason,
                    "patient_id": cancelled_appointment.patient_id,
                    "clinician_id": cancelled_appointment.clinician_id,
                    "original_datetime": str(
                        cancelled_appointment.appointment_datetime
                    ),
                },
            )

            return cancelled_appointment

        except AppointmentStatusError as e:
            log_audit_event(
                db=db,
                action="CANCEL_APPOINTMENT",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role=role,
                target_resource_type="Appointment",
                target_resource_id=str(appointment_id),
                details={"reason": str(e), "current_status": appointment.status},
            )
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    except HTTPException as http_ex:
        # Re-raise HTTP exceptions from verify_appointment_access
        user_id = current_user.sub
        role = get_user_role(current_user)

        log_audit_event(
            db=db,
            action="CANCEL_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": http_ex.detail, "status_code": http_ex.status_code},
        )
        raise
    except AppointmentNotFoundError as e:
        user_id = current_user.sub
        role = get_user_role(current_user)

        log_audit_event(
            db=db,
            action="CANCEL_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": str(e)},
        )
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        # Log the error internally
        user_id = current_user.sub
        role = get_user_role(current_user)

        logger.error(
            f"Unexpected error cancelling appointment {appointment_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="CANCEL_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": f"Unexpected error: {str(e)}"},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred.",
        )


@router.put("/{appointment_id}", response_model=AppointmentResponse)
def update_appointment(
    *,
    db: Session = Depends(deps.get_db),
    appointment_id: UUID,
    appointment_in: AppointmentUpdate,
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> AppointmentResponse:
    """Update an appointment"""
    try:
        # Use our verify_appointment_access dependency to check authorization
        # Allow admins to access any appointment
        appointment = verify_appointment_access(
            db=db,
            appointment_id=appointment_id,
            current_user=current_user,
            allow_roles=["admin"],
        )

        # Additional authorization checks for specific fields
        role = get_user_role(current_user)
        user_id = current_user.sub

        try:
            if role == "patient":
                # Patients can only update certain fields (e.g., patient_notes)
                # Create a filtered update object with only allowed fields
                filtered_update = AppointmentUpdate(
                    patient_notes=appointment_in.patient_notes
                )
                updated_appointment = crud.appointment.update(
                    db, db_obj=appointment, obj_in=filtered_update
                )

                log_audit_event(
                    db=db,
                    action="UPDATE_APPOINTMENT",
                    outcome="SUCCESS",
                    actor_user_id=str(user_id),
                    actor_role=role,
                    target_resource_type="Appointment",
                    target_resource_id=str(appointment_id),
                    details={
                        "fields_updated": ["patient_notes"],
                        "patient_id": updated_appointment.patient_id,
                    },
                )

                return updated_appointment
            else:
                # Clinicians and admins can update all fields
                updated_appointment = crud.appointment.update(
                    db, db_obj=appointment, obj_in=appointment_in
                )

                # Determine which fields were updated
                updated_fields = []
                for field, value in appointment_in.dict(exclude_unset=True).items():
                    updated_fields.append(field)

                log_audit_event(
                    db=db,
                    action="UPDATE_APPOINTMENT",
                    outcome="SUCCESS",
                    actor_user_id=str(user_id),
                    actor_role=role,
                    target_resource_type="Appointment",
                    target_resource_id=str(appointment_id),
                    details={
                        "fields_updated": updated_fields,
                        "patient_id": updated_appointment.patient_id,
                        "clinician_id": updated_appointment.clinician_id,
                    },
                )

                return updated_appointment

        except Exception as e:
            logger.error(f"Error updating appointment: {e}", exc_info=True)
            log_audit_event(
                db=db,
                action="UPDATE_APPOINTMENT",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role=role,
                target_resource_type="Appointment",
                target_resource_id=str(appointment_id),
                details={"reason": f"Database error: {str(e)}"},
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while updating the appointment",
            )

    except HTTPException as http_ex:
        # Re-raise HTTP exceptions from verify_appointment_access
        user_id = current_user.sub
        role = get_user_role(current_user)

        log_audit_event(
            db=db,
            action="UPDATE_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": http_ex.detail, "status_code": http_ex.status_code},
        )
        raise
    except AppointmentNotFoundError as e:
        user_id = current_user.sub
        role = get_user_role(current_user)

        log_audit_event(
            db=db,
            action="UPDATE_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": str(e)},
        )
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        # Log the error internally
        user_id = current_user.sub
        role = get_user_role(current_user)

        logger.error(
            f"Unexpected error updating appointment {appointment_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_APPOINTMENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=role,
            target_resource_type="Appointment",
            target_resource_id=str(appointment_id),
            details={"reason": f"Unexpected error: {str(e)}"},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred.",
        )


# DELETE endpoint is not implemented as cancellation is the preferred way to handle unwanted appointments
