import logging
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api import deps
from app.utils.audit import log_audit_event

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get(
    "/",
    response_model=list[schemas.audit_log.AuditLog],
    summary="Get Audit Log Entries",
    description="Retrieves a paginated list of audit log entries. Access level determines visible entries.",
)
async def get_audit_logs(
    patient_id: Optional[str] = Query(None, description="Filter by patient ID"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        20, ge=1, le=100, description="Maximum number of records to return"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> list[schemas.audit_log.AuditLog]:
    """
    Retrieve audit logs based on user role and permissions:
    - Admins: All logs for their clinic(s)
    - Clinicians: Logs related to their patients
    - Patients: Their own logs only
    """
    try:
        # Determine access scope based on role
        if isinstance(current_user.role, list):
            roles = set(current_user.role)
        else:
            roles = {current_user.role}

        # Build base query filters
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if event_type:
            filters["event_type"] = event_type

        # Apply role-based filters
        if "admin" in roles:
            # Admins see logs for their clinics
            admin_db = crud.admin.get_admin_by_clerk_id(db, clerk_id=current_user.sub)
            if not admin_db:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Admin record not found",
                )

            if crud.admin.is_super_admin(db, admin_id=admin_db.id):
                # Super admins see all logs
                pass
            else:
                # Regular admins see logs for their clinics
                clinic_ids = [
                    c.id for c in crud.admin.get_admin_clinics(db, admin_id=admin_db.id)
                ]
                filters["clinic_ids"] = clinic_ids

        elif "clinician" in roles:
            # Clinicians see logs related to their patients
            clinician_db = crud.clinician.get_clinician_by_clerk_id(
                db, clerk_id=current_user.sub
            )
            if not clinician_db:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Clinician record not found",
                )

            if patient_id:
                # Verify patient belongs to clinician
                if not crud.clinician.is_patient_assigned(
                    db, clinician_id=clinician_db.id, patient_id=patient_id
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied to patient's audit logs",
                    )
            else:
                # Get all patients assigned to clinician
                patient_ids = crud.clinician.get_assigned_patient_ids(
                    db, clinician_id=clinician_db.id
                )
                filters["patient_ids"] = patient_ids

        elif "patient" in roles:
            # Patients only see their own logs
            filters["patient_id"] = current_user.sub

            # If patient_id was specified, ensure it matches current user
            if patient_id and patient_id != current_user.sub:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Can only access own audit logs",
                )

        # Get paginated audit logs with filters
        audit_logs = crud.audit_log.get_multi(db, skip=skip, limit=limit, **filters)

        # Log the audit retrieval
        log_audit_event(
            db=db,
            event_type="audit_logs_accessed",
            user_id=current_user.sub,
            user_role=current_user.role,
            details={"filters": filters, "pagination": {"skip": skip, "limit": limit}},
        )

        return audit_logs

    except Exception as e:
        logger.error(f"Error retrieving audit logs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving audit logs",
        )


@router.get(
    "/summary",
    response_model=schemas.audit_log.AuditLogSummary,
    summary="Get Audit Log Summary",
    description="Retrieves a summary of audit log activity (counts by type, recent trends).",
)
async def get_audit_summary(
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_admin),
    db: Session = Depends(deps.get_db),
) -> schemas.audit_log.AuditLogSummary:
    """
    Get summary statistics of audit logs (admin only).
    Includes counts by event type, user role, and time-based trends.
    """
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date

        # Get summary statistics
        summary = crud.audit_log.get_summary(db, **filters)

        # Log the summary access
        log_audit_event(
            db=db,
            event_type="audit_summary_accessed",
            user_id=current_user.sub,
            user_role=current_user.role,
            details={"filters": filters},
        )

        return summary

    except Exception as e:
        logger.error(f"Error retrieving audit summary: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving audit summary",
        )
