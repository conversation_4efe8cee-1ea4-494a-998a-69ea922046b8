# backend/app/api/v1/endpoints/auth.py
import logging
from datetime import datetime, timezone

from fastapi import (
    API<PERSON><PERSON>er,
    BackgroundTasks,
    Body,
    Depends,
    HTTPException,
    Request,
    Security,
    status,
)
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

# Unified CRUD import
from app import crud

# Dependencies
from app.api import deps

# Core functionality imports
from app.core.auth_utils import create_access_token, verify_access_token
from app.core.config import settings
from app.core.limiter import limiter

# Access code exceptions
from app.crud.crud_access_code import (
    AccessCodeExpiredError,
    AccessCodeNotFoundError,
    AccessCodeUsedError,
)

# Schema imports
from app.schemas.auth import MagicCodeRequest, TokenResponse
from app.schemas.clinician import ClinicianCreate
from app.schemas.events import PatientRegisteredEvent
from app.schemas.patient import PatientCreate

# Utility imports
from app.utils.audit import log_audit_event
from app.utils.event_bus import publish_event

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize security scheme
token_auth_scheme = HTTPBearer()

# Initialize router
router = APIRouter()  # Removed tag from router

# --- Removed Placeholder Database Simulation ---

# --- API Endpoints ---


@router.post(
    "/validate-code",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Validate Magic Access Code",
    description="Validates a patient's magic access code and returns a JWT session token upon success.",
    tags=["Authentication"],  # Added tag directly to endpoint
)
@limiter.limit("10/minute")  # Apply rate limit decorator here
async def validate_magic_code(
    request: Request,  # Add request parameter for slowapi
    *,  # Ensures following parameters are keyword-only
    # request: Request, # Removed as limiter is handled by Depends
    db: Session = Depends(deps.get_db),  # Added DB dependency
    background_tasks: BackgroundTasks,  # Kept order for now
    request_body: MagicCodeRequest = Body(...),
    # Removed limiter from Depends
):
    """
    Validates the provided magic code using the database.

    - Checks if the code exists, is active (not used), and not expired.
    - Does NOT mark the code as used (consumption happens later).
    - Generates and returns a JWT access token containing the associated patient_id.
    """
    plaintext_code = request_body.code

    # Find the code in the database using updated verification method
    # This now verifies the plaintext against all active hashed codes
    db_code = crud.access_code.verify_and_get_access_code(
        db=db, plaintext_code=plaintext_code
    )

    validation_error_detail = "Invalid, expired, or already used magic code."
    validation_error_status = status.HTTP_401_UNAUTHORIZED

    if not db_code:
        logger.warning("Magic code not found or invalid")
        log_audit_event(
            db=db,
            action="VALIDATE_MAGIC_CODE",
            outcome="FAILURE",
            actor_user_id=None,
            actor_role=None,
            details={"reason": "Code not found"},
        )
        raise HTTPException(
            status_code=validation_error_status,
            detail=validation_error_detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Ensure patient_id is available for logging even if other checks fail
    patient_id_for_log = str(db_code.patient_id) if db_code.patient_id else None

    if db_code.is_used:
        logger.warning("Magic code already used")
        log_audit_event(
            db=db,
            action="VALIDATE_MAGIC_CODE",
            outcome="FAILURE",
            actor_user_id=patient_id_for_log,
            actor_role="patient",
            details={"reason": "Code already used"},
        )
        raise HTTPException(
            status_code=validation_error_status,
            detail=validation_error_detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Ensure expires_at is timezone-aware for comparison
    expires_at_aware = db_code.expires_at
    if expires_at_aware.tzinfo is None:
        # Assuming UTC if naive, based on crud_access_code logic
        expires_at_aware = expires_at_aware.replace(tzinfo=timezone.utc)

    now_aware = datetime.now(timezone.utc)

    if expires_at_aware < now_aware:
        logger.warning("Magic code expired")
        log_audit_event(
            db=db,
            action="VALIDATE_MAGIC_CODE",
            outcome="FAILURE",
            actor_user_id=patient_id_for_log,
            actor_role="patient",
            details={"reason": "Code expired", "expiry": str(db_code.expires_at)},
        )
        raise HTTPException(
            status_code=validation_error_status,
            detail=validation_error_detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

    # --- Find or Create Patient ---
    patient_email = db_code.email
    patient = crud.patient.get_by_email(db=db, email=patient_email)
    if not patient:
        logger.info(
            f"No existing patient found for email {patient_email}. Creating new patient."
        )
        # Create a basic patient record
        # Consider adding placeholder names or prompting for them later
        patient_in = PatientCreate(
            email=patient_email,
            first_name="New",
            last_name="Patient",
            # Add other required fields with defaults if necessary
        )
        try:
            patient = crud.patient.create(db=db, obj_in=patient_in)
            logger.info(
                f"Created new patient with ID: {patient.id} for email {patient_email}"
            )
        except Exception as e:
            logger.error(
                f"Failed to create patient for email {patient_email}: {e}",
                exc_info=True,
            )
            log_audit_event(
                db=db,
                action="VALIDATE_MAGIC_CODE",
                outcome="FAILURE",
                actor_user_id=None,
                actor_role=None,
                details={"reason": "Patient creation failed", "email": patient_email},
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create patient profile during onboarding.",
            )
    else:
        logger.info(
            f"Found existing patient with ID: {patient.id} for email {patient_email}"
        )

    patient_id = patient.id  # Use the found or created patient's ID

    # --- Consume Code & Create Association ---
    try:
        # consume_access_code function has been updated to handle hashed verification
        consumed_code = crud.access_code.consume_access_code(
            db=db, code_value=plaintext_code, patient_id=patient_id
        )
        logger.info(f"Successfully consumed access code for patient {patient_id}")

        # Create patient-clinician association
        if consumed_code.clinician_id:
            clinician_id = consumed_code.clinician_id
            logger.info(
                f"Attempting to associate patient {patient_id} with clinician {clinician_id}"
            )
            assigned = crud.clinician.assign_patient_to_clinician(
                db=db, clinician_id=clinician_id, patient_id=patient_id
            )
            if assigned:
                logger.info(
                    f"Successfully associated patient {patient_id} with clinician {clinician_id}"
                )
            else:
                # Log failure but don't necessarily block login
                logger.error(
                    f"Failed to associate clinician {clinician_id} with patient {patient_id} "
                    f"(clinician/patient not found or already associated?)"
                )
                # Consider adding specific audit log for association failure
        else:
            logger.warning(
                "Consumed access code has no associated clinician_id. Cannot create association."
            )

    except (AccessCodeNotFoundError, AccessCodeExpiredError, AccessCodeUsedError) as e:
        # These should have been caught earlier, but handle defensively
        logger.error(f"Error consuming code after initial validation passed: {e}")
        log_audit_event(
            db=db,
            action="VALIDATE_MAGIC_CODE",
            outcome="FAILURE",
            actor_user_id=str(patient_id),
            actor_role="patient",
            details={"reason": f"Code consumption failed: {e}"},
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,  # Or 500 if internal state issue
            detail="Failed to process access code.",
        )
    except Exception as e:
        logger.error(
            f"Unexpected error consuming code or creating association: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="VALIDATE_MAGIC_CODE",
            outcome="FAILURE",
            actor_user_id=str(patient_id),
            actor_role="patient",
            details={
                "reason": f"Internal error during code consumption/association: {e}"
            },
        )
        # Don't necessarily block login if association failed, but log it.
        # If consumption failed critically, an error might be appropriate.
        # For now, proceed to token generation but log the error.

    # --- Generate Token & Publish Event ---
    access_token = create_access_token(
        subject=str(patient_id), role="patient"
    )  # Explicitly set role

    # Publish PatientRegisteredEvent
    # Assuming validation implies registration/first login for MVP
    event = PatientRegisteredEvent(user_id=patient_id)
    publish_event(event=event, background_tasks=background_tasks)

    logger.info(f"Magic code validated successfully for patient ID: {patient_id}")
    log_audit_event(
        db=db,
        action="VALIDATE_MAGIC_CODE",
        outcome="SUCCESS",
        actor_user_id=patient_id,
        actor_role="patient",
        target_resource_id=str(db_code.id),
        details={},
    )
    return TokenResponse(accessToken=access_token, tokenType="bearer")


@router.post(
    "/clinician-login",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Clinician Login via Clerk Token",
    description="Authenticates a clinician using a Clerk session token provided in the Authorization header.",
    tags=["Authentication"],
)
@limiter.limit("10/minute")  # Apply rate limit decorator here
async def clinician_login(
    request: Request,  # Add request parameter for slowapi
    *,  # Ensures following parameters are keyword-only
    db: Session = Depends(deps.get_db),  # Added DB dependency (for future use)
    token: HTTPAuthorizationCredentials = Security(token_auth_scheme),
):
    """
    Handles clinician login by verifying a Clerk session token.

    1.  Extracts the Bearer token from the Authorization header.
    2.  Verifies the token using the Clerk SDK.
    3.  (Placeholder) Checks if the clinician's email domain is allowed via ALLOWED_CLINICIAN_DOMAINS env var.
    4.  Generates a JWT session token upon successful verification.
    5.  (Future) Will use `db` session to fetch/create internal Clinician record.
    """
    clerk_token = token.credentials
    if not clerk_token:
        logger.warning("Clinician login attempt failed: Bearer token missing.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Bearer token missing or invalid.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create credentials exception to be used by verify_access_token
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Use the verify_access_token function from security.py
        # This will attempt internal JWT verification first, then Clerk verification
        token_data = await verify_access_token(
            token=clerk_token,
            request=request,
            credentials_exception=credentials_exception,
        )

        # Extract Clerk user ID and email from token data
        clerk_user_id = token_data.sub
        email = token_data.email if hasattr(token_data, "email") else None

        # Domain verification (if configured)
        if email and settings.ALLOWED_CLINICIAN_DOMAINS:
            # Split the comma-separated string into a list of domains and strip whitespace
            allowed_domains_list = [
                domain.strip().lower()
                for domain in settings.ALLOWED_CLINICIAN_DOMAINS.split(",")
            ]
            logger.info(f"Verifying against allowed domains: {allowed_domains_list}")

            email_domain = email.split("@")[-1].lower()
            if email_domain not in allowed_domains_list:
                logger.warning(
                    f"Unauthorized domain attempt: {email} (Domain: {email_domain})"
                )
                log_audit_event(
                    db=db,
                    action="CLINICIAN_LOGIN",
                    outcome="FAILURE",
                    actor_user_id=clerk_user_id,
                    actor_role="clinician",
                    details={"reason": "Unauthorized email domain", "email": email},
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Email domain '{email_domain}' is not authorized for clinician access.",
                )
            logger.info(f"Clinician email domain '{email_domain}' verified.")
        elif settings.ALLOWED_CLINICIAN_DOMAINS:
            logger.warning(
                f"Email not available for domain verification for user {clerk_user_id}"
            )
        else:
            logger.warning(
                "ALLOWED_CLINICIAN_DOMAINS not set, skipping domain verification."
            )

        # Try to fetch clinician by clerk_id
        clinician = crud.clinician.get_clinician_by_clerk_id(
            db=db, clerk_id=clerk_user_id
        )

        # If not found, create the clinician
        if not clinician:
            # Get user details from Clerk token data
            first_name = (
                token_data.first_name if hasattr(token_data, "first_name") else None
            )
            last_name = (
                token_data.last_name if hasattr(token_data, "last_name") else None
            )

            clinician_in = ClinicianCreate(
                email=email,  # Use the email from the token
                clerk_id=clerk_user_id,
                first_name=first_name,  # Use the values from Clerk
                last_name=last_name,  # Use the values from Clerk
            )
            clinician = crud.clinician.create_clinician(db=db, obj_in=clinician_in)
            logger.info(
                f"Created new clinician record for Clerk user ID: {clerk_user_id}"
            )

        # Generate internal JWT session token after Clerk verification
        # This allows us to use a consistent authentication mechanism across the API
        additional_claims = {
            "email": email,
            "clinician_id": str(
                clinician.id
            ),  # Include internal clinician ID in claims
        }
        access_token = create_access_token(
            subject=str(clinician.id),  # Use internal clinician ID as subject
            role="clinician",
            additional_claims=additional_claims,
        )

        logger.info(f"Clinician login successful for Clerk user ID: {clerk_user_id}")
        log_audit_event(
            db=db,
            action="CLINICIAN_LOGIN",
            outcome="SUCCESS",
            actor_user_id=str(clinician.id),
            actor_role="clinician",
        )
        return TokenResponse(accessToken=access_token, tokenType="bearer")

    except HTTPException as http_exc:
        # Re-raise specific HTTPExceptions raised within the try block
        # (e.g., from domain check or missing payload)
        # Audit logging should have happened before these were raised.
        raise http_exc
    except Exception as e:
        # Catch-all for *other* unexpected errors
        logger.exception(f"An unexpected error occurred during clinician login: {e}")
        log_audit_event(
            db=db,
            action="CLINICIAN_LOGIN",
            outcome="FAILURE",
            actor_user_id=None,
            actor_role="clinician",
            details={"reason": f"Unexpected internal error: {e}"},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error occurred during authentication.",
        )
