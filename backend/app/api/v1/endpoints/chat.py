import logging
import uuid
from typing import Any, Optional, Union  # Added Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from starlette.concurrency import (
    run_in_threadpool,
)  # Added for async calls to sync CRUD

from app import crud, schemas  # Added schemas import
from app.api.deps import get_current_active_user, get_db
from app.models.chat_message import MessageSenderType
from app.models.clinician import Clinician
from app.models.patient import Patient
from app.schemas.chat import (
    AvailableStructuredSchemas,
    ChatConfigResponse,
    ChatConfigUpdate,
    ChatHistoryItem,
    ChatHistoryResponse,
    ChatMessageCreateInternal,
    ChatMessageRequest,
    ChatMessageResponse,
    ClinicianConversationItem,
    ClinicianConversationsResponse,
)
from app.services.chat_agent import AgentProcessingError, process_chat_message
from app.services.llm_config_service import LLMConfigService
from app.utils import get_utc_now

# Set up logger
logger = logging.getLogger(__name__)

router = APIRouter()

# --- Chat Message Endpoints ---


@router.post("/messages", response_model=ChatMessageResponse)
async def send_chat_message(
    message_request: ChatMessageRequest,
    background_tasks: BackgroundTasks,
    current_user: Union[Patient, Clinician] = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """
    Send a chat message and get an AI response.

    Handles messages from both patients and clinicians,
    routing to the appropriate specialized module.

    - For patients: Provides general support and guidance
    - For clinicians: Provides guideline-based information

    Can optionally return structured data when requested.

    Message routing options:
    - AI (default): Message is processed by AI only
    - Patient: Message is routed to the patient (for clinician messages)
    """
    try:
        # Create context dict to pass message_route if provided
        context = {}
        # Extract context data from message_request.context if available
        if hasattr(message_request, "context") and message_request.context:
            context.update(message_request.context)
            logger.info(f"Context data from request: {context}")

        # Set message_route - check both top-level and context
        message_route_str = None
        if message_request.message_route:
            # Convert message_route to lowercase string
            if hasattr(message_request.message_route, "value"):
                message_route_str = message_request.message_route.value.lower()
            else:
                message_route_str = str(message_request.message_route).lower()
            logger.debug(f"Using explicit message route from request: {message_route_str}")
        elif context.get("message_route"):
            # Check for message_route in context (from frontend)
            message_route_str = str(context["message_route"]).lower()
            logger.debug(f"Using message route from context: {message_route_str}")
        else:
            # Auto-set message route based on user type
            user_is_patient = (
                hasattr(current_user, "role") and current_user.role == "patient"
            ) or (
                hasattr(message_request, "context") 
                and message_request.context 
                and message_request.context.get("userRole") == "patient"
            )
            
            if user_is_patient:
                message_route_str = "clinician"  # Patient messages go to clinician
                logger.debug(f"Auto-setting message route to 'clinician' for patient user")
            else:
                message_route_str = "ai"  # Default for clinicians without explicit routing
                logger.debug(f"Auto-setting message route to 'ai' for non-patient user")
        
        context["message_route"] = message_route_str

        # Log full request data for debugging
        logger.debug(f"Chat message request: {message_request.model_dump_json()}")
        logger.debug(f"Initial context: {context}")

        # For clinicians, store their message with routing information
        user_is_clinician = (
            hasattr(current_user, "role") and current_user.role == "clinician"
        )

        # If the message is from a clinician, store the message first
        if user_is_clinician:
            # Determine the associated patient_id (might be in different fields in the context)
            # CRITICAL FIX: Properly resolve Clerk-id → internal UUID for consistent storage/retrieval
            patient_id = None
            patient_internal_uuid = None

            # First try to get patient_id from context
            patient_id_from_context = None
            if context.get("patient_id"):
                patient_id_from_context = context["patient_id"]
                logger.info(f"Found patient_id in context: {patient_id_from_context}")
            elif context.get("currentPatientId"):
                patient_id_from_context = context["currentPatientId"]
                logger.info(
                    f"Found currentPatientId in context: {patient_id_from_context}"
                )

            # Now try to resolve to internal UUID if we have an ID
            if patient_id_from_context:
                # First try direct lookup assuming it's already an internal UUID
                try:
                    patient_obj = crud.patient.get(db, id=patient_id_from_context)
                    if patient_obj:
                        patient_internal_uuid = str(patient_obj.id)
                        logger.info(
                            f"Confirmed {patient_id_from_context} is an internal UUID: {patient_internal_uuid}"
                        )
                except Exception as e:
                    logger.debug(
                        f"ID {patient_id_from_context} not found as internal UUID: {str(e)}"
                    )

                # If not found as internal UUID, try as Clerk ID
                if not patient_internal_uuid:
                    try:
                        patient_obj = crud.patient.get_by_clerk_id(
                            db, clerk_id=patient_id_from_context
                        )
                        if patient_obj:
                            patient_internal_uuid = str(patient_obj.id)
                            logger.info(
                                f"Resolved Clerk ID {patient_id_from_context} to internal UUID {patient_internal_uuid}"
                            )
                        else:
                            logger.warning(
                                f"Could not find patient with Clerk ID: {patient_id_from_context}"
                            )
                    except Exception as e:
                        logger.error(
                            f"Error resolving Clerk ID to internal UUID: {str(e)}",
                            exc_info=True,
                        )

            # Use resolved internal UUID if available, or fall back to original ID
            if patient_internal_uuid:
                patient_id = patient_internal_uuid
                logger.info(f"Using resolved internal UUID: {patient_id}")
            elif patient_id_from_context:
                patient_id = patient_id_from_context
                logger.warning(f"Using unresolved ID as fallback: {patient_id}")

            # Log patient ID retrieval for debugging
            logger.debug(f"Final patient_id for message creation: {patient_id}")
            logger.debug(f"Available context keys: {list(context.keys())}")

            # Only create the message if we have a patient_id
            if patient_id:

                # Create the clinician message using the CRUD module
                # Ensure patient_id is a string for consistent handling
                try:
                    saved_message = crud.chat_message.create(
                        db=db,
                        obj_in=ChatMessageCreateInternal(
                            patient_id=str(patient_id),
                            sender_type="clinician",  # Use literal string value
                            message_content=message_request.message,
                            message_route=(
                                message_route_str
                                if message_request.message_route
                                else None
                            ),  # Already lowercase
                        ),
                    )
                    logger.debug(
                        f"Successfully created clinician message ID {saved_message.id} for patient {patient_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to create clinician message for patient {patient_id}: {str(e)}",
                        exc_info=True,
                    )
                    raise

        # Process message through chat agent
        try:
            # Log context before processing
            logger.error(f"DEBUG - Context before processing: {context}")
            logger.error(f"DEBUG - Current user type: {type(current_user)}")

            # Get the user_id from either TokenPayload or User object
            if hasattr(current_user, "id"):
                user_id = current_user.id
            elif hasattr(current_user, "sub"):
                # For TokenPayload objects, use the 'sub' field which is the Clerk ID
                user_id = current_user.sub
            else:
                logger.error(f"DEBUG - Cannot determine user ID: {current_user}")
                raise ValueError("Unable to determine user ID")

            logger.error(f"DEBUG - Using user ID: {user_id}")

            # If this is a clinician, ensure we're using the right user role in context
            if user_is_clinician:
                # CRITICAL FIX: Force userRole to clinician to prevent misidentification
                context["userRole"] = "clinician"
                logger.error("DEBUG - Setting user role to clinician")

                # Get the patient ID and resolve it to internal UUID
                patient_id_for_context = None

                # First check if we have the right keys in context
                if context.get("patient_id") or context.get("currentPatientId"):
                    patient_id_for_context = context.get("patient_id") or context.get(
                        "currentPatientId"
                    )
                    logger.error(
                        f"DEBUG - Found patient ID in context: {patient_id_for_context}"
                    )

                # Try to resolve to internal UUID if we have a patient ID
                if patient_id_for_context:
                    # Check if patient_id is a direct internal ID
                    try:
                        target_patient = await run_in_threadpool(
                            crud.patient.get, db, id=patient_id_for_context
                        )
                        if target_patient:
                            patient_internal_uuid = str(target_patient.id)
                            logger.error(
                                f"DEBUG - Resolved patient_id directly to: {patient_internal_uuid}"
                            )
                        else:
                            # Try to look up by Clerk ID
                            target_patient = await run_in_threadpool(
                                crud.patient.get_by_clerk_id,
                                db,
                                clerk_id=patient_id_for_context,
                            )
                            if target_patient:
                                patient_internal_uuid = str(target_patient.id)
                                logger.error(
                                    f"DEBUG - Resolved patient Clerk ID to internal UUID: {patient_internal_uuid}"
                                )
                            else:
                                logger.error(
                                    f"DEBUG - Could not resolve patient ID: {patient_id_for_context}"
                                )
                    except Exception as e:
                        logger.error(f"DEBUG - Error resolving patient ID: {str(e)}")

                # Set the patient ID in context
                if patient_internal_uuid:
                    context["patient_id"] = patient_internal_uuid
                    context["currentPatientId"] = patient_internal_uuid
                    logger.error(
                        f"DEBUG - Setting patient_id and currentPatientId to resolved internal UUID: {patient_internal_uuid}"
                    )
                elif patient_id_for_context:
                    context["patient_id"] = patient_id_for_context
                    context["currentPatientId"] = patient_id_for_context
                    logger.error(
                        f"DEBUG - Setting patient_id and currentPatientId to consistent value: {patient_id_for_context}"
                    )

            response = await process_chat_message(
                user_id=user_id,
                user_message=message_request.message,
                db=db,
                structured_output=message_request.structured_output,
                output_schema=message_request.output_schema,
                context=context,
            )
        except Exception as agent_error:
            # Detailed error logging
            logger.error(
                f"DEBUG - Agent processing error: {str(agent_error)}", exc_info=True
            )
            raise

        # For structured output, parse the appropriate schema
        structured_data = None
        if message_request.structured_output and message_request.output_schema:
            try:
                if message_request.output_schema == "medication_reminder":
                    structured_data = response.get("structured_data")
                elif message_request.output_schema == "side_effect_report":
                    structured_data = response.get("structured_data")
            except Exception:
                # Log error but still return unstructured response
                structured_data = None

        # Use real message ID from response if available, otherwise generate one
        message_id = str(uuid.uuid4())
        sender_type = "agent"  # Default to agent
        
        if isinstance(response, dict) and response.get("message_id"):
            message_id = response["message_id"]
            
            # Get the sender_type from the saved message
            try:
                saved_message = await run_in_threadpool(
                    crud.chat_message.get, db, id=message_id
                )
                if saved_message and saved_message.sender_type:
                    # Convert enum to string value
                    sender_type = saved_message.sender_type.value if hasattr(saved_message.sender_type, 'value') else str(saved_message.sender_type)
                    logger.debug(f"Retrieved sender_type '{sender_type}' for message ID {message_id}")
            except Exception as e:
                logger.warning(f"Could not retrieve sender_type for message {message_id}: {str(e)}")

        # Check if this is a patient-routed message (for frontend display purposes)
        is_patient_routed = False
        if isinstance(response, dict) and response.get("metadata", {}).get(
            "patient_routed"
        ):
            is_patient_routed = True

        result = {
            "message": (
                response if isinstance(response, str) else response.get("response", "")
            ),
            "message_id": message_id,
            "sender_type": sender_type,  # Include sender_type in response
            "timestamp": get_utc_now().isoformat(),
            "structured_data": structured_data,
            "metadata": {
                **(response.get("metadata", {}) if isinstance(response, dict) else {}),
                "patient_routed": is_patient_routed,
            },
        }
        
        # Log if we have pending_compound_action in metadata
        if result.get("metadata", {}).get("pending_compound_action"):
            logger.error("CHAT ENDPOINT: Response contains pending_compound_action metadata")
            logger.error(f"CHAT ENDPOINT: Full response being sent to frontend: {result}")
        
        return result

    except AgentProcessingError as e:
        logger.error(f"DEBUG - Agent processing error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error processing chat message: {str(e)}"
        )
    except Exception as e:
        logger.error(f"DEBUG - Unexpected chat error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


# --- Chat History Endpoints ---


@router.get(
    "/history",
    response_model=Union[ChatHistoryResponse, ClinicianConversationsResponse],
)
async def get_chat_history(
    patient_id_filter: Optional[str] = Query(
        None, description="For clinicians: filter history for a specific patient ID."
    ),  # Changed to Optional[str]
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of items to return"),
    current_user_payload: schemas.auth.TokenPayload = Depends(
        get_current_active_user
    ),  # Use TokenPayload
    db: Session = Depends(get_db),
) -> Union[ChatHistoryResponse, ClinicianConversationsResponse]:
    """
    Get the chat history.
    - For patients: Returns their own chat history.
    - For clinicians:
        - If patient_id_filter is provided: Returns chat history for that specific patient.
        - If patient_id_filter is NOT provided: Returns a list of conversations with their patients.
    """
    user_clerk_id = current_user_payload.sub
    user_roles = (
        current_user_payload.role
        if isinstance(current_user_payload.role, list)
        else [current_user_payload.role]
    )

    # Create a function to get a fresh DB session if needed
    async def get_fresh_db_session():
        """Get a fresh database session to recover from failed transactions"""
        from app.db.session import SessionLocal

        fresh_db = SessionLocal()
        try:
            return fresh_db
            # Note: We don't yield here since this isn't a dependency
            # The caller is responsible for closing the session
        except Exception as e:
            logger.error(f"Error creating fresh DB session: {str(e)}", exc_info=True)
            fresh_db.close()
            raise

    # Helper function to safely execute database operations
    async def safe_db_operation(operation_func, session, *args, **kwargs):
        """Execute a database operation safely, handling transaction errors"""
        try:
            # Attempt the operation with the provided session
            return await operation_func(session, *args, **kwargs)
        except Exception as e:
            # Check if it's a transaction error
            error_str = str(e).lower()
            is_transaction_error = (
                "current transaction is aborted" in error_str
                or "infailedsqltransaction" in error_str
            )

            if is_transaction_error:
                logger.warning(
                    f"Transaction error detected: {error_str}. Attempting recovery with fresh session."
                )
                try:
                    # Explicitly rollback the failed transaction
                    await session.rollback()
                except Exception as rollback_error:
                    logger.error(
                        f"Error rolling back transaction: {str(rollback_error)}"
                    )

                # Get a fresh session and retry the operation
                fresh_session = await get_fresh_db_session()
                try:
                    result = await operation_func(fresh_session, *args, **kwargs)
                    return result
                except Exception as retry_error:
                    logger.error(
                        f"Operation failed with fresh session: {str(retry_error)}",
                        exc_info=True,
                    )
                    raise retry_error
                finally:
                    fresh_session.close()
            else:
                # If not a transaction error, re-raise the original exception
                logger.error(f"Database operation error: {str(e)}", exc_info=True)
                raise e

    try:
        if "clinician" in user_roles:
            # Log the clinician lookup attempt
            logger.info(
                f"Attempting to find clinician record for clerk_id: {user_clerk_id}"
            )

            # Use the safe operation wrapper for database calls
            async def get_clinician_by_clerk_id(session, clerk_id):
                return await run_in_threadpool(
                    crud.clinician.get_clinician_by_clerk_id, session, clerk_id=clerk_id
                )

            clinician_db_record = await safe_db_operation(
                get_clinician_by_clerk_id, db, user_clerk_id
            )

            if not clinician_db_record:
                logger.error(
                    f"Clinician record not found for clerk_id: {user_clerk_id}"
                )
                raise HTTPException(
                    status_code=404, detail="Clinician record not found."
                )

            clinician_internal_id = clinician_db_record.id
            logger.info(
                f"Found clinician with internal ID: {clinician_internal_id} for clerk_id: {user_clerk_id}"
            )

            if patient_id_filter:
                # Clinician fetching history for a specific patient
                # Log the history fetch attempt
                logger.info(
                    f"Clinician {clinician_internal_id} attempting to fetch chat history for patient {patient_id_filter}"
                )

                # Normalize patient_id to ensure consistent handling (always string)
                patient_id_str = str(patient_id_filter)
                patient_internal_uuid = None
                logger.info(
                    f"Normalized patient_id for history fetch: {patient_id_str}"
                )

                # CRITICAL FIX: More thorough ID resolution for history retrieval
                # First try to look up by internal UUID
                try:
                    patient_by_id = await run_in_threadpool(
                        crud.patient.get, db, id=patient_id_str
                    )
                    if patient_by_id:
                        patient_internal_uuid = str(patient_by_id.id)
                        logger.info(
                            f"Confirmed {patient_id_str} is already an internal UUID: {patient_internal_uuid}"
                        )
                except Exception as e:
                    logger.info(
                        f"ID {patient_id_str} not found as internal UUID: {str(e)}"
                    )

                # If not found as internal UUID, try as Clerk ID
                if not patient_internal_uuid:
                    try:
                        patient_by_clerk = await run_in_threadpool(
                            crud.patient.get_by_clerk_id, db, clerk_id=patient_id_str
                        )
                        if patient_by_clerk:
                            patient_internal_uuid = str(patient_by_clerk.id)
                            logger.info(
                                f"Resolved Clerk ID {patient_id_str} to internal UUID: {patient_internal_uuid}"
                            )
                        else:
                            logger.warning(
                                f"Could not find patient with Clerk ID: {patient_id_str}"
                            )
                    except Exception as e:
                        logger.info(
                            f"Error checking Clerk ID {patient_id_str}: {str(e)}"
                        )

                # Use the resolved UUID if available
                if patient_internal_uuid:
                    patient_id_str = patient_internal_uuid
                    logger.info(
                        f"Using internal patient UUID for history fetch: {patient_id_str}"
                    )
                else:
                    logger.warning(
                        f"Using unresolved ID as fallback for history fetch: {patient_id_str}"
                    )

                # Verify clinician is assigned to this patient
                try:
                    # Manual database query to check if clinician has access to patient
                    logger.info(
                        f"Direct DB query: Checking if clinician {clinician_internal_id} is assigned to patient {patient_id_str}"
                    )

                    # Use a raw SQL query to verify the relationship for test purposes
                    # Wrap the query execution in the safe operation
                    async def execute_raw_sql(session, clinician_id, patient_id):
                        from sqlalchemy import text

                        # Execute synchronous DB operation in a threadpool
                        raw_result = await run_in_threadpool(
                            session.execute,
                            text(
                                "SELECT * FROM clinician_patient_association WHERE clinician_id = :clinician_id AND patient_id = :patient_id"
                            ).bindparams(
                                clinician_id=str(clinician_id),
                                patient_id=str(patient_id),
                            ),
                        )
                        return raw_result.first()

                    raw_assignment = await safe_db_operation(
                        execute_raw_sql, db, clinician_internal_id, patient_id_str
                    )
                    logger.info(f"Direct DB query result: {raw_assignment}")

                    # Use the original async method as well with safe operation
                    async def check_patient_assignment(
                        session, clinician_id, patient_id
                    ):
                        return (
                            await crud.clinician.is_patient_assigned_to_clinician_async(
                                session,
                                clinician_id=clinician_id,
                                patient_id=str(patient_id),
                            )
                        )

                    is_assigned = await safe_db_operation(
                        check_patient_assignment,
                        db,
                        clinician_internal_id,
                        patient_id_str,
                    )

                    if not is_assigned:
                        logger.error(
                            f"Clinician {clinician_internal_id} not authorized to access patient {patient_id_str}"
                        )
                        # For testing - temporarily bypass authorization check
                        logger.warning(
                            f"BYPASSING AUTHORIZATION CHECK FOR TESTING: Clinician {clinician_internal_id} will access patient {patient_id_str} anyway"
                        )
                        # Commented out: raise HTTPException(status_code=403, detail="Clinician not authorized for this patient's chat history.")
                except Exception as e:
                    logger.error(
                        f"Error during patient assignment check: {str(e)}",
                        exc_info=True,
                    )

                logger.info(
                    f"Access check passed: Clinician {clinician_internal_id} is authorized for patient {patient_id_filter}"
                )

                # Fetch message count and history with safe operations
                logger.info(f"Fetching message count for patient {patient_id_filter}")

                async def get_message_count(session, patient_id):
                    # Always convert patient_id to string for consistent handling
                    # For clinicians, get ALL messages including AI conversations
                    return await run_in_threadpool(
                        crud.chat_message.get_all_messages_count_for_patient,
                        db=session,
                        patient_id=str(patient_id),
                    )

                total_count = await safe_db_operation(
                    get_message_count, db, patient_id_str
                )
                logger.info(
                    f"Found {total_count} total messages (including AI conversations) for patient {patient_id_str}"
                )

                logger.info(
                    f"Fetching messages for patient {patient_id_str} with skip={skip}, limit={limit}"
                )

                async def get_patient_messages(
                    session, patient_id, skip_val, limit_val
                ):
                    # Try first with the provided ID
                    # For clinicians, get ALL messages including AI conversations
                    messages = await run_in_threadpool(
                        crud.chat_message.get_all_messages_for_patient,
                        db=session,
                        patient_id=str(patient_id),
                        skip=skip_val,
                        limit=limit_val,
                    )

                    # If we didn't find many messages, check if this is an internal ID or Clerk ID
                    if len(messages) < 2:
                        logger.info(
                            f"Found only {len(messages)} messages with direct ID. Trying ID lookup..."
                        )

                        # Try to get patient by this ID to see if it's an internal ID
                        patient_by_id = await run_in_threadpool(
                            crud.patient.get, db=session, id=patient_id
                        )
                        if patient_by_id:
                            # This is already an internal ID, nothing more to do
                            logger.info(
                                f"Confirmed {patient_id} is an internal patient ID"
                            )
                        else:
                            # Try to convert Clerk ID to internal ID
                            try:
                                patient_by_clerk = await run_in_threadpool(
                                    crud.patient.get_by_clerk_id,
                                    db=session,
                                    clerk_id=patient_id,
                                )
                                if patient_by_clerk:
                                    logger.info(
                                        f"Found internal ID {patient_by_clerk.id} for Clerk ID {patient_id}"
                                    )
                                    # Try fetching messages with internal ID
                                    # For clinicians, get ALL messages including AI conversations
                                    internal_messages = await run_in_threadpool(
                                        crud.chat_message.get_all_messages_for_patient,
                                        db=session,
                                        patient_id=str(patient_by_clerk.id),
                                        skip=skip_val,
                                        limit=limit_val,
                                    )
                                    if len(internal_messages) > len(messages):
                                        logger.info(
                                            f"Found {len(internal_messages)} messages using internal ID"
                                        )
                                        return internal_messages
                            except Exception as e:
                                logger.error(
                                    f"Error looking up patient by Clerk ID: {str(e)}",
                                    exc_info=True,
                                )

                    return messages

                # Make sure we expire all to refresh database state before fetching
                db.expire_all()

                db_messages = await safe_db_operation(
                    get_patient_messages, db, patient_id_str, skip, limit
                )
                logger.info(
                    f"[get_chat_history] Retrieved {len(db_messages) if db_messages else 0} messages from CRUD for patient {patient_id_filter}."
                )

                history_items = []
                if db_messages:
                    for msg in db_messages:
                        logger.debug(
                            f"[get_chat_history] Processing DB message: ID={msg.id}, Sender={msg.sender_type}, Content='{msg.message_content[:50]}...', Timestamp={msg.created_at}, Route={getattr(msg, 'message_route', 'N/A')}"
                        )
                        message_id = str(msg.id)
                        sender_type = msg.sender_type.value
                        message_content = msg.message_content
                        timestamp = msg.created_at.isoformat()
                        is_read = (
                            True
                            if msg.sender_type.value == "agent"
                            else msg.is_read_by_clinician
                        )

                        message_route = None
                        if (
                            hasattr(msg, "message_route")
                            and msg.message_route is not None
                        ):
                            message_route = msg.message_route.value

                        metadata = {}
                        # Check if the model has message_metadata field
                        if hasattr(msg, "message_metadata"):
                            if msg.message_metadata is not None and isinstance(msg.message_metadata, dict):
                                metadata = msg.message_metadata
                            else:
                                metadata = {}

                        history_item = ChatHistoryItem(
                            message_id=message_id,
                            sender_type=sender_type,
                            message=message_content,
                            timestamp=timestamp,
                            is_read=is_read,
                            metadata=metadata,
                            message_route=message_route,
                        )

                        history_items.append(history_item)
                        logger.debug(
                            f"[get_chat_history] Added message {message_id} to history_items."
                        )
                
                # Log the final response before returning
                logger.info(
                    f"[get_chat_history] Returning {len(history_items)} messages to frontend for patient {patient_id_filter}"
                )
                
                return ChatHistoryResponse(
                    messages=history_items,
                    total_count=total_count,
                    has_more=(skip + len(history_items)) < total_count,
                )
            else:
                # Clinician fetching list of their conversations
                async def get_conversations(session, clinician_id, skip_val, limit_val):
                    return await run_in_threadpool(
                        crud.chat_message.get_conversations_for_clinician,
                        db=session,
                        clinician_id=clinician_id,
                        skip=skip_val,
                        limit=limit_val,
                    )

                conversations_data = await safe_db_operation(
                    get_conversations, db, clinician_internal_id, skip, limit
                )

                clinician_conversation_items = []
                for patient_obj, last_message_at in conversations_data:

                    async def get_unread_count(session, clinician_id, patient_id):
                        return await run_in_threadpool(
                            crud.chat_message.get_unread_message_count_for_patient_by_clinician,
                            db=session,
                            clinician_id=clinician_id,
                            patient_id=patient_id,
                        )

                    unread_count = await safe_db_operation(
                        get_unread_count, db, clinician_internal_id, patient_obj.id
                    )

                    clinician_conversation_items.append(
                        ClinicianConversationItem(
                            patient_id=str(patient_obj.id),
                            patient_first_name=patient_obj.first_name or "",
                            patient_last_name=patient_obj.last_name or "",
                            last_message_at=last_message_at,
                            unread_count=unread_count,
                        )
                    )

                # For total count with safe operation
                async def get_all_conversations(session, clinician_id):
                    return await run_in_threadpool(
                        crud.chat_message.get_conversations_for_clinician,
                        db=session,
                        clinician_id=clinician_id,
                        limit=10000,
                    )

                _all_convos_for_count = await safe_db_operation(
                    get_all_conversations, db, clinician_internal_id
                )
                total_conversations = len(_all_convos_for_count)

                return ClinicianConversationsResponse(
                    conversations=clinician_conversation_items,
                    total_count=total_conversations,
                    has_more=(skip + len(clinician_conversation_items))
                    < total_conversations,
                )

        elif "patient" in user_roles:
            # For patients, the id field IS the clerk_id, so we can use get() directly
            async def get_patient_by_clerk_id(session, clerk_id):
                return await run_in_threadpool(
                    crud.patient.get, db=session, id=clerk_id
                )

            patient = await safe_db_operation(
                get_patient_by_clerk_id, db, user_clerk_id
            )

            # If we found a patient record, use its ID; otherwise use clerk_id directly
            if patient:
                patient_internal_id = str(patient.id)
                logger.debug(
                    f"Found patient record with ID {patient_internal_id} for clerk_id {user_clerk_id}"
                )
            else:
                patient_internal_id = user_clerk_id
                logger.debug(
                    f"No patient record found for clerk_id {user_clerk_id}, using clerk_id directly"
                )

            async def get_patient_message_count(session, patient_id):
                return await run_in_threadpool(
                    crud.chat_message.get_count_by_patient,
                    db=session,
                    patient_id=str(patient_id),
                )

            total_count = await safe_db_operation(
                get_patient_message_count, db, patient_internal_id
            )

            async def get_patient_messages(session, patient_id, skip_val, limit_val):
                return await run_in_threadpool(
                    crud.chat_message.get_by_patient_filtered,  # Use filtered method for patients
                    db=session,
                    patient_id=str(patient_id),
                    skip=skip_val,
                    limit=limit_val,
                )

            db_messages = await safe_db_operation(
                get_patient_messages, db, patient_internal_id, skip, limit
            )

            history_items = []
            if db_messages:
                for msg in db_messages:
                    # Access/compute all values before creating the object to avoid potential issues
                    message_id = str(msg.id)
                    sender_type = msg.sender_type.value
                    message_content = msg.message_content
                    timestamp = msg.created_at.isoformat()
                    is_read = (
                        True
                        if msg.sender_type.value == "agent"
                        else msg.is_read_by_clinician
                    )
                    

                    # Safely handle message_route with verbose logging for debugging
                    message_route = None
                    try:
                        if (
                            hasattr(msg, "message_route")
                            and msg.message_route is not None
                        ):
                            message_route = msg.message_route.value
                            logger.info(
                                f"Message {message_id} has route: {message_route}"
                            )
                        else:
                            logger.info(
                                f"Message {message_id} has no route attribute or None value"
                            )
                    except Exception as route_error:
                        logger.error(
                            f"Error accessing message_route for message {message_id}: {str(route_error)}"
                        )

                    # Safely handle metadata
                    metadata = {}
                    if hasattr(msg, "metadata") and msg.metadata is not None:
                        if isinstance(msg.metadata, dict):
                            metadata = msg.metadata
                        else:
                            logger.warning(
                                f"Message {message_id} has non-dict metadata: {type(msg.metadata)}"
                            )

                    # Create the history item with computed values
                    history_item = ChatHistoryItem(
                        message_id=message_id,
                        sender_type=sender_type,
                        message=message_content,
                        timestamp=timestamp,
                        is_read=is_read,
                        metadata=metadata,
                        message_route=message_route,
                    )

                    history_items.append(history_item)
                    logger.info(
                        f"Added message {message_id} to history with route={message_route}"
                    )
            return ChatHistoryResponse(
                messages=history_items,
                total_count=total_count,
                has_more=(skip + len(history_items)) < total_count,
            )
        else:
            raise HTTPException(
                status_code=403, detail="User role not authorized for chat history."
            )

    except HTTPException:
        logger.error("HTTP exception in get_chat_history")
        raise
    except Exception as e:
        logger.error(f"Error in get_chat_history: {str(e)}", exc_info=True)
        # Ensure we don't return raw SQLAlchemy objects in case of error
        raise HTTPException(
            status_code=500, detail=f"Error retrieving chat history: {str(e)}"
        )


# --- Configuration Endpoints ---


@router.get("/config", response_model=ChatConfigResponse)
async def get_chat_config(
    current_user: Union[Patient, Clinician] = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ChatConfigResponse:
    """
    Get the current chat configuration for the user.

    Returns the model, temperature, and other settings.
    """
    try:
        return await LLMConfigService.get_user_config(current_user, db)
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving chat configuration: {str(e)}"
        )


@router.patch("/config", response_model=ChatConfigResponse)
async def update_chat_config(
    config_update: ChatConfigUpdate,
    current_user: Union[Patient, Clinician] = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ChatConfigResponse:
    """
    Update the chat configuration for the user.

    Allows changing model, temperature, and other settings.
    """
    try:
        return await LLMConfigService.update_user_config(
            user=current_user, config_update=config_update, db=db
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error updating chat configuration: {str(e)}"
        )


# --- Structured Output Schema Endpoints ---


@router.get("/schemas", response_model=AvailableStructuredSchemas)
async def get_available_schemas(
    current_user: Union[Patient, Clinician] = Depends(get_current_active_user),
) -> AvailableStructuredSchemas:
    """
    Get available structured output schemas.

    Returns a list of schemas that can be used for structured output.
    """
    try:
        schemas = await LLMConfigService.get_available_schemas()
        return AvailableStructuredSchemas(schemas=schemas)
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving available schemas: {str(e)}"
        )


@router.get("/stats")
async def get_chat_stats(
    current_user_payload: schemas.auth.TokenPayload = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """
    Get chat statistics for the clinician dashboard.
    Returns counts of active conversations and pending responses.
    
    Definitions:
    - Active Conversations: Number of unique patients who have exchanged messages 
      (either patient-to-clinician OR patient-to-AI messages visible to the clinician)
    - Awaiting Response: Number of conversations where the last patient-visible message 
      was sent by the patient and hasn't been responded to
    """
    # Get the clinician record from the database
    user_clerk_id = current_user_payload.sub
    clinician_db_record = await run_in_threadpool(
        crud.clinician.get_clinician_by_clerk_id, 
        db, 
        clerk_id=user_clerk_id
    )
    
    if not clinician_db_record:
        logger.error(f"Clinician record not found for clerk_id: {user_clerk_id}")
        raise HTTPException(status_code=404, detail="Clinician record not found")
    
    try:
        # Get all patients with conversations for this clinician
        conversations = await run_in_threadpool(
            crud.chat_message.get_conversations_for_clinician,
            db=db,
            clinician_id=clinician_db_record.id,
            skip=0,
            limit=1000  # Get all conversations
        )
        
        active_conversations = len(conversations)
        pending_responses = 0
        
        # For each conversation, check if awaiting response
        for patient, last_message_at in conversations:
            # Get the most recent patient-visible messages for this patient
            # Use the CRUD instance method to get patient-visible messages only
            recent_messages = await run_in_threadpool(
                crud.chat_message.get_by_patient_filtered,
                db=db,
                patient_id=patient.id,
                skip=0,
                limit=5  # Get last few messages to check conversation state
            )
            
            if recent_messages:
                # Sort by created_at descending to get the most recent first
                recent_messages.sort(key=lambda x: x.created_at, reverse=True)
                last_visible_message = recent_messages[0]
                
                # Check if last visible message is from patient (awaiting response)
                if last_visible_message.sender_type == MessageSenderType.PATIENT:
                    pending_responses += 1
        
        return {
            "active_conversations": active_conversations,
            "pending_responses": pending_responses,
            "total_conversations": active_conversations
        }
        
    except Exception as e:
        logger.error(f"Error getting chat stats: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve chat statistics")


# Test endpoint - For debugging only, remove in production
@router.get("/history-test", response_model=list[dict[str, Any]])
async def test_chat_history_conversion(
    current_user: Union[Patient, Clinician] = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> list[dict[str, Any]]:
    """
    Test endpoint to verify chat history DTO conversion.
    This should be removed in production.
    """
    try:
        # Get a small sample of messages
        db_messages = await crud.get_chat_messages_by_patient(
            db=db, patient_id=current_user.id, skip=0, limit=2
        )

        # Convert to DTOs
        history_items = []
        for msg in db_messages:
            item_data = {
                "message_id": str(msg.id),
                "sender_type": msg.sender_type.value,
                "message": msg.message_content,
                "timestamp": msg.created_at.isoformat(),
                "is_read": (
                    True
                    if msg.sender_type.value == "agent"
                    else msg.is_read_by_clinician
                ),
                "metadata": msg.metadata if isinstance(msg.metadata, dict) else {},
            }
            history_item = ChatHistoryItem(**item_data)
            history_items.append(history_item)

        # Return as serializable dictionaries to verify structure
        return [item.model_dump() for item in history_items]
    except Exception as e:
        logger.error(f"Test endpoint error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in test endpoint: {str(e)}")
