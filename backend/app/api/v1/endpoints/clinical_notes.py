"""API endpoints for Clinical Notes."""

import logging
from typing import Any, List, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app import crud
from app.api import deps
from app.api.deps import get_current_clinician
from app.models.clinician import Clinician
from app.models.clinical_note import ClinicalNoteStatus
from app.schemas.clinical_note import (
    ClinicalNote,
    ClinicalNoteCreate,
    ClinicalNoteUpdate,
    ClinicalNoteApprove,
    ClinicalNoteList,
    ClinicalNoteWithRelations,
    GenerateClinicalNoteRequest,
    GenerateClinicalNoteResponse,
)
from app.services.clinical_notes_service import clinical_notes_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate", response_model=GenerateClinicalNoteResponse)
async def generate_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    request: GenerateClinicalNoteRequest,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Generate a clinical note from chat conversation.
    
    This endpoint uses AI to extract clinical information from patient-clinician
    chat messages and structures it into a SOAP-format clinical note.
    
    Args:
        request: Generation parameters including patient ID, time range, note type
        
    Returns:
        Generated clinical note with confidence score and metadata
        
    Raises:
        HTTPException: If patient not found or no messages available
    """
    logger.info(
        f"Clinician {current_clinician.id} generating clinical note for patient {request.patient_id}"
    )
    
    # Verify clinician has access to this patient
    # Patient ID is the clerk_id (string primary key)
    patient = crud.patient.get(db, id=request.patient_id)
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Patient {request.patient_id} not found"
        )
    
    logger.info(f"Found patient with internal ID: {patient.id} for clerk_id: {request.patient_id}")
    
    # Check if clinician is associated with patient
    if current_clinician not in patient.clinicians:
        logger.warning(
            f"Clinician {current_clinician.id} not associated with patient {patient.id}. "
            f"Patient has {len(patient.clinicians)} associated clinicians"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have access to this patient's records"
        )
    
    try:
        # Generate the clinical note
        logger.info(f"Generating note for patient internal ID: {patient.id}")
        response = await clinical_notes_service.generate_clinical_note(
            db=db,
            request=request,
            clinician_id=current_clinician.id
        )
        
        logger.info(
            f"Generated clinical note {response.note.id} with confidence {response.confidence_score}"
        )
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating clinical note: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate clinical note"
        )


@router.get("/", response_model=ClinicalNoteList)
def get_clinical_notes(
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician),
    patient_id: Optional[str] = Query(None, description="Filter by patient ID"),
    status: Optional[ClinicalNoteStatus] = Query(None, description="Filter by status"),
    note_type: Optional[str] = Query(None, description="Filter by note type"),
    include_relations: bool = Query(False, description="Include patient and clinician names"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
) -> Any:
    """
    Get clinical notes with optional filters.
    
    Returns notes authored by the current clinician or for patients they have access to.
    """
    # If filtering by patient, verify access
    if patient_id:
        patient = crud.patient.get(db, id=patient_id)
        if not patient or current_clinician not in patient.clinicians:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to patient records"
            )
        
        notes = crud.clinical_note.get_by_patient(
            db, 
            patient_id=patient_id,
            status=status,
            skip=skip,
            limit=limit
        )
        total = db.query(crud.clinical_note.model).filter(
            crud.clinical_note.model.patient_id == patient_id
        ).count()
    else:
        # Get notes authored by current clinician
        notes = crud.clinical_note.search(
            db,
            clinician_id=current_clinician.id,
            status=status,
            note_type=note_type,
            skip=skip,
            limit=limit
        )
        total = db.query(crud.clinical_note.model).filter(
            crud.clinical_note.model.clinician_id == current_clinician.id
        ).count()
    
    # Enrich with relations if requested
    if include_relations:
        enriched_notes = []
        for note in notes:
            enriched = ClinicalNoteWithRelations.model_validate(note)
            
            # Add patient name
            patient = crud.patient.get(db, id=note.patient_id)
            if patient:
                enriched.patient_name = f"{patient.first_name} {patient.last_name}"
            
            # Add clinician name
            if note.clinician:
                enriched.clinician_name = f"{note.clinician.first_name} {note.clinician.last_name}"
            
            # Add approver name if approved
            if note.approved_by:
                approver = crud.clinician.get(db, id=note.approved_by)
                if approver:
                    enriched.approver_name = f"{approver.first_name} {approver.last_name}"
            
            enriched_notes.append(enriched)
        
        return ClinicalNoteList(
            items=enriched_notes,
            total=total,
            skip=skip,
            limit=limit
        )
    
    return ClinicalNoteList(
        items=notes,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{note_id}", response_model=ClinicalNoteWithRelations)
def get_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    note_id: UUID,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Get a specific clinical note by ID.
    
    Returns the note with related patient, clinician, and appointment information.
    """
    note = crud.clinical_note.get(db, note_id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinical note not found"
        )
    
    # Verify access - either author or has access to patient
    patient = crud.patient.get(db, id=note.patient_id)
    if note.clinician_id != current_clinician.id and current_clinician not in patient.clinicians:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this clinical note"
        )
    
    # Enrich with related data
    response = ClinicalNoteWithRelations.model_validate(note)
    response.patient_name = f"{patient.first_name} {patient.last_name}"
    response.clinician_name = f"{note.clinician.first_name} {note.clinician.last_name}"
    
    if note.approved_by:
        approver = crud.clinician.get(db, id=note.approved_by)
        if approver:
            response.approver_name = f"{approver.first_name} {approver.last_name}"
    
    if note.appointment:
        response.appointment_datetime = note.appointment.appointment_datetime
    
    if note.template:
        response.template_name = note.template.name
    
    return response


@router.put("/{note_id}", response_model=ClinicalNote)
def update_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    note_id: UUID,
    note_update: ClinicalNoteUpdate,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Update a clinical note.
    
    Only the author can update a note, and only if it's still in draft status.
    """
    note = crud.clinical_note.get(db, note_id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinical note not found"
        )
    
    # Verify author
    if note.clinician_id != current_clinician.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the author can edit this note"
        )
    
    # Check if note is editable (not approved)
    if note.status == ClinicalNoteStatus.APPROVED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot edit an approved note"
        )
    
    # Update the note
    updated_note = crud.clinical_note.update(
        db, db_obj=note, obj_in=note_update
    )
    
    logger.info(f"Clinical note {note_id} updated by {current_clinician.id}")
    
    return updated_note


@router.post("/{note_id}/approve", response_model=ClinicalNote)
def approve_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    note_id: UUID,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Approve a clinical note.
    
    The approver can be the author or another clinician with access to the patient.
    """
    note = crud.clinical_note.get(db, note_id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinical note not found"
        )
    
    # Verify access
    patient = crud.patient.get(db, id=note.patient_id)
    if current_clinician not in patient.clinicians:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to approve this note"
        )
    
    # Check if already approved
    if note.status == ClinicalNoteStatus.APPROVED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Note is already approved"
        )
    
    # Approve the note
    approved_note = crud.clinical_note.approve(
        db, db_obj=note, approver_id=current_clinician.id
    )
    
    logger.info(
        f"Clinical note {note_id} approved by {current_clinician.id}"
    )
    
    return approved_note


@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    note_id: UUID,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> None:
    """
    Delete a clinical note.
    
    Only the author can delete a note, and only if it's still in draft status.
    """
    note = crud.clinical_note.get(db, note_id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinical note not found"
        )
    
    # Verify author
    if note.clinician_id != current_clinician.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the author can delete this note"
        )
    
    # Remove the restriction on deleting approved notes
    # Authors can delete their own notes regardless of status
    logger.info(f"Allowing deletion of {note.status} note by author")
    
    # Before deleting, find and update any associated chat messages
    from app.models.chat_message import ChatMessage
    
    # Get clinician info for the message
    clinician_name = f"Dr. {current_clinician.last_name}" if current_clinician.last_name else "Clinician"
    
    chat_messages = db.query(ChatMessage).filter(
        and_(
            ChatMessage.sender_type == "CLINICAL_NOTE",
            ChatMessage.message_metadata.op("@>")({"clinical_note_id": str(note_id)})
        )
    ).all()
    
    for chat_msg in chat_messages:
        # Update the metadata to mark as deleted
        updated_metadata = chat_msg.message_metadata.copy() if chat_msg.message_metadata else {}
        updated_metadata["deleted"] = True
        updated_metadata["deleted_at"] = datetime.utcnow().isoformat()
        updated_metadata["deleted_by"] = current_clinician.id
        
        # Remove sensitive data but keep basic info
        if "sections" in updated_metadata:
            del updated_metadata["sections"]
        if "suggested_icd10_codes" in updated_metadata:
            del updated_metadata["suggested_icd10_codes"]
        if "suggested_cpt_codes" in updated_metadata:
            del updated_metadata["suggested_cpt_codes"]
        
        # Update the message
        chat_msg.message_metadata = updated_metadata
        chat_msg.message_content = f"Clinical Note (Deleted): {note.note_type} by {clinician_name}"
        
    db.commit()
    
    # Delete the note
    crud.clinical_note.remove(db, id=note_id)
    
    logger.info(f"Clinical note {note_id} deleted by {current_clinician.id}, updated {len(chat_messages)} chat messages")


@router.get("/patient/{patient_id}/latest", response_model=Optional[ClinicalNote])
def get_latest_clinical_note(
    *,
    db: Session = Depends(deps.get_db),
    patient_id: str,
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Get the most recent clinical note for a patient.
    """
    # Verify access
    patient = crud.patient.get(db, id=patient_id)
    if not patient or current_clinician not in patient.clinicians:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to patient records"
        )
    
    notes = crud.clinical_note.get_by_patient(
        db, patient_id=patient_id, limit=1
    )
    
    return notes[0] if notes else None


@router.get("/drafts/mine", response_model=List[ClinicalNote])
def get_my_draft_notes(
    db: Session = Depends(deps.get_db),
    current_clinician: Clinician = Depends(get_current_clinician),
) -> Any:
    """
    Get all draft clinical notes authored by the current clinician.
    """
    drafts = crud.clinical_note.get_draft_notes_by_clinician(
        db, clinician_id=current_clinician.id
    )
    
    return drafts