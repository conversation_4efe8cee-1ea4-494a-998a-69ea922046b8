# backend\app\api\v1\endpoints\clinicians.py

# Standard Library Imports
import datetime
import logging
import math
import mimetypes
from datetime import timedelta, timezone
from enum import Enum
from typing import IO, Optional
from uuid import UUID

# Third-Party Imports
from clerk_backend_api import models as clerk_models
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Path,
    Query,
    Response,
    UploadFile,
)
from fastapi import (
    status as http_status,
)
from fastapi.responses import (
    StreamingResponse,
)
from sqlalchemy.orm import Session

# Local Imports
from app import crud, models
from app.api import deps
from app.core.auth_utils import (
    clerk,
    generate_secure_magic_code,
)
from app.core.config import settings
from app.core.storage import upload_clinician_profile_photo
from app.crud.crud_access_code import AccessCodeError
from app.models.medication_request import MedicationRequest, MedicationRequestStatus
from app.models.patient import Patient  # Add import for Patient model
from app.schemas.appointment import AppointmentResponse
from app.schemas.appointment_request import (
    AppointmentRequestResponse,
    AppointmentRequestStatusUpdate,
    PaginatedAppointmentRequestResponse,
)
from app.schemas.auth import AccessCodeCreate, AccessCodeResponse, TokenPayload
from app.schemas.clinic import ClinicRead
from app.schemas.clinician import ClinicianResponse
from app.schemas.invitation import (
    PaginatedClerkInvitationListResponse,
    PatientInvitationRequest,
    PatientInvitationResponse,
)
from app.schemas.medication_request import MedicationRequestResponse
from app.schemas.patient import (
    PaginatedPatientListResponse,
    PatientProfileResponse,
)
from app.schemas.side_effect_report import SideEffectReportResponse
from app.schemas.weight_log import WeightLogEntryResponse
from app.utils.audit import log_audit_event
from app.utils.sorting import validate_sort_params

logger = logging.getLogger(__name__)

router = APIRouter()


# --- Re-add Helper Enum for Sorting Order ---
class SortOrder(str, Enum):
    ASC = "asc"
    DESC = "desc"


# --- End Re-add Helper Enum ---


@router.get(
    "/patients/{patientId}",
    response_model=PatientProfileResponse,
    summary="Get Patient Profile by ID (Clinician)",
    description="View patient profile. Requires clinician auth and access rights.",
    responses={
        200: {"description": "Patient profile retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or not authorized for this patient"
        },
        404: {"description": "Patient not found"},
    },
    tags=["Clinicians"],
)
async def read_patient_profile_by_clinician(
    *,
    patientId: str = Path(..., title="The ID of the patient to retrieve"),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(
        deps.get_current_clinician
    ),
) -> PatientProfileResponse:
    logger.debug(
        f"Clinician {current_clinician.clerk_id} requesting profile for patient {patientId} (RBAC passed)"
    )

    patient = crud.patient.get(db=db, id=patientId)
    if not patient:
        logger.error(f"RBAC dependency passed but patient {patientId} not found.")
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND, detail="Patient not found."
        )

    logger.debug(f"Found profile data for patient {patientId}: {patient.email}")
    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_PATIENT_PROFILE_BY_CLINICIAN",
        outcome="SUCCESS",
        target_resource_type="Patient",
        target_resource_id=str(patientId),
        details={"accessed_fields": "full_profile"},
    )
    return patient


@router.get(
    "/patients/{patientId}/weight-log",
    response_model=list[WeightLogEntryResponse],
    summary="Get Patient Weight Log History (Clinician)",
    description="Retrieves the weight logging history for a patient, accessible only by authorized clinicians.",
    responses={
        200: {"description": "Weight log history retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or not authorized for this patient"
        },
        404: {"description": "Patient not found"},
    },
    tags=["Weight Tracking"],
)
async def read_patient_weight_log_history_by_clinician(
    *,
    patientId: str = Path(
        ..., title="The ID of the patient whose weight log history is requested"
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return per page"
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[WeightLogEntryResponse]:
    logger.debug(
        f"Clinician {current_clinician.clerk_id} requesting weight log history for patient {patientId}, skip: {skip}, limit: {limit} (RBAC passed)"
    )

    weight_logs = crud.weight_log.get_weight_logs_by_patient(
        db=db, patient_id=patientId, skip=skip, limit=limit
    )
    logger.debug(f"Found {len(weight_logs)} weight log entries for patient {patientId}")
    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_PATIENT_WEIGHT_LOG_BY_CLINICIAN",
        outcome="SUCCESS",
        target_resource_type="WeightLog",
        target_resource_id=str(patientId),
        details={"count": len(weight_logs), "skip": skip, "limit": limit},
    )
    return weight_logs


@router.get(
    "/medication-requests",
    response_model=list[MedicationRequestResponse],
    summary="Get Medication Requests (Clinician)",
    description="List all medication requests for clinicians' patients.",
    responses={
        200: {"description": "Medication requests retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
    },
    tags=["Medication"],
)
async def read_medication_requests_by_clinician(
    status: Optional[str] = Query(
        None, description="Filter requests by status (e.g., Pending, Approved, Denied)."
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[MedicationRequestResponse]:
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    logger.debug(
        f"Fetching medication requests. Filter status: {status}, "
        f"Skip: {skip}, Limit: {limit}"
    )

    assigned_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_id, limit=10000
    )
    assigned_patient_ids = [p.id for p in assigned_patients]

    if not assigned_patient_ids:
        return []

    # Update the query to join with Patient model to include patient details
    query = (
        db.query(MedicationRequest, Patient)
        .join(Patient, MedicationRequest.patient_id == Patient.id)
        .filter(MedicationRequest.patient_id.in_(assigned_patient_ids))
    )

    if status:
        query = query.filter(MedicationRequest.status == status)

    results = (
        query.order_by(MedicationRequest.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

    # Transform the results using Pydantic's ORM mode
    validated_requests = []
    for med_req, patient in results:
        try:
            # Validate the main MedicationRequest object using ORM mode
            # Pydantic will handle fields like requested_at (alias for created_at)
            validated_req = MedicationRequestResponse.model_validate(med_req)

            # Manually add the patient info from the joined query
            # Ensure PatientInfo schema has from_attributes=True if validating Patient ORM directly
            validated_req.patient = {
                "id": patient.id,
                "first_name": patient.first_name,
                "last_name": patient.last_name,
            }
            validated_requests.append(validated_req)
        except Exception as e:
            logger.error(
                f"Failed to validate medication request {med_req.id} for response: {e}",
                exc_info=True,
            )
            # Optionally skip this request or handle the error differently

    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_MEDICATION_REQUESTS_BY_CLINICIAN",
        outcome="SUCCESS",
        target_resource_type="MedicationRequest",
        details={
            "count": len(validated_requests),
            "skip": skip,
            "limit": limit,
            "status_filter": status,
        },
    )

    return validated_requests


@router.get(
    "/patients",
    response_model=PaginatedPatientListResponse,  # Changed response model
    summary="Get Patient List (Clinician)",
    description="Retrieves a paginated list of patients associated with the authenticated clinician. Supports basic search and sorting.",
    responses={
        200: {"description": "Patient list retrieved successfully"},
        400: {"description": "Invalid search or sort parameters"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
    },
    tags=["Clinicians"],
)
async def read_patients_by_clinician(
    search: Optional[str] = Query(
        None, description="Search term to filter patients by name or email."
    ),
    sort_by: Optional[str] = Query(
        "last_name",
        description="Field to sort by ('last_name', 'first_name', 'email', 'created_at'). Defaults to 'last_name'.",
    ),
    sort_order: SortOrder = Query(
        SortOrder.ASC, description="Sort order ('asc' or 'desc')."
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> PaginatedPatientListResponse:  # Changed return type hint
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    logger.debug(
        f"Clinician {current_clinician.clerk_id} (DB ID: {clinician_id}) requesting patient list. "
        f"Search: {search}, Sort: {sort_by} {sort_order.value}, Page: skip={skip}, limit={limit}"
    )

    allowed_sort_fields = ["last_name", "first_name", "email", "created_at"]
    if sort_by not in allowed_sort_fields:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid sort_by field. Allowed fields are: {', '.join(allowed_sort_fields)}",
        )

    # Fetch the paginated list of patients
    patients = crud.clinician.get_patients_for_clinician(
        db=db,
        clinician_id=clinician_id,
        skip=skip,
        limit=limit,
        search=search,  # Pass search term
        sort_by=sort_by,  # Pass sort field
        sort_order=sort_order.value,  # Pass sort order
    )

    # Fetch the total count of patients matching the criteria
    total_patients = crud.clinician.get_patients_count_for_clinician(
        db=db, clinician_id=clinician_id, search=search
    )

    total_pages = math.ceil(total_patients / limit) if limit > 0 else 0
    current_page = (skip // limit) + 1 if limit > 0 else 1

    logger.debug(
        f"Found {len(patients)} patients (total: {total_patients}) for clinician {clinician_id}"
    )
    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_PATIENT_LIST",
        outcome="SUCCESS",
        target_resource_type="PatientList",
        details={
            "count": len(patients),
            "total_count": total_patients,
            "page": current_page,
            "size": limit,
            "total_pages": total_pages,
            "search": search,
            "sort_by": sort_by,
            "sort_order": sort_order.value,
            "skip": skip,
            "limit": limit,
        },
    )

    # Return the paginated response object
    return PaginatedPatientListResponse(
        items=patients,
        total=total_patients,
        page=current_page,
        size=limit,
        pages=total_pages,
    )


@router.post(
    "/access-codes",
    response_model=AccessCodeResponse,
    status_code=http_status.HTTP_201_CREATED,
    summary="Generate Patient Access Code",
    description="Generates a unique, short-lived access code for patient onboarding. Requires clinician authentication.",
    responses={
        201: {"description": "Access code generated successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Failed to generate or store the access code"},
    },
    tags=["Authentication"],
)
async def create_access_code(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> AccessCodeResponse:
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    logger.debug(
        f"Clinician {current_clinician.clerk_id} (DB ID: {clinician_id}) requesting to generate an access code."
    )

    max_retries = 5
    for attempt in range(max_retries):
        try:
            plaintext_code = generate_secure_magic_code(length=10)
            logger.debug(
                f"Generated potential code (Attempt {attempt + 1}/{max_retries})"
            )
        except Exception as e:
            logger.error(f"Error during code generation: {e}", exc_info=True)
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate access code.",
            ) from e

        existing_code = crud.access_code.verify_and_get_access_code(
            db=db, plaintext_code=plaintext_code
        )
        if not existing_code:
            logger.debug("Code appears to be unique. Attempting to store.")
            try:
                expires_at = datetime.datetime.now(timezone.utc) + timedelta(hours=24)
                email = current_clinician.email

                code_in = AccessCodeCreate(
                    code=plaintext_code,
                    email=email,
                    expires_at=expires_at,
                    clinician_id=clinician_id,
                )

                _ = crud.access_code.create_access_code(db=db, code_in=code_in)
                logger.info(
                    f"Successfully generated and stored code for clinician {clinician_id}."
                )

                log_audit_event(
                    db=db,
                    actor_user_id=str(current_clinician.clerk_id),
                    actor_role="clinician",
                    action="CREATE_ACCESS_CODE",
                    outcome="SUCCESS",
                    target_resource_type="AccessCode",
                    details={"expires_at": expires_at.isoformat()},
                )
                return AccessCodeResponse(access_code=plaintext_code)
            except AccessCodeError as e:
                logger.warning(f"Failed to store code (possibly race condition): {e}")
            except Exception as e:
                logger.error(f"Unexpected error storing code: {e}", exc_info=True)
                raise HTTPException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to store access code.",
                ) from e
        else:
            logger.warning("Code collision detected. Retrying...")

    logger.error(f"Failed to generate a unique code after {max_retries} attempts.")
    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="CREATE_ACCESS_CODE",
        outcome="FAILURE",
        target_resource_type="AccessCode",
        details={"reason": f"Failed after {max_retries} attempts"},
    )
    raise HTTPException(
        status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to generate a unique access code after multiple attempts.",
    )


@router.get(
    "/me/appointments/today",
    response_model=list[AppointmentResponse],
    summary="Get Today's Appointments (Clinician)",
    description="Retrieves a list of appointments scheduled for the current day for the authenticated clinician.",
    responses={
        200: {"description": "Appointments retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        404: {"description": "Clinician record not found"},
    },
    tags=["Appointments"],
)
async def read_todays_appointments(
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[AppointmentResponse]:
    """
    Retrieve today's appointments for the authenticated clinician.
    """
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    logger.debug(
        f"Clinician {current_clinician.clerk_id} (DB ID: {clinician_id}) requesting today's appointments. Page: skip={skip}, limit={limit}"
    )

    appointments = crud.appointment.get_todays_appointments_by_clinician(
        db=db,
        clinician_id=clinician_id,
        skip=skip,
        limit=limit,
    )

    logger.debug(
        f"Found {len(appointments)} appointments for today for clinician {clinician_id}"
    )

    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_TODAYS_APPOINTMENTS",
        outcome="SUCCESS",
        target_resource_type="Appointment",
        details={
            "count": len(appointments),
            "date": datetime.datetime.now(timezone.utc).date().isoformat(),
            "skip": skip,
            "limit": limit,
        },
    )
    return appointments


@router.get(
    "/me/clinics",
    response_model=list[ClinicRead],
    summary="Get My Associated Clinics",
    description="Retrieves a list of clinics the authenticated clinician is associated with.",
    responses={
        200: {"description": "Clinics retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        404: {"description": "Clinician record not found"},
    },
    tags=["Clinicians", "RBAC"],
)
async def read_my_clinics(
    clinics: list[ClinicRead] = Depends(deps.get_clinician_clinics),
) -> list[ClinicRead]:
    """
    Retrieve the list of clinics associated with the currently authenticated clinician.
    The dependency handles authentication, fetching the clinician, and retrieving clinics.
    """
    logger.info(
        f"Returning {len(clinics)} associated clinics for the authenticated clinician."
    )
    return clinics


@router.post(
    "/patients/invitations",
    response_model=PatientInvitationResponse,
    status_code=http_status.HTTP_201_CREATED,
    summary="Invite a Patient",
    description="Creates a Clerk invitation for a patient, associating them with the inviting clinician and their primary clinic.",
    responses={
        201: {"description": "Patient invitation created successfully"},
        400: {"description": "Invalid request or Clerk API error"},
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or clinician has no associated clinic"
        },
        404: {"description": "Clinician record not found"},
        500: {"description": "Internal server error"},
    },
    tags=["Patients", "Invitations"],
)
async def invite_patient(
    *,
    invitation_in: PatientInvitationRequest,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> PatientInvitationResponse:
    """
    Sends a Clerk invitation to the specified email address.
    The invitation metadata will include the inviting clinician's ID and their primary clinic ID.
    """
    logger.info(
        f"Clinician {current_clinician.clerk_id} attempting to invite patient: {invitation_in.email}"
    )

    # 1. Get Clinician's DB record
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician_db:
        logger.error(
            f"Clinician record not found for Clerk ID {current_clinician.clerk_id} during patient invite."
        )
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found.",
        )
    clinician_db_id = (
        clinician_db.id
    )  # Internal DB ID if needed, but Clerk ID is primary

    # 2. Get Clinician's associated Clinic ID
    clinics = crud.clinician.get_clinics_for_clinician(
        db=db, clinician_id=clinician_db_id
    )
    if not clinics:
        logger.error(
            f"Clinician {current_clinician.clerk_id} (DB ID: {clinician_db_id}) has no associated clinics. Cannot invite patient."
        )
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="Clinician is not associated with any clinic.",
        )
    # Assuming the first clinic is the primary one for the invitation context
    clinic_id = clinics[0].id
    logger.debug(f"Clinician {current_clinician.clerk_id} associated with clinic {clinic_id}")

    # 3. Prepare Clerk Invitation
    redirect_url = f"{settings.FRONTEND_URL_PATIENT}/accept-invitation"  # Use configured patient frontend URL
    public_metadata = {
        "role": ["patient"],  # Role should be an array
        "inviter_user_id": current_clinician.clerk_id,
        "associated_clinic_id": str(clinic_id),
    }

    try:
        logger.debug(
            f"Creating Clerk invitation for {invitation_in.email} with metadata: {public_metadata}"
        )
        invitation = clerk.invitations.create(
            email_address=invitation_in.email,
            public_metadata=public_metadata,
            redirect_url=redirect_url,
        )
        logger.info(
            f"Successfully created Clerk invitation {invitation.id} for {invitation_in.email}"
        )

        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="INVITE_PATIENT",
            outcome="SUCCESS",
            target_resource_type="PatientInvitation",
            target_resource_id=invitation.id,
            details={
                "invited_email": invitation_in.email,
                "clinic_id": str(clinic_id),
                "clerk_invitation_status": invitation.status,
            },
        )

        return PatientInvitationResponse(
            invitation_id=invitation.id,
            email=invitation.email_address,
            status=invitation.status,
        )

    except clerk_models.SDKError as e:  # Catch the correct SDKError
        logger.error(
            f"Clerk API error inviting patient {invitation_in.email}: {e}",
            exc_info=True,
        )
        # Extract more specific details if possible from e.errors or e.status/detail
        error_detail = (
            f"Failed to create patient invitation. Clerk Error: {e.status} - {e.detail}"
        )
        if e.errors:
            # Try to get the first detailed error message
            first_error = e.errors[0]
            error_detail = f"Failed to create patient invitation: {first_error.get('long_message', first_error.get('message', 'Unknown Clerk Error'))}"

        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="INVITE_PATIENT",
            outcome="FAILURE",
            target_resource_type="PatientInvitation",
            details={
                "invited_email": invitation_in.email,
                "clinic_id": str(clinic_id),
                "error": str(e),
                "clerk_status": e.status,
                "clerk_detail": e.detail,
            },
        )
        raise HTTPException(
            status_code=e.status if e.status else http_status.HTTP_400_BAD_REQUEST,
            detail=error_detail,
        ) from e
    except Exception as e:
        logger.error(
            f"Unexpected error inviting patient {invitation_in.email}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="INVITE_PATIENT",
            outcome="FAILURE",
            target_resource_type="PatientInvitation",
            details={
                "invited_email": invitation_in.email,
                "clinic_id": str(clinic_id),
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while creating the patient invitation.",
        ) from e


# --- Patient Invitation Listing and Revocation ---


@router.get(
    "/invitations",
    response_model=PaginatedClerkInvitationListResponse,
    summary="List Patient Invitations Sent by Clinician",
    description="""Retrieves a paginated list of patient invitations sent by the currently authenticated clinician.
    Supports filtering by status.""",
    responses={
        200: {"description": "Invitations retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Failed to retrieve invitations from Clerk"},
    },
    tags=["Invitations", "Clinicians"],
)
async def list_patient_invitations_by_clinician(
    status: Optional[clerk_models.InvitationStatus] = Query(
        None,
        description="Filter invitations by status (e.g., pending, accepted, revoked).",
    ),
    skip: int = Query(
        0,
        ge=0,
        description="Number of records to skip (offset). Clerk uses offset, not page number.",
    ),
    limit: int = Query(
        10,
        ge=1,
        le=50,
        description="Maximum number of records to return per page (limit).",
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> PaginatedClerkInvitationListResponse:
    """
    Retrieves patient invitations sent by the current clinician from Clerk.
    Note: Clerk API uses offset/limit for pagination.
    """
    logger.debug(
        f"Clinician {current_clinician.clerk_id} requesting their sent patient invitations. Status filter: {status}, Offset: {skip}, Limit: {limit}"
    )

    try:
        # Use the Clerk SDK to list invitations
        # IMPORTANT: Filter by inviter_user_id to only get invitations sent by this clinician
        invitations_response = clerk.invitations.list(  # Removed await, SDK method is likely synchronous
            status=status,
            limit=limit,
            offset=skip,
            # inviter_user_id=current_clinician.clerk_id # TODO: Check if Clerk SDK supports filtering by inviter_user_id directly.
            # If not, manual filtering is needed post-fetch.
        )

        # Manual filtering: Clerk SDK doesn't support filtering by inviter_user_id directly.
        # Filter for invitations sent BY this clinician AND intended for the 'patient' role.
        logger.debug(
            f"Filtering invitations for current_clinician.clerk_id: {current_clinician.clerk_id}"
        )
        logger.debug(f"Raw invitations received from Clerk: {invitations_response}")

        filtered_invitations = []
        for inv in invitations_response:
            metadata = getattr(inv, "public_metadata", {})
            inviter_id = metadata.get(
                "inviter_user_id"
            )  # Check metadata instead of direct attribute
            roles = metadata.get("role", [])

            if inviter_id == current_clinician.clerk_id and "patient" in roles:
                filtered_invitations.append(inv)
                logger.debug(
                    f"Invitation ID {inv.id} MATCHED filter (inviter_id == current_clinician.clerk_id), "
                    f"Role Match: {'patient' in roles})."
                )
            else:
                logger.debug(
                    f"Invitation ID {inv.id} DID NOT match filter (Inviter Match: {inviter_id == current_clinician.clerk_id}, "
                    f"Role Match: {'patient' in roles})."
                )

        # Note: Clerk's total_count in the response might not reflect this manual filtering.
        # We will return the count of the *filtered* list for accuracy in our context.
        total_count = len(filtered_invitations)

        logger.debug(
            f"Retrieved {len(filtered_invitations)} invitations for clinician "
            f"{current_clinician.clerk_id} (Total matching filter: {total_count})."
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="LIST_PATIENT_INVITATIONS",
            outcome="SUCCESS",
            target_resource_type="InvitationList",
            details={
                "count": len(filtered_invitations),
                "total_count_filtered": total_count,
                "status_filter": status.value if status else None,
                "offset": skip,
                "limit": limit,
            },
        )

        # Adapt to PaginatedClerkInvitationListResponse schema
        return PaginatedClerkInvitationListResponse(
            invitations=filtered_invitations,
            total_count=total_count,  # Use the filtered count
        )

    except clerk_models.SDKError as e:  # Use the imported SDKError
        logger.error(
            f"Clerk API error listing invitations for clinician {current_clinician.clerk_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="LIST_PATIENT_INVITATIONS",
            outcome="FAILURE",
            details={"reason": f"Clerk API error: {e.status} {e.body}"},
        )
        raise HTTPException(
            status_code=e.status if e.status else 500,
            detail=f"Failed to retrieve invitations from Clerk: {e.body}",
        ) from e
    except Exception as e:
        logger.error(
            f"Unexpected error listing invitations for clinician {current_clinician.clerk_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="LIST_PATIENT_INVITATIONS",
            outcome="FAILURE",
            details={"reason": f"Unexpected error: {e}"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving invitations.",
        ) from e


@router.delete(
    "/invitations/{invitationId}",
    status_code=http_status.HTTP_204_NO_CONTENT,
    summary="Revoke Patient Invitation",
    description="Revokes a specific patient invitation sent by the currently authenticated clinician.",
    responses={
        204: {"description": "Invitation revoked successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or did not send this invitation"},
        404: {"description": "Invitation not found"},
        500: {"description": "Failed to revoke invitation via Clerk"},
    },
    tags=["Invitations", "Clinicians"],
)
async def revoke_patient_invitation_by_clinician(
    invitationId: str = Path(..., title="The ID of the invitation to revoke"),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
):
    """
    Revokes a patient invitation sent by the current clinician.
    """
    logger.debug(
        f"Clinician {current_clinician.clerk_id} attempting to revoke patient invitation {invitationId}"
    )

    try:
        # **Security Check**: First, retrieve the invitation to verify the inviter
        invitation_to_revoke = await clerk.invitations.get_invitation(
            invitation_id=invitationId
        )  # Use await

        # Verify the invitation exists and was sent by the current clinician for a patient
        inviter_id_from_meta = invitation_to_revoke.public_metadata.get(
            "inviter_user_id"
        )
        invitee_role_from_meta = invitation_to_revoke.public_metadata.get(
            "invitee_role"
        )

        if (
            inviter_id_from_meta != current_clinician.clerk_id
            or invitee_role_from_meta != "patient"
        ):
            logger.warning(
                f"Forbidden: Clinician {current_clinician.clerk_id} attempted to revoke invitation "
                f"{invitationId} (Inviter: {inviter_id_from_meta}, Role: {invitee_role_from_meta})."
            )
            log_audit_event(
                db=db,
                actor_user_id=str(current_clinician.clerk_id),
                actor_role="clinician",
                action="REVOKE_PATIENT_INVITATION",
                outcome="FAILURE",
                target_resource_type="Invitation",
                target_resource_id=invitationId,
                details={
                    "reason": "Forbidden - Invitation not sent by this clinician or not for a patient"
                },
            )
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="You can only revoke patient invitations that you sent.",
            )

        # If checks pass, proceed with revocation
        await clerk.invitations.revoke_invitation(
            invitation_id=invitationId
        )  # Use await

        logger.info(
            f"Successfully revoked patient invitation {invitationId} by clinician {current_clinician.clerk_id}."
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="REVOKE_PATIENT_INVITATION",
            outcome="SUCCESS",
            target_resource_type="Invitation",
            target_resource_id=invitationId,
        )
        # No content returned on successful DELETE

    except clerk_models.SDKError as e:  # Catch Clerk SDK errors
        logger.warning(  # Log as warning initially, might be expected (e.g., 404)
            f"Clerk SDK error while revoking invitation {invitationId} by {current_clinician.clerk_id}: {e}",
            exc_info=False,  # Don't need full traceback for potentially expected errors
        )

        # Default error details
        error_detail = f"Failed to revoke invitation via Clerk: {e}"
        status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
        audit_outcome = "FAILURE"

        # Check for specific Clerk error codes within the SDKError's errors list
        # Mimic the structure used in admin.py revoke_invitation
        if e.errors and isinstance(e.errors, list) and len(e.errors) > 0:
            first_error = e.errors[0]
            if isinstance(first_error, dict):
                error_code = first_error.get("code")
                error_message = first_error.get("message")

                if (
                    error_code == "resource_not_found"
                ):  # Check for Clerk's specific not found code
                    status_code = http_status.HTTP_404_NOT_FOUND
                    error_detail = (
                        error_message
                        or f"Invitation with ID '{invitationId}' not found."
                    )
                    audit_outcome = "FAILURE_NOT_FOUND"
                elif error_code == "invitation_already_accepted":
                    status_code = http_status.HTTP_400_BAD_REQUEST
                    error_detail = (
                        error_message
                        or "Invitation has already been accepted and cannot be revoked."
                    )
                    audit_outcome = (
                        "FAILURE_ACCEPTED"  # More specific outcome for audit
                    )
                # Add other specific error code checks here if needed

        # Log the failure with the determined outcome
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="REVOKE_PATIENT_INVITATION",
            outcome=audit_outcome,
            target_resource_type="Invitation",
            target_resource_id=invitationId,
            details={"error": str(e)},  # Log the raw error from Clerk
        )

        raise HTTPException(status_code=status_code, detail=error_detail) from e
    except Exception as e:
        logger.error(
            f"Unexpected error revoking invitation {invitationId} for clinician {current_clinician.clerk_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="REVOKE_PATIENT_INVITATION",
            outcome="FAILURE",
            target_resource_type="Invitation",
            target_resource_id=invitationId,
            details={"reason": f"Unexpected error: {e}"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while revoking the invitation.",
        ) from e


@router.post(
    "/patients/invite",
    response_model=PatientInvitationResponse,
    summary="Send Patient Invitation (Clinician)",
    description="Sends an invitation email to a potential patient. The invitation includes a secure link for registration.",
    responses={
        201: {"description": "Invitation sent successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        409: {"description": "Email already registered"},
    },
    tags=["Clinicians"],
)
async def create_patient_invitation(
    *,
    invitation_request: PatientInvitationRequest,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> PatientInvitationResponse:
    """
    Create and send a patient invitation.
    """
    # Get clinician's clinic ID
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician_db or not clinician_db.clinic_id:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="Clinician must be associated with a clinic to send invitations",
        )

    logger.info(
        f"Clinician {current_clinician.clerk_id} creating invitation for {invitation_request.email} from clinic {clinician_db.clinic_id}"
    )

    try:
        # Create invitation via Clerk
        invitation = clerk.invitations.create_invitation(
            email_address=invitation_request.email,
            public_metadata={
                "role": ["patient"],
                "inviter_user_id": current_clinician.clerk_id,
                "associated_clinic_id": str(
                    clinician_db.clinic_id
                ),  # Convert UUID to string
            },
            redirect_url=f"{settings.FRONTEND_URL_PATIENT}/accept-invitation",  # Use configured patient frontend URL
        )

        log_audit_event(
            db=db,
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            action="CREATE_PATIENT_INVITATION",
            outcome="SUCCESS",
            target_resource_type="Invitation",
            target_resource_id=invitation.id,
            details={
                "invited_email": invitation_request.email,
                "clinic_id": str(clinician_db.clinic_id),
            },
        )

        return PatientInvitationResponse(
            id=invitation.id,
            email=invitation.email_address,
            status=invitation.status,
            created_at=datetime.datetime.fromtimestamp(
                invitation.created_at / 1000, tz=timezone.utc
            ),
        )

    except Exception as e:
        logger.error(
            f"Error creating invitation for {invitation_request.email}: {str(e)}"
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create invitation",
        )


@router.get(
    "/patients/{patient_id}/side-effects", response_model=list[SideEffectReportResponse]
)
def get_patient_side_effects(
    patient_id: str,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    sort_by: str = Query("reported_at:desc"),
    current_user: TokenPayload = Depends(deps.get_current_clinician),
    db: Session = Depends(deps.get_db),
) -> list[SideEffectReportResponse]:
    """Get side effect reports for a specific patient."""

    field, order = validate_sort_params(
        sort_by=sort_by,
        allowed_fields={"reported_at", "severity"},
        default_field="reported_at",
        default_order="desc",
    )

    # Verify clinician has access to this patient
    if not crud.clinician.is_patient_assigned_to_clinician(
        db=db, clinician_id=current_user.clerk_id, patient_id=patient_id
    ):
        raise HTTPException(
            status_code=403, detail="Not authorized to access this patient's data"
        )

    try:
        reports = crud.side_effect_report.get_by_patient(
            db=db,
            patient_id=patient_id,
            skip=offset,
            limit=limit,
            sort_by=field,
            sort_desc=order == "desc",
        )
        return reports
    except Exception:
        logger.error(
            f"Error fetching side effects for patient {patient_id}", exc_info=True
        )
        raise HTTPException(
            status_code=500, detail="Failed to fetch side effect reports"
        )


@router.get(
    "/me",
    response_model=ClinicianResponse,
    summary="Get Current Clinician Profile",
    description="Retrieves the profile information for the currently authenticated clinician.",
    responses={
        200: {"description": "Clinician profile retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        404: {"description": "Clinician record not found"},
    },
    tags=["Clinicians"],
)
async def read_current_clinician_profile(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> ClinicianResponse:
    logger.debug(f"Clinician {current_clinician.clerk_id} requesting their own profile")

    clinician = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician:
        logger.error(f"Clinician record not found for {current_clinician.clerk_id}")
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found.",
        )

    logger.debug(f"Found profile data for clinician {current_clinician.clerk_id}")
    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="GET_OWN_PROFILE",
        outcome="SUCCESS",
        target_resource_type="Clinician",
        target_resource_id=str(current_clinician.clerk_id),
        details={"accessed_fields": "full_profile"},
    )
    return clinician


@router.get(
    "/profile-photos/{clinician_id}/{filename}",
    response_class=Response,  # Use Response class for binary data
    summary="Get Clinician Profile Photo",
    description="Retrieves the profile photo for a specific clinician by filename. Requires authentication.",
    responses={
        200: {
            "description": "Clinician profile photo retrieved successfully",
            "content": {"image/*": {}},  # Indicate image content type
        },
        401: {"description": "Not authenticated"},
        403: {"description": "Forbidden (future use, e.g., admin access)"},
        404: {"description": "Clinician or photo not found"},
    },
    tags=["Clinicians"],
)
async def get_clinician_profile_photo(
    clinician_id: str = Path(
        ..., description="The Clerk User ID of the clinician"
    ),  # Changed type hint to str
    filename: str = Path(..., description="The filename of the photo"),
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(
        deps.get_current_active_user
    ),  # Basic auth check
) -> Response:
    """
    Fetches a clinician's profile photo blob from storage via a proxy.
    """
    logger.debug(
        f"User {current_user.sub} requesting profile photo '{filename}' for clinician {clinician_id}"
    )

    # Assume crud.clinician.get_profile_photo_blob exists and returns BytesIO or similar
    # It should handle fetching from Azure based on clinician_id and filename stored in DB
    # Correctly call the method on the instantiated CRUDClinician class instance
    blob_content: Optional[IO[bytes]] = crud.clinician.clinician.get_profile_photo_blob(
        db=db, clinician_id=clinician_id, filename=filename
    )

    if blob_content is None:
        logger.warning(
            f"Profile photo '{filename}' not found for clinician {clinician_id}"
        )
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Profile photo not found.",
        )

    media_type, _ = mimetypes.guess_type(filename)
    if media_type is None:
        media_type = "application/octet-stream"  # Default if type cannot be guessed

    # Use StreamingResponse for efficient handling of potentially large files
    return StreamingResponse(blob_content, media_type=media_type)


@router.post(
    "/medication-requests/{request_id}/approve",
    response_model=MedicationRequestResponse,
    summary="Approve Medication Request",
    description="Approves a medication request and updates its status.",
    responses={
        200: {"description": "Medication request approved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized"},
        404: {"description": "Medication request not found"},
    },
    tags=["Medication"],
)
async def approve_medication_request(
    request_id: UUID,
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> MedicationRequestResponse:
    """
    Approve a medication request.
    """
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    # Get the medication request
    medication_request = crud.medication_request.get_medication_request_by_id(
        db, request_id=request_id
    )
    if not medication_request:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Medication request not found",
        )

    # Verify the clinician is assigned to the patient
    assigned_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_id, limit=10000
    )
    assigned_patient_ids = [p.id for p in assigned_patients]
    if medication_request.patient_id not in assigned_patient_ids:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="Not authorized to approve this request",
        )

    # Update the request
    medication_request.status = MedicationRequestStatus.APPROVED
    medication_request.clinician_id = clinician_id
    medication_request.resolved_at = datetime.datetime.now(timezone.utc)
    db.commit()
    db.refresh(medication_request)

    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="APPROVE_MEDICATION_REQUEST",
        outcome="SUCCESS",
        target_resource_type="MedicationRequest",
        target_resource_id=str(request_id),
        details={
            "previous_status": "pending",
            "new_status": "approved",
        },
    )

    return medication_request


@router.post(
    "/medication-requests/{request_id}/reject",
    response_model=MedicationRequestResponse,
    summary="Reject Medication Request",
    description="Rejects a medication request and updates its status.",
    responses={
        200: {"description": "Medication request rejected successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized"},
        404: {"description": "Medication request not found"},
    },
    tags=["Medication"],
)
async def reject_medication_request(
    request_id: UUID,
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> MedicationRequestResponse:
    """
    Reject a medication request.
    """
    # Clinician model is already available via dependency
    if not current_clinician:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )
    clinician_id = current_clinician.id

    # Get the medication request
    medication_request = crud.medication_request.get_medication_request_by_id(
        db, request_id=request_id
    )
    if not medication_request:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Medication request not found",
        )

    # Verify the clinician is assigned to the patient
    assigned_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_id, limit=10000
    )
    assigned_patient_ids = [p.id for p in assigned_patients]
    if medication_request.patient_id not in assigned_patient_ids:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="Not authorized to reject this request",
        )

    # Update the request
    medication_request.status = MedicationRequestStatus.REJECTED
    medication_request.clinician_id = clinician_id
    medication_request.resolved_at = datetime.datetime.now(timezone.utc)
    db.commit()
    db.refresh(medication_request)

    log_audit_event(
        db=db,
        actor_user_id=str(current_clinician.clerk_id),
        actor_role="clinician",
        action="REJECT_MEDICATION_REQUEST",
        outcome="SUCCESS",
        target_resource_type="MedicationRequest",
        target_resource_id=request_id,
        details={
            "previous_status": "pending",
            "new_status": "rejected",
        },
    )

    return medication_request


@router.put(
    "/me",
    response_model=ClinicianResponse,
    summary="Update Current Clinician Profile",
    description="Updates the profile information and photo for the currently authenticated clinician.",
    responses={
        200: {"description": "Profile updated successfully"},
        400: {"description": "Invalid input data or JSON parsing error"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        404: {"description": "Clinician record not found"},
        500: {"description": "Failed to upload profile photo"},
    },
    tags=["Clinicians"],
)
async def update_current_clinician_profile(
    # Replace single JSON string with individual optional form fields
    email: Optional[str] = Form(None),
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    specialty: Optional[str] = Form(None),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    profile_photo: UploadFile = File(None, description="Optional profile photo upload"),
    db: Session = Depends(deps.get_db),
) -> ClinicianResponse:
    """
    Update own clinician profile. Accepts JSON string in 'profile_update' form field and optional file upload for photo.
    """

    user_id = current_clinician.clerk_id
    logger.debug(f"Attempting to update profile for clinician_id: {user_id}")

    # Removed obsolete log line that referenced the old 'profile_update' variable
    # logger.info(f"Raw profile_update string received: {profile_update}") # Removed this line

    # Construct update dictionary from individual form fields
    update_data_dict = {}
    if email is not None:
        update_data_dict["email"] = email
    if first_name is not None:
        update_data_dict["first_name"] = first_name
    if last_name is not None:
        update_data_dict["last_name"] = last_name
    if specialty is not None:
        update_data_dict["specialty"] = specialty

    # Validate the constructed dictionary (optional but good practice)
    try:
        # Use model_validate to handle potential validation errors if needed
        # ClinicianUpdate.model_validate(update_data_dict) # Can uncomment if strict validation needed
        logger.debug(f"Received update data from form fields: {update_data_dict}")
        pass  # Keep structure similar, no exception needed if not validating here
    except (
        Exception
    ) as e:  # Catch potential validation errors if model_validate is used
        logger.error(f"Error constructing/validating profile update data: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid profile data: {str(e)}",
        ) from e

    # Get the clinician record
    clinician = crud.clinician.get_clinician_by_clerk_id(db=db, clerk_id=user_id)
    if not clinician:
        logger.warning(
            f"Clinician record not found during update for user_id: {user_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_CLINICIAN_PROFILE",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="clinician",
            target_resource_id=str(user_id),
            details={"reason": "Clinician not found"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found for the authenticated user.",
        )

    # Handle profile photo upload
    uploaded_photo_url: Optional[str] = None
    if profile_photo:
        logger.info(  # Changed to info for better visibility
            f"Received profile photo upload: {profile_photo.filename}, content_type: {profile_photo.content_type}. Attempting upload..."
        )
        try:
            uploaded_photo_url = await upload_clinician_profile_photo(
                file=profile_photo, clinician_id=str(user_id)
            )
            logger.info(
                f"Photo upload function returned URL: {uploaded_photo_url}"
            )  # Log the result
            if not uploaded_photo_url:
                logger.error(f"Azure photo upload failed for clinician: {user_id}")
                log_audit_event(
                    db=db,
                    action="UPDATE_CLINICIAN_PROFILE_PHOTO",
                    outcome="FAILURE",
                    actor_user_id=str(user_id),
                    actor_role="clinician",
                    target_resource_id=str(user_id),
                    details={"reason": "Upload function returned None"},
                )
            else:
                logger.info(
                    f"Successfully uploaded photo to Azure. URL: {uploaded_photo_url}"
                )
                log_audit_event(
                    db=db,
                    action="UPDATE_CLINICIAN_PROFILE_PHOTO",
                    outcome="SUCCESS",
                    actor_user_id=str(user_id),
                    actor_role="clinician",
                    target_resource_id=str(user_id),
                    details={
                        "filename": profile_photo.filename,
                        "content_type": profile_photo.content_type,
                        "photo_url": uploaded_photo_url,
                    },
                )
        except Exception as e:
            logger.error(
                f"Error during Azure photo upload for clinician {user_id}: {e}",
                exc_info=True,
            )
            log_audit_event(
                db=db,
                action="UPDATE_CLINICIAN_PROFILE_PHOTO",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="clinician",
                target_resource_id=str(user_id),
                details={"reason": f"Unexpected upload error: {e}"},
            )

    # Add the photo URL to the update data if upload was successful
    if uploaded_photo_url:
        logger.info(f"Adding photo_url to update data: {uploaded_photo_url}")
        update_data_dict["photo_url"] = uploaded_photo_url

    # If no update data (neither profile fields nor photo), just return current data
    if not update_data_dict:
        logger.info(f"No update data provided for clinician {user_id}.")
        return clinician

    # Update the clinician record
    logger.info(
        f"Attempting DB update for clinician {user_id} with data: {update_data_dict}"
    )  # Log data before DB update
    try:
        # Correctly call the update method on the instantiated CRUDClinician class instance
        updated_clinician = crud.clinician.clinician.update(
            db=db, db_obj=clinician, obj_in=update_data_dict
        )

        logger.info(
            f"Successfully updated profile data for clinician: {updated_clinician.email}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_CLINICIAN_PROFILE",
            outcome="SUCCESS",
            actor_user_id=str(user_id),
            actor_role="clinician",
            target_resource_id=str(user_id),
            details={"updated_fields": list(update_data_dict.keys())},
        )
        return updated_clinician

    except Exception as e:
        db.rollback()
        logger.error(
            f"Error updating clinician profile in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_CLINICIAN_PROFILE",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="clinician",
            target_resource_id=str(user_id),
            details={
                "reason": f"DB update error: {e}",
                "update_data": update_data_dict,
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile information.",
        ) from e


# --- Clinician Chat Endpoints ---


@router.get(
    "/me/chat",
    response_model=dict,
    summary="Clinician Chat History Endpoint",
    description="Get recent chat messages for a clinician.",
    responses={
        200: {"description": "Chat history retrieved successfully"},
        404: {"description": "User not found"},
        500: {"description": "Server error"},
    },
    tags=["Clinicians"],
)
async def get_clinician_chat(
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of items to return"),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
):
    """
    Get recent chat messages for a clinician.

    Returns a paginated list of chat messages with their responses.
    """
    try:
        # Get the clinician object using the correct method
        clinician = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_clinician.clerk_id
        )
        if not clinician:
            logger.error(f"Clinician not found: {current_clinician.clerk_id}")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND, detail="Clinician not found"
            )

        # Log the chat history retrieval for audit purposes
        log_audit_event(
            db=db,
            action="GET_CLINICIAN_CHAT_HISTORY",
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            target_resource_type="clinician",
            target_resource_id=str(current_clinician.clerk_id),
            outcome="SUCCESS",
            details={"skip": skip, "limit": limit},
        )

        # Return a default empty response for now (to be implemented with actual history)
        return {"messages": [], "total": 0, "has_more": False}

    except Exception as e:
        logger.error(f"Error retrieving clinician chat history: {e}", exc_info=True)
        log_audit_event(
            db=db,
            action="GET_CLINICIAN_CHAT_HISTORY",
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            target_resource_type="clinician",
            target_resource_id=str(current_clinician.clerk_id),
            outcome="FAILURE",
            details={"error": str(e), "skip": skip, "limit": limit},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving chat history: {str(e)}",
        )


@router.post(
    "/me/chat",
    response_model=dict,
    summary="Clinician Chat Endpoint",
    description="Process chat messages for clinicians, including command-like messages that can be processed as API actions.",
    responses={
        200: {"description": "Message processed successfully"},
        404: {"description": "User not found"},
        500: {"description": "Server error"},
    },
    tags=["Clinicians"],
)
async def clinician_chat(
    message: dict,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
):
    """
    Process a chat message from a clinician with LLM Action support.

    This endpoint bridges the clinician UI with the chat system, forwarding
    messages to the appropriate backend endpoints based on the message content.
    Messages may be processed as regular chat messages or as LLM-driven API actions.
    """
    try:
        logger.info(
            f"Received chat message from clinician: {current_clinician.clerk_id} (message length: {len(message.get('message', ''))})"
        )
        logger.debug(f"Message content: {message}")

        # Get the clinician object
        clinician = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_clinician.clerk_id
        )
        if not clinician:
            logger.error(f"Clinician not found: {current_clinician.clerk_id}")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND, detail="Clinician not found"
            )

        # Log the incoming message for audit purposes
        log_audit_event(
            db=db,
            action="CLINICIAN_CHAT_MESSAGE",
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            target_resource_type="clinician",
            target_resource_id=str(current_clinician.clerk_id),
            outcome="SUCCESS",
            details={"message_length": len(message.get("message", ""))},
        )

        # Extract message content with proper fallback
        user_message = message.get("message", "")
        if not user_message:
            logger.warning("Empty message received")
            return {
                "response": "I didn't receive any message. Please try again.",
                "timestamp": datetime.datetime.now(timezone.utc).isoformat(),
                "metadata": {},
            }

        # Process the message through the chat agent
        from app.services.chat_agent import process_chat_message

        # Extract context from the message
        context = message.get("context", {})

        # Log the context for debugging
        logger.debug(f"Context from frontend: {context}")

        # Call process_chat_message with the Clerk ID for the clinician
        response = await process_chat_message(
            user_id=current_clinician.clerk_id,  # Use the string Clerk ID as-is
            user_message=user_message,
            db=db,
            structured_output=message.get("structured_output", False),
            output_schema=message.get("output_schema", None),
            context=context,  # Pass the context from the frontend
        )

        # Return the response in the expected format
        if isinstance(response, dict):
            # If it's already a dict (structured output), return as is
            return response
        else:
            # If it's a string (simple response), wrap it in a dict
            return {
                "response": response,
                "timestamp": datetime.datetime.now(timezone.utc).isoformat(),
                "metadata": {},
            }

    except Exception as e:
        logger.error(f"Error processing clinician chat message: {e}", exc_info=True)
        log_audit_event(
            db=db,
            action="CLINICIAN_CHAT_MESSAGE",
            actor_user_id=str(current_clinician.clerk_id),
            actor_role="clinician",
            target_resource_type="clinician",
            target_resource_id=str(current_clinician.clerk_id),
            outcome="FAILURE",
            details={
                "error": str(e),
                "message_length": len(message.get("message", "")),
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing message: {str(e)}",
        )


@router.get(
    "/clinic/{clinic_id}/appointment-requests",
    response_model=PaginatedAppointmentRequestResponse,
    summary="Get Pending Appointment Requests for Clinic",
    description="Retrieves pending appointment requests for patients in the specified clinic.",
    responses={
        200: {"description": "Appointment requests retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to access this clinic"},
        404: {"description": "Clinic not found"},
    },
    tags=["Appointments"],
)
def read_clinic_appointment_requests(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    clinic_id: UUID,
    skip: int = 0,
    limit: int = 20,
) -> PaginatedAppointmentRequestResponse:
    """
    Retrieve pending appointment requests for a clinic.
    """
    # Verify the clinician has access to this clinic
    clinician = crud.clinician.get_clinician_by_clerk_id(
        db=db, clerk_id=current_clinician.clerk_id
    )
    if not clinician:
        raise HTTPException(status_code=404, detail="Clinician not found")

    if not crud.clinician.is_associated_with_clinic(
        db=db, clinician_id=clinician.id, clinic_id=clinic_id
    ):
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access appointment requests for this clinic",
        )

    # Get requests with relation data loaded
    requests = crud.appointment_request.get_pending_for_clinic(
        db=db, clinic_id=clinic_id, skip=skip, limit=limit
    )

    # Count total for pagination
    total = len(requests)  # For simplicity we're not doing a separate count query

    # Calculate pagination values
    pages = (total + limit - 1) // limit if limit > 0 else 1
    page = (skip // limit) + 1 if limit > 0 else 1

    return PaginatedAppointmentRequestResponse(
        items=requests, total=total, page=page, size=limit, pages=pages
    )


@router.put(
    "/appointment-requests/{appointment_request_id}/status",
    response_model=AppointmentRequestResponse,
    summary="Update Appointment Request Status",
    description="Updates the status of an appointment request (approve, reject, etc.).",
    responses={
        200: {"description": "Appointment request status updated successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to update this request"},
        404: {"description": "Appointment request not found"},
        422: {"description": "Validation error"},
    },
    tags=["Appointments"],
)
def update_appointment_request_status(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    appointment_request_id: UUID,
    status_update: AppointmentRequestStatusUpdate,
) -> AppointmentRequestResponse:
    """
    Update the status of an appointment request.
    """
    # Get the appointment request
    appointment_request = crud.appointment_request.get_by_id_with_relations(
        db=db, id=appointment_request_id
    )
    if not appointment_request:
        raise HTTPException(status_code=404, detail="Appointment request not found")

    # Verify clinician has access to this patient's clinic
    clinician = crud.clinician.get_clinician_by_clerk_id(
        db=db, clerk_id=current_clinician.clerk_id
    )
    if not clinician:
        raise HTTPException(status_code=404, detail="Clinician not found")

    patient = appointment_request.patient
    if not patient.associated_clinic_id:
        raise HTTPException(
            status_code=403, detail="Patient not associated with any clinic"
        )

    if not crud.clinician.is_associated_with_clinic(
        db=db, clinician_id=clinician.id, clinic_id=patient.associated_clinic_id
    ):
        raise HTTPException(
            status_code=403,
            detail="Not authorized to update appointment requests for this patient's clinic",
        )

    # Set the reviewer ID
    status_update.reviewed_by_id = clinician.id

    # Update the status
    updated_request = crud.appointment_request.update_status(
        db=db, db_obj=appointment_request, obj_in=status_update
    )

    # Get the updated record with related entities loaded
    result = crud.appointment_request.get_by_id_with_relations(
        db=db, id=updated_request.id
    )

    # Log the action
    log_audit_event(
        db=db,
        actor_user_id=current_clinician.clerk_id,
        actor_role="clinician",
        action="appointment_request.status_update",
        outcome="SUCCESS",
        target_resource_type="appointment_request",
        target_resource_id=str(appointment_request_id),
        details={"new_status": status_update.status},
    )

    return result


@router.post(
    "/me/photo",
    response_model=dict,
    summary="Upload Clinician Profile Photo",
    description="Upload a new profile photo for the currently authenticated clinician.",
    responses={
        200: {"description": "Profile photo uploaded successfully"},
        400: {"description": "Invalid file type or size"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Failed to upload profile photo"},
    },
    tags=["Clinicians"],
)
async def upload_clinician_photo(
    *,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    photo: UploadFile = File(..., description="Profile photo to upload (max 5MB)"),
):
    """
    Upload a new profile photo for the currently authenticated clinician.
    
    Accepts image files up to 5MB in size. Supported formats: JPEG, PNG, WebP.
    """
    try:
        # Get the clinician object
        clinician = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_clinician.clerk_id
        )
        if not clinician:
            logger.error(f"Clinician not found: {current_clinician.clerk_id}")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician not found"
            )

        # Validate file type and size
        if not photo.content_type or not photo.content_type.startswith('image/'):
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="File must be an image (JPEG, PNG, WebP, etc.)"
            )

        # Check file size (5MB limit)
        MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB in bytes
        if hasattr(photo, 'size') and photo.size and photo.size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="File size must be less than 5MB"
            )

        logger.info(
            f"Uploading profile photo for clinician {clinician.id}: {photo.filename}, "
            f"content_type: {photo.content_type}"
        )

        # Upload to Azure storage
        uploaded_photo_url = await upload_clinician_profile_photo(
            file=photo, clinician_id=str(clinician.id)
        )

        if not uploaded_photo_url:
            logger.error(f"Azure photo upload failed for clinician: {clinician.id}")
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload profile photo"
            )

        logger.info(f"Successfully uploaded photo to Azure. URL: {uploaded_photo_url}")

        # Update the clinician record with the new photo URL
        updated_clinician = crud.clinician.update(
            db=db,
            db_obj=clinician,
            obj_in={"photo_url": uploaded_photo_url}
        )

        # Log the action
        log_audit_event(
            db=db,
            action="UPLOAD_CLINICIAN_PHOTO",
            outcome="SUCCESS",
            actor_user_id=str(clinician.id),
            actor_role="clinician",
            target_resource_id=str(clinician.id),
            details={
                "filename": photo.filename,
                "content_type": photo.content_type,
                "photo_url": uploaded_photo_url,
            },
        )

        return {
            "message": "Profile photo uploaded successfully",
            "photo_url": uploaded_photo_url,
            "filename": photo.filename
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error uploading photo for clinician {current_clinician.clerk_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPLOAD_CLINICIAN_PHOTO",
            outcome="FAILURE",
            actor_user_id=current_clinician.clerk_id,
            actor_role="clinician",
            target_resource_id=current_clinician.clerk_id,
            details={"reason": f"Upload error: {e}"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload profile photo"
        ) from e
