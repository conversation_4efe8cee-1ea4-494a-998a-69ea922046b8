import logging
import uuid
from uuid import UUID  # Import UUID

import redis.asyncio as redis
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    HTTPException,
    Query,
    Request,
    Response,  # Import Response for 204 status
    status,
)

# Use AsyncSession for async endpoints
from sqlalchemy.orm import Session  # Import Session

from app import crud  # Unified CRUD import, import models for User type hint
from app.api import deps
from app.crud.exceptions import ConflictError, NotFoundError  # Import custom exceptions
from app.schemas.audit_log import AuditLogCreate  # Import AuditLogCreate
from app.schemas.auth import TokenPayload  # Import TokenPayload
from app.schemas.clinic import (
    ClinicCreate,
    ClinicRead,
    ClinicScrapeRequest,
    ClinicUpdate,
    PaginatedClinicResponse,
)

# Import the new schemas
from app.schemas.clinic_medication_association import (
    ClinicMedicationAssociationRequest,
    ClinicMedicationAssociationResponse,  # Corrected import source
)
from app.schemas.clinic_medication_upsert import (
    ClinicMedicationUpsertRequest,  # Corrected import
)
from app.schemas.medication import (  # Import MedicationResponse and MedicationCreate
    MedicationCreate,
    MedicationResponse,
)
from app.services.web_scraper import scrape_clinic_website

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get(
    "",
    response_model=PaginatedClinicResponse,
    summary="Retrieve multiple clinics",
    dependencies=[Depends(deps.get_current_admin_user)],
)
def read_clinics(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of clinics to skip"),
    limit: int = Query(
        100, ge=1, le=200, description="Maximum number of clinics to return"
    ),
):
    """
    Retrieve a list of clinics with pagination. Requires admin privileges.
    """
    logger.info(f"API: Reading clinics (skip={skip}, limit={limit})")
    clinics = crud.clinic.get_multi(db, skip=skip, limit=limit)
    total_count = crud.clinic.get_count(db)
    return PaginatedClinicResponse(items=clinics, total=total_count)


@router.post(
    "",
    response_model=ClinicRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new clinic",
    dependencies=[Depends(deps.get_current_admin_user)],
)
def create_clinic(
    *,
    db: Session = Depends(deps.get_db),
    clinic_in: ClinicCreate,
):
    """
    Create a new clinic record. Requires admin privileges.
    """
    logger.info(f"API: Creating new clinic with name '{clinic_in.name}'")
    clinic = crud.clinic.create(db=db, obj_in=clinic_in)
    return clinic


@router.put(
    "/{clinic_id}",
    response_model=ClinicRead,
    summary="Update an existing clinic",
    dependencies=[Depends(deps.get_current_admin_user)],
)
def update_clinic(
    *,
    db: Session = Depends(deps.get_db),
    clinic_id: uuid.UUID,
    clinic_in: ClinicUpdate,
):
    """
    Update a clinic record by its ID. Requires admin privileges.
    """
    logger.info(f"API: Updating clinic with ID {clinic_id}")
    clinic = crud.clinic.get(db=db, id=clinic_id)
    if not clinic:
        logger.warning(f"API: Clinic with ID {clinic_id} not found for update.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinic not found",
        )
    updated_clinic = crud.clinic.update(db=db, db_obj=clinic, obj_in=clinic_in)
    return updated_clinic


@router.delete(
    "/{clinic_id}",
    response_model=ClinicRead,
    summary="Delete a clinic",
    dependencies=[Depends(deps.get_current_admin_user)],
)
def delete_clinic(
    *,
    db: Session = Depends(deps.get_db),
    clinic_id: uuid.UUID,
):
    """
    Delete a clinic record by its ID. Requires admin privileges.
    """
    logger.info(f"API: Deleting clinic with ID {clinic_id}")
    deleted_clinic = crud.clinic.remove(db=db, id=clinic_id)
    if not deleted_clinic:
        logger.warning(f"API: Clinic with ID {clinic_id} not found for deletion.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinic not found",
        )
    return deleted_clinic


@router.post(
    "/scrape",
    status_code=status.HTTP_200_OK,
    summary="Trigger web scraping for a clinic URL and return a task ID",
    response_description="Scraping task initiated, returns task ID for status polling",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def trigger_clinic_scrape(
    scrape_request: ClinicScrapeRequest,
    background_tasks: BackgroundTasks,
    request: Request,
):
    """
    Accepts a URL and optional clinic ID and triggers scraping in the
    background. Once scraping completes, the embedding pipeline is run for the
    same clinic. The endpoint returns immediately with a task identifier so
    processing occurs asynchronously.
    Requires admin privileges.
    """
    logger.info(
        f"API: Received scrape request for URL: {scrape_request.url}, Clinic ID: {scrape_request.clinic_id}"
    )
    redis_client: redis.Redis | None = request.app.state.redis_client
    if not redis_client:
        logger.error("API: Redis client not available for triggering scrape task.")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Redis connection not available.",
        )

    try:
        task_id = str(uuid.uuid4())
        await redis_client.setex(task_id, 3600, "PENDING")
        logger.info(
            f"API: Generated task_id {task_id} for scrape request and set status in Redis."
        )

        background_tasks.add_task(
            scrape_clinic_website,
            clinic_url=str(scrape_request.url),
            clinic_id=scrape_request.clinic_id,
            task_id=task_id,
        )
        return {
            "message": "Clinic scraping task initiated successfully.",
            "task_id": task_id,
        }
    except Exception as e:
        logger.error(f"API: Error triggering scrape task: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate scraping task.",
        )


@router.get(
    "/scrape/status/{task_id}",
    summary="Get the status of a clinic scraping task",
    response_description="Returns the current status of the scraping task",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def get_scrape_status(
    task_id: str, request: Request
):  # Add request and make async
    """
    Retrieves the status of an asynchronous clinic scraping task by its ID from Redis.
    Requires admin privileges.
    """
    logger.info(f"API: Checking status for scrape task_id: {task_id}")
    redis_client: redis.Redis | None = request.app.state.redis_client
    if not redis_client:
        logger.error("API: Redis client not available for checking scrape status.")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Redis connection not available.",
        )

    task_status = await redis_client.get(task_id)

    if task_status is None:
        logger.warning(
            f"API: Scrape task_id {task_id} not found in Redis (or expired)."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping task not found or expired",
        )

    return {"task_id": task_id, "status": task_status}


# ===============================================================================
# Clinic Medication Association Endpoint
# ===============================================================================


@router.get(
    "/{clinic_id}/medications",
    response_model=list[MedicationResponse],
    summary="List all medications associated with a clinic",
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def get_clinic_medications(
    *,
    db: Session = Depends(deps.get_db),
    clinic_id: uuid.UUID,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    List all medications associated with a clinic. Admin only.
    """
    logger.info(
        f"API: Fetching medications for clinic {clinic_id} by user {current_user.sub}"
    )
    clinic = crud.clinic.get(db=db, id=clinic_id)
    if not clinic:
        logger.warning(
            f"API: Clinic with ID {clinic_id} not found for medication list."
        )
        # Audit log failure
        crud.create_audit_log(
            db=db,
            obj_in=AuditLogCreate(
                actor_user_id=current_user.sub,
                action="GET_CLINIC_MEDICATIONS",
                actor_role="admin",
                outcome="FAILURE",
                target_resource_type="clinic",
                target_resource_id=str(clinic_id),
                details={"reason": f"Clinic not found: {clinic_id}"},
            ),
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Clinic not found"
        )
    # Eager load medications
    medications = clinic.medications
    # Audit log success
    crud.create_audit_log(
        db=db,
        obj_in=AuditLogCreate(
            actor_user_id=current_user.sub,
            action="GET_CLINIC_MEDICATIONS",
            actor_role="admin",
            outcome="SUCCESS",
            target_resource_type="clinic",
            target_resource_id=str(clinic_id),
            details={"count": len(medications)},
        ),
    )
    return [MedicationResponse.model_validate(med) for med in medications]


@router.post(
    "/{clinic_id}/medications",
    summary="Associate an existing medication with a clinic (shorthand endpoint)",
    response_model=ClinicMedicationAssociationResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def associate_medication_shorthand(
    *,
    db: Session = Depends(deps.get_db),
    clinic_id: UUID,
    association_in: ClinicMedicationAssociationRequest,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """Shorthand endpoint to associate an existing medication with a clinic.
    This endpoint exists to match frontend expectations and simply calls the
    associate_medication_with_clinic function.
    """
    # Simply delegate to the main association endpoint
    return await associate_medication_with_clinic(
        db=db,
        clinic_id=clinic_id,
        association_in=association_in,
        current_user=current_user,
    )


@router.post(
    "/{clinic_id}/medications/associate",
    summary="Associate an existing medication with a clinic",
    response_model=ClinicMedicationAssociationResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def associate_medication_with_clinic(
    *,
    db: Session = Depends(deps.get_db),  # Use regular Session
    clinic_id: UUID,
    association_in: ClinicMedicationAssociationRequest,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    Associate an existing medication with a clinic.
    """
    try:
        from app import crud

        # Check if the association already exists
        existing = crud.clinic_medication_association.get_by_clinic_and_medication(
            db, clinic_id=clinic_id, medication_id=association_in.medication_id
        )

        if existing:
            # Association already exists
            logger.info(
                f"API: Medication {association_in.medication_id} already associated with clinic {clinic_id}"
            )
            # Return the existing association
            return ClinicMedicationAssociationResponse(
                clinic_id=existing.clinic_id,
                medication_id=existing.medication_id,
                message="Medication is already associated with the clinic.",
            )

        # Create the association
        association = crud.clinic_medication_association.create_association(
            db,
            clinic_id=clinic_id,
            medication_id=association_in.medication_id,
            notes=association_in.notes if hasattr(association_in, "notes") else None,
        )

        # Log the successful association
        logger.info(
            f"API: Successfully associated medication {association_in.medication_id} with clinic {clinic_id}"
        )

        # Return the association response
        return ClinicMedicationAssociationResponse(
            clinic_id=association.clinic_id,
            medication_id=association.medication_id,
            message="Medication successfully associated with the clinic.",
        )

    except NotFoundError as e:
        logger.warning(f"API: Failed association - Not Found: {e}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ConflictError as e:
        logger.warning(f"API: Failed association - Conflict: {e}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        logger.error(
            f"API: Unexpected error associating medication {association_in.medication_id} with clinic {clinic_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while associating the medication.",
        )


@router.post(
    "/{clinic_id}/medications/upsert",
    summary="Create or update a medication and associate with a clinic (by name & category)",
    response_model=ClinicMedicationAssociationResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def upsert_and_associate_medication(
    *,
    db: Session = Depends(deps.get_db),  # Use regular Session
    clinic_id: uuid.UUID,
    upsert_in: ClinicMedicationUpsertRequest,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    If a medication with the same name and category exists, associate it (and update if already associated).
    If not, create it and associate it with the clinic.
    """
    from app import crud

    # 1. Find medication by name+category
    med = crud.medication.medication.get_by_name_and_category(
        db, name=upsert_in.name, category=upsert_in.category
    )
    if med:
        # If updating, update fields if needed
        updated = False
        for field in ["description", "dosage_guidelines", "common_side_effects"]:
            if getattr(upsert_in, field, None) and getattr(med, field, None) != getattr(
                upsert_in, field, None
            ):
                setattr(med, field, getattr(upsert_in, field))
                updated = True
        if updated:
            db.commit()
            db.refresh(med)
        # Check if already associated
        assoc = crud.clinic_medication_association.get_by_clinic_and_medication(
            db, clinic_id=clinic_id, medication_id=med.id
        )
        if not assoc:
            assoc = crud.clinic_medication_association.create_association(
                db,
                clinic_id=clinic_id,
                medication_id=med.id,
                notes=upsert_in.clinic_specific_notes,
            )

        # Create a dictionary with the necessary fields for the response
        return ClinicMedicationAssociationResponse(
            message="Medication already exists. Associated with clinic.",
            clinic_id=assoc.clinic_id,
            medication_id=assoc.medication_id,
        )
    else:
        # Only pass Medication fields to Medication.create
        medication_fields = upsert_in.dict(
            exclude_unset=True, exclude={"clinic_specific_notes"}
        )
        # Remove any keys not in Medication model
        # Ensure only fields present in MedicationCreate are used
        valid_create_fields = MedicationCreate.__fields__.keys()
        filtered_fields_for_create = {
            k: v for k, v in medication_fields.items() if k in valid_create_fields
        }

        # Create the correct Pydantic schema instance
        medication_create_obj = MedicationCreate(**filtered_fields_for_create)

        existing_med = crud.medication.medication.get_by_name(
            db, name=medication_create_obj.name
        )

        if existing_med:
            # UPDATE existing medication
            logger.info(
                f"Medication '{medication_create_obj.name}' already exists with ID {existing_med.id}. Attempting update."
            )
            # Ensure we only pass valid fields for update
            update_data = medication_create_obj.dict(exclude_unset=True)
            try:
                updated_med = crud.medication.medication.update(
                    db, db_obj=existing_med, obj_in=update_data
                )
                logger.info(
                    f"Successfully updated existing medication '{updated_med.name}' with ID {updated_med.id}."
                )
                medication_id = updated_med.id
            except Exception as e:  # Catch potential update errors
                logger.error(
                    f"Error updating existing medication {existing_med.id}: {e}",
                    exc_info=True,
                )
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to update existing medication: {str(e)}",
                )
        else:
            # CREATE new medication
            logger.info(
                f"Medication '{medication_create_obj.name}' not found. Creating new entry."
            )
            new_med = crud.medication.medication.create(
                db, obj_in=medication_create_obj
            )
            medication_id = new_med.id

        # Associate using the original notes
        assoc = crud.clinic_medication_association.create_association(
            db,
            clinic_id=clinic_id,
            medication_id=medication_id,
            notes=upsert_in.clinic_specific_notes,
        )

        # Create a dictionary with the necessary fields for the response
        return ClinicMedicationAssociationResponse(
            message="Medication created and associated with clinic.",
            clinic_id=assoc.clinic_id,
            medication_id=assoc.medication_id,
        )


@router.delete(
    "/{clinic_id}/medications/{medication_id}",
    summary="Remove the association between a clinic and a medication",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(deps.get_current_admin_user)],
)
async def remove_medication_association(
    *,
    db: Session = Depends(deps.get_db),
    clinic_id: UUID,
    medication_id: UUID,
    current_user: TokenPayload = Depends(deps.get_current_admin_user),
):
    """
    Remove the association between a specific clinic and medication. Admin only.
    """
    logger.info(
        f"API: Attempting to remove association between clinic {clinic_id} and medication {medication_id} by user {current_user.sub}"
    )
    try:
        from app import crud

        removed = crud.clinic_medication_association.remove_association(
            db, clinic_id=clinic_id, medication_id=medication_id
        )
        if not removed:
            # This case might happen if the association didn't exist in the first place
            logger.warning(
                f"API: Association between clinic {clinic_id} and medication {medication_id} not found for removal."
            )
            # Optionally log audit failure if strict existence check is desired
            # crud.create_audit_log(...)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Association between clinic and medication not found.",
            )

        logger.info(
            f"API: Successfully removed association between clinic {clinic_id} and medication {medication_id}"
        )
        # Log audit success
        crud.create_audit_log(
            db=db,
            obj_in=AuditLogCreate(
                actor_user_id=current_user.sub,
                action="REMOVE_CLINIC_MEDICATION_ASSOCIATION",
                actor_role="admin",  # Assuming admin role based on dependency
                outcome="SUCCESS",
                target_resource_type="clinic_medication_association",
                target_resource_id=f"{clinic_id}_{medication_id}",  # Composite ID for logging
                details={
                    "clinic_id": str(clinic_id),
                    "medication_id": str(medication_id),
                },
            ),
        )
        # Return No Content on success
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except NotFoundError as e:  # Catch specific CRUD errors if defined
        logger.warning(f"API: Failed removal - Not Found: {e}")
        # Log audit failure
        crud.create_audit_log(
            db=db,
            obj_in=AuditLogCreate(
                actor_user_id=current_user.sub,
                action="REMOVE_CLINIC_MEDICATION_ASSOCIATION",
                actor_role="admin",
                outcome="FAILURE",
                target_resource_type="clinic_medication_association",
                target_resource_id=f"{clinic_id}_{medication_id}",
                details={"reason": str(e)},
            ),
        )
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(
            f"API: Unexpected error removing association between clinic {clinic_id} and medication {medication_id}: {e}",
            exc_info=True,
        )
        # Log audit failure
        crud.create_audit_log(
            db=db,
            obj_in=AuditLogCreate(
                actor_user_id=current_user.sub,
                action="REMOVE_CLINIC_MEDICATION_ASSOCIATION",
                actor_role="admin",
                outcome="FAILURE",
                target_resource_type="clinic_medication_association",
                target_resource_id=f"{clinic_id}_{medication_id}",
                details={"error": f"Unexpected error: {str(e)}"},
            ),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while removing the association.",
        )
