import logging

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.schemas.auth import TokenPayload
from app.schemas.content import PaginatedContentResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/educational",
    response_model=PaginatedContentResponse,
    summary="Get Educational Content",
    description="""Retrieves a paginated list of available published educational content items.
    Requires authentication as either a patient or clinician.""",
    status_code=status.HTTP_200_OK,
    responses={
        200: {"description": "Successfully retrieved educational content"},
        401: {"description": "Not authenticated"},
        500: {"description": "Internal server error"},
    },
    tags=["Content"],
)
def read_educational_content(
    current_user: TokenPayload = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(
        100, ge=1, le=200, description="Maximum number of items to return"
    ),
) -> PaginatedContentResponse:
    """
    Retrieve published educational content with pagination.

    Args:
        current_user: Authenticated user information
        db: Database session
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return

    Returns:
        PaginatedContentResponse containing the content items and total count

    Raises:
        HTTPException: If there's an error fetching the content
    """
    try:
        content_items = crud.content.get_all_content(
            db=db, skip=skip, limit=limit, only_published=True
        )
        total_count = crud.content.get_count(db=db, only_published=True)

        return PaginatedContentResponse(
            items=content_items,
            total=total_count,
            page=skip // limit + 1,
            size=limit,
            pages=(total_count + limit - 1) // limit,
        )

    except Exception as e:
        logger.error(
            "Error fetching educational content",
            exc_info=True,
            extra={
                "user_id": current_user.sub,
                "skip": skip,
                "limit": limit,
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching educational content.",
        )
