import logging
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.schemas.patient_alert import PatientAlertRead
from app.services.dashboard_ai_service import DashboardAIService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize the dashboard AI service
dashboard_ai_service = DashboardAIService()


@router.get(
    "/task-summary",
    response_model=schemas.DashboardTaskSummaryResponse,
    tags=["Dashboard"],
    summary="Get Dashboard Task Summary Counts",
    description="Retrieves summary counts of pending tasks for the authenticated clinician.",
    responses={
        200: {"description": "Successfully retrieved task summary"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized - clinician access required"},
        500: {"description": "Internal server error"},
    },
)
def read_dashboard_task_summary(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.DashboardTaskSummaryResponse:
    """
    Get summary counts for pending tasks relevant to the clinician's dashboard.

    Returns counts for:
    - Pending medication requests
    - Pending lab results
    - Unread patient messages
    """
    try:
        # Fetch the clinician's DB record using the Clerk ID from the token payload
        # Clinician model is already available via dependency

        if not current_clinician:
            logger.error(
                f"Clinician record not found in read_dashboard_task_summary"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found.",
            )

        clinician_internal_id = (
            current_clinician.id
        )  # Use the internal DB ID for CRUD operations

        logger.debug(
            f"Fetching task summary for clinician Clerk ID {current_clinician.clerk_id}, DB ID {clinician_internal_id}"
        )

        pending_med_requests_count = (
            crud.medication_request.get_pending_request_count_for_clinician(
                db=db, clinician_id=clinician_internal_id
            )
        )
        pending_lab_results_count = (
            crud.lab_result.get_pending_review_count_by_clinician(
                db=db, clinician_id=clinician_internal_id
            )
        )
        unread_messages_count = crud.chat_message.get_unread_count_for_clinician(
            db=db, clinician_id=clinician_internal_id
        )  # Corrected method name
        pending_appointment_requests_count = (
            crud.appointment_request.get_pending_request_count_for_clinician(
                db=db, clinician_id=clinician_internal_id
            )
        )

        logger.debug(
            f"Task summary for clinician DB ID {clinician_internal_id}: "
            f"med_requests={pending_med_requests_count}, "
            f"lab_results={pending_lab_results_count}, "
            f"messages={unread_messages_count}, "
            f"appointment_requests={pending_appointment_requests_count}"
        )

        return schemas.DashboardTaskSummaryResponse(
            pending_medication_requests=pending_med_requests_count,
            pending_lab_results=pending_lab_results_count,
            unread_patient_messages=unread_messages_count,
            pending_appointment_requests=pending_appointment_requests_count,
        )

    except HTTPException:  # Re-raise HTTPExceptions directly
        raise
    except Exception as e:
        # Use clinician clerk_id for logging
        clinician_id_for_log = current_clinician.clerk_id
        logger.error(
            "Error fetching dashboard task summary",
            exc_info=True,
            extra={"clinician_clerk_id": clinician_id_for_log, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard task summary.",
        )


@router.get(
    "/ai-summary",
    response_model=schemas.AISummaryResponse,
    tags=["Dashboard"],
    summary="Get AI Summary Insights",
    description="Retrieves AI-driven summary insights for the clinician dashboard.",
    responses={
        200: {"description": "Successfully retrieved AI summary"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized - clinician access required"},
        500: {"description": "Internal server error"},
    },
)
def read_ai_summary(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.AISummaryResponse:
    """
    Get AI summary insights for the dashboard.
    Currently returns static placeholder text.

    TODO: Implement actual AI service integration
    """
    try:
        logger.debug(f"Generating AI summary for clinician {current_clinician.id}")

        # Generate AI summary based on real data
        ai_service = dashboard_ai_service.DashboardAIService(db)
        dashboard_data = ai_service.get_ai_summary_data(current_clinician.id)
        
        # Create summary from actual data
        summary_parts = []
        
        # Add urgent items summary
        if dashboard_data.urgent_items > 0:
            summary_parts.append(f"{dashboard_data.urgent_items} urgent items requiring immediate attention")
        
        # Add at-risk patients
        if dashboard_data.at_risk_patients > 0:
            summary_parts.append(f"{dashboard_data.at_risk_patients} patients showing concerning patterns")
        
        # Add pending tasks
        if dashboard_data.pending_reviews > 0:
            summary_parts.append(f"{dashboard_data.pending_reviews} items pending review")
        
        # Default if no issues
        if not summary_parts:
            summary_parts.append("All patients are progressing well. No immediate concerns detected.")
        
        ai_summary = "AI Insights: " + ". ".join(summary_parts) + "."
        
        return schemas.AISummaryResponse(summary_text=ai_summary)

    except Exception as e:
        logger.error(
            "Error generating AI summary",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate AI summary.",
        )


@router.get(
    "/patient-alerts",
    response_model=list[PatientAlertRead],
    tags=["Dashboard"],
    summary="Get Patient Alerts for Dashboard",
    description="Retrieves a list of patient alerts relevant to the authenticated clinician, filterable by status.",
    responses={
        200: {"description": "Successfully retrieved patient alerts"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized - clinician access required"},
        500: {"description": "Internal server error"},
    },
)
def read_patient_alerts(
    alert_status: Optional[str] = Query(
        "new", description='Filter alerts by status (e.g., "new", "acknowledged")', alias="status"
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[PatientAlertRead]:
    """
    Retrieve patient alerts for the authenticated clinician's dashboard.
    Defaults to showing 'new' alerts.

    Args:
        status: Filter alerts by their status
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session
        current_clinician: Currently authenticated clinician

    Returns:
        List of patient alerts matching the criteria
    """
    try:
        logger.debug(
            f"Fetching patient alerts for clinician {current_clinician.id}. Status: {alert_status}, Skip: {skip}, Limit: {limit}"
        )

        alerts = crud.patient_alert.get_by_clinician(
            db=db,
            clinician_id=current_clinician.id,
            status=alert_status,
            skip=skip,
            limit=limit,
        )

        logger.debug(
            f"Found {len(alerts)} patient alerts for clinician {current_clinician.id} with status '{alert_status}'"
        )

        return alerts

    except Exception as e:
        logger.error(
            "Error fetching patient alerts",
            exc_info=True,
            extra={
                "clinician_id": current_clinician.id,
                "status": alert_status,
                "skip": skip,
                "limit": limit,
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve patient alerts.",
        )


@router.get(
    "/ai-prioritized",
    response_model=schemas.dashboard.AIPrioritizedDashboardResponse,
    tags=["Dashboard"],
    summary="Get AI-Prioritized Dashboard",
    description="Retrieves an AI-powered prioritized dashboard with intelligent card ordering and insights.",
    responses={
        200: {"description": "Successfully retrieved AI-prioritized dashboard"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized - clinician access required"},
        500: {"description": "Internal server error"},
    },
)
async def read_ai_prioritized_dashboard(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.dashboard.AIPrioritizedDashboardResponse:
    """
    Get an AI-prioritized dashboard for the authenticated clinician.
    
    This endpoint analyzes multiple data points to:
    - Prioritize dashboard cards based on urgency and relevance
    - Generate actionable insights
    - Provide a summary of current priorities
    
    The AI considers:
    - Critical patient alerts and side effects
    - Overdue appointments
    - Pending task volumes
    - Recent activity patterns
    - Historical clinician interaction patterns
    
    Returns:
        AIPrioritizedDashboardResponse with intelligently ordered cards and insights
    """
    try:
        # Log the clinician object type for debugging
        logger.debug(f"Current clinician type: {type(current_clinician)}")
        logger.debug(f"Current clinician attributes: {dir(current_clinician)}")
        
        clinician_id = getattr(current_clinician, 'id', 'unknown')
        logger.debug(f"Generating AI-prioritized dashboard for clinician {clinician_id}")
        
        # Generate the prioritized dashboard
        prioritized_dashboard = await dashboard_ai_service.generate_prioritized_dashboard(
            db=db,
            clinician=current_clinician,
        )
        
        logger.debug(
            f"Successfully generated AI-prioritized dashboard for clinician {clinician_id} "
            f"with {len(prioritized_dashboard.prioritized_cards)} cards and "
            f"{len(prioritized_dashboard.insights)} insights"
        )
        
        return prioritized_dashboard
        
    except Exception as e:
        logger.error(
            "Error generating AI-prioritized dashboard",
            exc_info=True,
            extra={"clinician_id": str(current_clinician.id) if hasattr(current_clinician, 'id') else 'unknown', "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate AI-prioritized dashboard.",
        )


@router.get(
    "/morning-brief",
    response_model=dict,
    tags=["Dashboard"],
    summary="Get Morning Brief",
    description="Generates a morning brief with urgent items and prioritized insights for the day.",
    responses={
        200: {"description": "Successfully generated morning brief"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized - clinician access required"},
        500: {"description": "Internal server error"},
    },
)
async def read_morning_brief(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> dict:
    """
    Generate a morning brief for the authenticated clinician.
    
    The morning brief includes:
    - Summary of urgent items requiring immediate attention
    - Critical patient alerts
    - Overdue appointments
    - Prioritized insights for the day
    
    Returns:
        Dict containing morning brief data with urgent items and insights
    """
    try:
        logger.debug(f"Generating morning brief for clinician {current_clinician.id}")
        
        # Generate the morning brief
        morning_brief = await dashboard_ai_service.generate_morning_brief(
            db=db,
            clinician=current_clinician,
        )
        
        logger.debug(
            f"Successfully generated morning brief for clinician {current_clinician.id}"
        )
        
        return morning_brief
        
    except Exception as e:
        logger.error(
            "Error generating morning brief",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate morning brief.",
        )
