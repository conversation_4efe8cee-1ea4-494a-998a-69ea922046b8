"""Demo management endpoints for investor demonstrations."""

from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
import subprocess
import os
import sys

from app import crud, models, schemas
from app.api import deps
from app.core.config import settings
from app.api.deps import get_current_active_user
from app.utils.audit import log_audit_event

class DemoResetRequest(BaseModel):
    confirm: bool = False

router = APIRouter()


@router.post("/reset", response_model=schemas.StandardResponse)
def reset_demo_environment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: schemas.TokenPayload = Depends(get_current_active_user),
    reset_request: DemoResetRequest
) -> Any:
    """
    Reset the demo environment to a clean state.
    
    **DANGER**: This will delete all demo data and reseed the database.
    Only available to superusers and blocked in production.
    
    Args:
        confirm: Must be True to execute the reset
    """
    # Safety check 1: Ensure we're not in production
    if "prod" in settings.DATABASE_URL.lower():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Demo reset is not allowed in production environment"
        )
    
    # Safety check 2: Require explicit confirmation
    if not reset_request.confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Confirmation required. Set 'confirm' to true to proceed with demo reset."
        )
    
    # Check if user is admin or clinician
    # TokenPayload might have 'role' as a string or list
    user_roles = []
    if hasattr(current_user, 'role'):
        if isinstance(current_user.role, list):
            user_roles = [r.lower() for r in current_user.role]
        elif isinstance(current_user.role, str):
            user_roles = [current_user.role.lower()]
    
    # Debug logging
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"User roles: {user_roles}, type: {type(current_user.role)}")
    
    if "admin" not in user_roles and "clinician" not in user_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"User roles '{current_user.role}' are not authorized for this endpoint"
        )
    
    # Log the reset attempt
    actor_role = str(current_user.role) if hasattr(current_user, 'role') else "unknown"
    log_audit_event(
        db=db,
        actor_user_id=current_user.sub,
        actor_role=actor_role,
        action="DEMO_RESET_INITIATED",
        outcome="ATTEMPT",
        target_resource_type="system",
        details={
            "initiated_by": current_user.sub,
            "environment": "production" if "prod" in settings.DATABASE_URL.lower() else "development"
        }
    )
    
    try:
        # Get the correct path to the demo reset script
        # In Docker container, the app is at /home/<USER>/app
        # Scripts are at /home/<USER>/app/scripts
        if os.path.exists("/home/<USER>/app/scripts/demo_reset.py"):
            # We're in the Docker container
            script_path = "/home/<USER>/app/scripts/demo_reset.py"
        else:
            # Local development
            app_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            script_path = os.path.join(app_root, "scripts", "demo_reset.py")
        
        print(f"Demo reset script path: {script_path}")
        
        # Run the reset script with automatic confirmation
        env = os.environ.copy()
        env['DEMO_RESET_CONFIRM'] = 'RESET'  # Auto-confirm for API usage
        env['PYTHONPATH'] = '/home/<USER>/app:' + env.get('PYTHONPATH', '')
        
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            env=env,
            cwd="/home/<USER>/app" if os.path.exists("/home/<USER>/app") else None
        )
        
        if result.returncode != 0:
            error_detail = result.stderr or "Unknown error occurred"
            print(f"Demo reset failed with error: {error_detail}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Demo reset failed: {error_detail}"
            )
        
        # Log successful reset
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=actor_role,
            action="DEMO_RESET_COMPLETED",
            outcome="SUCCESS",
            target_resource_type="system",
            details={
                "success": True,
                "message": "Demo environment reset completed successfully",
                "output": result.stdout[-500:] if result.stdout else "No output"  # Last 500 chars
            }
        )
        
        print(f"Demo reset completed successfully!")
        if result.stdout:
            print("Output:", result.stdout[-1000:])  # Print last 1000 chars for debugging
        
        return {
            "success": True,
            "message": "Demo environment reset successfully",
            "data": {
                "reset_by": current_user.sub,
                "summary": "All demo data cleared and reseeded with fresh content"
            }
        }
        
    except Exception as e:
        # Log the error
        log_audit_event(
            db=db,
            actor_user_id=current_user.sub,
            actor_role=actor_role,
            action="DEMO_RESET_FAILED",
            outcome="FAILURE",
            target_resource_type="system",
            details={
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        
        import traceback
        traceback.print_exc()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute demo reset: {str(e)}"
        )


@router.get("/status", response_model=schemas.DemoStatusResponse)
def get_demo_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: schemas.TokenPayload = Depends(get_current_active_user),
) -> Any:
    """
    Get the current status of the demo environment.
    
    Returns counts of various data types and last reset information.
    """
    # Check if user is admin or clinician
    import logging
    logger = logging.getLogger(__name__)
    
    logger.info(f"Checking roles for user: {current_user.sub}")
    logger.info(f"Raw role value: {current_user.role}, type: {type(current_user.role)}")
    
    user_roles = []
    if hasattr(current_user, 'role') and current_user.role:
        if isinstance(current_user.role, list):
            user_roles = [str(r).lower() for r in current_user.role if r]
        elif isinstance(current_user.role, str):
            user_roles = [current_user.role.lower()]
    
    logger.info(f"Processed user_roles: {user_roles}")
    
    if "admin" not in user_roles and "clinician" not in user_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"User roles '{current_user.role}' are not authorized for this endpoint. Processed roles: {user_roles}"
        )
    # Get data counts
    patient_count = db.query(models.Patient).count()
    appointment_count = db.query(models.Appointment).count()
    message_count = db.query(models.ChatMessage).count()
    weight_log_count = db.query(models.WeightLog).count()
    side_effect_count = db.query(models.SideEffectReport).count()
    clinical_note_count = db.query(models.ClinicalNote).count()
    
    # Check for demo accounts
    demo_clinician = db.query(models.Clinician).filter_by(
        clerk_id="user_2waSREJSlduBPyK6Vbv9TU3VhI7"
    ).first()
    demo_patient = db.query(models.Patient).filter_by(
        id="user_2waTCuGL3kQC9k2rY47INdcJXk5"
    ).first()
    
    # Get last reset info from audit logs (if available)
    # This would require querying audit logs for demo_reset_completed actions
    
    return {
        "is_production": "prod" in settings.DATABASE_URL.lower(),
        "demo_accounts_exist": bool(demo_clinician and demo_patient),
        "data_counts": {
            "patients": patient_count,
            "appointments": appointment_count,
            "chat_messages": message_count,
            "weight_logs": weight_log_count,
            "side_effects": side_effect_count,
            "clinical_notes": clinical_note_count
        },
        "demo_accounts": {
            "clinician": {
                "exists": bool(demo_clinician),
                "name": f"{demo_clinician.first_name} {demo_clinician.last_name}" if demo_clinician else None
            },
            "patient": {
                "exists": bool(demo_patient),
                "name": f"{demo_patient.first_name} {demo_patient.last_name}" if demo_patient else None
            }
        }
    }


@router.post("/quick-seed", response_model=schemas.StandardResponse)
def quick_seed_demo_data(
    *,
    db: Session = Depends(deps.get_db),
    current_user: schemas.TokenPayload = Depends(get_current_active_user),
    data_type: str
) -> Any:
    """
    Quickly seed specific types of demo data without full reset.
    
    Useful for adding more data during a demo.
    
    Args:
        data_type: Type of data to seed ('messages', 'appointments', 'weights', 'side_effects')
    """
    if settings.ENVIRONMENT == "production":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Demo seeding is not allowed in production"
        )
    
    # Check if user is admin or clinician
    user_roles = []
    if hasattr(current_user, 'role'):
        if isinstance(current_user.role, list):
            user_roles = [r.lower() for r in current_user.role]
        elif isinstance(current_user.role, str):
            user_roles = [current_user.role.lower()]
    
    if "admin" not in user_roles and "clinician" not in user_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can seed demo data"
        )
    
    valid_types = ['messages', 'appointments', 'weights', 'side_effects']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid data type. Must be one of: {', '.join(valid_types)}"
        )
    
    # This would call specific seeding functions based on data_type
    # For now, return a placeholder response
    
    return {
        "success": True,
        "message": f"Demo {data_type} data seeded successfully",
        "data": {
            "type": data_type,
            "seeded_by": current_user.sub
        }
    }