"""
Education Materials API endpoints.

This module handles CRUD operations for educational materials including:
- Material creation and management
- File upload handling
- Multi-tenant access control
- Search and filtering
"""

from typing import List, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Body, status, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from azure.storage.blob import BlobServiceClient
import logging

from app.api.deps import get_db, get_current_user
from app.crud.crud_education_material import education_material as crud_education_material
from app.crud.crud_patient_education_assignment import patient_education_assignment as crud_assignment
from app.models.education_material import MaterialType
from app.schemas.education_material import (
    EducationMaterial,
    EducationMaterialCreate,
    EducationMaterialUpdate,
    EducationMaterialSearchFilters,
    FileUploadResponse,
)
from app.schemas.auth import TokenPayload
from app.services.education_embedding_service import process_education_material_for_rag
from app.core.config import settings

logger = logging.getLogger(__name__)


router = APIRouter()


def get_user_clinic_id(user: TokenPayload, db: Session) -> Optional[str]:
    """Helper function to get clinic_id for a user from the database."""
    # If it's in the token metadata, use that first
    if hasattr(user, 'public_metadata') and user.public_metadata:
        clinic_id = user.public_metadata.get("associated_clinic_id")
        if clinic_id:
            return clinic_id
    
    # Otherwise, query the database for clinician's clinic
    if user.role == "clinician" or (isinstance(user.role, list) and "clinician" in user.role):
        # Query the clinician_clinic_association table
        from sqlalchemy import text
        result = db.execute(
            text("""
                SELECT cca.clinic_id 
                FROM clinicians c 
                JOIN clinician_clinic_association cca ON c.id = cca.clinician_id 
                WHERE c.clerk_id = :clerk_id
                LIMIT 1
            """),
            {"clerk_id": user.sub}
        ).first()
        
        if result:
            return str(result[0])
    
    return None


@router.post("/", response_model=EducationMaterial)
async def create_education_material(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    background_tasks: BackgroundTasks,
    material_in: EducationMaterialCreate,
) -> EducationMaterial:
    """
    Create a new education material.
    
    Requires clinician or admin role.
    """
    # Handle role as either string or list
    allowed_roles = {"clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles
    
    if not has_role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only clinicians and admins can create education materials"
        )
    
    # For clinicians, materials are private to their clinic by default
    if current_user.role == "clinician" and not material_in.clinic_id:
        clinic_id = get_user_clinic_id(current_user, db)
        if clinic_id:
            material_in.clinic_id = clinic_id
            material_in.is_public = False
    
    # Use the create_with_creator method from CRUD
    material = crud_education_material.create_with_creator(
        db=db,
        obj_in=material_in,
        created_by=current_user.sub
    )
    
    # If material has a file URL, process it for RAG
    if material.content_url:
        background_tasks.add_task(
            process_education_material_for_rag,
            db,
            material
        )
    
    return material


@router.get("/", response_model=List[EducationMaterial])
def list_education_materials(
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category: Optional[str] = None,
    type: Optional[MaterialType] = None,
    clinic_id: Optional[UUID] = None,
    is_public: Optional[bool] = None,
) -> List[EducationMaterial]:
    """
    Retrieve education materials with filtering and search.
    
    Access control:
    - Admins: see all materials
    - Clinicians: see public materials + their clinic's materials
    - Patients: see only public materials + assigned materials
    """
    filters = EducationMaterialSearchFilters(
        search=search,
        category=category,
        type=type,
        clinic_id=clinic_id,
        is_public=is_public,
    )
    
    materials = crud_education_material.get_multi_filtered(
        db=db,
        skip=skip,
        limit=limit,
        filters=filters,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    )
    
    return materials


@router.get("/{material_id}", response_model=EducationMaterial)
def get_education_material(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    material_id: UUID,
) -> EducationMaterial:
    """
    Get a specific education material by ID.
    
    Access control enforced based on user role and material visibility.
    """
    material = crud_education_material.get(db=db, id=material_id)
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education material not found"
        )
    
    # Check access permissions
    if not crud_education_material.can_access_material(
        db=db,
        material=material,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this material"
        )
    
    return material


@router.put("/{material_id}", response_model=EducationMaterial)
def update_education_material(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    material_id: UUID,
    material_in: EducationMaterialUpdate,
) -> EducationMaterial:
    """
    Update an education material.
    
    Only the creator, clinic admins, or system admins can update materials.
    """
    material = crud_education_material.get(db=db, id=material_id)
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education material not found"
        )
    
    # Check update permissions
    if not crud_education_material.can_modify_material(
        material=material,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this material"
        )
    
    material = crud_education_material.update(db=db, db_obj=material, obj_in=material_in)
    return material


@router.delete("/{material_id}")
def delete_education_material(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    material_id: UUID,
) -> dict:
    """
    Delete an education material (soft delete).
    
    Only the creator, clinic admins, or system admins can delete materials.
    """
    material = crud_education_material.get(db=db, id=material_id)
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education material not found"
        )
    
    # Check delete permissions
    if not crud_education_material.can_modify_material(
        material=material,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this material"
        )
    
    crud_education_material.remove(db=db, id=material_id)
    return {"message": "Education material deleted successfully"}


@router.post("/upload", response_model=FileUploadResponse)
async def upload_education_file(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: str = Form(...),
    description: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    clinic_id: Optional[UUID] = Form(None),
    is_public: bool = Form(False),
) -> FileUploadResponse:
    """
    Upload an education file (PDF, document, etc.).
    
    Requires clinician or admin role. Files are stored in Azure Blob Storage.
    """
    # Handle role as either string or list
    allowed_roles = {"clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles
    
    if not has_role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only clinicians and admins can upload education materials"
        )
    
    # Validate file type
    allowed_types = ["application/pdf", "application/msword", 
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "text/plain", "text/html"]
    
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file.content_type} not allowed. Allowed types: PDF, DOC, DOCX, TXT, HTML"
        )
    
    # Validate file size (max 50MB)
    max_size = 50 * 1024 * 1024  # 50MB
    # Read file content to check size
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="File size exceeds 50MB limit"
        )
    # Reset file position for later use
    file.file.seek(0)
    
    try:
        # Upload file to Azure Blob Storage
        file_url, file_path = await crud_education_material.upload_file(
            file=file,
            user_id=current_user.sub,
            clinic_id=clinic_id or get_user_clinic_id(current_user, db),
        )
        
        # Determine material type based on content type
        material_type = MaterialType.PDF if file.content_type == "application/pdf" else MaterialType.DOCUMENT
        
        # Determine clinic_id - use provided or default to user's clinic
        final_clinic_id = clinic_id if clinic_id else get_user_clinic_id(current_user, db)
        
        # Create education material record
        material_create = EducationMaterialCreate(
            title=title,
            description=description,
            type=material_type,
            category=category,
            content_url=file_url,
            is_public=is_public,
            clinic_id=final_clinic_id,
        )
        
        # Use the create_with_creator method from CRUD
        material = crud_education_material.create_with_creator(
            db=db,
            obj_in=material_create,
            created_by=current_user.sub
        )
        
        # Update with file_path if needed
        if file_path:
            material.file_path = file_path
            db.add(material)
            db.commit()
            db.refresh(material)
        
        # Process the material for RAG in the background
        background_tasks.add_task(
            process_education_material_for_rag,
            db,
            material
        )
        
        return FileUploadResponse(
            material_id=str(material.id),
            file_url=file_url,
            file_path=file_path,
            message="File uploaded successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File upload failed: {str(e)}"
        )


@router.get("/categories/list", response_model=List[str])
def get_material_categories(
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
) -> List[str]:
    """
    Get list of all available material categories.
    """
    categories = crud_education_material.get_categories(
        db=db,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    )
    return categories


@router.get("/analytics/summary")
def get_material_analytics(
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
) -> dict:
    """
    Get analytics summary for education materials.
    
    Admins see global stats, clinicians see clinic-specific stats.
    """
    # Handle role as either string or list
    allowed_roles = {"clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles
    
    if not has_role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    analytics = crud_education_material.get_analytics(
        db=db,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user, db),
    )
    
    return analytics


@router.get("/{material_id}/content")
async def get_education_material_content(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    material_id: UUID,
):
    """
    Stream education material content through backend proxy.
    
    This endpoint:
    1. Verifies user has permission to view the material
    2. Fetches content from Azure blob storage using backend credentials
    3. Streams the content to the user
    
    This avoids exposing Azure storage keys to the frontend.
    """
    # Get the education material
    material = crud_education_material.get(db=db, id=material_id)
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education material not found"
        )
    
    # Check permissions
    user_can_view = False
    
    # Check if material is public
    if material.is_public:
        user_can_view = True
    else:
        # Check if user is a clinician in the same clinic
        user_clinic_id = get_user_clinic_id(current_user, db)
        if user_clinic_id and material.clinic_id and str(user_clinic_id) == str(material.clinic_id):
            # Handle role as either string or list
            if isinstance(current_user.role, list):
                if "clinician" in current_user.role or "admin" in current_user.role:
                    user_can_view = True
            elif isinstance(current_user.role, str):
                if current_user.role in ["clinician", "admin"]:
                    user_can_view = True
        
        # Check if user is a patient with assignment
        is_patient = False
        if isinstance(current_user.role, list):
            is_patient = "patient" in current_user.role
        elif isinstance(current_user.role, str):
            is_patient = current_user.role == "patient"
            
        if not user_can_view and is_patient:
            # Check if this material is assigned to the patient
            assignment = crud_assignment.get_by_patient_and_material(
                db=db,
                patient_id=current_user.sub,
                material_id=str(material_id)
            )
            if assignment:
                user_can_view = True
    
    if not user_can_view:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this material"
        )
    
    # Extract content URL
    if not material.content_url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Material content not available"
        )
    
    try:
        # Parse Azure blob URL to get container and blob name
        # URL format: https://{account}.blob.core.windows.net/{container}/{blob_path}
        url_parts = material.content_url.split('/')
        container_name = url_parts[3]  # Container name is after the domain
        blob_name = '/'.join(url_parts[4:])  # Rest is the blob path
        
        # Create blob service client
        blob_service_client = BlobServiceClient.from_connection_string(
            settings.AZURE_STORAGE_CONNECTION_STRING
        )
        
        # Get blob client
        blob_client = blob_service_client.get_blob_client(
            container=container_name,
            blob=blob_name
        )
        
        # Download blob as stream
        blob_stream = blob_client.download_blob()
        
        # Get content type based on file extension
        content_type = "application/pdf"  # Default for PDFs
        if material.type == MaterialType.DOCUMENT:
            if blob_name.lower().endswith('.docx'):
                content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            elif blob_name.lower().endswith('.doc'):
                content_type = "application/msword"
            elif blob_name.lower().endswith('.txt'):
                content_type = "text/plain"
        
        # Return streaming response
        return StreamingResponse(
            blob_stream.chunks(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"inline; filename=\"{material.title}\"",
                "Cache-Control": "private, max-age=3600",  # Cache for 1 hour
            }
        )
        
    except Exception as e:
        logger.error(f"Error streaming education material content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve material content"
        )


@router.post("/{material_id}/assign")
async def assign_education_material(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    material_id: UUID,
    patient_ids: List[str] = Body(..., description="List of patient IDs to assign material to"),
    priority: Optional[str] = Body("medium", description="Assignment priority"),
    due_date: Optional[datetime] = Body(None, description="Optional due date for assignment"),
    notes: Optional[str] = Body(None, description="Optional notes for the assignment"),
):
    """
    Assign an education material to one or more patients.
    
    Only clinicians and admins can assign materials.
    Materials can only be assigned if they are public or from the clinician's clinic.
    """
    # Verify user is a clinician or admin
    allowed_roles = {"clinician", "admin"}
    has_role = False
    if isinstance(current_user.role, list):
        has_role = any(r in allowed_roles for r in current_user.role)
    elif isinstance(current_user.role, str):
        has_role = current_user.role in allowed_roles
    
    if not has_role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only clinicians and admins can assign education materials"
        )
    
    # Get the education material
    material = crud_education_material.get(db=db, id=material_id)
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education material not found"
        )
    
    # Check if clinician can assign this material
    user_clinic_id = get_user_clinic_id(current_user, db)
    if not crud_education_material.can_modify_material(
        material=material,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=user_clinic_id,
    ):
        # For assignment, we also allow public materials
        if not material.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only assign public materials or materials from your clinic"
            )
    
    # Import here to avoid circular imports
    from app.models.patient_education_assignment import AssignmentPriority, AssignmentStatus
    from app.schemas.patient_education_assignment import PatientEducationAssignmentCreate
    
    # Convert priority string to enum
    priority_map = {
        "low": AssignmentPriority.LOW,
        "medium": AssignmentPriority.MEDIUM,
        "high": AssignmentPriority.HIGH,
        "urgent": AssignmentPriority.URGENT
    }
    assignment_priority = priority_map.get(priority.lower(), AssignmentPriority.MEDIUM)
    
    # Track results
    successful_assignments = []
    failed_assignments = []
    
    for patient_id in patient_ids:
        try:
            # Check if assignment already exists
            existing = crud_assignment.get_existing_assignment(
                db=db,
                patient_id=patient_id,
                material_id=material_id
            )
            
            if existing:
                failed_assignments.append({
                    "patient_id": patient_id,
                    "reason": "Material already assigned to this patient"
                })
                continue
            
            # Create the assignment
            assignment_data = PatientEducationAssignmentCreate(
                patient_id=patient_id,
                material_id=material_id,
                assigned_by=current_user.sub,
                priority=assignment_priority,
                status=AssignmentStatus.ASSIGNED,
                due_date=due_date,
                notes=notes
            )
            
            assignment = crud_assignment.create(db=db, obj_in=assignment_data)
            successful_assignments.append({
                "patient_id": patient_id,
                "assignment_id": str(assignment.id)
            })
            
        except Exception as e:
            logger.error(f"Failed to assign material to patient {patient_id}: {e}")
            failed_assignments.append({
                "patient_id": patient_id,
                "reason": str(e)
            })
    
    return {
        "material_id": str(material_id),
        "material_title": material.title,
        "successful_assignments": successful_assignments,
        "failed_assignments": failed_assignments,
        "summary": {
            "total_requested": len(patient_ids),
            "successful": len(successful_assignments),
            "failed": len(failed_assignments)
        }
    }