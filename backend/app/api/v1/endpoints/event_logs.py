import logging
from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api import deps
from app.utils.audit import log_audit_event
from app.services.event_log_enrichment_service import event_log_enrichment_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get(
    "/",
    response_model=list[dict[str, Any]],
    summary="Get Event Logs",
    description="Retrieves a paginated list of event logs based on user access level.",
)
async def get_event_logs(
    actor_id: Optional[str] = Query(None, description="Filter by actor user ID"),
    actor_role: Optional[str] = Query(None, description="Filter by actor role"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    resource_id: Optional[str] = Query(None, description="Filter by resource ID"),
    clinic_id: Optional[UUID] = Query(None, description="Filter by clinic ID"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    llm_only: bool = Query(False, description="Show only LLM-driven actions"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        20, ge=1, le=100, description="Maximum number of records to return"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> list[schemas.EventLog]:
    """Get event logs based on user role and permissions."""
    try:
        # Determine user's roles for permission checking
        if isinstance(current_user.role, list):
            roles = set(current_user.role)
        else:
            roles = {current_user.role}

        # Base filters
        filters = {}
        if action:
            filters["action"] = action

        if start_date:
            filters["start_date"] = start_date

        if end_date:
            filters["end_date"] = end_date

        # Role-based access control
        if "admin" in roles:
            # Admins can see logs for their clinics
            if clinic_id:
                filters["clinic_id"] = clinic_id

            if actor_id:
                filters["actor_user_id"] = actor_id

            if actor_role:
                filters["actor_role"] = actor_role

            if resource_type:
                filters["resource_type"] = resource_type

            if resource_id:
                filters["resource_id"] = resource_id

        elif "clinician" in roles:
            # Clinicians can see logs related to their patients and themselves
            clinician_db = crud.clinician.get_clinician_by_clerk_id(
                db, clerk_id=current_user.sub
            )
            if not clinician_db:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Clinician record not found",
                )

            # Can always see their own logs
            if actor_id and actor_id != current_user.sub:
                # Verify patient belongs to clinician if filtering by patient
                # This assumes actor_id is a patient ID when actor_role is 'patient'
                if actor_role == "patient" and not crud.clinician.is_patient_assigned(
                    db, clinician_id=clinician_db.id, patient_id=actor_id
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied to patient's event logs",
                    )
                filters["actor_user_id"] = actor_id
            else:
                # If not filtering by specific patient, show logs for all patients
                # This allows the dashboard to show activity across all patients
                if actor_role:
                    # Only filter by actor_role if specifically requested
                    filters["actor_role"] = actor_role
                # If no actor_role specified, show all activity types (patient, ai_agent, clinician)

            if clinic_id:
                # TODO: Fix clinic verification - get_clinics method issue
                # For now, allow clinic filtering without verification
                filters["clinic_id"] = clinic_id

        elif "patient" in roles:
            # Patients can only see their own logs
            filters["actor_user_id"] = current_user.sub

            # Ignore any actor_id filter that's not their own
            if actor_id and actor_id != current_user.sub:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Can only access own event logs",
                )

        # Get the event logs with filters
        if llm_only:
            event_logs = crud.event_log.get_llm_actions(
                db,
                actor_user_id=filters.get("actor_user_id"),
                clinic_id=filters.get("clinic_id"),
                skip=skip,
                limit=limit,
            )
        else:
            # Apply specific filters based on what was provided
            query_params = {}

            if "actor_user_id" in filters:
                query_params["actor_user_id"] = filters["actor_user_id"]

            if "actor_role" in filters:
                query_params["actor_role"] = filters["actor_role"]

            if "action" in filters:
                query_params["action"] = filters["action"]

            if "clinic_id" in filters:
                query_params["clinic_id"] = filters["clinic_id"]

            if "resource_type" in filters and "resource_id" in filters:
                event_logs = crud.event_log.get_by_resource(
                    db,
                    resource_type=filters["resource_type"],
                    resource_id=filters["resource_id"],
                    skip=skip,
                    limit=limit,
                )
            elif "actor_user_id" in query_params:
                event_logs = crud.event_log.get_by_actor_user_id(
                    db,
                    actor_user_id=query_params["actor_user_id"],
                    skip=skip,
                    limit=limit,
                )
            elif "actor_role" in query_params:
                event_logs = crud.event_log.get_by_actor_role(
                    db, actor_role=query_params["actor_role"], skip=skip, limit=limit
                )
            elif "action" in query_params:
                event_logs = crud.event_log.get_by_action(
                    db, action=query_params["action"], skip=skip, limit=limit
                )
            elif "clinic_id" in query_params:
                event_logs = crud.event_log.get_by_clinic(
                    db, clinic_id=query_params["clinic_id"], skip=skip, limit=limit
                )
            else:
                # Fallback to basic pagination if no specific filters
                event_logs = crud.event_log.get_multi(db, skip=skip, limit=limit)

        # Log the event log access
        log_audit_event(
            db=db,
            action="GET_EVENT_LOGS",
            actor_user_id=current_user.sub,
            actor_role=current_user.role if isinstance(current_user.role, str) else current_user.role[0],
            outcome="SUCCESS",
            details={
                "filters": filters,
                "pagination": {"skip": skip, "limit": limit},
                "count": len(event_logs),
                "llm_only": llm_only,
            },
        )

        # Enrich event logs with names
        enriched_logs = event_log_enrichment_service.enrich_event_logs(db, event_logs)
        
        return enriched_logs

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving event logs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving event logs",
        )


@router.get(
    "/stats",
    response_model=dict[str, Any],
    summary="Get Event Log Statistics",
    description="Retrieves statistical information about event logs (admin only).",
)
async def get_event_log_stats(
    clinic_id: Optional[UUID] = Query(None, description="Filter by clinic ID"),
    days: int = Query(
        30, ge=1, le=365, description="Number of days to include in stats"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_admin),
    db: Session = Depends(deps.get_db),
) -> dict[str, Any]:
    """Get event log statistics (admin only)."""
    try:
        # Get the statistics
        stats = crud.event_log.get_stats(db, clinic_id=clinic_id, days=days)

        # Log the statistics access
        log_audit_event(
            db=db,
            event_type="GET_EVENT_LOG_STATS",
            user_id=current_user.sub,
            user_role=current_user.role,
            details={"clinic_id": str(clinic_id) if clinic_id else None, "days": days},
        )

        return stats

    except Exception as e:
        logger.error(f"Error retrieving event log statistics: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving event log statistics",
        )


@router.get(
    "/{event_log_id}",
    response_model=schemas.EventLog,
    summary="Get Event Log",
    description="Retrieves a specific event log by ID.",
)
async def get_event_log(
    event_log_id: UUID = Path(..., description="The ID of the event log to retrieve"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> schemas.EventLog:
    """Get a specific event log by ID."""
    try:
        # Get the event log
        event_log = crud.event_log.get(db, id=event_log_id)
        if not event_log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Event log not found"
            )

        # Check permissions based on role
        if isinstance(current_user.role, list):
            roles = set(current_user.role)
        else:
            roles = {current_user.role}

        # Admins can see all event logs
        if "admin" in roles:
            pass
        # Clinicians can see their own logs and those of their patients
        elif "clinician" in roles:
            # Can always see their own logs
            if (
                event_log.actor_user_id != current_user.sub
                and event_log.actor_role == "patient"
            ):
                # Verify patient belongs to clinician
                clinician_db = crud.clinician.get_clinician_by_clerk_id(
                    db, clerk_id=current_user.sub
                )
                if not clinician_db or not crud.clinician.is_patient_assigned(
                    db, clinician_id=clinician_db.id, patient_id=event_log.actor_user_id
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied to this event log",
                    )
        # Patients can only see their own logs
        elif "patient" in roles:
            if event_log.actor_user_id != current_user.sub:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this event log",
                )

        # Log the event log access
        log_audit_event(
            db=db,
            event_type="GET_EVENT_LOG",
            user_id=current_user.sub,
            user_role=current_user.role,
            target_resource_type="event_log",
            target_resource_id=str(event_log_id),
            details={
                "action": event_log.action,
                "actor_role": event_log.actor_role,
                "actor_user_id": event_log.actor_user_id,
            },
        )

        return event_log

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving event log {event_log_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving event log",
        )
