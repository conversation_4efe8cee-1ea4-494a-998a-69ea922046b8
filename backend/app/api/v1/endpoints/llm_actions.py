"""API endpoints for LLM-driven actions.

These endpoints enable processing natural language inputs to execute API actions,
including the text-based interface for LLM-driven actions.
"""

import json
import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.services.action_executor_service import ActionExecutorService
from app.services.intent_resolver_service import (
    IntentResolutionResult,
    IntentResolverService,
)
from app.services.template_validation_service import TemplateValidationService
from app.utils.audit import log_audit_event

router = APIRouter()
logger = logging.getLogger(__name__)


class TextActionRequest(BaseModel):
    """Request model for text-based LLM actions."""

    text: str = Field(
        ...,
        min_length=1,
        description="Natural language text describing the action to perform",
    )
    template_id: UUID = Field(
        ..., description="ID of the template to use for intent resolution"
    )
    context: Optional[dict[str, Any]] = Field(
        None,
        description="Additional context for the action. Can include 'timezone_offset' (float, hours from UTC) "
        "to ensure correct local time interpretation for appointment scheduling.",
    )


class TextActionResponse(BaseModel):
    """Response model for text-based LLM actions."""

    success: bool = Field(..., description="Whether the action was successful")
    message: str = Field(
        ..., description="Human-readable message describing the outcome"
    )
    action_type: Optional[str] = Field(
        None, description="Type of action that was performed"
    )
    data: Optional[dict[str, Any]] = Field(
        None, description="Additional data returned by the action"
    )
    debug_info: Optional[dict[str, Any]] = Field(
        None, description="Debug information (admin only)"
    )


@router.post(
    "/text",
    response_model=TextActionResponse,
    summary="Process Text Action",
    description="Processes natural language text to perform an API action using LLM.",
)
async def process_text_action(
    request: TextActionRequest = Body(..., description="Text action request"),
    include_debug: bool = Query(
        False, description="Include debug information in response (admin only)"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> TextActionResponse:
    """Process a text-based LLM action request."""
    try:
        # Get user ID and role
        user_id = current_user.sub
        user_role = current_user.role

        # Convert to list for consistency
        if not isinstance(user_role, list):
            user_roles = [user_role]
        else:
            user_roles = user_role

        # Set user role for permission checking
        primary_role = user_roles[0] if user_roles else "patient"

        # Initialize services
        intent_resolver = IntentResolverService()

        # Process the request
        template_id = str(request.template_id)
        user_text = request.text
        context = request.context or {}

        # Get timezone offset from context if available
        client_timezone_offset = (
            context.get("timezone_offset")
            if context.get("timezone_offset") is not None
            else context.get("client_timezone_offset")
        )
        if client_timezone_offset is not None:
            try:
                client_timezone_offset = float(client_timezone_offset)
                logger.info(
                    f"Using client timezone offset from request: {client_timezone_offset}"
                )
            except (ValueError, TypeError):
                logger.warning(
                    f"Invalid timezone_offset value: {client_timezone_offset}, ignoring"
                )
                client_timezone_offset = None

        # Log the request
        logger.info(
            f"Processing text action: '{user_text[:50]}...' with template {template_id}"
        )
        log_audit_event(
            db=db,
            event_type="PROCESS_TEXT_ACTION",
            user_id=user_id,
            user_role=user_role,
            target_resource_type="template",
            target_resource_id=template_id,
            details={"text_length": len(user_text), "has_context": bool(context)},
        )

        # Get template for intent resolution
        template = await TemplateValidationService.get_valid_template(
            db=db,
            template_id=template_id,
            user_id=user_id,
            role=primary_role
        )
        
        # Resolve the intent from the text
        resolved_result, actions = await intent_resolver.extract_intent(
            template=template,
            user_input=user_text,
            client_timezone_offset=client_timezone_offset,
            user_role=primary_role,
            db=db,
        )
        
        # Check if this is a ChainedAction
        from app.schemas.action_chain_v2 import ChainedAction
        if isinstance(resolved_result, ChainedAction):
            result = {
                "type": "compound",
                "chain": resolved_result
            }
        else:
            result = {
                "type": "single", 
                "intent": resolved_result
            }
        
        # Check if this is a compound action
        if isinstance(result, dict) and result.get("type") == "compound":
            # Handle compound action
            from app.services.action_chain_executor_service import ActionChainExecutorService
            
            chain = result["chain"]
            chain_executor = ActionChainExecutorService(db)
            
            # Execute the chain
            chain_result = await chain_executor.execute_chain(
                user_id=user_id,
                user_role=primary_role,
                chain=chain,
                initial_context=context
            )
            
            # Format response for compound action
            if chain_result.success:
                # Summarize successful actions
                action_summaries = []
                for result in chain_result.results:
                    if result.success:
                        action_summaries.append(f"✓ {result.action_type}: {result.message}")
                    else:
                        action_summaries.append(f"✗ {result.action_type}: {result.error_message}")
                
                message = f"Completed {len([r for r in chain_result.results if r.success])} of {len(chain_result.results)} actions:\n" + "\n".join(action_summaries)
            else:
                message = f"Chain execution failed: {chain_result.error_message}"
            
            return TextActionResponse(
                success=chain_result.success,
                message=message,
                action_type="compound_action",
                data={
                    "chain_id": chain_result.chain_id,
                    "execution_time_ms": chain_result.execution_time_ms,
                    "results": [
                        {
                            "action_type": r.action_type,
                            "success": r.success,
                            "message": r.message if r.success else r.error_message
                        }
                        for r in chain_result.results
                    ]
                },
                debug_info=(
                    {
                        "compound_info": result.get("compound_info"),
                        "chain": chain.dict() if chain else None,
                        "chain_result": chain_result.dict() if chain_result else None
                    }
                    if include_debug and "admin" in user_roles
                    else None
                )
            )
        
        # Handle single action (existing flow)
        resolved_intent = result.get("intent") if isinstance(result, dict) else result

        # Check intent resolution result
        if resolved_intent.result != IntentResolutionResult.SUCCESS:
            error_message = (
                resolved_intent.error_message
                or f"Failed to resolve intent: {resolved_intent.result}"
            )

            # Log the failure
            log_audit_event(
                db=db,
                event_type="INTENT_RESOLUTION_FAILED",
                user_id=user_id,
                user_role=user_role,
                target_resource_type="template",
                target_resource_id=template_id,
                outcome="FAILURE",
                details={"error": error_message, "result": resolved_intent.result},
            )

            # Return error response
            return TextActionResponse(
                success=False,
                message=error_message,
                action_type=None,
                debug_info=(
                    resolved_intent.raw_llm_response
                    if include_debug and "admin" in user_roles
                    else None
                ),
            )

        # Execute the resolved action
        action_executor = ActionExecutorService(db)
        execution_result = await action_executor.execute_action(
            user_id=user_id, user_role=primary_role, resolved_intent=resolved_intent
        )

        # Convert execution result to response
        response = TextActionResponse(
            success=execution_result.success,
            message=execution_result.message,
            action_type=execution_result.action_type,
            data=execution_result.data,
            debug_info=(
                {
                    "intent": (
                        resolved_intent.dict(exclude={"raw_llm_response"})
                        if resolved_intent
                        else None
                    ),
                    "execution_result": (
                        execution_result.dict(exclude={"message", "data"})
                        if execution_result
                        else None
                    ),
                    "llm_response": (
                        resolved_intent.raw_llm_response if resolved_intent else None
                    ),
                }
                if include_debug and "admin" in user_roles
                else None
            ),
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing text action: {e}", exc_info=True)

        # Log the error
        log_audit_event(
            db=db,
            event_type="PROCESS_TEXT_ACTION_ERROR",
            user_id=current_user.sub if current_user else None,
            user_role=current_user.role if current_user else None,
            outcome="FAILURE",
            details={"error": str(e)},
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing text action: {str(e)}",
        )


class TemplateActionRequest(BaseModel):
    """Request model for template-specific actions."""

    action_type: str = Field(..., description="Type of action to perform")
    parameters: dict[str, Any] = Field(
        default_factory=dict, description="Parameters for the action"
    )


@router.post(
    "/templates/{template_id}/actions",
    response_model=TextActionResponse,
    summary="Execute Template Action",
    description="Directly executes an action from a template with the provided parameters.",
)
async def execute_template_action(
    template_id: UUID = Path(
        ..., description="ID of the template containing the action"
    ),
    request: TemplateActionRequest = Body(..., description="Action request"),
    include_debug: bool = Query(
        False, description="Include debug information in response (admin only)"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> TextActionResponse:
    """Execute a specific action from a template."""
    try:
        # Get user ID and role
        user_id = current_user.sub
        user_role = current_user.role

        # Convert to list for consistency
        if not isinstance(user_role, list):
            user_roles = [user_role]
        else:
            user_roles = user_role

        # Set user role for permission checking
        primary_role = user_roles[0] if user_roles else "patient"

        # Log the request
        logger.info(
            f"Executing template action: {request.action_type} with template {template_id}"
        )
        log_audit_event(
            db=db,
            event_type="EXECUTE_TEMPLATE_ACTION",
            user_id=user_id,
            user_role=user_role,
            target_resource_type="template",
            target_resource_id=str(template_id),
            details={
                "action_type": request.action_type,
                "parameter_count": len(request.parameters),
            },
        )

        # Validate template access
        try:
            template = await TemplateValidationService.validate_template_access(
                db=db, template_id=str(template_id), user_id=user_id, roles=user_roles
            )
        except HTTPException as e:
            logger.warning(f"Template access validation failed: {e.detail}")
            return TextActionResponse(
                success=False, message=str(e.detail), action_type=request.action_type
            )

        # Check if action exists in template
        actions = (
            template.actions
            if hasattr(template, "actions")
            else json.loads(template.actions_json)
        )
        if not any(a.get("action_type") == request.action_type for a in actions):
            message = f"Action '{request.action_type}' not found in template"
            logger.warning(message)
            return TextActionResponse(
                success=False, message=message, action_type=request.action_type
            )

        # Create synthetic resolved intent
        from app.services.intent_resolver_service import (
            IntentParameterValue,
            ResolvedIntent,
        )

        parameters = []
        for key, value in request.parameters.items():
            parameters.append(
                IntentParameterValue(
                    name=key, value=value, confidence=1.0, source="direct_input"
                )
            )

        resolved_intent = ResolvedIntent(
            action_type=request.action_type,
            template_id=str(template_id),
            parameters=parameters,
            confidence_score=1.0,
            result=IntentResolutionResult.SUCCESS,
        )

        # Execute the action
        action_executor = ActionExecutorService(db)
        execution_result = await action_executor.execute_action(
            user_id=user_id, user_role=primary_role, resolved_intent=resolved_intent
        )

        # Convert execution result to response
        response = TextActionResponse(
            success=execution_result.success,
            message=execution_result.message,
            action_type=execution_result.action_type,
            data=execution_result.data,
            debug_info=(
                {
                    "execution_result": (
                        execution_result.dict(exclude={"message", "data"})
                        if execution_result
                        else None
                    )
                }
                if include_debug and "admin" in user_roles
                else None
            ),
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing template action: {e}", exc_info=True)

        # Log the error
        log_audit_event(
            db=db,
            event_type="EXECUTE_TEMPLATE_ACTION_ERROR",
            user_id=current_user.sub if current_user else None,
            user_role=current_user.role if current_user else None,
            outcome="FAILURE",
            details={"error": str(e)},
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error executing template action: {str(e)}",
        )


class ChainedActionRequest(BaseModel):
    """Request model for chained actions."""
    
    actions: list[dict[str, Any]] = Field(
        ..., 
        description="List of actions to chain together",
        min_items=1
    )
    execution_mode: str = Field(
        default="sequential",
        description="How to execute the actions: 'sequential' or 'parallel'"
    )
    failure_mode: str = Field(
        default="stop_on_failure",
        description="How to handle failures: 'stop_on_failure' or 'continue_on_failure'"
    )
    context: Optional[dict[str, Any]] = Field(
        default_factory=dict,
        description="Initial context for the chain"
    )


@router.post(
    "/chain",
    response_model=TextActionResponse,
    summary="Execute Chained Actions",
    description="Execute multiple related actions in sequence or parallel.",
)
async def execute_chained_actions(
    request: ChainedActionRequest = Body(..., description="Chained action request"),
    include_debug: bool = Query(
        False, description="Include debug information in response (admin only)"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> TextActionResponse:
    """Execute a chain of related actions."""
    try:
        from app.schemas.action_chain_v2 import (
            ChainedIntent,
            ChainedAction,
            ExecutionMode,
            FailureMode
        )
        from app.services.action_chain_executor_service import ActionChainExecutorService
        
        # Get user ID and role
        user_id = current_user.sub
        user_role = current_user.role
        
        # Convert to list for consistency
        if not isinstance(user_role, list):
            user_roles = [user_role]
        else:
            user_roles = user_role
        
        # Set user role for permission checking
        primary_role = user_roles[0] if user_roles else "patient"
        
        # Convert request actions to ChainedIntent objects
        if not request.actions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one action is required"
            )
        
        chained_intents = []
        for action_data in request.actions:
            intent = ChainedIntent(
                action_type=action_data.get("action_type"),
                parameters=action_data.get("parameters", {}),
                confidence=action_data.get("confidence", 1.0),
                dependencies=action_data.get("dependencies", []),
                context_requirements=action_data.get("context_requirements", [])
            )
            chained_intents.append(intent)
        
        # Create ChainedAction
        chain = ChainedAction(
            primary_action=chained_intents[0],
            follow_up_actions=chained_intents[1:] if len(chained_intents) > 1 else [],
            execution_mode=ExecutionMode(request.execution_mode),
            failure_mode=FailureMode(request.failure_mode)
        )
        
        # Log the request
        logger.info(f"Executing chained actions: {len(chained_intents)} actions")
        log_audit_event(
            db=db,
            event_type="EXECUTE_CHAINED_ACTIONS",
            user_id=user_id,
            user_role=user_role,
            details={
                "action_count": len(chained_intents),
                "execution_mode": request.execution_mode,
                "failure_mode": request.failure_mode
            }
        )
        
        # Execute the chain
        chain_executor = ActionChainExecutorService(db)
        chain_result = await chain_executor.execute_chain(
            user_id=user_id,
            user_role=primary_role,
            chain=chain,
            initial_context=request.context
        )
        
        # Format response
        if chain_result.success:
            action_summaries = []
            for result in chain_result.results:
                if result.success:
                    action_summaries.append(f"✓ {result.action_type}")
                else:
                    action_summaries.append(f"✗ {result.action_type}: {result.error_message}")
            
            message = f"Successfully completed {len([r for r in chain_result.results if r.success])} of {len(chain_result.results)} actions"
        else:
            message = f"Chain execution failed: {chain_result.error_message}"
        
        return TextActionResponse(
            success=chain_result.success,
            message=message,
            action_type="chained_actions",
            data={
                "chain_id": chain_result.chain_id,
                "execution_time_ms": chain_result.execution_time_ms,
                "completed_actions": len([r for r in chain_result.results if r.success]),
                "total_actions": len(chain_result.results),
                "results": [
                    {
                        "action_type": r.action_type,
                        "success": r.success,
                        "message": r.message if r.success else r.error_message,
                        "data": r.data if r.success else None
                    }
                    for r in chain_result.results
                ],
                "final_context": chain_result.context.data
            },
            debug_info=(
                {
                    "chain": chain.dict(),
                    "chain_result": chain_result.dict()
                }
                if include_debug and "admin" in user_roles
                else None
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing chained actions: {e}", exc_info=True)
        
        # Log the error
        log_audit_event(
            db=db,
            event_type="EXECUTE_CHAINED_ACTIONS_ERROR",
            user_id=current_user.sub if current_user else None,
            user_role=current_user.role if current_user else None,
            outcome="FAILURE",
            details={"error": str(e)}
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error executing chained actions: {str(e)}"
        )
