import logging
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.api.deps import TokenPayload
from app.models.medication_request import MedicationRequestStatus
from app.schemas.medication_request import (
    MedicationRequestCreate,
    MedicationRequestResponse,
    MedicationRequestUpdate,
)
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/",
    response_model=MedicationRequestResponse,
    status_code=http_status.HTTP_201_CREATED,
    summary="Create Medication Request",
    description="Creates a new medication request.",
    responses={
        201: {"description": "Medication request created successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized"},
    },
    tags=["Medication Requests"],
)
def create_medication_request(
    background_tasks: BackgroundTasks,
    *,
    medication_request_in: MedicationRequestCreate,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> MedicationRequestResponse:
    """
    Create a new medication request.
    """
    user_id = current_user.sub
    logger.debug(
        f"Creating medication request: user={user_id}, role={current_user.role}, med={medication_request_in.medication_name}"
    )

    # Determine if this is a patient creating their own request or a clinician creating for a patient
    patient_id = None
    if "patient" in current_user.role:
        # Patient creating their own request, use their ID
        patient_id = user_id
        actor_role = "patient"
    elif "clinician" in current_user.role:
        # Clinician creating request for a patient
        if (
            not hasattr(medication_request_in, "patient_id")
            or not medication_request_in.patient_id
        ):
            logger.error(
                f"Clinician {user_id} attempted to create medication request without specifying patient_id"
            )
            log_audit_event(
                db=db,
                action="REQUEST_MEDICATION_REFILL",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="clinician",
                details={
                    "reason": "patient_id is required when a clinician creates a medication request",
                    "medication_name": medication_request_in.medication_name,
                },
            )
            raise HTTPException(
                status_code=400,
                detail="patient_id is required when a clinician creates a medication request",
            )

        # Verify clinician has permission to create requests for this patient
        clinician_db = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=user_id)
        if not clinician_db:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        assigned_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [p.id for p in assigned_patients]

        if medication_request_in.patient_id not in assigned_patient_ids:
            log_audit_event(
                db=db,
                action="REQUEST_MEDICATION_REFILL",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="clinician",
                details={
                    "reason": f"Clinician not authorized to create medication request for patient {medication_request_in.patient_id}",
                    "medication_name": medication_request_in.medication_name,
                },
            )
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Not authorized to create medication request for this patient",
            )

        patient_id = medication_request_in.patient_id
        actor_role = "clinician"
    else:
        # Admin or other role not authorized to create medication requests
        log_audit_event(
            db=db,
            action="REQUEST_MEDICATION_REFILL",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=current_user.role[0] if current_user.role else "unknown",
            details={
                "reason": "Unauthorized role for creating medication requests",
                "medication_name": medication_request_in.medication_name,
            },
        )
        raise HTTPException(
            status_code=403,
            detail="Only patients and clinicians can create medication requests",
        )

    try:
        created_request = crud.medication_request.create_medication_request(
            db=db, obj_in=medication_request_in, patient_id=patient_id
        )
    except Exception as e:
        logger.error(
            f"Error creating medication request in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="REQUEST_MEDICATION_REFILL",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role=actor_role,
            details={
                "reason": f"DB create error: {e}",
                "medication_name": medication_request_in.medication_name,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to submit medication request."
        ) from e

    logger.info(f"Patient {user_id} requested medication")
    # TODO: Publish MedicationRequestedEvent if needed
    log_audit_event(
        db=db,
        action="REQUEST_MEDICATION_REFILL",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role=actor_role,
        target_resource_id=str(created_request.id),
        details={
            "medication_name": created_request.medication_name,
            "dosage": created_request.dosage,
            "frequency": created_request.frequency,
            "duration": created_request.duration,
            "patient_id": patient_id,
        },
    )
    return created_request


@router.get(
    "/",
    response_model=list[MedicationRequestResponse],
    summary="Get Medication Requests",
    description="Retrieves medication requests with optional filtering.",
    responses={
        200: {"description": "Medication requests retrieved successfully"},
        401: {"description": "Not authenticated"},
    },
    tags=["Medication Requests"],
)
def get_medication_requests(
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
) -> list[MedicationRequestResponse]:
    """
    Get medication requests with optional filtering.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching medication requests for user_id: {user_id}")

    # Determine if user is a patient or clinician and fetch appropriate requests
    if "patient" in current_user.role:
        requests = crud.medication_request.get_multi_by_patient(
            db=db, patient_id=user_id, skip=skip, limit=limit, status=status
        )
    elif "clinician" in current_user.role:
        # Get patients associated with this clinician
        clinician_db = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=user_id)
        if not clinician_db:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        assigned_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [p.id for p in assigned_patients]

        if not assigned_patient_ids:
            return []

        requests = crud.medication_request.get_multi_by_patient_ids(
            db=db,
            patient_ids=assigned_patient_ids,
            skip=skip,
            limit=limit,
            status=status,
        )
    else:
        # Admin or other role - use the newly implemented get_multi function
        requests = crud.medication_request.get_multi(
            db=db, skip=skip, limit=limit, status=status
        )

    log_audit_event(
        db=db,
        action="GET_MEDICATION_REQUESTS",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role=current_user.role[0] if current_user.role else "unknown",
        details={
            "count": len(requests),
            "skip": skip,
            "limit": limit,
            "status": status,
        },
    )

    return requests


@router.get(
    "/{request_id}",
    response_model=MedicationRequestResponse,
    summary="Get Medication Request by ID",
    description="Retrieves a specific medication request by ID.",
    responses={
        200: {"description": "Medication request retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to view this request"},
        404: {"description": "Medication request not found"},
    },
    tags=["Medication Requests"],
)
def get_medication_request(
    request_id: UUID,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_user),
) -> MedicationRequestResponse:
    """
    Get a specific medication request by ID.
    """
    medication_request = crud.medication_request.get_medication_request_by_id(
        db, request_id=request_id
    )
    if not medication_request:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Medication request not found",
        )

    # Check authorization based on role
    if (
        "patient" in current_user.role
        and medication_request.patient_id != current_user.sub
    ):
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this medication request",
        )
    elif "clinician" in current_user.role:
        # Check if clinician is associated with the patient
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if not clinician_db:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        assigned_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [p.id for p in assigned_patients]

        if medication_request.patient_id not in assigned_patient_ids:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this medication request",
            )

    log_audit_event(
        db=db,
        action="GET_MEDICATION_REQUEST",
        outcome="SUCCESS",
        actor_user_id=str(current_user.sub),
        actor_role=current_user.role[0] if current_user.role else "unknown",
        target_resource_id=str(request_id),
    )

    return medication_request


@router.put(
    "/{request_id}/status",
    response_model=MedicationRequestResponse,
    summary="Update Medication Request Status",
    description="Updates the status of a medication request (approve/reject). Only clinicians and admins can update status.",
    responses={
        200: {"description": "Medication request status updated successfully"},
        400: {"description": "Invalid status update"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to update this request"},
        404: {"description": "Medication request not found"},
    },
    tags=["Medication Requests"],
)
def update_medication_request_status(
    request_id: UUID,
    *,
    update_data: MedicationRequestUpdate,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Only admin or clinician can update status
) -> MedicationRequestResponse:
    """
    Update the status of a medication request (approve/reject).
    Only clinicians and admins can update the status.
    """
    # Get the medication request
    medication_request = crud.medication_request.get_medication_request_by_id(
        db, request_id=request_id
    )
    if not medication_request:
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            details={
                "reason": "Medication request not found",
                "request_id": str(request_id),
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Medication request not found",
        )

    # If clinician, check if they're associated with the patient
    if "clinician" in current_user.role:
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if not clinician_db:
            log_audit_event(
                db=db,
                action="UPDATE_MEDICATION_REQUEST_STATUS",
                outcome="FAILURE",
                actor_user_id=str(current_user.sub),
                actor_role="clinician",
                target_resource_id=str(request_id),
                details={"reason": "Clinician record not found"},
            )
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        assigned_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [str(p.id) for p in assigned_patients]

        if str(medication_request.patient_id) not in assigned_patient_ids:
            log_audit_event(
                db=db,
                action="UPDATE_MEDICATION_REQUEST_STATUS",
                outcome="FAILURE",
                actor_user_id=str(current_user.sub),
                actor_role="clinician",
                target_resource_id=str(request_id),
                details={"reason": "Not authorized to update this medication request"},
            )
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this medication request",
            )

    # Validate the status update
    if not update_data.status:
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={"reason": "Status is required for update"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="Status is required for update",
        )

    # Convert string status to enum
    try:
        new_status = MedicationRequestStatus(update_data.status)
    except ValueError:
        valid_statuses = [status.value for status in MedicationRequestStatus]
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={
                "reason": f"Invalid status: {update_data.status}",
                "valid_statuses": valid_statuses,
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Valid statuses are: {', '.join(valid_statuses)}",
        )

    # Prevent updating already approved/rejected requests
    if medication_request.status in [
        MedicationRequestStatus.APPROVED,
        MedicationRequestStatus.REJECTED,
    ]:
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={
                "reason": f"Cannot update a request with status: {medication_request.status.value}",
                "current_status": medication_request.status.value,
                "requested_status": new_status.value,
            },
        )
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot update a request that has already been {medication_request.status.value.lower()}",
        )

    # Update the status
    try:
        # Update the medication request with new status and notes
        medication_request.status = new_status
        medication_request.notes = (
            update_data.notes if update_data.notes else medication_request.notes
        )
        medication_request.resolved_at = datetime.now(timezone.utc)

        # If there's a clinician_id field, set it to the current clinician's ID
        if "clinician" in current_user.role and hasattr(
            medication_request, "clinician_id"
        ):
            clinician_db = crud.clinician.get_clinician_by_clerk_id(
                db, clerk_id=current_user.sub
            )
            if clinician_db:
                medication_request.clinician_id = clinician_db.id

        db.add(medication_request)
        db.commit()
        db.refresh(medication_request)

        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="SUCCESS",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={
                "new_status": new_status.value,
                "previous_status": medication_request.status.value,
                "notes_updated": update_data.notes is not None,
            },
        )

        # TODO: Notify patient about status change if needed

        return medication_request
    except Exception as e:
        db.rollback()
        logger.error(
            f"Error updating medication request status: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION_REQUEST_STATUS",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={"reason": f"Database error: {str(e)}"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update medication request status.",
        ) from e


# ------------------------------------------------------------
# DELETE /api/v1/medication-requests/{request_id}
# ------------------------------------------------------------
@router.delete(
    "/{request_id}",
    status_code=http_status.HTTP_204_NO_CONTENT,
    summary="Delete Medication Request",
    description="Deletes a medication request by ID. Only clinicians assigned to the patient or admins can delete.",
    responses={
        204: {"description": "Medication request deleted successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to delete this request"},
        404: {"description": "Medication request not found"},
    },
    tags=["Medication Requests"],
)
def delete_medication_request(
    request_id: UUID,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(deps.get_current_admin_or_clinician),
):
    """
    Delete a medication request by ID.
    Only clinicians assigned to the patient or admins can delete.
    """
    medication_request = crud.medication_request.get_medication_request_by_id(
        db, request_id=request_id
    )
    if not medication_request:
        log_audit_event(
            db=db,
            action="DELETE_MEDICATION_REQUEST",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={"reason": "Medication request not found"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Medication request not found",
        )

    # If clinician, check if they're associated with the patient
    if "clinician" in current_user.role:
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_user.sub
        )
        if not clinician_db:
            log_audit_event(
                db=db,
                action="DELETE_MEDICATION_REQUEST",
                outcome="FAILURE",
                actor_user_id=str(current_user.sub),
                actor_role="clinician",
                target_resource_id=str(request_id),
                details={"reason": "Clinician record not found"},
            )
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )
        assigned_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [str(p.id) for p in assigned_patients]
        if str(medication_request.patient_id) not in assigned_patient_ids:
            log_audit_event(
                db=db,
                action="DELETE_MEDICATION_REQUEST",
                outcome="FAILURE",
                actor_user_id=str(current_user.sub),
                actor_role="clinician",
                target_resource_id=str(request_id),
                details={"reason": "Not authorized to delete this medication request"},
            )
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this medication request",
            )

    deleted = crud.medication_request.delete_medication_request(
        db=db, request_id=request_id
    )
    if not deleted:
        log_audit_event(
            db=db,
            action="DELETE_MEDICATION_REQUEST",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=str(request_id),
            details={"reason": "Delete operation failed"},
        )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete medication request.",
        )

    log_audit_event(
        db=db,
        action="DELETE_MEDICATION_REQUEST",
        outcome="SUCCESS",
        actor_user_id=str(current_user.sub),
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
        target_resource_id=str(request_id),
        details={
            "medication_name": medication_request.medication_name,
            "patient_id": str(medication_request.patient_id),
        },
    )
    return
