from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.schemas.auth import TokenPayload
from app.schemas.medication import (
    MedicationCreate,
    MedicationResponse,
    MedicationUpdate,
)
from app.utils.audit import log_audit_event

router = APIRouter()


@router.get("/", response_model=dict[str, Any])
def get_medications(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Allow admin or clinician to view medications
):
    """Get all medications with pagination."""
    medications = crud.medication.medication.get_multi(db, skip=skip, limit=limit)
    total_count = crud.medication.medication.count(db)

    # Convert SQLAlchemy models to Pydantic models for proper serialization
    medication_responses = [
        MedicationResponse.model_validate(
            {
                **med.__dict__,
                "associated_clinic_ids": [
                    clinic.id for clinic in getattr(med, "clinics", [])
                ],
            }
        )
        for med in medications
    ]

    log_audit_event(
        db=db,
        action="LIST_MEDICATIONS",
        outcome="SUCCESS",
        actor_user_id=current_user.sub,
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
    )

    return {
        "items": medication_responses,
        "total": total_count,
        "skip": skip,
        "limit": limit,
    }


@router.get("/{medication_id}", response_model=MedicationResponse)
def get_medication(
    medication_id: str,
    db: Session = Depends(deps.get_db),
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Allow admin or clinician to view a medication
):
    """Get a specific medication by ID."""
    medication = crud.medication.medication.get(db, id=medication_id)
    if not medication:
        log_audit_event(
            db=db,
            action="GET_MEDICATION",
            outcome="FAILURE",
            actor_user_id=current_user.sub,
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            details={"reason": "Medication not found", "medication_id": medication_id},
        )
        raise HTTPException(status_code=404, detail="Medication not found")

    log_audit_event(
        db=db,
        action="GET_MEDICATION",
        outcome="SUCCESS",
        actor_user_id=current_user.sub,
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
        target_resource_id=medication_id,
    )

    return MedicationResponse.model_validate(medication)


@router.post("/", response_model=MedicationResponse)
def create_medication(
    *,
    db: Session = Depends(deps.get_db),
    medication_in: MedicationCreate,
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Only admin or clinician can create medications
):
    """Create a new medication."""
    medication = crud.medication.medication.get_by_name(db, name=medication_in.name)
    if medication:
        log_audit_event(
            db=db,
            action="CREATE_MEDICATION",
            outcome="FAILURE",
            actor_user_id=current_user.sub,
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            details={
                "reason": "Medication with this name already exists",
                "name": medication_in.name,
            },
        )
        raise HTTPException(
            status_code=400,
            detail="A medication with this name already exists",
        )

    medication = crud.medication.medication.create(db, obj_in=medication_in)

    log_audit_event(
        db=db,
        action="CREATE_MEDICATION",
        outcome="SUCCESS",
        actor_user_id=current_user.sub,
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
        target_resource_id=str(medication.id),
        details={"name": medication.name},
    )

    return MedicationResponse.model_validate(medication)


@router.put("/{medication_id}", response_model=MedicationResponse)
def update_medication(
    *,
    db: Session = Depends(deps.get_db),
    medication_id: str,
    medication_in: MedicationUpdate,
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Only admin or clinician can update medications
):
    """Update a medication."""
    medication = crud.medication.medication.get(db, id=medication_id)
    if not medication:
        log_audit_event(
            db=db,
            action="UPDATE_MEDICATION",
            outcome="FAILURE",
            actor_user_id=current_user.sub,
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            details={"reason": "Medication not found", "medication_id": medication_id},
        )
        raise HTTPException(status_code=404, detail="Medication not found")

    medication = crud.medication.medication.update(
        db, db_obj=medication, obj_in=medication_in
    )

    log_audit_event(
        db=db,
        action="UPDATE_MEDICATION",
        outcome="SUCCESS",
        actor_user_id=current_user.sub,
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
        target_resource_id=medication_id,
        details={"name": medication.name},
    )

    return MedicationResponse.model_validate(medication)


@router.delete("/{medication_id}", response_model=MedicationResponse)
def delete_medication(
    *,
    db: Session = Depends(deps.get_db),
    medication_id: str,
    current_user: TokenPayload = Depends(
        deps.get_current_admin_or_clinician
    ),  # Only admin or clinician can delete medications
):
    """Delete a medication if not associated with any clinics."""
    medication = crud.medication.medication.get(db, id=medication_id)
    if not medication:
        log_audit_event(
            db=db,
            action="DELETE_MEDICATION",
            outcome="FAILURE",
            actor_user_id=current_user.sub,
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            details={"reason": "Medication not found", "medication_id": medication_id},
        )
        raise HTTPException(status_code=404, detail="Medication not found")

    # Check for clinic associations
    if medication.clinics and len(medication.clinics) > 0:
        log_audit_event(
            db=db,
            action="DELETE_MEDICATION",
            outcome="FAILURE",
            actor_user_id=current_user.sub,
            actor_role=(
                current_user.role[0]
                if isinstance(current_user.role, list)
                else current_user.role
            ),
            target_resource_id=medication_id,
            details={
                "reason": "Medication is associated with one or more clinics",
                "clinic_ids": [clinic.id for clinic in medication.clinics],
                "name": medication.name,
            },
        )
        raise HTTPException(
            status_code=400,
            detail="Cannot delete medication: it is associated with one or more clinics.",
        )

    medication = crud.medication.medication.remove(db, id=medication_id)

    log_audit_event(
        db=db,
        action="DELETE_MEDICATION",
        outcome="SUCCESS",
        actor_user_id=current_user.sub,
        actor_role=(
            current_user.role[0]
            if isinstance(current_user.role, list)
            else current_user.role
        ),
        target_resource_id=medication_id,
        details={"name": medication.name},
    )

    return MedicationResponse.model_validate(medication)
