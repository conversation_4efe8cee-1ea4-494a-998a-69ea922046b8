# from app.utils.audit import log_audit_event # Import audit log utility if needed later
import logging

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/",
    response_model=schemas.note.NoteRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Note for Patient",
    description="Allows an authenticated clinician to create a note associated with one of their patients.",
    tags=["Notes"],
    responses={
        201: {
            "model": schemas.note.NoteRead,
            "description": "Note created successfully",
        },
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or not authorized for this patient"
        },
        404: {"description": "Patient not found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal server error"},
    },
)
def create_note_for_patient(
    *,  # Enforce keyword-only arguments
    db: Session = Depends(deps.get_db),
    note_in: schemas.note.NoteCreate,
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.note.NoteRead:
    """
    Create a new note for a patient, ensuring the clinician is authorized.

    Args:
        db: Database session
        note_in: Note creation data
        current_clinician: Currently authenticated clinician

    Returns:
        The created note

    Raises:
        HTTPException: If authorization fails or note creation fails
    """
    clinician_id = current_clinician.id
    patient_id = note_in.patient_id
    logger.debug(
        f"Clinician {clinician_id} attempting to create note for patient {patient_id}."
    )

    # 1. Authorization Check: Verify clinician is assigned to the patient
    is_assigned = crud.clinician.is_patient_assigned_to_clinician(
        db=db, clinician_id=clinician_id, patient_id=patient_id
    )
    if not is_assigned:
        # Check if patient exists at all to return 404 vs 403
        patient_exists = crud.patient.get(db=db, id=patient_id)
        status_code = (
            status.HTTP_404_NOT_FOUND
            if not patient_exists
            else status.HTTP_403_FORBIDDEN
        )
        detail = (
            "Patient not found."
            if not patient_exists
            else "Clinician not authorized to create notes for this patient."
        )
        logger.warning(
            f"Authorization failed: Clinician {clinician_id} creating note for patient {patient_id}. Reason: {detail}"
        )
        # log_audit_event(...) # Add audit log if needed
        raise HTTPException(status_code=status_code, detail=detail)

    # 2. Create the note using CRUD function
    try:
        created_note = crud.note.create_note(
            db=db, obj_in=note_in, clinician_id=clinician_id
        )
        logger.info(
            f"Clinician {clinician_id} successfully created note {created_note.id} for patient {patient_id}."
        )
        # log_audit_event(...) # Add audit log if needed
        return created_note
    except IntegrityError as e:
        logger.error(
            f"Database integrity error creating note for patient {patient_id}",
            exc_info=True,
            extra={"clinician_id": clinician_id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid data provided for note creation.",
        )
    except Exception as e:
        logger.error(
            f"Error creating note for patient {patient_id}",
            exc_info=True,
            extra={"clinician_id": clinician_id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the note.",
        )


@router.get(
    "/by-patient/{patient_id}",
    response_model=list[schemas.note.NoteRead],
    summary="Get Notes for a Specific Patient",
    description="Retrieves notes for a specific patient, accessible only by authorized clinicians.",
    tags=["Notes", "Patients"],
    responses={
        200: {
            "model": list[schemas.note.NoteRead],
            "description": "Notes retrieved successfully",
        },
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or not authorized for this patient"
        },
        404: {"description": "Patient not found"},
        500: {"description": "Internal server error"},
    },
)
def read_notes_for_patient(
    patient_id: str,  # Keeping as str since it's a Clerk user ID
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[schemas.note.NoteRead]:
    """
    Retrieve notes for a specific patient, ensuring the clinician is authorized.

    Args:
        patient_id: UUID of the patient
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session
        current_clinician: Currently authenticated clinician

    Returns:
        List of notes for the specified patient

    Raises:
        HTTPException: If authorization fails or retrieval fails
    """
    clinician_id = current_clinician.id
    logger.debug(
        f"Clinician {clinician_id} attempting to read notes for patient {patient_id}."
    )

    try:
        # 1. Authorization Check: Verify clinician is assigned to the patient
        is_assigned = crud.clinician.is_patient_assigned_to_clinician(
            db=db, clinician_id=clinician_id, patient_id=patient_id
        )
        if not is_assigned:
            patient_exists = crud.patient.get(db=db, id=patient_id)
            status_code = (
                status.HTTP_404_NOT_FOUND
                if not patient_exists
                else status.HTTP_403_FORBIDDEN
            )
            detail = (
                "Patient not found."
                if not patient_exists
                else "Clinician not authorized to view notes for this patient."
            )
            logger.warning(
                f"Authorization failed: Clinician {clinician_id} reading notes for patient {patient_id}. Reason: {detail}"
            )
            raise HTTPException(status_code=status_code, detail=detail)

        # 2. Fetch notes using CRUD function
        notes = crud.note.get_notes_by_patient(
            db=db, patient_id=patient_id, skip=skip, limit=limit
        )
        logger.info(
            f"Clinician {clinician_id} retrieved {len(notes)} notes for patient {patient_id}."
        )
        return notes

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving notes for patient {patient_id}",
            exc_info=True,
            extra={"clinician_id": clinician_id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving the notes.",
        )


@router.get(
    "/my-notes",
    response_model=list[schemas.note.NoteRead],
    summary="Get Notes Authored by Current Clinician",
    description="Retrieves notes authored by the currently authenticated clinician.",
    tags=["Notes", "Clinicians"],
    responses={
        200: {
            "model": list[schemas.note.NoteRead],
            "description": "Notes retrieved successfully",
        },
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Internal server error"},
    },
)
def read_my_notes(
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return per page."
    ),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[schemas.note.NoteRead]:  # Changed from Any to concrete type
    """
    Retrieve notes authored by the currently authenticated clinician.

    Args:
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session
        current_clinician: Currently authenticated clinician

    Returns:
        List of notes authored by the clinician

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        clinician_id = current_clinician.id
        logger.debug(
            f"Clinician {clinician_id} requesting their authored notes. Page: skip={skip}, limit={limit}"
        )

        notes = crud.note.get_notes_by_clinician(
            db=db, clinician_id=clinician_id, skip=skip, limit=limit
        )
        logger.info(
            f"Retrieved {len(notes)} notes authored by clinician {clinician_id}."
        )
        return notes

    except Exception as e:
        logger.error(
            "Error retrieving clinician's notes",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving your notes.",
        )


@router.get(
    "/{note_id}",
    response_model=schemas.note.NoteRead,
    summary="Get Single Note by ID",
    description="Retrieves a specific note by its ID, ensuring the clinician is authorized.",
    tags=["Notes"],
    responses={
        200: {
            "model": schemas.note.NoteRead,
            "description": "Note retrieved successfully",
        },
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized for this note"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
)
def read_note_by_id(
    note_id: str,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.note.NoteRead:
    """
    Retrieve a specific note by its ID, ensuring the clinician is authorized.

    Args:
        note_id: UUID of the note
        db: Database session
        current_clinician: Currently authenticated clinician

    Returns:
        The requested note

    Raises:
        HTTPException: If authorization fails or note not found
    """
    try:
        from uuid import UUID
        note_uuid = UUID(note_id)
        
        # Get the note with relationships loaded
        note = crud.note.get_note(db=db, note_id=note_uuid)
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found.",
            )
        
        # Check if the clinician is the author of this note
        if note.clinician_id != current_clinician.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not authorized to view this note.",
            )
        
        logger.info(f"Clinician {current_clinician.id} retrieved note {note_id}.")
        return note
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid note ID format.",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving note {note_id}",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving the note.",
        )


@router.put(
    "/{note_id}",
    response_model=schemas.note.NoteRead,
    summary="Update Note",
    description="Updates a specific note, ensuring the clinician is the author.",
    tags=["Notes"],
    responses={
        200: {
            "model": schemas.note.NoteRead,
            "description": "Note updated successfully",
        },
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized for this note"},
        404: {"description": "Note not found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal server error"},
    },
)
def update_note(
    note_id: str,
    note_update: schemas.note.NoteUpdate,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.note.NoteRead:
    """
    Update a specific note, ensuring the clinician is the author.

    Args:
        note_id: UUID of the note
        note_update: Note update data
        db: Database session
        current_clinician: Currently authenticated clinician

    Returns:
        The updated note

    Raises:
        HTTPException: If authorization fails, note not found, or update fails
    """
    try:
        from uuid import UUID
        note_uuid = UUID(note_id)
        
        # Get the existing note
        existing_note = crud.note.get_note(db=db, note_id=note_uuid)
        if not existing_note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found.",
            )
        
        # Check if the clinician is the author of this note
        if existing_note.clinician_id != current_clinician.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not authorized to update this note.",
            )
        
        # Update the note
        updated_note = crud.note.update_note(
            db=db, db_obj=existing_note, obj_in=note_update
        )
        
        logger.info(f"Clinician {current_clinician.id} updated note {note_id}.")
        return updated_note
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid note ID format.",
        )
    except HTTPException:
        raise
    except IntegrityError as e:
        logger.error(
            f"Database integrity error updating note {note_id}",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid data provided for note update.",
        )
    except Exception as e:
        logger.error(
            f"Error updating note {note_id}",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the note.",
        )


@router.delete(
    "/{note_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Note",
    description="Deletes a specific note, ensuring the clinician is the author.",
    tags=["Notes"],
    responses={
        204: {"description": "Note deleted successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized for this note"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
)
def delete_note(
    note_id: str,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
):
    """
    Delete a specific note, ensuring the clinician is the author.

    Args:
        note_id: UUID of the note
        db: Database session
        current_clinician: Currently authenticated clinician

    Raises:
        HTTPException: If authorization fails, note not found, or deletion fails
    """
    try:
        from uuid import UUID
        note_uuid = UUID(note_id)
        
        # Get the existing note
        existing_note = crud.note.get_note(db=db, note_id=note_uuid)
        if not existing_note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found.",
            )
        
        # Check if the clinician is the author of this note
        if existing_note.clinician_id != current_clinician.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not authorized to delete this note.",
            )
        
        # Delete the note
        crud.note.remove_note(db=db, note_id=note_uuid)
        
        logger.info(f"Clinician {current_clinician.id} deleted note {note_id}.")
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid note ID format.",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error deleting note {note_id}",
            exc_info=True,
            extra={"clinician_id": current_clinician.id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the note.",
        )
