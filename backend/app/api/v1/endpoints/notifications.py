import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/",
    response_model=list[schemas.notification.NotificationRead],
    summary="Get Notifications (Clinician)",
    description="Retrieves a paginated list of notifications for the authenticated clinician.",
    tags=["Notifications"],
    responses={
        200: {"description": "Notifications retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Internal server error"},
    },
)
def read_notifications(
    is_read: Optional[bool] = Query(
        None, description="Filter notifications by read status (true or false)."
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        20, ge=1, le=200, description="Maximum number of records to return per page."
    ),  # Default 20 for feed
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> list[schemas.notification.NotificationRead]:
    """
    Retrieve notifications for the authenticated clinician.
    """
    try:
        clinician_id = current_clinician.id  # This is already a string (Clerk user ID)
        logger.debug(
            f"Clinician {clinician_id} requesting notifications. Filters: is_read={is_read}. Page: skip={skip}, limit={limit}"
        )

        notifications = crud.notification.get_notifications_by_recipient(
            db=db,
            recipient_clinician_id=clinician_id,  # Passing string ID
            is_read=is_read,
            skip=skip,
            limit=limit,
        )

        logger.debug(
            f"Found {len(notifications)} notifications for clinician {clinician_id}"
        )
        return notifications

    except Exception as e:
        logger.error(
            "Error retrieving notifications",
            exc_info=True,
            extra={"clinician_id": clinician_id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications",
        )


@router.patch(
    "/{notification_id}/read",
    response_model=schemas.notification.NotificationRead,
    summary="Mark Notification as Read",
    description="Marks a specific notification as read for the authenticated clinician.",
    tags=["Notifications"],
    responses={
        200: {"description": "Notification marked as read successfully"},
        401: {"description": "Not authenticated"},
        403: {
            "description": "User is not a clinician or notification does not belong to user"
        },
        404: {"description": "Notification not found"},
        500: {"description": "Internal server error"},
    },
)
def mark_notification_read(
    notification_id: UUID,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.notification.NotificationRead:
    """
    Mark a specific notification as read.
    """
    try:
        clinician_id = current_clinician.id  # This is already a string (Clerk user ID)
        logger.debug(
            f"Clinician {clinician_id} attempting to mark notification {notification_id} as read."
        )

        # Verify notification exists and belongs to clinician
        notification = crud.notification.get_notification_for_clinician(
            db=db,
            notification_id=notification_id,
            clinician_id=clinician_id,  # Passing string ID
        )

        if not notification:
            logger.warning(
                f"Notification {notification_id} not found or not accessible by clinician {clinician_id}."
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Notification not found"
            )

        # Mark as read using CRUD function
        updated_notification = crud.notification.mark_notification_as_read(
            db=db, notification_id=notification_id
        )

        if not updated_notification:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update notification status",
            )

        logger.info(
            f"Clinician {clinician_id} successfully marked notification {notification_id} as read."
        )
        return updated_notification

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error marking notification as read",
            exc_info=True,
            extra={
                "clinician_id": clinician_id,
                "notification_id": str(notification_id),
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the notification",
        )


@router.post(
    "/read-all",
    response_model=schemas.notification.MarkAllReadResponse,
    summary="Mark All Notifications as Read",
    description="Marks all unread notifications for the authenticated clinician as read.",
    tags=["Notifications"],
    responses={
        200: {"description": "Notifications marked as read successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician"},
        500: {"description": "Failed to update notifications"},
    },
)
def mark_all_notifications_read(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> schemas.notification.MarkAllReadResponse:
    """
    Mark all unread notifications for the authenticated clinician as read.
    """
    try:
        clinician_id = current_clinician.id  # This is already a string (Clerk user ID)
        logger.debug(
            f"Clinician {clinician_id} attempting to mark all notifications as read."
        )

        updated_count = crud.notification.mark_all_notifications_as_read(
            db=db,
            recipient_clinician_id=clinician_id,  # Passing string ID
        )

        logger.info(
            f"Clinician {clinician_id} marked {updated_count} notifications as read."
        )
        return schemas.notification.MarkAllReadResponse(updated_count=updated_count)

    except Exception as e:
        logger.error(
            "Error marking all notifications as read",
            exc_info=True,
            extra={"clinician_id": clinician_id, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notifications",
        )
