import logging
from typing import Any, Optional, Dict
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_

from app import crud, models, schemas
from app.api import deps
from app.core.audit_logger import AuditLogEvent, AuditLogger

router = APIRouter()
logger = logging.getLogger(__name__)
audit_logger = AuditLogger()


@router.get("/", response_model=Dict[str, Any])
def get_patient_alerts(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    patient_id: Optional[str] = Query(None, description="Filter by specific patient ID"),
    severity: Optional[str] = Query(None, description="Filter by severity level"),
    status: Optional[str] = Query(None, description="Filter by status"),
    alert_type: Optional[str] = Query(None, description="Filter by alert type"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of records to return"),
) -> Dict[str, Any]:
    """
    Get patient alerts for the authenticated clinician with pagination and filtering.
    Returns both the alerts and total count for pagination.
    """
    try:
        logger.info(
            f"API: Clinician {current_clinician.id} requesting patient alerts. "
            f"Filters: patient_id={patient_id}, severity={severity}, status={status}, alert_type={alert_type}"
        )
        
        # Get all patient IDs for this clinician
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=current_clinician.id, limit=10000
        )
        patient_ids = [p.id for p in clinician_patients]
        
        # Build the query
        if patient_id:
            # If specific patient requested, verify clinician has access
            if patient_id not in patient_ids:
                logger.warning(
                    f"Clinician {current_clinician.id} attempted to access alerts for "
                    f"non-associated patient {patient_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to view alerts for this patient"
                )
            # Filter by specific patient
            query = db.query(models.PatientAlert).filter(
                models.PatientAlert.patient_id == patient_id
            )
        else:
            # Get alerts for all associated patients
            query = db.query(models.PatientAlert).filter(
                models.PatientAlert.patient_id.in_(patient_ids)
            )
        
        # Apply filters
        if severity:
            query = query.filter(models.PatientAlert.severity == severity)
        if status:
            query = query.filter(models.PatientAlert.status == status)
        if alert_type:
            query = query.filter(models.PatientAlert.alert_type == alert_type)
        
        # Get total count before pagination
        total = query.count()
        
        # Apply pagination and get results with eager loading
        alerts = (
            query.options(joinedload(models.PatientAlert.patient))
            .order_by(models.PatientAlert.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
        
        # Convert to response format
        alert_list = []
        for alert in alerts:
            alert_dict = {
                "id": str(alert.id),
                "patient_id": alert.patient_id,
                "patient": {
                    "id": alert.patient.id,
                    "first_name": alert.patient.first_name,
                    "last_name": alert.patient.last_name,
                    "email": alert.patient.email,
                } if alert.patient else None,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "description": alert.description,
                "status": alert.status,
                "created_at": alert.created_at.isoformat(),
                "updated_at": alert.updated_at.isoformat(),
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
                "resolved_by_clinician_id": alert.resolved_by_clinician_id,
            }
            alert_list.append(alert_dict)
        
        logger.debug(f"Found {len(alerts)} patient alerts (total: {total})")
        
        # Log the access
        audit_logger.log(
            event=AuditLogEvent.VIEW_PATIENT_ALERTS,
            user_id=str(current_clinician.id),
            user_type="clinician",
            resource_id=None,
            details={
                "filters": {
                    "patient_id": patient_id,
                    "severity": severity,
                    "status": status,
                    "alert_type": alert_type,
                },
                "pagination": {
                    "skip": skip,
                    "limit": limit,
                    "returned": len(alerts),
                    "total": total,
                },
            },
        )
        
        return {
            "items": alert_list,
            "total": total,
            "skip": skip,
            "limit": limit,
        }
        
    except Exception as e:
        logger.error(
            "Error fetching patient alerts",
            exc_info=True,
            extra={
                "clinician_id": current_clinician.id,
                "severity": severity,
                "status": status,
                "alert_type": alert_type,
                "skip": skip,
                "limit": limit,
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve patient alerts.",
        )


@router.patch("/{alert_id}", response_model=schemas.patient_alert.PatientAlertRead)
def update_patient_alert(
    alert_id: UUID,
    alert_update: schemas.patient_alert.PatientAlertUpdate,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Update a patient alert status.
    Clinician must be associated with the patient who has the alert.
    """
    try:
        # Get the alert
        alert = crud.patient_alert.get(db=db, alert_id=alert_id)
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient alert not found",
            )
        
        # Verify clinician has access to this patient's data
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=current_clinician.id
        )
        patient_ids = [p.id for p in clinician_patients]
        
        if alert.patient_id not in patient_ids:
            logger.warning(
                f"Clinician {current_clinician.id} attempted unauthorized access to alert "
                f"{alert_id} for non-associated patient {alert.patient_id}"
            )
            audit_logger.log(
                event=AuditLogEvent.UNAUTHORIZED_ACCESS,
                user_id=str(current_clinician.id),
                user_type="clinician",
                resource_id=str(alert_id),
                details={
                    "resource_type": "patient_alert",
                    "patient_id": str(alert.patient_id),
                    "action": "update",
                },
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this patient alert",
            )
        
        # Handle status update with resolved fields
        if alert_update.status == schemas.patient_alert.PatientAlertStatus.RESOLVED:
            updated_alert = crud.patient_alert.update_status(
                db=db,
                alert_id=alert_id,
                status=alert_update.status,
                resolved_by_clinician_id=current_clinician.id,
            )
        else:
            # For other updates, use the general update method
            updated_alert = crud.patient_alert.update(
                db=db,
                db_obj=alert,
                obj_in=alert_update,
            )
        
        # Log the successful update
        audit_logger.log(
            event=AuditLogEvent.UPDATE_PATIENT_ALERT,
            user_id=str(current_clinician.id),
            user_type="clinician",
            resource_id=str(alert_id),
            details={
                "patient_id": str(alert.patient_id),
                "old_status": alert.status,
                "new_status": updated_alert.status,
                "severity": alert.severity,
            },
        )
        
        return updated_alert
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error updating patient alert {alert_id}",
            exc_info=True,
            extra={
                "clinician_id": current_clinician.id,
                "alert_id": str(alert_id),
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update patient alert.",
        )


@router.get("/{alert_id}", response_model=schemas.patient_alert.PatientAlertRead)
def get_patient_alert_detail(
    alert_id: UUID,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Get detailed information about a specific patient alert.
    Clinician must be associated with the patient who has the alert.
    """
    try:
        # Get the alert with patient data
        alert = (
            db.query(models.PatientAlert)
            .options(joinedload(models.PatientAlert.patient))
            .filter(models.PatientAlert.id == alert_id)
            .first()
        )
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient alert not found",
            )
        
        # Verify clinician has access to this patient's data
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=current_clinician.id
        )
        patient_ids = [p.id for p in clinician_patients]
        
        if alert.patient_id not in patient_ids:
            logger.warning(
                f"Clinician {current_clinician.id} attempted unauthorized access to alert "
                f"{alert_id} for non-associated patient {alert.patient_id}"
            )
            audit_logger.log(
                event=AuditLogEvent.UNAUTHORIZED_ACCESS,
                user_id=str(current_clinician.id),
                user_type="clinician",
                resource_id=str(alert_id),
                details={
                    "resource_type": "patient_alert",
                    "patient_id": str(alert.patient_id),
                    "action": "view",
                },
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this patient alert",
            )
        
        # Log the successful retrieval
        audit_logger.log(
            event=AuditLogEvent.VIEW_PATIENT_ALERT,
            user_id=str(current_clinician.id),
            user_type="clinician",
            resource_id=str(alert_id),
            details={
                "patient_id": str(alert.patient_id),
                "severity": alert.severity,
                "status": alert.status,
                "alert_type": alert.alert_type,
            },
        )
        
        return alert
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error fetching patient alert {alert_id}",
            exc_info=True,
            extra={
                "clinician_id": current_clinician.id,
                "alert_id": str(alert_id),
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve patient alert details.",
        )