"""
Patient Education Assignment API endpoints.

This module handles the assignment workflow for educational materials:
- Clinician assignment of materials to patients
- Patient viewing of assigned materials
- Progress tracking and completion
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.crud.crud_patient_education_assignment import patient_education_assignment as crud_assignment
from app.crud.crud_education_progress import education_progress as crud_progress
from app.crud.crud_event_log import event_log as crud_event_log
from app.schemas.patient_education_assignment import (
    PatientEducationAssignment,
    PatientEducationAssignmentCreate,
    PatientEducationAssignmentUpdate,
    PatientEducationAssignmentWithMaterial,
    AssignmentStatus,
    AssignmentPriority,
)
from app.schemas.education_progress import (
    EducationProgress,
    EducationProgressCreate,
    EducationProgressUpdate,
)
from app.schemas.auth import TokenPayload
from app.schemas.event_log import EventLogCreate


router = APIRouter()


def get_user_role(user: TokenPayload) -> str:
    """Helper function to extract role from user token (handles string or list)."""
    if isinstance(user.role, str):
        return user.role
    elif isinstance(user.role, list) and user.role:
        return user.role[0]
    else:
        return "patient"  # Default role


def get_user_clinic_id(user: TokenPayload) -> Optional[str]:
    """Helper function to extract clinic_id from user token."""
    if hasattr(user, 'public_metadata') and user.public_metadata:
        return user.public_metadata.get("associated_clinic_id")
    return None


@router.post("/assignments", response_model=PatientEducationAssignment)
def assign_material_to_patient(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    assignment_in: PatientEducationAssignmentCreate,
) -> PatientEducationAssignment:
    """
    Assign an education material to a patient.
    
    Only clinicians and admins can make assignments.
    """
    user_role = get_user_role(current_user)
    if user_role not in ["clinician", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only clinicians and admins can assign materials"
        )
    
    # Set the assigning clinician
    assignment_data = assignment_in.dict()
    assignment_data["assigned_by"] = current_user.sub
    
    # Validate that the material exists and is accessible
    if not crud_assignment.validate_material_assignment(
        db=db,
        material_id=assignment_data["material_id"],
        patient_id=assignment_data["patient_id"],
        clinician_id=current_user.sub,
        user_role=current_user.role,
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot assign this material to the patient"
        )
    
    # Check for existing assignment
    existing = crud_assignment.get_by_patient_and_material(
        db=db,
        patient_id=assignment_data["patient_id"],
        material_id=assignment_data["material_id"],
    )
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Material already assigned to this patient"
        )
    
    assignment = crud_assignment.create(db=db, obj_in=assignment_data)
    
    # Log the assignment event
    event_log_data = EventLogCreate(
        actor_user_id=current_user.sub,
        actor_role=user_role,
        action="EDUCATION_MATERIAL_ASSIGNED",
        target_resource_type="education_assignment",
        target_resource_id=str(assignment.id),
        patient_id=assignment.patient_id,
        metadata={
            "material_id": str(assignment.material_id),
            "priority": assignment.priority.value,
            "due_date": assignment.due_date.isoformat() if assignment.due_date else None,
        },
        outcome="success"
    )
    crud_event_log.create(db=db, obj_in=event_log_data)
    
    return assignment


@router.get("/assignments", response_model=List[PatientEducationAssignmentWithMaterial])
def list_assignments(
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    patient_id: Optional[str] = None,
    material_id: Optional[str] = None,
    status: Optional[AssignmentStatus] = None,
    priority: Optional[AssignmentPriority] = None,
    assigned_by: Optional[str] = None,
) -> List[PatientEducationAssignment]:
    """
    List education assignments.
    
    Access control:
    - Patients: see only their own assignments
    - Clinicians: see assignments they made + their clinic's patients
    - Admins: see all assignments
    """
    user_role = get_user_role(current_user)
    if user_role == "patient":
        # Patients can only see their own assignments
        patient_id = current_user.sub
    
    assignments = crud_assignment.get_multi_filtered(
        db=db,
        skip=skip,
        limit=limit,
        patient_id=patient_id,
        material_id=material_id,
        status=status,
        priority=priority,
        assigned_by=assigned_by,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user),
    )
    
    return assignments


@router.get("/assignments/{assignment_id}", response_model=PatientEducationAssignment)
def get_assignment(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    assignment_id: UUID,
) -> PatientEducationAssignment:
    """
    Get a specific assignment by ID.
    """
    assignment = crud_assignment.get(db=db, id=assignment_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    # Check access permissions
    if not crud_assignment.can_access_assignment(
        assignment=assignment,
        user_id=current_user.sub,
        user_role=current_user.role,
        user_clinic_id=get_user_clinic_id(current_user),
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this assignment"
        )
    
    return assignment


@router.put("/assignments/{assignment_id}", response_model=PatientEducationAssignment)
def update_assignment(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    assignment_id: UUID,
    assignment_in: PatientEducationAssignmentUpdate,
) -> PatientEducationAssignment:
    """
    Update an assignment.
    
    Clinicians can update assignments they made.
    Patients can update status (viewed, completed).
    """
    assignment = crud_assignment.get(db=db, id=assignment_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    # Check update permissions
    user_role = get_user_role(current_user)
    
    if user_role == "patient":
        # Patients can only update their own assignments and only certain fields
        if assignment.patient_id != current_user.sub:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Can only update your own assignments"
            )
        
        # Restrict patient updates to status changes only
        allowed_updates = {"status"}
        update_data = {k: v for k, v in assignment_in.dict(exclude_unset=True).items() if k in allowed_updates}
        
    elif user_role in ["clinician", "admin"]:
        # Clinicians can update assignments they made or for their clinic's patients
        if not crud_assignment.can_modify_assignment(
            assignment=assignment,
            user_id=current_user.sub,
            user_role=user_role,
            user_clinic_id=get_user_clinic_id(current_user),
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this assignment"
            )
        
        update_data = assignment_in.dict(exclude_unset=True)
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    assignment = crud_assignment.update(db=db, db_obj=assignment, obj_in=update_data)
    
    # Log status changes
    if "status" in update_data:
        action_map = {
            "viewed": "EDUCATION_MATERIAL_VIEWED",
            "in_progress": "EDUCATION_MATERIAL_VIEWED",
            "completed": "EDUCATION_ASSIGNMENT_COMPLETED"
        }
        
        if update_data["status"] in action_map:
            event_log_data = EventLogCreate(
                actor_user_id=current_user.sub,
                actor_role=user_role,
                action=action_map[update_data["status"]],
                target_resource_type="education_assignment",
                target_resource_id=str(assignment.id),
                patient_id=assignment.patient_id,
                metadata={
                    "material_id": str(assignment.material_id),
                    "new_status": update_data["status"],
                    "previous_status": assignment.status.value if hasattr(assignment.status, 'value') else assignment.status
                },
                outcome="success"
            )
            crud_event_log.create(db=db, obj_in=event_log_data)
    
    return assignment


@router.delete("/assignments/{assignment_id}")
def delete_assignment(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    assignment_id: UUID,
) -> dict:
    """
    Delete an assignment.
    
    Only the assigning clinician or admins can delete assignments.
    """
    user_role = get_user_role(current_user)
    if user_role == "patient":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Patients cannot delete assignments"
        )
    
    assignment = crud_assignment.get(db=db, id=assignment_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    if not crud_assignment.can_modify_assignment(
        assignment=assignment,
        user_id=current_user.sub,
        user_role=user_role,
        user_clinic_id=get_user_clinic_id(current_user),
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this assignment"
        )
    
    crud_assignment.remove(db=db, id=assignment_id)
    return {"message": "Assignment deleted successfully"}


@router.post("/progress", response_model=EducationProgress)
def create_or_update_progress(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    progress_in: EducationProgressCreate,
) -> EducationProgress:
    """
    Create or update progress tracking for an assignment.
    
    Patients create their own progress records when viewing materials.
    """
    # Validate assignment exists and patient has access
    assignment = crud_assignment.get(db=db, id=progress_in.assignment_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    user_role = get_user_role(current_user)
    if user_role == "patient":
        if assignment.patient_id != current_user.sub:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Can only track progress for your own assignments"
            )
        # Set patient_id from the current user
        progress_data = progress_in.dict()
        progress_data["patient_id"] = current_user.sub
    else:
        # Clinicians and admins can create progress for any patient
        progress_data = progress_in.dict()
    
    # Check if progress record already exists
    existing_progress = crud_progress.get_by_assignment(
        db=db,
        assignment_id=progress_in.assignment_id,
        patient_id=progress_data["patient_id"],
    )
    
    if existing_progress:
        # Update existing progress
        update_data = {k: v for k, v in progress_data.items() if k not in ["assignment_id", "patient_id", "material_id"]}
        progress = crud_progress.update(db=db, db_obj=existing_progress, obj_in=update_data)
    else:
        # Create new progress record
        progress = crud_progress.create(db=db, obj_in=progress_data)
    
    # Log progress update event
    event_log_data = EventLogCreate(
        actor_user_id=current_user.sub,
        actor_role=user_role,
        action="EDUCATION_PROGRESS_UPDATED",
        target_resource_type="education_progress",
        target_resource_id=str(progress.id),
        patient_id=progress.patient_id,
        metadata={
            "assignment_id": str(progress.assignment_id),
            "material_id": str(progress.material_id),
            "progress_percentage": progress.progress_percentage,
            "time_spent_minutes": progress.time_spent_minutes,
            "is_new": existing_progress is None
        },
        outcome="success"
    )
    crud_event_log.create(db=db, obj_in=event_log_data)
    
    # Update assignment status based on progress
    if progress.progress_percentage >= 100 and assignment.status != AssignmentStatus.COMPLETED:
        crud_assignment.update(
            db=db,
            db_obj=assignment,
            obj_in={"status": AssignmentStatus.COMPLETED}
        )
    elif progress.progress_percentage > 0 and assignment.status == AssignmentStatus.ASSIGNED:
        crud_assignment.update(
            db=db,
            db_obj=assignment,
            obj_in={"status": AssignmentStatus.IN_PROGRESS}
        )
    
    return progress


@router.get("/progress/{assignment_id}", response_model=EducationProgress)
def get_progress(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    assignment_id: UUID,
) -> EducationProgress:
    """
    Get progress for a specific assignment.
    """
    assignment = crud_assignment.get(db=db, id=assignment_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    # Check access permissions
    user_role = get_user_role(current_user)
    if user_role == "patient" and assignment.patient_id != current_user.sub:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only view progress for your own assignments"
        )
    
    progress = crud_progress.get_by_assignment(
        db=db,
        assignment_id=assignment_id,
        patient_id=assignment.patient_id,
    )
    
    if not progress:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Progress record not found"
        )
    
    return progress


@router.get("/patient/{patient_id}/dashboard")
def get_patient_education_dashboard(
    *,
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    patient_id: str,
) -> dict:
    """
    Get education dashboard data for a patient.
    
    Includes assigned materials, progress statistics, and recommendations.
    """
    # Access control
    user_role = get_user_role(current_user)
    if user_role == "patient":
        if patient_id != current_user.sub:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Can only view your own dashboard"
            )
    elif user_role not in ["clinician", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    dashboard_data = crud_assignment.get_patient_dashboard(
        db=db,
        patient_id=patient_id,
        requesting_user_role=user_role,
        requesting_user_clinic_id=get_user_clinic_id(current_user),
    )
    
    return dashboard_data


@router.get("/analytics/assignments")
def get_assignment_analytics(
    db: Session = Depends(get_db),
    current_user: TokenPayload = Depends(get_current_user),
    clinic_id: Optional[UUID] = None,
) -> dict:
    """
    Get analytics for education assignments.
    
    Admins see global stats, clinicians see clinic-specific stats.
    """
    user_role = get_user_role(current_user)
    if user_role not in ["clinician", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # For clinicians, limit to their clinic
    if user_role == "clinician":
        clinic_id = get_user_clinic_id(current_user)
    
    analytics = crud_assignment.get_analytics(
        db=db,
        clinic_id=clinic_id,
        user_role=user_role,
    )
    
    return analytics