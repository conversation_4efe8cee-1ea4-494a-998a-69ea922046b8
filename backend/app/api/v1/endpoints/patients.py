# Standard Library Imports
import datetime
import json  # Added missing import used later
import logging
import mimetypes
import uuid
from typing import Any, Optional
from uuid import UUID

# Third-Party Imports
from clerk_backend_api import models as clerk_models
from fastapi import (
    <PERSON>Router,
    BackgroundTasks,
    Depends,
    File,
    Form,
    HTTPException,
    Path,
    Query,
    Request,  # Added Request import
    UploadFile,
    status,
)
from fastapi.responses import StreamingResponse
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

# Internal Imports
from app import crud, schemas
from app.api import deps
from app.core.auth_utils import clerk
from app.core.config import settings
from app.core.storage import blob_service_client, upload_profile_photo
from app.models.appointment import Appointment
from app.models.chat_message import MessageSenderType
from app.schemas.appointment import PaginatedAppointmentResponse
from app.schemas.appointment_request import (
    PaginatedAppointmentRequestResponse,
)
from app.schemas.auth import TokenPayload
from app.schemas.chat import (
    ChatMessageCreateInternal,
    ChatMessageRequest,
    ChatMessageResponse,
)
from app.schemas.events import (
    ChatMessageSentEvent,
    SideEffectReportedEvent,
)
from app.schemas.invitation import (
    PatientInvitationRequest,
    PatientInvitationResponse,
)
from app.schemas.medication import MedicationResponse
from app.schemas.medication_request import (
    MedicationRequestCreate,
    MedicationRequestResponse,
    PaginatedMedicationRequestResponse,  # Import the new paginated schema
)
from app.schemas.patient import PatientProfileResponse, PatientUpdate
from app.schemas.side_effect_report import (
    PaginatedSideEffectReportResponse,  # Add this import
    SideEffectReportCreate,
    SideEffectReportResponse,
    SideEffectReportUpdate,
    SideEffectSeveritySummary,  # Import the new summary schema
)
from app.schemas.weight_log import (
    PaginatedWeightLogResponse,  # Add this import
    WeightLogCreate,
    WeightLogEntryResponse,
    WeightLogRequest,
    WeightLogResponse,
    WeightLogUpdate,
)
from app.schemas.goal_weight import (
    GoalWeightUpdate,
    GoalWeightResponse,
    GoalWeightProgressSummary,
)
from app.services.chat_agent import (
    AgentProcessingError,
    ContextRetrievalError,
    GuardrailViolationError,
    LLMConnectionError,
    process_chat_message,
)
from app.utils import audit
from app.utils.audit import log_audit_event
from app.utils.event_bus import publish_event

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/me/medications",
    response_model=list[MedicationResponse],
    summary="Get Medications for Patient's Clinic",
    description="Retrieves all medications associated with the patient's current clinic. Patient role only.",
    tags=["Medications"],
)
def get_clinic_medications_for_patient(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
):
    """
    Retrieve all medications for the patient's currently associated clinic.
    """
    user_id = current_user.sub
    patient = crud.patient.get(db=db, id=user_id)
    if not patient:
        log_audit_event(
            db=db,
            action="GET_CLINIC_MEDICATIONS_FOR_PATIENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={"reason": "Patient not found"},
        )
        raise HTTPException(status_code=404, detail="Patient not found.")

    # --- BEGIN DEBUG LOGGING ---
    logger.info(f"Patient found: {patient.id}, Email: {patient.email}")
    logger.info(f"Patient associated_clinic_id: {patient.associated_clinic_id}")
    try:
        # Attempt to access the relationship to see if it loads or is None
        clinic_obj = patient.associated_clinic
        logger.info(
            f"Patient associated_clinic object (type: {type(clinic_obj)}): {clinic_obj}"
        )
        if clinic_obj:
            logger.info(
                f"Associated clinic details: ID={clinic_obj.id}, Name={clinic_obj.name}"
            )
    except Exception as e:
        logger.error(
            f"Error accessing patient.associated_clinic relationship: {e}",
            exc_info=True,
        )
        clinic_obj = None  # Ensure clinic_obj is defined for the next check
    # --- END DEBUG LOGGING ---

    # Modified check to use logged object and add pre-exception logging
    if not patient.associated_clinic_id or not clinic_obj:
        log_audit_event(
            db=db,
            action="GET_CLINIC_MEDICATIONS_FOR_PATIENT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={"reason": "No associated clinic"},
        )
        # --- DEBUG: Log before raising exception ---
        logger.warning(
            f"Raising 404: associated_clinic_id={patient.associated_clinic_id}, clinic_obj exists={clinic_obj is not None}"
        )
        # --- END DEBUG ---
        raise HTTPException(
            status_code=404, detail="No associated clinic for this patient."
        )

    # Use the potentially loaded clinic_obj
    medications = clinic_obj.medications or []

    log_audit_event(
        db=db,
        action="GET_CLINIC_MEDICATIONS_FOR_PATIENT",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(patient.associated_clinic_id),
        details={"count": len(medications)},
    )

    return [MedicationResponse.model_validate(med) for med in medications]


@router.get(
    "/me",
    response_model=PatientProfileResponse,
    summary="Get Current Patient Profile",
    description="Retrieves the profile information for the currently authenticated patient.",
    responses={
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
        404: {"description": "Patient profile not found for authenticated user"},
    },
    tags=["Patients"],
)
async def read_patient_me(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> PatientProfileResponse:
    """
    Get own patient profile.
    """
    user_id = current_user.sub
    logger.debug(f"Attempting to fetch profile for user_id: {user_id}")

    patient = crud.patient.get(db=db, id=user_id)
    if not patient:
        logger.warning(f"Patient profile not found for user_id: {user_id}")
        log_audit_event(
            db=db,
            action="GET_PATIENT_PROFILE",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={"reason": "Patient not found"},
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient profile not found for the authenticated user.",
        )

    logger.debug(f"Found profile data for patient: {patient.email}")
    # TEMP DEBUG: log serialized response body
    logger.info(f"Returning patient profile: {patient}")

    log_audit_event(
        db=db,
        action="GET_PATIENT_PROFILE",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(user_id),
    )
    return patient


@router.put(
    "/me",
    response_model=PatientProfileResponse,
    summary="Update Current Patient Profile",
    description="Updates the profile information (email, phone, height, photo) for the currently authenticated patient.",
    responses={
        200: {"description": "Profile updated successfully"},
        400: {"description": "Invalid input data or JSON parsing error"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
        404: {"description": "Patient profile not found for authenticated user"},
        500: {"description": "Failed to upload profile photo"},
    },
    tags=["Patients"],
)
async def update_patient_me(
    profile_update: str = Form(
        None, description="JSON string with profile update data"
    ),
    current_user: TokenPayload = Depends(deps.get_current_patient),
    profile_photo: UploadFile = File(None, description="Optional profile photo upload"),
    db: Session = Depends(deps.get_db),
) -> PatientProfileResponse:
    """
    Update own patient profile. Accepts JSON string in 'profile_update' form field and optional file upload for photo.
    """

    user_id = current_user.sub
    logger.debug(f"Attempting to update profile for user_id: {user_id}")

    # Debug: Log the raw profile_update string
    logger.info(f"Raw profile_update string received: {profile_update}")

    # Parse the JSON string into a dictionary
    update_data_dict = {}
    if profile_update:
        try:
            update_data = json.loads(profile_update)
            # Debug: Log the parsed JSON before validation
            logger.info(f"Parsed JSON before validation: {update_data}")

            # Validate against PatientUpdate schema
            profile_update_obj = PatientUpdate(**update_data)
            update_data_dict = profile_update_obj.model_dump(exclude_unset=True)
            logger.debug(f"Received update data: {update_data_dict}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in profile_update: {e}")
            raise HTTPException(
                status_code=400, detail=f"Invalid JSON format: {str(e)}"
            ) from e
        except Exception as e:
            logger.error(f"Error validating profile update data: {e}")
            raise HTTPException(
                status_code=400, detail=f"Invalid profile data: {str(e)}"
            ) from e

    # --- Add uploaded photo URL to the update data --- START
    uploaded_photo_url: Optional[str] = None
    if profile_photo:
        logger.debug(
            f"Received profile photo upload: {profile_photo.filename}, content_type: {profile_photo.content_type}"
        )
        try:
            uploaded_photo_url = await upload_profile_photo(
                file=profile_photo, patient_id=str(user_id)
            )
            if uploaded_photo_url:
                logger.info(
                    f"Successfully uploaded photo to Azure. URL: {uploaded_photo_url}"
                )
                # Add the URL to the data dictionary to be saved in the database
                update_data_dict["photo_url"] = uploaded_photo_url
                log_audit_event(
                    db=db,
                    action="UPDATE_PATIENT_PROFILE_PHOTO",
                    outcome="SUCCESS",
                    actor_user_id=str(user_id),
                    actor_role="patient",
                    target_resource_id=str(user_id),
                    details={
                        "filename": profile_photo.filename,
                        "content_type": profile_photo.content_type,
                        "photo_url": uploaded_photo_url,
                    },
                )
            else:
                logger.error(f"Azure photo upload failed for user_id: {user_id}")
                # Log failure, but don't necessarily raise 500 unless required
                log_audit_event(
                    db=db,
                    action="UPDATE_PATIENT_PROFILE_PHOTO",
                    outcome="FAILURE",
                    actor_user_id=str(user_id),
                    actor_role="patient",
                    target_resource_id=str(user_id),
                    details={"reason": "Upload function returned None"},
                )

        except Exception as e:
            logger.error(
                f"Error during Azure photo upload for user_id {user_id}: {e}",
                exc_info=True,
            )
            # Log failure, potentially raise 500 or handle differently
            log_audit_event(
                db=db,
                action="UPDATE_PATIENT_PROFILE_PHOTO",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="patient",
                target_resource_id=str(user_id),
                details={"reason": f"Unexpected upload error: {e}"},
            )
    # --- Add uploaded photo URL to the update data --- END

    # TEMP DEBUG: Log data passed to CRUD *after* potential photo URL addition
    logger.info(f"Final data being passed to crud_patient.update: {update_data_dict}")

    patient = crud.patient.get(db=db, id=user_id)
    if not patient:
        logger.warning(
            f"Patient profile not found during update for user_id: {user_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_PATIENT_PROFILE",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={"reason": "Patient not found"},
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient profile not found for the authenticated user.",
        )

    if not update_data_dict:
        logger.info(f"No update data provided for patient {user_id}.")
        return patient

    try:
        updated_patient = crud.patient.update(
            db=db, db_obj=patient, obj_in=update_data_dict
        )
    except IntegrityError as ie:
        db.rollback()  # Rollback the transaction on integrity error
        logger.warning(
            f"Integrity error updating patient profile for user {user_id}: {ie}",
            exc_info=True,
        )
        error_detail = str(ie.orig) if hasattr(ie, "orig") else str(ie)

        # Check if it's a phone number unique constraint violation
        if "ix_patients_phone_number" in error_detail:
            log_audit_event(
                db=db,
                action="UPDATE_PATIENT_PROFILE",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="patient",
                target_resource_id=str(user_id),
                details={
                    "reason": "Duplicate phone number",
                    "update_data": update_data_dict,
                },
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This phone number is already associated with another account.",
            ) from ie
        else:
            # Handle other integrity errors
            log_audit_event(
                db=db,
                action="UPDATE_PATIENT_PROFILE",
                outcome="FAILURE",
                actor_user_id=str(user_id),
                actor_role="patient",
                target_resource_id=str(user_id),
                details={
                    "reason": f"DB integrity error: {ie}",
                    "update_data": update_data_dict,
                },
            )
            raise HTTPException(
                status_code=500, detail="Database constraint violation."
            ) from ie
    except Exception as e:
        db.rollback()  # Also rollback on other exceptions
        logger.error(
            f"Error updating patient profile in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_PATIENT_PROFILE",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(user_id),
            details={
                "reason": f"DB update error: {e}",
                "update_data": update_data_dict,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to update profile information."
        ) from e

    logger.info(
        f"Successfully updated profile data for patient: {updated_patient.email}"
    )
    log_audit_event(
        db=db,
        action="UPDATE_PATIENT_PROFILE",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(user_id),
        details={"updated_fields": list(update_data_dict.keys())},
    )
    return updated_patient


@router.get(
    "/profile-photos/{user_id}/{filename}",
    summary="Get Patient Profile Photo",
    description="Retrieves the profile photo for a given user and filename. Requires authentication.",
    tags=["Patients", "Media"],
    responses={
        200: {"content": {"image/*": {}}, "description": "Profile photo stream"},
        401: {"description": "Not authenticated"},
        404: {"description": "Photo not found or user not found"},
        503: {"description": "Azure Storage client not available"},
    },
)
async def get_profile_photo(
    user_id: str = Path(..., description="The ID of the user whose photo is requested"),
    filename: str = Path(..., description="The filename of the photo"),
    current_user: TokenPayload = Depends(
        deps.get_current_active_user
    ),  # Use general active user dependency
    # db: Session = Depends(deps.get_db), # DB not strictly needed unless checking patient existence
) -> StreamingResponse:
    """
    Streams the profile photo from Azure Blob Storage.
    Requires authentication but doesn't strictly enforce that the requesting user
    matches the user_id in the path, allowing clinicians to potentially view photos
    if needed in the future (though currently only patients use this).
    A more robust implementation might check relationships if needed.
    """
    if not blob_service_client:
        logger.error("Azure Blob Service Client not available for photo retrieval.")
        raise HTTPException(status_code=503, detail="Storage service is unavailable.")

    blob_path = f"profile_photos/{user_id}/{filename}"
    logger.debug(f"Attempting to retrieve profile photo from Azure: {blob_path}")

    try:
        blob_client = blob_service_client.get_blob_client(
            container=settings.AZURE_STORAGE_CONTAINER_NAME, blob=blob_path
        )

        # Check if blob exists before attempting to download
        if not blob_client.exists():
            logger.warning(f"Profile photo not found at path: {blob_path}")
            raise HTTPException(status_code=404, detail="Profile photo not found.")

        stream = blob_client.download_blob()

        # Guess media type based on filename extension
        media_type, _ = mimetypes.guess_type(filename)
        if not media_type:
            media_type = "application/octet-stream"  # Default if type cannot be guessed
            logger.warning(
                f"Could not determine media type for {filename}, defaulting to {media_type}"
            )

        return StreamingResponse(stream.chunks(), media_type=media_type)

    except Exception as e:
        logger.error(
            f"Error retrieving profile photo {blob_path} from Azure: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve profile photo."
        ) from e


@router.post(
    "/me/weight-log",
    response_model=WeightLogResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Log Weight Entry",
    description="Logs a new weight entry for the currently authenticated patient. Adheres to a weekly logging interval.",
    responses={
        201: {"description": "Weight logged successfully"},
        400: {"description": "Invalid input data or weekly interval violation"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Weight Tracking"],
)
def log_weight_entry(
    background_tasks: BackgroundTasks,
    *,
    current_user: TokenPayload = Depends(deps.get_current_patient),
    weight_log_in: WeightLogRequest,
    db: Session = Depends(deps.get_db),
) -> WeightLogResponse:
    """
    Log a new weight entry for the current patient.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to log weight for user_id: {user_id}, weight: {weight_log_in.weight_kg}, log_date: {weight_log_in.log_date}"
    )

    weight_log_create_schema = WeightLogCreate(
        weight_kg=weight_log_in.weight_kg, log_date=weight_log_in.log_date
    )

    # Update to use crud.weight_log
    weight_log = crud.weight_log.create_weight_log(
        db=db, obj_in=weight_log_create_schema, patient_id=user_id
    )
    # Ensure ORM object is converted to response schema
    return WeightLogResponse.model_validate(weight_log)


@router.get(
    "/me/weight-log",
    response_model=PaginatedWeightLogResponse,
    summary="Get Weight History",
    description="Retrieves the weight log history for the currently authenticated patient, ordered by log date descending.",
    responses={
        200: {"description": "Weight history retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Weight Tracking"],
)
def read_weight_log_history(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=500, description="Maximum number of records to return per page."
    ),
) -> PaginatedWeightLogResponse:
    """
    Retrieve own weight log history.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching weight history for user_id: {user_id}")

    # Update to use crud.patient
    patient_height_cm = crud.patient.get_patient_height(db=db, patient_id=user_id)
    if patient_height_cm is None:
        logger.warning(f"Height not found for patient {user_id}, BMI will be null.")

    # Use crud.weight_log instead of undefined patient_weight
    weight_logs = crud.weight_log.get_weight_logs_by_patient(
        db=db, patient_id=user_id, skip=skip, limit=limit
    )

    # Get total count for pagination - use crud.weight_log
    total = crud.weight_log.get_weight_logs_count_by_patient(db=db, patient_id=user_id)
    page = (skip // limit) + 1 if limit else 1
    pages = (total + limit - 1) // limit if limit else 1

    # Get goal weight progress data
    patient = crud.patient.get(db=db, id=user_id)
    goal_weight_kg = patient.goal_weight_kg if patient else None

    # Manually construct response to include BMI and goal progress
    response_entries = []
    for log in weight_logs:
        bmi = None
        if patient_height_cm and log.weight_kg:
            try:
                height_m = patient_height_cm / 100.0
                bmi = round(log.weight_kg / (height_m**2), 2)
            except ZeroDivisionError:
                logger.error(
                    f"Height is zero for patient {user_id}, cannot calculate BMI."
                )
            except Exception as e:
                logger.error(f"Error calculating BMI for log {log.id}: {e}")

        # Calculate goal progress for this entry
        progress_to_goal_kg = None
        progress_percentage = None
        
        if goal_weight_kg and log.weight_kg:
            progress_to_goal_kg = goal_weight_kg - log.weight_kg
            # For percentage, we'd need starting weight which we don't have here
            # This is a simplified calculation

        response_entries.append(
            WeightLogEntryResponse(
                id=log.id,
                patient_id=log.patient_id,
                weight_kg=log.weight_kg,
                bmi=bmi,
                log_date=log.log_date,
                created_at=log.created_at,
                updated_at=log.updated_at,
                goal_weight_kg=goal_weight_kg,
                progress_to_goal_kg=progress_to_goal_kg,
                progress_percentage=progress_percentage
            )
        )

    log_audit_event(
        db=db,
        action="GET_WEIGHT_HISTORY",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        details={"count": len(response_entries), "skip": skip, "limit": limit},
    )
    return {
        "items": response_entries,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages,
    }


@router.patch(
    "/me/weight-log/{weight_log_id}",
    response_model=WeightLogResponse,
    summary="Update Weight Entry",
    description="Updates a specific weight log entry for the currently authenticated patient.",
    responses={
        200: {"description": "Weight entry updated successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient or does not own this entry"},
        404: {"description": "Weight log entry not found"},
    },
    tags=["Weight Tracking"],
)
def update_weight_log_entry(
    weight_log_id: UUID = Path(
        ..., description="The ID of the weight log entry to update"
    ),
    weight_log_update: WeightLogUpdate = Depends(),  # Use Depends for Pydantic v2 compatibility with form-like data
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> WeightLogResponse:
    """
    Update a specific weight log entry.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to update weight log entry {weight_log_id} for user_id: {user_id}"
    )

    db_weight_log = crud.weight_log.get_weight_log_by_id(
        db=db, weight_log_id=weight_log_id
    )

    if not db_weight_log:
        logger.warning(
            f"Weight log entry {weight_log_id} not found for update attempt by user {user_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={"reason": "Weight log not found"},
        )
        raise HTTPException(status_code=404, detail="Weight log entry not found")

    # Authorization check: Ensure the log belongs to the current user
    if str(db_weight_log.patient_id) != str(user_id):
        logger.error(
            f"Authorization failed: User {user_id} attempted to update weight log {weight_log_id} belonging to {db_weight_log.patient_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={"reason": "Authorization failed - entry does not belong to user"},
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this weight log entry"
        )

    # Convert Pydantic model to dict, excluding unset values
    update_data = weight_log_update.model_dump(exclude_unset=True)
    logger.debug(f"Update data for weight log {weight_log_id}: {update_data}")

    if not update_data:
        logger.info(f"No update data provided for weight log {weight_log_id}.")
        return db_weight_log  # Return existing object if no changes

    try:
        updated_entry = crud.weight_log.update_weight_log(
            db=db, db_obj=db_weight_log, obj_in=update_data
        )
    except Exception as e:
        logger.error(
            f"Error updating weight log {weight_log_id} in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={
                "reason": f"DB update error: {e}",
                "update_data": update_data,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to update weight entry."
        ) from e

    logger.info(f"Successfully updated weight log entry: {updated_entry.id}")
    log_audit_event(
        db=db,
        action="UPDATE_WEIGHT_LOG",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(updated_entry.id),
        details={"updated_fields": list(update_data.keys())},
    )
    return updated_entry


@router.delete(
    "/me/weight-log/{weight_log_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Weight Entry",
    description="Deletes a specific weight log entry for the currently authenticated patient.",
    responses={
        204: {"description": "Weight entry deleted successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient or does not own this entry"},
        404: {"description": "Weight log entry not found"},
    },
    tags=["Weight Tracking"],
)
def delete_weight_log_entry(
    weight_log_id: UUID = Path(
        ..., description="The ID of the weight log entry to delete"
    ),
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> None:
    """
    Delete a specific weight log entry.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to delete weight log entry {weight_log_id} for user_id: {user_id}"
    )

    db_weight_log = crud.weight_log.get_weight_log_by_id(
        db=db, weight_log_id=weight_log_id
    )

    if not db_weight_log:
        logger.warning(
            f"Weight log entry {weight_log_id} not found for delete attempt by user {user_id}"
        )
        log_audit_event(
            db=db,
            action="DELETE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={"reason": "Weight log not found"},
        )
        raise HTTPException(status_code=404, detail="Weight log entry not found")

    # Authorization check: Ensure the log belongs to the current user
    if str(db_weight_log.patient_id) != str(user_id):
        logger.error(
            f"Authorization failed: User {user_id} attempted to delete weight log {weight_log_id} belonging to {db_weight_log.patient_id}"
        )
        log_audit_event(
            db=db,
            action="DELETE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={"reason": "Authorization failed - entry does not belong to user"},
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to delete this weight log entry"
        )

    try:
        crud.weight_log.delete_weight_log(db=db, weight_log_id=weight_log_id)
    except Exception as e:
        logger.error(
            f"Error deleting weight log {weight_log_id} from DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="DELETE_WEIGHT_LOG",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(weight_log_id),
            details={"reason": f"DB delete error: {e}"},
        )
        raise HTTPException(
            status_code=500, detail="Failed to delete weight entry."
        ) from e

    logger.info(f"Successfully deleted weight log entry: {weight_log_id}")
    log_audit_event(
        db=db,
        action="DELETE_WEIGHT_LOG",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(weight_log_id),
    )
    return None  # Return None for 204 No Content


@router.post(
    "/me/medication-requests",
    response_model=MedicationRequestResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Request Medication Refill",
    description="Submits a request for a medication refill for the currently authenticated patient.",
    responses={
        201: {"description": "Medication request submitted successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Medications"],
)
def request_medication_refill(
    background_tasks: BackgroundTasks,
    *,
    current_user: TokenPayload = Depends(deps.get_current_patient),
    med_request_in: MedicationRequestCreate,
    db: Session = Depends(deps.get_db),
) -> MedicationRequestResponse:
    """
    Submit a medication refill request.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to create medication request for user_id: {user_id}, medication: {med_request_in.medication_name}"
    )

    try:
        created_request = crud.medication_request.create_medication_request(
            db=db, obj_in=med_request_in, patient_id=user_id
        )
    except Exception as e:
        logger.error(
            f"Error creating medication request in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="REQUEST_MEDICATION_REFILL",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={
                "reason": f"DB create error: {e}",
                "medication_name": med_request_in.medication_name,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to submit medication request."
        ) from e

    logger.info(f"Successfully created medication request: {created_request.id}")
    # TODO: Publish MedicationRequestedEvent if needed
    log_audit_event(
        db=db,
        action="REQUEST_MEDICATION_REFILL",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(created_request.id),
        details={
            "medication_name": created_request.medication_name,
            "dosage": created_request.dosage,
            "frequency": created_request.frequency,
            "duration": created_request.duration,
        },
    )
    return created_request


@router.get(
    "/me/medication-requests",
    response_model=PaginatedMedicationRequestResponse,
    summary="Get Patient Medication Requests",
    description="Retrieves the medication request history for the currently authenticated patient.",
    responses={
        200: {"description": "Medication requests retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Medications"],
)
def get_medication_requests_for_patient(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        100, ge=1, le=500, description="Maximum number of records to return per page."
    ),
) -> PaginatedMedicationRequestResponse:
    """
    Retrieve own medication request history.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching medication request history for user_id: {user_id}")

    requests = crud.medication_request.get_medication_requests_by_patient(
        db=db, patient_id=user_id, skip=skip, limit=limit
    )

    total_count = crud.medication_request.get_medication_requests_count_by_patient(
        db=db, patient_id=user_id
    )

    log_audit_event(
        db=db,
        action="GET_MEDICATION_REQUESTS",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        details={"count": len(requests), "skip": skip, "limit": limit},
    )

    # Calculate pagination details
    page = (skip // limit) + 1 if limit else 1
    pages = (total_count + limit - 1) // limit if limit else 1

    # Return data structured according to the response model
    return PaginatedMedicationRequestResponse(
        items=requests,
        total=total_count,
        page=page,
        size=limit,
        pages=pages,
    )


@router.get(
    "/me/medication-requests/latest",
    response_model=Optional[MedicationRequestResponse],
    summary="Get Latest Patient Medication Request",
    description="Retrieves the most recent medication request for the currently authenticated patient.",
    responses={
        200: {
            "description": "Latest medication request retrieved successfully or null"
        },
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Medications"],
)
def get_latest_medication_request(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> Optional[MedicationRequestResponse]:
    """
    Retrieve the most recent medication request for the current patient.
    Returns null if no requests exist.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching latest medication request for user_id: {user_id}")

    latest_request = crud.medication_request.get_latest_by_patient(
        db=db, patient_id=user_id
    )

    log_audit_event(
        db=db,
        action="GET_LATEST_MEDICATION_REQUEST",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(latest_request.id) if latest_request else None,
    )
    return latest_request


@router.post(
    "/me/side-effects",
    response_model=SideEffectReportResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Submit Side Effect Report",
    description="Submits a report detailing side effects experienced by the currently authenticated patient.",
    responses={
        201: {"description": "Side effect report submitted successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Side Effects"],
)
def submit_side_effect_report(
    background_tasks: BackgroundTasks,
    *,
    current_user: TokenPayload = Depends(deps.get_current_patient),
    report_in: SideEffectReportCreate,
    db: Session = Depends(deps.get_db),
) -> SideEffectReportResponse:
    """
    Submit a side effect report.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to create side effect report for user_id: {user_id}, severity: {report_in.severity}"
    )

    try:
        created_report = crud.side_effect_report.create(
            db=db, obj_in=report_in, patient_id=user_id
        )
    except Exception as e:
        logger.error(
            f"Error creating side effect report in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="SUBMIT_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            details={
                "reason": f"DB create error: {e}",
                "severity": report_in.severity,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to submit side effect report."
        ) from e

    logger.info(f"Successfully created side effect report: {created_report.id}")
    event = SideEffectReportedEvent(
        user_id=str(user_id),
        report_id=str(created_report.id),
        severity=created_report.severity.value,
    )
    publish_event(event=event, background_tasks=background_tasks)
    log_audit_event(
        db=db,
        action="SUBMIT_SIDE_EFFECT_REPORT",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(created_report.id),
        details={
            "severity": created_report.severity.value,
            "description_length": len(created_report.description),
        },
    )
    return created_report


@router.get(
    "/me/side-effects",
    response_model=PaginatedSideEffectReportResponse,
    summary="Get Patient Side Effect Reports",
    description="Retrieves the side effect report history for the currently authenticated patient, with pagination and sorting.",
    responses={
        200: {"description": "Side effect reports retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Side Effects"],
)
def read_patient_side_effects(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination."),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return per page."
    ),
    sortBy: Optional[str] = Query(
        "created_at", description="Field to sort by (e.g., 'created_at', 'severity')."
    ),
    sortDesc: bool = Query(
        True, description="Sort descending (True) or ascending (False)."
    ),
) -> PaginatedSideEffectReportResponse:
    """
    Retrieve own side effect report history with pagination and sorting.
    """
    user_id = current_user.sub
    logger.debug(
        f"Fetching side effect reports for user_id: {user_id}, skip={skip}, limit={limit}, sortBy={sortBy}, sortDesc={sortDesc}"
    )

    # Validate sortBy field
    allowed_sort_fields = ["created_at", "severity"]
    if sortBy not in allowed_sort_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid sortBy field. Allowed fields: {', '.join(allowed_sort_fields)}",
        )

    total_count = crud.side_effect_report.get_count_by_patient(
        db=db, patient_id=user_id
    )
    reports = crud.side_effect_report.get_by_patient(
        db=db,
        patient_id=user_id,
        skip=skip,
        limit=limit,
        sort_by=sortBy,
        sort_desc=sortDesc,
    )

    log_audit_event(
        db=db,
        action="GET_SIDE_EFFECT_REPORTS",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        details={
            "count": len(reports),
            "total_count": total_count,
            "skip": skip,
            "limit": limit,
            "sortBy": sortBy,
            "sortDesc": sortDesc,
        },
    )
    page = (skip // limit) + 1 if limit else 1
    pages = (total_count + limit - 1) // limit if limit else 1
    return {
        "items": reports,
        "total": total_count,
        "page": page,
        "size": limit,
        "pages": pages,
    }


@router.patch(
    "/me/side-effects/{report_id}",
    response_model=SideEffectReportResponse,
    summary="Update Side Effect Report",
    description="Updates a specific side effect report submitted by the currently authenticated patient.",
    responses={
        200: {"description": "Report updated successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient or does not own this report"},
        404: {"description": "Side effect report not found"},
    },
    tags=["Side Effects"],
)
def update_patient_side_effect_report(
    report_id: UUID = Path(..., description="The ID of the report to update"),
    report_update: SideEffectReportUpdate = Depends(),
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> SideEffectReportResponse:
    """
    Update a specific side effect report submitted by the patient.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to update side effect report {report_id} for user_id: {user_id}"
    )

    db_report = crud.side_effect_report.get(db=db, id=report_id)

    if not db_report:
        logger.warning(
            f"Side effect report {report_id} not found for update attempt by user {user_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={"reason": "Report not found"},
        )
        raise HTTPException(status_code=404, detail="Side effect report not found")

    # Authorization check
    if str(db_report.patient_id) != str(user_id):
        logger.error(
            f"Authorization failed: User {user_id} attempted to update report {report_id} belonging to {db_report.patient_id}"
        )
        log_audit_event(
            db=db,
            action="UPDATE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={"reason": "Authorization failed - report does not belong to user"},
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this report"
        )

    update_data = report_update.model_dump(exclude_unset=True)
    logger.debug(f"Update data for side effect report {report_id}: {update_data}")

    if not update_data:
        logger.info(f"No update data provided for side effect report {report_id}.")
        return db_report

    try:
        updated_report = crud.side_effect_report.update_side_effect_report(
            db=db, db_obj=db_report, obj_in=update_data
        )
    except Exception as e:
        logger.error(
            f"Error updating side effect report {report_id} in DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="UPDATE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={
                "reason": f"DB update error: {e}",
                "update_data": update_data,
            },
        )
        raise HTTPException(
            status_code=500, detail="Failed to update side effect report."
        ) from e

    logger.info(f"Successfully updated side effect report: {updated_report.id}")
    log_audit_event(
        db=db,
        action="UPDATE_SIDE_EFFECT_REPORT",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(updated_report.id),
        details={"updated_fields": list(update_data.keys())},
    )
    return updated_report


@router.delete(
    "/me/side-effects/{report_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Side Effect Report",
    description="Deletes a specific side effect report submitted by the currently authenticated patient.",
    responses={
        204: {"description": "Report deleted successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient or does not own this report"},
        404: {"description": "Side effect report not found"},
    },
    tags=["Side Effects"],
)
def delete_patient_side_effect_report(
    report_id: UUID = Path(..., description="The ID of the report to delete"),
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> None:
    """
    Delete a specific side effect report submitted by the patient.
    """
    user_id = current_user.sub
    logger.debug(
        f"Attempting to delete side effect report {report_id} for user_id: {user_id}"
    )

    db_report = crud.side_effect_report.get(db=db, id=report_id)

    if not db_report:
        logger.warning(
            f"Side effect report {report_id} not found for delete attempt by user {user_id}"
        )
        log_audit_event(
            db=db,
            action="DELETE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={"reason": "Report not found"},
        )
        raise HTTPException(status_code=404, detail="Side effect report not found")

    # Authorization check
    if str(db_report.patient_id) != str(user_id):
        logger.error(
            f"Authorization failed: User {user_id} attempted to delete report {report_id} belonging to {db_report.patient_id}"
        )
        log_audit_event(
            db=db,
            action="DELETE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={"reason": "Authorization failed - report does not belong to user"},
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to delete this report"
        )

    try:
        crud.side_effect_report.remove(db=db, id=report_id)
    except Exception as e:
        logger.error(
            f"Error deleting side effect report {report_id} from DB for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="DELETE_SIDE_EFFECT_REPORT",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="patient",
            target_resource_id=str(report_id),
            details={"reason": f"DB delete error: {e}"},
        )
        raise HTTPException(
            status_code=500, detail="Failed to delete side effect report."
        ) from e

    logger.info(f"Successfully deleted side effect report: {report_id}")
    log_audit_event(
        db=db,
        action="DELETE_SIDE_EFFECT_REPORT",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(report_id),
    )
    return None


@router.get(
    "/me/side-effects/summary",
    response_model=SideEffectSeveritySummary,
    summary="Get Side Effect Severity Summary",
    description="Retrieves the count of side effect reports for the current patient, grouped by severity.",
    responses={
        200: {"description": "Summary retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Side Effects"],
)
def get_patient_side_effect_summary(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> SideEffectSeveritySummary:
    """
    Get the summary of side effect reports by severity for the current patient.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching side effect summary for user_id: {user_id}")

    summary = crud.side_effect_report.get_severity_summary(db=db, patient_id=user_id)

    logger.info(
        f"Returning side effect summary for user {user_id}: Minor={summary.minor}, Mod={summary.moderate}, Major={summary.major}"
    )
    log_audit_event(
        db=db,
        action="GET_SIDE_EFFECT_SUMMARY",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
    )
    return summary


@router.get(
    "/me/appointments",
    response_model=PaginatedAppointmentResponse,
    summary="Get Patient Appointments",
    description="Retrieves upcoming appointments for the currently authenticated patient.",
    responses={
        200: {"description": "Appointments retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Appointments"],
)
def read_patient_appointments(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
    limit: Optional[int] = Query(
        None, description="Limit the number of upcoming appointments returned"
    ),
) -> PaginatedAppointmentResponse:
    """
    Retrieve upcoming appointments for the current patient.
    """
    user_id = current_user.sub
    logger.debug(
        f"Fetching upcoming appointments for user_id: {user_id} with limit: {limit}"
    )

    # Use get_upcoming_by_patient_id and pass the limit
    appointments: list[Appointment] = crud.appointment.get_upcoming_by_patient_id(
        db=db,
        patient_id=user_id,
        limit=limit,  # Pass limit to CRUD method
    )

    # We don't know the *total* number of upcoming appointments easily without another query
    # For pagination response, we'll report total based on what was returned (or handle differently if needed)
    # For the dashboard use case (limit=small number), this is probably fine.
    total_returned = len(appointments)

    logger.debug(f"Found {total_returned} upcoming appointments.")

    return PaginatedAppointmentResponse(
        items=appointments,
        total=total_returned,  # Reporting total based on limited result
        page=1,
        size=total_returned,
        pages=1,
    )


@router.post(
    "/me/chat",
    response_model=ChatMessageResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Send Chat Message",
    description="Sends a message from the currently authenticated patient to the AI chat agent.",
    responses={
        201: {"description": "Message sent successfully, processing started"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
        500: {"description": "Agent processing error or LLM failure"},
        503: {"description": "Guardrail violation detected"},
    },
    tags=["Chat"],
)
async def send_chat_message(
    background_tasks: BackgroundTasks,
    *,
    current_user: TokenPayload = Depends(deps.get_current_patient),
    chat_in: ChatMessageRequest,
    db: Session = Depends(deps.get_db),
) -> ChatMessageResponse:
    """
    Send a chat message from the patient to the agent.
    """
    user_id = current_user.sub
    user_message_content = chat_in.message
    logger.debug(
        f"Received chat message from user_id: {user_id}, content: '{user_message_content[:50]}...'"
    )

    # 1. Save the user's message to the database
    user_message_db = None
    try:
        user_message_schema = ChatMessageCreateInternal(
            patient_id=user_id,
            sender_type=MessageSenderType.PATIENT,
            message_content=user_message_content,
        )
        user_message_db = crud.create_chat_message(db=db, obj_in=user_message_schema)
        logger.info(f"Saved user message {user_message_db.id} to DB")

        # --- Publish ChatMessageSentEvent after successful persistence of user message ---
        # This event will be handled by downstream consumers (e.g., OpenAIChatHandler).
        # The payload includes the original message, context, db session, and relevant metadata.
        event_payload = ChatMessageSentEvent(
            user_id=str(user_id),
            message_id=str(user_message_db.id),
            sender_type="PATIENT",
            clinic_id="unknown",
            # Add context required by OpenAIChatHandler
            context={
                "patient_id": str(user_id),
                "user_role": getattr(current_user, "role", "patient"),
                # Optionally add more context fields as needed
            },
            message=user_message_content,
            db=db,
        )
        # Use BackgroundTasks to publish the event asynchronously.
        # The event name is required to be exactly "ChatMessageSentEvent".
        if background_tasks:
            background_tasks.add_task(
                publish_event, event=event_payload, background_tasks=background_tasks
            )
        # If/when a central event_bus.publish is available, replace above with:
        # event_bus.publish("ChatMessageSentEvent", event_payload)
        # --------------------------------------------------------------------
    except Exception as e:
        logger.error(
            f"Error saving user chat message to DB for user {user_id}: {e}",
            exc_info=True,
        )
        # Decide if we should proceed or raise an error immediately
        raise HTTPException(
            status_code=500, detail="Failed to save your message before processing."
        ) from e

    # 2. Trigger the agent processing in the background
    agent_response_content = await process_chat_message(
        user_id=user_id, user_message=user_message_content, db=db
    )
    # Ensure string conversion before slicing for logging
    response_str = str(agent_response_content)
    logger.info(
        f"Agent processing complete for user {user_id}. Response: '{response_str[:50]}...'"
    )

    # --- FIX: Ensure agent_response_content is a string before saving ---
    agent_message_db = None
    try:
        # If the agent returns a dict (structured output), extract the 'response' key as the message_content
        if isinstance(agent_response_content, dict):
            agent_message_content = agent_response_content.get(
                "response", str(agent_response_content)
            )
        else:
            agent_message_content = str(agent_response_content)

        agent_message_schema = ChatMessageCreateInternal(
            patient_id=user_id,
            sender_type=MessageSenderType.AGENT,
            message_content=agent_message_content,
        )
        agent_message_db = crud.create_chat_message(db=db, obj_in=agent_message_schema)
        logger.info(f"Saved agent message {agent_message_db.id} to DB")
    except Exception as e:
        logger.error(
            f"Error saving agent chat message to DB for user {user_id}: {e}",
            exc_info=True,
        )
        # Log the error but still return the response to the user if agent processing succeeded
        # Consider how to handle this failure (e.g., retry saving later?)

    except GuardrailViolationError as gve:
        logger.warning(f"Guardrail violation for user {user_id}: {gve}")
        # Audit log is handled within process_chat_message for guardrails
        raise HTTPException(status_code=503, detail=f"Response blocked: {gve}") from gve
    except (
        AgentProcessingError,
        LLMConnectionError,
        ContextRetrievalError,
    ) as agent_exc:
        logger.error(
            f"Agent processing error for user {user_id}: {agent_exc}", exc_info=True
        )
        log_audit_event(
            db=db,
            action="AGENT_CHAT_PROCESSING",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="agent",
            details={"reason": f"Agent error: {agent_exc}"},
        )
        raise HTTPException(
            status_code=500, detail=f"Agent error: {agent_exc}"
        ) from agent_exc
    except Exception as e:
        # Catch any other unexpected errors from process_chat_message
        logger.error(
            f"Unexpected error during agent processing for user {user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="AGENT_CHAT_PROCESSING",
            outcome="FAILURE",
            actor_user_id=str(user_id),
            actor_role="agent",
            details={"reason": f"Unexpected error: {e}"},
        )
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred during processing."
        ) from e

    # 4. Publish event (optional, if needed for real-time updates etc.)
    # --- FIX: Use correct fields for ChatMessageSentEvent ---
    # Retrieve the patient's clinic_id (associated_clinic_id) from the DB
    clinic_id = None
    try:
        patient_obj = db.query(crud.patient.model).filter_by(id=user_id).first()
        if patient_obj and hasattr(patient_obj, "associated_clinic_id"):
            clinic_id = (
                str(patient_obj.associated_clinic_id)
                if patient_obj.associated_clinic_id
                else None
            )
    except Exception as e:
        logger.warning(f"Could not retrieve clinic_id for patient {user_id}: {e}")
    if not clinic_id:
        clinic_id = "unknown"

    event = ChatMessageSentEvent(
        user_id=str(user_id),
        message_id=str(agent_message_db.id) if agent_message_db else None,
        sender_type="AGENT",
        clinic_id=clinic_id,
        # Add context required by OpenAIChatHandler
        context={
            "patient_id": str(user_id),
            "user_role": getattr(current_user, "role", "patient"),
            # Optionally add more context fields as needed
        },
        message=agent_message_content,
        db=db,
    )
    publish_event(event=event, background_tasks=background_tasks)

    # 5. Log successful interaction
    log_audit_event(
        db=db,
        action="SEND_CHAT_MESSAGE",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(user_message_db.id) if user_message_db else None,
        details={
            "user_message_length": len(user_message_content),
            "agent_response_length": len(agent_response_content),
            "agent_message_id": str(agent_message_db.id) if agent_message_db else None,
        },
    )

    # 6. Return the agent's response (even if saving it failed, user should see it)
    # Use the agent_message_db object if available, otherwise construct from content
    if agent_message_db:
        # Return a ChatMessageResponse using the saved DB object
        return ChatMessageResponse(
            message=agent_message_db.message_content,
            message_id=str(agent_message_db.id),
            timestamp=agent_message_db.created_at.isoformat(),
            structured_data=(
                agent_response_content.get("structured_data")
                if isinstance(agent_response_content, dict)
                else None
            ),
            metadata=(
                agent_response_content.get("metadata")
                if isinstance(agent_response_content, dict)
                else None
            ),
        )
    else:
        # Fallback if agent message saving failed
        return ChatMessageResponse(
            message=agent_message_content,
            message_id=str(uuid.uuid4()),  # Generate a dummy ID
            timestamp=datetime.datetime.now(datetime.timezone.utc).isoformat(),
            structured_data=(
                agent_response_content.get("structured_data")
                if isinstance(agent_response_content, dict)
                else None
            ),
            metadata=(
                agent_response_content.get("metadata")
                if isinstance(agent_response_content, dict)
                else None
            ),
        )


@router.get(
    "/me/chat",
    response_model=list[ChatMessageResponse],
    summary="Get Chat History",
    description="Retrieves the chat message history for the currently authenticated patient.",
    responses={
        200: {"description": "Chat history retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Chat"],
)
def get_chat_history(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
    skip: int = Query(
        0, ge=0, description="Number of messages to skip for pagination."
    ),
    limit: int = Query(
        50, ge=1, le=200, description="Maximum number of messages to return per page."
    ),
) -> list[ChatMessageResponse]:
    """
    Retrieve chat history for the current patient.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching chat history for user_id: {user_id}")

    messages = crud.chat_message.get_by_patient(
        db=db, patient_id=user_id, skip=skip, limit=limit
    )

    log_audit_event(
        db=db,
        action="GET_CHAT_HISTORY",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        details={"count": len(messages), "skip": skip, "limit": limit},
    )

    # Convert ORM objects to ChatMessageResponse with correct field mapping
    return [
        ChatMessageResponse(
            message=msg.message_content,
            message_id=str(msg.id),
            timestamp=msg.created_at.isoformat(),
            structured_data=getattr(msg, "structured_data", None),
            metadata=msg.metadata if hasattr(msg, "metadata") else None,
        )
        for msg in messages
    ]


# Imports moved to top of file


@router.post(
    "/invitations",
    response_model=PatientInvitationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Invite a New Patient (Clinician Action)",
    description="""Send a patient invitation email. Endpoint is placed under /patients
    for frontend compatibility but requires clinician auth.""",
    responses={
        201: {"description": "Patient invitation sent successfully"},
        400: {
            "description": "Invalid email format or invitation already exists/failed"
        },
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a clinician or not authorized"},
        500: {"description": "Failed to send invitation (e.g., Clerk API issue)"},
    },
    tags=["Patients", "Invitations"],  # Added Invitations tag
)
async def invite_patient(
    *,
    invitation_in: PatientInvitationRequest,
    db: Session = Depends(deps.get_db),
    current_clinician: TokenPayload = Depends(
        deps.get_current_clinician
    ),  # Require clinician auth
    background_tasks: BackgroundTasks,
):
    """
    Invite a new patient by email.

    **Note:** This endpoint handles the *request* to invite. The actual email sending
    and invitation management are handled by the underlying authentication service (Clerk).
    """
    clinician_id = current_clinician.clerk_id
    logger.info(
        f"Clinician {clinician_id} attempting to invite patient with email: {invitation_in.email}"
    )

    if not settings.FRONTEND_URL_PATIENT:
        logger.error(
            "FRONTEND_URL_PATIENT is not set in settings. Cannot send patient invitation."
        )
        # Log audit failure before raising exception
        log_audit_event(
            db=db,
            action="INVITE_PATIENT",
            outcome="FAILURE",
            actor_user_id=str(clinician_id),
            actor_role="clinician",
            details={
                "invited_email": invitation_in.email,
                "reason": "Server configuration error: Patient frontend URL missing.",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Server configuration error: Patient frontend URL is missing.",
        )

    # Get clinician's clinic ID
    clinics = crud.clinician.get_clinics_for_clinician(db=db, clinician_id=clinician_id)
    if not clinics:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Clinician is not associated with any clinic.",
        )
    clinic_id = clinics[0].id  # Use first clinic as primary

    public_metadata = {
        "role": ["patient"],  # Role as array
        "inviter_user_id": clinician_id,
        "associated_clinic_id": str(clinic_id),  # Convert UUID to string
    }

    try:
        logger.debug(f"Sending invitation with metadata: {public_metadata}")
        invitation = clerk.invitations.create(
            request={
                "email_address": invitation_in.email,
                "public_metadata": public_metadata,
                "redirect_url": f"{settings.FRONTEND_URL_PATIENT}/accept-invitation",
            }
        )
        logger.debug(f"Created invitation with metadata: {invitation.public_metadata}")
        logger.info(
            f"Successfully created Clerk invitation for patient {invitation_in.email} with ID {invitation.id}"
        )
        # Prepare success data
        invitation_result = {
            "invitation_id": invitation.id,
            "email": invitation.email_address,
            "status": invitation.status,
        }
        final_audit_outcome = "SUCCESS"  # Set outcome to success

    except clerk_models.ClerkErrors as e:
        logger.warning(
            f"Clerk SDK error during patient invitation for {invitation_in.email}: {e}",
            exc_info=False,
        )
        clerk_exception_details = str(e)  # Store exception details for audit log

        # Debug the structure of the error object
        logger.debug(f"Clerk error type: {type(e)}, dir: {dir(e)}")

        is_duplicate = False
        try:
            # First try to get structured data from the error object
            error_data = getattr(e, "data", None)

            # If error_data exists and has errors attribute
            if (
                error_data
                and hasattr(error_data, "errors")
                and isinstance(error_data.errors, list)
                and error_data.errors
            ):
                first_error = error_data.errors[0]
                if isinstance(first_error, dict):
                    error_code = first_error.get("code")
                    if error_code == "duplicate_record":
                        is_duplicate = True
                        long_message = first_error.get("long_message")
                        error_message = first_error.get("message")
                        final_error_detail = (
                            long_message
                            or error_message
                            or "An invitation already exists for this email address."
                        )
                        final_status_code = status.HTTP_400_BAD_REQUEST
                        final_audit_outcome = "FAILURE_DUPLICATE"

            # If we couldn't extract structured data, try parsing the error string
            elif not is_duplicate:
                # Try to parse the error string as JSON
                error_str = str(e)
                if "duplicate" in error_str.lower() or "duplicate_record" in error_str:
                    is_duplicate = True
                    final_error_detail = (
                        "An invitation already exists for this email address."
                    )
                    final_status_code = status.HTTP_400_BAD_REQUEST
                    final_audit_outcome = "FAILURE_DUPLICATE"

                    # Try to extract more specific error message from JSON if possible
                    import json

                    try:
                        # Check if the error string contains a JSON object
                        if "{" in error_str and "}" in error_str:
                            json_start = error_str.find("{")
                            json_end = error_str.rfind("}") + 1
                            json_str = error_str[json_start:json_end]

                            error_json = json.loads(json_str)
                            if "errors" in error_json and isinstance(
                                error_json["errors"], list
                            ):
                                first_error = error_json["errors"][0]
                                long_message = first_error.get("long_message")
                                if long_message:
                                    final_error_detail = long_message
                    except (json.JSONDecodeError, TypeError, KeyError) as json_err:
                        logger.debug(f"Failed to parse error JSON: {json_err}")

        except Exception as parsing_error:
            logger.error(
                f"Error while parsing Clerk error response: {parsing_error}",
                exc_info=True,
            )

        # If not a duplicate, set generic Clerk error details
        if not is_duplicate:
            final_error_detail = f"Failed to create patient invitation via Clerk: {e}"
            final_status_code = (
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )  # Keep 500 for other Clerk errors
            final_audit_outcome = "FAILURE"

    except Exception as e:
        logger.error(
            f"Unexpected error during patient invitation for {invitation_in.email}: {e}",
            exc_info=True,
        )
        clerk_exception_details = f"Unexpected error: {e}"  # Store exception details
        # Keep default failure outcome, status code, and detail

    # --- Final Audit Logging and Response/Exception Handling ---

    # For both success and failure cases, log an audit event
    audit_outcome = final_audit_outcome
    audit_details = {
        "invited_email": invitation_in.email,
    }

    # Add specific details based on outcome
    if audit_outcome == "SUCCESS":
        audit_details["invitation_id"] = (
            invitation_result.get("invitation_id") if invitation_result else None
        )
    elif audit_outcome == "FAILURE_DUPLICATE":
        audit_details["reason"] = "Duplicate invitation"
        audit_details["error_detail"] = final_error_detail
    else:  # Regular failure
        audit_details["reason"] = "Invitation failed"
        audit_details["error_detail"] = clerk_exception_details

    # Log the audit event for all outcomes
    try:
        log_audit_event(
            db=db,
            action="INVITE_PATIENT",
            outcome=audit_outcome,
            actor_user_id=str(clinician_id),
            actor_role="clinician",
            details=audit_details,
        )
    except Exception as audit_error:
        # Don't let audit logging failures prevent response to client
        logger.error(f"Failed to log audit event: {audit_error}", exc_info=True)

    # Return success result or raise exception for failures
    if final_audit_outcome == "SUCCESS":
        return invitation_result
    else:
        raise HTTPException(
            status_code=final_status_code,
            detail=final_error_detail,
        )


@router.post("/sync-clerk-details/{patient_id}")
def sync_patient_clerk_details(
    patient_id: str,
    db: Session = Depends(deps.get_db),
):
    """
    Sync patient details from Clerk to our database
    """
    try:
        # Get user details from Clerk
        clerk_user = clerk.users.get(patient_id)

        # Update patient record
        patient_update = PatientUpdate(
            first_name=clerk_user.first_name,
            last_name=clerk_user.last_name,
            email=(
                clerk_user.email_addresses[0].email_address
                if clerk_user.email_addresses
                else None
            ),
        )

        # Update to use crud.patient
        updated_patient = crud.patient.update(
            db=db, db_obj=crud.patient.get(db=db, id=patient_id), obj_in=patient_update
        )

        return updated_patient

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync patient details from Clerk: {str(e)}",
        )


@router.post(
    "/{patientId}/weight-logs",
    response_model=WeightLogResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Add Weight Log for Patient",
    description="""Allows a clinician to add a weight log entry for a specific patient.
    Only clinicians assigned to the patient may use this endpoint.""",
    responses={
        201: {"description": "Weight log created successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized to access this patient's data"},
        404: {"description": "Patient not found"},
    },
    tags=["Weight Tracking"],
)
def add_weight_log_for_patient(
    request: Request,
    weight_log_in: WeightLogRequest,
    patientId: str = Path(..., description="The ID of the patient"),
    current_user: TokenPayload = Depends(deps.get_current_active_clinician_for_patient),
    db: Session = Depends(deps.get_db),
) -> WeightLogResponse:
    """
    Add a weight log entry for a specific patient.
    Only clinicians assigned to the patient may use this endpoint.
    """
    logger.debug(
        f"Clinician {current_user.sub} attempting to add weight log for patient {patientId}, "
        f"weight: {weight_log_in.weight_kg}, log_date: {weight_log_in.log_date}"
    )

    # Verify patient exists
    patient = crud.patient.get(db=db, id=patientId)
    if not patient:
        logger.warning(f"Patient {patientId} not found")
        log_audit_event(
            db=db,
            action="ADD_WEIGHT_LOG_FOR_PATIENT",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role="clinician",
            target_resource_id=patientId,
            details={"reason": "Patient not found"},
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found",
        )

    # Create weight log entry
    weight_log_create_schema = WeightLogCreate(
        weight_kg=weight_log_in.weight_kg, log_date=weight_log_in.log_date
    )

    try:
        weight_log = crud.weight_log.create_weight_log(
            db=db, obj_in=weight_log_create_schema, patient_id=patientId
        )

        # Log successful audit event
        log_audit_event(
            db=db,
            action="ADD_WEIGHT_LOG_FOR_PATIENT",
            outcome="SUCCESS",
            actor_user_id=str(current_user.sub),
            actor_role="clinician",
            target_resource_id=patientId,
            details={
                "weight_log_id": str(weight_log.id),
                "weight_kg": weight_log.weight_kg,
                "log_date": weight_log.log_date.isoformat(),
            },
        )

        logger.info(
            f"Successfully created weight log {weight_log.id} for patient {patientId}"
        )
        return WeightLogResponse.model_validate(weight_log)

    except Exception as e:
        logger.error(
            f"Error creating weight log for patient {patientId}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="ADD_WEIGHT_LOG_FOR_PATIENT",
            outcome="FAILURE",
            actor_user_id=str(current_user.sub),
            actor_role="clinician",
            target_resource_id=patientId,
            details={"reason": f"Database error: {str(e)}"},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create weight log entry",
        )


# ===============================================================
# GOAL WEIGHT ENDPOINTS
# ===============================================================

@router.get(
    "/me/goal-weight",
    response_model=GoalWeightResponse,
    summary="Get Goal Weight Progress",
    description="Retrieves the current goal weight and progress for the authenticated patient.",
    responses={
        200: {"description": "Goal weight progress retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Goal Weight Tracking"],
)
def get_goal_weight_progress(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> GoalWeightResponse:
    """
    Get goal weight and progress information for the current patient.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching goal weight progress for user_id: {user_id}")

    try:
        progress_data = crud.patient.get_goal_weight_progress(db=db, patient_id=user_id)
        return GoalWeightResponse(**progress_data)
    except Exception as e:
        logger.error(
            f"Error retrieving goal weight progress for user {user_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve goal weight progress."
        ) from e


@router.patch(
    "/me/goal-weight",
    response_model=GoalWeightResponse,
    summary="Update Goal Weight",
    description="Updates the goal weight and/or target date for the authenticated patient.",
    responses={
        200: {"description": "Goal weight updated successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Goal Weight Tracking"],
)
def update_goal_weight(
    goal_weight_update: GoalWeightUpdate,
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> GoalWeightResponse:
    """
    Update goal weight information for the current patient.
    """
    user_id = current_user.sub
    logger.debug(f"Updating goal weight for user_id: {user_id}, data: {goal_weight_update.model_dump()}")

    # Get current patient
    patient = crud.patient.get(db=db, id=user_id)
    if not patient:
        logger.error(f"Patient {user_id} not found during goal weight update")
        raise HTTPException(status_code=404, detail="Patient not found")

    # Convert Pydantic model to dict, excluding unset values
    update_data = goal_weight_update.model_dump(exclude_unset=True)
    
    if not update_data:
        logger.info(f"No update data provided for goal weight update for patient {user_id}")
        progress_data = crud.patient.get_goal_weight_progress(db=db, patient_id=user_id)
        return GoalWeightResponse(**progress_data)

    try:
        # Update patient with goal weight data
        updated_patient = crud.patient.update(db=db, db_obj=patient, obj_in=update_data)
        
        # Log the goal weight change event
        event_data = {
            "patient_id": user_id,
            "goal_weight_kg": updated_patient.goal_weight_kg,
            "goal_weight_date": updated_patient.goal_weight_date.isoformat() if updated_patient.goal_weight_date else None,
        }
        
        crud.event_log.create(
            db=db,
            obj_in=EventLogCreate(
                patient_id=user_id,
                event_type="goal_weight_updated",
                description=f"Patient updated goal weight to {updated_patient.goal_weight_kg}kg",
                metadata=event_data
            )
        )

        logger.info(f"Successfully updated goal weight for patient: {user_id}")
        
        # Return updated progress data
        progress_data = crud.patient.get_goal_weight_progress(db=db, patient_id=user_id)
        return GoalWeightResponse(**progress_data)

    except Exception as e:
        logger.error(
            f"Error updating goal weight for patient {user_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to update goal weight."
        ) from e


@router.get(
    "/me/goal-weight/summary",
    response_model=GoalWeightProgressSummary,
    summary="Get Goal Weight Summary",
    description="Retrieves a condensed summary of goal weight progress for dashboard use.",
    responses={
        200: {"description": "Goal weight summary retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
    },
    tags=["Goal Weight Tracking"],
)
def get_goal_weight_summary(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> GoalWeightProgressSummary:
    """
    Get condensed goal weight progress summary for the current patient.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching goal weight summary for user_id: {user_id}")

    try:
        progress_data = crud.patient.get_goal_weight_progress(db=db, patient_id=user_id)
        
        # Return only the fields needed for summary
        summary_data = {
            "has_goal": progress_data.get("has_goal", False),
            "goal_weight_kg": progress_data.get("goal_weight_kg"),
            "current_weight_kg": progress_data.get("current_weight_kg"),
            "progress_percentage": progress_data.get("progress_percentage"),
            "is_goal_achieved": progress_data.get("is_goal_achieved", False),
            "trend": progress_data.get("trend"),
        }
        
        return GoalWeightProgressSummary(**summary_data)
    except Exception as e:
        logger.error(
            f"Error retrieving goal weight summary for user {user_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve goal weight summary."
        ) from e


@router.get(
    "/me/appointment-requests",
    response_model=PaginatedAppointmentRequestResponse,
    summary="Get My Appointment Requests",
    description="Retrieves appointment requests made by the authenticated patient.",
    responses={
        200: {"description": "Appointment requests retrieved successfully"},
        401: {"description": "Not authenticated"},
    },
    tags=["Appointments"],
)
def read_patient_appointment_requests(
    *,
    db: Session = Depends(deps.get_db),
    current_patient: TokenPayload = Depends(deps.get_current_patient),
    skip: int = 0,
    limit: int = 20,
) -> Any:
    """
    Retrieve appointment requests for the current patient.
    """
    patient_id = current_patient.sub  # Use the user ID directly
    logger.debug(
        f"Fetching appointment requests for patient_id: {patient_id}, skip={skip}, limit={limit}"
    )

    # Fetch the paginated requests using the correct method name
    requests = crud.appointment_request.get_by_patient_id(
        db=db, patient_id=patient_id, skip=skip, limit=limit
    )

    # Fetch the total count separately for accurate pagination
    # Since get_count_for_patient doesn't exist, query all requests for the patient and count them.
    # Note: This is less efficient than a dedicated count query but fixes the immediate error.
    all_requests_for_patient = crud.appointment_request.get_by_patient_id(
        db=db, patient_id=patient_id, limit=10000
    )  # Fetch all (up to a high limit)
    total_count = len(all_requests_for_patient)
    logger.debug(
        f"Found {len(requests)} requests for current page, total count: {total_count}"
    )

    # Calculate pagination values using the total count
    pages = (total_count + limit - 1) // limit if limit > 0 else 1
    page = (skip // limit) + 1 if limit > 0 else 1

    # --- Populate preferred_clinician details --- START
    response_items = []
    for req in requests:
        response_data = (
            schemas.appointment_request.AppointmentRequestResponse.model_validate(req)
        )
        if req.clinician_preference:
            clinician = crud.clinician.get(db=db, id=req.clinician_preference)
            if clinician:
                response_data.preferred_clinician = (
                    schemas.clinician.ClinicianBasicInfo.model_validate(clinician)
                )
            else:
                logger.warning(
                    f"Could not find clinician with ID {req.clinician_preference} for request {req.id}"
                )
                response_data.preferred_clinician = (
                    None  # Explicitly set to None if not found
                )
        else:
            response_data.preferred_clinician = (
                None  # Explicitly set to None if no preference ID
            )
        response_items.append(response_data)
    # --- Populate preferred_clinician details --- END

    return schemas.appointment_request.PaginatedAppointmentRequestResponse(
        items=response_items,  # Use the enriched items
        total=total_count,  # Use the actual total count
        page=page,
        size=limit,
        pages=pages,
    )


@router.post(
    "/me/appointment-requests",
    response_model=schemas.appointment_request.AppointmentRequestResponse,
    summary="Create Appointment Request",
    description="Creates a new appointment request for the currently authenticated patient.",
    responses={
        200: {"description": "Appointment request created successfully"},
        401: {"description": "Not authenticated"},
        422: {"description": "Validation error"},
    },
    tags=["Appointments"],
)
def create_appointment_request(
    *,
    db: Session = Depends(deps.get_db),
    current_user_id: str = Depends(deps.get_current_user),
    appointment_request_in: schemas.appointment_request.AppointmentRequestCreate,
) -> schemas.appointment_request.AppointmentRequestResponse:
    """
    Create a new appointment request.
    """
    # Ensure patient_id matches the current authenticated user for security
    appointment_request_in.patient_id = current_user_id

    appointment_request = crud.appointment_request.create(
        db=db, obj_in=appointment_request_in
    )

    # Get back the record with related entities loaded
    result = crud.appointment_request.get_by_id_with_relations(
        db=db, id=appointment_request.id, patient_id=current_user_id
    )

    # Log the action
    audit.log_audit_event(
        db=db,
        actor_user_id=current_user_id,
        actor_role="patient",
        action="appointment_request.create",
        outcome="SUCCESS",
        target_resource_type="appointment_request",
        target_resource_id=str(appointment_request.id),
        details={"preferred_datetime": str(appointment_request_in.preferred_datetime)},
    )

    return result


@router.get(
    "/me/appointment-requests/{appointment_request_id}",
    response_model=schemas.appointment_request.AppointmentRequestResponse,
    summary="Get Appointment Request",
    description="Retrieves a specific appointment request by ID for the currently authenticated patient.",
    responses={
        200: {"description": "Appointment request retrieved successfully"},
        404: {"description": "Appointment request not found"},
        401: {"description": "Not authenticated"},
    },
    tags=["Appointments"],
)
def read_patient_appointment_request(
    *,
    db: Session = Depends(deps.get_db),
    current_user_id: str = Depends(deps.get_current_user),
    appointment_request_id: UUID,
) -> schemas.appointment_request.AppointmentRequestResponse:
    """
    Retrieve a specific appointment request by ID.
    """
    # Get the request with related entities and security check that it belongs to this patient
    appointment_request = crud.appointment_request.get_by_id_with_relations(
        db=db, id=appointment_request_id, patient_id=current_user_id
    )

    if appointment_request is None:
        raise HTTPException(status_code=404, detail="Appointment request not found")

    return appointment_request


@router.put(
    "/me/appointment-requests/{appointment_request_id}",
    response_model=schemas.appointment_request.AppointmentRequestResponse,
    summary="Update Appointment Request",
    description="Updates an existing appointment request for the currently authenticated patient.",
    responses={
        200: {"description": "Appointment request updated successfully"},
        404: {"description": "Appointment request not found"},
        401: {"description": "Not authenticated"},
        422: {"description": "Validation error"},
    },
    tags=["Appointments"],
)
def update_appointment_request(
    *,
    db: Session = Depends(deps.get_db),
    current_user_id: str = Depends(deps.get_current_user),
    appointment_request_id: UUID,
    appointment_request_in: schemas.appointment_request.AppointmentRequestUpdate,
) -> schemas.appointment_request.AppointmentRequestResponse:
    """
    Update an existing appointment request.
    """
    # Get the existing request with security check
    appointment_request = crud.appointment_request.get_by_id_with_relations(
        db=db, id=appointment_request_id, patient_id=current_user_id
    )

    if appointment_request is None:
        raise HTTPException(status_code=404, detail="Appointment request not found")

    # Can only update if status is pending
    if appointment_request.status != "pending":
        raise HTTPException(
            status_code=400,
            detail=f"Cannot update appointment request with status '{appointment_request.status}'",
        )

    updated_request = crud.appointment_request.update(
        db=db, db_obj=appointment_request, obj_in=appointment_request_in
    )

    # Get the updated record with related entities loaded
    result = crud.appointment_request.get_by_id_with_relations(
        db=db, id=updated_request.id, patient_id=current_user_id
    )

    # Log the action
    audit.log_audit_event(
        db=db,
        actor_user_id=current_user_id,
        actor_role="patient",
        action="appointment_request.update",
        outcome="SUCCESS",
        target_resource_type="appointment_request",
        target_resource_id=str(appointment_request_id),
    )

    return result


@router.delete(
    "/me/appointment-requests/{appointment_request_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Appointment Request",
    description="Deletes a specific appointment request for the currently authenticated patient.",
    responses={
        204: {"description": "Appointment request deleted successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient or does not own this request"},
        404: {"description": "Appointment request not found"},
    },
    tags=["Appointments"],
)
def delete_patient_appointment_request(
    appointment_request_id: UUID = Path(
        ..., description="The ID of the appointment request to delete"
    ),
    db: Session = Depends(deps.get_db),
    current_user_id: str = Depends(deps.get_current_user),
) -> None:
    """
    Delete a specific appointment request submitted by the current patient.
    """
    logger.debug(
        f"Attempting to delete appointment request {appointment_request_id} for user_id: {current_user_id}"
    )
    # Extract the user ID from current_user_id if it's a TokenPayload object
    patient_id = (
        current_user_id.sub if hasattr(current_user_id, "sub") else current_user_id
    )

    logger.debug(f"DEBUG: Using patient_id: {patient_id} for security check")

    # Use get method first to check if the appointment request exists
    appointment_request = crud.appointment_request.get(db=db, id=appointment_request_id)

    # Add detailed debug logging
    logger.info(f"DEBUG: Found appointment request: {appointment_request is not None}")
    if appointment_request:
        logger.info(
            f"DEBUG: Appointment patient_id: {appointment_request.patient_id} (type: {type(appointment_request.patient_id)})"
        )
        logger.info(f"DEBUG: Current user_id: {patient_id} (type: {type(patient_id)})")
        logger.info(
            f"DEBUG: String comparison: {str(appointment_request.patient_id)} == {patient_id}"
        )
        logger.info(
            f"DEBUG: Direct comparison result: {str(appointment_request.patient_id) == patient_id}"
        )

    if not appointment_request:
        logger.warning(
            f"Appointment request {appointment_request_id} not found for delete by user {patient_id}"
        )
        detail = "Appointment request not found"
        status_code = status.HTTP_404_NOT_FOUND
        raise HTTPException(status_code=status_code, detail=detail)

    # Separate the authorization check for clarity
    if str(appointment_request.patient_id) != patient_id:
        logger.error(
            f"Authorization failed: User {patient_id} attempted to delete request {appointment_request_id}"
        )
        logger.error(f"Request belongs to patient_id: {appointment_request.patient_id}")
        detail = "Not authorized to delete this appointment request"
        status_code = status.HTTP_403_FORBIDDEN
        raise HTTPException(status_code=status_code, detail=detail)

    # Add detailed debug logging
    logger.info(f"DEBUG: Found appointment request: {appointment_request is not None}")

    if not appointment_request:
        logger.warning(
            f"Appointment request {appointment_request_id} not found for delete by user {current_user_id}"
        )
        detail = "Appointment request not found"
        status_code = status.HTTP_404_NOT_FOUND
        raise HTTPException(status_code=status_code, detail=detail)

    # No need for separate authorization check since get_by_id_with_relations already filters by patient_id
    try:
        crud.appointment_request.remove(db=db, id=appointment_request_id)
    except Exception as e:
        logger.error(
            f"Error deleting appointment request {appointment_request_id} for user {current_user_id}: {e}",
            exc_info=True,
        )
        log_audit_event(
            db=db,
            action="DELETE_APPOINTMENT_REQUEST",
            outcome="FAILURE",
            actor_user_id=current_user_id,
            actor_role="patient",
            target_resource_id=str(appointment_request_id),
            details={"reason": str(e)},
        )
        raise HTTPException(
            status_code=500, detail="Failed to delete appointment request."
        ) from e

    logger.info(f"Successfully deleted appointment request: {appointment_request_id}")
    log_audit_event(
        db=db,
        action="DELETE_APPOINTMENT_REQUEST",
        outcome="SUCCESS",
        actor_user_id=current_user_id,
        actor_role="patient",
        target_resource_id=str(appointment_request_id),
    )
    return None


@router.get(
    "/me/primary-clinician",
    response_model=schemas.clinician.ClinicianBasicInfo,
    summary="Get Patient's Primary Clinician",
    description="Retrieves the primary clinician assigned to the currently authenticated patient.",
    responses={
        200: {"description": "Primary clinician retrieved successfully"},
        401: {"description": "Not authenticated"},
        403: {"description": "User is not a patient"},
        404: {"description": "No primary clinician found for this patient"},
    },
    tags=["Patients", "Clinicians"],
)
def get_patient_primary_clinician(
    current_user: TokenPayload = Depends(deps.get_current_patient),
    db: Session = Depends(deps.get_db),
) -> schemas.clinician.ClinicianBasicInfo:
    """
    Retrieve the primary clinician for the currently authenticated patient.
    """
    user_id = current_user.sub
    logger.debug(f"Fetching primary clinician for patient {user_id}")

    primary_clinician = crud.patient.get_primary_clinician(db=db, patient_id=user_id)

    if not primary_clinician:
        logger.warning(f"No primary clinician found for patient {user_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No primary clinician found for this patient",
        )

    log_audit_event(
        db=db,
        action="GET_PRIMARY_CLINICIAN",
        outcome="SUCCESS",
        actor_user_id=str(user_id),
        actor_role="patient",
        target_resource_id=str(primary_clinician.id),
        details={
            "clinician_name": f"{primary_clinician.first_name} {primary_clinician.last_name}"
        },
    )

    return schemas.clinician.ClinicianBasicInfo.model_validate(primary_clinician)
