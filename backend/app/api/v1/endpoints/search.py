from typing import Any

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.core.logging import logger
from app.utils.audit import log_audit_event

router = APIRouter()


@router.get(
    "/search",  # Route relative to the prefix defined in api.py
    response_model=list[schemas.search.UniversalSearchResult],
    summary="Universal Search (Clinician)",
    description="Performs a search across relevant entities (currently Patients) associated with the authenticated clinician.",
    tags=["Search"],
)
async def universal_search(
    q: str = Query(..., min_length=2, description="Search query string"),
    limit: int = Query(default=10, le=50),
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Perform a universal search across entities relevant to the clinician.
    Currently searches Patients by name or email.
    """
    clinician_id = current_clinician.id
    logger.debug(
        f"Clinician {clinician_id} performing universal search. Query: '{q}', Limit: {limit}"
    )

    search_results: list[schemas.search.UniversalSearchResult] = []

    # Use the unified crud.patient pattern
    patients = crud.patient.search_by_clinician(
        db=db, clinician_id=clinician_id, query=q, limit=limit
    )

    logger.debug(f"Found {len(patients)} patient results for query '{q}'")
    for patient in patients:
        search_results.append(
            schemas.search.UniversalSearchResult(
                entity_type="Patient",
                entity_id=patient.id,
                display_name=f"{patient.first_name} {patient.last_name}",
                details=f"DOB: {patient.date_of_birth.strftime('%Y-%m-%d') if patient.date_of_birth else 'N/A'}",
            )
        )

    # --- Search Other Entities (Future) ---
    # Example: Appointments, Notes, etc.
    # if not entity_types or "Appointment" in entity_types:
    #    appointments = crud.appointment.search_appointments_for_clinician(db, clinician_id=clinician_id, query=q, limit=limit)
    #    for appt in appointments:
    #        search_results.append(UniversalSearchResult(...))

    # Apply overall limit if results from multiple sources exceed it
    # This simple implementation relies on the limit applied within each CRUD call.
    # A more robust implementation might fetch slightly more from each source and then limit the combined list.
    limited_results = search_results[:limit]

    # Add audit logging if required
    log_audit_event(
        db=db,
        actor_user_id=str(clinician_id),
        action="UNIVERSAL_SEARCH",
        outcome="SUCCESS",
        actor_role="clinician",
        details={"query": q, "limit": limit, "results_count": len(limited_results)},
    )

    return limited_results
