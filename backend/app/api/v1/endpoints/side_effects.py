import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas, models
from app.api import deps
from app.core.audit_logger import AuditLogEvent, AuditLogger
from app.schemas.side_effect_report import (
    SeverityLevel,
    SideEffectReportCreate,
    SideEffectReportResponse,
    SideEffectReportResponseWithPatientDetails,
    SideEffectReportUpdate,
)

router = APIRouter()
logger = logging.getLogger(__name__)
audit_logger = AuditLogger()


@router.get("/", response_model=list[SideEffectReportResponseWithPatientDetails])
def get_side_effect_reports_for_clinician(
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
    severity: Optional[SeverityLevel] = None,
    report_status: Optional[str] = None,
    sort_by: str = Query(
        "reported_at", description="Field to sort by: 'reported_at' or 'severity'"
    ),
    sort_order: str = Query("desc", description="Sort order: 'asc' or 'desc'"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
) -> list[SideEffectReportResponseWithPatientDetails]:
    """
    Retrieve side effect reports for patients associated with the authenticated clinician.
    Supports filtering by severity and status, as well as sorting and pagination.
    Returns detailed patient information within each report.
    """
    logger.info(
        f"API: Clinician {current_clinician.clerk_id} requesting side effect reports. Filters: severity={severity}, status={report_status}"
    )

    try:
        # Get the clinician database record using the sub (Clerk ID) from the token
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_clinician.clerk_id
        )
        if not clinician_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        reports = crud.side_effect_report.get_for_clinician(
            db=db,
            clinician_id=clinician_db.id,
            severity=severity,
            status=report_status,
            sort_by=sort_by,
            sort_order=sort_order,
            skip=skip,
            limit=limit,
        )

        # Log the successful retrieval
        audit_logger.log(
            event=AuditLogEvent.VIEW_SIDE_EFFECT_REPORTS,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=None,  # No specific resource ID for a list
            details={
                "filters": {
                    "severity": severity.value if severity else None,
                    "status": report_status,
                },
                "pagination": {"skip": skip, "limit": limit},
                "count": len(reports),
            },
        )

        return reports

    except Exception as e:
        logger.error(
            f"Error retrieving side effect reports for clinician {current_clinician.clerk_id}",
            exc_info=True,
        )
        # Log the error
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=None,
            details={
                "error": str(e),
                "action": "get_side_effect_reports",
                "filters": {
                    "severity": severity.value if severity else None,
                    "status": report_status,
                },
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving side effect reports",
        )


@router.get("/{report_id}", response_model=SideEffectReportResponse)
def get_side_effect_report_detail(
    report_id: UUID,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Get detailed information about a specific side effect report.
    Clinician must be associated with the patient who submitted the report.
    """
    logger.info(
        f"API: Clinician {current_clinician.clerk_id} requesting side effect report {report_id}"
    )

    # Get the clinician database record using the sub (Clerk ID) from the token
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    # Get the report
    report = crud.side_effect_report.get_by_id(db=db, report_id=report_id)
    if not report:
        logger.warning(f"Side effect report {report_id} not found")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Side effect report not found",
        )

    # Verify clinician has access to this patient's data
    patient = crud.patient.get(db=db, id=report.patient_id)
    if not patient:
        logger.error(f"Patient {report.patient_id} not found for report {report_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found",
        )

    # Check if clinician is associated with the patient
    clinician_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_db.id
    )
    if patient.id not in [p.id for p in clinician_patients]:
        logger.warning(
            f"Clinician {clinician_db.id} attempted unauthorized access report "
            f"{report_id} for non-associated patient {patient.id}"
        )
        # Log unauthorized access attempt
        audit_logger.log(
            event=AuditLogEvent.UNAUTHORIZED_ACCESS,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "resource_type": "side_effect_report",
                "patient_id": str(patient.id),
                "action": "view",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this side effect report",
        )

    # Log the successful retrieval
    audit_logger.log(
        event=AuditLogEvent.VIEW_SIDE_EFFECT_REPORT,
        user_id=str(current_clinician.clerk_id),
        user_type="clinician",
        resource_id=str(report_id),
        details={
            "patient_id": str(patient.id),
            "severity": report.severity,
            "status": report.status,
        },
    )

    return report


@router.put("/{report_id}", response_model=SideEffectReportResponse)
def update_side_effect_report(
    report_id: UUID,
    report_update: SideEffectReportUpdate,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Update a side effect report.
    Clinicians can update the status of the report (e.g., mark as "Reviewed" or "Resolved").
    """
    logger.info(
        f"API: Clinician {current_clinician.clerk_id} updating side effect report {report_id}"
    )

    # Get the clinician database record using the sub (Clerk ID) from the token
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    # Get the report
    db_report = crud.side_effect_report.get_by_id(db=db, report_id=report_id)
    if not db_report:
        logger.warning(f"Side effect report {report_id} not found for update")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Side effect report not found",
        )

    # Verify clinician has access to this patient's data
    patient = crud.patient.get(db=db, id=db_report.patient_id)
    if not patient:
        logger.error(f"Patient {db_report.patient_id} not found for report {report_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found",
        )

    # Check if clinician is associated with the patient
    clinician_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_db.id
    )
    if patient.id not in [p.id for p in clinician_patients]:
        logger.warning(
            f"Clinician {clinician_db.id} attempted unauthorized update report "
            f"{report_id} for non-associated patient {patient.id}"
        )
        # Log unauthorized access attempt
        audit_logger.log(
            event=AuditLogEvent.UNAUTHORIZED_ACCESS,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "resource_type": "side_effect_report",
                "patient_id": str(patient.id),
                "action": "update",
                "update_data": report_update.model_dump(exclude_unset=True),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this side effect report",
        )

    try:
        # Track what fields are being updated for audit logging
        update_data = report_update.model_dump(exclude_unset=True)

        # Update the report
        updated_report = crud.side_effect_report.update(
            db=db, db_obj=db_report, obj_in=report_update
        )

        # Log the update
        audit_logger.log(
            event=AuditLogEvent.UPDATE_SIDE_EFFECT_REPORT,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "patient_id": str(patient.id),
                "updated_fields": update_data,
                "previous_status": db_report.status,
                "new_status": updated_report.status,
            },
        )

        return updated_report

    except Exception as e:
        logger.error(f"Error updating side effect report {report_id}", exc_info=True)
        # Log the error
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "error": str(e),
                "action": "update_side_effect_report",
                "update_data": report_update.model_dump(exclude_unset=True),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the side effect report",
        )


@router.delete("/{report_id}", status_code=204)
def delete_side_effect_report(
    report_id: UUID,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> None:
    """
    Delete a side effect report.
    Only clinicians associated with the patient can delete the report.
    Returns 204 No Content on success.
    """
    logger.info(
        f"API: Clinician {current_clinician.clerk_id} deleting side effect report {report_id}"
    )

    # Get the clinician database record using the sub (Clerk ID) from the token
    clinician_db = crud.clinician.get_clinician_by_clerk_id(
        db, clerk_id=current_clinician.clerk_id
    )
    if not clinician_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Clinician record not found",
        )

    # Get the report
    report = crud.side_effect_report.get_by_id(db=db, report_id=report_id)
    if not report:
        logger.warning(f"Side effect report {report_id} not found for deletion")
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "error": "Side effect report not found",
                "action": "delete_side_effect_report",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Side effect report not found",
        )

    # Verify clinician has access to this patient's data
    patient = crud.patient.get(db=db, id=report.patient_id)
    if not patient:
        logger.error(f"Patient {report.patient_id} not found for report {report_id}")
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "error": "Patient not found",
                "action": "delete_side_effect_report",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found",
        )

    # Check if clinician is associated with the patient
    clinician_patients = crud.clinician.get_patients_for_clinician(
        db=db, clinician_id=clinician_db.id
    )
    if patient.id not in [p.id for p in clinician_patients]:
        logger.warning(
            f"Clinician {clinician_db.id} attempted unauthorized delete report {report_id} "
            f"for non-associated patient {patient.id}"
        )
        audit_logger.log(
            event=AuditLogEvent.UNAUTHORIZED_ACCESS,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "resource_type": "side_effect_report",
                "patient_id": str(patient.id),
                "action": "delete",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this side effect report",
        )

    try:
        deleted_report = crud.side_effect_report.delete(db=db, report_id=report_id)
        if not deleted_report:
            logger.warning(
                f"Side effect report {report_id} could not be deleted (not found or unauthorized)"
            )
            audit_logger.log(
                event=AuditLogEvent.ERROR,
                user_id=str(current_clinician.clerk_id),
                user_type="clinician",
                resource_id=str(report_id),
                details={
                    "error": "Delete operation failed",
                    "action": "delete_side_effect_report",
                },
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Side effect report not found or not authorized",
            )
        # Log the successful deletion
        audit_logger.log(
            event=AuditLogEvent.DELETE_SIDE_EFFECT_REPORT,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "patient_id": str(patient.id),
                "severity": report.severity,
                "status": report.status,
            },
        )
        return
    except Exception as e:
        logger.error(f"Error deleting side effect report {report_id}", exc_info=True)
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(report_id),
            details={
                "error": str(e),
                "action": "delete_side_effect_report",
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the side effect report",
        )


@router.post("/{patient_id}", response_model=SideEffectReportResponse)
def create_side_effect_report(
    patient_id: str,
    report_in: SideEffectReportCreate,
    db: Session = Depends(deps.get_db),
    current_clinician: models.Clinician = Depends(deps.get_current_clinician),
) -> Any:
    """
    Create a side effect report for a patient.
    Clinicians can create reports on behalf of patients during appointments or phone calls.
    """
    logger.info(
        f"API: Clinician {current_clinician.clerk_id} creating side effect report for patient {patient_id}"
    )

    try:
        # Get the clinician database record using the sub (Clerk ID) from the token
        clinician_db = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id=current_clinician.clerk_id
        )
        if not clinician_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Clinician record not found",
            )

        # Verify the patient exists by Clerk ID using the standard get method
        patient = crud.patient.get(db=db, id=patient_id)
        if not patient:
            logger.warning(
                f"Patient with Clerk ID {patient_id} not found for creating side effect report"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found",
            )

        # Check if clinician is associated with the patient
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician_db.id
        )
        if patient.id not in [p.id for p in clinician_patients]:
            logger.warning(
                f"Authorization failed: Clinician {clinician_db.id} attempted to create report for non-associated patient {patient_id}"
            )
            # Log unauthorized access attempt
            audit_logger.log(
                event=AuditLogEvent.UNAUTHORIZED_ACCESS,
                user_id=str(current_clinician.clerk_id),
                user_type="clinician",
                resource_id=None,
                details={
                    "resource_type": "side_effect_report",
                    "patient_id": patient_id,
                    "action": "create",
                },
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to create side effect reports for this patient",
            )

        # Always set clinician_id from the authenticated user
        report_data = report_in.model_copy(
            update={"clinician_id": current_clinician.clerk_id}
        )
        created_report = crud.side_effect_report.create(
            db=db,
            obj_in=report_data,
            patient_id=patient.id,
        )
        logger.info(
            f"Created side effect report {created_report.id} for patient {patient_id} by clinician {current_clinician.clerk_id}"
        )
        audit_logger.log(
            event=AuditLogEvent.CREATE_SIDE_EFFECT_REPORT,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=str(created_report.id),
            details={
                "patient_id": patient_id,
                "severity": created_report.severity,
                "status": created_report.status,
            },
        )
        return created_report

    except Exception as e:
        audit_logger.log(
            event=AuditLogEvent.ERROR,
            user_id=str(current_clinician.clerk_id),
            user_type="clinician",
            resource_id=None,
            details={
                "error": str(e),
                "action": "create_side_effect_report",
                "patient_id": patient_id,
                "report_data": report_in.model_dump(),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the side effect report",
        )
