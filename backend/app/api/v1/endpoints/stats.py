# backend/app/api/v1/endpoints/stats.py
import logging

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.schemas.stats import DashboardCounts

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/dashboard-counts", response_model=DashboardCounts)
def get_dashboard_counts(db: Session = Depends(deps.get_db)) -> DashboardCounts:
    """
    Retrieve counts for key items on the dashboard.
    """
    try:
        clinics_count = crud.clinic.get_count(db)
        # Use the count of active access codes for invitations
        active_invitations_count = crud.access_code.get_active_access_codes_count(db)
        total_clinicians_count = crud.clinician.get_count(db)

        return DashboardCounts(
            clinics_count=clinics_count,
            active_invitations_count=active_invitations_count,
            total_clinicians_count=total_clinicians_count,
        )
    except Exception as e:
        logger.error(f"Error fetching dashboard counts: {e}", exc_info=True)
        # Re-raise or handle as appropriate for your error strategy
        # For now, just re-raising for visibility
        raise
