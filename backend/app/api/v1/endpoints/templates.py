import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api import deps
from app.services.template_validation_service import TemplateValidationService
from app.utils.audit import log_audit_event

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get(
    "/",
    response_model=list[schemas.Template],
    summary="Get Templates",
    description="Retrieves a list of available LLM templates based on user access level.",
)
async def get_templates(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        20, ge=1, le=100, description="Maximum number of records to return"
    ),
    active_only: bool = Query(True, description="Return only active templates"),
    clinic_id: Optional[UUID] = Query(None, description="Filter by clinic ID"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> list[schemas.Template]:
    """Get available templates based on user role."""
    try:
        # Determine user's roles for permission checking
        if isinstance(current_user.role, list):
            roles = current_user.role
        else:
            roles = [current_user.role]

        # Get accessible templates using the validation service
        templates = (
            await TemplateValidationService.validate_and_get_accessible_templates(
                db=db,
                user_id=current_user.sub,
                role=roles[0] if len(roles) == 1 else roles,
            )
        )

        # Apply additional filters
        if active_only:
            templates = [t for t in templates if t.is_active]

        if clinic_id:
            templates = [t for t in templates if t.clinic_id == clinic_id]

        # Apply pagination
        start = skip
        end = skip + limit
        templates = templates[start:end]

        # Log the template access
        log_audit_event(
            db=db,
            event_type="GET_TEMPLATES",
            user_id=current_user.sub,
            user_role=current_user.role,
            details={
                "count": len(templates),
                "filters": {
                    "active_only": active_only,
                    "clinic_id": str(clinic_id) if clinic_id else None,
                },
            },
        )

        return templates

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving templates: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving templates",
        )


@router.get(
    "/{template_id}",
    response_model=schemas.TemplateWithRoles,
    summary="Get Template with Roles",
    description="Retrieves a specific template with its associated roles.",
)
async def get_template(
    template_id: UUID = Path(..., description="The ID of the template to retrieve"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db),
) -> schemas.TemplateWithRoles:
    """Get a specific template with its associated roles."""
    try:
        # Determine user's roles for permission checking
        if isinstance(current_user.role, list):
            roles = current_user.role
        else:
            roles = [current_user.role]

        # Validate template access using the validation service
        await TemplateValidationService.validate_template_access(
            db=db, template_id=template_id, user_id=current_user.sub, roles=roles
        )

        # Get the template with roles after access is validated
        template_with_roles = crud.template.get_with_roles(db, template_id=template_id)
        if not template_with_roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
            )

        # Log the template access
        log_audit_event(
            db=db,
            event_type="GET_TEMPLATE",
            user_id=current_user.sub,
            user_role=current_user.role,
            target_resource_type="template",
            target_resource_id=str(template_id),
            details={
                "template_name": template_with_roles["name"],
                "roles": template_with_roles["roles"],
                "clinic_id": (
                    str(template_with_roles["clinic_id"])
                    if template_with_roles["clinic_id"]
                    else None
                ),
            },
        )

        return template_with_roles

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving template {template_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving template",
        )


@router.post(
    "/",
    response_model=schemas.Template,
    status_code=status.HTTP_201_CREATED,
    summary="Create Template",
    description="Creates a new LLM template with role assignments (admin only).",
)
async def create_template(
    template_in: schemas.TemplateCreate = Body(..., description="Template data"),
    roles: list[str] = Body(..., description="Roles that can access this template"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_admin),
    db: Session = Depends(deps.get_db),
) -> schemas.Template:
    """Create a new template with roles (admin only)."""
    try:
        # Check if template with same name already exists for the clinic
        if template_in.clinic_id:
            existing = crud.template.get_by_name(
                db, name=template_in.name, clinic_id=template_in.clinic_id
            )
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Template with name '{template_in.name}' already exists for this clinic",
                )

        # Validate template structure, actions, and system prompt
        template_data = template_in.dict()
        template_data["roles"] = roles
        TemplateValidationService.validate_template(template_data)

        # Create the template
        template = crud.template.create(db, obj_in=template_in)

        # Add roles
        for role in roles:
            role_in = schemas.TemplateRoleCreate(template_id=template.id, role=role)
            crud.template_role.create(db, obj_in=role_in)

        # Log the template creation
        log_audit_event(
            db=db,
            event_type="CREATE_TEMPLATE",
            user_id=current_user.sub,
            user_role=current_user.role,
            target_resource_type="template",
            target_resource_id=str(template.id),
            details={
                "template_name": template.name,
                "roles": roles,
                "clinic_id": str(template.clinic_id) if template.clinic_id else None,
            },
        )

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating template: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating template",
        )


@router.put(
    "/{template_id}",
    response_model=schemas.Template,
    summary="Update Template",
    description="Updates an existing LLM template (admin only).",
)
async def update_template(
    template_id: UUID = Path(..., description="The ID of the template to update"),
    template_in: schemas.TemplateUpdate = Body(
        ..., description="Updated template data"
    ),
    roles: Optional[list[str]] = Body(
        None, description="Updated roles that can access this template"
    ),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_admin),
    db: Session = Depends(deps.get_db),
) -> schemas.Template:
    """Update an existing template (admin only)."""
    try:
        # Check if template exists
        template = crud.template.get(db, id=template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
            )

        # If name is changed, check for conflicts
        if template_in.name and template_in.name != template.name:
            existing = crud.template.get_by_name(
                db, name=template_in.name, clinic_id=template.clinic_id
            )
            if existing and existing.id != template_id:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Template with name '{template_in.name}' already exists for this clinic",
                )

        # Validate template updates
        if template_in.system_prompt or template_in.actions:
            # Create a composite template for validation
            template_data = template.dict()
            update_data = template_in.dict(exclude_unset=True)

            # Update with new data for validation
            template_data.update(update_data)

            # Add roles for validation if provided
            if roles is not None:
                template_data["roles"] = roles
            else:
                # Use existing roles
                template_roles = crud.template_role.get_by_template(
                    db, template_id=template_id
                )
                template_data["roles"] = [tr.role for tr in template_roles]

            # Validate the updated template
            TemplateValidationService.validate_template(template_data)

        # Update the template
        template = crud.template.update(db, db_obj=template, obj_in=template_in)

        # Update roles if provided
        if roles is not None:
            # Remove existing roles
            crud.template_role.remove_all_by_template(db, template_id=template_id)

            # Add new roles
            for role in roles:
                role_in = schemas.TemplateRoleCreate(template_id=template.id, role=role)
                crud.template_role.create(db, obj_in=role_in)

        # Log the template update
        log_audit_event(
            db=db,
            event_type="UPDATE_TEMPLATE",
            user_id=current_user.sub,
            user_role=current_user.role,
            target_resource_type="template",
            target_resource_id=str(template.id),
            details={
                "template_name": template.name,
                "updated_roles": roles if roles is not None else "unchanged",
                "clinic_id": str(template.clinic_id) if template.clinic_id else None,
            },
        )

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template {template_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating template",
        )


@router.delete(
    "/{template_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Template",
    description="Deletes an LLM template (admin only).",
)
async def delete_template(
    template_id: UUID = Path(..., description="The ID of the template to delete"),
    current_user: schemas.auth.TokenPayload = Depends(deps.get_current_admin),
    db: Session = Depends(deps.get_db),
) -> None:
    """Delete a template (admin only)."""
    try:
        # Check if template exists
        template = crud.template.get(db, id=template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
            )

        # Store template details for logging before deletion
        template_name = template.name
        clinic_id = template.clinic_id

        # Delete all associated roles first
        crud.template_role.remove_all_by_template(db, template_id=template_id)

        # Delete the template
        crud.template.remove(db, id=template_id)

        # Log the template deletion
        log_audit_event(
            db=db,
            event_type="DELETE_TEMPLATE",
            user_id=current_user.sub,
            user_role=current_user.role,
            target_resource_type="template",
            target_resource_id=str(template_id),
            details={
                "template_name": template_name,
                "clinic_id": str(clinic_id) if clinic_id else None,
            },
        )

        return None

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template {template_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting template",
        )
