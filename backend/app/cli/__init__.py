"""CLI commands for the PulseTrack application."""

import click

import app.cli.evaluation_commands as evaluation_commands
from app.cli.embedding_commands import embedding


@click.group()
def cli():
    """PulseTrack CLI."""
    pass


# Register command groups
cli.add_command(embedding)


# Create an evaluation command group
@cli.group()
def evaluation():
    """Evaluation tools for the RAG system."""
    pass


# Add evaluation commands
evaluation.add_command(evaluation_commands.app, "run")


if __name__ == "__main__":
    cli()
