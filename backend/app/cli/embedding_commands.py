"""CLI commands for the embedding pipeline.

This module implements CLI commands for managing the embedding pipeline.
"""

import logging
import time
from typing import Optional

import click
import redis

from app.core.config import settings
# from app.core.metrics import increment_counter, set_gauge  # Missing module
from app.workers.embedding_worker import (
    EMBEDDING_CONSUMER_GROUP,
    EMBEDDING_STREAM,
    EmbeddingWorker,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@click.group()
def embedding():
    """Commands for managing the embedding pipeline."""
    pass


@embedding.command()
@click.option(
    "--collection",
    "-c",
    help="Collection to reembed. Use '*' for all collections.",
    required=True,
)
@click.option(
    "--model",
    "-m",
    help="Model to use for reembedding. Defaults to the current default model.",
)
@click.option(
    "--batch-size",
    "-b",
    help="Batch size for reembedding.",
    type=int,
    default=128,
)
@click.option(
    "--dry-run",
    "-d",
    is_flag=True,
    help="Show what would be reembedded without actually doing it.",
)
def reembed(collection: str, model: Optional[str], batch_size: int, dry_run: bool):
    """Reembed documents in a collection.

    Args:
        collection: Collection to reembed. Use '*' for all collections.
        model: Model to use for reembedding. Defaults to the current default model.
        batch_size: Batch size for reembedding.
        dry_run: Show what would be reembedded without actually doing it.
    """
    # from app.core.qdrant_client import qdrant_client  # Missing module
    # from app.workers.embedding_emitters import get_sqlalchemy_emitter  # Missing module
    raise NotImplementedError("The required modules for this command are missing (qdrant_client, embedding_emitters).")

    # Create a Redis client
    redis_client = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        decode_responses=True,
    )

    # Get the Qdrant client
    client = qdrant_client.get_client()

    # Get collections to reembed
    collections = []
    if collection == "*":
        # Get all collections
        response = client.get_collections()
        collections = [c.name for c in response.collections]
        logger.info("Found %d collections: %s", len(collections), collections)
    else:
        collections = [collection]

    # Get the embedding emitter
    emitter = get_sqlalchemy_emitter(redis_client)

    # Process each collection
    for coll in collections:
        logger.info("Processing collection %s", coll)

        # Get all points in the collection
        try:
            # Check if collection exists
            if not qdrant_client.check_collection_exists(coll):
                logger.warning("Collection %s does not exist, skipping", coll)
                continue

            # Get scroll API to iterate through all points
            points_processed = 0
            scroll_response = client.scroll(
                collection_name=coll,
                limit=batch_size,
                with_payload=True,
                with_vectors=False,
            )

            while scroll_response.points:
                logger.info(
                    "Processing batch of %d points from collection %s",
                    len(scroll_response.points),
                    coll,
                )

                # Process each point
                for point in scroll_response.points:
                    payload = point.payload
                    if not payload:
                        logger.warning("Point %s has no payload, skipping", point.id)
                        continue

                    # Extract data from payload
                    doc_id = payload.get("doc_id", str(point.id))
                    source_id = payload.get("source_id")
                    doc_type = payload.get("doc_type")
                    text = payload.get("text_chunk")
                    metadata = {
                        k: v
                        for k, v in payload.items()
                        if k not in ["doc_id", "source_id", "doc_type", "text_chunk"]
                    }

                    if not all([doc_id, source_id, doc_type, text]):
                        logger.warning(
                            "Point %s is missing required fields, skipping",
                            point.id,
                        )
                        continue

                    # Add to embedding queue
                    if not dry_run:
                        emitter.add_to_queue(
                            collection=coll,
                            doc_id=doc_id,
                            source_id=source_id,
                            doc_type=doc_type,
                            text=text,
                            metadata=metadata,
                        )

                    points_processed += 1

                # Get next batch
                if not scroll_response.next_page_offset:
                    break

                scroll_response = client.scroll(
                    collection_name=coll,
                    limit=batch_size,
                    with_payload=True,
                    with_vectors=False,
                    offset=scroll_response.next_page_offset,
                )

            logger.info(
                "Processed %d points from collection %s",
                points_processed,
                coll,
            )

        except Exception as e:
            logger.error("Error processing collection %s: %s", coll, e)


@embedding.command()
@click.option(
    "--consumer-name",
    "-n",
    help="Name for this consumer instance.",
    default=f"worker-{time.time()}",
)
@click.option(
    "--batch-size",
    "-b",
    help="Maximum number of tasks to process in a batch.",
    type=int,
    default=128,
)
@click.option(
    "--buffer-check-interval",
    "-i",
    help="Interval in seconds to check the disk buffer.",
    type=int,
    default=60,
)
@click.option(
    "--reclaim-interval",
    "-r",
    help="Interval in seconds to reclaim pending messages.",
    type=int,
    default=300,
)
@click.option(
    "--min-idle-time",
    "-m",
    help="Minimum idle time in milliseconds before reclaiming a message.",
    type=int,
    default=30000,  # 30 seconds
)
@click.option(
    "--run-once",
    "-o",
    is_flag=True,
    help="Process a single batch and exit.",
)
def worker(
    consumer_name: str,
    batch_size: int,
    buffer_check_interval: int,
    reclaim_interval: int,
    min_idle_time: int,
    run_once: bool,
):
    """Run the embedding worker process.

    Args:
        consumer_name: Unique name for this consumer instance
        batch_size: Maximum number of tasks to process in a batch
        buffer_check_interval: Interval in seconds to check the disk buffer
        reclaim_interval: Interval in seconds to reclaim pending messages
        min_idle_time: Minimum idle time in milliseconds before reclaiming a message
        run_once: Process a single batch and exit.
    """
    # Create Redis client
    redis_client = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        decode_responses=False,  # We'll decode manually to handle binary data
    )
    # Get the embedding worker
    worker = EmbeddingWorker(redis_client)

    # Initialize metrics
    start_time = time.time()
    total_tasks_processed = 0
    # set_gauge("embedding_worker_uptime_seconds", 0)
    # set_gauge("embedding_worker_tasks_processed_total", 0)
    raise NotImplementedError("The required metrics module is missing. This command cannot be run.")

    # Process tasks
    last_buffer_check = time.time()
    last_reclaim = time.time()

    try:
        while True:
            # Process a batch of tasks
            tasks_processed = worker.process_embedding_tasks(consumer_name, batch_size)
            total_tasks_processed += tasks_processed

            # Update metrics
            uptime = time.time() - start_time
            set_gauge("embedding_worker_uptime_seconds", uptime)
            set_gauge("embedding_worker_tasks_processed_total", total_tasks_processed)

            if tasks_processed > 0:
                logger.info(
                    f"Processed {tasks_processed} tasks (total: {total_tasks_processed})"
                )

            # Check if we should process the disk buffer
            if time.time() - last_buffer_check > buffer_check_interval:
                logger.info("Checking disk buffer...")
                buffer_tasks = worker.process_disk_buffer()
                if buffer_tasks > 0:
                    logger.info(f"Processed {buffer_tasks} tasks from disk buffer")
                    total_tasks_processed += buffer_tasks
                    set_gauge(
                        "embedding_worker_tasks_processed_total", total_tasks_processed
                    )
                    increment_counter("embedding_buffer_tasks_processed", buffer_tasks)
                last_buffer_check = time.time()

            # Check if we should reclaim pending messages
            if time.time() - last_reclaim > reclaim_interval:
                logger.info("Reclaiming pending messages...")
                reclaimed = worker.reclaim_pending_messages(
                    consumer_name, min_idle_time
                )
                if reclaimed > 0:
                    logger.info(f"Reclaimed {reclaimed} pending messages")
                    increment_counter("embedding_messages_reclaimed", reclaimed)
                last_reclaim = time.time()

            # Exit if we're only running once
            if run_once:
                break

            # Sleep for a short time to avoid hammering Redis
            time.sleep(0.1)
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker error: {e}", exc_info=True)
    finally:
        logger.info(
            f"Worker exiting. Processed {total_tasks_processed} tasks in {time.time() - start_time:.2f} seconds"
        )


@embedding.command()
@click.option(
    "--stream",
    "-s",
    help="Stream to inspect.",
    default=EMBEDDING_STREAM,
)
@click.option(
    "--count",
    "-c",
    help="Number of messages to inspect.",
    type=int,
    default=10,
)
def inspect(stream, count):
    """Inspect the embedding queue.

    Args:
        stream: Stream to inspect
        count: Number of messages to inspect
    """
    # Create a Redis client
    r = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        decode_responses=True,
    )

    # Get stream info
    stream_info = r.xinfo_stream(stream)
    logger.info(
        "Stream %s has %d messages, first: %s, last: %s",
        stream,
        stream_info["length"],
        (
            stream_info["first-entry"][0].decode()
            if stream_info["first-entry"]
            else "None"
        ),
        stream_info["last-entry"][0].decode() if stream_info["last-entry"] else "None",
    )

    # Get consumer group info
    try:
        group_info = r.xinfo_groups(stream)
        for group in group_info:
            logger.info(
                "Consumer group %s has %d consumers, pending: %d, last delivered: %s",
                group["name"].decode(),
                group["consumers"],
                group["pending"],
                group["last-delivered-id"].decode(),
            )

            # Get consumer info
            consumer_info = r.xinfo_consumers(stream, group["name"])
            for consumer in consumer_info:
                logger.info(
                    "  Consumer %s has %d pending messages, idle: %d ms",
                    consumer["name"].decode(),
                    consumer["pending"],
                    consumer["idle"],
                )
    except redis.ResponseError as e:
        if "NOGROUP" in str(e):
            logger.info("No consumer groups found for stream %s", stream)
        else:
            raise

    # Get recent messages
    messages = r.xrevrange(stream, count=count)
    logger.info("Recent messages:")
    for message_id, message_data in messages:
        logger.info(
            "  Message %s: %s",
            message_id.decode(),
            {
                k.decode(): v.decode() if isinstance(v, bytes) else v
                for k, v in message_data.items()
            },
        )


@embedding.command()
@click.option(
    "--stream",
    "-s",
    help="Stream to clear.",
    default=EMBEDDING_STREAM,
)
@click.option(
    "--group",
    "-g",
    help="Consumer group to clear.",
    default=EMBEDDING_CONSUMER_GROUP,
)
@click.option(
    "--consumer",
    "-c",
    help="Consumer to clear. If not specified, all consumers in the group will be cleared.",
)
@click.option(
    "--delete-stream",
    "-d",
    is_flag=True,
    help="Delete the stream entirely.",
)
@click.option(
    "--confirm",
    "-y",
    is_flag=True,
    help="Confirm the operation without prompting.",
)
def clear(
    stream: str,
    group: str,
    consumer: Optional[str],
    delete_stream: bool,
    confirm: bool,
):
    """Clear the embedding queue.

    Args:
        stream: Stream to clear.
        group: Consumer group to clear.
        consumer: Consumer to clear. If not specified, all consumers in the group will be cleared.
        delete_stream: Delete the stream entirely.
        confirm: Confirm the operation without prompting.
    """
    # Get the Redis client
    redis_client = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        decode_responses=True,
    )

    # Confirm the operation
    if not confirm:
        if delete_stream:
            click.confirm(
                f"Are you sure you want to delete the stream {stream}?",
                abort=True,
            )
        else:
            click.confirm(
                f"Are you sure you want to clear pending messages for group {group}?",
                abort=True,
            )

    # Delete the stream
    if delete_stream:
        redis_client.delete(stream)
        logger.info("Deleted stream %s", stream)
        return

    # Get pending messages
    try:
        pending_info = redis_client.xpending(stream, group)
        if pending_info[0] == 0:
            logger.info("No pending messages for group %s", group)
            return

        logger.info(
            "Found %d pending messages for group %s",
            pending_info[0],
            group,
        )

        # Get pending messages
        pending_messages = redis_client.xpending_range(
            stream,
            group,
            min="-",
            max="+",
            count=pending_info[0],
        )

        # Group by consumer
        consumers = {}
        for message in pending_messages:
            consumer_name = message["consumer"].decode()
            if consumer and consumer_name != consumer:
                continue

            if consumer_name not in consumers:
                consumers[consumer_name] = []

            consumers[consumer_name].append(message["message_id"])

        # Process each consumer
        for consumer_name, message_ids in consumers.items():
            logger.info(
                "Claiming and acknowledging %d messages for consumer %s",
                len(message_ids),
                consumer_name,
            )

            # Claim the messages
            redis_client.xclaim(
                stream,
                group,
                consumer_name,
                min_idle_time=0,
                message_ids=message_ids,
            )

            # Acknowledge the messages
            redis_client.xack(stream, group, *message_ids)

        logger.info("Cleared pending messages for group %s", group)

    except redis.ResponseError as e:
        if "NOGROUP" in str(e):
            logger.info("No consumer group %s found for stream %s", group, stream)
        else:
            raise
