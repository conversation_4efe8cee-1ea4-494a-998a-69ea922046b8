import logging
from enum import Enum
from typing import Any, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)


class AuditLogEvent(Enum):
    """Enum for audit log events used throughout the application."""

    # General events
    ERROR = "ERROR"
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS"

    # Side effect report events
    VIEW_SIDE_EFFECT_REPORTS = "VIEW_SIDE_EFFECT_REPORTS"
    VIEW_SIDE_EFFECT_REPORT = "VIEW_SIDE_EFFECT_REPORT"
    UPDATE_SIDE_EFFECT_REPORT = "UPDATE_SIDE_EFFECT_REPORT"
    CREATE_SIDE_EFFECT_REPORT = "CREATE_SIDE_EFFECT_REPORT"
    DELETE_SIDE_EFFECT_REPORT = "DELETE_SIDE_EFFECT_REPORT"

    # Appointment events
    CREATE_APPOINTMENT = "CREATE_APPOINTMENT"
    UPDATE_APPOINTMENT = "UPDATE_APPOINTMENT"
    CANCEL_APPOINTMENT = "CANCEL_APPOINTMENT"
    VIEW_APPOINTMENT = "VIEW_APPOINTMENT"

    # Medication request events
    CREATE_MEDICATION_REQUEST = "CREATE_MEDICATION_REQUEST"
    UPDATE_MEDICATION_REQUEST = "UPDATE_MEDICATION_REQUEST"
    APPROVE_MEDICATION_REQUEST = "APPROVE_MEDICATION_REQUEST"
    REJECT_MEDICATION_REQUEST = "REJECT_MEDICATION_REQUEST"
    
    # Patient alert events
    VIEW_PATIENT_ALERTS = "VIEW_PATIENT_ALERTS"
    VIEW_PATIENT_ALERT = "VIEW_PATIENT_ALERT"
    UPDATE_PATIENT_ALERT = "UPDATE_PATIENT_ALERT"


class AuditLogger:
    """
    A class for logging audit events consistently throughout the application.
    This is a wrapper around the log_audit_event function that provides a more
    object-oriented interface and standardizes the event types.
    """

    def __init__(self):
        """Initialize the AuditLogger."""
        self.logger = logging.getLogger(__name__)

    def log(
        self,
        *,
        event: AuditLogEvent,
        user_id: str,
        user_type: str,
        resource_id: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        db: Optional[Session] = None,
    ) -> None:
        """
        Log an audit event.

        Args:
            event: The event type from AuditLogEvent enum
            user_id: ID of the user performing the action
            user_type: Type of user (e.g., 'patient', 'clinician', 'admin')
            resource_id: ID of the resource being acted upon (optional)
            details: Additional details about the event (optional)
            db: Database session (optional) - if not provided, only logs to console
        """
        try:
            # Convert event to string if it's an enum
            event_str = event.value if isinstance(event, Enum) else str(event)

            # Convert UUID to string if needed
            if isinstance(user_id, UUID):
                user_id = str(user_id)
            if isinstance(resource_id, UUID):
                resource_id = str(resource_id)

            # If no DB session is provided, just log to console
            if db is None:
                self.logger.info(
                    f"AUDIT: {event_str} by {user_type} {user_id} "
                    f"on resource {resource_id if resource_id else 'N/A'} "
                    f"with details: {details if details else 'N/A'}"
                )
                return

            # Determine outcome based on event
            outcome = "SUCCESS"
            if event == AuditLogEvent.ERROR:
                outcome = "FAILURE"
            elif event == AuditLogEvent.UNAUTHORIZED_ACCESS:
                outcome = "UNAUTHORIZED"

            # Determine resource type based on event
            resource_type = None
            if "SIDE_EFFECT" in event_str:
                resource_type = "SideEffectReport"
            elif "APPOINTMENT" in event_str:
                resource_type = "Appointment"
            elif "MEDICATION_REQUEST" in event_str:
                resource_type = "MedicationRequest"

            # Log the event using the utility function
            log_audit_event(
                db=db,
                action=event_str,
                outcome=outcome,
                actor_user_id=user_id,
                actor_role=user_type,
                target_resource_type=resource_type,
                target_resource_id=resource_id,
                details=details,
            )

        except Exception as e:
            self.logger.error(f"Failed to log audit event: {e}", exc_info=True)
