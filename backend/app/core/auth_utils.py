import base64
import json
import logging
import secrets  # Moved import to top
import string  # Moved import to top
from datetime import datetime, timedelta, timezone
from typing import Any

import bcrypt  # Import bcrypt for password hashing
import jwt

# Import Clerk SDK and relevant helpers/models
from clerk_backend_api import Clerk
from clerk_backend_api import models as clerk_models  # Import Clerk error models
from clerk_backend_api.jwks_helpers import (
    AuthenticateRequestOptions,
    RequestState,
)  # Import necessary components
from fastapi import (
    HTTPException,
    Request,
)  # Import Request for authenticate_request
from pydantic import ValidationError

from app.core.config import settings
from app.schemas.auth import TokenPayload

logger = logging.getLogger(__name__)

# Initialize Clerk client
# Expects CLERK_SECRET_KEY environment variable to be set via app.core.config.settings
# Note: The SDK might handle initialization differently if using authenticate_request directly without a global client instance.
# Let's keep the client for now, but authenticate_request might re-initialize internally or use env vars.
clerk = Clerk(bearer_auth=settings.CLERK_SECRET_KEY)


# --- create_access_token remains the same ---
def create_access_token(
    subject: str | Any,
    role: str,
    expires_delta: timedelta | None = None,
    additional_claims: dict[str, Any] | None = None,
) -> str:
    """
    Generates a JWT access token.

    Args:
        subject: The subject of the token (e.g., user ID).
        role: The role associated with the subject (e.g., 'patient', 'clinician').
        expires_delta: Optional timedelta for token expiry. Defaults to ACCESS_TOKEN_EXPIRE_MINUTES from settings.
        additional_claims: Optional dictionary of additional claims to include in the payload.

    Returns:
        The encoded JWT access token string.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode: dict[str, Any] = {
        "exp": expire,
        "sub": str(subject),  # Ensure subject is a string
        "iat": datetime.now(timezone.utc),
        "role": role,
    }

    if additional_claims:
        to_encode.update(additional_claims)

    encoded_jwt = jwt.encode(
        to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    logger.debug(f"Generated JWT for sub: {subject}, role: {role}, expires: {expire}")
    return encoded_jwt


async def verify_access_token(
    token: str, request: Request, credentials_exception: HTTPException
) -> TokenPayload:
    """
    Verifies the access token. Prioritizes Clerk verification for RS256 tokens,
    falls back to local JWT verification for HS256 tokens.

    Args:
        token: The JWT token string.
        request: The incoming FastAPI Request object (needed for authenticate_request).
        credentials_exception: The HTTPException to raise if verification fails.

    Returns:
        The validated token payload as a TokenPayload object.

    Raises:
        credentials_exception: If the token is invalid, expired, or cannot be decoded.
    """
    try:
        # Decode header to check algorithm without full verification yet
        header_b64 = token.split(".")[0]
        padding = len(header_b64) % 4
        if padding:
            header_b64 += "=" * (4 - padding)
        header_json = base64.b64decode(
            header_b64.replace("-", "+").replace("_", "/")
        ).decode("utf-8")
        header = json.loads(header_json)
        alg = header.get("alg")

        logger.debug(f"Detected token algorithm: {alg}")

        if alg == "RS256":
            # --- Log raw payload before Clerk validation ---
            try:
                # Decode without verification for logging only
                unverified_payload = jwt.decode(
                    token, options={"verify_signature": False}, algorithms=["RS256"]
                )
                logger.debug(
                    f"DEBUG: Raw RS256 token payload (unverified): {unverified_payload}"
                )
                raw_public_metadata = unverified_payload.get("public_metadata")
                logger.debug(
                    f"DEBUG: Raw public_metadata from token: type={type(raw_public_metadata)}, value={raw_public_metadata}"
                )
            except Exception as log_err:
                logger.warning(
                    f"DEBUG: Could not decode raw token payload for logging: {log_err}"
                )
            # ---------------------------------------------
            # Assume Clerk token, verify using authenticate_request
            logger.debug(
                "Attempting Clerk token verification using authenticate_request"
            )
            clerk_payload_dict = await verify_clerk_token_with_auth_request(
                token, request, credentials_exception
            )
            # Map Clerk payload to our TokenPayload schema
            # Ensure required fields (sub, role, exp, iat) are present
            if not all(k in clerk_payload_dict for k in ("sub", "role", "exp", "iat")):
                logger.error(
                    f"Mapped Clerk payload is missing required fields: {clerk_payload_dict}"
                )
                raise credentials_exception
            return TokenPayload(**clerk_payload_dict)

        elif alg == "HS256":
            # Internal token, verify using local secret
            logger.debug("Attempting local HS256 JWT verification")
            return verify_jwt_token(token, credentials_exception)
        else:
            logger.error(f"Unsupported token algorithm: {alg}")
            raise credentials_exception

    except (
        IndexError,
        ValueError,
        TypeError,
        json.JSONDecodeError,
        base64.binascii.Error,
    ) as decode_err:
        logger.error(f"Error decoding token header: {decode_err}")
        raise credentials_exception
    except HTTPException as http_exc:
        # Re-raise HTTPExceptions from verification functions
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors during verification
        logger.error(f"Unexpected error during token verification: {e}", exc_info=True)
        raise credentials_exception


async def verify_clerk_token_with_auth_request(
    token: str, request: Request, credentials_exception: HTTPException
) -> dict[str, Any]:
    """
    Verifies a Clerk JWT token using the clerk_backend_api SDK's authenticate_request method.

    Args:
        token: The JWT token string from the Authorization header.
        request: The incoming FastAPI Request object.
        credentials_exception: The HTTPException to raise on failure.

    Returns:
        A dictionary representing the standardized payload for TokenPayload.
    """
    try:
        # Use authenticate_request - it needs the full request object
        # It implicitly extracts the token from the header.
        # We might need to configure authorized_parties if applicable.
        # Build authorized_parties list from settings
        authorized_parties = []
        if settings.FRONTEND_URL_CLINICIAN:
            authorized_parties.append(settings.FRONTEND_URL_CLINICIAN)
        if settings.FRONTEND_URL_PATIENT:
            authorized_parties.append(settings.FRONTEND_URL_PATIENT)
        if settings.FRONTEND_URL_ADMIN:
            authorized_parties.append(settings.FRONTEND_URL_ADMIN)

        options = AuthenticateRequestOptions(authorized_parties=authorized_parties)

        logger.debug(
            f"Attempting authenticate_request with authorized_parties: {authorized_parties}"
        )
        request_state: RequestState = None  # Initialize
        try:
            # Explicitly log before the call
            logger.debug("DEBUG: Calling clerk.authenticate_request...")
            request_state = clerk.authenticate_request(request, options)
            # Explicitly log after the call succeeds
            logger.debug("DEBUG: clerk.authenticate_request call successful.")
        except Exception as auth_req_err:
            # Log any exception originating *directly* from the SDK call
            logger.error(
                f"CRITICAL: Exception directly from clerk.authenticate_request: {auth_req_err}",
                exc_info=True,
            )
            # Re-raise immediately to ensure it's handled (likely as 401 by outer try/except)
            raise credentials_exception

        # --- Log the entire request_state and its payload ---
        try:
            # Convert potentially complex object to string safely for logging
            request_state_str = str(request_state)
            logger.debug(f"DEBUG: Full request_state object: {request_state_str}")
        except Exception as e:
            logger.warning(
                f"DEBUG: Could not serialize full request_state for logging: {e}"
            )

        clerk_payload = request_state.payload  # Get payload as before
        logger.debug(f"DEBUG: request_state.payload dictionary: {clerk_payload}")
        # ----------------------------------------------------

        logger.debug(
            f"Clerk authenticate_request state: status={request_state.status}, reason={request_state.reason}"
        )

        # FIX: We see from the logs that we're getting a successful authentication
        # but the code is still failing because we're misinterpreting the check
        # Let's invert the condition to only proceed if the user IS signed in
        # Check if signed in
        logger.debug("Checking if request_state.is_signed_in...")
        if not request_state.is_signed_in:
            logger.warning(
                f"Cannot verify session state for request_state.valid == false. "
                f"Reason: {request_state.reason}. Request State: {request_state}"
            )
            raise credentials_exception
        logger.debug("request_state.is_signed_in check PASSED.")

        # Extract payload details
        clerk_payload = request_state.payload
        # Check if payload exists
        logger.debug("Checking if clerk_payload exists...")
        if not clerk_payload:
            # Log the request state if payload is missing
            logger.error(
                f"Clerk authentication succeeded but payload is missing. Request State: {request_state}"
            )
            raise credentials_exception

        # --- Map Clerk payload to our standardized TokenPayload format ---
        user_id = clerk_payload.get("sub")
        # Check for 'sub'
        logger.debug("Checking for 'sub' in clerk_payload...")
        if not user_id:
            # Log the payload if 'sub' is missing
            logger.error(
                f"Clerk token payload missing 'sub' (user ID). Payload: {clerk_payload}"
            )
            raise credentials_exception
        logger.debug("'sub' check PASSED.")

        # --- Template Validation Removed ---
        # The previous check for a 'template' claim with value 'CodenamePulsetrack' was removed.
        # Reason: The Clerk JWT template configuration for 'CodenamePulsetrack' (verified 2025-04-07)
        # adds 'email' and 'public_metadata', but does NOT add its own name ('CodenamePulsetrack')
        # as a 'template' claim (or any other claim) to the JWT payload.
        # The check `clerk_payload.get("template")` was therefore always failing.
        # The presence of 'public_metadata' (added by the correct template) is now implicitly relied upon.
        # ---------------------------------

        # Determine role - Check custom claims (public_metadata) or organization roles
        # This part is highly dependent on how roles are configured in Clerk
        # Determine role - Check custom claims (public_metadata)
        # Try accessing custom claims potentially stored in request_state.claims
        # Note: We still need clerk_payload for standard claims like 'sub', 'exp', 'iat'
        claims_payload = getattr(
            request_state, "claims", clerk_payload
        )  # Fallback to payload if 'claims' doesn't exist
        public_metadata = claims_payload.get("public_metadata")
        role = None
        parsed_metadata = None  # Initialize variable to hold the parsed metadata dict

        # Handle public_metadata as either a dictionary or a string
        if isinstance(public_metadata, dict):
            # If it's already a dictionary, extract role directly
            role = public_metadata.get("role")
            parsed_metadata = public_metadata  # Assign the dict directly
            logger.debug(f"Extracted metadata: role={role}")
        elif isinstance(public_metadata, str) and public_metadata.strip():
            # If it's a string, try to parse it as JSON
            try:
                public_metadata_obj = json.loads(public_metadata)
                if isinstance(public_metadata_obj, dict):
                    role = public_metadata_obj.get("role")
                    parsed_metadata = public_metadata_obj  # Assign the parsed dict
                    logger.debug(
                        f"Extracted role '{role}' from parsed public_metadata JSON for user {user_id}"
                    )
                else:
                    logger.warning(
                        f"Parsed public_metadata for user {user_id} is not a dictionary: {public_metadata}"
                    )
            except json.JSONDecodeError:
                logger.error(
                    f"Failed to parse public_metadata JSON string for user {user_id}: {public_metadata}"
                )
        elif public_metadata:
            logger.warning(
                f"public_metadata claim for user {user_id} has unsupported type: {type(public_metadata)}"
            )

        # --- Add detailed logging before validation ---
        logger.debug(f"DEBUG: Role validation for user {user_id}: role={role}")
        # --------------------------------------------

        # Validate the extracted role - MUST be present
        # Check if role was extracted
        logger.debug(
            f"Checking extracted role for user {user_id} (expecting non-empty)... Role: {role}"
        )
        if not role:
            logger.warning(
                f"Failed to determine user role for {user_id} - user metadata was invalid/missing. "
                f"Metadata: {parsed_metadata}, Payload: {clerk_payload}"
            )
            raise credentials_exception  # Fail if role is missing or couldn't be parsed
        logger.debug("Role presence check PASSED.")
        # Validate the extracted role(s) - MUST be present and valid
        if isinstance(role, list):
            # Check if all roles in the list are valid
            # Validate roles in list
            logger.debug(f"Validating roles in list: {role}")
            if not all(r in {"patient", "clinician", "admin"} for r in role):
                logger.error(
                    f"Clerk token for user {user_id} contains invalid role(s) {role} in public_metadata."
                )
                raise credentials_exception
        elif isinstance(role, str):
            # Original check for single string role
            # Validate single string role
            logger.debug(f"Validating single role string: {role}")
            if role not in {"patient", "clinician", "admin"}:
                logger.error(
                    f"Clerk token for user {user_id} has invalid role '{role}' in public_metadata."
                )
                raise credentials_exception
            logger.debug("Role value validation PASSED.")
        else:
            # Handle unexpected type if necessary, though the check on line 235 should catch missing roles
            logger.error(
                f"Clerk token for user {user_id} has unexpected type for role: {type(role)}"
            )
            raise credentials_exception

        # Extract and VALIDATE email - MUST be present for patient creation
        # Check for email
        logger.debug("Checking for 'email' in clerk_payload...")
        email = clerk_payload.get("email")
        if not email:
            # Log payload if email is missing
            logger.error(
                f"Clerk token for user {user_id} missing required 'email' claim. Payload: {clerk_payload}"
            )
            raise credentials_exception  # Fail if email is missing
        logger.debug("'email' check PASSED.")

        # Extract expiry and issued_at times
        exp = clerk_payload.get("exp")
        iat = clerk_payload.get("iat")
        # Check for exp and iat
        logger.debug("Checking for 'exp' and 'iat' in clerk_payload...")
        if not exp or not iat:
            # Log payload if exp/iat are missing
            logger.error(
                f"Clerk token payload missing 'exp' or 'iat'. Payload: {clerk_payload}"
            )
            raise credentials_exception
        logger.debug("'exp' and 'iat' check PASSED.")

        logger.info(
            f"Clerk token verified successfully for user: {user_id}, role: {role}"
        )

        # Return a dictionary compatible with TokenPayload schema
        return {
            "sub": user_id,
            "role": role,
            "email": email,
            "exp": exp,
            "iat": iat,
            "public_metadata": parsed_metadata,  # Include the parsed metadata
            # Add any other relevant claims needed downstream
            "raw_claims": clerk_payload,  # Optionally include raw claims if needed elsewhere
        }

    except clerk_models.SDKError as e:
        # Catch specific Clerk SDK errors if possible, otherwise general exception
        logger.error(f"Clerk SDK error during authenticate_request: {e}", exc_info=True)
        raise credentials_exception
    except Exception as e:
        logger.error(
            f"Unexpected error during Clerk authenticate_request: {e}", exc_info=True
        )
        # Map unexpected errors to the standard credentials exception
        raise credentials_exception


# --- verify_jwt_token remains the same for internal HS256 tokens ---
def verify_jwt_token(token: str, credentials_exception: HTTPException) -> TokenPayload:
    """
    Verifies a JWT token using the local JWT_SECRET_KEY (for internal HS256 tokens).
    """
    try:
        logger.debug("Attempting local JWT verification...")

        # Decode and verify the token
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM],  # Should be HS256 from settings
        )

        # Extract the subject (user ID)
        subject = payload.get("sub")
        if subject is None:
            logger.error("Token missing 'sub' claim")
            raise credentials_exception

        # Validate the payload
        token_data = TokenPayload(**payload)
        logger.info(
            f"Local HS256 token payload validated successfully for role: {token_data.role}, subject: {token_data.sub}"
        )
        return token_data

    except jwt.ExpiredSignatureError:
        logger.error("Local HS256 token has expired")
        raise credentials_exception
    except jwt.JWTError as e:
        logger.error(f"Local HS256 JWT verification failed: {e}")
        raise credentials_exception
    except ValidationError as ve:
        logger.error(f"Local HS256 TokenPayload validation failed: {ve}")
        raise credentials_exception


# --- generate_secure_magic_code remains the same ---
# import secrets # Moved to top
# import string # Moved to top


def generate_secure_magic_code(length: int = 10) -> str:
    """
    Generates a cryptographically secure random string for use as a magic code.
    """
    alphabet = string.ascii_letters + string.digits
    code = "".join(secrets.choice(alphabet) for _ in range(length))
    return code


# --- New functions for access code hashing ---


def hash_access_code(plaintext_code: str) -> str:
    """
    Hashes an access code using bcrypt with an appropriate work factor.

    Args:
        plaintext_code: The plaintext access code to hash

    Returns:
        The bcrypt hash as a string (UTF-8 encoded)
    """
    # Use a work factor of 12 which is generally considered secure
    # while still having reasonable performance for short-lived codes
    salt = bcrypt.gensalt(rounds=12)

    # Encode the plaintext code as bytes, then hash
    hashed = bcrypt.hashpw(plaintext_code.encode("utf-8"), salt)

    # Return the hash as a string
    return hashed.decode("utf-8")


def verify_access_code(plaintext_code: str, hashed_code: str) -> bool:
    """
    Verifies if a plaintext access code matches a stored hash.

    Args:
        plaintext_code: The plaintext code to verify
        hashed_code: The stored hash to check against

    Returns:
        True if the access code matches the hash, False otherwise
    """
    try:
        # Encode both strings to bytes
        plaintext_bytes = plaintext_code.encode("utf-8")
        hashed_bytes = hashed_code.encode("utf-8")

        # Use checkpw to verify
        return bcrypt.checkpw(plaintext_bytes, hashed_bytes)
    except Exception as e:
        logger.error(f"Error verifying access code: {e}")
        # User Role Management Strategy:
        # User roles (e.g., Patient, Clinician, Clinic Admin, System Admin) are primarily managed
        # through the external authentication provider (Clerk).
        # Custom roles defined in Clerk are inspected from the validated JWT payload
        # within API dependency functions (e.g., deps.get_current_clinician) to authorize actions.
        # The backend database models (Patient, Clinician) store user-specific data but do not
        # typically store the role directly, relying on the JWT as the source of truth for roles.

        return False
