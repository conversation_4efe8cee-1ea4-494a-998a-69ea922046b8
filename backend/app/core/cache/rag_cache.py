import hashlib
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional

from app.core.cache.redis_manager import redis_manager

# from app.schemas.content_chunk import ContentChunk # This import is unused and causes an error

logger = logging.getLogger(__name__)


@dataclass
class CachedRAGResult:
    """Cached RAG search result."""

    chunks: list[dict[str, Any]]
    scraped_pages: list[dict[str, Any]]
    query_embedding: list[float]
    similarity_scores: list[float]
    clinic_id: str
    cached_at: datetime
    ttl: int


class RAGCache:
    """Cache service for RAG embeddings and search results."""

    def __init__(
        self,
        cache_manager=None,
        embedding_ttl: int = 86400,  # 24 hours for embeddings
        rag_result_ttl: int = 3600,  # 1 hour for RAG results
        text_embedding_ttl: int = 7200,  # 2 hours for text embeddings
    ):
        """
        Initialize RAG cache service.

        Args:
            cache_manager: Redis manager instance (defaults to global instance)
            embedding_ttl: TTL for model embeddings (24 hours)
            rag_result_ttl: TTL for RAG search results (1 hour)
            text_embedding_ttl: TTL for text embeddings (2 hours)
        """
        self.cache = cache_manager or redis_manager
        self.embedding_ttl = embedding_ttl
        self.rag_result_ttl = rag_result_ttl
        self.text_embedding_ttl = text_embedding_ttl

    def _generate_embedding_key(self, text: str) -> str:
        """Generate cache key for text embedding."""
        text_hash = hashlib.sha256(text.encode()).hexdigest()
        return f"embedding:{text_hash}"

    def _generate_rag_key(self, query: str, clinic_id: str, user_role: str) -> str:
        """Generate cache key for RAG results."""
        combined = f"{query}:{clinic_id}:{user_role}"
        query_hash = hashlib.sha256(combined.encode()).hexdigest()
        return f"rag:result:{query_hash}"

    def _generate_chunk_key(self, chunk_id: str) -> str:
        """Generate cache key for content chunk."""
        return f"chunk:{chunk_id}"

    def get_text_embedding(self, text: str) -> Optional[list[float]]:
        """Get cached embedding for text."""
        try:
            key = self._generate_embedding_key(text)
            embedding = self.cache.get(key)
            if embedding:
                logger.debug(f"Cache hit for text embedding: {text[:50]}...")
                return embedding
            return None
        except Exception as e:
            logger.error(f"Error retrieving text embedding from cache: {e}")
            return None

    def set_text_embedding(self, text: str, embedding: list[float]) -> bool:
        """Cache embedding for text."""
        try:
            key = self._generate_embedding_key(text)
            return self.cache.set(key, embedding, ttl=self.text_embedding_ttl)
        except Exception as e:
            logger.error(f"Error caching text embedding: {e}")
            return False

    def get_text_embeddings_batch(self, texts: list[str]) -> dict[str, list[float]]:
        """Get multiple text embeddings from cache."""
        try:
            keys = [self._generate_embedding_key(text) for text in texts]
            cached_embeddings = self.cache.get_many(keys)

            result = {}
            for text, key in zip(texts, keys):
                key_without_prefix = key.replace(self.cache.key_prefix, "")
                if key_without_prefix in cached_embeddings:
                    result[text] = cached_embeddings[key_without_prefix]

            if result:
                logger.debug(
                    f"Cache hit for {len(result)}/{len(texts)} text embeddings"
                )

            return result
        except Exception as e:
            logger.error(f"Error retrieving batch text embeddings from cache: {e}")
            return {}

    def set_text_embeddings_batch(
        self, text_embeddings: dict[str, list[float]]
    ) -> bool:
        """Cache multiple text embeddings."""
        try:
            mapping = {}
            for text, embedding in text_embeddings.items():
                key = self._generate_embedding_key(text)
                mapping[key] = embedding

            return self.cache.set_many(mapping, ttl=self.text_embedding_ttl)
        except Exception as e:
            logger.error(f"Error caching batch text embeddings: {e}")
            return False

    def get_rag_result(
        self, query: str, clinic_id: str, user_role: str
    ) -> Optional[CachedRAGResult]:
        """Get cached RAG search result."""
        try:
            key = self._generate_rag_key(query, clinic_id, user_role)
            result = self.cache.get(key)

            if result:
                logger.debug(f"Cache hit for RAG result: {query[:50]}...")
                # Convert dict back to CachedRAGResult
                result["cached_at"] = datetime.fromisoformat(result["cached_at"])
                return CachedRAGResult(**result)

            return None
        except Exception as e:
            logger.error(f"Error retrieving RAG result from cache: {e}")
            return None

    def set_rag_result(
        self,
        query: str,
        clinic_id: str,
        user_role: str,
        chunks: list[dict[str, Any]],
        scraped_pages: list[dict[str, Any]],
        query_embedding: list[float],
        similarity_scores: Optional[list[float]] = None,
    ) -> bool:
        """Cache RAG search result."""
        try:
            key = self._generate_rag_key(query, clinic_id, user_role)
            result = CachedRAGResult(
                chunks=chunks,
                scraped_pages=scraped_pages,
                query_embedding=query_embedding,
                similarity_scores=similarity_scores or [],
                clinic_id=clinic_id,
                cached_at=datetime.now(),
                ttl=self.rag_result_ttl,
            )

            # Convert to dict for serialization
            result_dict = {
                "chunks": result.chunks,
                "scraped_pages": result.scraped_pages,
                "query_embedding": result.query_embedding,
                "similarity_scores": result.similarity_scores,
                "clinic_id": result.clinic_id,
                "cached_at": result.cached_at.isoformat(),
                "ttl": result.ttl,
            }

            return self.cache.set(key, result_dict, ttl=self.rag_result_ttl)
        except Exception as e:
            logger.error(f"Error caching RAG result: {e}")
            return False

    def get_chunk(self, chunk_id: str) -> Optional[dict[str, Any]]:
        """Get cached content chunk."""
        try:
            key = self._generate_chunk_key(chunk_id)
            return self.cache.get(key)
        except Exception as e:
            logger.error(f"Error retrieving chunk from cache: {e}")
            return None

    def set_chunk(self, chunk_id: str, chunk_data: dict[str, Any]) -> bool:
        """Cache content chunk."""
        try:
            key = self._generate_chunk_key(chunk_id)
            return self.cache.set(key, chunk_data, ttl=self.embedding_ttl)
        except Exception as e:
            logger.error(f"Error caching chunk: {e}")
            return False

    def invalidate_clinic_cache(self, clinic_id: str) -> int:
        """Invalidate all cached data for a clinic."""
        try:
            # Clear RAG results for this clinic
            pattern = f"rag:result:*{clinic_id}*"
            deleted_count = self.cache.clear_pattern(pattern)

            logger.info(
                f"Invalidated {deleted_count} cached items for clinic {clinic_id}"
            )
            return deleted_count
        except Exception as e:
            logger.error(f"Error invalidating clinic cache: {e}")
            return 0

    def warm_embedding_cache(
        self, texts: list[str], embeddings: list[list[float]]
    ) -> bool:
        """Pre-populate embedding cache (useful for warmup)."""
        try:
            if len(texts) != len(embeddings):
                logger.error("Mismatch between texts and embeddings count")
                return False

            text_embeddings = dict(zip(texts, embeddings))
            return self.set_text_embeddings_batch(text_embeddings)
        except Exception as e:
            logger.error(f"Error warming embedding cache: {e}")
            return False

    def get_cache_stats(self) -> dict[str, Any]:
        """Get cache statistics."""
        try:
            # Count different types of cached items

            # This is a simplified version - in production you might want
            # to use SCAN with COUNT to handle large datasets
            stats = {
                "connected": self.cache.is_connected(),
                "embedding_ttl": self.embedding_ttl,
                "rag_result_ttl": self.rag_result_ttl,
                "text_embedding_ttl": self.text_embedding_ttl,
            }

            return stats
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}


# Create a global RAG cache instance
rag_cache = RAGCache()
