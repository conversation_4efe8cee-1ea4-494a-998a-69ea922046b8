import json
import logging
from typing import Any, Optional
from urllib.parse import urlparse

import redis
from redis.exceptions import RedisError

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis cache manager for the RAG system."""

    def __init__(
        self,
        host: str = None,
        port: int = None,
        db: int = 0,
        password: Optional[str] = None,
        decode_responses: bool = True,
        key_prefix: str = "pulsetrack:rag:",
        default_ttl: int = 3600,
    ):
        """
        Initialize Redis connection.

        Args:
            host: Redis host (defaults to parsed from REDIS_URL or localhost)
            port: Redis port (defaults to parsed from REDIS_URL or 6379)
            db: Redis database number
            password: Redis password (optional)
            decode_responses: Whether to decode responses as strings
            key_prefix: Prefix for all cache keys
            default_ttl: Default TTL in seconds (1 hour)
        """
        # Try to parse REDIS_URL first
        redis_url = getattr(settings, "REDIS_URL", None)
        if redis_url and not host:
            try:
                parsed = urlparse(redis_url)
                self.host = parsed.hostname or "localhost"
                self.port = parsed.port or 6379
                self.password = parsed.password
                # Extract db from path if present (e.g., redis://localhost:6379/0)
                if parsed.path and len(parsed.path) > 1:
                    try:
                        self.db = int(parsed.path[1:])
                    except ValueError:
                        self.db = db
                else:
                    self.db = db
            except Exception as e:
                logger.warning(f"Failed to parse REDIS_URL: {e}, falling back to defaults")
                self.host = "localhost"
                self.port = 6379
                self.password = None
                self.db = db
        else:
            # Fallback to individual settings or defaults
            self.host = host or getattr(settings, "REDIS_HOST", "redis")  # Use "redis" as default for Docker
            self.port = port or getattr(settings, "REDIS_PORT", 6379)
            self.db = db
            self.password = password or getattr(settings, "REDIS_PASSWORD", None)
            
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl

        self._redis_client = None
        self._connect()

    def _connect(self):
        """Establish Redis connection with retry logic."""
        max_retries = 3
        retry_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                self._redis_client = redis.Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                )
                # Test connection
                self._redis_client.ping()
                logger.info(f"Successfully connected to Redis at {self.host}:{self.port}")
                return
            except (RedisError, ConnectionError) as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"Failed to connect to Redis (attempt {attempt + 1}/{max_retries}): {e}. "
                        f"Retrying in {retry_delay} seconds..."
                    )
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(
                        f"Failed to connect to Redis after {max_retries} attempts: {e}. "
                        "RAG caching will be disabled."
                    )
                    self._redis_client = None

    def _make_key(self, key: str) -> str:
        """Create a prefixed key."""
        return f"{self.key_prefix}{key}"

    def _serialize(self, value: Any) -> str:
        """Serialize value for storage."""
        try:
            return json.dumps(value)
        except TypeError:
            logger.warning(f"Failed to serialize value of type {type(value)}")
            return str(value)

    def _deserialize(self, value: str) -> Any:
        """Deserialize value from storage."""
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value

    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self._redis_client:
            return None

        try:
            full_key = self._make_key(key)
            value = self._redis_client.get(full_key)
            if value is None:
                return None
            return self._deserialize(value)
        except RedisError as e:
            logger.error(f"Redis get error for key {key}: {e}")
            return None

    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False,
    ) -> bool:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (defaults to self.default_ttl)
            nx: Only set if key doesn't exist
            xx: Only set if key exists

        Returns:
            True if successful, False otherwise
        """
        if not self._redis_client:
            return False

        try:
            full_key = self._make_key(key)
            serialized_value = self._serialize(value)
            ttl = ttl or self.default_ttl

            return self._redis_client.set(
                full_key, serialized_value, ex=ttl, nx=nx, xx=xx
            )
        except RedisError as e:
            logger.error(f"Redis set error for key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self._redis_client:
            return False

        try:
            full_key = self._make_key(key)
            return bool(self._redis_client.delete(full_key))
        except RedisError as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            return False

    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if not self._redis_client:
            return False

        try:
            full_key = self._make_key(key)
            return bool(self._redis_client.exists(full_key))
        except RedisError as e:
            logger.error(f"Redis exists error for key {key}: {e}")
            return False

    def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for a key."""
        if not self._redis_client:
            return False

        try:
            full_key = self._make_key(key)
            return bool(self._redis_client.expire(full_key, ttl))
        except RedisError as e:
            logger.error(f"Redis expire error for key {key}: {e}")
            return False

    def get_many(self, keys: list[str]) -> dict[str, Any]:
        """Get multiple values from cache."""
        if not self._redis_client:
            return {}

        try:
            full_keys = [self._make_key(key) for key in keys]
            values = self._redis_client.mget(full_keys)
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    result[key] = self._deserialize(value)
            return result
        except RedisError as e:
            logger.error(f"Redis mget error: {e}")
            return {}

    def set_many(self, mapping: dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values in cache."""
        if not self._redis_client:
            return False

        try:
            ttl = ttl or self.default_ttl
            pipeline = self._redis_client.pipeline()

            for key, value in mapping.items():
                full_key = self._make_key(key)
                serialized_value = self._serialize(value)
                pipeline.setex(full_key, ttl, serialized_value)

            pipeline.execute()
            return True
        except RedisError as e:
            logger.error(f"Redis mset error: {e}")
            return False

    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        if not self._redis_client:
            return 0

        try:
            full_pattern = self._make_key(pattern)
            cursor = "0"
            deleted_count = 0

            while cursor != 0:
                cursor, keys = self._redis_client.scan(
                    cursor=cursor, match=full_pattern, count=100
                )
                if keys:
                    deleted_count += self._redis_client.delete(*keys)

            return deleted_count
        except RedisError as e:
            logger.error(f"Redis clear pattern error: {e}")
            return 0

    def is_connected(self) -> bool:
        """Check if Redis is connected and responsive."""
        if not self._redis_client:
            return False

        try:
            return self._redis_client.ping()
        except RedisError:
            return False


# Create a global Redis manager instance
redis_manager = RedisManager()
