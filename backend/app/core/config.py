import os
from typing import Optional

from dotenv import load_dotenv
from pydantic import ConfigDict, Field, field_validator
from pydantic_settings import BaseSettings

# Determine the path to the .env file relative to this config file
# backend/app/core/config.py -> backend/.env
env_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".env"
)
load_dotenv(dotenv_path=env_path)


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    """

    # Database
    DATABASE_URL: str

    DB_POOL_SIZE: int = 5  # Default pool size
    DB_MAX_OVERFLOW: int = 10  # Default max overflow

    # JWT Settings
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60  # Default to 1 hour

    # Azure Blob Storage Settings
    AZURE_STORAGE_CONNECTION_STRING: str
    AZURE_STORAGE_CONTAINER_NAME: str

    # Logging
    LOG_LEVEL: str = "INFO"  # Default log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

    # Rate Limiting
    DEFAULT_RATE_LIMIT: str = "100/minute"  # Default rate limit for general endpoints

    # Clerk Authentication
    CLERK_SECRET_KEY: str = ""
    CLERK_PUBLISHABLE_KEY: str = ""

    ALLOWED_CLINICIAN_DOMAINS: Optional[list[str]] = (
        None  # Optional list of allowed email domains for clinicians
    )

    # Frontend URLs (for CORS, Clerk authorized_parties, etc.)
    FRONTEND_URL_CLINICIAN: Optional[str] = None
    FRONTEND_URL_PATIENT: Optional[str] = None
    FRONTEND_URL_ADMIN: Optional[str] = None  # Added for Admin Frontend

    # Redis
    REDIS_URL: Optional[str] = None  # Added for Redis connection

    llm_patient_side_effects_enabled: bool = Field(
        False, env="LLM_PATIENT_SIDE_EFFECTS_ENABLED"
    )

    # Pydantic V2 configuration using model_config
    # Since load_dotenv is used manually, we don't need env_file here.

    @field_validator("ALLOWED_CLINICIAN_DOMAINS", mode="before")
    @classmethod
    def validate_allowed_domains(cls, value):
        if isinstance(value, str) and value.strip() == "":
            return None  # Treat empty string env var as None
        # Pydantic will handle parsing comma-separated strings or JSON lists automatically
        return value

    model_config = ConfigDict()


# Create a single instance of the settings to be imported elsewhere
settings = Settings()

# You can optionally add validation or computed properties here if needed
# Example:
# from pydantic import validator
# @validator("DATABASE_URL", pre=True)
# def check_db_url(cls, v):
#     if not v:
#         raise ValueError("DATABASE_URL must be set")
#     return v
