# backend/app/core/constants/appointment.py

# Regular expression for validating Clerk IDs
CLERK_ID_REGEX = r"^user_[a-zA-Z0-9]{28}$"

# Allowed appointment durations in minutes
ALLOWED_APPOINTMENT_DURATIONS: list[int] = [15, 30, 45, 60]

# Maximum number of future appointments a patient can have
MAX_FUTURE_APPOINTMENTS: int = 3

# Minimum notice required for cancellation (in hours)
MIN_CANCELLATION_NOTICE_HOURS: int = 24

# Maximum days in advance that appointments can be booked
MAX_ADVANCE_BOOKING_DAYS: int = 90

# Allowed appointment types
ALLOWED_APPOINTMENT_TYPES: list[str] = [
    "Initial",
    "Follow-up",
    "Procedure",
    "Consultation",
]

# Allowed appointment statuses
ALLOWED_APPOINTMENT_STATUSES: list[str] = ["scheduled", "cancelled", "completed"]
VALID_APPOINTMENT_TYPES: list[str] = [
    "Initial",
    "Follow-up",
    "Procedure",
    "Consultation",
]
VALID_STATUS_TRANSITIONS: dict[str, set[str]] = {
    "scheduled": {"cancelled", "completed"},
    "cancelled": set(),
    "completed": set(),
}
