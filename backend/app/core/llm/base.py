import logging
from abc import ABC, abstractmethod
from typing import Any, Generic, Optional, TypeVar

from pydantic import BaseModel, ConfigDict

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


class LLMConfig(BaseModel):
    """Base configuration for LLM providers"""

    model: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None

    model_config = ConfigDict(extra="allow", arbitrary_types_allowed=True)


class LLMResponse(BaseModel):
    """Base response from LLM providers"""

    content: str
    model: str
    usage: dict[str, Any] = {}

    model_config = ConfigDict(extra="allow", arbitrary_types_allowed=True)


class PromptTemplate(BaseModel):
    """Base class for prompt templates"""

    template: str
    input_variables: list[str]
    use_template: bool = False

    model_config = ConfigDict(extra="allow", arbitrary_types_allowed=True)

    def format(self, **kwargs) -> str:
        """Format the template with the given variables"""
        # Log missing variables for debugging
        missing_vars = [var for var in self.input_variables if var not in kwargs]
        if missing_vars:
            logger.warning(f"Missing template variables: {missing_vars}")
            # Add placeholders for missing variables to prevent errors
            for var in missing_vars:
                kwargs[var] = f"[missing:{var}]"

        # Process template
        try:
            if self.use_template:
                from string import Template

                return Template(self.template).safe_substitute(**kwargs)
            else:
                # Manual variable replacement to avoid format() errors
                result = self.template
                for var_name, var_value in kwargs.items():
                    placeholder = "{" + var_name + "}"
                    if not isinstance(var_value, str):
                        var_value = str(var_value)
                    result = result.replace(placeholder, var_value)
                return result
        except Exception as e:
            logger.error(f"Template formatting error: {str(e)}")
            # Return with placeholders if formatting fails
            return f"ERROR_FORMATTING_TEMPLATE: {self.template}"


class LLMProvider(ABC, Generic[T]):
    """Base class for LLM providers"""

    def __init__(self, config: LLMConfig):
        self.config = config

    @property
    @abstractmethod
    def model_name(self) -> str:
        """Get the model name for token management"""
        pass

    @abstractmethod
    async def generate(self, prompt: str) -> LLMResponse:
        """Generate a response from the LLM"""
        pass

    @abstractmethod
    async def generate_structured(self, prompt: str, output_schema: type[T]) -> T:
        """Generate a structured response from the LLM"""
        pass

    @abstractmethod
    async def generate_with_template(
        self, template: PromptTemplate, variables: dict[str, Any]
    ) -> LLMResponse:
        """Generate a response using a prompt template"""
        pass

    @abstractmethod
    async def generate_structured_with_template(
        self,
        template: PromptTemplate,
        variables: dict[str, Any],
        output_schema: type[T],
    ) -> T:
        """Generate a structured response using a prompt template"""
        pass

    @abstractmethod
    async def close(self) -> None:
        """Close any resources used by the provider"""
        pass


class LLMException(Exception):
    """Base exception for LLM-related errors"""

    pass


class ProviderNotFoundError(LLMException):
    """Raised when a requested provider is not found"""

    pass


class PromptTemplateError(LLMException):
    """Raised when there's an error with prompt templates"""

    pass


class ResponseValidationError(LLMException):
    """Raised when response validation fails"""

    pass
