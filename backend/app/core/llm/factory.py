from typing import Any

# Ensure relative imports work correctly
try:
    # Attempt to import settings if needed, otherwise remove if unused
    # from app.core.config import settings
    from app.core.llm.base import LLMConfig, LLMProvider, ProviderNotFoundError
    from app.core.llm.providers.openai import OpenAIConfig, OpenAIProvider
except ImportError as e:
    import logging

    logging.error(f"Import error in factory.py: {e}. Check paths/dependencies.")
    raise


class LLMFactory:
    """Factory for creating LLM providers"""

    # Use Type[LLMProvider[Any]] for broader compatibility if needed,
    # but Type[LLMProvider] might suffice if generics aren't critical here.
    _providers: dict[str, type[LLMProvider]] = {
        "openai": OpenAIProvider,
        # Add other providers here as they are implemented
        # "anthropic": AnthropicProvider,
    }

    _configs: dict[str, type[LLMConfig]] = {
        "openai": OpenAIConfig,
        # "anthropic": AnthropicConfig,
    }

    @classmethod
    def register_provider(
        cls, name: str, provider: type[LLMProvider], config: type[LLMConfig]
    ) -> None:
        """Register a new LLM provider"""
        if name in cls._providers:
            # Optionally log a warning or raise an error if overwriting
            pass
        cls._providers[name] = provider
        cls._configs[name] = config

    @classmethod
    def create_provider(cls, name: str, **config_kwargs: Any) -> LLMProvider:
        """
        Create a new LLM provider instance.

        Args:
            name (str): The name of the provider to create (e.g., 'openai').
            **config_kwargs: Keyword arguments to pass to the provider's config class.
                             Common args include 'model', 'temperature', 'max_tokens', 'api_key'.

        Returns:
            LLMProvider: An instance of the requested LLM provider.

        Raises:
            ProviderNotFoundError: If the provider name is not registered or initialization fails.
        """
        if name not in cls._providers:
            raise ProviderNotFoundError(
                f"Provider '{name}' not found. Available: {list(cls._providers.keys())}"
            )

        provider_cls = cls._providers[name]
        config_cls = cls._configs[name]

        try:
            # Create config instance using the provided kwargs
            # Pydantic models handle validation and default values
            config_instance = config_cls(**config_kwargs)

            # Instantiate the provider with the config instance
            provider_instance = provider_cls(config_instance)
            return provider_instance

        except Exception as e:
            # Catch potential Pydantic validation errors or provider init errors
            import logging  # Use logging within methods if not globally imported

            logging.error(
                f"Failed to initialize provider '{name}' with config {config_kwargs}: {type(e).__name__}: {e}"
            )
            # Optionally include traceback for debugging
            # import traceback
            # logging.error(traceback.format_exc())
            raise ProviderNotFoundError(
                f"Failed to initialize provider '{name}': {str(e)}"
            )

    @classmethod
    def get_config_class(cls, name: str) -> type[LLMConfig]:
        """Get the configuration class for a provider"""
        if name not in cls._configs:
            raise ProviderNotFoundError(
                f"Config class for provider '{name}' not found."
            )
        return cls._configs[name]

    @classmethod
    def available_providers(cls) -> list[str]:
        """Get a list of available provider names"""
        return list(cls._providers.keys())
