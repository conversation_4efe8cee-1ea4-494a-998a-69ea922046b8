# Import providers here for easy access and registration with the factory if needed
try:
    from .openai import OpenAIConfig, OpenAIProvider

    # Import other providers as they are added
    # from .anthropic import AnthropicProvider, AnthropicConfig

    # You might want to explicitly register providers with the factory here
    # if they aren't automatically discovered or registered elsewhere.
    # from app.core.llm.factory import LLMFactory
    # LLMFactory.register_provider("openai", OpenAIProvider, OpenAIConfig)
    # LLMFactory.register_provider("anthropic", AnthropicProvider, AnthropicConfig)

except ImportError as e:
    import logging

    logging.warning(f"Could not import all LLM providers from __init__.py: {e}")
    # Define __all__ carefully based on successful imports if necessary
    __all__ = []  # Default to empty if imports fail
else:
    # Define __all__ to control what `from .providers import *` imports
    __all__ = [
        "OpenAIProvider",
        "OpenAIConfig",
        # "AnthropicProvider",
        # "AnthropicConfig",
    ]
