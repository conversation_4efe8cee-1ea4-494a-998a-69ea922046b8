import json
import logging
from typing import Any, Optional, TypeVar

from openai import <PERSON>ync<PERSON>penAI
from pydantic import BaseModel, ConfigDict, Field

# Ensure relative imports work correctly within the backend structure
try:
    from app.core.llm.base import (
        LLMConfig,
        LLMException,
        LLMProvider,
        LLMResponse,
        PromptTemplate,
    )
    from app.core.security.key_management import KeyManager, KeyType, ProviderType
except ImportError as e:
    # Provide more context if imports fail
    logging.error(
        f"Import error in openai.py: {e}. Check module paths and circular dependencies."
    )
    # Re-raise or define placeholders if necessary for the script to load
    raise

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


class OpenAIConfig(LLMConfig):
    """OpenAI-specific configuration"""

    model: str = Field(
        ..., description="The OpenAI model to use (e.g., gpt-4, gpt-3.5-turbo)"
    )
    temperature: float = Field(default=0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(
        default=None, description="Maximum number of tokens to generate"
    )
    api_key: Optional[str] = Field(
        default=None, description="Optional OpenAI API key override"
    )

    model_config = ConfigDict(
        extra="allow",  # Allow extra fields
        arbitrary_types_allowed=True,
    )


class OpenAIProvider(LLMProvider[T]):
    """OpenAI implementation of LLM provider"""

    def __init__(self, config: OpenAIConfig):
        super().__init__(config)
        self.config: OpenAIConfig = config

        # Use config key if provided, otherwise get from KeyManager
        api_key_to_use = None
        if config.api_key:
            api_key_to_use = config.api_key
            logger.info("Using API key provided directly in config.")
        else:
            try:
                key_config = KeyManager.get_key(
                    key_type=KeyType.PROVIDER, provider=ProviderType.OPENAI
                )
                api_key_to_use = key_config.key
                logger.info("Retrieved API key using KeyManager.")
            except Exception as e:  # Catch potential APIError from KeyManager
                logger.error(f"Failed to get OpenAI API key via KeyManager: {e}")
                # Decide how to handle: raise, use a default, or proceed without a key
                # For now, let's raise an exception if the key is essential
                raise LLMException(
                    f"OpenAI API key not found or KeyManager failed: {e}"
                )

        if not api_key_to_use:
            # This case might occur if KeyManager fails and we don't raise immediately
            logger.error("OpenAI API key is missing. Provider cannot be initialized.")
            raise LLMException(
                "OpenAI API key is required but was not found or provided."
            )

        self.client = AsyncOpenAI(api_key=api_key_to_use)
        logger.info(f"OpenAIProvider initialized for model: {self.config.model}")

    @property
    def model_name(self) -> str:
        return self.config.model

    async def generate(self, prompt: str) -> LLMResponse:
        """Generate a response using OpenAI"""
        logger.debug(
            f"Generating response for prompt (first 100 chars): {prompt[:100]}..."
        )
        # Add full prompt logging for debugging (consider removing in production)
        logger.debug(f"Full prompt being sent to OpenAI: {prompt}")
        try:
            # Configure parameters based on model
            params = {
                "model": self.config.model,
                "messages": [{"role": "user", "content": prompt}],
            }

            # Handle o4-mini model restrictions
            if self.config.model == "o4-mini":
                # o4-mini only supports default temperature (1.0)
                # No temperature parameter should be set

                # o4-mini uses max_completion_tokens instead of max_tokens
                if self.config.max_tokens:
                    params["max_completion_tokens"] = self.config.max_tokens
            else:
                # For other models, use standard parameters
                params["temperature"] = self.config.temperature
                if self.config.max_tokens:
                    params["max_tokens"] = self.config.max_tokens

            # Log the exact parameters we're sending
            logger.info(f"Sending to OpenAI with params: {params}")

            completion = await self.client.chat.completions.create(**params)

            # Log the raw response
            logger.info(f"Raw OpenAI API response: {completion}")

            if not completion.choices:
                logger.warning("No response choices received from OpenAI.")
                logger.debug(f"Raw completion object: {completion}")
                raise LLMException("No response choices from OpenAI")

            content = completion.choices[0].message.content
            logger.debug(f"Raw completion: {completion}")
            if content is None:  # Check for None explicitly
                logger.warning("Received None content from OpenAI.")
                content = ""  # Assign empty string or handle as error

            logger.debug(
                f"Raw OpenAI response content (first 100 chars): {content[:100]}..."
            )

            usage_data = completion.usage.model_dump() if completion.usage else {}
            logger.debug(f"Token usage: {usage_data}")

            return LLMResponse(content=content, model=self.model_name, usage=usage_data)

        except Exception as e:
            logger.error(
                f"OpenAI API error during generate: {type(e).__name__}: {str(e)}"
            )
            # Consider logging traceback for detailed debugging
            # import traceback
            # logger.error(traceback.format_exc())
            raise LLMException(f"OpenAI API error: {str(e)}")

    async def generate_structured(
        self,
        prompt: str,  # This should now be ONLY the user's natural language input
        output_schema: type[BaseModel],
        # Add context parameters needed for the system prompt
        supported_actions_text: str,
        current_datetime: str,
        current_year: int,
        local_timezone_offset: float,
    ) -> BaseModel:
        """
        Generate a structured response from OpenAI using JSON mode, validating against the provided Pydantic model.
        Includes necessary context in the system prompt for intent extraction.
        """
        logger.debug(
            f"Generating structured response for user input (first 100 chars): {prompt[:100]}..."
        )
        logger.debug(f"Target Pydantic schema: {output_schema.__name__}")
        try:
            # Get JSON schema from the Pydantic model
            schema = output_schema.model_json_schema()
            schema_json = json.dumps(schema, indent=2)
            # logger.debug(f"Using JSON schema derived from {output_schema.__name__}: {schema_json}") # Schema details logged below

            # Construct the detailed system prompt, incorporating context previously in intent_resolver_service.py
            system_prompt_content = f"""
You are an AI assistant tasked with extracting structured information from user queries in a healthcare management system.
Your job is to identify the user's intent and extract relevant parameters for a specific API action.
Respond ONLY with a valid JSON object conforming to the specified schema. Do not include any explanatory text or markdown formatting.

Today's date and time is: {current_datetime}.
The local timezone offset from UTC is: {local_timezone_offset} hours.
The target JSON schema (derived from Pydantic model {output_schema.__name__}) is:
```json
{schema_json}
```

Here are the supported actions and their parameters:
{supported_actions_text}

Given the user's input, determine the most likely action they want to perform and extract the required parameters according to the schema.

Follow these guidelines:
1. Only select from the supported `action_type` values listed above.
2. Extract all parameters needed for the action as defined in the schema.
3. If a parameter is mentioned but ambiguous, assign a confidence score less than 1.0.
4. If a required parameter is missing completely, mark its value as null in the JSON.
5. IMPORTANT FOR TIMES: Interpret times as being in the user's local timezone. Format them as ISO8601 strings WITH the +00:00 UTC timezone suffix, but the actual time value should be what the user means in their local time. Example: User says 2pm, timezone is UTC-5 -> return "YYYY-MM-DDTHH:14:00:00+00:00".
6. Do not invent or assume information not provided by the user unless specified by default rules.
7. If you cannot determine a clear action, set `action_type` to null or an appropriate error string if defined in the schema.
8. For appointment actions, ALWAYS attempt to identify date and time. Use relative dates ("tomorrow", "next week") based on today's date. Default to next business day if no date mentioned. Default to 9:00 AM if no time mentioned. Use {current_year} for dates. Use ISO 8601 format (YYYY-MM-DDThh:mm:ss+00:00).
9. Extract appointment duration in minutes (numeric) for `duration_minutes`.
10. Extract appointment type ("initial", "follow-up") for `appointment_type`.
11. Extract patient names for `patient_id`. Use the full name provided.

Output only the JSON object.
            """.strip()

            messages = [
                {"role": "system", "content": system_prompt_content},
                {
                    "role": "user",
                    "content": prompt,
                },  # 'prompt' is now just the user input
            ]

            # Log the API call parameters (be careful with sensitive data in logs)
            api_params = {
                "model": self.config.model,
                "messages": [
                    {
                        "role": m["role"],
                        "content": (
                            m["content"][:100] + "..."
                            if len(m["content"]) > 100
                            else m["content"]
                        ),
                    }
                    for m in messages
                ],  # Log truncated messages
                "response_format": {
                    "type": "json_object"
                },  # Only include type, not schema
            }

            # Add the appropriate parameters based on model
            if self.config.model == "o4-mini":
                # o4-mini only supports default temperature (1.0)
                # o4-mini uses max_completion_tokens instead of max_tokens
                if self.config.max_tokens:
                    api_params["max_completion_tokens"] = self.config.max_tokens
            else:
                # For standard models, use regular parameters
                api_params["temperature"] = self.config.temperature
                if self.config.max_tokens:
                    api_params["max_tokens"] = self.config.max_tokens

            logger.debug(
                f"OpenAI API call parameters (truncated messages): {json.dumps(api_params, indent=2)}"
            )

            # Enhanced logging for troubleshooting
            logger.info(f"Sending to OpenAI with params: {api_params}")

            # Clone api_params and actually use them for the API call
            call_params = dict(api_params)
            # Remove the truncated messages used for logging
            call_params.pop("messages")
            # Use the actual full messages
            call_params["messages"] = messages
            # Set response_format to JSON object without schema
            call_params["response_format"] = {"type": "json_object"}

            # Remove schema parameter completely - not supported
            if "schema" in call_params.get("response_format", {}):
                del call_params["response_format"]["schema"]

            # Make the API call
            completion = await self.client.chat.completions.create(**call_params)

            # Log the raw response
            logger.info(f"Raw OpenAI structured generation response: {completion}")

            if not completion.choices:
                logger.warning(
                    "No response choices received from OpenAI in structured generation."
                )
                logger.debug(f"Raw completion object: {completion}")
                raise LLMException("No response choices from OpenAI")

            content = completion.choices[0].message.content
            logger.debug(f"Raw completion: {completion}")
            if not content:
                logger.warning(
                    "Empty content string received from OpenAI in structured generation."
                )
                # Decide how to handle: raise error or try to return default/empty schema instance?
                # Raising an error is usually safer.
                raise LLMException(
                    "Empty response content from OpenAI, cannot parse JSON."
                )

            logger.debug(
                f"Raw OpenAI JSON response: {content[:500]}..."
            )  # Log more for JSON debugging

            try:
                # Parse and validate response directly into the Pydantic model
                instance = output_schema.model_validate_json(content)
                logger.debug(
                    f"Successfully validated response against Pydantic schema {output_schema.__name__}"
                )
                return instance
            except Exception as e:
                logger.error(
                    f"Pydantic validation failed for {output_schema.__name__}: {type(e).__name__}: {str(e)}"
                )
                logger.error(f"Raw response that failed validation: {content}")
                logger.error(
                    f"Expected schema (from {output_schema.__name__}): {schema_json}"
                )
                # Consider including the prompt in the error for better debugging context
                raise LLMException(
                    f"Response did not match schema {output_schema.__name__}: {str(e)}. Raw response: {content[:200]}..."
                )

        except Exception as e:
            logger.error(f"Error in generate_structured: {type(e).__name__}: {str(e)}")
            # import traceback
            # logger.error(traceback.format_exc())
            raise LLMException(f"Failed during structured generation: {str(e)}")

    async def generate_with_template(
        self, template: PromptTemplate, variables: dict[str, Any]
    ) -> LLMResponse:
        """Generate a response using a prompt template"""
        logger.debug(f"Generating response with template: {template.input_variables}")
        try:
            # Log the variables being passed to the template
            logger.debug(f"Template variables: {variables.keys()}")

            # Add model name to variables if not present
            if "model_name" not in variables:
                variables["model_name"] = self.model_name

            # Directly process the template instead of using format method
            if hasattr(template, "template") and isinstance(template.template, str):
                # Start with the template string
                template_str = template.template

                # Manual variable replacement
                prompt = template_str
                for var_name, var_value in variables.items():
                    placeholder = "{" + var_name + "}"
                    if placeholder in prompt:
                        # Convert any non-string values to strings
                        if not isinstance(var_value, str):
                            var_value = str(var_value)
                        prompt = prompt.replace(placeholder, var_value)

                # Check for unresolved variables
                import re

                unresolved = re.findall(r"\{([a-zA-Z_][a-zA-Z0-9_]*)\}", prompt)
                if unresolved:
                    logger.warning(
                        f"Unresolved template variables detected: {unresolved}"
                    )
                    logger.warning(f"Available variables: {list(variables.keys())}")
                    # Replace unresolved with empty strings to prevent confusion
                    for var in unresolved:
                        prompt = prompt.replace("{" + var + "}", f"[{var}]")
            else:
                # Fallback to original formatting if template is not a string
                prompt = template.format(**variables)

            # Log the formatted prompt
            logger.debug(f"Formatted prompt (first 100 chars): {prompt[:100]}...")
            logger.debug(f"Full processed prompt: {prompt}")

            # Check for potential placeholder issues
            placeholder_patterns = [
                "{{assistant_response}}",
                "{{response}}",
                "{assistant_response}",
                "{response}",
            ]
            for pattern in placeholder_patterns:
                if pattern in prompt:
                    logger.warning(
                        f"Potential placeholder issue detected in processed prompt: {pattern}"
                    )

            return await self.generate(prompt)

        except Exception as e:
            logger.error(
                f"Error in generate_with_template: {type(e).__name__}: {str(e)}"
            )
            raise LLMException(f"Error generating from template: {str(e)}")

    async def generate_structured_with_template(
        self,
        template: PromptTemplate,
        variables: dict[str, Any],
        output_schema: type[T],
    ) -> T:
        """Generate a structured response using a prompt template."""
        logger.debug(
            f"Generating structured response with template: {template.input_variables}, schema: {output_schema.__name__}"
        )
        try:
            # Add model name to variables if not present
            if "model_name" not in variables:
                variables["model_name"] = self.model_name

            # Format template
            prompt = template.format(**variables)
            logger.debug(
                f"Formatted prompt for structured generation (first 100 chars): {prompt[:100]}..."
            )
            return await self.generate_structured(prompt, output_schema)

        except Exception as e:
            logger.error(
                f"Error in generate_structured_with_template: {type(e).__name__}: {str(e)}"
            )
            raise LLMException(
                f"Error generating structured response from template: {str(e)}"
            )

    async def close(self) -> None:
        """Close any resources used by the provider"""
        # AsyncOpenAI client doesn't explicitly require closing in typical usage
        # If connection pooling or other resources were managed, close them here.
        logger.info("Closing OpenAIProvider (no specific resources to release).")
        pass
