import logging
from logging.config import dictConfig


def configure_logging():
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
            }
        },
        "handlers": {
            "console": {"class": "logging.StreamHandler", "formatter": "default"}
        },
        "root": {"level": "DEBUG", "handlers": ["console"]},
    }
    dictConfig(logging_config)


# Create and configure a logger instance
logger = logging.getLogger("app_logger")
configure_logging()
