from typing import Dict, Optional, Any

from pydantic_settings import BaseSettings


class RAGConfig(BaseSettings):
    """Configuration for RAG system optimization and caching."""

    # Embedding model configuration
    embedding_model_name: str = "all-mpnet-base-v2"
    chunk_size: int = 500
    chunk_overlap: int = 50

    # RAG search configuration
    similarity_threshold: float = 0.35  # Default threshold (slightly higher)
    max_chunks: int = 5  # Updated from 3
    max_scraped_pages: int = 2
    
    # Dynamic similarity thresholds
    similarity_thresholds: Dict[str, float] = {
        "default": 0.35,      # General queries
        "specific": 0.45,     # Specific information queries (names, numbers, hours)
        "general": 0.35,      # General information queries
        "exploratory": 0.25,  # Broad, exploratory searches
        "clinical": 0.40,     # Clinical/medical queries requiring accuracy
    }

    # Redis cache configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: Optional[str] = None
    redis_db: int = 0
    redis_key_prefix: str = "pulsetrack:rag:"

    # Cache TTL settings (in seconds)
    embedding_ttl: int = 86400  # 24 hours for embeddings
    rag_result_ttl: int = 3600  # 1 hour for RAG results
    text_embedding_ttl: int = 7200  # 2 hours for text embeddings
    chunk_cache_ttl: int = 86400  # 24 hours for content chunks

    # Performance optimization settings
    batch_size: int = 10
    max_concurrency: int = 5
    timeout: int = 30

    # Feature flags
    enable_caching: bool = True
    enable_async_processing: bool = False  # For future implementation
    enable_monitoring: bool = True

    class Config:
        env_prefix = "RAG_"
        env_file = ".env"
    
    def get_dynamic_threshold(self, query: str, context: Optional[Dict] = None) -> float:
        """Get dynamic similarity threshold based on query characteristics."""
        query_lower = query.lower()
        
        # Specific queries (looking for exact information)
        specific_indicators = [
            "what is", "how much", "when does", "where is", 
            "phone", "address", "hours", "cost", "price", "email",
            "contact", "location", "schedule", "time"
        ]
        if any(indicator in query_lower for indicator in specific_indicators):
            return self.similarity_thresholds["specific"]
        
        # Clinical/medical queries (requiring accuracy)
        clinical_indicators = [
            "dosage", "dose", "medication", "side effect", "contraindication",
            "interaction", "symptom", "treatment", "diagnosis", "procedure"
        ]
        if any(indicator in query_lower for indicator in clinical_indicators):
            return self.similarity_thresholds["clinical"]
        
        # Exploratory queries (browsing for information)
        exploratory_indicators = [
            "tell me about", "information about", "what do you know",
            "anything about", "overview", "general", "explain"
        ]
        if any(indicator in query_lower for indicator in exploratory_indicators):
            return self.similarity_thresholds["exploratory"]
        
        # Check context for additional hints
        if context:
            user_role = context.get("user_role", "").lower()
            if user_role == "clinician":
                # Clinicians might need more precise information
                return self.similarity_thresholds["clinical"]
        
        # Default threshold
        return self.similarity_thresholds["default"]
