"""Unified API key management system."""

import os
from datetime import UTC, datetime
from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator
from sqlalchemy.orm import Session

# Attempt to import APIError, handle potential circularity or missing module gracefully
try:
    from app.core.exceptions import APIError
except ImportError:
    # Define a placeholder if APIError is not available at this stage
    class APIError(Exception):
        def __init__(self, status_code: int, detail: str):
            self.status_code = status_code
            self.detail = detail
            super().__init__(detail)


class KeyType(str, Enum):
    """Types of API keys in the system."""

    AUTH = "auth"  # For authenticating with our API
    PROVIDER = "provider"  # For external service providers


class ProviderType(str, Enum):
    """Supported external providers."""

    OPENAI = "openai"
    AZURE = "azure"
    ANTHROPIC = "anthropic"


class KeyFormat(BaseModel):
    model_config = ConfigDict(extra="forbid")
    """Key format requirements."""
    prefix: Optional[str] = None
    length: Optional[int] = None
    pattern: Optional[str] = None
    required_fields: Optional[dict[str, Any]] = None


class ProviderKeyConfig(BaseModel):
    model_config = ConfigDict(extra="forbid")
    """Provider-specific key configuration."""
    env_var: str
    file_env_var: Optional[str] = None
    format: KeyFormat
    description: str


class KeyConfig(BaseModel):
    """Unified key configuration."""

    key: str = Field(..., description="The API key")
    key_type: KeyType
    provider: Optional[ProviderType] = None
    user_id: Optional[str] = None
    is_valid: bool = Field(default=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    additional_fields: dict[str, Any] = Field(default_factory=dict)

    model_config = ConfigDict(extra="forbid")

    @field_validator("provider")
    def validate_provider_presence(cls, v, info):
        """Ensure provider is set for provider keys."""
        # Access data using info.data instead of validation_info.data
        if info.data.get("key_type") == KeyType.PROVIDER and not v:
            raise ValueError("Provider must be specified for provider keys")
        return v

    @field_validator("key")
    def validate_key_format(cls, v, info):
        """Validate key format based on type and provider."""
        # Access data using info.data
        key_type = info.data.get("key_type")
        provider = info.data.get("provider")

        if key_type == KeyType.PROVIDER and provider:
            # Ensure provider exists in PROVIDER_CONFIGS before accessing
            if provider not in PROVIDER_CONFIGS:
                raise ValueError(
                    f"Unsupported provider type: {provider}"
                )  # Or handle appropriately

            format_config = PROVIDER_CONFIGS[provider].format

            if format_config.prefix and not v.startswith(format_config.prefix):
                raise ValueError(
                    f"{provider} API key must start with '{format_config.prefix}'"
                )

            if format_config.length and len(v) != format_config.length:
                raise ValueError(
                    f"{provider} API key must be {format_config.length} characters long"
                )

        return v


# Provider-specific configurations
PROVIDER_CONFIGS: dict[ProviderType, ProviderKeyConfig] = {
    ProviderType.OPENAI: ProviderKeyConfig(
        env_var="OPENAI_API_KEY",
        file_env_var="OPENAI_API_KEY_FILE",
        format=KeyFormat(
            prefix=None,  # OpenAI keys can have different prefixes
            length=None,  # Length can vary
            pattern=None,
        ),
        description="OpenAI API key",
    ),
    ProviderType.AZURE: ProviderKeyConfig(
        env_var="AZURE_API_KEY",
        format=KeyFormat(required_fields={"api_version": str, "deployment_name": str}),
        description="Azure OpenAI API key",
    ),
    ProviderType.ANTHROPIC: ProviderKeyConfig(
        env_var="ANTHROPIC_API_KEY",
        format=KeyFormat(prefix="sk-ant-", length=None, pattern=None),
        description="Anthropic API key",
    ),
}


class KeyManager:
    """Unified key management system."""

    @staticmethod
    def get_key(
        key_type: KeyType,
        provider: Optional[ProviderType] = None,
        key: Optional[str] = None,
        db: Optional[Session] = None,
        user_id: Optional[str] = None,
        additional_fields: Optional[dict[str, Any]] = None,
    ) -> KeyConfig:
        """
        Get a key configuration.

        Args:
            key_type: Type of key (auth or provider)
            provider: Provider type for provider keys
            key: Specific key to validate (for auth keys)
            db: Database session (required for auth keys)
            user_id: User ID (required for auth keys)
            additional_fields: Additional provider-specific fields

        Returns:
            KeyConfig: The key configuration

        Raises:
            APIError: If key cannot be retrieved or validated
        """
        if key_type == KeyType.AUTH:
            if not all([db, key, user_id]):
                raise APIError(
                    status_code=500,
                    detail="Database session, key, and user_id required for auth keys",
                )

            # Verify against database
            # Placeholder - Replace with actual verification logic
            if not key or not user_id:
                raise APIError(status_code=401, detail="Invalid authentication key")

            return KeyConfig(
                key=key,
                key_type=KeyType.AUTH,
                user_id=user_id,
                created_at=datetime.now(UTC),
            )

        elif key_type == KeyType.PROVIDER:
            if not provider:
                raise APIError(
                    status_code=500, detail="Provider type required for provider keys"
                )

            if provider not in PROVIDER_CONFIGS:
                raise APIError(
                    status_code=500, detail=f"Unsupported provider: {provider}"
                )

            config = PROVIDER_CONFIGS[provider]

            # Check for required additional fields
            if config.format.required_fields:
                if not additional_fields:
                    raise APIError(
                        status_code=500,
                        detail=f"Additional fields required for {provider}: {list(config.format.required_fields.keys())}",
                    )
                for field, field_type in config.format.required_fields.items():
                    if field not in additional_fields or not isinstance(
                        additional_fields[field], field_type
                    ):
                        raise APIError(
                            status_code=500,
                            detail=f"Invalid or missing field '{field}' for {provider}",
                        )

            # Get key from environment or file
            provider_key = key or os.getenv(config.env_var)

            if not provider_key and config.file_env_var:
                key_file = os.getenv(config.file_env_var)
                if key_file:
                    try:
                        with open(key_file) as f:
                            provider_key = f.read().strip()
                    except Exception as e:
                        raise APIError(
                            status_code=500,
                            detail=f"Failed to read API key file: {str(e)}",
                        )

            if not provider_key:
                raise APIError(
                    status_code=500,
                    detail=f"{provider} API key not found in environment or key file",
                )

            return KeyConfig(
                key=provider_key,
                key_type=KeyType.PROVIDER,
                provider=provider,
                additional_fields=additional_fields or {},
            )

        raise APIError(status_code=500, detail=f"Invalid key type: {key_type}")

    @staticmethod
    def validate_key_format(
        key: str, key_type: KeyType, provider: Optional[ProviderType] = None
    ) -> bool:
        """Validate a key's format."""
        try:
            # Pass additional_fields if needed for validation, especially for Azure
            additional_fields = {}
            if provider == ProviderType.AZURE:
                # You might need to fetch/pass actual Azure fields here if validation depends on them
                pass
            KeyConfig(
                key=key,
                key_type=key_type,
                provider=provider,
                additional_fields=additional_fields,  # Pass potentially required fields
            )
            return True
        except Exception:
            return False

    @staticmethod
    def get_provider_requirements(provider: ProviderType) -> ProviderKeyConfig:
        """Get the key requirements for a provider."""
        if provider not in PROVIDER_CONFIGS:
            raise ValueError(f"Unsupported provider: {provider}")
        return PROVIDER_CONFIGS[provider]
