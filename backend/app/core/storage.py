import logging
import uuid
from typing import Optional

from azure.core.exceptions import AzureError
from azure.storage.blob import (
    BlobClient,
    BlobServiceClient,
    ContentSettings,
)  # Added ContentSettings
from fastapi import UploadFile

from app.core.config import settings  # Corrected import path

logger = logging.getLogger(__name__)

# Initialize BlobServiceClient
# --- DEBUG: Log the connection string value ---
logger.info(
    f"Attempting to use Azure Connection String: '{settings.AZURE_STORAGE_CONNECTION_STRING}'"
)
# --- END DEBUG ---
try:
    blob_service_client = BlobServiceClient.from_connection_string(
        settings.AZURE_STORAGE_CONNECTION_STRING
    )
    logger.info("Azure Blob Service Client initialized successfully.")
except ValueError as e:
    logger.error(f"Azure Storage Connection String is invalid: {e}")
    blob_service_client = None
except Exception as e:
    logger.error(f"Failed to initialize Azure Blob Service Client: {e}")
    blob_service_client = None


def generate_unique_filename(original_filename: str) -> str:
    """Generates a unique filename using UUID and preserving the original extension."""
    extension = ""
    if "." in original_filename:
        extension = original_filename.rsplit(".", 1)[1].lower()
    unique_id = uuid.uuid4()
    return f"{unique_id}.{extension}" if extension else str(unique_id)


async def upload_file_to_azure(
    file: UploadFile,
    destination_blob_name: str,
    container_name: str = settings.AZURE_STORAGE_CONTAINER_NAME,
) -> str | None:
    """
    Uploads a file to Azure Blob Storage.

    Args:
        file: The file uploaded via FastAPI.
        destination_blob_name: The desired name/path for the blob in the container.
        container_name: The name of the Azure Blob Storage container.

    Returns:
        The URL of the uploaded blob, or None if upload fails.
    """
    if not blob_service_client:
        logger.error(
            "Azure Blob Service Client is not initialized. Cannot upload file."
        )
        return None

    try:
        # Get a client for the specific blob
        blob_client: BlobClient = blob_service_client.get_blob_client(
            container=container_name, blob=destination_blob_name
        )

        # Ensure the file pointer is at the beginning
        await file.seek(0)
        file_content = await file.read()

        logger.info(
            f"Uploading file '{file.filename}' to Azure Blob Storage as '{destination_blob_name}' in container '{container_name}'..."
        )

        # Upload the file content
        blob_client.upload_blob(
            file_content,
            overwrite=True,
            content_settings=ContentSettings(content_type=file.content_type),
        )  # Use ContentSettings model

        logger.info(f"Successfully uploaded file to: {blob_client.url}")
        return blob_client.url

    except AzureError as e:
        logger.error(f"Azure error during file upload: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during file upload: {e}")
        return None
    finally:
        await file.close()


async def upload_profile_photo(
    file: UploadFile, user_id: str, user_type: str = "patient"
) -> str | None:
    """
    Uploads a user's profile photo to a structured path in Azure Blob Storage.

    Args:
        file: The uploaded file
        user_id: The ID of the user (patient or clinician)
        user_type: The type of user ("patient" or "clinician")

    Returns:
        The URL of the uploaded blob, or None if upload fails.
    """
    if not file or not file.filename:
        logger.warning("No file provided for profile photo upload.")
        return None

    unique_filename = generate_unique_filename(file.filename)
    destination_path = f"profile_photos/{user_type}/{user_id}/{unique_filename}"

    return await upload_file_to_azure(file=file, destination_blob_name=destination_path)


# For backward compatibility - this function just calls the more generic one
async def upload_patient_profile_photo(file: UploadFile, patient_id: str) -> str | None:
    """
    Uploads a patient's profile photo to a structured path in Azure Blob Storage.

    This function is maintained for backward compatibility.
    """
    return await upload_profile_photo(
        file=file, user_id=patient_id, user_type="patient"
    )


async def upload_clinician_profile_photo(
    file: UploadFile, clinician_id: str
) -> str | None:
    """
    Uploads a clinician's profile photo to a structured path in Azure Blob Storage.
    """
    return await upload_profile_photo(
        file=file, user_id=clinician_id, user_type="clinician"
    )


async def upload_education_material(
    file: UploadFile, 
    user_id: str, 
    clinic_id: Optional[str] = None
) -> str | None:
    """
    Uploads an education material file to a structured path in Azure Blob Storage.
    
    Args:
        file: The uploaded file
        user_id: The ID of the user uploading the file
        clinic_id: Optional clinic ID for clinic-specific materials
        
    Returns:
        The URL of the uploaded blob, or None if upload fails.
    """
    if not file or not file.filename:
        logger.warning("No file provided for education material upload.")
        return None
    
    unique_filename = generate_unique_filename(file.filename)
    
    # Construct path based on whether it's clinic-specific or public
    if clinic_id:
        destination_path = f"education_materials/clinics/{clinic_id}/{unique_filename}"
    else:
        destination_path = f"education_materials/public/{unique_filename}"
    
    return await upload_file_to_azure(file=file, destination_blob_name=destination_path)


# Example Usage (within an API endpoint):
# from fastapi import File, UploadFile
# from .storage import upload_profile_photo
#
# @router.put("/me")
# async def update_patient_me(
#     *,
#     db: Session = Depends(deps.get_db),
#     current_patient: models.Patient = Depends(deps.get_current_patient),
#     profile_photo: UploadFile = File(None), # Make photo optional
#     # ... other parameters
# ):
#     # ... existing logic ...
#     photo_url = None
#     if profile_photo:
#         photo_url = await upload_profile_photo(file=profile_photo, patient_id=str(current_patient.id))
#         if not photo_url:
#             raise HTTPException(status_code=500, detail="Failed to upload profile photo.")
#
#     # Update patient data in DB, including the photo_url if successful
#     # ... crud.patient.update(db=db, db_obj=current_patient, obj_in={"photo_url": photo_url, ...}) ...
#     return updated_patient
