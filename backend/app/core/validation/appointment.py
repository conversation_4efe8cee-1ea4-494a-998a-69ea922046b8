import uuid
from datetime import datetime, time, timedelta
from typing import TYPE_CHECKING, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.constants import appointment as appointment_constants

if TYPE_CHECKING:
    from app.schemas.appointment import AppointmentCreate, AppointmentUpdate


# Placeholder for potential custom exceptions
class AppointmentValidationError(ValueError):
    """Custom exception for appointment validation errors."""

    pass


class AppointmentValidationRules:
    """
    Encapsulates validation logic for appointment creation and updates.
    """

    @staticmethod
    def validate_datetime_is_timezone_aware(dt: datetime) -> datetime:
        """
        Validates that a datetime is timezone-aware.

        Args:
            dt: The datetime to validate

        Returns:
            The validated datetime

        Raises:
            AppointmentValidationError: If the datetime is not timezone-aware
        """
        if dt.tzinfo is None:
            raise AppointmentValidationError(
                "Appointment datetime must be timezone-aware"
            )
        return dt

    @staticmethod
    def validate_appointment_datetime(dt: datetime) -> datetime:
        """
        Validates that an appointment time is in the future and within business hours.

        Args:
            dt: The datetime to validate

        Returns:
            The validated datetime

        Raises:
            AppointmentValidationError: If the datetime is invalid
        """
        # Check if appointment is in the future
        now = datetime.now(dt.tzinfo)
        if dt < now:
            raise AppointmentValidationError("Appointment time must be in the future")

        # Check if within business hours (9 AM to 5 PM, Mon-Fri)
        business_start = time(9, 0)
        business_end = time(17, 0)
        if not (business_start <= dt.time() <= business_end and dt.weekday() < 5):
            raise AppointmentValidationError(
                "Appointment time must be within business hours (9 AM to 5 PM, Mon-Fri)"
            )

        return dt

    def _is_within_business_hours(self, dt: datetime) -> bool:
        """Checks if a datetime falls within configurable business hours."""
        # TODO: Implement logic based on settings (e.g., settings.BUSINESS_HOURS_START, settings.BUSINESS_HOURS_END)
        # Placeholder implementation: Assume 9 AM to 5 PM UTC for now
        business_start = time(9, 0)
        business_end = time(17, 0)
        return (
            business_start <= dt.time() <= business_end and dt.weekday() < 5
        )  # Mon-Fri

    def _is_valid_duration(self, duration_minutes: int) -> bool:
        """Checks if the duration is one of the allowed values."""
        return duration_minutes in appointment_constants.VALID_DURATIONS

    async def validate_appointment_time(
        self,
        db: AsyncSession,
        appointment_datetime: datetime,
        clinician_id: str,
        duration_minutes: int,
        exclude_appointment_id: Optional[uuid.UUID] = None,
    ) -> None:
        """
        Validates the proposed time for an appointment.

        Raises:
            AppointmentValidationError: If validation fails.
        """
        # 1. Check if within business hours (Placeholder)
        if not self._is_within_business_hours(appointment_datetime):
            raise AppointmentValidationError(
                "Appointment time is outside business hours."
            )

        # 2. Check if appointment starts in the past
        if appointment_datetime < datetime.utcnow():
            raise AppointmentValidationError(
                "Cannot schedule appointments in the past."
            )

        # 3. Check maximum advance booking days
        max_advance_delta = timedelta(
            days=appointment_constants.MAX_ADVANCE_BOOKING_DAYS
        )
        if appointment_datetime > datetime.utcnow() + max_advance_delta:
            raise AppointmentValidationError(
                f"Appointments cannot be booked more than {appointment_constants.MAX_ADVANCE_BOOKING_DAYS} days in advance."
            )

        # 4. Check duration validity
        if not self._is_valid_duration(duration_minutes):
            valid_durations_str = ", ".join(
                map(str, appointment_constants.VALID_DURATIONS)
            )
            raise AppointmentValidationError(
                f"Invalid appointment duration. Valid durations are: {valid_durations_str} minutes."
            )

        # 5. Check for conflicts with existing appointments for the clinician
        # This will be implemented in the service layer
        # TODO: Implement conflict checking

    async def check_clinician_availability(
        self,
        db: AsyncSession,
        clinician_id: str,
        start_time: datetime,
        end_time: datetime,
        exclude_appointment_id: Optional[uuid.UUID] = None,
    ) -> bool:
        """
        Checks if a clinician has any conflicting appointments during the specified time slot.

        Returns:
            bool: True if the clinician is available, False otherwise.
        """
        # This will be implemented in the service layer
        # TODO: Implement availability checking
        return True

    async def validate_patient_appointment_limits(
        self, db: AsyncSession, patient_id: str, appointment_datetime: datetime
    ) -> None:
        """
        Validates if a patient has reached the maximum number of future scheduled appointments.

        Raises:
            AppointmentValidationError: If the patient exceeds the limit.
        """
        # This will be implemented in the service layer
        # TODO: Implement patient limits checking

    def validate_clerk_id(self, user_id: str) -> None:
        """
        Validates the format of a Clerk User ID.
        Clerk User IDs typically start with 'user_' followed by alphanumeric characters.

        Raises:
            AppointmentValidationError: If the ID format is invalid.
        """
        # Basic check: starts with 'user_' and has some length after prefix
        prefix = "user_"
        min_length_after_prefix = 10  # Adjust based on observed Clerk ID lengths
        if (
            not user_id
            or not isinstance(user_id, str)
            or not user_id.startswith(prefix)
            or len(user_id) < len(prefix) + min_length_after_prefix
        ):
            raise AppointmentValidationError(
                f"Invalid Clerk User ID format for ID: {user_id}"
            )

    def validate_status_transition(self, current_status: str, new_status: str) -> None:
        """
        Validates if a status transition is allowed based on defined rules.

        Raises:
            AppointmentValidationError: If the transition is invalid.
        """
        allowed_transitions = appointment_constants.VALID_STATUS_TRANSITIONS.get(
            current_status
        )
        if allowed_transitions is None:
            raise AppointmentValidationError(
                f"Invalid current status: {current_status}"
            )
        if new_status not in allowed_transitions:
            raise AppointmentValidationError(
                f"Invalid status transition from '{current_status}' to '{new_status}'. Allowed: {', '.join(allowed_transitions) or 'None'}"
            )

    def validate_cancellation_policy(self, appointment_datetime: datetime) -> None:
        """
        Validates if an appointment can be cancelled based on the minimum notice period.

        Raises:
            AppointmentValidationError: If cancellation is attempted too close to the appointment time.
        """
        now = datetime.utcnow()
        time_until_appointment = appointment_datetime - now
        min_notice = timedelta(
            hours=appointment_constants.MIN_CANCELLATION_NOTICE_HOURS
        )

        if time_until_appointment < min_notice:
            raise AppointmentValidationError(
                f"Appointments cannot be cancelled less than {appointment_constants.MIN_CANCELLATION_NOTICE_HOURS} hours in advance."
            )

    def validate_appointment_type(self, appointment_type: str) -> None:
        """
        Validates if the appointment type is one of the allowed values.

        Raises:
            AppointmentValidationError: If the type is invalid.
        """
        if appointment_type not in appointment_constants.VALID_APPOINTMENT_TYPES:
            valid_types_str = ", ".join(appointment_constants.VALID_APPOINTMENT_TYPES)
            raise AppointmentValidationError(
                f"Invalid appointment type: '{appointment_type}'. Valid types are: {valid_types_str}."
            )

    async def validate_appointment_creation(
        self, db: AsyncSession, appointment_in: "AppointmentCreate"
    ) -> None:
        """
        Validates all aspects of appointment creation.

        Args:
            db: Database session
            appointment_in: Appointment creation data

        Raises:
            AppointmentValidationError: If any validation fails
        """
        # Validate datetime is timezone-aware
        self.validate_datetime_is_timezone_aware(appointment_in.appointment_datetime)

        # Validate appointment time (business hours, etc.)
        self.validate_appointment_datetime(appointment_in.appointment_datetime)

        # Validate duration
        if not self._is_valid_duration(appointment_in.duration_minutes):
            raise AppointmentValidationError(
                f"Invalid duration: {appointment_in.duration_minutes}. "
                f"Must be one of: {appointment_constants.ALLOWED_APPOINTMENT_DURATIONS}"
            )

        # Validate appointment type
        self.validate_appointment_type(appointment_in.appointment_type)

        # Validate Clerk IDs
        self.validate_clerk_id(appointment_in.patient_id)
        self.validate_clerk_id(appointment_in.clinician_id)

        # Validate appointment time availability
        await self.validate_appointment_time(
            db,
            appointment_in.appointment_datetime,
            appointment_in.clinician_id,
            appointment_in.duration_minutes,
        )

        # Validate patient appointment limits
        await self.validate_patient_appointment_limits(
            db, appointment_in.patient_id, appointment_in.appointment_datetime
        )

    async def validate_appointment_update(
        self,
        db: AsyncSession,
        appointment_id: uuid.UUID,
        current_appointment: dict,
        appointment_in: "AppointmentUpdate",
    ) -> None:
        """
        Validates all aspects of appointment updates.

        Args:
            db: Database session
            appointment_id: ID of the appointment being updated
            current_appointment: Current appointment data
            appointment_in: Update data

        Raises:
            AppointmentValidationError: If any validation fails
        """
        # If datetime is being updated
        if appointment_in.appointment_datetime is not None:
            self.validate_datetime_is_timezone_aware(
                appointment_in.appointment_datetime
            )
            self.validate_appointment_datetime(appointment_in.appointment_datetime)

            # Validate appointment time availability
            await self.validate_appointment_time(
                db,
                appointment_in.appointment_datetime,
                current_appointment["clinician_id"],
                appointment_in.duration_minutes
                or current_appointment["duration_minutes"],
                exclude_appointment_id=appointment_id,
            )

        # If duration is being updated
        if appointment_in.duration_minutes is not None:
            if not self._is_valid_duration(appointment_in.duration_minutes):
                raise AppointmentValidationError(
                    f"Invalid duration: {appointment_in.duration_minutes}. "
                    f"Must be one of: {appointment_constants.ALLOWED_APPOINTMENT_DURATIONS}"
                )

        # If appointment type is being updated
        if appointment_in.appointment_type is not None:
            self.validate_appointment_type(appointment_in.appointment_type)

        # Validate status transition if status is being changed
        if (
            appointment_in.status is not None
            and appointment_in.status != current_appointment["status"]
        ):
            self.validate_status_transition(
                current_appointment["status"], appointment_in.status
            )
            # If changing to 'cancelled', check cancellation policy
            if appointment_in.status == "cancelled":
                self.validate_cancellation_policy(current_appointment["start_time"])

        # Re-validate patient limits only if the patient ID changed (unlikely but possible)
        if (
            appointment_in.patient_id is not None
            and appointment_in.patient_id != current_appointment["patient_id"]
        ):
            await self.validate_patient_appointment_limits(
                db=db,
                patient_id=appointment_in.patient_id,
                appointment_datetime=appointment_in.appointment_datetime,
            )


# Instantiate the validator for easy import and use
appointment_validator = AppointmentValidationRules()
