"""
CRUD package interface providing standardized access to all CRUD operations.
This module explicitly aliases all CRUD modules for consistent access patterns
throughout the application.

Example usage:
    from app import crud

    # Get a patient
    patient = crud.patient.get(db, id=patient_id)

    # Create a note
    note = crud.note.create_note(db, obj_in=note_data, clinician_id=clinician_id)
"""

# Access and Authentication
from . import crud_access_code as access_code

# Clinical Data
from . import crud_appointment as appointment
from .crud_clinical_note import clinical_note

# Auditing and Logging
from . import crud_audit_log as audit_log
from . import crud_clinic as clinic
from . import crud_clinic_medication_association as clinic_medication_association
from . import crud_clinician as clinician

# Content Management
from . import crud_content as content
from . import crud_content_chunk as content_chunk

# Education System
from . import crud_education_material as education_material
from . import crud_education_progress as education_progress
from . import crud_patient_education_assignment as patient_education_assignment

# LLM and Templates
from . import crud_event_log as event_log
from . import crud_lab_result as lab_result
from . import crud_medication as medication
from . import crud_note as note
from . import crud_notification as notification
from .crud_patient_alert import patient_alert
from . import crud_scraped_page as scraped_page
from . import crud_weight_log as weight_log
from .crud_appointment_request import appointment_request
from .crud_audit_log import create_audit_log

# Communication
from .crud_chat_message import chat_message, create_chat_message

# Correct the import to bring in the instance, not the module
from .crud_medication_request import medication_request

# Core Patient and Clinician
from .crud_patient import patient
from .crud_side_effect_report import side_effect_report
from .crud_template import template, template_role

__all__ = [
    # Access and Authentication
    "access_code",
    # Core Patient and Clinician
    "patient",
    "clinician",
    "clinic",
    # Clinical Data
    "appointment",
    "appointment_request",
    "clinical_note",
    "lab_result",
    "medication",
    "clinic_medication_association",
    "medication_request",
    "note",
    "side_effect_report",
    "weight_log",
    # Communication
    "chat_message",
    "notification",
    "patient_alert",
    # Content Management
    "content",
    "content_chunk",
    "scraped_page",
    # Education System
    "education_material",
    "education_progress",
    "patient_education_assignment",
    # Auditing and Logging
    "audit_log",
    "create_audit_log",
    "create_chat_message",
    # LLM and Templates
    "event_log",
    "template",
    "template_role",
]
