import uuid
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import (
    func,
    select,
)
from sqlalchemy.orm import Session

from app.core.auth_utils import (
    hash_access_code,
    verify_access_code,
)
from app.crud.base import CRUDBase
from app.models.access_code import AccessCode
from app.schemas.auth import AccessCodeCreate, AccessCodeUpdate


class AccessCodeError(Exception):
    """Custom exception for access code errors."""

    pass


class AccessCodeNotFoundError(AccessCodeError):
    """Raised when an access code is not found."""

    pass


class AccessCodeExpiredError(AccessCodeError):
    """Raised when an access code is expired."""

    pass


class AccessCodeUsedError(AccessCodeError):
    """Raised when an access code has already been used."""

    pass


class CRUDAccessCode(CRUDBase[AccessCode, AccessCodeCreate, AccessCodeUpdate]):
    """CRUD operations for AccessCode model."""

    def create(self, db: Session, *, obj_in: AccessCodeCreate) -> AccessCode:
        """
        Creates a new access code in the database.
        Hashes the provided code value and stores the hash instead of plaintext.
        Requires email for the invitation.
        """
        # Ensure expires_at is timezone-aware (UTC assumed if naive)
        if obj_in.expires_at.tzinfo is None:
            expires_at_aware = obj_in.expires_at.replace(tzinfo=timezone.utc)
        else:
            expires_at_aware = obj_in.expires_at

        # Store the plaintext code temporarily
        plaintext_code = obj_in.code

        # Hash the code before storing it
        hashed_code = hash_access_code(plaintext_code)

        db_obj = AccessCode(
            code=hashed_code,  # Store the hash, not the plaintext
            email=obj_in.email,
            expires_at=expires_at_aware,
            is_used=obj_in.is_used if hasattr(obj_in, "is_used") else False,
            patient_id=obj_in.patient_id if hasattr(obj_in, "patient_id") else None,
            clinician_id=(
                obj_in.clinician_id if hasattr(obj_in, "clinician_id") else None
            ),
        )
        db.add(db_obj)
        try:
            db.commit()
        except Exception as e:
            db.rollback()
            raise AccessCodeError(
                f"Failed to create access code. Possible duplicate or missing required field: {e}"
            )

        db.refresh(db_obj)

        # Create a new object with the plaintext code for return
        # This ensures the plaintext code is only available during this function call
        result = db_obj
        # We need to temporarily return the plaintext code to the caller
        # Modified class attributes are temporary and never stored in DB
        result.plaintext_code = plaintext_code

        return result

    def get_active_access_codes(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[AccessCode]:
        """
        Retrieves a list of active (not used, not expired) access codes.
        """
        now = datetime.now(timezone.utc)
        statement = (
            select(self.model)
            .where(self.model.is_used.is_(False))
            .where(self.model.expires_at > now)
            .offset(skip)
            .limit(limit)
        )
        result = db.execute(statement)
        return result.scalars().all()

    def get_by_id(self, db: Session, *, code_id: uuid.UUID) -> Optional[AccessCode]:
        """
        Retrieves an access code by its ID.
        """
        statement = select(self.model).where(self.model.id == code_id)
        result = db.execute(statement)
        return result.scalar_one_or_none()

    def get_by_email(self, db: Session, *, email: str) -> list[AccessCode]:
        """
        Retrieves all access codes associated with a specific email.
        """
        statement = select(self.model).where(self.model.email == email)
        result = db.execute(statement)
        return result.scalars().all()

    def update(
        self, db: Session, *, db_obj: AccessCode, obj_in: AccessCodeUpdate
    ) -> AccessCode:
        """
        Updates an existing access code.
        'db_obj' should be the SQLAlchemy model instance fetched from the DB.
        """
        # Use the parent class update method
        return super().update(db, db_obj=db_obj, obj_in=obj_in)

    def remove(self, db: Session, *, id: uuid.UUID) -> Optional[AccessCode]:
        """
        Deletes an access code by its ID.
        Returns the deleted object or None if not found.
        NOTE: Consider if codes should be truly deleted or just marked inactive/expired.
              True deletion might hinder auditing.
        """
        obj = self.get_by_id(db, code_id=id)
        if obj:
            db.delete(obj)
            db.commit()
            return obj
        return None

    def verify_and_get_access_code(
        self, db: Session, *, plaintext_code: str
    ) -> Optional[AccessCode]:
        """
        Retrieves an access code by verifying the plaintext against stored hashes.
        This replaces the original get_access_code_by_value function.

        Args:
            db: The database session
            plaintext_code: The plaintext access code to verify

        Returns:
            The matching AccessCode object or None if no match is found
        """
        # Get all active codes
        now = datetime.now(timezone.utc)
        statement = (
            select(self.model)
            .where(self.model.is_used.is_(False))
            .where(self.model.expires_at > now)
        )
        result = db.execute(statement)
        active_codes = result.scalars().all()

        # Check each active code to see if the plaintext matches
        for code in active_codes:
            if verify_access_code(
                plaintext_code, code.code
            ):  # code.code contains the hash
                return code

        # No matching code found
        return None

    def consume_access_code(
        self, db: Session, *, code_value: str, patient_id: uuid.UUID
    ) -> AccessCode:
        """
        Validates and marks an access code as used, associating it with the patient.
        Uses the hashing verification system to find the matching code.
        Returns the updated code object containing the email and clinician_id.

        Args:
            db: The database session.
            code_value: The plaintext access code string to consume.
            patient_id: The UUID of the patient using the code.

        Raises:
            AccessCodeNotFoundError: If the code does not exist.
            AccessCodeExpiredError: If the code has expired.
            AccessCodeUsedError: If the code has already been used.
            AccessCodeError: For other validation or update errors.
        """
        # Find the code by verifying against all active codes
        db_code = self.verify_and_get_access_code(db, plaintext_code=code_value)

        if not db_code:
            raise AccessCodeNotFoundError("Access code not found or invalid.")

        if db_code.is_used:
            # Code already used, raise error.
            raise AccessCodeUsedError("Access code has already been used.")

        # Ensure expires_at is timezone-aware for comparison
        expires_at_aware = db_code.expires_at
        if expires_at_aware.tzinfo is None:
            expires_at_aware = expires_at_aware.replace(tzinfo=timezone.utc)

        now_aware = datetime.now(timezone.utc)

        if expires_at_aware < now_aware:
            raise AccessCodeExpiredError("Access code has expired.")

        # Mark the code as used and associate the patient_id
        update_data = AccessCodeUpdate(is_used=True, patient_id=patient_id)
        try:
            # Use the existing update function for consistency
            updated_code = self.update(db=db, db_obj=db_code, obj_in=update_data)
            return updated_code
        except Exception as e:
            db.rollback()
            raise AccessCodeError(f"Failed to consume access code: {e}")

    def get_access_code_by_value(
        self, db: Session, *, code_value: str
    ) -> Optional[AccessCode]:
        """
        DEPRECATED: Use verify_and_get_access_code instead for hashed code comparison.

        This method directly searches for a code value in the database.
        Since codes are now hashed, direct comparison won't work.
        For compatibility, this now calls verify_and_get_access_code.
        """
        return self.verify_and_get_access_code(db, plaintext_code=code_value)

    def get_count(self, db: Session) -> int:
        """
        Get the total count of all access codes (used or unused, expired or not).
        """
        count = db.scalar(select(func.count(self.model.id)))
        return count or 0

    def get_active_access_codes_count(self, db: Session) -> int:
        """
        Get the total count of active (not used, not expired) access codes.
        """
        now = datetime.now(timezone.utc)
        statement = (
            select(func.count(self.model.id))
            .where(self.model.is_used.is_(False))
            .where(self.model.expires_at > now)
        )
        count = db.scalar(statement)
        return count or 0

    def get_used_access_codes_count(self, db: Session) -> int:
        """
        Get the total count of used access codes.
        """
        statement = select(func.count(self.model.id)).where(
            self.model.is_used.is_(True)
        )
        count = db.scalar(statement)
        return count or 0

    def get_expired_access_codes_count(self, db: Session) -> int:
        """
        Get the total count of expired access codes (that were not used).
        """
        now = datetime.now(timezone.utc)
        statement = (
            select(func.count(self.model.id))
            .where(self.model.is_used.is_(False))
            .where(self.model.expires_at <= now)
        )
        count = db.scalar(statement)
        return count or 0


# Create an instance of CRUDAccessCode
access_code = CRUDAccessCode(AccessCode)

# Export methods at module level for backward compatibility
create_access_code = access_code.create
get_active_access_codes = access_code.get_active_access_codes
get_access_code_by_id = access_code.get_by_id
get_access_codes_by_email = access_code.get_by_email
update_access_code = access_code.update
delete_access_code = access_code.remove
verify_and_get_access_code = access_code.verify_and_get_access_code
consume_access_code = access_code.consume_access_code
get_access_code_by_value = access_code.get_access_code_by_value
get_count = access_code.get_count
get_active_access_codes_count = access_code.get_active_access_codes_count
get_used_access_codes_count = access_code.get_used_access_codes_count
get_expired_access_codes_count = access_code.get_expired_access_codes_count
