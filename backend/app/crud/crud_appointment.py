"""CRUD operations for appointments."""

import uuid
from datetime import datetime, timezone
from typing import Any, Optional

from sqlalchemy.orm import Session, selectinload

from app.core.constants.appointment import VALID_STATUS_TRANSITIONS
from app.crud.base import CRUDBase
from app.crud.exceptions import AppointmentNotFoundError, AppointmentStatusError
from app.models.appointment import Appointment
from app.schemas.appointment import AppointmentCreate, AppointmentUpdate
from app import crud
from app.schemas.event_log import EventLogCreate


class CRUDAppointment(CRUDBase[Appointment, AppointmentCreate, AppointmentUpdate]):
    def get_by_patient_id(
        self,
        db: Session,
        *,
        patient_id: str,  # Clerk ID
    ) -> list[Appointment]:
        """Get all appointments for a patient with eager loading of related entities."""
        return (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.desc())
            .all()
        )

    def get_by_clinician_id(
        self,
        db: Session,
        *,
        clinician_id: str,  # Clerk ID
    ) -> list[Appointment]:
        """Get all appointments for a clinician with eager loading of related entities."""
        return (
            db.query(self.model)
            .filter(self.model.clinician_id == clinician_id)
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.desc())
            .all()
        )

    def get_upcoming_by_patient_id(
        self,
        db: Session,
        *,
        patient_id: str,  # Clerk ID
        from_datetime: Optional[datetime] = None,
        limit: Optional[int] = None,
    ) -> list[Appointment]:
        """Get upcoming appointments for a patient with eager loading, optionally limited."""
        if from_datetime is None:
            from_datetime = datetime.now(timezone.utc)

        query = (
            db.query(self.model)
            .filter(
                self.model.patient_id == patient_id,
                self.model.appointment_datetime >= from_datetime,
                self.model.status.in_(["scheduled", "Scheduled"]),
            )
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.asc())
        )

        if limit is not None and limit > 0:
            query = query.limit(limit)

        return query.all()

    def get_upcoming_by_clinician_id(
        self,
        db: Session,
        *,
        clinician_id: str,  # Clerk ID
        from_datetime: Optional[datetime] = None,
    ) -> list[Appointment]:
        """Get upcoming appointments for a clinician with eager loading of related entities."""
        if from_datetime is None:
            from_datetime = datetime.now(timezone.utc)

        return (
            db.query(self.model)
            .filter(
                self.model.clinician_id == clinician_id,
                self.model.appointment_datetime >= from_datetime,
                self.model.status.in_(["scheduled", "Scheduled"]),
            )
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.asc())
            .all()
        )

    def cancel_appointment(
        self,
        db: Session,
        *,
        appointment_id: uuid.UUID,
        cancelled_by_id: str,  # Clerk ID
        reason: Optional[str] = None,
    ) -> Appointment:
        """Cancel an appointment."""
        appointment = self.get(db=db, id=appointment_id)
        if not appointment:
            raise AppointmentNotFoundError(f"Appointment {appointment_id} not found")

        if "cancelled" not in VALID_STATUS_TRANSITIONS.get(appointment.status, []):
            raise AppointmentStatusError(
                f"Cannot transition from {appointment.status} to cancelled"
            )

        update_data = {
            "status": "cancelled",
            "cancelled_at": datetime.now(timezone.utc),
            "cancelled_by_id": cancelled_by_id,
            "cancellation_reason": reason,
        }

        return self.update(db=db, db_obj=appointment, obj_in=update_data)

    def get_todays_appointments_by_clinician(
        self,
        db: Session,
        *,
        clinician_id: str,  # Clerk ID
        skip: int = 0,
        limit: int = 100,
    ) -> list[Appointment]:
        """Get today's appointments for a specific clinician with eager loading of related entities."""
        today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start.replace(
            hour=23, minute=59, second=59, microsecond=999999
        )

        return (
            db.query(self.model)
            .filter(
                self.model.clinician_id == clinician_id,
                self.model.appointment_datetime >= today_start,
                self.model.appointment_datetime <= today_end,
            )
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    # Override base methods to include eager loading
    def get(self, db: Session, id: Any) -> Optional[Appointment]:
        """Get a single appointment by ID with eager loading of related entities."""
        return (
            db.query(self.model)
            .filter(self.model.id == id)
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .first()
        )

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Appointment]:
        """Get multiple appointments with eager loading of related entities."""
        return (
            db.query(self.model)
            .options(
                selectinload(self.model.patient), selectinload(self.model.clinician)
            )
            .order_by(self.model.appointment_datetime.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_with_ids(self, db: Session, *, obj_in: AppointmentCreate) -> Appointment:
        """
        Create an appointment using AppointmentCreate schema.
        """
        obj_in_data = obj_in.model_dump()
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # Create event log for appointment
        try:
            event_log_data = EventLogCreate(
                action="CREATE",
                actor_user_id=obj_in.patient_id,
                actor_role="patient",
                target_resource_type="appointment",
                target_resource_id=str(db_obj.id),
                status="SUCCESS",
                outcome="SUCCESS",
                details={
                    "appointment_type": db_obj.appointment_type,
                    "status": db_obj.status,
                    "appointment_datetime": db_obj.appointment_datetime.isoformat(),
                    "duration_minutes": db_obj.duration_minutes,
                    "reason": db_obj.reason if db_obj.reason else "",
                    "actor_name": "Patient"
                }
            )
            
            crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=obj_in.patient_id,
                actor_role="patient"
            )
        except Exception as e:
            # Log error but don't fail the appointment creation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create event log for appointment: {str(e)}", exc_info=True)
        
        return db_obj


# Create an instance of CRUDAppointment to expose methods
_instance = CRUDAppointment(Appointment)

# Export methods at module level
get_by_patient_id = _instance.get_by_patient_id
get_by_clinician_id = _instance.get_by_clinician_id
get_upcoming_by_patient_id = _instance.get_upcoming_by_patient_id
get_upcoming_by_clinician_id = _instance.get_upcoming_by_clinician_id
cancel_appointment = _instance.cancel_appointment
get_todays_appointments_by_clinician = _instance.get_todays_appointments_by_clinician
get = _instance.get
get_multi = _instance.get_multi
create = _instance.create
update = _instance.update
remove = _instance.remove
# Create an instance of CRUDAppointment
appointment = CRUDAppointment(Appointment)
create_with_ids = _instance.create_with_ids
