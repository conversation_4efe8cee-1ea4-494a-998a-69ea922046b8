from datetime import datetime
from typing import Optional
from uuid import UUID

from fastapi.encoders import jsonable_encoder
from sqlalchemy import func, select
from sqlalchemy.orm import Session, selectinload

from app.crud.base import CRUDBase
from app.models.appointment_request import AppointmentRequest
from app.models.clinician import clinician_patient_association  # Added for join
from app.models.patient import Patient  # Added for join
from app.schemas.appointment_request import (
    AppointmentRequestCreate,
    AppointmentRequestStatusUpdate,
    AppointmentRequestUpdate,
)


class CRUDAppointmentRequest(
    CRUDBase[AppointmentRequest, AppointmentRequestCreate, AppointmentRequestUpdate]
):
    def create(
        self, db: Session, *, obj_in: AppointmentRequestCreate
    ) -> AppointmentRequest:
        """Create a new appointment request"""
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = AppointmentRequest(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_id_with_relations(
        self, db: Session, *, id: UUID, patient_id: Optional[str] = None
    ) -> Optional[AppointmentRequest]:
        """Get appointment request by ID with related entities loaded"""
        query = (
            select(AppointmentRequest)
            .options(
                selectinload(AppointmentRequest.patient),
                selectinload(AppointmentRequest.reviewed_by),
            )
            .where(AppointmentRequest.id == id)
        )

        # If patient_id provided, ensure it matches for security
        if patient_id:
            query = query.where(AppointmentRequest.patient_id == patient_id)

        return db.execute(query).scalars().first()

    def get_pending_for_clinic(
        self, db: Session, *, clinic_id: UUID, skip: int = 0, limit: int = 100
    ) -> list[AppointmentRequest]:
        """Get pending appointment requests for a specific clinic"""
        return (
            db.query(AppointmentRequest)
            .join(AppointmentRequest.patient)
            .filter(AppointmentRequest.status == "pending")
            .filter(AppointmentRequest.patient.has(associated_clinic_id=clinic_id))
            .options(
                selectinload(AppointmentRequest.patient),
                selectinload(AppointmentRequest.reviewed_by),
            )
            .order_by(AppointmentRequest.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_patient_id(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[AppointmentRequest]:
        """Get appointment requests for a specific patient"""
        return (
            db.query(AppointmentRequest)
            .filter(AppointmentRequest.patient_id == patient_id)
            .options(
                selectinload(AppointmentRequest.patient),
                selectinload(AppointmentRequest.reviewed_by),
            )
            .order_by(AppointmentRequest.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status(
        self,
        db: Session,
        *,
        db_obj: AppointmentRequest,
        obj_in: AppointmentRequestStatusUpdate
    ) -> AppointmentRequest:
        """Update appointment request status"""
        update_data = obj_in.model_dump(exclude_unset=True)

        # Set reviewed_at when status changes from pending
        if db_obj.status == "pending" and "status" in update_data:
            update_data["reviewed_at"] = datetime.utcnow()

        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def get_pending_request_count_for_clinician(
        self, db: Session, *, clinician_id: str
    ) -> int:
        """
        Counts the number of pending appointment requests for patients associated with a specific clinician.
        """
        count = (
            db.query(func.count(self.model.id))
            .join(Patient, self.model.patient_id == Patient.id)
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
            .filter(self.model.status == "pending")
            .scalar()
        )
        return count or 0

    def get_by_clinician(
        self, db: Session, *, clinician_id: str, skip: int = 0, limit: int = 100
    ) -> list[AppointmentRequest]:
        """Get appointment requests for patients assigned to a specific clinician"""
        return (
            db.query(AppointmentRequest)
            .join(Patient, AppointmentRequest.patient_id == Patient.id)
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
            .options(
                selectinload(AppointmentRequest.patient),
                selectinload(AppointmentRequest.reviewed_by),
            )
            .order_by(AppointmentRequest.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_patient(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[AppointmentRequest]:
        """Get appointment requests for a specific patient"""
        return (
            db.query(AppointmentRequest)
            .filter(AppointmentRequest.patient_id == patient_id)
            .options(
                selectinload(AppointmentRequest.patient),
                selectinload(AppointmentRequest.reviewed_by),
            )
            .order_by(AppointmentRequest.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status_by_id(
        self,
        db: Session,
        *,
        request_id: UUID,
        status_update: AppointmentRequestStatusUpdate
    ) -> AppointmentRequest:
        """Update appointment request status by ID"""
        db_obj = self.get(db, id=request_id)
        if not db_obj:
            raise ValueError(f"AppointmentRequest with id {request_id} not found")
        
        return self.update_status(db, db_obj=db_obj, obj_in=status_update)


appointment_request = CRUDAppointmentRequest(AppointmentRequest)
