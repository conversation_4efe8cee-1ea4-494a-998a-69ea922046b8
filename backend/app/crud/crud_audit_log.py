from typing import Optional

from sqlalchemy import and_, desc, or_, select
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.audit_log import AuditLog
from app.models.clinician import (
    clinician_patient_association,
)
from app.schemas.audit_log import (
    AuditLogCreate,
    AuditLogUpdate,
)

# Note: The primary way to create logs is via the utility function:
# from app.utils.audit import log_audit_event
# This CRUD file provides basic direct access if needed.


class CRUDAuditLog(CRUDBase[AuditLog, AuditLogCreate, AuditLogUpdate]):
    """CRUD operations for AuditLog model."""

    def create(self, db: Session, *, obj_in: AuditLogCreate) -> AuditLog:
        """
        Creates a new audit log entry directly.
        Prefer using the log_audit_event utility for consistency.
        """
        db_obj = AuditLog(**obj_in.model_dump())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        actor_user_id: Optional[str] = None,
        action: Optional[str] = None,
        outcome: Optional[str] = None,
        target_resource_type: Optional[str] = None,
        target_resource_id: Optional[str] = None,
    ) -> list[AuditLog]:
        """
        Retrieves a list of audit logs with optional filtering and pagination.
        """
        query = db.query(self.model)

        if actor_user_id:
            # Assuming actor_user_id is stored as string representation of UUID
            query = query.filter(self.model.actor_user_id == str(actor_user_id))
        if action:
            query = query.filter(
                self.model.action.ilike(f"%{action}%")
            )  # Case-insensitive search
        if outcome:
            query = query.filter(self.model.outcome == outcome)
        if target_resource_type:
            query = query.filter(
                self.model.target_resource_type == target_resource_type
            )
        if target_resource_id:
            # Assuming target_resource_id is stored as string representation of UUID
            query = query.filter(
                self.model.target_resource_id == str(target_resource_id)
            )

        return (
            query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
        )

    def get_for_clinician(
        self,
        db: Session,
        clinician_id: str,
        *,
        patient_id: Optional[str] = None,
        event_type: Optional[
            str
        ] = None,  # Renamed 'action' to 'event_type' for clarity
        skip: int = 0,
        limit: int = 100,
    ) -> list[AuditLog]:
        """
        Retrieves audit logs relevant to a clinician, including their own actions
        and actions performed on their associated patients.

        Args:
            db: The database session.
            clinician_id: The ID of the clinician.
            patient_id: Optional ID to filter logs for a specific patient.
            event_type: Optional string to filter by event type (action).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of AuditLog ORM objects.
        """
        # 1. Get IDs of patients associated with the clinician
        associated_patient_ids_query = select(
            clinician_patient_association.c.patient_id
        ).where(clinician_patient_association.c.clinician_id == clinician_id)
        associated_patient_ids = [
            row[0] for row in db.execute(associated_patient_ids_query).fetchall()
        ]
        # Convert UUIDs to strings if stored as strings in AuditLog
        associated_patient_ids_str = [str(pid) for pid in associated_patient_ids]

        # 2. Build the base query
        query = db.query(self.model)

        # 3. Apply core filtering logic (Clinician's actions OR actions on their patients)
        clinician_actions = self.model.actor_user_id == clinician_id
        patient_actions = and_(
            self.model.target_resource_type == "Patient",
            self.model.target_resource_id.in_(associated_patient_ids_str),
        )
        query = query.filter(or_(clinician_actions, patient_actions))

        # 4. Apply optional patient_id filter
        if patient_id:
            # Filter further: either clinician acted on this patient OR it's an action on this patient
            # This needs to be applied to the already filtered results based on clinician/associated patients
            clinician_on_patient = and_(
                self.model.actor_user_id == clinician_id,
                self.model.target_resource_id == patient_id,
                # No need to check target_resource_type again if actor is clinician
            )
            action_on_patient = and_(
                self.model.target_resource_type
                == "Patient",  # Ensure it's a patient action
                self.model.target_resource_id == patient_id,
            )
            query = query.filter(or_(clinician_on_patient, action_on_patient))

        # 5. Apply optional event_type filter
        if event_type:
            # Assuming event_type corresponds to the 'action' field
            query = query.filter(self.model.action.ilike(f"%{event_type}%"))

        # 6. Apply ordering and pagination
        return (
            query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
        )


# Create an instance of CRUDAuditLog
audit_log = CRUDAuditLog(AuditLog)

# Export methods at module level for backward compatibility
create_audit_log = audit_log.create
get_audit_logs = audit_log.get_multi
get_audit_logs_for_clinician = audit_log.get_for_clinician

# Add other CRUD functions (get_by_id, etc.) if needed in the future.
