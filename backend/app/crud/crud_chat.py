import logging
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy import desc
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.chat_message import ChatMessage
from app.schemas.chat import ChatMessageCreateInternal, ChatMessageUpdate

logger = logging.getLogger(__name__)


class CRUDChatMessage(
    CRUDBase[ChatMessage, ChatMessageCreateInternal, ChatMessageUpdate]
):
    """CRUD operations for ChatMessage model."""

    def create(self, db: Session, *, obj_in: ChatMessageCreateInternal) -> ChatMessage:
        """
        Creates a new chat message using the internal schema.

        Args:
            db: The database session
            obj_in: Pydantic schema with chat message data

        Returns:
            The newly created ChatMessage ORM object
        """
        logger.debug(
            f"CRUD: Creating chat message for patient {obj_in.patient_id} from {obj_in.sender_type}"
        )
        db_obj = ChatMessage(**obj_in.model_dump())
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating chat message for patient {obj_in.patient_id}",
                exc_info=True,
            )
            raise e
        db.refresh(db_obj)
        logger.info(f"CRUD: Chat message {db_obj.id} created successfully.")
        return db_obj

    def get_by_id(self, db: Session, *, id: UUID) -> Optional[ChatMessage]:
        """
        Get a chat message by its UUID primary key.

        Args:
            db: The database session
            id: The UUID of the chat message

        Returns:
            The ChatMessage ORM object or None if not found
        """
        return db.query(self.model).filter(self.model.id == id).first()

    def get_by_patient(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[ChatMessage]:
        """
        Retrieves chat messages for a specific patient, ordered by creation time descending.

        Args:
            db: The database session
            patient_id: The ID of the patient
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            A list of ChatMessage ORM objects
        """
        logger.debug(
            f"CRUD: Fetching chat messages for patient {patient_id}, skip={skip}, limit={limit}"
        )
        return (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def remove(self, db: Session, *, id: str) -> Optional[ChatMessage]:
        """
        Delete a chat message by its ID.

        Args:
            db: The database session
            id: The ID of the chat message to delete

        Returns:
            The deleted ChatMessage ORM object or None if not found
        """
        obj = db.query(self.model).get(id)
        if obj:
            logger.debug(f"CRUD: Deleting chat message {id}")
            db.delete(obj)
            db.commit()
            return obj
        logger.warning(f"CRUD: Chat message {id} not found for deletion.")
        return None

    def update(
        self,
        db: Session,
        *,
        db_obj: ChatMessage,
        obj_in: Union[ChatMessageUpdate, dict[str, Any]],
    ) -> ChatMessage:
        """
        Updates an existing chat message record.

        Args:
            db: The database session
            db_obj: The existing ChatMessage ORM object to update
            obj_in: Pydantic schema or dictionary with update data

        Returns:
            The updated ChatMessage ORM object

        Raises:
            IntegrityError: If a database constraint is violated
        """
        logger.debug(f"CRUD: Updating chat message ID {db_obj.id}")
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(
                exclude_unset=True
            )  # Only update provided fields

        # Prevent changing immutable fields
        update_data.pop("patient_id", None)
        update_data.pop("sender_type", None)
        update_data.pop("created_at", None)

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Error updating chat message {db_obj.id}", exc_info=True)
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Updated chat message ID {db_obj.id}")
        return db_obj


# Create an instance of CRUDChatMessage
chat_message = CRUDChatMessage(ChatMessage)

# Export methods at module level for backward compatibility
create_chat_message = chat_message.create
get_chat_message = chat_message.get_by_id
get_chat_messages_by_patient = chat_message.get_by_patient
remove_chat_message = chat_message.remove
update_chat_message = chat_message.update

# Note: The old placeholder functions (process_chat_message, save_conversation_turn)
# and related exceptions have been correctly moved to the service layer
# (app/services/chat_agent.py) or handled in the API endpoint.
