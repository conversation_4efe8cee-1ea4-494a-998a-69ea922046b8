import logging
from datetime import datetime, timezone  # Added for timestamping read status
from typing import Optional
from uuid import UUID

from sqlalchemy import func  # Added for count query
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload

from app.crud.base import CRUDBase

# Import models and schemas
from app.models.chat_message import ChatMessage, MessageSenderType
from app.models.clinician import clinician_patient_association  # Added for join
from app.models.patient import Patient  # Added for join
from app.schemas.chat import (
    ChatMessageCreateInternal,
    ChatMessageUpdate,
)  # Use the internal schema for creation
from app import crud
from app.schemas.event_log import EventLogCreate

logger = logging.getLogger(__name__)

# --- CRUD Class for ChatMessage ---


class CRUDChatMessage(
    CRUDBase[ChatMessage, ChatMessageCreateInternal, ChatMessageUpdate]
):
    """CRUD operations for ChatMessage model."""

    def create(self, db: Session, *, obj_in: ChatMessageCreateInternal) -> ChatMessage:
        """
        Creates a new chat message record in the database.

        Args:
            db: The database session.
            obj_in: Pydantic schema with chat message data matching the model
                    (patient_id, sender_type, message_content).

        Returns:
            The newly created ChatMessage ORM object.
        """
        # Convert the obj_in to a dict with JSON mode to ensure enum values are serialized correctly
        obj_data = obj_in.model_dump(mode="json", exclude_none=True)

        # Log the sender type value safely
        sender_type_value = obj_data.get("sender_type")
        message_route_value = obj_data.get("message_route")
        logger.info(
            f"CRUD: Creating chat message for patient {obj_in.patient_id} from sender {sender_type_value} with route {message_route_value}"
        )

        # Log the complete object data for debugging
        logger.info(f"CRUD: Full message data before DB insertion: {obj_data}")

        # Create ORM object from the modified dict
        db_obj = ChatMessage(**obj_data)
        db.add(db_obj)
        try:
            logger.info(
                f"CRUD: Committing transaction for chat message for patient {obj_in.patient_id}"
            )
            db.commit()
            logger.info("CRUD: Transaction committed successfully")
        except IntegrityError as e:  # Catch potential DB errors like FK violation
            db.rollback()
            logger.error(
                f"CRUD: IntegrityError creating chat message for patient {obj_in.patient_id}: {str(e)}",
                exc_info=True,
            )
            raise e  # Re-raise or handle appropriately
        except Exception as e:  # Catch any other database errors
            db.rollback()
            logger.error(
                f"CRUD: Unexpected error creating chat message for patient {obj_in.patient_id}: {str(e)}",
                exc_info=True,
            )
            raise e

        db.refresh(db_obj)
        logger.info(
            f"CRUD: Successfully created chat message ID {db_obj.id} for patient {obj_in.patient_id} with route {message_route_value}"
        )

        # Create event log for chat message
        try:
            # Determine actor based on sender type
            if sender_type_value == "PATIENT":
                actor_id = obj_in.patient_id
                actor_role = "patient"
                actor_name = "Patient"
            elif sender_type_value == "CLINICIAN":
                # For clinician messages, we'd need the clinician ID
                # This might need to be passed in the obj_in
                actor_id = "system"  # Default for now
                actor_role = "clinician"
                actor_name = "Clinician"
            else:
                actor_id = "system"
                actor_role = "system"
                actor_name = "AI Assistant"
            
            event_log_data = EventLogCreate(
                action="CREATE",
                actor_user_id=actor_id,
                actor_role=actor_role,
                target_resource_type="chat_message",
                target_resource_id=str(db_obj.id),
                status="SUCCESS",
                outcome="SUCCESS",
                details={
                    "message_preview": db_obj.message_content[:100] if db_obj.message_content else "",
                    "sender_type": sender_type_value,
                    "message_route": message_route_value,
                    "actor_name": actor_name
                }
            )
            
            crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=actor_id,
                actor_role=actor_role
            )
            logger.info(f"CRUD: Created event log for chat message ID {db_obj.id}")
        except Exception as e:
            # Log error but don't fail the chat message creation
            logger.error(f"CRUD: Failed to create event log for chat message: {str(e)}", exc_info=True)

        return db_obj

    def get_by_id(self, db: Session, *, message_id: str) -> Optional[ChatMessage]:
        """
        Get a specific chat message by its UUID primary key.

        Args:
            db: The database session.
            message_id: The ID of the chat message.

        Returns:
            The ChatMessage ORM object or None if not found.
        """
        return db.query(self.model).filter(self.model.id == message_id).first()

    def get_by_patient_filtered(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[ChatMessage]:
        """
        Get patient messages excluding clinical notes and clinician conversations.
        This method is used when displaying chat history to patients.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose messages to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of ChatMessage ORM objects excluding clinical notes and clinician messages.
        """
        # Import MessageSenderType enum
        from app.models.chat_message import MessageSenderType
        
        # Ensure patient_id is a string
        patient_id_str = str(patient_id)

        logger.info(
            f"[CRUD] PATIENT FILTER START: Retrieving filtered messages for patient {patient_id_str}, skip={skip}, limit={limit}"
        )

        try:
            # Get messages excluding clinical notes AND clinician-AI conversations
            # We want to exclude messages where:
            # 1. sender_type is CLINICAL_NOTE
            # 2. Both sender is CLINICIAN and route is AI (clinician->AI)
            # 3. Both sender is AGENT and route is CLINICIAN (AI->clinician response)
            
            from sqlalchemy import and_, or_, not_
            
            # First get all messages with new ordering (newest first)
            all_messages = (
                db.query(self.model)
                .filter(self.model.patient_id == patient_id_str)
                .filter(self.model.sender_type != "CLINICAL_NOTE")  # Exclude clinical notes
                .options(joinedload(self.model.patient))
                .order_by(self.model.created_at.desc(), self.model.id.desc())
                .all()
            )
            
            # Filter out clinician-AI conversations and messages FROM clinician to patient
            filtered_messages = []
            for msg in all_messages:
                # Log every message for debugging
                logger.info(f"[FILTER DEBUG] Message {msg.id}: sender={msg.sender_type.value if hasattr(msg.sender_type, 'value') else msg.sender_type}, route={msg.message_route.value if msg.message_route and hasattr(msg.message_route, 'value') else msg.message_route}")
                
                # Skip clinician->AI messages (clinician consulting AI)
                if msg.sender_type.value == "CLINICIAN" and msg.message_route and msg.message_route.value == "ai":
                    logger.info(f"[FILTER] Filtering out clinician->AI message: {msg.id}")
                    continue
                    
                # Skip AI->clinician messages (AI responses to clinician queries)
                if msg.sender_type.value == "AGENT" and msg.message_route and msg.message_route.value == "clinician":
                    logger.info(f"[FILTER] Filtering out AI->clinician message: {msg.id}")
                    continue
                    
                # Keep patient-visible messages:
                # - Patient messages (sender=PATIENT) regardless of route
                # - AI responses to patients (sender=AGENT) with route='patient' or route='ai'  
                # - Clinician responses to patients (sender=CLINICIAN) with route='patient' or route='clinician'
                logger.info(f"[FILTER] Keeping message: {msg.id}")
                filtered_messages.append(msg)
            
            # Apply pagination to filtered results
            paginated_messages = filtered_messages[skip:skip + limit]
            
            # Reverse to maintain chronological order
            paginated_messages.reverse()
            
            messages = paginated_messages

            logger.debug(f"[CRUD] Retrieved {len(messages)} filtered messages")
            return messages
        except Exception as e:
            logger.error(
                f"[CRUD] Error retrieving filtered messages for patient {patient_id}: {str(e)}",
                exc_info=True,
            )
            return []

    def get_by_patient(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[ChatMessage]:
        """
        Get multiple chat messages associated with a specific patient, ordered by creation time.
        Only returns messages with message_route='patient' to exclude clinician-AI conversations.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose messages to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of ChatMessage ORM objects for the specified patient.
        """
        # Import MessageRouteType and MessageSenderType enums
        from app.models.chat_message import MessageRouteType, MessageSenderType
        
        # Ensure patient_id is a string to maintain consistent lookup behavior
        patient_id_str = str(patient_id)

        # Expire all cached entities to ensure we get fresh data from the database
        # This fixes issues where newly created messages don't appear in the results
        db.expire_all()

        # Log patient_id details to help debug type mismatches
        logger.info(
            f"[CRUD] Retrieving patient conversation messages for patient {patient_id_str} (type: {type(patient_id_str)}) with skip={skip}, limit={limit}"
        )

        try:
            # Flush pending changes to ensure everything is committed before querying
            db.flush()

            # Log the exact SQL query parameters
            logger.debug(
                f"[CRUD] Query parameters: patient_id={patient_id_str}, skip={skip}, limit={limit}"
            )

            # Count how many messages exist before applying limit/offset
            # Filter to include patient-originated conversations:
            # 1. Messages with message_route='patient' (patient-to-AI conversations)
            # 2. Messages with message_route='clinician' (patient-to-clinician conversations)
            # EXCLUDE: Only clinician-to-AI conversations (message_route='ai')
            total_count = (
                db.query(self.model)
                .filter(self.model.patient_id == patient_id_str)
                .filter(self.model.message_route.in_([MessageRouteType.PATIENT, MessageRouteType.CLINICIAN]))
                .count()
            )
            logger.debug(f"[CRUD] Total patient conversation message count before pagination: {total_count}")

            # Order by created_at ASC and id ASC to ensure consistent ordering
            messages = (
                db.query(self.model)
                .filter(self.model.patient_id == patient_id_str)
                .filter(self.model.message_route.in_([MessageRouteType.PATIENT, MessageRouteType.CLINICIAN]))
                .options(joinedload(self.model.patient))
                .order_by(self.model.created_at.asc(), self.model.id.asc())
                .offset(skip)
                .limit(limit)
                .all()
            )

            # Log message details for debugging
            logger.debug(f"[CRUD] Retrieved {len(messages)} messages from database")
            for i, msg in enumerate(messages):
                logger.debug(
                    f"[CRUD] Message {i+1}: ID={msg.id}, sender_type={msg.sender_type}, patient_id={msg.patient_id}, created_at={msg.created_at}"
                )

            for msg in messages:
                try:
                    _ = msg.id
                    _ = msg.sender_type.value
                    _ = msg.message_content
                    _ = msg.created_at
                    _ = msg.is_read_by_clinician
                    if hasattr(msg, "message_route") and msg.message_route is not None:
                        _ = msg.message_route.value
                    if (
                        hasattr(msg, "message_metadata")
                        and msg.message_metadata
                        and not isinstance(msg.message_metadata, dict)
                    ):
                        msg.message_metadata = {}
                except Exception as attr_error:
                    logger.error(
                        f"[CRUD] Error accessing message attributes for message {msg.id}: {str(attr_error)}"
                    )

            logger.debug(
                f"[CRUD] Successfully retrieved {len(messages)} messages for patient {patient_id}."
            )
            return messages
        except Exception as e:
            logger.error(
                f"[CRUD] Error retrieving chat messages for patient {patient_id}: {str(e)}",
                exc_info=True,
            )
            return []

    def get_all_messages_for_patient(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[ChatMessage]:
        """
        Get ALL chat messages associated with a specific patient, including clinician-AI conversations.
        This method is intended for clinician use to see the complete conversation history.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose messages to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of ChatMessage ORM objects for the specified patient.
        """
        # Import MessageRouteType and MessageSenderType enums
        from app.models.chat_message import MessageRouteType, MessageSenderType
        
        # Ensure patient_id is a string to maintain consistent lookup behavior
        patient_id_str = str(patient_id)

        # Expire all cached entities to ensure we get fresh data from the database
        db.expire_all()

        logger.info(
            f"[CRUD] Retrieving ALL messages (including clinician-AI) for patient {patient_id_str} with skip={skip}, limit={limit}"
        )

        try:
            # Flush pending changes to ensure everything is committed before querying
            db.flush()

            # Count how many messages exist before applying limit/offset
            total_count = (
                db.query(self.model)
                .filter(self.model.patient_id == patient_id_str)
                .count()
            )
            logger.debug(f"[CRUD] Total message count (all types) before pagination: {total_count}")

            # Get ALL messages for this patient, regardless of message_route
            # Order by created_at DESC and id DESC to get newest messages first
            # when messages have the same timestamp
            messages = (
                db.query(self.model)
                .filter(self.model.patient_id == patient_id_str)
                .options(joinedload(self.model.patient))
                .order_by(self.model.created_at.desc(), self.model.id.desc())
                .offset(skip)
                .limit(limit)
                .all()
            )
            
            # Reverse the list to maintain chronological order (oldest to newest)
            # This ensures newest messages are fetched but displayed in correct order
            messages.reverse()

            logger.debug(f"[CRUD] Retrieved {len(messages)} messages from database (all types)")
            for i, msg in enumerate(messages):
                logger.debug(
                    f"[CRUD] Message {i+1}: ID={msg.id}, sender_type={msg.sender_type}, route={msg.message_route if hasattr(msg, 'message_route') else 'N/A'}, created_at={msg.created_at}"
                )

            return messages
        except Exception as e:
            logger.error(
                f"[CRUD] Error retrieving all messages for patient {patient_id}: {str(e)}",
                exc_info=True,
            )
            return []

    def get_all_messages_count_for_patient(self, db: Session, *, patient_id: str) -> int:
        """
        Get the count of ALL chat messages associated with a specific patient,
        including clinician-AI conversations.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose messages to count.

        Returns:
            The count of all ChatMessage records for the specified patient.
        """
        try:
            logger.info(f"Counting ALL messages for patient {patient_id}")
            count = (
                db.query(func.count(self.model.id))
                .filter(self.model.patient_id == patient_id)
                .scalar()
                or 0
            )
            logger.info(f"Found {count} total messages (all types) for patient {patient_id}")
            return count
        except Exception as e:
            logger.error(
                f"Error counting all messages for patient {patient_id}: {str(e)}",
                exc_info=True,
            )
            return 0

    def get_count_by_patient(self, db: Session, *, patient_id: str) -> int:
        """
        Get the count of chat messages associated with a specific patient.
        Excludes clinical notes and clinician-AI conversations.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose messages to count.

        Returns:
            The count of ChatMessage records for the specified patient.
        """
        # Import MessageRouteType and MessageSenderType enums
        from app.models.chat_message import MessageRouteType, MessageSenderType
        
        try:
            # Start a clean transaction
            db.begin_nested()

            try:
                logger.info(f"Counting patient-visible messages for patient {patient_id}")
                
                # Get all messages for this patient excluding clinical notes
                all_messages = (
                    db.query(self.model)
                    .filter(self.model.patient_id == patient_id)
                    .filter(self.model.sender_type != "CLINICAL_NOTE")
                    .all()
                )
                
                # Count only messages that should be visible to patients
                count = 0
                for msg in all_messages:
                    # Skip clinician->AI messages
                    if msg.sender_type.value == "CLINICIAN" and msg.message_route and msg.message_route.value == "ai":
                        continue
                    # Skip any message FROM clinician
                    if msg.sender_type.value == "CLINICIAN":
                        continue
                    # Skip AI->clinician messages (AI responses to clinician queries)
                    if msg.sender_type.value == "AGENT" and msg.message_route and msg.message_route.value == "clinician":
                        continue
                    # Count patient messages and AI responses to patient messages
                    count += 1
                logger.info(f"Found {count} patient conversation messages for patient {patient_id}")
                db.commit()
                return count

            except Exception as query_error:
                # Rollback this transaction
                db.rollback()
                logger.error(
                    f"Error counting messages for patient {patient_id}: {str(query_error)}",
                    exc_info=True,
                )
                raise query_error

        except Exception as e:
            logger.error(
                f"Error in get_count_by_patient for patient {patient_id}: {str(e)}",
                exc_info=True,
            )

            # Try with a fresh session as a last resort
            try:
                # Create a fresh session
                from sqlalchemy import create_engine
                from sqlalchemy.orm import sessionmaker

                from app.core.config import settings

                logger.info("Attempting count with fresh session")

                # Create a new engine and session
                temp_engine = create_engine(settings.DATABASE_URL)
                TempSessionLocal = sessionmaker(
                    autocommit=False, autoflush=False, bind=temp_engine
                )
                temp_db = TempSessionLocal()

                try:
                    # Retry the count with fresh session
                    fresh_count = (
                        temp_db.query(func.count(self.model.id))
                        .filter(self.model.patient_id == patient_id)
                        .filter(
                            (self.model.message_route == MessageRouteType.PATIENT) |
                            (self.model.message_route.is_(None))  # Include legacy messages without route
                        )
                        .scalar()
                        or 0
                    )

                    logger.info(f"Fresh session count: {fresh_count}")
                    return fresh_count
                except Exception as fresh_error:
                    logger.error(
                        f"Fresh session count failed: {str(fresh_error)}", exc_info=True
                    )
                    return 0
                finally:
                    temp_db.close()

            except Exception as recovery_error:
                logger.error(
                    f"Recovery attempt failed in get_count_by_patient: {str(recovery_error)}",
                    exc_info=True,
                )
                return 0  # Default to 0 on error

    def delete(self, db: Session, *, message_id: UUID) -> Optional[ChatMessage]:
        """
        Delete a chat message by its UUID (Hard Delete).
        Consider implications for conversation history and compliance.

        Args:
            db: The database session.
            message_id: The UUID of the message to delete.

        Returns:
            The deleted ChatMessage ORM object or None if not found.
        """
        message = db.query(self.model).filter(self.model.id == message_id).first()
        if not message:
            logger.warning(f"CRUD: Chat message {message_id} not found for deletion.")
            return None

        db.delete(message)
        try:
            db.commit()
            logger.info(f"CRUD: Deleted chat message {message_id}")
            return message
        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting chat message {message_id}", exc_info=True)
            raise e

    def mark_as_read_by_clinician(
        self, db: Session, *, message_id: str
    ) -> Optional[ChatMessage]:
        """
        Marks a specific patient-sent chat message as read by a clinician.

        Args:
            db: The database session.
            message_id: The ID of the chat message to mark as read.

        Returns:
            The updated ChatMessage ORM object or None if not found or not a patient message.
        """
        message = db.query(self.model).filter(self.model.id == message_id).first()
        if not message:
            logger.warning(f"CRUD: Message {message_id} not found to mark as read.")
            return None

        if message.sender_type != MessageSenderType.PATIENT:
            logger.warning(
                f"CRUD: Message {message_id} was not sent by patient, cannot mark as read by clinician."
            )
            return None  # Or maybe return the message without changes?

        if message.is_read_by_clinician:
            logger.debug(
                f"CRUD: Message {message_id} already marked as read by clinician."
            )
            return message  # Already read, return as is

        message.is_read_by_clinician = True
        message.read_by_clinician_at = datetime.now(timezone.utc)
        db.add(message)
        try:
            db.commit()
            db.refresh(message)
            logger.debug(f"CRUD: Marked message {message_id} as read by clinician.")
            return message
        except Exception as e:
            db.rollback()
            logger.error(f"Error marking message {message_id} as read", exc_info=True)
            raise e

    def get_unread_count_for_clinician(self, db: Session, *, clinician_id: str) -> int:
        """
        Counts the number of conversations where patients are awaiting responses from the clinician.
        Uses the same logic as /chat/stats to ensure consistency.

        Args:
            db: The database session.
            clinician_id: The ID of the clinician.

        Returns:
            The count of conversations awaiting responses from the clinician.
        """
        logger.debug(
            f"CRUD: Counting conversations awaiting responses for clinician {clinician_id}"
        )
        
        try:
            # Get all conversations for this clinician
            conversations = self.get_conversations_for_clinician(
                db=db,
                clinician_id=clinician_id,
                skip=0,
                limit=1000  # Get all conversations
            )
            
            pending_responses = 0
            
            # For each conversation, check if awaiting response using same logic as /chat/stats
            for patient, last_message_at in conversations:
                # Get the most recent patient-visible messages for this patient
                recent_messages = self.get_by_patient_filtered(
                    db=db,
                    patient_id=patient.id,
                    skip=0,
                    limit=5  # Get last few messages to check conversation state
                )
                
                if recent_messages:
                    # Sort by created_at descending to get the most recent first
                    recent_messages.sort(key=lambda x: x.created_at, reverse=True)
                    last_visible_message = recent_messages[0]
                    
                    # Check if last visible message is from patient (awaiting response)
                    if last_visible_message.sender_type == MessageSenderType.PATIENT:
                        pending_responses += 1
            
            logger.debug(
                f"CRUD: Found {pending_responses} conversations awaiting responses for clinician {clinician_id}"
            )
            return pending_responses
            
        except Exception as e:
            logger.error(
                f"CRUD: Error counting conversations awaiting responses for clinician {clinician_id}: {str(e)}",
                exc_info=True,
            )
            return 0

    def get_conversations_for_clinician(
        self, db: Session, *, clinician_id: UUID, skip: int = 0, limit: int = 100
    ) -> list[tuple[Patient, Optional[datetime]]]:
        """
        Gets a list of patients associated with a clinician who have chat messages,
        along with the timestamp of their last message.
        Orders by the most recent last message.

        Args:
            db: The database session.
            clinician_id: The UUID of the clinician.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of tuples, each containing (Patient object, last_message_timestamp).
        """
        # Subquery to get the latest message timestamp for each patient
        latest_message_subquery = (
            db.query(
                self.model.patient_id,
                func.max(self.model.created_at).label("last_message_at"),
            )
            .group_by(self.model.patient_id)
            .subquery("latest_messages")
        )

        # Query to get patients associated with the clinician who have chat messages
        query = (
            db.query(Patient, latest_message_subquery.c.last_message_at)
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .join(
                latest_message_subquery,
                Patient.id == latest_message_subquery.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
            .order_by(
                latest_message_subquery.c.last_message_at.desc().nullslast()
            )  # Handle cases where last_message_at might be null
            .offset(skip)
            .limit(limit)
        )

        results = query.all()
        return results

    def get_unread_message_count_for_patient_by_clinician(
        self, db: Session, *, clinician_id: UUID, patient_id: UUID
    ) -> int:
        """
        Counts unread messages from a specific patient that are meant for a specific clinician.
        This assumes that messages from a patient are implicitly for any clinician assigned to them.
        The `is_read_by_clinician` flag on the ChatMessage model is generic.
        If a more direct clinician-message link or read status per clinician is needed,
        the data model would need to change.

        For now, this counts messages from `patient_id` where `sender_type` is PATIENT
        and `is_read_by_clinician` is False. The `clinician_id` is used here primarily
        to ensure the request is authorized (which should be handled at the API layer)
        but the query itself doesn't strictly need clinician_id if we assume
        `is_read_by_clinician` is a global flag for "any clinician has read it".

        If the intent is "unread by THIS specific clinician", the model needs adjustment.
        Assuming the current model: counts patient messages not yet marked as read by *any* clinician.
        """
        # First, verify the clinician is actually associated with the patient.
        # This check should ideally also be in the API layer for security.
        is_associated = (
            db.query(clinician_patient_association)
            .filter(
                clinician_patient_association.c.clinician_id == clinician_id,
                clinician_patient_association.c.patient_id == patient_id,
            )
            .first()
        )
        if not is_associated:
            logger.warning(
                f"Clinician {clinician_id} is not associated with patient {patient_id}. Cannot get unread count."
            )
            return 0  # Or raise an error

        count = (
            db.query(func.count(self.model.id))
            .filter(self.model.patient_id == patient_id)
            .filter(self.model.sender_type == MessageSenderType.PATIENT)
            .filter(self.model.is_read_by_clinician.is_(False))
            .scalar()
        )
        return count or 0


# Create an instance of CRUDChatMessage
chat_message = CRUDChatMessage(ChatMessage)

# Export all CRUD methods at module level for backward compatibility and pattern compliance
create_chat_message = chat_message.create
get_chat_message_by_id = chat_message.get_by_id
get_chat_messages_by_patient = chat_message.get_by_patient
get_chat_messages_by_patient_filtered = chat_message.get_by_patient_filtered  # New filtered method
get_chat_message_count_by_patient = chat_message.get_count_by_patient
delete_chat_message = chat_message.delete
mark_message_as_read_by_clinician = chat_message.mark_as_read_by_clinician
get_unread_patient_message_count_for_clinician = (
    chat_message.get_unread_count_for_clinician
)
get_conversations_for_clinician = (
    chat_message.get_conversations_for_clinician
)  # Export new method
get_unread_message_count_for_patient_by_clinician = (
    chat_message.get_unread_message_count_for_patient_by_clinician
)  # Export new method


# Placeholder for the agent processing logic previously in crud_chat.py
# This should likely move to a separate service/utility module.
class AgentProcessingError(Exception):
    pass


# Function moved to services/chat_agent.py - keeping this comment for reference
# This avoids naming conflicts between the two implementations
# def process_chat_message(user_id: UUID, user_message: str) -> str:
#     """
#     (Simulated) Processes the user's message and generates a response.
#     Placeholder - Replace with actual AI agent interaction logic.
#     """
#     logger.info(f"AGENT SIM: Processing message from {user_id}: '{user_message}'")
#     # Simulate some basic response generation
#     if "hello" in user_message.lower():
#         return "Hello there! How can I help you with your treatment today?"
#     elif "weight" in user_message.lower():
#         return "Tracking your weight is important. Have you logged your weight this week using the app?"
#     elif "side effect" in user_message.lower():
#         return (
#             "For reporting side effects, please use the 'Report Side Effect' feature "
#             "in the app to provide details so your clinician can review it."
#         )
#     else:
#         return (
#             "Thank you for your message. I am an AI assistant and will do my "
#             "best to help or forward your message to your care team if needed."
#         )
# Placeholder for saving conversation turn previously in crud_chat.py
# This logic is now handled by create_chat_message above.
# def save_conversation_turn(user_id: UUID, user_message: str, agent_response: str):
#     """(Simulated) Saves the user message and agent response."""
#     print(f"DB SIM: Saving turn for {user_id}: User='{user_message[:50]}...', Agent='{agent_response[:50]}...'")
#     # In real app, call create_chat_message twice (once for user, once for agent)
#     pass
