# backend/app/crud/crud_clinic.py
import logging
import uuid
from typing import Any, Optional

from sqlalchemy import func, select
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.clinic import Clinic
from app.models.scraped_page import ScrapedPage
from app.schemas.clinic import ClinicCreate, ClinicUpdate

logger = logging.getLogger(__name__)


class CRUDClinic(CRUDBase[Clinic, ClinicCreate, ClinicUpdate]):
    """CRUD operations for Clinic model."""

    def get(self, db: Session, id: uuid.UUID) -> Optional[Clinic]:
        """
        Get a clinic by its ID.
        """
        logger.debug(f"CRUD Clinic: Getting clinic by ID {id}")
        clinic = db.query(self.model).filter(self.model.id == id).first()
        if clinic:
            # Fetch the latest scraped_at timestamp for this clinic
            latest_scrape_query = select(func.max(ScrapedPage.scraped_at)).where(
                ScrapedPage.clinic_id == id
            )
            latest_scrape_timestamp = db.scalar(latest_scrape_query)
            # Assign to a dynamic attribute (Pydantic model will pick it up if defined)
            clinic.last_scraped_at = latest_scrape_timestamp
        else:
            logger.debug(f"CRUD Clinic: Clinic with ID {id} not found.")
        return clinic

    def create(self, db: Session, *, obj_in: ClinicCreate) -> Clinic:
        """
        Create a new clinic.
        """
        logger.debug(f"CRUD Clinic: Creating new clinic with name {obj_in.name}")
        db_obj = Clinic(
            name=obj_in.name,
            address=obj_in.address,
            website_url=str(obj_in.website_url) if obj_in.website_url else None,
            # scraped_data is typically added later, not during initial creation
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        logger.info(f"CRUD Clinic: Created clinic with ID {db_obj.id}")
        return db_obj

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Clinic]:
        """
        Retrieve multiple clinics with pagination.
        """
        logger.debug(
            f"CRUD Clinic: Getting multiple clinics (skip={skip}, limit={limit})"
        )
        clinics = db.query(self.model).offset(skip).limit(limit).all()
        # Add last_scraped_at to each clinic
        for clinic in clinics:
            latest_scrape_query = select(func.max(ScrapedPage.scraped_at)).where(
                ScrapedPage.clinic_id == clinic.id
            )
            latest_scrape_timestamp = db.scalar(latest_scrape_query)
            clinic.last_scraped_at = latest_scrape_timestamp
        logger.debug(f"CRUD Clinic: Found {len(clinics)} clinics.")
        return clinics

    def get_count(self, db: Session) -> int:
        """
        Get the total count of clinics.
        """
        logger.debug("CRUD Clinic: Getting total clinic count")
        count = db.scalar(select(func.count(self.model.id)))
        logger.debug(f"CRUD Clinic: Total count is {count}")
        return count or 0

    def update(
        self, db: Session, *, db_obj: Clinic, obj_in: ClinicUpdate | dict[str, Any]
    ) -> Clinic:
        """
        Update an existing clinic.
        """
        logger.debug(f"CRUD Clinic: Updating clinic with ID {db_obj.id}")
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # Iterate over the update data and set attributes on the db_obj
        for field, value in update_data.items():
            # Special handling for URL to ensure it's stored as string or None
            if field == "website_url" and value is not None:
                setattr(db_obj, field, str(value))
            elif hasattr(db_obj, field):
                setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        logger.info(f"CRUD Clinic: Updated clinic with ID {db_obj.id}")
        return db_obj

    def remove(self, db: Session, *, id: uuid.UUID) -> Optional[Clinic]:
        """
        Delete a clinic by its ID.
        """
        logger.debug(f"CRUD Clinic: Deleting clinic with ID {id}")
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
            logger.info(f"CRUD Clinic: Deleted clinic with ID {id}")
            return obj
        else:
            logger.warning(f"CRUD Clinic: Clinic with ID {id} not found for deletion.")
            return None


# Create an instance of CRUDClinic
clinic = CRUDClinic(Clinic)

# Export methods at module level for backward compatibility
get = clinic.get
create = clinic.create
get_multi = clinic.get_multi
get_count = clinic.get_count
update = clinic.update
remove = clinic.remove
