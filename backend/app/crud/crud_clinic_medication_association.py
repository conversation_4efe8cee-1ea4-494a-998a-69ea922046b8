from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import (  # If needed for related data, maybe not here
    Session,
)

from app.crud.base import CRUDBase
from app.models.clinic_medication_association import (
    ClinicMedicationAssociation,
)
from app.schemas.clinic_medication_association import (
    ClinicMedicationAssociationCreate,
    ClinicMedicationAssociationUpdate,
)

# Import other CRUD instances if needed for checks (assuming they are in __init__)
from . import crud_clinic, crud_medication
from .exceptions import ConflictError, NotFoundError  # Import from sibling module


class CRUDClinicMedicationAssociation(
    CRUDBase[
        ClinicMedicationAssociation,
        ClinicMedicationAssociationCreate,
        ClinicMedicationAssociationUpdate,
    ]
):  # Use the imported Model class
    async def create_association(
        self, db: AsyncSession, *, clinic_id: UUID, medication_id: UUID
    ) -> ClinicMedicationAssociation:
        """
        Creates an association between a clinic and a medication.

        Args:
            db: The database session.
            clinic_id: The ID of the clinic.
            medication_id: The ID of the medication.

        Returns:
            The created ClinicMedicationAssociation object.

        Raises:
            NotFoundError: If the clinic or medication does not exist.
            ConflictError: If the association already exists.
        """
        # 1. Check if Clinic exists
        clinic = await crud_clinic.clinic.get(db=db, id=clinic_id)
        if not clinic:
            raise NotFoundError(f"Clinic with id {clinic_id} not found.")

        # 2. Check if Medication exists
        medication = await crud_medication.medication.get(db=db, id=medication_id)
        if not medication:
            raise NotFoundError(f"Medication with id {medication_id} not found.")

        # 3. Check if association already exists
        existing_association_stmt = select(self.model).where(
            self.model.clinic_id == clinic_id, self.model.medication_id == medication_id
        )
        result = await db.execute(existing_association_stmt)
        existing_association = result.scalars().first()

        if existing_association:
            raise ConflictError(
                f"Association between Clinic {clinic_id} and Medication {medication_id} already exists."
            )

        # 4. Create the association object
        association_in = ClinicMedicationAssociationCreate(
            clinic_id=clinic_id, medication_id=medication_id
        )

        # 5. Use CRUDBase's create method
        # Note: CRUDBase might need adjustment if it doesn't directly support composite keys
        # or if specific logic is needed beyond simple creation.
        # Assuming CRUDBase.create takes the schema object.
        return await super().create(db=db, obj_in=association_in)


# Instantiate the CRUD class for dependency injection
clinic_medication_association = CRUDClinicMedicationAssociation(
    ClinicMedicationAssociation
)


# Add synchronous versions of the methods
def get_by_clinic_and_medication(
    db: Session, clinic_id: UUID, medication_id: UUID
) -> ClinicMedicationAssociation:
    """Synchronous version of get_by_clinic_and_medication"""
    return (
        db.query(ClinicMedicationAssociation)
        .filter(
            ClinicMedicationAssociation.clinic_id == clinic_id,
            ClinicMedicationAssociation.medication_id == medication_id,
        )
        .first()
    )


def create_association(
    db: Session, clinic_id: UUID, medication_id: UUID, notes: str = None
) -> ClinicMedicationAssociation:
    """Synchronous version of create_association"""
    # 1. Check if Clinic exists
    clinic = crud_clinic.clinic.get(db=db, id=clinic_id)
    if not clinic:
        raise NotFoundError(f"Clinic with id {clinic_id} not found.")

    # 2. Check if Medication exists
    medication = crud_medication.medication.get(db=db, id=medication_id)
    if not medication:
        raise NotFoundError(f"Medication with id {medication_id} not found.")

    # 3. Check if association already exists
    existing_association = get_by_clinic_and_medication(db, clinic_id, medication_id)
    if existing_association:
        return existing_association

    # 4. Create the association object
    association_in = ClinicMedicationAssociationCreate(
        clinic_id=clinic_id, medication_id=medication_id, notes=notes
    )

    # 5. Create the association directly
    obj_in_data = association_in.dict()
    db_obj = ClinicMedicationAssociation(**obj_in_data)
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def remove_association(db: Session, clinic_id: UUID, medication_id: UUID) -> bool:
    """Synchronous version to remove an association"""
    association = get_by_clinic_and_medication(
        db, clinic_id=clinic_id, medication_id=medication_id
    )
    if association:
        db.delete(association)
        db.commit()
        return True
    return False


# Add the synchronous methods to the instance
clinic_medication_association.get_by_clinic_and_medication = (
    get_by_clinic_and_medication
)
clinic_medication_association.create_association = create_association
clinic_medication_association.remove_association = remove_association
