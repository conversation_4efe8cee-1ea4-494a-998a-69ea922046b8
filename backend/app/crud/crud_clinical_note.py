"""CRUD operations for Clinical Notes."""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, or_
from sqlalchemy.orm import Session, joinedload

from app.crud.base import CRUDBase
from app.models.clinical_note import ClinicalNote, ClinicalNoteStatus
from app.schemas.clinical_note import ClinicalNoteCreate, ClinicalNoteUpdate

logger = logging.getLogger(__name__)


class CRUDClinicalNote(CRUDBase[ClinicalNote, ClinicalNoteCreate, ClinicalNoteUpdate]):
    """CRUD operations for ClinicalNote model."""

    def create_with_clinician(
        self, db: Session, *, obj_in: ClinicalNoteCreate, clinician_id: str
    ) -> ClinicalNote:
        """
        Create a new clinical note.
        
        Args:
            db: Database session
            obj_in: Clinical note creation data
            clinician_id: ID of the clinician creating the note
            
        Returns:
            Created clinical note
        """
        logger.debug(
            f"Creating clinical note for patient {obj_in.patient_id} by clinician {clinician_id}"
        )
        
        # Convert Pydantic model to dict and handle JSONB fields
        obj_data = obj_in.model_dump(exclude_unset=True)
        # Remove status if it exists to ensure we use the correct value
        obj_data.pop('status', None)
        
        # Create object with explicit field assignments to avoid enum issues
        db_obj = ClinicalNote(
            patient_id=obj_data['patient_id'],
            clinician_id=clinician_id,
            appointment_id=obj_data.get('appointment_id'),
            chat_session_id=obj_data.get('chat_session_id'),
            template_id=obj_data.get('template_id'),
            note_type=obj_data['note_type'],
            sections=obj_data['sections'],
            raw_text=obj_data['raw_text'],
            ai_generated=True,
            ai_confidence_score=obj_data.get('ai_confidence_score'),
            suggested_icd10_codes=obj_data.get('suggested_icd10_codes'),
            suggested_cpt_codes=obj_data.get('suggested_cpt_codes'),
            billing_notes=obj_data.get('billing_notes'),
            status="draft"  # Use string directly
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        logger.info(f"Created clinical note {db_obj.id} for patient {obj_in.patient_id}")
        return db_obj

    def get_by_patient(
        self, 
        db: Session, 
        patient_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ClinicalNoteStatus] = None,
        include_relations: bool = False
    ) -> List[ClinicalNote]:
        """
        Get clinical notes for a patient.
        
        Args:
            db: Database session
            patient_id: Patient ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Filter by note status
            include_relations: Whether to include related data
            
        Returns:
            List of clinical notes
        """
        query = db.query(self.model).filter(self.model.patient_id == patient_id)
        
        if status:
            query = query.filter(self.model.status == status)
            
        if include_relations:
            query = query.options(
                joinedload(self.model.patient),
                joinedload(self.model.clinician),
                joinedload(self.model.appointment),
                joinedload(self.model.template)
            )
            
        return (
            query.order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_clinician(
        self,
        db: Session,
        clinician_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ClinicalNoteStatus] = None
    ) -> List[ClinicalNote]:
        """
        Get clinical notes authored by a clinician.
        
        Args:
            db: Database session
            clinician_id: Clinician ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Filter by note status
            
        Returns:
            List of clinical notes
        """
        query = db.query(self.model).filter(self.model.clinician_id == clinician_id)
        
        if status:
            query = query.filter(self.model.status == status)
            
        return (
            query.order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_appointment(
        self, db: Session, appointment_id: UUID
    ) -> Optional[ClinicalNote]:
        """
        Get clinical note for an appointment.
        
        Args:
            db: Database session
            appointment_id: Appointment ID
            
        Returns:
            Clinical note if found
        """
        return (
            db.query(self.model)
            .filter(self.model.appointment_id == appointment_id)
            .order_by(desc(self.model.created_at))
            .first()
        )

    def get_by_chat_session(
        self, db: Session, chat_session_id: UUID
    ) -> List[ClinicalNote]:
        """
        Get clinical notes generated from a chat session.
        
        Args:
            db: Database session
            chat_session_id: Chat session ID
            
        Returns:
            List of clinical notes
        """
        return (
            db.query(self.model)
            .filter(self.model.chat_session_id == chat_session_id)
            .order_by(desc(self.model.created_at))
            .all()
        )

    def update(
        self,
        db: Session,
        *,
        db_obj: ClinicalNote,
        obj_in: ClinicalNoteUpdate | Dict[str, Any]
    ) -> ClinicalNote:
        """
        Update a clinical note.
        
        Args:
            db: Database session
            db_obj: Existing clinical note
            obj_in: Update data
            
        Returns:
            Updated clinical note
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
            
        # Track human edits if sections are being updated
        if "sections" in update_data and db_obj.ai_generated:
            if not db_obj.human_edits:
                db_obj.human_edits = {}
            db_obj.human_edits["sections_edited"] = True
            db_obj.human_edits["edited_at"] = datetime.utcnow().isoformat()
            
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def approve(
        self,
        db: Session,
        *,
        db_obj: ClinicalNote,
        approver_id: str
    ) -> ClinicalNote:
        """
        Approve a clinical note.
        
        Args:
            db: Database session
            db_obj: Clinical note to approve
            approver_id: ID of the approving clinician
            
        Returns:
            Approved clinical note
        """
        db_obj.status = ClinicalNoteStatus.APPROVED.value  # Use the string value
        db_obj.approved_by = approver_id
        db_obj.approved_at = datetime.utcnow()
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        logger.info(f"Clinical note {db_obj.id} approved by {approver_id}")
        return db_obj

    def search(
        self,
        db: Session,
        *,
        clinician_id: Optional[str] = None,
        patient_id: Optional[str] = None,
        status: Optional[ClinicalNoteStatus] = None,
        note_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ClinicalNote]:
        """
        Search clinical notes with multiple filters.
        
        Args:
            db: Database session
            clinician_id: Filter by clinician
            patient_id: Filter by patient
            status: Filter by status
            note_type: Filter by note type
            start_date: Filter by creation date (start)
            end_date: Filter by creation date (end)
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of matching clinical notes
        """
        query = db.query(self.model)
        
        if clinician_id:
            query = query.filter(self.model.clinician_id == clinician_id)
        if patient_id:
            query = query.filter(self.model.patient_id == patient_id)
        if status:
            query = query.filter(self.model.status == status)
        if note_type:
            query = query.filter(self.model.note_type == note_type)
        if start_date:
            query = query.filter(self.model.created_at >= start_date)
        if end_date:
            query = query.filter(self.model.created_at <= end_date)
            
        return (
            query.order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_draft_notes_by_clinician(
        self, db: Session, clinician_id: str
    ) -> List[ClinicalNote]:
        """
        Get all draft notes for a clinician.
        
        Args:
            db: Database session
            clinician_id: Clinician ID
            
        Returns:
            List of draft clinical notes
        """
        return (
            db.query(self.model)
            .filter(
                and_(
                    self.model.clinician_id == clinician_id,
                    self.model.status == ClinicalNoteStatus.DRAFT.value
                )
            )
            .order_by(desc(self.model.created_at))
            .all()
        )


# Create instance
clinical_note = CRUDClinicalNote(ClinicalNote)