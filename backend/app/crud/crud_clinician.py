# backend/app/crud/crud_clinician.py
import logging
import uuid
from io import BytesIO  # Added for blob download
from typing import Any, Optional, Union
from uuid import UUID

from azure.core.exceptions import (  # Added for blob download
    AzureError,
    ResourceNotFoundError,
)
from azure.storage.blob import BlobClient  # Added for blob download
from fastapi import HTTPException, status
from sqlalchemy import func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, selectinload  # Import selectinload
from starlette.concurrency import run_in_threadpool  # Added for async wrapper

from app.core.config import settings  # Added for blob download
from app.core.storage import blob_service_client  # Added for blob download
from app.crud.base import CRUDBase
from app.models.clinic import Clinic
from app.models.clinician import (
    Clinician,
    clinician_clinic_association,
    clinician_patient_association,
)
from app.models.patient import Patient
from app.schemas.clinic import ClinicRead
from app.schemas.clinician import (
    Clinician as ClinicianSchema,
)
from app.schemas.clinician import ClinicianCreate, ClinicianUpdate

logger = logging.getLogger(__name__)


class CRUDClinician(CRUDBase[Clinician, ClinicianCreate, ClinicianUpdate]):
    """CRUD operations for Clinician model."""

    def create(
        self, db: Session, *, obj_in: Union[ClinicianCreate, dict[str, Any]]
    ) -> Clinician:
        """
        Create a new clinician.
        """
        if isinstance(obj_in, dict):
            create_data = obj_in
        else:
            create_data = obj_in.model_dump(exclude_unset=True)

        clinician_id = uuid.uuid4()

        db_obj = Clinician(
            id=clinician_id,
            email=create_data["email"],
            first_name=create_data["first_name"],
            last_name=create_data["last_name"],
            specialty=create_data.get("specialty"),
            is_active=create_data.get("is_active", True),
            clerk_id=create_data.get("clerk_id"),
        )

        try:
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            logger.debug(
                f"CRUD: Created clinician {clinician_id} with email {create_data['email']}"
            )
            return db_obj
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Error creating clinician: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A clinician with this email or Clerk ID already exists.",
            )

    def get_by_id(self, db: Session, clinician_id: str) -> Optional[Clinician]:
        """
        Retrieve a clinician by their ID.
        """
        return db.query(self.model).filter(self.model.id == clinician_id).first()

    def get_by_id_with_clinic(
        self, db: Session, clinician_id: str
    ) -> Optional[dict[str, Any]]:
        """
        Retrieve a single clinician by their ID, including their first associated clinic.
        Returns data structured according to the Clinician Pydantic schema.
        """
        try:
            query = (
                select(self.model, Clinic)
                .outerjoin(
                    clinician_clinic_association,
                    self.model.id == clinician_clinic_association.c.clinician_id,
                )
                .outerjoin(
                    Clinic, clinician_clinic_association.c.clinic_id == Clinic.id
                )
                .where(self.model.id == clinician_id)
            )
            result = db.execute(query).first()  # Fetch the first result

            if not result:
                return None

            clinician_orm, clinic_orm = result
            clinic_data = ClinicRead.model_validate(clinic_orm) if clinic_orm else None

            # Construct dictionary matching Clinician schema
            clinician_data = {
                "id": clinician_orm.id,
                "email": clinician_orm.email,
                "first_name": clinician_orm.first_name,
                "last_name": clinician_orm.last_name,
                "specialty": clinician_orm.specialty,
                "is_active": clinician_orm.is_active,
                "clerk_id": clinician_orm.clerk_id,
                "assigned_clinic": clinic_data,
            }
            # Validate and return (or just return the dict if validation isn't strictly needed here)
            return ClinicianSchema.model_validate(clinician_data).model_dump()

        except Exception as e:
            logger.error(
                f"Error retrieving clinician {clinician_id} with clinic: {e}",
                exc_info=True,
            )
            # Let the caller handle the exception/HTTP response
            return None

    def get_by_email(self, db: Session, email: str) -> Optional[Clinician]:
        """
        Retrieve a clinician by their email address.
        """
        return db.query(self.model).filter(self.model.email == email).first()

    def get_by_clerk_id(self, db: Session, clerk_id: str) -> Optional[Clinician]:
        """
        Retrieve a clinician by their Clerk ID.
        """
        return (
            db.query(self.model)
            .options(selectinload(self.model.clinics))  # Eager load clinics
            .filter(self.model.clerk_id == clerk_id)
            .first()
        )

    def get_multi(
        self, db: Session, skip: int = 0, limit: int = 100
    ) -> list[Clinician]:
        """
        Get multiple clinicians with pagination.
        """
        return db.query(self.model).offset(skip).limit(limit).all()

    def get_count(self, db: Session) -> int:
        """
        Get the total count of clinicians.
        """
        count = db.scalar(select(func.count(self.model.id)))
        return count or 0

    def get_active_with_clinic(
        self, db: Session, skip: int = 0, limit: int = 100
    ) -> dict[str, Any]:
        """
        Get multiple active clinicians with pagination, including their first associated clinic.
        Returns data structured according to ClinicianListResponse schema.
        """
        try:
            # Query for active clinicians and their associated clinic
            query = (
                select(self.model, Clinic)
                .outerjoin(
                    clinician_clinic_association,
                    self.model.id == clinician_clinic_association.c.clinician_id,
                )
                .outerjoin(
                    Clinic, clinician_clinic_association.c.clinic_id == Clinic.id
                )
                .where(self.model.is_active.is_(True))
                .order_by(self.model.last_name, self.model.first_name)
                .offset(skip)
                .limit(limit)
            )

            results = db.execute(query).all()

            # Get total count for pagination
            count_query = (
                select(func.count())
                .select_from(self.model)
                .where(self.model.is_active.is_(True))
            )
            total = db.scalar(count_query)

            # Process results into dictionaries matching the Clinician Pydantic schema
            clinician_items = []
            for idx, (clinician_orm, clinic_orm) in enumerate(results):
                try:
                    logger.debug(
                        f"[DEBUG] Processing clinician #{idx}: {clinician_orm}"
                    )
                    logger.debug(f"[DEBUG] Associated clinic: {clinic_orm}")
                    clinic_data = (
                        ClinicRead.model_validate(clinic_orm) if clinic_orm else None
                    )
                    clinician_data = {
                        "id": clinician_orm.id,
                        "email": clinician_orm.email,
                        "first_name": clinician_orm.first_name,
                        "last_name": clinician_orm.last_name,
                        "specialty": clinician_orm.specialty,
                        "is_active": clinician_orm.is_active,
                        "clerk_id": clinician_orm.clerk_id,
                        "assigned_clinic": clinic_data,
                    }
                    clinician_items.append(
                        ClinicianSchema.model_validate(clinician_data)
                    )
                except Exception as e:
                    logger.error(
                        f"[DEBUG] Error serializing clinician #{idx}: {clinician_orm}, clinic: {clinic_orm}, error: {e}",
                        exc_info=True,
                    )
                    raise

            logger.info(
                f"Retrieved {len(clinician_items)} active clinicians with clinic info (skip={skip}, limit={limit}, total={total})"
            )
            return {"items": clinician_items, "total": total}

        except Exception as e:
            logger.error(
                f"Error retrieving active clinicians with clinic: {e}", exc_info=True
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while retrieving clinician data.",
            ) from e

    def update(
        self,
        db: Session,
        *,
        db_obj: Clinician,
        obj_in: Union[ClinicianUpdate, dict[str, Any]],
    ) -> Clinician:
        """
        Update an existing clinician record.
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def remove(self, db: Session, *, clinician_id: str) -> Optional[Clinician]:
        """
        Delete a clinician by their UUID.
        Note: This performs a hard delete. Implement soft delete if required.
        """
        obj = self.get_by_id(db, clinician_id=clinician_id)
        if obj:
            db.delete(obj)
            db.commit()
            return obj
        return None

    def delete_clinician(self, db: Session, clinician_id: str) -> Optional[Clinician]:
        """
        Delete a clinician by their ID.
        Audit logs the deletion. RBAC and Clerk role checks must be enforced at the API/service layer due to signature constraints.
        """
        obj = self.get_by_id(db, clinician_id=clinician_id)
        if not obj:
            logger.warning(
                f"Attempted to delete non-existent clinician {clinician_id}."
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Clinician not found."
            )
        try:
            db.delete(obj)
            db.commit()
            logger.info(f"Clinician {clinician_id} deleted.")
            return obj
        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting clinician {clinician_id}: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while deleting the clinician.",
            )

    def create_from_clerk(self, db: Session, *, obj_in: ClinicianCreate) -> Clinician:
        """
        Create a new clinician using Clerk ID as the primary key.
        Expects obj_in.id to be the Clerk user ID.
        """
        try:
            # First check if a clinician with this Clerk ID already exists
            existing_clinician = self.get_by_clerk_id(db, clerk_id=obj_in.clerk_id)
            if existing_clinician:
                logger.warning(
                    f"Attempted to create clinician with existing Clerk ID: {obj_in.clerk_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="A clinician with this Clerk ID already exists.",
                )

            # Check if a clinician with this email already exists
            existing_email = self.get_by_email(db, email=obj_in.email)
            if existing_email:
                logger.warning(
                    f"Attempted to create clinician with existing email: {obj_in.email}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="A clinician with this email already exists.",
                )

            # Create the clinician with Clerk ID as the primary key
            db_obj = Clinician(
                id=obj_in.clerk_id,  # Use Clerk ID as the primary key
                email=obj_in.email,
                first_name=obj_in.first_name,
                last_name=obj_in.last_name,
                specialty=obj_in.specialty if hasattr(obj_in, "specialty") else None,
                is_active=True,  # Default to active
                clerk_id=obj_in.clerk_id,  # Also store in the clerk_id field for consistency
            )

            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)

            logger.info(
                f"Created clinician with Clerk ID {obj_in.clerk_id} and email {obj_in.email}"
            )

            # If clinic_id is provided, associate the clinician with the clinic
            if hasattr(obj_in, "clinic_id") and obj_in.clinic_id:
                success = self.add_to_clinic(
                    db, clinician_id=db_obj.id, clinic_id=obj_in.clinic_id
                )
                if not success:
                    logger.warning(
                        f"Failed to associate clinician {db_obj.id} with clinic {obj_in.clinic_id}"
                    )

            return db_obj

        except HTTPException:
            # Re-raise HTTP exceptions without wrapping
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating clinician from Clerk: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while creating the clinician.",
            ) from e

    # --- Relationship management methods ---

    def assign_patient(
        self, db: Session, *, clinician_id: str, patient_id: str
    ) -> bool:
        """
        Assign a patient to a clinician.
        Returns True if assignment was successful or already existed, False if clinician/patient not found.
        """
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(
                f"Cannot assign patient: Clinician {clinician_id} not found."
            )
            return False

        # Check if patient exists
        patient = db.query(Patient).filter(Patient.id == patient_id).first()
        if not patient:
            logger.warning(f"Cannot assign patient: Patient {patient_id} not found.")
            return False

        # Check if assignment already exists
        existing_assignment = (
            db.query(clinician_patient_association)
            .filter(
                clinician_patient_association.c.clinician_id == clinician_id,
                clinician_patient_association.c.patient_id == patient_id,
            )
            .first()
        )

        if existing_assignment:
            logger.debug(
                f"Patient {patient_id} already assigned to clinician {clinician_id}."
            )
            return True

        # Create the association
        try:
            stmt = clinician_patient_association.insert().values(
                clinician_id=clinician_id, patient_id=patient_id
            )
            db.execute(stmt)
            db.commit()
            logger.info(f"Assigned patient {patient_id} to clinician {clinician_id}.")
            return True
        except Exception as e:
            db.rollback()
            logger.error(f"Error assigning patient to clinician: {e}", exc_info=True)
            return False

    def remove_patient(
        self, db: Session, *, clinician_id: str, patient_id: str
    ) -> bool:
        """
        Remove a patient assignment from a clinician.
        Returns True if removal was successful, False if clinician/patient not found or not assigned.
        """
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(
                f"Cannot remove patient: Clinician {clinician_id} not found."
            )
            return False

        # Check if patient exists
        patient = db.query(Patient).filter(Patient.id == patient_id).first()
        if not patient:
            logger.warning(f"Cannot remove patient: Patient {patient_id} not found.")
            return False

        # Check if assignment exists
        existing_assignment = (
            db.query(clinician_patient_association)
            .filter(
                clinician_patient_association.c.clinician_id == clinician_id,
                clinician_patient_association.c.patient_id == patient_id,
            )
            .first()
        )

        if not existing_assignment:
            logger.debug(
                f"Patient {patient_id} not assigned to clinician {clinician_id}."
            )
            return False

        # Remove the association
        try:
            stmt = clinician_patient_association.delete().where(
                clinician_patient_association.c.clinician_id == clinician_id,
                clinician_patient_association.c.patient_id == patient_id,
            )
            db.execute(stmt)
            db.commit()
            logger.info(f"Removed patient {patient_id} from clinician {clinician_id}.")
            return True
        except Exception as e:
            db.rollback()
            logger.error(f"Error removing patient from clinician: {e}", exc_info=True)
            return False

    def get_patients(
        self,
        db: Session,
        *,
        clinician_id: str,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        sort_by: str = "last_name",
        sort_order: str = "asc",
    ) -> list[Patient]:
        """
        Get a paginated list of patients assigned to a specific clinician,
        with optional search and sorting.
        """
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(f"Cannot get patients: Clinician {clinician_id} not found.")
            return []

        # Start with a query for patients assigned to this clinician
        query = (
            db.query(Patient)
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
        )

        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Patient.first_name.ilike(search_term))
                | (Patient.last_name.ilike(search_term))
                | (Patient.email.ilike(search_term))
            )

        # Apply sorting
        if hasattr(Patient, sort_by):
            sort_attr = getattr(Patient, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(sort_attr.desc())
            else:
                query = query.order_by(sort_attr.asc())
        else:
            # Default sort by last_name if invalid sort_by provided
            query = query.order_by(Patient.last_name.asc())

        # Apply pagination
        return query.offset(skip).limit(limit).all()

    def get_patients_count(
        self, db: Session, *, clinician_id: str, search: Optional[str] = None
    ) -> int:
        """
        Get the total count of patients assigned to a specific clinician,
        with optional search filter.
        """
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(
                f"Cannot get patient count: Clinician {clinician_id} not found."
            )
            return 0

        # Start with a query for patients assigned to this clinician
        query = (
            db.query(func.count(Patient.id))
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
        )

        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Patient.first_name.ilike(search_term))
                | (Patient.last_name.ilike(search_term))
                | (Patient.email.ilike(search_term))
            )

        return query.scalar() or 0

    def is_patient_assigned(
        self, db: Session, *, clinician_id: str, patient_id: str
    ) -> bool:
        """
        Check if a specific patient is assigned to a specific clinician efficiently.
        """
        query = db.query(clinician_patient_association).filter(
            clinician_patient_association.c.clinician_id == clinician_id,
            clinician_patient_association.c.patient_id == patient_id,
        )
        return db.query(query.exists()).scalar()

    def add_to_clinic(self, db: Session, *, clinician_id: str, clinic_id: UUID) -> bool:
        """
        Associate a clinician with a clinic in the association table.
        Returns True if successful or already associated, False otherwise.
        """
        # Check if clinician exists
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(f"Cannot add to clinic: Clinician {clinician_id} not found.")
            return False

        # Check if clinic exists
        clinic = db.query(Clinic).filter(Clinic.id == clinic_id).first()
        if not clinic:
            logger.warning(f"Cannot add clinician: Clinic {clinic_id} not found.")
            return False

        # Check if association already exists
        existing_association = (
            db.query(clinician_clinic_association)
            .filter(
                clinician_clinic_association.c.clinician_id == clinician_id,
                clinician_clinic_association.c.clinic_id == clinic_id,
            )
            .first()
        )

        if existing_association:
            logger.debug(
                f"Clinician {clinician_id} already associated with clinic {clinic_id}."
            )
            return True

        # Create the association
        try:
            stmt = clinician_clinic_association.insert().values(
                clinician_id=clinician_id, clinic_id=clinic_id
            )
            db.execute(stmt)
            db.commit()
            logger.info(f"Associated clinician {clinician_id} with clinic {clinic_id}.")
            return True
        except Exception as e:
            db.rollback()
            logger.error(f"Error associating clinician with clinic: {e}", exc_info=True)
            return False

    def update_clinic_association(
        self, db: Session, *, clinician_id: str, new_clinic_id: Optional[UUID]
    ) -> bool:
        """
        Updates the clinic association for a clinician.
        Removes all existing associations and adds the new one if provided.
        Returns True if successful, False otherwise.
        """
        # Check if clinician exists
        clinician = self.get_by_clerk_id(db, clerk_id=clinician_id)
        if not clinician:
            logger.warning(
                f"Cannot update clinic association: Clinician {clinician_id} not found."
            )
            return False

        try:
            # Begin transaction
            # Remove all existing clinic associations for this clinician
            delete_stmt = clinician_clinic_association.delete().where(
                clinician_clinic_association.c.clinician_id == clinician_id
            )
            db.execute(delete_stmt)

            # If a new clinic ID is provided, add the new association
            if new_clinic_id:
                # Check if clinic exists
                clinic = db.query(Clinic).filter(Clinic.id == new_clinic_id).first()
                if not clinic:
                    logger.warning(
                        f"Cannot update association: Clinic {new_clinic_id} not found."
                    )
                    db.rollback()
                    return False

                # Add the new association
                insert_stmt = clinician_clinic_association.insert().values(
                    clinician_id=clinician_id, clinic_id=new_clinic_id
                )
                db.execute(insert_stmt)

            # Commit the transaction
            db.commit()
            if new_clinic_id:
                logger.info(
                    f"Updated clinic association for clinician {clinician_id} to clinic {new_clinic_id}."
                )
            else:
                logger.info(
                    f"Removed all clinic associations for clinician {clinician_id}."
                )
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating clinic association: {e}", exc_info=True)
            return False

    # --- RBAC / Multi-Tenancy specific methods ---

    def get_clinics(self, db: Session, *, clinician_id: str) -> list[Clinic]:
        """
        Retrieve a list of clinics associated with a specific clinician.
        """
        clinician = self.get_by_id(db, clinician_id=clinician_id)
        if not clinician:
            logger.warning(
                f"CRUD Clinician: Could not find clinician {clinician_id} to get clinics."
            )
            return []
        return clinician.clinics

    def is_associated_with_clinic(
        self, db: Session, *, clinician_id: str, clinic_id: UUID
    ) -> bool:
        """
        Check if a specific clinician is associated with a specific clinic efficiently.
        Assumes a many-to-many relationship via an association table (e.g., clinician_clinic_association).
        """
        try:
            from app.models.clinician import clinician_clinic_association
        except ImportError:
            logger.error(
                "Could not import clinician_clinic_association table for RBAC check."
            )
            return False

        query = db.query(clinician_clinic_association).filter(
            clinician_clinic_association.c.clinician_id == clinician_id,
            clinician_clinic_association.c.clinic_id == clinic_id,
        )
        return db.query(query.exists()).scalar()

    def get_with_clinic(
        self, db: Session, skip: int = 0, limit: int = 100
    ) -> dict[str, Any]:
        """
        Retrieve clinicians with their associated clinic information.
        Returns data structured according to ClinicianListResponse schema.
        """
        try:
            # Query for clinicians and their associated clinic
            query = (
                select(self.model, Clinic)
                .outerjoin(
                    clinician_clinic_association,
                    self.model.id == clinician_clinic_association.c.clinician_id,
                )
                .outerjoin(
                    Clinic, clinician_clinic_association.c.clinic_id == Clinic.id
                )
                .order_by(self.model.last_name, self.model.first_name)
                .offset(skip)
                .limit(limit)
            )

            results = db.execute(query).all()

            # Get total count for pagination
            count_query = select(func.count()).select_from(self.model)
            total = db.scalar(count_query)

            # Process results into dictionaries matching the Clinician Pydantic schema
            clinician_items = []
            for idx, (clinician_orm, clinic_orm) in enumerate(results):
                try:
                    logger.debug(
                        f"[DEBUG] Processing clinician #{idx}: {clinician_orm}"
                    )
                    logger.debug(f"[DEBUG] Associated clinic: {clinic_orm}")
                    clinic_data = (
                        ClinicRead.model_validate(clinic_orm) if clinic_orm else None
                    )
                    clinician_data = {
                        "id": clinician_orm.id,
                        "email": clinician_orm.email,
                        "first_name": clinician_orm.first_name,
                        "last_name": clinician_orm.last_name,
                        "specialty": clinician_orm.specialty,
                        "is_active": clinician_orm.is_active,
                        "clerk_id": clinician_orm.clerk_id,
                        "assigned_clinic": clinic_data,
                    }
                    clinician_items.append(
                        ClinicianSchema.model_validate(clinician_data)
                    )
                except Exception as e:
                    logger.error(
                        f"[DEBUG] Error serializing clinician #{idx}: {clinician_orm}, clinic: {clinic_orm}, error: {e}",
                        exc_info=True,
                    )
                    raise

            logger.info(
                f"Retrieved {len(clinician_items)} clinicians with clinic info (skip={skip}, limit={limit}, total={total})"
            )
            return {"items": clinician_items, "total": total}

        except Exception as e:
            logger.error(f"Error retrieving clinicians with clinic: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while retrieving clinician data.",
            ) from e

    def get_profile_photo_blob(
        self, db: Session, *, clinician_id: str, filename: str
    ) -> Optional[BytesIO]:  # Changed clinician_id type hint to str
        """
        Downloads the profile photo blob for a given clinician and filename from Azure Storage.

        Args:
            db: The database session.
            clinician_id: The UUID of the clinician.
            filename: The specific filename requested (should match the one in photo_url).

        Returns:
            A BytesIO stream containing the blob content, or None if not found or error occurs.
        """
        clinician = self.get_by_id(db, clinician_id=str(clinician_id))
        if not clinician:
            logger.warning(
                f"Clinician {clinician_id} not found when fetching photo blob."
            )
            return None

        if not clinician.photo_url:
            logger.warning(f"Clinician {clinician_id} has no photo_url set.")
            return None

        # Basic validation: Ensure the requested filename matches the one in the URL
        # Assumes URL structure like: .../profile_photos/clinician/{uuid}/{filename}
        try:
            expected_filename = clinician.photo_url.split("/")[-1]
            if filename != expected_filename:
                logger.error(
                    f"Requested filename '{filename}' does not match stored filename '{expected_filename}' for clinician {clinician_id}."
                )
                return None  # Or raise HTTPException(status.HTTP_403_FORBIDDEN)? Returning None seems safer.
        except IndexError:
            logger.error(
                f"Could not parse filename from photo_url '{clinician.photo_url}' for clinician {clinician_id}."
            )
            return None

        if not blob_service_client:
            logger.error(
                "Azure Blob Service Client not initialized. Cannot download photo."
            )
            return None

        blob_path = f"profile_photos/clinician/{clinician_id}/{filename}"
        logger.debug(
            f"Attempting to download blob: container='{settings.AZURE_STORAGE_CONTAINER_NAME}', path='{blob_path}'"
        )

        try:
            blob_client: BlobClient = blob_service_client.get_blob_client(
                container=settings.AZURE_STORAGE_CONTAINER_NAME, blob=blob_path
            )

            # Download the blob's content into memory
            downloader = blob_client.download_blob()
            blob_bytes = downloader.readall()
            logger.info(
                f"Successfully downloaded profile photo blob '{filename}' for clinician {clinician_id} ({len(blob_bytes)} bytes)."
            )
            return BytesIO(blob_bytes)

        except ResourceNotFoundError:
            logger.warning(f"Blob not found at path: {blob_path}")
            return None
        except AzureError as e:
            logger.error(
                f"Azure error downloading blob {blob_path}: {e}", exc_info=True
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error downloading blob {blob_path}: {e}", exc_info=True
            )
            return None


# Create and export the CRUD instance
clinician = CRUDClinician(Clinician)

# Export methods at module level for backward compatibility
create_clinician = clinician.create
get = clinician.get  # Add the missing get method export
get_clinician_by_id = clinician.get_by_id
get_clinician_by_id_with_clinic = clinician.get_by_id_with_clinic
get_clinician_by_email = clinician.get_by_email
get_clinician_by_clerk_id = clinician.get_by_clerk_id
get_clinicians = clinician.get_multi
get_count = clinician.get_count
get_active_clinicians_with_clinic = clinician.get_active_with_clinic
update_clinician = clinician.update
delete_clinician = clinician.remove
create_clinician_from_clerk = clinician.create_from_clerk
assign_patient_to_clinician = clinician.assign_patient
remove_patient_from_clinician = clinician.remove_patient
get_patients_for_clinician = clinician.get_patients
get_patients_count_for_clinician = clinician.get_patients_count
is_patient_assigned_to_clinician = clinician.is_patient_assigned
add_clinician_to_clinic = clinician.add_to_clinic
update_clinician_clinic_association = clinician.update_clinic_association
get_clinics_for_clinician = clinician.get_clinics
is_clinician_associated_with_clinic = clinician.is_associated_with_clinic
get_clinicians_with_clinic = clinician.get_with_clinic


# Async wrapper for use in async endpoints
async def is_patient_assigned_to_clinician_async(
    db: Session, *, clinician_id: str, patient_id: str
) -> bool:
    return await run_in_threadpool(
        clinician.is_patient_assigned,
        db=db,
        clinician_id=clinician_id,
        patient_id=patient_id,
    )
