import logging
import uuid
from typing import Any, Optional, Union

from sqlalchemy import select
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.content import Content
from app.schemas.content import ContentCreate, ContentUpdate

logger = logging.getLogger(__name__)

# --- Caching Potential ---
# get_content_by_id, get_all_content (especially with only_published=True): Content is read-heavy and changes infrequently.
# Strong candidates for caching. Invalidation required on create/update/delete.


class CRUDContent(CRUDBase[Content, ContentCreate, ContentUpdate]):
    """CRUD operations for Content model."""

    def create(self, db: Session, *, obj_in: ContentCreate) -> Content:
        """
        Creates a new content item in the database.
        """
        db_obj = Content(**obj_in.model_dump())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_id(self, db: Session, *, content_id: uuid.UUID) -> Optional[Content]:
        """
        Retrieves a specific content item by its ID.
        """
        statement = select(self.model).where(self.model.id == content_id)
        result = db.execute(statement)
        return result.scalar_one_or_none()

    def get_by_slug(self, db: Session, *, slug: str) -> Optional[Content]:
        """
        Retrieves content by a unique slug.
        NOTE: The 'slug' field is not currently defined in the Content model.
              This field needs to be added to the model for this function to work.
        """
        # Placeholder implementation - requires 'slug' field on Content model
        # statement = select(self.model).where(self.model.slug == slug)
        # result = db.execute(statement)
        # return result.scalar_one_or_none()
        logger.warning(
            "get_content_by_slug called, but 'slug' field missing from Content model."
        )
        return None  # Return None until slug field is implemented

    def get_all(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        only_published: bool = False
    ) -> list[Content]:
        """
        Retrieves a list of content items with pagination.
        Optionally filters for only published content.
        TODO: Add filtering by content_type, tags, etc. if needed.
        """
        statement = (
            select(self.model)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        if only_published:
            statement = statement.where(
                self.model.is_published.is_(True)
            )  # Corrected comparison
        result = db.execute(statement)
        return result.scalars().all()

    def update(
        self,
        db: Session,
        *,
        db_obj: Content,
        obj_in: Union[ContentUpdate, dict[str, Any]]
    ) -> Content:
        """
        Updates an existing content item.
        'db_obj' should be the SQLAlchemy model instance fetched from the DB.

        Args:
            db: The database session
            db_obj: The existing Content ORM object to update
            obj_in: Pydantic schema or dictionary with update data

        Returns:
            The updated Content ORM object
        """
        if isinstance(obj_in, dict):
            update_dict = obj_in
        else:
            update_dict = obj_in.model_dump(exclude_unset=True)

        if not update_dict:
            return db_obj  # No updates provided

        # Update the model instance directly
        for field, value in update_dict.items():
            setattr(db_obj, field, value)

        db.add(db_obj)  # Add the modified instance to the session
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, content_id: uuid.UUID) -> Optional[Content]:
        """
        Deletes a content item by its ID. Returns the deleted object or None if not found.
        """
        db_obj = self.get_by_id(db, content_id=content_id)
        if db_obj:
            db.delete(db_obj)
            db.commit()
            return db_obj
        return None


# Create an instance of CRUDContent
content = CRUDContent(Content)

# Export methods at module level for backward compatibility
create_content = content.create
get_content_by_id = content.get_by_id
get_content_by_slug = content.get_by_slug
get_all_content = content.get_all
update_content = content.update
delete_content = content.remove
