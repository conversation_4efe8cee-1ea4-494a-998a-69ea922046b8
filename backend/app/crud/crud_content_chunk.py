import uuid
from typing import Optional

from sqlalchemy import desc
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.content_chunk import ContentChunk
from app.models.scraped_page import ScrapedPage
from app.schemas.content_chunk import ContentChunkCreate, ContentChunkUpdate


class CRUDContentChunk(CRUDBase[ContentChunk, ContentChunkCreate, ContentChunkUpdate]):
    def find_similar_content_chunks(
        self,
        db: Session,
        query_embedding: list[float],
        limit: int = 5,
        clinic_id: Optional[str] = None,
        similarity_threshold: float = 0.3,
    ) -> list[ContentChunk]:
        """
        Find content chunks similar to the query embedding using cosine similarity.

        Args:
            db: Database session
            query_embedding: The query embedding vector to compare against
            limit: Maximum number of similar chunks to return
            clinic_id: Optional clinic ID to filter chunks by
            similarity_threshold: Minimum similarity score (0-1) for chunks to be included

        Returns:
            List of ContentChunk objects ordered by similarity (most similar first)
        """
        try:
            # Calculate similarity: 1 - cosine_distance
            similarity_col = (
                1 - self.model.embedding.cosine_distance(query_embedding)
            ).label("similarity")

            # Build the query using SQLAlchemy ORM
            query = db.query(self.model, similarity_col)

            # Apply similarity threshold filter
            query = query.filter(similarity_col > similarity_threshold)

            # If clinic_id is provided, filter by clinic_id in two ways:
            # 1. Chunks from scraped pages belonging to the clinic
            # 2. Chunks from education materials (public or clinic-specific)
            if clinic_id:
                # Create subqueries for both sources
                from sqlalchemy import or_, and_
                
                # Subquery 1: Chunks from scraped pages
                scraped_chunks = db.query(self.model.id).join(
                    ScrapedPage, self.model.scraped_page_id == ScrapedPage.id
                ).filter(ScrapedPage.clinic_id == clinic_id)
                
                # Condition 2: Education material chunks
                # Either public materials or materials for this clinic
                # Note: JSON boolean true is stored without quotes, so we use cast
                from sqlalchemy import cast, Boolean
                education_chunks_public = cast(
                    self.model.metadata_.op('->')('is_public'), 
                    Boolean
                ) == True
                education_chunks_clinic = self.model.metadata_.op('->>')('clinic_id') == str(clinic_id)
                education_chunks_filter = self.model.metadata_.op('->>')('source_type') == 'education_material'
                
                # Combine all conditions
                query = query.filter(
                    or_(
                        self.model.id.in_(scraped_chunks),
                        and_(
                            education_chunks_filter,
                            or_(education_chunks_public, education_chunks_clinic)
                        )
                    )
                )

            # Apply ordering and limit
            query = query.order_by(desc(similarity_col)).limit(limit)

            # Execute the query
            results = query.all()

            # Extract the ContentChunk objects and attach similarity scores
            chunks = []
            for chunk, similarity_score in results:
                # Attach similarity score as a dynamic attribute
                chunk.similarity_score = float(similarity_score)
                chunks.append(chunk)
            
            return chunks

        except Exception as e:
            print(f"Error finding similar content chunks: {e}")
            raise e

    def delete_by_scraped_page(self, db: Session, *, scraped_page_id: uuid.UUID) -> int:
        """
        Delete all content chunks associated with a specific scraped page.

        Args:
            db: Database session
            scraped_page_id: The UUID of the scraped page

        Returns:
            Number of deleted chunks
        """
        chunks = (
            db.query(self.model)
            .filter(self.model.scraped_page_id == scraped_page_id)
            .all()
        )
        count = len(chunks)
        for chunk in chunks:
            db.delete(chunk)
        db.commit()
        return count


crud_content_chunk = CRUDContentChunk(ContentChunk)
