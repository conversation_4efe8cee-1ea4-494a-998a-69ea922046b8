from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import UploadFile
import os
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.education_material import EducationMaterial, MaterialType
from app.schemas.education_material import EducationMaterialCreate, EducationMaterialUpdate, EducationMaterialSearchFilters
from app.core.config import settings
import logging
from app.core.storage import upload_file_to_azure, generate_unique_filename

logger = logging.getLogger(__name__)


class CRUDEducationMaterial(CRUDBase[EducationMaterial, EducationMaterialCreate, EducationMaterialUpdate]):
    def get_multi_with_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        category: Optional[str] = None,
        material_type: Optional[MaterialType] = None,
        is_public: Optional[bool] = None,
        created_by: Optional[str] = None,
        clinic_id: Optional[str] = None,
    ) -> List[EducationMaterial]:
        """
        Get education materials with optional filters
        """
        query = db.query(self.model)
        
        # Apply filters
        filters = []
        
        if search:
            search_filter = or_(
                self.model.title.ilike(f"%{search}%"),
                self.model.description.ilike(f"%{search}%")
            )
            filters.append(search_filter)
        
        if category:
            filters.append(self.model.category == category)
        
        if material_type:
            filters.append(self.model.type == material_type)
        
        if is_public is not None:
            filters.append(self.model.is_public == is_public)
        
        if created_by:
            filters.append(self.model.created_by == created_by)
        
        if clinic_id:
            filters.append(self.model.clinic_id == clinic_id)
        
        if filters:
            query = query.filter(and_(*filters))
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_public_materials(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        category: Optional[str] = None,
        material_type: Optional[MaterialType] = None,
    ) -> List[EducationMaterial]:
        """
        Get only public education materials
        """
        return self.get_multi_with_filters(
            db,
            skip=skip,
            limit=limit,
            search=search,
            category=category,
            material_type=material_type,
            is_public=True
        )

    def get_by_clinic(
        self,
        db: Session,
        *,
        clinic_id: str,
        skip: int = 0,
        limit: int = 100,
        include_public: bool = True,
    ) -> List[EducationMaterial]:
        """
        Get materials for a specific clinic (including public materials if specified)
        """
        query = db.query(self.model)
        
        if include_public:
            # Get both clinic-specific and public materials
            filters = or_(
                self.model.clinic_id == clinic_id,
                self.model.is_public == True
            )
        else:
            # Get only clinic-specific materials
            filters = self.model.clinic_id == clinic_id
        
        return query.filter(filters).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_categories(self, db: Session) -> List[str]:
        """
        Get all unique categories
        """
        categories = db.query(self.model.category).filter(
            self.model.category.isnot(None)
        ).distinct().all()
        return [category[0] for category in categories if category[0]]

    def get_analytics_summary(self, db: Session, clinic_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get analytics summary for education materials
        """
        base_query = db.query(self.model)
        
        if clinic_id:
            # Filter to clinic-specific materials and public materials
            base_query = base_query.filter(
                or_(
                    self.model.clinic_id == clinic_id,
                    self.model.is_public == True
                )
            )
        
        total_materials = base_query.count()
        public_materials = base_query.filter(self.model.is_public == True).count()
        clinic_materials = base_query.filter(
            and_(
                self.model.is_public == False,
                self.model.clinic_id == clinic_id if clinic_id else True
            )
        ).count()
        
        # Total assignments (sum of assignments_count)
        total_assignments = db.query(
            func.sum(self.model.assignments_count)
        ).scalar() or 0
        
        # Most assigned material
        most_assigned_material = base_query.order_by(
            desc(self.model.assignments_count)
        ).first()
        
        return {
            "total_materials": total_materials,
            "public_materials": public_materials,
            "clinic_materials": clinic_materials,
            "total_assignments": total_assignments,
            "most_assigned_material": most_assigned_material
        }

    def increment_views(self, db: Session, *, material_id: str) -> Optional[EducationMaterial]:
        """
        Increment the views count for a material
        """
        material = self.get(db=db, id=material_id)
        if material:
            material.views_count += 1
            db.add(material)
            db.commit()
            db.refresh(material)
        return material

    def increment_assignments(self, db: Session, *, material_id: str) -> Optional[EducationMaterial]:
        """
        Increment the assignments count for a material
        """
        material = self.get(db=db, id=material_id)
        if material:
            material.assignments_count += 1
            db.add(material)
            db.commit()
            db.refresh(material)
        return material

    def create_with_creator(
        self, db: Session, *, obj_in: EducationMaterialCreate, created_by: str
    ) -> EducationMaterial:
        """
        Create education material with creator information
        """
        # Use dict() for backwards compatibility with Pydantic v1/v2
        try:
            obj_data = obj_in.model_dump()
        except AttributeError:
            obj_data = obj_in.dict()
        
        obj_data["created_by"] = created_by
        
        # Convert UUID to string if needed
        if "clinic_id" in obj_data and obj_data["clinic_id"]:
            obj_data["clinic_id"] = str(obj_data["clinic_id"])
        
        db_obj = self.model(**obj_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_filtered(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: EducationMaterialSearchFilters,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[str] = None,
    ) -> List[EducationMaterial]:
        """
        Get education materials with filters based on user role and permissions
        """
        query = db.query(self.model)
        
        # Apply search filters
        filter_conditions = []
        
        if filters.search:
            search_filter = or_(
                self.model.title.ilike(f"%{filters.search}%"),
                self.model.description.ilike(f"%{filters.search}%")
            )
            filter_conditions.append(search_filter)
        
        if filters.category:
            filter_conditions.append(self.model.category == filters.category)
        
        if filters.type:
            filter_conditions.append(self.model.type == filters.type)
        
        if filters.clinic_id:
            filter_conditions.append(self.model.clinic_id == filters.clinic_id)
        
        if filters.is_public is not None:
            filter_conditions.append(self.model.is_public == filters.is_public)
        
        # Apply role-based filtering
        # Handle role as either string or list
        is_admin = False
        is_clinician = False
        
        if isinstance(user_role, list):
            is_admin = "admin" in user_role
            is_clinician = "clinician" in user_role
        elif isinstance(user_role, str):
            is_admin = user_role == "admin"
            is_clinician = user_role == "clinician"
        
        if is_admin:
            # Admins see all materials
            pass
        elif is_clinician:
            # Clinicians see public materials and their clinic's materials
            role_filter = or_(
                self.model.is_public == True,
                self.model.clinic_id == user_clinic_id
            )
            filter_conditions.append(role_filter)
        else:  # patient role
            # Patients see only public materials
            # (assigned materials would be handled through a separate endpoint)
            filter_conditions.append(self.model.is_public == True)
        
        if filter_conditions:
            query = query.filter(and_(*filter_conditions))
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def can_access_material(
        self,
        db: Session,
        material: EducationMaterial,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[str] = None,
    ) -> bool:
        """
        Check if a user can access a specific education material
        """
        # Handle role as either string or list
        is_admin = False
        is_clinician = False
        is_patient = False
        
        if isinstance(user_role, list):
            is_admin = "admin" in user_role
            is_clinician = "clinician" in user_role
            is_patient = "patient" in user_role
        elif isinstance(user_role, str):
            is_admin = user_role == "admin"
            is_clinician = user_role == "clinician"
            is_patient = user_role == "patient"
        
        # Admins can access all materials
        if is_admin:
            return True
        
        # Public materials are accessible to all
        if material.is_public:
            return True
        
        # Clinicians can access their clinic's materials
        if is_clinician and material.clinic_id == user_clinic_id:
            return True
        
        # Patients can access public materials or assigned materials
        if is_patient:
            if material.is_public:
                return True
            
            # Check if material is assigned to the patient
            from app.crud.crud_patient_education_assignment import patient_education_assignment as crud_assignment
            # Convert material.id to UUID if it's a string
            import uuid
            material_uuid = material.id if isinstance(material.id, uuid.UUID) else uuid.UUID(str(material.id))
            
            assignment = crud_assignment.get_by_patient_and_material(
                db=db,
                patient_id=user_id,
                material_id=material_uuid
            )
            if assignment:
                return True
        
        return False

    def can_modify_material(
        self,
        material: EducationMaterial,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[str] = None,
    ) -> bool:
        """
        Check if a user can modify or delete an education material
        """
        # Handle role as either string or list
        is_admin = False
        is_clinician = False
        
        if isinstance(user_role, list):
            is_admin = "admin" in user_role
            is_clinician = "clinician" in user_role
        elif isinstance(user_role, str):
            is_admin = user_role == "admin"
            is_clinician = user_role == "clinician"
        
        # Admins can modify all materials
        if is_admin:
            return True
        
        # Creators can modify their own materials
        if material.created_by == user_id:
            return True
        
        # Clinicians can modify their clinic's materials
        if is_clinician and material.clinic_id == user_clinic_id:
            return True
        
        return False

    def get_analytics(
        self,
        db: Session,
        user_role: str,
        user_clinic_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get analytics summary based on user role
        """
        # Handle role as either string or list
        is_admin = False
        is_clinician = False
        
        if isinstance(user_role, list):
            is_admin = "admin" in user_role
            is_clinician = "clinician" in user_role
        elif isinstance(user_role, str):
            is_admin = user_role == "admin"
            is_clinician = user_role == "clinician"
        
        if is_admin:
            # Global analytics for admins
            return self.get_analytics_summary(db)
        elif is_clinician and user_clinic_id:
            # Clinic-specific analytics for clinicians
            return self.get_analytics_summary(db, clinic_id=user_clinic_id)
        else:
            return {
                "total_materials": 0,
                "public_materials": 0,
                "clinic_materials": 0,
                "total_assignments": 0,
                "most_assigned_material": None
            }

    async def upload_file(
        self,
        file: UploadFile,
        user_id: str,
        clinic_id: Optional[str] = None,
    ) -> tuple[str, str]:
        """
        Upload a file to Azure Blob Storage for education materials
        
        Returns:
            tuple: (file_url, file_path)
        """
        if not file or not file.filename:
            raise ValueError("No file provided for upload")
        
        # Generate unique filename preserving the original extension
        unique_filename = generate_unique_filename(file.filename)
        
        # Construct file path with proper folder structure
        if clinic_id:
            file_path = f"education_materials/clinics/{clinic_id}/{unique_filename}"
        else:
            file_path = f"education_materials/public/{unique_filename}"
        
        # Upload to Azure Blob Storage
        # For now, return a placeholder URL since Azure upload might not be configured
        # In production, this should upload to Azure Blob Storage
        try:
            file_url = await upload_file_to_azure(
                file=file,
                destination_blob_name=file_path,
                container_name=settings.AZURE_STORAGE_CONTAINER_NAME
            )
        except Exception as e:
            logger.warning(f"Azure upload failed, using placeholder: {e}")
            file_url = f"https://placeholder.blob.core.windows.net/{file_path}"
        
        if not file_url:
            raise Exception("Failed to upload file to Azure Blob Storage")
        
        return file_url, file_path

    def get_categories(
        self,
        db: Session,
        user_role: str,
        user_clinic_id: Optional[str] = None,
    ) -> List[str]:
        """
        Get all unique categories based on user permissions
        """
        query = db.query(self.model.category).filter(
            self.model.category.isnot(None)
        )
        
        # Apply role-based filtering
        # Handle role as either string or list
        is_admin = False
        is_clinician = False
        is_patient = False
        
        if isinstance(user_role, list):
            is_admin = "admin" in user_role
            is_clinician = "clinician" in user_role
            is_patient = "patient" in user_role
        elif isinstance(user_role, str):
            is_admin = user_role == "admin"
            is_clinician = user_role == "clinician"
            is_patient = user_role == "patient"
        
        if is_clinician and user_clinic_id:
            query = query.filter(
                or_(
                    self.model.is_public == True,
                    self.model.clinic_id == user_clinic_id
                )
            )
        elif is_patient:
            query = query.filter(self.model.is_public == True)
        # Admin sees all categories
        
        categories = query.distinct().all()
        return sorted([category[0] for category in categories if category[0]])


education_material = CRUDEducationMaterial(EducationMaterial)