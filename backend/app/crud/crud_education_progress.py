import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func
from sqlalchemy.orm import Session, joinedload

from app.crud.base import CRUDBase
from app.models.education_progress import EducationProgress
from app.schemas.education_progress import EducationProgressCreate, EducationProgressUpdate

logger = logging.getLogger(__name__)


class CRUDEducationProgress(CRUDBase[EducationProgress, EducationProgressCreate, EducationProgressUpdate]):
    """CRUD operations for EducationProgress model."""

    def get_by_assignment(
        self,
        db: Session,
        *,
        assignment_id: uuid.UUID,
        patient_id: Optional[str] = None,
    ) -> Optional[EducationProgress]:
        """
        Get progress record for a specific assignment.

        Args:
            db: The database session.
            assignment_id: Assignment ID.
            patient_id: Optional patient ID for additional filtering.

        Returns:
            The EducationProgress ORM object or None if not found.
        """
        query = db.query(EducationProgress).filter(
            EducationProgress.assignment_id == assignment_id
        )
        
        if patient_id:
            query = query.filter(EducationProgress.patient_id == patient_id)
        
        return query.first()

    def get_by_patient_and_material(
        self,
        db: Session,
        *,
        patient_id: str,
        material_id: uuid.UUID
    ) -> Optional[EducationProgress]:
        """
        Get progress record for a specific patient and material.

        Args:
            db: The database session.
            patient_id: Patient ID.
            material_id: Material ID.

        Returns:
            The EducationProgress ORM object or None if not found.
        """
        return db.query(EducationProgress).filter(
            EducationProgress.patient_id == patient_id,
            EducationProgress.material_id == material_id
        ).first()

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        patient_id: Optional[str] = None,
        material_id: Optional[uuid.UUID] = None,
        completed_only: bool = False,
        in_progress_only: bool = False
    ) -> List[EducationProgress]:
        """
        Get multiple progress records with optional filtering.

        Args:
            db: The database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            patient_id: Filter by patient ID.
            material_id: Filter by material ID.
            completed_only: Filter for completed materials only.
            in_progress_only: Filter for in-progress materials only.

        Returns:
            List of EducationProgress ORM objects.
        """
        query = db.query(EducationProgress).options(
            joinedload(EducationProgress.patient),
            joinedload(EducationProgress.material),
            joinedload(EducationProgress.assignment)
        )

        # Apply filters
        if patient_id:
            query = query.filter(EducationProgress.patient_id == patient_id)

        if material_id:
            query = query.filter(EducationProgress.material_id == material_id)

        if completed_only:
            query = query.filter(EducationProgress.completed_at.isnot(None))

        if in_progress_only:
            query = query.filter(
                and_(
                    EducationProgress.progress_percentage > 0,
                    EducationProgress.completed_at.is_(None)
                )
            )

        return query.order_by(
            EducationProgress.last_accessed.desc()
        ).offset(skip).limit(limit).all()

    def get_by_patient(
        self,
        db: Session,
        *,
        patient_id: str,
        skip: int = 0,
        limit: int = 100,
        completed_only: bool = False
    ) -> List[EducationProgress]:
        """
        Get progress records for a specific patient.

        Args:
            db: The database session.
            patient_id: Patient ID.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            completed_only: Filter for completed materials only.

        Returns:
            List of EducationProgress ORM objects.
        """
        query = db.query(EducationProgress).options(
            joinedload(EducationProgress.material),
            joinedload(EducationProgress.assignment)
        ).filter(EducationProgress.patient_id == patient_id)

        if completed_only:
            query = query.filter(EducationProgress.completed_at.isnot(None))

        return query.order_by(
            EducationProgress.last_accessed.desc()
        ).offset(skip).limit(limit).all()

    def update_progress(
        self,
        db: Session,
        *,
        assignment_id: uuid.UUID,
        progress_percentage: float,
        time_spent_minutes: int = 0
    ) -> Optional[EducationProgress]:
        """
        Update progress for an assignment.

        Args:
            db: The database session.
            assignment_id: Assignment ID.
            progress_percentage: Progress percentage (0-100).
            time_spent_minutes: Additional time spent.

        Returns:
            The updated EducationProgress or None if not found.
        """
        db_obj = self.get_by_assignment(db, assignment_id=assignment_id)
        if db_obj:
            db_obj.progress_percentage = progress_percentage
            db_obj.time_spent_minutes += time_spent_minutes
            db_obj.last_accessed = datetime.utcnow()
            
            # Mark as completed if 100%
            if progress_percentage >= 100.0 and not db_obj.completed_at:
                db_obj.completed_at = datetime.utcnow()
            elif progress_percentage < 100.0 and db_obj.completed_at:
                # Unmark completion if progress goes below 100%
                db_obj.completed_at = None
                
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def mark_completed(
        self,
        db: Session,
        *,
        assignment_id: uuid.UUID,
        patient_feedback: Optional[str] = None,
        patient_rating: Optional[int] = None
    ) -> Optional[EducationProgress]:
        """
        Mark an assignment as completed with optional feedback.

        Args:
            db: The database session.
            assignment_id: Assignment ID.
            patient_feedback: Optional feedback from patient.
            patient_rating: Optional rating (1-5 stars).

        Returns:
            The updated EducationProgress or None if not found.
        """
        db_obj = self.get_by_assignment(db, assignment_id=assignment_id)
        if db_obj:
            db_obj.progress_percentage = 100.0
            db_obj.completed_at = datetime.utcnow()
            db_obj.last_accessed = datetime.utcnow()
            
            if patient_feedback:
                db_obj.patient_feedback = patient_feedback
            
            if patient_rating:
                db_obj.patient_rating = patient_rating
                
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def get_completion_stats(
        self,
        db: Session,
        *,
        patient_id: Optional[str] = None,
        material_id: Optional[uuid.UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get completion statistics.

        Args:
            db: The database session.
            patient_id: Optional patient ID filter.
            material_id: Optional material ID filter.
            start_date: Optional start date filter.
            end_date: Optional end date filter.

        Returns:
            Dictionary with completion statistics.
        """
        query = db.query(EducationProgress)

        # Apply filters
        if patient_id:
            query = query.filter(EducationProgress.patient_id == patient_id)

        if material_id:
            query = query.filter(EducationProgress.material_id == material_id)

        if start_date:
            query = query.filter(EducationProgress.created_at >= start_date)

        if end_date:
            query = query.filter(EducationProgress.created_at <= end_date)

        # Calculate statistics
        total_assignments = query.count()
        completed_assignments = query.filter(
            EducationProgress.completed_at.isnot(None)
        ).count()
        
        in_progress_assignments = query.filter(
            and_(
                EducationProgress.progress_percentage > 0,
                EducationProgress.completed_at.is_(None)
            )
        ).count()

        avg_progress = query.with_entities(
            func.avg(EducationProgress.progress_percentage)
        ).scalar() or 0.0

        avg_time_spent = query.with_entities(
            func.avg(EducationProgress.time_spent_minutes)
        ).scalar() or 0.0

        avg_rating = query.filter(
            EducationProgress.patient_rating.isnot(None)
        ).with_entities(
            func.avg(EducationProgress.patient_rating)
        ).scalar() or 0.0

        completion_rate = (completed_assignments / total_assignments * 100) if total_assignments > 0 else 0.0

        return {
            "total_assignments": total_assignments,
            "completed_assignments": completed_assignments,
            "in_progress_assignments": in_progress_assignments,
            "not_started_assignments": total_assignments - completed_assignments - in_progress_assignments,
            "completion_rate": round(completion_rate, 2),
            "average_progress": round(avg_progress, 2),
            "average_time_spent_minutes": round(avg_time_spent, 2),
            "average_rating": round(avg_rating, 2)
        }

    def get_material_analytics(
        self,
        db: Session,
        *,
        material_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        Get analytics for a specific material.

        Args:
            db: The database session.
            material_id: Material ID.

        Returns:
            Dictionary with material analytics.
        """
        query = db.query(EducationProgress).filter(
            EducationProgress.material_id == material_id
        )

        total_assignments = query.count()
        if total_assignments == 0:
            return {
                "total_assignments": 0,
                "completion_rate": 0.0,
                "average_progress": 0.0,
                "average_time_spent": 0.0,
                "average_rating": 0.0,
                "ratings_count": 0
            }

        completed_count = query.filter(
            EducationProgress.completed_at.isnot(None)
        ).count()

        stats = query.with_entities(
            func.avg(EducationProgress.progress_percentage).label('avg_progress'),
            func.avg(EducationProgress.time_spent_minutes).label('avg_time'),
            func.avg(EducationProgress.patient_rating).label('avg_rating'),
            func.count(EducationProgress.patient_rating).label('ratings_count')
        ).first()

        return {
            "total_assignments": total_assignments,
            "completion_rate": round((completed_count / total_assignments * 100), 2),
            "average_progress": round(stats.avg_progress or 0.0, 2),
            "average_time_spent": round(stats.avg_time or 0.0, 2),
            "average_rating": round(stats.avg_rating or 0.0, 2),
            "ratings_count": stats.ratings_count or 0
        }

    def count(
        self,
        db: Session,
        *,
        patient_id: Optional[str] = None,
        material_id: Optional[uuid.UUID] = None,
        completed_only: bool = False
    ) -> int:
        """
        Count progress records with optional filtering.

        Args:
            db: The database session.
            patient_id: Filter by patient ID.
            material_id: Filter by material ID.
            completed_only: Filter for completed materials only.

        Returns:
            Count of matching progress records.
        """
        query = db.query(EducationProgress)

        if patient_id:
            query = query.filter(EducationProgress.patient_id == patient_id)

        if material_id:
            query = query.filter(EducationProgress.material_id == material_id)

        if completed_only:
            query = query.filter(EducationProgress.completed_at.isnot(None))

        return query.count()


# Create the crud instance
education_progress = CRUDEducationProgress(EducationProgress)