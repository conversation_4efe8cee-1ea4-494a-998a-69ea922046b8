from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.event_log import EventLog
from app.schemas.event_log import EventLogCreate, EventLogUpdate


class CRUDEventLog(CRUDBase[EventLog, EventLogCreate, EventLogUpdate]):
    def create_with_actor(
        self,
        db: Session,
        *,
        obj_in: EventLogCreate,
        actor_user_id: str,
        actor_role: str
    ) -> EventLog:
        """Create a new event log with actor information."""
        obj_in_data = (
            obj_in.model_dump()
        )  # Use model_dump instead of dict in newer Pydantic
        obj_in_data["actor_user_id"] = actor_user_id
        obj_in_data["actor_role"] = actor_role

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_actor_user_id(
        self, db: Session, *, actor_user_id: str, skip: int = 0, limit: int = 100
    ) -> list[EventLog]:
        """Get event logs by actor_user_id."""
        return (
            db.query(self.model)
            .filter(self.model.actor_user_id == actor_user_id)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_actor_role(
        self, db: Session, *, actor_role: str, skip: int = 0, limit: int = 100
    ) -> list[EventLog]:
        """Get event logs by actor_role."""
        return (
            db.query(self.model)
            .filter(self.model.actor_role == actor_role)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_clinic(
        self, db: Session, *, clinic_id: UUID, skip: int = 0, limit: int = 100
    ) -> list[EventLog]:
        """Get event logs by clinic_id."""
        return (
            db.query(self.model)
            .filter(self.model.clinic_id == clinic_id)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_action(
        self, db: Session, *, action: str, skip: int = 0, limit: int = 100
    ) -> list[EventLog]:
        """Get event logs by action."""
        return (
            db.query(self.model)
            .filter(self.model.action == action)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_resource(
        self,
        db: Session,
        *,
        resource_type: str,
        resource_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[EventLog]:
        """Get event logs by resource type and optionally resource id."""
        query = db.query(self.model).filter(
            self.model.target_resource_type == resource_type
        )
        if resource_id:
            query = query.filter(self.model.target_resource_id == resource_id)

        return (
            query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
        )

    def get_llm_actions(
        self,
        db: Session,
        *,
        actor_user_id: Optional[str] = None,
        clinic_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[EventLog]:
        """Get LLM-driven action events, optionally filtered by user or clinic."""
        # Filter for events with LLM-specific fields populated
        query = db.query(self.model).filter(
            self.model.extracted_intent.isnot(None)
            | self.model.executed_api_action.isnot(None)
        )

        if actor_user_id:
            query = query.filter(self.model.actor_user_id == actor_user_id)

        if clinic_id:
            query = query.filter(self.model.clinic_id == clinic_id)

        return (
            query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
        )

    def get_stats(
        self, db: Session, *, clinic_id: Optional[UUID] = None, days: int = 30
    ) -> dict[str, Any]:
        """Get event statistics for the specified period."""
        cutoff_date = datetime.utcnow().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        # Calculate date N days ago
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)

        # Base query filter for the time period
        base_filter = self.model.created_at >= cutoff_date
        if clinic_id:
            base_filter = and_(base_filter, self.model.clinic_id == clinic_id)

        # Success vs failure stats
        outcome_counts = (
            db.query(self.model.outcome, func.count(self.model.id))
            .filter(base_filter)
            .group_by(self.model.outcome)
            .all()
        )

        # Action type distribution
        action_counts = (
            db.query(self.model.action, func.count(self.model.id))
            .filter(base_filter)
            .group_by(self.model.action)
            .all()
        )

        # Most common intents extracted
        intent_counts = (
            db.query(self.model.extracted_intent, func.count(self.model.id))
            .filter(and_(base_filter, self.model.extracted_intent.isnot(None)))
            .group_by(self.model.extracted_intent)
            .order_by(desc(func.count(self.model.id)))
            .limit(10)
            .all()
        )

        # Format results
        return {
            "total_events": db.query(func.count(self.model.id))
            .filter(base_filter)
            .scalar(),
            "outcomes": {outcome: count for outcome, count in outcome_counts},
            "actions": {action: count for action, count in action_counts},
            "top_intents": {intent: count for intent, count in intent_counts if intent},
        }


event_log = CRUDEventLog(EventLog)

# Export methods at module level for backward compatibility
create_event_log_with_actor = event_log.create_with_actor
get_by_actor_user_id = event_log.get_by_actor_user_id
get_by_actor_role = event_log.get_by_actor_role
get_by_clinic = event_log.get_by_clinic
get_by_action = event_log.get_by_action
get_by_resource = event_log.get_by_resource
get_llm_actions = event_log.get_llm_actions
get_multi = event_log.get_multi
