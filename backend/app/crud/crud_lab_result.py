import logging
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from sqlalchemy import func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase

# Models
from app.models.lab_result import LabResult  # Assuming model exists at this path

# Schemas
from app.schemas.lab_result import LabResultCreate, LabResultStatus, LabResultUpdate

logger = logging.getLogger(__name__)

# --- CRUD Class for LabResult ---


class CRUDLabResult(CRUDBase[LabResult, LabResultCreate, LabResultUpdate]):
    """CRUD operations for LabResult model."""

    def create(self, db: Session, *, obj_in: LabResultCreate) -> LabResult:
        """
        Creates a new lab result record in the database.

        Args:
            db: The database session.
            obj_in: Pydantic schema with lab result creation data.

        Returns:
            The newly created LabResult ORM object.

        Raises:
            IntegrityError: If a database constraint is violated (e.g., FK).
        """
        logger.debug(
            f"CRUD: Creating lab result '{obj_in.test_name}' for patient {obj_in.patient_id}"
        )
        # Status defaults to PENDING_REVIEW in schema/model
        db_obj = LabResult(**obj_in.model_dump())
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating lab result for patient {obj_in.patient_id}",
                exc_info=True,
            )
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created lab result ID {db_obj.id}")
        return db_obj

    def get(self, db: Session, lab_result_id: UUID) -> Optional[LabResult]:
        """
        Retrieves a lab result by its UUID primary key.

        Args:
            db: The database session.
            lab_result_id: The UUID of the lab result.

        Returns:
            The LabResult ORM object or None if not found.
        """
        logger.debug(f"CRUD: Getting lab result ID {lab_result_id}")
        return db.query(self.model).filter(self.model.id == lab_result_id).first()

    def get_by_patient(
        self,
        db: Session,
        patient_id: str,
        *,
        status: Optional[LabResultStatus] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[LabResult]:
        """
        Retrieves lab results for a specific patient, optionally filtered by status,
        with pagination, ordered by collection time descending.

        Args:
            db: The database session.
            patient_id: The string ID of the patient (from Clerk).
            status: Optional status to filter lab results by.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of LabResult ORM objects.
        """
        logger.debug(
            f"CRUD: Getting lab results for patient {patient_id}, status={status}, skip={skip}, limit={limit}"
        )
        query = db.query(self.model).filter(self.model.patient_id == patient_id)

        if status:
            query = query.filter(self.model.status == status)

        return (
            query.order_by(self.model.collected_at.desc())  # Order by most recent first
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_pending_review_count_by_clinician(
        self, db: Session, clinician_id: str
    ) -> int:
        """
        Retrieves the count of lab results pending review for a specific clinician.

        Args:
            db: The database session.
            clinician_id: The string ID of the clinician (from Clerk).

        Returns:
            The count of lab results pending review.
        """
        logger.debug(f"CRUD: Counting pending lab results for clinician {clinician_id}")
        count = (
            db.query(func.count(self.model.id))
            .filter(self.model.clinician_id == clinician_id)
            .filter(self.model.status == LabResultStatus.PENDING_REVIEW)
            .scalar()
        )
        logger.debug(
            f"CRUD: Found {count} pending lab results for clinician {clinician_id}"
        )
        return count or 0  # Ensure 0 is returned if count is None

    def update_status(
        self,
        db: Session,
        *,
        lab_result_id: UUID,
        status: LabResultStatus,
        reviewed_by_clinician_id: Optional[str] = None,
    ) -> Optional[LabResult]:
        """
        Updates the status of a specific lab result.
        Sets reviewed_at and reviewed_by_clinician_id if status is REVIEWED.

        Args:
            db: The database session.
            lab_result_id: The UUID of the lab result to update.
            status: The new status for the lab result.
            reviewed_by_clinician_id: The string ID of the clinician reviewing the result (from Clerk, required if status is REVIEWED).

        Returns:
            The updated LabResult ORM object or None if not found.

        Raises:
            ValueError: If status is REVIEWED but reviewed_by_clinician_id is not provided.
        """
        logger.debug(
            f"CRUD: Updating status of lab result {lab_result_id} to {status.value}"
        )
        lab_result = db.query(self.model).filter(self.model.id == lab_result_id).first()
        if not lab_result:
            logger.warning(
                f"CRUD: Lab result {lab_result_id} not found for status update."
            )
            return None

        lab_result.status = status
        if status == LabResultStatus.REVIEWED:
            if not reviewed_by_clinician_id:
                # Rollback potential status change before raising
                db.rollback()
                raise ValueError(
                    "reviewed_by_clinician_id is required when setting status to REVIEWED"
                )
            lab_result.reviewed_at = datetime.now(timezone.utc)
            lab_result.reviewed_by_clinician_id = reviewed_by_clinician_id
        elif (
            lab_result.reviewed_at is not None
        ):  # Clear review fields if status changes from REVIEWED
            lab_result.reviewed_at = None
            lab_result.reviewed_by_clinician_id = None

        db.add(lab_result)
        try:
            db.commit()
            db.refresh(lab_result)
            logger.debug(f"CRUD: Updated status for lab result ID {lab_result_id}")
            return lab_result
        except Exception as e:
            db.rollback()
            logger.error(
                f"Error updating status for lab result {lab_result_id}", exc_info=True
            )
            raise e


# Create an instance of CRUDLabResult
lab_result = CRUDLabResult(LabResult)

# Export methods at module level for backward compatibility
create_lab_result = lab_result.create
get_lab_result = lab_result.get
get_lab_results_by_patient = lab_result.get_by_patient
get_pending_review_count_by_clinician = lab_result.get_pending_review_count_by_clinician
update_lab_result_status = lab_result.update_status
