from typing import Optional

from sqlalchemy.orm import Session

from app import models
from app.crud.base import CRUDBase
from app.schemas.medication import MedicationCreate, MedicationUpdate


class CRUDMedication(CRUDBase[models.Medication, MedicationCreate, MedicationUpdate]):
    """CRUD operations for Medication model."""

    def get_by_name(self, db: Session, *, name: str) -> Optional[models.Medication]:
        """Get a medication by name."""
        return (
            db.query(models.Medication).filter(models.Medication.name == name).first()
        )

    def count(self, db: Session) -> int:
        """Get the total count of medications."""
        return db.query(models.Medication).count()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[models.Medication]:
        from sqlalchemy.orm import joinedload

        return (
            db.query(models.Medication)
            .options(joinedload(models.Medication.clinics))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_name_and_category(self, db: Session, *, name: str, category: str):
        """Get a medication by name and category (case-insensitive)."""
        return (
            db.query(models.Medication)
            .filter(
                models.Medication.name.ilike(name),
                models.Medication.category.ilike(category),
            )
            .first()
        )


medication = CRUDMedication(models.Medication)
