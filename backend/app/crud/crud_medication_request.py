import logging
from typing import Optional
from uuid import UUID

from sqlalchemy import func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.clinician import clinician_patient_association
from app.models.medication_request import (
    MedicationRequest,
    MedicationRequestStatus,
)
from app.schemas.medication_request import (
    MedicationRequestCreate,
    MedicationRequestUpdate,
)
from app import crud
from app.schemas.event_log import EventLogCreate

# Initialize logger
logger = logging.getLogger(__name__)


class CRUDMedicationRequest(
    CRUDBase[MedicationRequest, MedicationRequestCreate, MedicationRequestUpdate]
):
    """CRUD operations for MedicationRequest model."""

    def create_medication_request(
        self,
        db: Session,
        *,
        obj_in: MedicationRequestCreate,
        patient_id: UUID,
    ) -> MedicationRequest:
        """
        Creates a new medication request record in the database for a specific patient.

        Args:
            db: The database session.
            obj_in: Pydantic schema with medication request creation data.
            patient_id: The UUID of the patient making the request.

        Returns:
            The newly created MedicationRequest ORM object.
        """
        logger.debug(
            f"CRUD: Creating medication request for patient {patient_id} with medication: '{obj_in.medication_name}'"
        )

        # Create ORM object from Pydantic schema, adding patient_id and default status
        create_data = {
            "medication_name": obj_in.medication_name,
            "dosage": obj_in.dosage,
            "frequency": obj_in.frequency,
            "duration": obj_in.duration,
            "notes": obj_in.notes,
        }
        db_obj = MedicationRequest(
            **create_data,
            patient_id=patient_id,
            status=MedicationRequestStatus.PENDING,  # Initial status from Enum
        )
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:  # Catch potential DB errors like FK violation
            db.rollback()
            logger.error(
                f"Error creating medication request for patient {patient_id}",
                exc_info=True,
            )
            raise e  # Re-raise or handle appropriately
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created medication request ID {db_obj.id}")

        # Create event log for medication request
        try:
            event_log_data = EventLogCreate(
                action="CREATE",
                actor_user_id=str(patient_id),
                actor_role="patient",
                target_resource_type="medication_request",
                target_resource_id=str(db_obj.id),
                status="SUCCESS",
                outcome="SUCCESS",
                details={
                    "medication_name": db_obj.medication_name,
                    "dosage": db_obj.dosage if db_obj.dosage else "",
                    "frequency": db_obj.frequency if db_obj.frequency else "",
                    "status": str(db_obj.status.value) if hasattr(db_obj.status, 'value') else str(db_obj.status),
                    "requested_at": str(db_obj.created_at),
                    "actor_name": "Patient"
                }
            )
            
            crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=str(patient_id),
                actor_role="patient"
            )
            logger.info(f"Created event log for medication request ID {db_obj.id}")
        except Exception as e:
            # Log error but don't fail the medication request creation
            logger.error(f"Failed to create event log for medication request: {str(e)}", exc_info=True)

        return db_obj

    def get_medication_request_by_id(
        self, db: Session, *, request_id: UUID
    ) -> Optional[MedicationRequest]:
        """
        Get a specific medication request by its UUID primary key.

        Args:
            db: The database session.
            request_id: The UUID of the medication request.

        Returns:
            The MedicationRequest ORM object or None if not found.
        """
        return (
            db.query(MedicationRequest)
            .filter(MedicationRequest.id == request_id)
            .first()
        )

    def get_medication_requests_count_by_patient(
        self, db: Session, *, patient_id: UUID
    ) -> int:
        """
        Counts the total number of medication requests for a specific patient.

        Args:
            db: The database session.
            patient_id: The UUID of the patient.

        Returns:
            The total count of medication requests for that patient.
        """
        count = (
            db.query(func.count(MedicationRequest.id))
            .filter(MedicationRequest.patient_id == patient_id)
            .scalar()
        )
        return count or 0

    def get_medication_requests_by_patient(
        self,
        db: Session,
        *,
        patient_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = "created_at:desc",
    ) -> list[MedicationRequest]:
        """
        Get multiple medication requests for a specific patient with pagination and sorting.

        Args:
            db: The database session.
            patient_id: The UUID of the patient whose requests to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            sort_by: Sorting criteria in 'field:direction' format (e.g., 'created_at:desc').

        Returns:
            A list of MedicationRequest ORM objects for the specified patient.
        """
        # Build the base query
        query = db.query(MedicationRequest).filter(
            MedicationRequest.patient_id == patient_id
        )

        # Apply dynamic sorting
        sort_field = MedicationRequest.created_at  # Default sort field
        sort_direction = "desc"  # Default sort direction

        if sort_by:
            try:
                field_name, direction_str = sort_by.split(":")
                # Handle frontend sending 'requested_at' when model has 'created_at'
                if field_name == "requested_at":
                    field_name = "created_at"

                model_field = getattr(MedicationRequest, field_name, None)
                if model_field:
                    sort_field = model_field
                    if direction_str.lower() in ["asc", "desc"]:
                        sort_direction = direction_str.lower()
            except (ValueError, AttributeError):
                logger.warning(f"Invalid sort parameter: {sort_by}, using default")

        # Apply the determined sort order
        if sort_direction == "desc":
            query = query.order_by(sort_field.desc())
        else:
            query = query.order_by(sort_field.asc())

        return query.offset(skip).limit(limit).all()

    def update_medication_request_status(
        self,
        db: Session,
        *,
        db_obj: MedicationRequest,
        status: MedicationRequestStatus,
        clinician_id: Optional[UUID] = None,
        notes: Optional[str] = None,
    ) -> MedicationRequest:
        """
        Update the status of an existing medication request.

        Args:
            db: The database session.
            db_obj: The existing MedicationRequest ORM object to update.
            status: The new status to set (from MedicationRequestStatus enum).
            clinician_id: Optional UUID of the clinician making the update.
            notes: Optional notes about the status update.

        Returns:
            The updated MedicationRequest ORM object.
        """
        # Update the status
        db_obj.status = status

        # Update resolved_at timestamp if status is terminal (approved/rejected)
        if status in [
            MedicationRequestStatus.APPROVED,
            MedicationRequestStatus.REJECTED,
        ]:
            db_obj.resolved_at = func.now()  # Use database timestamp

        # Update clinician_id if provided
        if clinician_id:
            db_obj.resolved_by_clinician_id = clinician_id

        # Update notes if provided
        if notes:
            db_obj.clinician_notes = notes

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete_medication_request(
        self, db: Session, *, request_id: UUID
    ) -> Optional[MedicationRequest]:
        """
        Delete a medication request by its UUID (Hard Delete).
        Consider implications based on compliance requirements.

        Args:
            db: The database session.
            request_id: The UUID of the request to delete.

        Returns:
            The deleted MedicationRequest ORM object or None if not found.
        """
        obj = db.query(MedicationRequest).get(request_id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    def get_pending_request_count_for_clinician(
        self, db: Session, *, clinician_id: UUID
    ) -> int:
        """
        Counts the number of pending medication requests for patients associated with a specific clinician.

        Args:
            db: The database session.
            clinician_id: The UUID of the clinician.

        Returns:
            The count of pending medication requests for that clinician's patients.
        """
        # Get the count of pending requests for patients associated with this clinician
        count = (
            db.query(func.count(MedicationRequest.id))
            .join(
                clinician_patient_association,
                MedicationRequest.patient_id
                == clinician_patient_association.c.patient_id,
            )
            .filter(
                clinician_patient_association.c.clinician_id == clinician_id,
                MedicationRequest.status == MedicationRequestStatus.PENDING,
            )
            .scalar()
        )
        return count or 0

    def get_latest_medication_request_by_patient(
        self, db: Session, *, patient_id: UUID
    ) -> Optional[MedicationRequest]:
        """
        Get the most recent medication request for a specific patient.

        Args:
            db: The database session.
            patient_id: The UUID of the patient.

        Returns:
            The most recent MedicationRequest ORM object or None if no requests exist.
        """
        return (
            db.query(MedicationRequest)
            .filter(MedicationRequest.patient_id == patient_id)
            .order_by(MedicationRequest.created_at.desc())
            .first()
        )

    def get_multi_by_patient_ids(
        self,
        db: Session,
        *,
        patient_ids: list[UUID],
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        sort_by: Optional[str] = "created_at:desc",
    ) -> list[MedicationRequest]:
        """
        Get medication requests for multiple patients with pagination, filtering, and sorting.

        Args:
            db: The database session.
            patient_ids: List of patient UUID to get requests for.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            status: Optional status filter.
            sort_by: Sorting criteria in 'field:direction' format (e.g., 'created_at:desc').

        Returns:
            A list of MedicationRequest ORM objects for the specified patients.
        """
        # Build the base query
        query = db.query(MedicationRequest).filter(
            MedicationRequest.patient_id.in_(patient_ids)
        )

        # Apply status filter if provided
        if status:
            try:
                # Try to convert string status to enum
                status_enum = MedicationRequestStatus(status.upper())
                query = query.filter(MedicationRequest.status == status_enum)
            except ValueError:
                # If invalid status, log warning but continue without filter
                logger.warning(f"Invalid status filter: {status}")

        # Apply dynamic sorting
        sort_field = MedicationRequest.created_at  # Default sort field
        sort_direction = "desc"  # Default sort direction

        if sort_by:
            try:
                field_name, direction_str = sort_by.split(":")
                # Handle frontend sending 'requested_at' when model has 'created_at'
                if field_name == "requested_at":
                    field_name = "created_at"

                model_field = getattr(MedicationRequest, field_name, None)
                if model_field:
                    sort_field = model_field
                    if direction_str.lower() in ["asc", "desc"]:
                        sort_direction = direction_str.lower()
            except (ValueError, AttributeError):
                logger.warning(f"Invalid sort parameter: {sort_by}, using default")

        # Apply the determined sort order
        if sort_direction == "desc":
            query = query.order_by(sort_field.desc())
        else:
            query = query.order_by(sort_field.asc())

        return query.offset(skip).limit(limit).all()

    def get_medication_requests_count_for_clinician(
        self,
        db: Session,
        *,
        clinician_id: str,
        get_clinician_by_clerk_id_fn,
        get_patients_for_clinician_fn,
        status: Optional[MedicationRequestStatus] = None,
    ) -> int:
        """
        Counts the total number of medication requests for patients associated with a specific clinician,
        with optional status filtering.

        Args:
            db: The database session.
            clinician_id: The Clerk ID of the clinician.
            get_clinician_by_clerk_id_fn: Function to get clinician by clerk ID.
            get_patients_for_clinician_fn: Function to get patients for clinician.
            status: Optional status filter for medication requests.

        Returns:
            The total count of medication requests for that clinician's patients.
        """
        logger.debug(f"CRUD: Counting medication requests for clinician {clinician_id}")

        # First get the clinician DB record by Clerk ID
        clinician_db = get_clinician_by_clerk_id_fn(db, clerk_id=clinician_id)
        if not clinician_db:
            logger.warning(f"CRUD: Clinician with Clerk ID {clinician_id} not found")
            return 0

        # Get patients associated with this clinician
        assigned_patients = get_patients_for_clinician_fn(
            db=db, clinician_id=clinician_db.id, limit=10000
        )
        assigned_patient_ids = [p.id for p in assigned_patients]

        if not assigned_patient_ids:
            logger.debug(f"CRUD: No patients found for clinician {clinician_id}")
            return 0

        # Build the base query
        query = db.query(func.count(MedicationRequest.id)).filter(
            MedicationRequest.patient_id.in_(assigned_patient_ids)
        )

        # Apply status filter if provided
        if status is not None:
            query = query.filter(MedicationRequest.status == status)
            logger.debug(f"CRUD: Filtering count by status: {status.value}")

        count = query.scalar()
        logger.debug(
            f"CRUD: Found {count} medication requests for clinician {clinician_id}"
        )

        return count or 0  # Ensure 0 is returned if count is None

    def get_active_by_patient(
        self,
        db: Session,
        *,
        patient_id: UUID,
        limit: int = 10,
    ) -> list[MedicationRequest]:
        """
        Get active medication requests for a patient (approved status).
        
        Args:
            db: The database session.
            patient_id: The UUID of the patient.
            limit: Maximum number of records to return.
            
        Returns:
            A list of approved MedicationRequest ORM objects for the patient, ordered by most recent.
        """
        return (
            db.query(MedicationRequest)
            .filter(
                MedicationRequest.patient_id == patient_id,
                MedicationRequest.status == MedicationRequestStatus.APPROVED
            )
            .order_by(MedicationRequest.created_at.desc())
            .limit(limit)
            .all()
        )

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        sort_by: Optional[str] = "created_at:desc",
    ) -> list[MedicationRequest]:
        """
        Get all medication requests with pagination, filtering, and sorting.
        This function is primarily for admin users who need to see all requests.

        Args:
            db: The database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            status: Optional status filter.
            sort_by: Sorting criteria in 'field:direction' format (e.g., 'created_at:desc').

        Returns:
            A list of all MedicationRequest ORM objects.
        """
        logger.debug("CRUD: Fetching all medication requests for admin")

        # Build the base query
        query = db.query(MedicationRequest)

        # Apply status filter if provided
        if status:
            try:
                # Try to convert string status to enum
                status_enum = MedicationRequestStatus(status.upper())
                query = query.filter(MedicationRequest.status == status_enum)
            except ValueError:
                # If invalid status, log warning but continue without filter
                logger.warning(f"Invalid status filter: {status}")

        # Apply dynamic sorting
        sort_field = MedicationRequest.created_at  # Default sort field
        sort_direction = "desc"  # Default sort direction

        if sort_by:
            try:
                field_name, direction_str = sort_by.split(":")
                # Handle frontend sending 'requested_at' when model has 'created_at'
                if field_name == "requested_at":
                    field_name = "created_at"

                model_field = getattr(MedicationRequest, field_name, None)
                if model_field:
                    sort_field = model_field
                    if direction_str.lower() in ["asc", "desc"]:
                        sort_direction = direction_str.lower()
            except (ValueError, AttributeError):
                logger.warning(f"Invalid sort parameter: {sort_by}, using default")

        # Apply the determined sort order
        if sort_direction == "desc":
            query = query.order_by(sort_field.desc())
        else:
            query = query.order_by(sort_field.asc())

        return query.offset(skip).limit(limit).all()


medication_request = CRUDMedicationRequest(MedicationRequest)
