import logging
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase

# Models
from app.models.note import Note  # Assuming model exists at this path

# Schemas
from app.schemas.note import NoteCreate, NoteUpdate

logger = logging.getLogger(__name__)

# --- CRUD Class for Note ---


class CRUDNote(CRUDBase[Note, NoteCreate, NoteUpdate]):
    """CRUD operations for Note model."""

    def create_with_clinician(
        self, db: Session, *, obj_in: NoteCreate, clinician_id: str
    ) -> Note:
        """
        Creates a new note record in the database.

        Args:
            db: The database session.
            obj_in: Pydantic schema with note creation data.
            clinician_id: The string ID of the clinician creating the note (author, from <PERSON>).

        Returns:
            The newly created Note ORM object.

        Raises:
            IntegrityError: If a database constraint is violated (e.g., FK).
        """
        logger.debug(
            f"CRUD: Creating note for patient {obj_in.patient_id} by clinician {clinician_id}"
        )
        create_data = obj_in.model_dump()
        db_obj = self.model(
            **create_data, clinician_id=clinician_id
        )  # Add author clinician_id
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating note for patient {obj_in.patient_id}", exc_info=True
            )
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created note ID {db_obj.id}")
        return db_obj

    def get(self, db: Session, note_id: UUID) -> Optional[Note]:
        """
        Retrieves a note by its UUID primary key with relationships loaded.

        Args:
            db: The database session.
            note_id: The UUID of the note.

        Returns:
            The Note ORM object with patient and clinician relationships loaded, or None if not found.
        """
        from sqlalchemy.orm import joinedload
        
        logger.debug(f"CRUD: Getting note ID {note_id}")
        return (
            db.query(self.model)
            .options(joinedload(self.model.patient), joinedload(self.model.clinician))
            .filter(self.model.id == note_id)
            .first()
        )

    def get_by_patient(
        self, db: Session, patient_id: str, *, skip: int = 0, limit: int = 100
    ) -> list[Note]:
        """
        Retrieves notes for a specific patient with pagination,
        ordered by creation time descending.

        Args:
            db: The database session.
            patient_id: The string ID of the patient (from Clerk).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of Note ORM objects.
        """
        logger.debug(
            f"CRUD: Getting notes for patient {patient_id}, skip={skip}, limit={limit}"
        )
        return (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_clinician(
        self, db: Session, clinician_id: str, *, skip: int = 0, limit: int = 100
    ) -> list[Note]:
        """
        Retrieves notes authored by a specific clinician with pagination,
        ordered by creation time descending.

        Args:
            db: The database session.
            clinician_id: The string ID of the clinician (author, from Clerk).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of Note ORM objects with patient and clinician relationships loaded.
        """
        from sqlalchemy.orm import joinedload
        
        logger.debug(
            f"CRUD: Getting notes by clinician {clinician_id}, skip={skip}, limit={limit}"
        )
        return (
            db.query(self.model)
            .options(joinedload(self.model.patient), joinedload(self.model.clinician))
            .filter(self.model.clinician_id == clinician_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update(
        self, db: Session, *, db_obj: Note, obj_in: Union[NoteUpdate, dict[str, Any]]
    ) -> Note:
        """
        Updates an existing note record.
        NOTE: Authorization (e.g., checking if the current user is the author)
              should be handled at the API endpoint layer.

        Args:
            db: The database session.
            db_obj: The existing Note ORM object to update.
            obj_in: Pydantic schema or dictionary with update data.

        Returns:
            The updated Note ORM object.

        Raises:
            IntegrityError: If a database constraint is violated.
        """
        logger.debug(f"CRUD: Updating note ID {db_obj.id}")
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(
                exclude_unset=True
            )  # Only update provided fields

        # Prevent changing patient or author
        update_data.pop("patient_id", None)
        update_data.pop("clinician_id", None)

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Error updating note {db_obj.id}", exc_info=True)
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Updated note ID {db_obj.id}")
        return db_obj

    def remove(self, db: Session, *, note_id: UUID) -> Optional[Note]:
        """
        Deletes a note by its UUID (Hard Delete).
        NOTE: Authorization (e.g., checking if the current user is the author)
              should be handled at the API endpoint layer.

        Args:
            db: The database session.
            note_id: The UUID of the note to delete.

        Returns:
            The deleted Note ORM object or None if not found.
        """
        logger.debug(f"CRUD: Deleting note ID {note_id}")
        obj = db.query(self.model).get(note_id)  # Use .get() for PK lookup
        if obj:
            # **Authorization Check Placeholder:**
            # In a real application, you would verify here or (preferably) in the API layer
            # if the requesting user (e.g., current_clinician.id) matches obj.clinician_id.
            # If not authorized, raise HTTPException(status_code=403, detail="Not authorized to delete this note")
            # Example (conceptual):
            # if current_user.id != obj.clinician_id:
            #     raise PermissionError("User not authorized to delete this note")

            db.delete(obj)
            db.commit()
            logger.debug(f"CRUD: Deleted note ID {note_id}")
            return obj
        logger.warning(f"CRUD: Note {note_id} not found for deletion.")
        return None


# Create an instance of CRUDNote
note = CRUDNote(Note)

# Export methods at module level for backward compatibility
create_note = note.create_with_clinician
get_note = note.get
get_notes_by_patient = note.get_by_patient
get_notes_by_clinician = note.get_by_clinician
update_note = note.update
remove_note = note.remove
