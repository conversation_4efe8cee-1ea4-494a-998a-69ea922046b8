import logging
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from sqlalchemy import func, update
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase

# Models
from app.models.notification import Notification  # Assuming model exists at this path

# Schemas
from app.schemas.notification import NotificationCreate, NotificationUpdate

logger = logging.getLogger(__name__)

# --- CRUD Class for Notification ---


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationUpdate]):
    """CRUD operations for Notification model."""

    def create(self, db: Session, *, obj_in: NotificationCreate) -> Notification:
        """
        Creates a new notification record in the database.

        Args:
            db: The database session.
            obj_in: Pydantic schema with notification creation data.

        Returns:
            The newly created Notification ORM object.

        Raises:
            IntegrityError: If a database constraint is violated (e.g., FK).
        """
        logger.debug(
            f"CRUD: Creating notification type {obj_in.notification_type.value} for clinician {obj_in.recipient_clinician_id}"
        )
        # is_read defaults to False in the model, read_at defaults to None
        db_obj = self.model(**obj_in.model_dump())
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating notification for clinician {obj_in.recipient_clinician_id}",
                exc_info=True,
            )
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created notification ID {db_obj.id}")
        return db_obj

    def get_for_clinician(
        self, db: Session, notification_id: UUID, clinician_id: str
    ) -> Optional[Notification]:
        """
        Retrieves a notification that belongs to a specific clinician.

        Args:
            db: The database session
            notification_id: The UUID of the notification
            clinician_id: The Clerk user ID of the clinician (string)

        Returns:
            The Notification ORM object if found and owned by the clinician, None otherwise
        """
        logger.debug(
            f"CRUD: Getting notification {notification_id} for clinician {clinician_id}"
        )
        return (
            db.query(self.model)
            .filter(
                self.model.id == notification_id,
                self.model.recipient_clinician_id == clinician_id,
            )
            .first()
        )

    def get_by_recipient(
        self,
        db: Session,
        recipient_clinician_id: UUID,
        *,
        is_read: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Notification]:
        """
        Retrieves notifications for a specific clinician, optionally filtered by read status,
        with pagination, ordered by creation time descending.

        Args:
            db: The database session.
            recipient_clinician_id: The UUID of the clinician.
            is_read: Optional boolean to filter by read status.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of Notification ORM objects.
        """
        logger.debug(
            f"CRUD: Getting notifications for clinician {recipient_clinician_id}, is_read={is_read}, skip={skip}, limit={limit}"
        )
        query = db.query(self.model).filter(
            self.model.recipient_clinician_id == recipient_clinician_id
        )

        if is_read is not None:
            query = query.filter(
                self.model.is_read.is_(is_read)
            )  # Use is_() for boolean comparison

        return (
            query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        )

    def get_unread_count(self, db: Session, recipient_clinician_id: UUID) -> int:
        """
        Retrieves the count of unread notifications for a specific clinician.

        Args:
            db: The database session.
            recipient_clinician_id: The UUID of the clinician.

        Returns:
            The count of unread notifications.
        """
        logger.debug(
            f"CRUD: Counting unread notifications for clinician {recipient_clinician_id}"
        )
        count = (
            db.query(func.count(self.model.id))
            .filter(self.model.recipient_clinician_id == recipient_clinician_id)
            .filter(self.model.is_read.is_(False))  # Corrected comparison
            .scalar()
        )
        logger.debug(
            f"CRUD: Found {count} unread notifications for clinician {recipient_clinician_id}"
        )
        return count or 0  # Ensure 0 is returned if count is None

    def mark_as_read(
        self, db: Session, *, notification_id: UUID
    ) -> Optional[Notification]:
        """
        Marks a specific notification as read.

        Args:
            db: The database session.
            notification_id: The UUID of the notification to mark as read.

        Returns:
            The updated Notification ORM object or None if not found.
        """
        logger.debug(f"CRUD: Marking notification {notification_id} as read")
        notification = (
            db.query(self.model).filter(self.model.id == notification_id).first()
        )
        if not notification:
            logger.warning(
                f"CRUD: Notification {notification_id} not found to mark as read."
            )
            return None

        if notification.is_read:
            logger.debug(
                f"CRUD: Notification {notification_id} already marked as read."
            )
            return notification  # Already read

        notification.is_read = True
        notification.read_at = datetime.now(timezone.utc)
        db.add(notification)
        try:
            db.commit()
            db.refresh(notification)
            logger.debug(f"CRUD: Marked notification {notification_id} as read.")
            return notification
        except Exception as e:
            db.rollback()
            logger.error(
                f"Error marking notification {notification_id} as read", exc_info=True
            )
            raise e

    def mark_all_as_read(self, db: Session, *, recipient_clinician_id: UUID) -> int:
        """
        Marks all unread notifications for a specific clinician as read.

        Args:
            db: The database session.
            recipient_clinician_id: The UUID of the clinician whose notifications to mark as read.

        Returns:
            The number of notifications updated.
        """
        logger.debug(
            f"CRUD: Marking all unread notifications as read for clinician {recipient_clinician_id}"
        )
        now_utc = datetime.now(timezone.utc)
        stmt = (
            update(self.model)
            .where(
                self.model.recipient_clinician_id == recipient_clinician_id,
                self.model.is_read.is_(False),  # Corrected comparison
            )
            .values(is_read=True, read_at=now_utc)
            .execution_options(synchronize_session=False)  # Important for bulk updates
        )
        try:
            result = db.execute(stmt)
            db.commit()
            updated_count = result.rowcount
            logger.debug(
                f"CRUD: Marked {updated_count} notifications as read for clinician {recipient_clinician_id}"
            )
            return updated_count
        except Exception as e:
            db.rollback()
            logger.error(
                f"Error marking all notifications as read for clinician {recipient_clinician_id}",
                exc_info=True,
            )
            raise e


# Create an instance of CRUDNotification
notification = CRUDNotification(Notification)

# Export methods at module level for backward compatibility
create_notification = notification.create
get_notification_for_clinician = notification.get_for_clinician
get_notifications_by_recipient = notification.get_by_recipient
get_unread_notification_count = notification.get_unread_count
mark_notification_as_read = notification.mark_as_read
mark_all_notifications_as_read = notification.mark_all_as_read
