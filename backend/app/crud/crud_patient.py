# backend\app\crud\crud_patient.py

import logging
import uuid
from typing import Any, Optional, Union

from sqlalchemy import or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.clinic import Clinic
from app.models.clinician import Clinician
from app.models.patient import Patient
from app.schemas.patient import PatientCreate, PatientUpdate

logger = logging.getLogger(__name__)


class CRUDPatient(CRUDBase[Patient, PatientCreate, PatientUpdate]):
    """CRUD operations for Patient model."""

    def get_by_email(self, db: Session, *, email: str) -> Optional[Patient]:
        """
        Get a patient by their email address.

        Args:
            db: The database session.
            email: The email address of the patient.

        Returns:
            The Patient ORM object or None if not found.
        """
        return db.query(Patient).filter(Patient.email == email).first()

    def get_multi(
        self,
        db: Session,
        *,
        clinician_id: Optional[str] = None,
        search_query: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Patient]:
        """
        Get multiple patients with pagination, optionally filtered by clinician and search query.

        Args:
            db: The database session.
            clinician_id: Optional string ID of the clinician to filter patients by.
            search_query: Optional string to filter patients by (matches name or email).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of Patient ORM objects.
        """
        query = db.query(self.model)

        if clinician_id:
            query = query.join(Patient.clinicians).filter(Clinician.id == clinician_id)

        if search_query:
            search_term = f"%{search_query}%"
            query = query.filter(
                or_(
                    Patient.first_name.ilike(search_term),
                    Patient.last_name.ilike(search_term),
                    Patient.email.ilike(search_term),
                )
            )

        return query.offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: PatientCreate) -> Patient:
        """
        Create a new patient record and optionally assign clinicians.

        Args:
            db: The database session.
            obj_in: Pydantic schema with patient creation data, potentially including 'clinician_ids'.

        Returns:
            The newly created Patient ORM object.

        Raises:
            IntegrityError: If a unique constraint (like email) is violated.
            ValueError: If a provided clinician_id does not exist.
        """
        create_data = obj_in.model_dump(exclude={"clinician_ids"})
        clinician_ids = getattr(obj_in, "clinician_ids", None)

        patient_model_fields = {column.name for column in Patient.__table__.columns}
        filtered_create_data = {
            k: v for k, v in create_data.items() if k in patient_model_fields
        }

        db_obj = Patient(**filtered_create_data)
        db.add(db_obj)

        if clinician_ids:
            assigned_clinicians = (
                db.query(Clinician).filter(Clinician.id.in_(clinician_ids)).all()
            )
            if len(assigned_clinicians) != len(set(clinician_ids)):
                found_ids = {c.id for c in assigned_clinicians}
                missing_ids = set(clinician_ids) - found_ids
                db.rollback()
                raise ValueError(f"Clinician(s) not found with IDs: {missing_ids}")
            db_obj.clinicians.extend(assigned_clinicians)

        try:
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Error creating patient: {e}", exc_info=True)
            raise

    def update(
        self,
        db: Session,
        *,
        db_obj: Patient,
        obj_in: Union[PatientUpdate, dict[str, Any]],
    ) -> Patient:
        """
        Update an existing patient record.
        Note: This function does NOT handle updating clinician assignments.
              Use the clinician assignment functions for that.

        Args:
            db: The database session.
            db_obj: The existing Patient ORM object to update.
            obj_in: Pydantic schema or dictionary with update data.

        Returns:
            The updated Patient ORM object.
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # Ensure we only update valid model fields
        patient_model_fields = {column.name for column in Patient.__table__.columns}
        filtered_update_data = {
            k: v for k, v in update_data.items() if k in patient_model_fields
        }

        for field, value in filtered_update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: str) -> Optional[Patient]:
        """
        Delete a patient by their ID (Clerk user ID string).
        Note: This performs a hard delete. Associations in the clinician_patient_association table
              should be removed automatically if ondelete='CASCADE' is set on the foreign key.

        Args:
            db: The database session.
            id: The string ID of the patient (from Clerk).

        Returns:
            The deleted Patient ORM object or None if not found.
        """
        obj = db.query(Patient).get(id)
        if obj:
            db.delete(obj)
            db.commit()
            return obj
        return None

    def is_patient_associated_with_clinic(
        self, db: Session, *, patient_id: str, clinic_id: uuid.UUID
    ) -> bool:
        """
        Check if a specific patient is associated with a specific clinic.
        First checks for a direct 'clinic_id' FK, then falls back to checking via assigned clinicians.

        Args:
            db: The database session.
            patient_id: The patient's ID.
            clinic_id: The clinic's UUID.

        Returns:
            True if the patient is associated with the clinic, False otherwise.
        """
        patient = self.get(db, id=patient_id)
        if not patient:
            logger.warning(
                f"CRUD Patient: Could not find patient {patient_id} for clinic association check."
            )
            return False

        if hasattr(patient, "clinic_id") and patient.clinic_id == clinic_id:
            logger.debug(
                f"RBAC Check: Patient {patient_id} directly associated with Clinic {clinic_id}."
            )
            return True

        try:
            query = (
                db.query(Patient)
                .join(Patient.clinicians)
                .join(Clinician.clinics)
                .filter(Patient.id == patient_id, Clinic.id == clinic_id)
            )
            is_associated_indirectly = db.query(query.exists()).scalar()
            if is_associated_indirectly:
                logger.debug(
                    f"RBAC Check: Patient {patient_id} indirectly associated with Clinic {clinic_id} via clinician."
                )
                return True
        except ImportError:
            logger.error(
                "Could not import models needed for indirect patient-clinic check."
            )
        except Exception as e:
            logger.error(
                f"Error checking indirect patient-clinic association for patient {patient_id} and clinic {clinic_id}: {e}",
                exc_info=True,
            )

        logger.debug(
            f"RBAC Check: Patient {patient_id} is NOT associated with Clinic {clinic_id}."
        )
        return False

    def get_or_create(self, db: Session, *, email: str, create_data: dict) -> Patient:
        """Get existing patient or create new one atomically."""
        logger.info(
            f"Attempting get_or_create for email: {email} with data: {create_data}"
        )

        # First try to get by email
        patient = (
            db.query(Patient).filter(Patient.email == email).with_for_update().first()
        )
        if patient:
            logger.info(f"Found existing patient: {patient.__dict__}")
            return patient

        # Create new patient if doesn't exist
        try:
            # Ensure we're only using valid model fields
            patient_model_fields = {column.name for column in Patient.__table__.columns}
            filtered_create_data = {
                k: v
                for k, v in create_data.items()
                if k in patient_model_fields
                and v is not None  # Only include non-None values
            }

            logger.info(
                f"Creating new patient with filtered data: {filtered_create_data}"
            )

            patient = Patient(**filtered_create_data)
            db.add(patient)
            db.commit()
            db.refresh(patient)

            logger.info(f"Created new patient: {patient.__dict__}")
            return patient
        except IntegrityError as e:
            logger.error(f"IntegrityError during patient creation: {e}")
            db.rollback()
            # If we got an integrity error, try one more time to get the patient
            patient = db.query(Patient).filter(Patient.email == email).first()
            if patient:
                return patient
            raise

    def get_by_full_name(
        self, db: Session, *, first_name: str, last_name: str
    ) -> Optional[Patient]:
        """
        Get a patient by their full name (case-insensitive exact match).
        Returns the Patient ORM object or None if not found. If multiple found, returns None (ambiguous).
        """
        import logging

        logger = logging.getLogger(__name__)
        logger.debug(
            f"Name resolution: Looking up patient with first_name='{first_name}', last_name='{last_name}'"
        )
        matches = (
            db.query(Patient)
            .filter(
                Patient.first_name.ilike(first_name), Patient.last_name.ilike(last_name)
            )
            .all()
        )
        logger.debug(
            f"Name resolution: Found {len(matches)} matches for first_name='{first_name}', last_name='{last_name}'"
        )
        if len(matches) == 1:
            logger.debug(f"Name resolution: Resolved patient_id={matches[0].id}")
            return matches[0]
        return None

    def get_patient_height(self, db: Session, *, patient_id: str) -> Optional[float]:
        """
        Retrieve the height (in cm) for a patient by their ID.

        Args:
            db: The database session.
            patient_id: The ID of the patient.

        Returns:
            The height in centimeters (float) if found, else None.
        """
        patient = self.get(db, id=patient_id)
        if patient:
            return patient.height_cm
        return None

    def get_patient_weight(self, db: Session, *, patient_id: str) -> Optional[float]:
        """
        Retrieve the weight (in kg) for a patient by their ID.

        Args:
            db: The database session.
            patient_id: The ID of the patient.

        Returns:
            The weight in kilograms (float) if found, else None.
        """
        patient = self.get(db, id=patient_id)
        if patient:
            return patient.weight_kg
        return None

    def get_primary_clinician(
        self, db: Session, patient_id: str
    ) -> Optional[Clinician]:
        """
        Get the primary clinician for a patient.
        Currently returns the first assigned clinician if multiple exist.

        Args:
            db: The database session
            patient_id: The patient's ID (Clerk user ID)

        Returns:
            Optional[Clinician]: The primary clinician or None if no clinicians assigned
        """
        patient = self.get(db, id=patient_id)
        if not patient:
            return None

        # Get the first assigned clinician
        # TODO: Add proper primary clinician logic if needed
        return (
            db.query(Clinician)
            .join(Patient.clinicians)
            .filter(Patient.id == patient_id)
            .first()
        )

    def get_goal_weight_progress(self, db: Session, *, patient_id: str) -> dict[str, Any]:
        """
        Calculate goal weight progress for a patient.
        
        Args:
            db: The database session
            patient_id: The patient's ID (Clerk user ID)
            
        Returns:
            Dict containing goal weight progress information
        """
        from datetime import date
        from app.crud.crud_weight_log import get_latest_weight_log_by_patient
        
        patient = self.get(db, id=patient_id)
        if not patient:
            return {}
            
        # Get the most recent weight log
        latest_weight_log = get_latest_weight_log_by_patient(db=db, patient_id=patient_id)
        current_weight_kg = latest_weight_log.weight_kg if latest_weight_log else None
        
        progress_data = {
            "has_goal": bool(patient.goal_weight_kg),
            "goal_weight_kg": patient.goal_weight_kg,
            "current_weight_kg": current_weight_kg,
            "progress_percentage": None,
            "progress_to_goal_kg": None,
            "is_goal_achieved": False,
            "days_to_goal": None,
            "trend": None
        }
        
        if not patient.goal_weight_kg or not current_weight_kg:
            return progress_data
            
        # Calculate progress
        progress_to_goal_kg = patient.goal_weight_kg - current_weight_kg
        progress_data["progress_to_goal_kg"] = progress_to_goal_kg
        progress_data["is_goal_achieved"] = abs(progress_to_goal_kg) < 0.5  # Within 0.5kg of goal
        
        # Calculate progress percentage (assuming weight loss goal)
        if latest_weight_log and len(patient.weight_logs) > 1:
            # Get starting weight (earliest weight log)
            starting_weight = min(patient.weight_logs, key=lambda x: x.created_at).weight_kg
            if starting_weight != patient.goal_weight_kg:
                total_to_lose = starting_weight - patient.goal_weight_kg
                weight_lost = starting_weight - current_weight_kg
                if total_to_lose > 0:
                    progress_percentage = min(100.0, max(0.0, (weight_lost / total_to_lose) * 100))
                    progress_data["progress_percentage"] = round(progress_percentage, 1)
        
        # Calculate days to goal
        if patient.goal_weight_date:
            today = date.today()
            days_to_goal = (patient.goal_weight_date - today).days
            progress_data["days_to_goal"] = days_to_goal
            
        # Calculate trend (simplified)
        if len(patient.weight_logs) >= 2:
            recent_logs = sorted(patient.weight_logs, key=lambda x: x.created_at, reverse=True)[:2]
            if len(recent_logs) == 2:
                weight_change = recent_logs[0].weight_kg - recent_logs[1].weight_kg
                if weight_change < -0.5:
                    progress_data["trend"] = "losing"
                elif weight_change > 0.5:
                    progress_data["trend"] = "gaining" 
                else:
                    progress_data["trend"] = "stable"
                    
        return progress_data


# Create an instance of CRUDPatient
patient = CRUDPatient(Patient)

# Export methods at module level for backward compatibility
get = patient.get
get_by_email = patient.get_by_email
get_multi = patient.get_multi
create = patient.create
update = patient.update
remove = patient.remove
is_patient_associated_with_clinic = patient.is_patient_associated_with_clinic
get_or_create = patient.get_or_create
get_by_full_name = patient.get_by_full_name
get_patient_height = patient.get_patient_height
get_patient_weight = patient.get_patient_weight
get_primary_clinician = patient.get_primary_clinician
get_goal_weight_progress = patient.get_goal_weight_progress
