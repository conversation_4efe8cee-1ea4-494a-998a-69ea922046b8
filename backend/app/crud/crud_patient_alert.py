import logging
from datetime import datetime, timezone
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy import case

from sqlalchemy.orm import joinedload

from app.crud.base import CRUDBase
from app.models.clinician import clinician_patient_association  # Needed for join
from app.models.patient import Patient  # Needed for join

# Models
from app.models.patient_alert import PatientAlert

# Schemas
from app.schemas.patient_alert import (
    PatientAlertCreate,
    PatientAlertStatus,
    PatientAlertUpdate,
)

logger = logging.getLogger(__name__)

# --- CRUD Class for PatientAlert ---


class CRUDPatientAlert(CRUDBase[Patient<PERSON><PERSON><PERSON>, PatientAlertCreate, PatientAlertUpdate]):
    """CRUD operations for PatientAlert model."""

    def create(self, db: Session, *, obj_in: PatientAlertCreate) -> PatientAlert:
        """
        Creates a new patient alert record in the database.

        Args:
            db: The database session.
            obj_in: Pydantic schema with patient alert creation data.

        Returns:
            The newly created PatientAlert ORM object.

        Raises:
            IntegrityError: If a database constraint is violated (e.g., FK).
        """
        logger.debug(
            f"CRUD: Creating patient alert type {obj_in.alert_type.value} for patient {obj_in.patient_id}"
        )
        db_obj = self.model(**obj_in.model_dump())
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating patient alert for patient {obj_in.patient_id}",
                exc_info=True,
            )
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created patient alert ID {db_obj.id}")
        return db_obj

    def get(self, db: Session, alert_id: UUID) -> Optional[PatientAlert]:
        """
        Retrieves a patient alert by its UUID primary key.

        Args:
            db: The database session.
            alert_id: The UUID of the patient alert.

        Returns:
            The PatientAlert ORM object or None if not found.
        """
        logger.debug(f"CRUD: Getting patient alert ID {alert_id}")
        return db.query(self.model).filter(self.model.id == alert_id).first()

    def get_by_patient(
        self,
        db: Session,
        patient_id: str,
        *,
        status: Optional[PatientAlertStatus] = PatientAlertStatus.NEW,
        skip: int = 0,
        limit: int = 100,
    ) -> list[PatientAlert]:
        """
        Retrieves alerts for a specific patient, optionally filtered by status,
        with pagination, ordered by creation time descending.

        Args:
            db: The database session.
            patient_id: The string ID of the patient (from Clerk).
            status: Optional status to filter alerts by (defaults to NEW).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of PatientAlert ORM objects.
        """
        logger.debug(
            f"CRUD: Getting alerts for patient {patient_id}, status={status}, skip={skip}, limit={limit}"
        )
        query = db.query(self.model).filter(self.model.patient_id == patient_id)

        if status:
            query = query.filter(self.model.status == status)

        # Sort by severity first (critical > high > medium > low), then by created_at
        severity_order = case(
            (self.model.severity == "critical", 1),
            (self.model.severity == "high", 2),
            (self.model.severity == "medium", 3),
            (self.model.severity == "low", 4),
            else_=5
        )
        
        return (
            query.order_by(severity_order, self.model.created_at.desc()).offset(skip).limit(limit).all()
        )

    def get_by_clinician(
        self,
        db: Session,
        clinician_id: str,
        *,
        status: Optional[PatientAlertStatus] = PatientAlertStatus.NEW,
        skip: int = 0,
        limit: int = 100,
    ) -> list[PatientAlert]:
        """
        Retrieves alerts for patients associated with a specific clinician,
        optionally filtered by status, with pagination, ordered by creation time descending.

        Args:
            db: The database session.
            clinician_id: The string ID of the clinician (from Clerk).
            status: Optional status to filter alerts by (defaults to NEW).
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of PatientAlert ORM objects.
        """
        logger.debug(
            f"CRUD: Getting alerts for clinician {clinician_id}, status={status}, skip={skip}, limit={limit}"
        )

        # Join patient_alert with patient and clinician_patient_association
        # to find alerts for patients associated with the specified clinician
        query = (
            db.query(self.model)
            .join(Patient, self.model.patient_id == Patient.id)
            .join(
                clinician_patient_association,
                Patient.id == clinician_patient_association.c.patient_id,
            )
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
        )

        if status:
            query = query.filter(self.model.status == status)

        # Sort by severity first (critical > high > medium > low), then by created_at
        severity_order = case(
            (self.model.severity == "critical", 1),
            (self.model.severity == "high", 2),
            (self.model.severity == "medium", 3),
            (self.model.severity == "low", 4),
            else_=5
        )
        
        return (
            query.options(joinedload(self.model.patient))
            .order_by(severity_order, self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status(
        self,
        db: Session,
        *,
        alert_id: UUID,
        status: PatientAlertStatus,
        resolved_by_clinician_id: Optional[str] = None,
    ) -> Optional[PatientAlert]:
        """
        Updates the status of a specific patient alert.
        Sets resolved_at and resolved_by_clinician_id if status is RESOLVED.

        Args:
            db: The database session.
            alert_id: The UUID of the alert to update.
            status: The new status for the alert.
            resolved_by_clinician_id: The string ID of the clinician resolving the alert (from Clerk, required if status is RESOLVED).

        Returns:
            The updated PatientAlert ORM object or None if not found.

        Raises:
            ValueError: If status is RESOLVED but resolved_by_clinician_id is not provided.
        """
        logger.debug(f"CRUD: Updating status of alert {alert_id} to {status.value}")
        alert = db.query(self.model).filter(self.model.id == alert_id).first()
        if not alert:
            logger.warning(f"CRUD: Alert {alert_id} not found for status update.")
            return None

        alert.status = status
        if status == PatientAlertStatus.RESOLVED:
            if not resolved_by_clinician_id:
                # Rollback potential status change before raising
                db.rollback()
                raise ValueError(
                    "resolved_by_clinician_id is required when setting status to RESOLVED"
                )
            alert.resolved_at = datetime.now(timezone.utc)
            alert.resolved_by_clinician_id = resolved_by_clinician_id
        elif (
            alert.resolved_at is not None
        ):  # Clear resolution fields if status changes from RESOLVED
            alert.resolved_at = None
            alert.resolved_by_clinician_id = None

        db.add(alert)
        try:
            db.commit()
            db.refresh(alert)
            logger.debug(f"CRUD: Updated status for alert ID {alert_id}")
            return alert
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating status for alert {alert_id}", exc_info=True)
            raise e

    def update(
        self,
        db: Session,
        *,
        db_obj: PatientAlert,
        obj_in: Union[PatientAlertUpdate, dict[str, Any]],
    ) -> PatientAlert:
        """
        Updates an existing patient alert record using provided data.
        Note: For status changes, prefer using `update_status`
              as this function doesn't automatically handle resolved_at/by fields.

        Args:
            db: The database session.
            db_obj: The existing PatientAlert ORM object to update.
            obj_in: Pydantic schema or dictionary with update data.

        Returns:
            The updated PatientAlert ORM object.

        Raises:
            IntegrityError: If a database constraint is violated.
        """
        logger.debug(f"CRUD: Updating patient alert ID {db_obj.id}")
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(
                exclude_unset=True
            )  # Only update provided fields

        # Be careful about updating status here without handling resolved fields
        if (
            "status" in update_data
            and update_data["status"] == PatientAlertStatus.RESOLVED
        ):
            logger.warning(
                f"CRUD: Updating status to RESOLVED via generic update for alert {db_obj.id}. Consider using update_status instead."
            )
            # Manually set resolved fields if needed, or enforce usage of the specific function
            if "resolved_at" not in update_data:
                update_data["resolved_at"] = datetime.now(timezone.utc)
            # resolved_by_clinician_id should ideally be passed in obj_in if using this method

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Error updating patient alert {db_obj.id}", exc_info=True)
            raise e
        db.refresh(db_obj)
        logger.debug(f"CRUD: Updated patient alert ID {db_obj.id}")
        return db_obj


# Create an instance of CRUDPatientAlert
patient_alert = CRUDPatientAlert(PatientAlert)

# Export methods at module level for backward compatibility
create_patient_alert = patient_alert.create
get_patient_alert = patient_alert.get
get_patient_alerts_by_patient = patient_alert.get_by_patient
get_patient_alerts_by_clinician = patient_alert.get_by_clinician
update_patient_alert_status = patient_alert.update_status
update_patient_alert = patient_alert.update
