import logging
import uuid
from datetime import datetime
from typing import Any, List, Optional

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session, joinedload

from app.crud.base import CRUDBase
from app.models.patient_education_assignment import PatientEducationAssignment, AssignmentPriority, AssignmentStatus
from app.schemas.patient_education_assignment import PatientEducationAssignmentCreate, PatientEducationAssignmentUpdate

logger = logging.getLogger(__name__)


class CRUDPatientEducationAssignment(CRUDBase[PatientEducationAssignment, PatientEducationAssignmentCreate, PatientEducationAssignmentUpdate]):
    """CRUD operations for PatientEducationAssignment model."""

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        patient_id: Optional[str] = None,
        assigned_by: Optional[str] = None,
        material_id: Optional[uuid.UUID] = None,
        status: Optional[AssignmentStatus] = None,
        priority: Optional[AssignmentPriority] = None,
        overdue_only: bool = False
    ) -> List[PatientEducationAssignment]:
        """
        Get multiple education assignments with optional filtering.

        Args:
            db: The database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            patient_id: Filter by patient ID.
            assigned_by: Filter by assigning clinician ID.
            material_id: Filter by material ID.
            status: Filter by assignment status.
            priority: Filter by assignment priority.
            overdue_only: Filter for overdue assignments only.

        Returns:
            List of PatientEducationAssignment ORM objects.
        """
        query = db.query(PatientEducationAssignment).options(
            joinedload(PatientEducationAssignment.patient),
            joinedload(PatientEducationAssignment.material),
            joinedload(PatientEducationAssignment.assigned_by_clinician),
            joinedload(PatientEducationAssignment.progress)
        )

        # Apply filters
        if patient_id:
            query = query.filter(PatientEducationAssignment.patient_id == patient_id)

        if assigned_by:
            query = query.filter(PatientEducationAssignment.assigned_by == assigned_by)

        if material_id:
            query = query.filter(PatientEducationAssignment.material_id == material_id)

        if status:
            query = query.filter(PatientEducationAssignment.status == status)

        if priority:
            query = query.filter(PatientEducationAssignment.priority == priority)

        if overdue_only:
            now = datetime.utcnow()
            query = query.filter(
                and_(
                    PatientEducationAssignment.due_date.isnot(None),
                    PatientEducationAssignment.due_date < now,
                    PatientEducationAssignment.status.in_([
                        AssignmentStatus.ASSIGNED,
                        AssignmentStatus.VIEWED,
                        AssignmentStatus.IN_PROGRESS
                    ])
                )
            )

        return query.order_by(
            PatientEducationAssignment.assigned_at.desc()
        ).offset(skip).limit(limit).all()

    def get_by_patient(
        self,
        db: Session,
        *,
        patient_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[AssignmentStatus] = None,
        priority: Optional[AssignmentPriority] = None
    ) -> List[PatientEducationAssignment]:
        """
        Get assignments for a specific patient.

        Args:
            db: The database session.
            patient_id: Patient ID.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            status: Optional status filter.
            priority: Optional priority filter.

        Returns:
            List of PatientEducationAssignment ORM objects.
        """
        query = db.query(PatientEducationAssignment).options(
            joinedload(PatientEducationAssignment.material),
            joinedload(PatientEducationAssignment.assigned_by_clinician),
            joinedload(PatientEducationAssignment.progress)
        ).filter(PatientEducationAssignment.patient_id == patient_id)

        if status:
            query = query.filter(PatientEducationAssignment.status == status)

        if priority:
            query = query.filter(PatientEducationAssignment.priority == priority)

        return query.order_by(
            PatientEducationAssignment.priority.desc(),
            PatientEducationAssignment.assigned_at.desc()
        ).offset(skip).limit(limit).all()

    def get_by_clinician(
        self,
        db: Session,
        *,
        assigned_by: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[AssignmentStatus] = None
    ) -> List[PatientEducationAssignment]:
        """
        Get assignments made by a specific clinician.

        Args:
            db: The database session.
            assigned_by: Clinician ID who made the assignments.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            status: Optional status filter.

        Returns:
            List of PatientEducationAssignment ORM objects.
        """
        query = db.query(PatientEducationAssignment).options(
            joinedload(PatientEducationAssignment.patient),
            joinedload(PatientEducationAssignment.material),
            joinedload(PatientEducationAssignment.progress)
        ).filter(PatientEducationAssignment.assigned_by == assigned_by)

        if status:
            query = query.filter(PatientEducationAssignment.status == status)

        return query.order_by(
            PatientEducationAssignment.assigned_at.desc()
        ).offset(skip).limit(limit).all()

    def get_existing_assignment(
        self,
        db: Session,
        *,
        patient_id: str,
        material_id: uuid.UUID
    ) -> Optional[PatientEducationAssignment]:
        """
        Check if a patient already has an assignment for a specific material.

        Args:
            db: The database session.
            patient_id: Patient ID.
            material_id: Material ID.

        Returns:
            The existing PatientEducationAssignment or None.
        """
        return db.query(PatientEducationAssignment).filter(
            PatientEducationAssignment.patient_id == patient_id,
            PatientEducationAssignment.material_id == material_id
        ).first()

    def get_overdue_assignments(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        clinician_id: Optional[str] = None
    ) -> List[PatientEducationAssignment]:
        """
        Get assignments that are overdue.

        Args:
            db: The database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            clinician_id: Optional filter by assigning clinician.

        Returns:
            List of overdue PatientEducationAssignment ORM objects.
        """
        now = datetime.utcnow()
        query = db.query(PatientEducationAssignment).options(
            joinedload(PatientEducationAssignment.patient),
            joinedload(PatientEducationAssignment.material)
        ).filter(
            and_(
                PatientEducationAssignment.due_date.isnot(None),
                PatientEducationAssignment.due_date < now,
                PatientEducationAssignment.status.in_([
                    AssignmentStatus.ASSIGNED,
                    AssignmentStatus.VIEWED,
                    AssignmentStatus.IN_PROGRESS
                ])
            )
        )

        if clinician_id:
            query = query.filter(PatientEducationAssignment.assigned_by == clinician_id)

        return query.order_by(
            PatientEducationAssignment.due_date.asc()
        ).offset(skip).limit(limit).all()

    def update_status(
        self,
        db: Session,
        *,
        id: uuid.UUID,
        status: AssignmentStatus
    ) -> Optional[PatientEducationAssignment]:
        """
        Update the status of an assignment.

        Args:
            db: The database session.
            id: Assignment ID.
            status: New status.

        Returns:
            The updated PatientEducationAssignment or None if not found.
        """
        db_obj = self.get(db, id=id)
        if db_obj:
            db_obj.status = status
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def mark_overdue(self, db: Session) -> int:
        """
        Mark assignments as overdue if they are past their due date.

        Args:
            db: The database session.

        Returns:
            Number of assignments marked as overdue.
        """
        now = datetime.utcnow()
        result = db.query(PatientEducationAssignment).filter(
            and_(
                PatientEducationAssignment.due_date.isnot(None),
                PatientEducationAssignment.due_date < now,
                PatientEducationAssignment.status.in_([
                    AssignmentStatus.ASSIGNED,
                    AssignmentStatus.VIEWED,
                    AssignmentStatus.IN_PROGRESS
                ])
            )
        ).update(
            {PatientEducationAssignment.status: AssignmentStatus.OVERDUE},
            synchronize_session=False
        )
        db.commit()
        return result

    def count(
        self,
        db: Session,
        *,
        patient_id: Optional[str] = None,
        assigned_by: Optional[str] = None,
        status: Optional[AssignmentStatus] = None,
        overdue_only: bool = False
    ) -> int:
        """
        Count assignments with optional filtering.

        Args:
            db: The database session.
            patient_id: Filter by patient ID.
            assigned_by: Filter by assigning clinician ID.
            status: Filter by assignment status.
            overdue_only: Filter for overdue assignments only.

        Returns:
            Count of matching assignments.
        """
        query = db.query(PatientEducationAssignment)

        if patient_id:
            query = query.filter(PatientEducationAssignment.patient_id == patient_id)

        if assigned_by:
            query = query.filter(PatientEducationAssignment.assigned_by == assigned_by)

        if status:
            query = query.filter(PatientEducationAssignment.status == status)

        if overdue_only:
            now = datetime.utcnow()
            query = query.filter(
                and_(
                    PatientEducationAssignment.due_date.isnot(None),
                    PatientEducationAssignment.due_date < now,
                    PatientEducationAssignment.status.in_([
                        AssignmentStatus.ASSIGNED,
                        AssignmentStatus.VIEWED,
                        AssignmentStatus.IN_PROGRESS
                    ])
                )
            )

        return query.count()

    def get_by_patient_and_material(
        self,
        db: Session,
        *,
        patient_id: str,
        material_id: uuid.UUID,
    ) -> Optional[PatientEducationAssignment]:
        """
        Get an assignment by patient and material IDs.

        Args:
            db: Database session
            patient_id: Patient ID
            material_id: Education material ID

        Returns:
            PatientEducationAssignment object or None
        """
        return self.get_existing_assignment(db=db, patient_id=patient_id, material_id=material_id)

    def validate_material_assignment(
        self,
        db: Session,
        *,
        material_id: uuid.UUID,
        patient_id: str,
        clinician_id: str,
        user_role: str,
    ) -> bool:
        """
        Validate that a material can be assigned to a patient.

        Args:
            db: Database session
            material_id: Education material ID
            patient_id: Patient ID
            clinician_id: Assigning clinician ID
            user_role: User role making the assignment

        Returns:
            True if assignment is valid
        """
        from app.models.education_material import EducationMaterial
        from app.models.patient import Patient
        from app.models.clinician import Clinician

        # Check if material exists and is active
        material = db.query(EducationMaterial).filter(
            EducationMaterial.id == material_id,
            EducationMaterial.is_active == True
        ).first()

        if not material:
            return False

        # Check if patient exists
        patient = db.query(Patient).filter(Patient.id == patient_id).first()
        if not patient:
            return False

        # Admins can assign any material
        if user_role == "admin":
            return True

        # Check if clinician can assign this material
        if user_role == "clinician":
            # Can assign public materials or materials from their clinic
            if material.is_public:
                return True
            
            # Check if clinician is from the same clinic as the material
            clinician = db.query(Clinician).filter(Clinician.id == clinician_id).first()
            if clinician and material.clinic_id and hasattr(clinician, 'clinic_id') and clinician.clinic_id == material.clinic_id:
                return True

        return False

    def get_multi_filtered(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        patient_id: Optional[str] = None,
        material_id: Optional[uuid.UUID] = None,
        status: Optional[AssignmentStatus] = None,
        priority: Optional[AssignmentPriority] = None,
        assigned_by: Optional[str] = None,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[uuid.UUID] = None,
    ) -> List[PatientEducationAssignment]:
        """
        Get education assignments with filtering and access control.

        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            patient_id: Filter by patient ID
            status: Filter by assignment status
            priority: Filter by assignment priority
            assigned_by: Filter by assigning clinician ID
            user_id: Current user ID
            user_role: Current user role
            user_clinic_id: Current user's clinic ID

        Returns:
            List of PatientEducationAssignment objects
        """
        # Use existing get_multi method with additional access control
        query_args = {
            "skip": skip,
            "limit": limit,
            "patient_id": patient_id,
            "material_id": material_id,
            "status": status,
            "priority": priority,
            "assigned_by": assigned_by,
        }

        # Apply access control based on user role
        if user_role == "patient":
            # Patients can only see their own assignments
            query_args["patient_id"] = user_id
        elif user_role == "clinician":
            # Clinicians see assignments they made + their clinic's patients (simplified for now)
            if not assigned_by:
                query_args["assigned_by"] = user_id

        return self.get_multi(db=db, **query_args)

    def can_access_assignment(
        self,
        *,
        assignment: PatientEducationAssignment,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[uuid.UUID] = None,
    ) -> bool:
        """
        Check if a user can access a specific assignment.

        Args:
            assignment: The education assignment
            user_id: Current user ID
            user_role: Current user role
            user_clinic_id: Current user's clinic ID

        Returns:
            True if user can access the assignment
        """
        # Admins can access everything
        if user_role == "admin":
            return True

        # Patients can access their own assignments
        if user_role == "patient" and assignment.patient_id == user_id:
            return True

        # Clinicians can access assignments they made
        if user_role == "clinician" and assignment.assigned_by == user_id:
            return True

        # TODO: Add clinic-based access when clinic relationships are established

        return False

    def can_modify_assignment(
        self,
        *,
        assignment: PatientEducationAssignment,
        user_id: str,
        user_role: str,
        user_clinic_id: Optional[uuid.UUID] = None,
    ) -> bool:
        """
        Check if a user can modify a specific assignment.

        Args:
            assignment: The education assignment
            user_id: Current user ID
            user_role: Current user role
            user_clinic_id: Current user's clinic ID

        Returns:
            True if user can modify the assignment
        """
        # Admins can modify everything
        if user_role == "admin":
            return True

        # Clinicians can modify assignments they made
        if user_role == "clinician" and assignment.assigned_by == user_id:
            return True

        return False

    def get_patient_dashboard(
        self,
        db: Session,
        *,
        patient_id: str,
        requesting_user_role: str,
        requesting_user_clinic_id: Optional[uuid.UUID] = None,
    ) -> dict:
        """
        Get dashboard data for a patient's education assignments.

        Args:
            db: Database session
            patient_id: Patient ID
            requesting_user_role: Role of the user requesting the dashboard
            requesting_user_clinic_id: Clinic ID of the requesting user

        Returns:
            Dictionary with dashboard data
        """
        # Get all assignments for the patient
        assignments = self.get_by_patient(db=db, patient_id=patient_id)

        # Calculate statistics
        total_assignments = len(assignments)
        completed_assignments = len([a for a in assignments if a.status == AssignmentStatus.COMPLETED])
        in_progress_assignments = len([a for a in assignments if a.status == AssignmentStatus.IN_PROGRESS])
        overdue_assignments = len([a for a in assignments if a.status == AssignmentStatus.OVERDUE])

        # Get recent assignments (last 5)
        recent_assignments = assignments[:5]

        # Get assignments by priority
        high_priority = len([a for a in assignments if a.priority == AssignmentPriority.HIGH])
        urgent_priority = len([a for a in assignments if a.priority == AssignmentPriority.URGENT])

        return {
            "patient_id": patient_id,
            "total_assignments": total_assignments,
            "completed_assignments": completed_assignments,
            "in_progress_assignments": in_progress_assignments,
            "overdue_assignments": overdue_assignments,
            "completion_rate": completed_assignments / total_assignments if total_assignments > 0 else 0,
            "high_priority_count": high_priority,
            "urgent_priority_count": urgent_priority,
            "recent_assignments": [
                {
                    "id": str(a.id),
                    "material_title": a.material.title,
                    "material_type": a.material.type.value,
                    "status": a.status.value,
                    "priority": a.priority.value,
                    "assigned_at": a.assigned_at.isoformat(),
                    "due_date": a.due_date.isoformat() if a.due_date else None,
                }
                for a in recent_assignments
            ],
        }

    def get_analytics(
        self,
        db: Session,
        *,
        clinic_id: Optional[uuid.UUID] = None,
        user_role: str,
    ) -> dict:
        """
        Get analytics for education assignments.

        Args:
            db: Database session
            clinic_id: Optional clinic ID to filter analytics
            user_role: User role requesting analytics

        Returns:
            Dictionary with analytics data
        """
        query = db.query(PatientEducationAssignment)

        # Apply clinic filtering if provided
        if clinic_id and user_role != "admin":
            # Filter by clinic when clinic relationships are established
            pass

        # Total assignments
        total_assignments = query.count()

        # Assignments by status
        status_counts = {}
        for status in AssignmentStatus:
            count = query.filter(PatientEducationAssignment.status == status).count()
            status_counts[status.value] = count

        # Assignments by priority
        priority_counts = {}
        for priority in AssignmentPriority:
            count = query.filter(PatientEducationAssignment.priority == priority).count()
            priority_counts[priority.value] = count

        # Recent assignments (last 30 days)
        thirty_days_ago = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        thirty_days_ago = thirty_days_ago.replace(day=max(1, thirty_days_ago.day - 30))
        
        recent_assignments = query.filter(
            PatientEducationAssignment.assigned_at >= thirty_days_ago
        ).count()

        # Completion rate
        completed_count = status_counts.get(AssignmentStatus.COMPLETED.value, 0)
        completion_rate = completed_count / total_assignments if total_assignments > 0 else 0

        return {
            "total_assignments": total_assignments,
            "assignments_by_status": status_counts,
            "assignments_by_priority": priority_counts,
            "recent_assignments_30d": recent_assignments,
            "completion_rate": completion_rate,
        }


# Create the crud instance
patient_education_assignment = CRUDPatientEducationAssignment(PatientEducationAssignment)