import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import select
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.scraped_page import ScrapedPage
from app.schemas.scraped_page import ScrapedPageCreate


# Define CRUD class for ScrapedPage
class CRUDScrapedPage(CRUDBase[ScrapedPage, ScrapedPageCreate, ScrapedPageCreate]):
    """CRUD operations for ScrapedPage model."""

    def get_by_url_and_clinic(
        self, db: Session, *, clinic_id: uuid.UUID, source_url: str
    ) -> Optional[ScrapedPage]:
        """
        Get a scraped page by its source URL and clinic ID.
        """
        return db.execute(
            select(self.model).where(
                self.model.clinic_id == clinic_id, self.model.source_url == source_url
            )
        ).scalar_one_or_none()

    def create(self, db: Session, *, obj_in: ScrapedPageCreate) -> ScrapedPage:
        """
        Creates a new scraped page record.
        """
        db_obj = self.model(
            id=uuid.uuid4(),  # Generate new UUID for the page
            clinic_id=obj_in.clinic_id,
            source_url=str(obj_in.source_url),  # Convert HttpUrl to string for DB
            cleaned_content=obj_in.cleaned_content,
            scraped_at=datetime.utcnow(),  # Set scraped timestamp on creation
            metadata_=obj_in.metadata_,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def create_or_update(
        self, db: Session, *, obj_in: ScrapedPageCreate
    ) -> ScrapedPage:
        """
        Creates a new scraped page record or updates an existing one based on clinic_id and source_url.
        """
        existing_page = self.get_by_url_and_clinic(
            db,
            clinic_id=obj_in.clinic_id,
            source_url=str(obj_in.source_url),  # Convert HttpUrl to string
        )

        if existing_page:
            # Update existing page
            existing_page.cleaned_content = obj_in.cleaned_content
            existing_page.scraped_at = datetime.utcnow()  # Update scraped timestamp
            existing_page.metadata_ = obj_in.metadata_  # Update metadata if provided
            db.add(existing_page)
            db.commit()
            db.refresh(existing_page)
            return existing_page
        else:
            # Create new page
            return self.create(db=db, obj_in=obj_in)

    def get_by_clinic(
        self, db: Session, *, clinic_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[ScrapedPage]:
        """
        Get multiple scraped pages for a specific clinic.
        """
        return db.scalars(
            select(self.model)
            .where(self.model.clinic_id == clinic_id)
            .offset(skip)
            .limit(limit)
        ).all()

    def get_multi_by_clinic(
        self, db: Session, *, clinic_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[ScrapedPage]:
        """
        Get multiple scraped pages for a specific clinic.
        Alias for get_by_clinic to match expected naming convention.
        """
        return self.get_by_clinic(db=db, clinic_id=clinic_id, skip=skip, limit=limit)


# Create an instance of CRUDScrapedPage
scraped_page = CRUDScrapedPage(ScrapedPage)

# Export methods at module level for backward compatibility
get_scraped_page_by_url_and_clinic = scraped_page.get_by_url_and_clinic
create_scraped_page = scraped_page.create
create_or_update_scraped_page = scraped_page.create_or_update
get_scraped_pages_by_clinic = scraped_page.get_by_clinic
get_multi_by_clinic = scraped_page.get_multi_by_clinic
