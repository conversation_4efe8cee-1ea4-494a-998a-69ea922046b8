import logging
from typing import Any, Optional, Union  # Added Union
from uuid import UUID

from sqlalchemy import asc, desc, func  # Added 'func', 'case', 'inspect'
from sqlalchemy.exc import IntegrityError  # Moved import to top
from sqlalchemy.orm import Session  # Moved import to top

from app.crud.base import CRUDBase
from app.models.clinician import Clinician  # Moved import to top
from app.models.patient import Patient  # Moved import to top
from app.models.side_effect_report import SideEffectReport  # Moved import to top

# Import schemas and models
from app.schemas.side_effect_report import (  # Moved import to top
    SeverityLevel,
    SideEffectReportCreate,
    SideEffectReportUpdate,
    SideEffectSeveritySummary,
)
from app import crud
from app.schemas.event_log import EventLogCreate

logger = logging.getLogger(__name__)

# --- CRUD Class for SideEffectReport ---


class CRUDSideEffectReport(
    CRUDBase[SideEffectReport, SideEffectReportCreate, SideEffectReportUpdate]
):
    """CRUD operations for SideEffectReport model."""

    def create(
        self, db: Session, *, obj_in: SideEffectReportCreate, patient_id: str
    ) -> SideEffectReport:
        """
        Creates a new side effect report record in the database for a specific patient.

        Args:
            db: The database session.
            obj_in: Pydantic schema with side effect report creation data.
            patient_id: The string ID of the patient submitting the report.

        Returns:
            The newly created SideEffectReport ORM object.
        """
        logger.debug(
            f"CRUD: Creating side effect report for patient {patient_id}. Severity: {obj_in.severity}, Desc: '{obj_in.description}'"
        )

        # Create ORM object. Pass Python Enum for native handling, string value for non-native.
        create_data = obj_in.model_dump(exclude={"severity", "clinician_id"})
        db_obj = self.model(
            **create_data,
            severity=obj_in.severity,  # Pass Python Enum object (native_enum=True)
            patient_id=patient_id,
            clinician_id=obj_in.clinician_id,  # Include clinician_id if provided
            status="Submitted",  # Initial status (native_enum=False, requires value)
        )
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:  # Catch potential DB errors like FK violation
            db.rollback()
            logger.error(
                f"Error creating side effect report for patient {patient_id}",
                exc_info=True,
            )
            raise e  # Re-raise or handle appropriately
        db.refresh(db_obj)
        logger.debug(f"CRUD: Created side effect report ID {db_obj.id}")

        # Create event log for side effect report
        try:
            event_log_data = EventLogCreate(
                action="CREATE",
                actor_user_id=patient_id,
                actor_role="patient",
                target_resource_type="side_effect_report",
                target_resource_id=str(db_obj.id),
                status="SUCCESS",
                outcome="SUCCESS",
                details={
                    "severity": str(obj_in.severity.value) if hasattr(obj_in.severity, 'value') else str(obj_in.severity),
                    "description": db_obj.description[:100] if db_obj.description else "",
                    "reported_at": str(db_obj.reported_at),
                    "status": db_obj.status,
                    "actor_name": "Patient"
                }
            )
            
            crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=patient_id,
                actor_role="patient"
            )
            logger.info(f"Created event log for side effect report ID {db_obj.id}")
        except Exception as e:
            # Log error but don't fail the side effect report creation
            logger.error(f"Failed to create event log for side effect report: {str(e)}", exc_info=True)

        return db_obj

    def get_by_id(self, db: Session, *, report_id: UUID) -> Optional[SideEffectReport]:
        """
        Get a specific side effect report by its UUID primary key.

        Args:
            db: The database session.
            report_id: The UUID of the side effect report.

        Returns:
            The SideEffectReport ORM object or None if not found.
        """
        return db.query(self.model).filter(self.model.id == report_id).first()

    def get_by_patient(
        self,
        db: Session,
        *,
        patient_id: str,
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_desc: bool = False,
    ) -> list[SideEffectReport]:
        """
        Get multiple side effect reports for a specific patient with pagination and sorting.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose reports to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.
            sort_by: Field to sort by (e.g., 'created_at', 'severity'). Defaults to 'created_at'.
            sort_desc: Whether to sort in descending order. Defaults to False (ascending).

        Returns:
            A list of SideEffectReport ORM objects for the specified patient.
        """
        query = db.query(self.model).filter(self.model.patient_id == patient_id)

        # Apply sorting - default to created_at if not specified
        sort_column = None
        if sort_by == "severity":
            sort_column = self.model.severity
        elif sort_by == "status":
            sort_column = self.model.status
        else:  # Default to reported_at
            sort_column = self.model.reported_at

        if sort_desc:
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))

        return query.offset(skip).limit(limit).all()

    def get_count_by_patient(self, db: Session, *, patient_id: str) -> int:
        """
        Counts the total number of side effect reports for a specific patient.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose reports to count.

        Returns:
            The total count of side effect reports for the patient.
        """
        return db.query(self.model).filter(self.model.patient_id == patient_id).count()

    def update(
        self,
        db: Session,
        *,
        db_obj: SideEffectReport,
        obj_in: Union[SideEffectReportUpdate, dict[str, Any]],
        patient_id: Optional[str] = None,
    ) -> SideEffectReport:
        """
        Update an existing side effect report record.
        If patient_id is provided, ensures the report belongs to the patient.
        Allows updating description, severity, and status based on SideEffectReportUpdate schema.

        Args:
            db: The database session.
            db_obj: The existing SideEffectReport ORM object to update.
            obj_in: Pydantic schema or dictionary with update data.
            patient_id: Optional string ID of the patient performing the update for authorization.

        Returns:
            The updated SideEffectReport ORM object.

        Raises:
            ValueError: If patient_id is provided and doesn't match the report's patient_id.
            IntegrityError: If a database constraint is violated.
        """
        # Authorization check: Ensure the report belongs to the patient if patient_id is provided
        if patient_id is not None and db_obj.patient_id != patient_id:
            logger.warning(
                f"Authorization failed: Patient {patient_id} attempted to update report "
                f"{db_obj.id} belonging to patient {db_obj.patient_id}"
            )
            raise ValueError(
                f"Report {db_obj.id} does not belong to patient {patient_id}"
            )

        # Convert Pydantic model to dict if needed
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # Use exclude_unset=True to only update provided fields
            update_data = obj_in.model_dump(exclude_unset=True)

        # Apply updates to the ORM object
        for field, value in update_data.items():
            if field == "severity" and value is not None:
                setattr(
                    db_obj, field, value
                )  # Pass Python Enum object (native_enum=True)
            elif field == "status" and value is not None:
                setattr(db_obj, field, value)  # Pass string value (native_enum=False)
            elif field == "description":  # Handle other allowed fields
                setattr(db_obj, field, value)

        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error updating side effect report {db_obj.id}", exc_info=True
            )
            raise e
        db.refresh(db_obj)
        logger.debug(
            f"Clinician {db_obj.clinician_id} updated side effect report {db_obj.id} "
            f"belonging to patient {db_obj.patient_id}"
        )
        return db_obj

    def delete(
        self, db: Session, *, report_id: UUID, patient_id: Optional[UUID] = None
    ) -> Optional[SideEffectReport]:
        """
        Delete a side effect report by its UUID (Hard Delete).
        If patient_id is provided, ensures the report belongs to the patient before deletion.
        Consider implications based on compliance requirements (HIPAA).

        Args:
            db: The database session.
            report_id: The UUID of the report to delete.
            patient_id: Optional UUID of the patient performing the deletion for authorization.

        Returns:
            The deleted SideEffectReport ORM object or None if not found or not authorized.
        """
        obj = db.query(self.model).get(report_id)  # Use .get() for PK lookup

        if not obj:
            logger.warning(
                f"CRUD: Side effect report {report_id} not found for deletion."
            )
            return None

        # Authorization check: Ensure the report belongs to the patient if patient_id is provided
        if patient_id is not None and obj.patient_id != patient_id:
            logger.warning(
                f"Authorization failed: Patient {patient_id} attempted to delete report {report_id} belonging to patient {obj.patient_id}"
            )
            return None  # Return None to indicate not found or not authorized

        logger.debug(
            f"CRUD: Deleting side effect report {report_id} for patient {obj.patient_id}"
        )
        db.delete(obj)
        db.commit()
        return obj

    def get_for_clinician(
        self,
        db: Session,
        *,
        clinician_id: str,
        severity: Optional[SeverityLevel] = None,
        status: Optional[str] = None,
        sort_by: str = "reported_at",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 50,
    ) -> list[SideEffectReport]:
        """
        Fetches paginated side effect reports for patients associated
        with a specific clinician, with filtering and sorting using the database.

        Args:
            db: The database session.
            clinician_id: The string ID of the clinician making the request.
            severity: Optional severity level to filter by.
            status: Optional status to filter by.
            sort_by: Field to sort by ('reported_at' or 'severity').
            sort_order: Sort order ('asc' or 'desc').
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of SideEffectReport ORM objects.
        """
        logger.debug(
            f"CRUD: Clinician {clinician_id} fetching side effects. "
            f"Filters: severity={severity}, status={status}. "
            f"Sort: {sort_by} {sort_order}. Page: skip={skip}, limit={limit}"
        )

        # Start with a query joining the reports to patients and the clinician-patient association
        # The issue is that we need to join through the association table
        from app.models.clinician import clinician_patient_association
        
        query = (
            db.query(self.model)
            .join(Patient, self.model.patient_id == Patient.id)
            .join(clinician_patient_association, Patient.id == clinician_patient_association.c.patient_id)
            .filter(clinician_patient_association.c.clinician_id == clinician_id)
        )

        # Apply optional filters
        if severity:
            query = query.filter(self.model.severity == severity)
        if status:
            query = query.filter(self.model.status == status)

        # Apply sorting
        sort_column = None
        if sort_by == "severity":
            sort_column = self.model.severity
        elif sort_by == "status":
            sort_column = self.model.status
        else:  # Default to reported_at
            sort_column = self.model.reported_at  # Corrected from created_at

        if sort_order == "desc":  # Corrected comparison
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))

        # Debug: Log the count before pagination
        count = query.count()
        logger.info(f"CRUD: Found {count} side effect reports for clinician {clinician_id} before pagination")
        
        results = query.offset(skip).limit(limit).all()
        logger.info(f"CRUD: Returning {len(results)} side effect reports after pagination")
        
        return results

    def get_severity_summary(
        self, db: Session, *, patient_id: str
    ) -> SideEffectSeveritySummary:
        """
        Calculates the count of side effect reports for a patient, grouped by severity.

        Args:
            db: The database session.
            patient_id: The ID of the patient.

        Returns:
            A SideEffectSeveritySummary object containing the counts.
        """
        counts = (
            db.query(self.model.severity, func.count(self.model.id).label("count"))
            .filter(self.model.patient_id == patient_id)
            .group_by(self.model.severity)
            .all()
        )

        summary_data = {level.value: 0 for level in SeverityLevel}
        for severity, count in counts:
            if severity in summary_data:
                summary_data[severity] = count

        return SideEffectSeveritySummary(
            minor=summary_data.get(SeverityLevel.MINOR, 0),
            moderate=summary_data.get(SeverityLevel.MODERATE, 0),
            major=summary_data.get(SeverityLevel.MAJOR, 0),
        )


# Create an instance of CRUDSideEffectReport
side_effect_report = CRUDSideEffectReport(SideEffectReport)

# Export methods at module level for backward compatibility
create_side_effect_report = side_effect_report.create
get_side_effect_report_by_id = side_effect_report.get_by_id
get_side_effect_reports_by_patient = side_effect_report.get_by_patient
get_side_effect_reports_count_by_patient = side_effect_report.get_count_by_patient
update_side_effect_report = side_effect_report.update
delete_side_effect_report = side_effect_report.delete
get_side_effect_reports_for_clinician = side_effect_report.get_for_clinician
get_side_effect_severity_summary = side_effect_report.get_severity_summary
