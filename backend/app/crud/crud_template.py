from typing import Any, Optional
from uuid import UUID

from sqlalchemy.orm import Session, undefer  # Import undefer

from app import models  # Import models directly for easier access
from app.crud.base import CRUDBase
from app.models.template import Template, TemplateRole
from app.schemas.template import (
    TemplateCreate,
    TemplateRoleCreate,
    TemplateRoleUpdate,
    TemplateUpdate,
)


class CRUDTemplate(CRUDBase[Template, TemplateCreate, TemplateUpdate]):
    def get_by_name(
        self, db: Session, *, name: str, clinic_id: Optional[UUID] = None
    ) -> Optional[Template]:
        """Get template by name, optionally filtered by clinic."""
        query = db.query(self.model).filter(self.model.name == name)
        if clinic_id:
            query = query.filter(self.model.clinic_id == clinic_id)
        return query.first()

    def get_multi_by_clinic(
        self,
        db: Session,
        *,
        clinic_id: UUID,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> list[Template]:
        """Get templates by clinic_id."""
        query = db.query(self.model).filter(self.model.clinic_id == clinic_id)
        if active_only:
            query = query.filter(self.model.is_active)
        # Eagerly load actions
        query = query.options(undefer(models.Template.actions))
        return query.offset(skip).limit(limit).all()

    def get_by_roles(
        self,
        db: Session,
        *,
        roles: list[str],
        clinic_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Template]:
        """Get templates by allowed roles."""
        template_ids = (
            db.query(TemplateRole.template_id)
            .filter(TemplateRole.role.in_(roles))
            .distinct()
            .all()
        )
        template_ids = [
            id[0] for id in template_ids
        ]  # Extract UUIDs from result tuples

        query = db.query(self.model).filter(
            self.model.id.in_(template_ids), self.model.is_active
        )

        if clinic_id:
            query = query.filter(self.model.clinic_id == clinic_id)

        # Eagerly load actions
        query = query.options(undefer(models.Template.actions))
        return query.offset(skip).limit(limit).all()

    def get_with_roles(
        self, db: Session, *, template_id: UUID
    ) -> Optional[dict[str, Any]]:
        """Get template with its associated roles."""
        template = db.query(self.model).filter(self.model.id == template_id).first()
        if not template:
            return None

        roles = (
            db.query(TemplateRole.role)
            .filter(TemplateRole.template_id == template_id)
            .all()
        )
        roles = [role[0] for role in roles]  # Extract roles from result tuples

        # Convert template to dict and add roles
        result = {**template.__dict__}
        if "_sa_instance_state" in result:
            del result["_sa_instance_state"]
        result["roles"] = roles
        return result


class CRUDTemplateRole(CRUDBase[TemplateRole, TemplateRoleCreate, TemplateRoleUpdate]):
    def get_by_template_and_role(
        self, db: Session, *, template_id: UUID, role: str
    ) -> Optional[TemplateRole]:
        """Get template role by template_id and role."""
        return (
            db.query(self.model)
            .filter(self.model.template_id == template_id, self.model.role == role)
            .first()
        )

    def get_by_template(self, db: Session, *, template_id: UUID) -> list[TemplateRole]:
        """Get all roles for a specific template."""
        return db.query(self.model).filter(self.model.template_id == template_id).all()

    def remove_by_template_and_role(
        self, db: Session, *, template_id: UUID, role: str
    ) -> Optional[TemplateRole]:
        """Remove a specific role from a template."""
        obj = (
            db.query(self.model)
            .filter(self.model.template_id == template_id, self.model.role == role)
            .first()
        )
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    def remove_all_by_template(self, db: Session, *, template_id: UUID) -> int:
        """Remove all roles for a specific template."""
        result = (
            db.query(self.model).filter(self.model.template_id == template_id).delete()
        )
        db.commit()
        return result


template = CRUDTemplate(Template)
template_role = CRUDTemplateRole(TemplateRole)
