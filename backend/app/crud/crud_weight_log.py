import logging
from datetime import UTC, date, datetime, timedelta  # Added date
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase

# Assuming models and schemas are defined as follows:
from app.models.weight_log import WeightLog
from app.schemas.weight_log import (  # Moved import to top
    WeightLogCreate,
    WeightLogUpdate,
)
from app import crud
from app.schemas.event_log import EventLogCreate

logger = logging.getLogger(__name__)

# --- CRUD Class for WeightLog ---


class CRUDWeightLog(CRUDBase[WeightLog, WeightLogCreate, WeightLogUpdate]):
    """CRUD operations for WeightLog model."""

    def create(
        self, db: Session, *, obj_in: WeightLogCreate, patient_id: str
    ) -> WeightLog:
        """
        Create a new weight log entry for a specific patient.

        Automatically sets log_date to the current date if not provided in the input schema.

        Args:
            db: The database session.
            obj_in: Pydantic schema with weight log creation data.
            patient_id: The string ID of the patient this log belongs to.

        Returns:
            The newly created WeightLog ORM object.

        Raises:
            IntegrityError: If there's a database integrity issue (e.g., FK violation).
            ValueError: If the associated patient_id does not exist (optional check).
        """
        # Optional: Check if patient exists first (adds a query but ensures FK validity)
        # patient = db.query(Patient).filter(Patient.id == patient_id).first()
        # if not patient:
        #     raise ValueError(f"Patient not found with ID: {patient_id}")

        # Create ORM object from Pydantic schema, adding the patient_id
        # Note: WeightLogCreate schema doesn't include log_date, so we add it here
        create_data = obj_in.model_dump()

        # Add log_date with current date if not provided
        if "log_date" not in create_data or create_data["log_date"] is None:
            create_data["log_date"] = date.today()
            logger.debug(f"Setting log_date to today: {create_data['log_date']}")

        db_obj = self.model(**create_data, patient_id=patient_id)
        db.add(db_obj)
        try:
            db.commit()
        except IntegrityError as e:
            db.rollback()
            logger.error(
                f"Error creating weight log for patient {patient_id}", exc_info=True
            )
            raise e  # Re-raise or handle appropriately
        db.refresh(db_obj)
        
        # Create event log for weight log
        try:
            event_log_data = EventLogCreate(
                action="CREATE",
                actor_user_id=patient_id,
                actor_role="patient",
                target_resource_type="weight_log",
                target_resource_id=str(db_obj.id),
                status="SUCCESS",
                outcome="SUCCESS",
                details={
                    "weight_kg": float(db_obj.weight_kg),
                    "log_date": str(db_obj.log_date),
                    "bmi": float(db_obj.bmi) if db_obj.bmi else None,
                    "actor_name": "Patient"
                }
            )
            
            crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=patient_id,
                actor_role="patient"
            )
            logger.info(f"Created event log for weight log ID {db_obj.id}")
        except Exception as e:
            # Log error but don't fail the weight log creation
            logger.error(f"Failed to create event log for weight log: {str(e)}", exc_info=True)
        
        return db_obj

    def get_by_id(self, db: Session, *, weight_log_id: UUID) -> Optional[WeightLog]:
        """
        Get a specific weight log by its UUID primary key.

        Args:
            db: The database session.
            weight_log_id: The UUID of the weight log.

        Returns:
            The WeightLog ORM object or None if not found.
        """
        return db.query(self.model).filter(self.model.id == weight_log_id).first()

    def get_by_patient(
        self, db: Session, *, patient_id: str, skip: int = 0, limit: int = 100
    ) -> list[WeightLog]:
        """
        Get multiple weight logs for a specific patient with pagination.

        Args:
            db: The database session.
            patient_id: The string ID of the patient whose logs to retrieve.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of WeightLog ORM objects for the specified patient.
        """
        logger.debug(f"Fetching weight logs for patient_id={patient_id}")
        result = (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .order_by(self.model.log_date.desc())  # Corrected: Order by log_date
            .offset(skip)
            .limit(limit)
            .all()
        )
        logger.debug(f"Found {len(result)} weight logs for patient_id={patient_id}")
        return result

    def update(
        self,
        db: Session,
        *,
        db_obj: WeightLog,
        obj_in: Union[WeightLogUpdate, dict[str, Any]],
    ) -> WeightLog:
        """
        Update an existing weight log record.

        Args:
            db: The database session.
            db_obj: The existing WeightLog ORM object to update.
            obj_in: Pydantic schema or dictionary with update data.

        Returns:
            The updated WeightLog ORM object.
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # Use exclude_unset=True to only include fields explicitly provided
            update_data = obj_in.model_dump(exclude_unset=True)

        # Filter out None values to prevent overwriting existing data with nulls
        # for required fields during partial updates.
        filtered_update_data = {k: v for k, v in update_data.items() if v is not None}

        # Iterate over the filtered update data and set attributes on the ORM object
        for field, value in filtered_update_data.items():
            # Ensure patient_id is not accidentally updated if present in obj_in
            # (though it shouldn't be in WeightLogUpdate schema)
            if field != "patient_id":
                setattr(db_obj, field, value)

        # Only commit if there were actual changes after filtering
        if filtered_update_data:
            db.add(db_obj)  # Add the modified object to the session
        else:
            logger.info(
                f"No non-null update data provided for weight log {db_obj.id}. Skipping commit."
            )
            return db_obj  # Return the unmodified object

        try:
            db.commit()
        except IntegrityError as e:  # Catch potential errors during commit
            db.rollback()
            logger.error(f"Error updating weight log {db_obj.id}", exc_info=True)
            raise e
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, *, weight_log_id: UUID) -> Optional[WeightLog]:
        """
        Delete a weight log by its UUID.

        Args:
            db: The database session.
            weight_log_id: The UUID of the weight log to delete.

        Returns:
            The deleted WeightLog ORM object or None if not found.
        """
        obj = db.query(self.model).get(weight_log_id)  # Use .get() for PK lookup
        if obj:
            db.delete(obj)
            db.commit()
            return obj
        return None

    def check_weekly_log_interval(self, db: Session, *, patient_id: str) -> bool:
        """
        Check if a patient can log a new weight entry based on a weekly interval rule.
        Compares against the creation timestamp (`created_at`) of the last log.

        Args:
            db: The database session.
            patient_id: The string ID of the patient.

        Returns:
            True if the patient is allowed to log a new weight entry (no previous log or last log > 7 days ago), False otherwise.
        """
        # Get the most recent weight log for this patient based on creation time
        most_recent_log = (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .order_by(self.model.created_at.desc())  # Use created_at for interval check
            .first()
        )

        # If no previous log exists, return True (first log is always allowed)
        if not most_recent_log:
            return True

        # Check if the most recent log's creation time is older than 7 days
        # Using timezone-aware datetime objects as recommended
        seven_days_ago = datetime.now(UTC) - timedelta(days=7)

        return most_recent_log.created_at < seven_days_ago

    def get_count_by_patient(self, db: Session, patient_id: str) -> int:
        """
        Return the total count of weight logs for a specific patient.

        Args:
            db: The database session.
            patient_id: The string ID of the patient.

        Returns:
            The count of weight logs for the patient.
        """
        return db.query(self.model).filter(self.model.patient_id == patient_id).count()

    def get_latest_by_patient(self, db: Session, *, patient_id: str) -> Optional[WeightLog]:
        """
        Get the most recent weight log entry for a specific patient.

        Args:
            db: The database session.
            patient_id: The string ID of the patient.

        Returns:
            The most recent WeightLog ORM object or None if no logs exist.
        """
        return (
            db.query(self.model)
            .filter(self.model.patient_id == patient_id)
            .order_by(self.model.log_date.desc(), self.model.created_at.desc())
            .first()
        )


# Create an instance of CRUDWeightLog
weight_log = CRUDWeightLog(WeightLog)

# Export methods at module level for backward compatibility
create_weight_log = weight_log.create
get_weight_log_by_id = weight_log.get_by_id
get_weight_logs_by_patient = weight_log.get_by_patient
update_weight_log = weight_log.update
delete_weight_log = weight_log.delete
check_weekly_log_interval = weight_log.check_weekly_log_interval
get_weight_logs_count_by_patient = weight_log.get_count_by_patient
get_latest_weight_log_by_patient = weight_log.get_latest_by_patient
