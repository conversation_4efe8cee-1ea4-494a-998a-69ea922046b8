"""Base exceptions for CRUD operations."""


class CRUDError(Exception):
    """Base class for all CRUD-related errors."""

    pass


class NotFoundError(CRUDError):
    """Base class for all not found errors."""

    pass


class ValidationError(CRUDError):
    """Base class for all validation errors."""

    pass


class ConflictError(CRUDError):
    """Raised when an operation conflicts with existing data (e.g., unique constraint)."""

    pass


# Appointment-specific exceptions
class AppointmentNotFoundError(NotFoundError):
    """Raised when an appointment is not found."""

    def __init__(self, appointment_id: str):
        self.appointment_id = appointment_id
        super().__init__(f"Appointment with ID {appointment_id} not found")


class AppointmentStatusError(ValidationError):
    """Raised when there's an invalid appointment status transition."""

    def __init__(self, message: str):
        super().__init__(message)
