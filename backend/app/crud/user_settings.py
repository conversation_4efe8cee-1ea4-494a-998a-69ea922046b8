"""CRUD operations for user settings."""

from typing import Optional

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user_settings import UserSetting
from app.utils import log_error


async def get_setting(db: AsyncSession, user_id: str, key: str) -> Optional[str]:
    """
    Get a user setting by key.

    Args:
        db: Database session
        user_id: User ID
        key: Setting key

    Returns:
        Setting value if found, None otherwise
    """
    try:
        stmt = select(UserSetting).where(
            UserSetting.user_id == user_id, UserSetting.key == key
        )
        result = await db.execute(stmt)
        setting = result.scalars().first()

        if setting:
            return setting.value
        return None

    except SQLAlchemyError as e:
        log_error(f"Error getting user setting: {e}")
        return None


async def update_setting(db: AsyncSession, user_id: str, key: str, value: str) -> bool:
    """
    Update or create a user setting.

    Args:
        db: Database session
        user_id: User ID
        key: Setting key
        value: Setting value

    Returns:
        True if successful, False otherwise
    """
    try:
        # Check if setting exists
        stmt = select(UserSetting).where(
            UserSetting.user_id == user_id, UserSetting.key == key
        )
        result = await db.execute(stmt)
        setting = result.scalars().first()

        if setting:
            # Update existing setting
            setting.value = value
        else:
            # Create new setting
            setting = UserSetting(user_id=user_id, key=key, value=value)
            db.add(setting)

        await db.commit()
        return True

    except SQLAlchemyError as e:
        await db.rollback()
        log_error(f"Error updating user setting: {e}")
        return False


async def delete_setting(db: AsyncSession, user_id: str, key: str) -> bool:
    """
    Delete a user setting.

    Args:
        db: Database session
        user_id: User ID
        key: Setting key

    Returns:
        True if successful, False otherwise
    """
    try:
        stmt = select(UserSetting).where(
            UserSetting.user_id == user_id, UserSetting.key == key
        )
        result = await db.execute(stmt)
        setting = result.scalars().first()

        if setting:
            await db.delete(setting)
            await db.commit()
            return True
        return False

    except SQLAlchemyError as e:
        await db.rollback()
        log_error(f"Error deleting user setting: {e}")
        return False


async def get_all_settings(db: AsyncSession, user_id: str) -> dict[str, str]:
    """
    Get all settings for a user.

    Args:
        db: Database session
        user_id: User ID

    Returns:
        Dictionary of settings
    """
    try:
        stmt = select(UserSetting).where(UserSetting.user_id == user_id)
        result = await db.execute(stmt)
        settings = result.scalars().all()

        return {setting.key: setting.value for setting in settings}

    except SQLAlchemyError as e:
        log_error(f"Error getting all user settings: {e}")
        return {}
