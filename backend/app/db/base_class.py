import re
import uuid

from sqlalchemy import Column, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declared_attr

from app.db.base import Base  # Import Base from base.py


class BaseWithTimestamps(Base):  # Inherit from Base
    """
    Abstract base class for SQLAlchemy models that includes UUID primary key,
    created_at, and updated_at timestamps.
    """

    __abstract__ = True  # Mark as abstract

    @declared_attr
    def __tablename__(cls):
        # Automatically generate table name from class name (e.g., UserProfile -> user_profiles)
        # Following naming_conventions.md (snake_case, plural)
        if (
            cls.__name__ == "BaseWithTimestamps"
        ):  # Avoid generating tablename for the base class itself
            return None
        name = re.sub(r"(?<!^)(?=[A-Z])", "_", cls.__name__).lower()
        # Simple pluralization by adding 's', might need refinement for irregular nouns
        if name.endswith("s"):
            return name  # Avoid double 's' like contentss
        elif name.endswith("y"):
            return name[:-1] + "ies"  # e.g., category -> categories
        else:
            return name + "s"

    # Define a UUID primary key column common to inheriting models
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Define timestamp columns common to inheriting models
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
