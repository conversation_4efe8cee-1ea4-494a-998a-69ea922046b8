from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# Create the SQLAlchemy engine
# pool_pre_ping=True helps manage connections that might have timed out
# Use pool size and max overflow settings from config
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
)

# Create a configured "Session" class
# autocommit=False and autoflush=False are standard defaults for FastAPI usage
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# Function to check DB connection for health checks
def check_db_connection():
    """Checks if a connection can be established with the database."""
    try:
        # The connection is automatically closed when exiting the 'with' block
        with engine.connect():  # Removed unused 'as connection'
            # Optionally run a simple query
            # connection.execute(text("SELECT 1"))
            return True
    except Exception as e:
        # Log the specific error for debugging health check failures
        import logging  # Add import if not already present at top

        logger = logging.getLogger(__name__)  # Get logger instance
        logger.error(f"Health check DB connection failed: {e}", exc_info=True)
        return False
