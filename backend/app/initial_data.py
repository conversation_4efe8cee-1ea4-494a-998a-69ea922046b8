import logging
from uuid import uuid4

from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.db.session import SessionLocal

logger = logging.getLogger(__name__)


def create_clinician_templates(db: Session) -> None:
    """
    Creates or updates default templates for clinicians.
    """
    # Define the clinician_assistant template
    template_name = "clinician_assistant"
    template = crud.template.get_by_name(db, name=template_name)

    # Define the template actions
    actions = [
        {
            "name": "appointment_create",
            "action_type": "appointment_create",
            "description": "Schedule an appointment for a patient",
            "example": "Schedule an appointment with patient <PERSON> next Tuesday at 2pm for 30 minutes",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "scheduled_time",
                    "type": "string",
                    "description": "Appointment Date & Time",
                    "required": True,
                    "format": "date-time",
                },
                {
                    "name": "duration_minutes",
                    "type": "number",
                    "description": "Duration (minutes)",
                    "required": True,
                    "placeholder": "30",
                },
                {
                    "name": "appointment_type",
                    "type": "string",
                    "description": "Appointment Type",
                    "required": True,
                    "nullable": False,
                    "enum": ["Initial", "Follow-up", "Procedure", "Consultation"],
                },
                {
                    "name": "notes",
                    "type": "string",
                    "description": "Additional Notes",
                    "required": False,
                    "nullable": True,
                    "placeholder": "Any special requirements or topics to discuss",
                },
            ],
            "api_endpoint": "/api/v1/appointments",
            "method": "POST",
        },
        {
            "action_type": "medication_request_create",
            "description": "Create a medication request for a patient",
            "example": "Request medication Ibuprofen 200mg for patient Jane Doe, to be taken twice daily for 7 days",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "medication_name",
                    "type": "string",
                    "description": "Medication Name",
                    "required": True,
                    "placeholder": "e.g., Ibuprofen 200mg",
                },
                {
                    "name": "dosage",
                    "type": "string",
                    "description": "Dosage",
                    "required": False,
                    "nullable": True,
                    "placeholder": "e.g., 10mg, 1 tablet",
                },
                {
                    "name": "frequency",
                    "type": "string",
                    "description": "Frequency",
                    "required": False,
                    "nullable": True,
                    "placeholder": "e.g., Once daily, Twice daily",
                },
                {
                    "name": "duration",
                    "type": "string",
                    "description": "Duration",
                    "required": False,
                    "nullable": True,
                    "placeholder": "e.g., 7 days, 2 weeks",
                },
                {
                    "name": "notes",
                    "type": "string",
                    "description": "Additional Instructions",
                    "required": False,
                    "nullable": True,
                },
            ],
            "api_endpoint": "/api/v1/medication-requests",
            "method": "POST",
        },
        {
            "action_type": "side_effect_report_create",
            "description": "Report a side effect for a medication",
            "example": "Report side effects for patient Jane Doe for Lisinopril - experiencing dry cough, moderate severity since last week",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "medication_name",
                    "type": "string",
                    "description": "Medication",
                    "required": True,
                    "placeholder": "Which medication is causing issues?",
                },
                {
                    "name": "symptoms",
                    "type": "string",
                    "description": "Symptoms/Side Effects",
                    "required": True,
                    "placeholder": "Describe what you're experiencing",
                },
                {
                    "name": "severity",
                    "type": "string",
                    "description": "Severity Level",
                    "required": False,
                    "nullable": True,
                    "enum": ["Mild", "Moderate", "Severe"],
                },
                {
                    "name": "onset_date",
                    "type": "string",
                    "description": "When did this start?",
                    "required": False,
                    "nullable": True,
                    "format": "date",
                },
                {
                    "name": "notes",
                    "type": "string",
                    "description": "Additional Details",
                    "required": False,
                    "nullable": True,
                },
            ],
            "api_endpoint": "/api/v1/side-effects/{patient_id}",
            "method": "POST",
        },
        {
            "action_type": "note_create",
            "description": "Add a clinical note to a patient's record",
            "example": "Add a note for patient John Smith: Patient reports feeling better after medication adjustment",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "note_content",
                    "type": "string",
                    "description": "Note Content",
                    "required": True,
                    "placeholder": "Enter your clinical notes here",
                },
                {
                    "name": "note_type",
                    "type": "string",
                    "description": "Note Type",
                    "required": False,
                    "nullable": True,
                    "enum": ["Progress", "Lab", "Consultation", "General"],
                    "placeholder": "Select note type (optional)",
                },
            ],
            "api_endpoint": "/api/v1/notes",
            "method": "POST",
        },
        {
            "action_type": "weight_log_create",
            "description": "Log or record a patient's weight measurement",
            "example": "Log a weight for patient John Smith, record weight 150 pounds, log weight measurement",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "weight_value",
                    "type": "number",
                    "description": "Weight Value",
                    "required": True,
                    "placeholder": "Enter weight value",
                },
                {
                    "name": "unit",
                    "type": "string",
                    "description": "Unit",
                    "required": False,
                    "enum": ["kg", "lb"],
                    "placeholder": "kg or lb",
                },
                {
                    "name": "date",
                    "type": "string",
                    "description": "Measurement Date",
                    "required": False,
                    "format": "date",
                    "placeholder": "Leave empty for today",
                },
            ],
            "api_endpoint": "/api/v1/weight-logs",
            "method": "POST",
        },
        {
            "action_type": "appointment_create",
            "description": "Schedule a new appointment",
            "example": "Schedule an appointment for patient John Smith next Tuesday at 2pm for 30 minutes",
            "parameters": [
                {
                    "name": "patient_id",
                    "type": "string",
                    "description": "Patient",
                    "required": True,
                    "format": "patient_selector",
                },
                {
                    "name": "scheduled_time",
                    "type": "string",
                    "description": "Appointment Date & Time",
                    "required": True,
                    "format": "date-time",
                },
                {
                    "name": "duration_minutes",
                    "type": "integer",
                    "description": "Duration (minutes)",
                    "required": True,
                    "placeholder": "30",
                },
                {
                    "name": "appointment_type",
                    "type": "string",
                    "description": "Appointment Type",
                    "required": False,
                    "nullable": True,
                    "enum": ["Initial", "Follow-up", "Procedure", "Consultation"],
                },
                {
                    "name": "notes",
                    "type": "string",
                    "description": "Additional Notes",
                    "required": False,
                    "nullable": True,
                    "placeholder": "Any special requirements or topics to discuss",
                },
            ],
            "api_endpoint": "/api/v1/appointments",
            "method": "POST",
        },
    ]

    if template:
        # Template exists, update it with potentially new actions
        logger.info(
            f"Updating existing clinician_assistant template with ID: {template.id}"
        )
        template_update = schemas.TemplateUpdate(
            actions=actions,  # Update with current actions
            is_active=True,
        )
        template = crud.template.update(db, db_obj=template, obj_in=template_update)
        logger.info(f"Updated template with {len(actions)} actions")
    else:
        # Create a new template
        logger.info("Creating new clinician_assistant template")
        clinician_assistant = schemas.TemplateCreate(
            name=template_name,
            description="Template for handling clinician requests and actions",
            system_prompt="You are an assistant for healthcare clinicians. Extract intents and parameters from their requests.",
            is_active=True,
            version="1.0",
            actions=actions,
            default_settings={
                "temperature": 0.2,
                "max_tokens": 1500,
                "confirm_actions": True,
                "include_action_examples": True,
            },
        )

        # Create the template
        template = crud.template.create(db, obj_in=clinician_assistant)
        logger.info(f"Created clinician_assistant template with ID: {template.id}")

        # Associate with clinician role
        role_in = schemas.TemplateRoleCreate(template_id=template.id, role="clinician")
        crud.template_role.create(db, obj_in=role_in)
        logger.info(f"Associated clinician role with template: {template.id}")


def create_patient_templates(db: Session) -> None:
    """
    Creates or updates default templates for patients.
    """
    from app.crud import template, template_role
    from app.schemas.template import TemplateCreate, TemplateRoleCreate, TemplateUpdate

    patient_templates = [
        {
            "name": "patient_side_effect_reporting",
            "description": "Template for patients to report medication side effects through natural language",
            "system_prompt": (
                "You are a medical assistant helping patients report medication side effects. "
                "Extract the medication name, symptoms, severity, and onset time from the patient's input. "
                "Ask follow-up questions if any critical information is missing. "
                "Be empathetic while collecting necessary clinical information."
            ),
            "is_active": True,
            "version": "1.0",
            "default_settings": {"temperature": 0.2, "max_tokens": 1024},
            "actions": [
                {
                    "action_type": "side_effect_report_create",
                    "description": "Report a medication side effect",
                    "parameters": [
                        {
                            "name": "medication_name",
                            "type": "string",
                            "description": "Medication Name",
                            "required": True,
                            "placeholder": "Which medication is causing issues?"
                        },
                        {
                            "name": "symptoms",
                            "type": "string",
                            "description": "Symptoms/Side Effects",
                            "required": True,
                            "placeholder": "Describe what you're experiencing"
                        },
                        {
                            "name": "severity",
                            "type": "string",
                            "description": "Severity Level",
                            "required": False,
                            "enum": ["Mild", "Moderate", "Severe"]
                        },
                        {
                            "name": "onset_time",
                            "type": "string",
                            "description": "When did this start?",
                            "required": False,
                            "format": "date-time"
                        },
                        {
                            "name": "additional_notes",
                            "type": "string",
                            "description": "Additional Details",
                            "required": False,
                            "placeholder": "Any other relevant information"
                        },
                    ],
                }
            ],
        },
        {
            "name": "patient_health_data_retrieval",
            "description": "Template for patients to retrieve their health data through natural language",
            "system_prompt": (
                "You are a medical assistant helping patients retrieve their health information. "
                "Extract the data type, time range, and any specific measurements from the patient's query. "
                "Be precise and helpful while maintaining privacy and security."
            ),
            "is_active": True,
            "version": "1.0",
            "default_settings": {"temperature": 0.2, "max_tokens": 1024},
            "actions": [
                {
                    "action_type": "health_data_retrieve",
                    "description": "Retrieve patient health data",
                    "parameters": [
                        {"name": "data_type", "type": "string", "required": True},
                        {"name": "time_range", "type": "string", "required": False},
                        {
                            "name": "specific_measure",
                            "type": "string",
                            "required": False,
                        },
                        {"name": "limit", "type": "integer", "required": False},
                    ],
                },
                {
                    "action_type": "weight_log_create",
                    "description": "Log a new weight measurement",
                    "parameters": [
                        {
                            "name": "weight_value",
                            "type": "number",
                            "required": True,
                            "description": "Numeric weight value",
                        },
                        {
                            "name": "unit",
                            "type": "string",
                            "required": False,
                            "description": "kg or lb (default lb)",
                        },
                        {
                            "name": "date",
                            "type": "string",
                            "required": False,
                            "description": "Date of measurement (ISO or natural language)",
                        },
                    ],
                },
            ],
        },
        {
            "name": "patient_appointment_management",
            "description": "Template for patients to manage appointments through natural language",
            "system_prompt": (
                "You are a medical assistant helping patients manage their appointments. "
                "Extract appointment details including desired date, time, and reason from the patient's request. "
                "For scheduling, identify if the patient has a preferred clinician or time. "
                "For queries about existing appointments, extract key filters. "
                "Be precise and helpful while respecting scheduling constraints."
            ),
            "is_active": True,
            "version": "1.0",
            "default_settings": {"temperature": 0.2, "max_tokens": 1024},
            "actions": [
                {
                    "action_type": "appointment_create",
                    "description": "Schedule a new appointment",
                    "example": "Schedule an appointment next Tuesday at 2pm for 30 minutes",
                    "parameters": [
                        {
                            "name": "patient_id",
                            "type": "string",
                            "description": "Patient",
                            "required": True,
                            "format": "patient_selector",
                        },
                        {
                            "name": "scheduled_time",
                            "type": "string",
                            "description": "Appointment Date & Time",
                            "required": True,
                            "format": "date-time",
                        },
                        {
                            "name": "duration_minutes",
                            "type": "number",
                            "description": "Duration (minutes)",
                            "required": True,
                            "placeholder": "30",
                        },
                        {
                            "name": "appointment_type",
                            "type": "string",
                            "description": "Appointment Type",
                            "required": False,
                            "nullable": True,
                            "enum": ["Initial", "Follow-up", "Procedure", "Consultation"],
                        },
                        {
                            "name": "notes",
                            "type": "string",
                            "description": "Additional Notes",
                            "required": False,
                            "nullable": True,
                            "placeholder": "Any special requirements or topics to discuss",
                        },
                    ],
                    "api_endpoint": "/api/v1/appointments",
                    "method": "POST",
                },
                {
                    "action_type": "appointment_request_create",
                    "description": "Patient requests an appointment with a clinician",
                    "required_role": "patient",
                    "parameters": [
                        {
                            "name": "preferred_date",
                            "type": "date",
                            "description": "Preferred Date",
                            "required": True,
                            "format": "date",
                        },
                        {
                            "name": "preferred_time",
                            "type": "time",
                            "description": "Preferred Time",
                            "required": True,
                            "format": "time",
                        },
                        {
                            "name": "reason",
                            "type": "string",
                            "description": "Reason for Visit",
                            "required": True,
                            "placeholder": "What would you like to discuss?",
                        },
                        {
                            "name": "clinician_preference",
                            "type": "string",
                            "description": "Preferred Clinician",
                            "required": True,
                            "nullable": True,
                            "placeholder": "Any preference? (optional)",
                        },
                    ],
                    "api_endpoint": "/api/v1/patients/me/appointment-requests",
                    "method": "POST",
                },
                {
                    "action_type": "appointment_cancel",
                    "description": "Cancel an existing appointment",
                    "parameters": [
                        {"name": "appointment_id", "type": "string", "required": True},
                        {
                            "name": "cancellation_reason",
                            "type": "string",
                            "required": False,
                        },
                    ],
                },
            ],
        },
    ]

    for tpl in patient_templates:
        tpl_obj = template.get_by_name(db, name=tpl["name"])
        if tpl_obj:
            logger.info(f"Updating existing patient template: {tpl['name']}")
            tpl_update = TemplateUpdate(
                description=tpl["description"],
                system_prompt=tpl["system_prompt"],
                is_active=tpl["is_active"],
                version=tpl["version"],
                default_settings=tpl["default_settings"],
                actions=tpl["actions"],
            )
            template.update(db, db_obj=tpl_obj, obj_in=tpl_update)
        else:
            logger.info(f"Creating patient template: {tpl['name']}")
            tpl_create = TemplateCreate(
                name=tpl["name"],
                description=tpl["description"],
                system_prompt=tpl["system_prompt"],
                is_active=tpl["is_active"],
                version=tpl["version"],
                default_settings=tpl["default_settings"],
                actions=tpl["actions"],
            )
            new_tpl = template.create(db, obj_in=tpl_create)
            role_in = TemplateRoleCreate(template_id=new_tpl.id, role="patient")
            template_role.create(db, obj_in=role_in)


def create_side_effect_alert(db: Session) -> None:
    """
    Upserts a template and notification rule that alert all assigned
    clinicians when a patient logs a MODERATE or MAJOR side‑effect report.
    """
    # Skip notification rule creation for now - model doesn't exist
    return
    tmpl_name = "clinician_side_effect_alert"

    # ---------- Template Upsert -------------------------------------------------
    tmpl = crud.template.get_by_name(db, name=tmpl_name)
    template_data = {
        "description": "In‑app alert to clinicians for moderate/severe side‑effect reports.",
        "system_prompt": "Render a concise notification for clinicians.",
        "is_active": True,
        "version": "1.0",
        "actions": [],
        "default_settings": {},
    }
    if tmpl:
        logger.info("Updating existing side‑effect alert template")
        crud.template.update(
            db, db_obj=tmpl, obj_in=schemas.TemplateUpdate(**template_data)
        )
    else:
        tmpl_create = schemas.TemplateCreate(name=tmpl_name, **template_data)
        tmpl = crud.template.create(db, obj_in=tmpl_create)
        crud.template_role.create(
            db, obj_in=schemas.TemplateRoleCreate(template_id=tmpl.id, role="clinician")
        )
        logger.info(f"Created side‑effect alert template {tmpl.id}")

    # ---------- NotificationRule Upsert ----------------------------------------
    rule = (
        db.query(models.NotificationRule)
        .filter(
            models.NotificationRule.template_id == tmpl.id,
            models.NotificationRule.event_type == "side_effect_report.created",
        )
        .first()
    )
    rule_data = dict(
        template_id=tmpl.id,
        clinic_id=None,
        event_type="side_effect_report.created",
        condition_expr='payload.severity in ["moderate","major"]',
        cooldown_minutes=15,
        recipient_selector="assigned_clinicians",
        channels=["in_app"],
        is_active=True,
    )
    if rule:
        logger.info("Updating existing NotificationRule for side‑effect alerts")
        for k, v in rule_data.items():
            setattr(rule, k, v)
        db.commit()
    else:
        rule = models.NotificationRule(id=uuid4(), **rule_data)
        db.add(rule)
        db.commit()
        logger.info(f"Created NotificationRule {rule.id} for side‑effect alerts")


def main() -> None:
    """Main initialization function."""
    logger.info("Creating initial data")
    db = SessionLocal()
    try:
        create_clinician_templates(db)
        create_patient_templates(db)
        create_side_effect_alert(db)
    finally:
        db.close()
    logger.info("Initial data created")


if __name__ == "__main__":
    main()
