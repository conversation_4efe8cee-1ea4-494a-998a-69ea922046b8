import json
import time

import structlog

from app.intent_pipeline.schemas.intent import Fill<PERSON>rom<PERSON>, FillResult, IntentResolution
from app.services.openai_client import OpenAIClient, get_openai_client

logger = structlog.get_logger(__name__)


class PayloadFillerError(Exception):
    """Exception raised when the payload filler encounters an error."""

    pass


class PayloadFiller:
    """Service for filling missing fields in intent payloads.

    This class is responsible for generating prompts to ask users for missing
    information and for parsing their responses to extract structured data.

    Attributes:
        client: The OpenAI client for making API calls.
    """

    def __init__(self, client: OpenAIClient):
        """Initialize the PayloadFiller with dependencies.

        Args:
            client: The OpenAI client for making API calls.
        """
        self.client = client

    async def ask_for_missing(
        self,
        *,
        user_id: str,
        user_role: str,
        message: str,
        resolution: IntentResolution,
        model: str = "o4-mini",
        temperature: float = 0.2,
    ) -> FillPrompt:
        """Generate a prompt asking the user for missing fields.

        Args:
            user_id: The ID of the user sending the message.
            user_role: The role of the user (e.g., "patient", "clinician").
            message: The original message from the user.
            resolution: The intent resolution containing missing fields.
            model: The OpenAI model to use (default: "o4-mini").
            temperature: The temperature parameter for the API call (default: 0.2).

        Returns:
            A FillPrompt object containing the text to send to the user and the
            list of missing fields.

        Raises:
            PayloadFillerError: If there is an error generating the prompt.
        """
        start_time = time.time()

        if not resolution.missing:
            return FillPrompt(text="", missing=[])

        # Build the system prompt
        system_prompt = "You are a helpful medical assistant that politely asks for missing information."

        # Build the user prompt
        user_prompt = f"""Original message: {message}

Intent: {resolution.intent}

Missing fields: {', '.join(resolution.missing)}

Generate a polite, concise message asking the user to provide the missing information. Be specific about what information is needed."""

        try:
            response = await self.client.chat(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=256,
                temperature=temperature,
                extra={"user": user_id},
            )

            # Extract the response content
            prompt_text = response.choices[0].message.content.strip()

            # Log the operation
            latency_ms = int((time.time() - start_time) * 1000)
            logger.info(
                "Generated missing fields prompt",
                user_id=user_id,
                user_role=user_role,
                missing=resolution.missing,
                latency_ms=latency_ms,
                model=model,
                event="generate_fill_prompt",
            )

            return FillPrompt(text=prompt_text, missing=resolution.missing)

        except Exception as e:
            # Log the error and re-raise
            logger.error(
                "Failed to generate missing fields prompt",
                user_id=user_id,
                user_role=user_role,
                error=str(e),
                error_type=type(e).__name__,
                model=model,
                event="generate_fill_prompt_error",
            )
            raise PayloadFillerError(f"Failed to generate prompt: {e}")

    async def integrate_user_reply(
        self,
        *,
        user_id: str,
        reply: str,
        previous_resolution: IntentResolution,
        missing_fields: list[str],
        model: str = "o4-mini",
    ) -> FillResult:
        """Parse a user's reply to extract values for missing fields.

        Args:
            user_id: The ID of the user sending the reply.
            reply: The user's reply to the prompt.
            previous_resolution: The previous intent resolution.
            missing_fields: The fields that were missing in the previous resolution.
            model: The OpenAI model to use (default: "o4-mini").

        Returns:
            A FillResult object containing the filled parameters and any remaining
            missing fields.

        Raises:
            PayloadFillerError: If there is an error parsing the reply.
        """
        start_time = time.time()

        if not missing_fields:
            return FillResult(filled={}, still_missing=[])

        # Build the system prompt
        system_prompt = (
            "You extract structured data from a patient's answer. Return JSON only."
        )

        # Build the user prompt
        user_prompt = f"""expected_fields: {json.dumps(missing_fields)}
user_reply: "{reply}"""

        try:
            response = await self.client.chat(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=256,
                temperature=0.0,  # Use 0.0 for deterministic extraction
                extra={"user": user_id},
            )

            # Extract the response content
            content = response.choices[0].message.content.strip()

            try:
                # Parse the JSON response
                result = json.loads(content)

                # Validate the response structure
                if "filled" not in result:
                    raise PayloadFillerError(
                        f"Response missing 'filled' key: {content}"
                    )

                # Create the result object
                fill_result = FillResult(
                    filled=result.get("filled", {}),
                    still_missing=result.get("still_missing", []),
                )

                # Log the operation
                latency_ms = int((time.time() - start_time) * 1000)
                logger.info(
                    "Integrated user reply",
                    user_id=user_id,
                    filled_count=len(fill_result.filled),
                    still_missing=fill_result.still_missing,
                    latency_ms=latency_ms,
                    model=model,
                    event="integrate_user_reply",
                )

                return fill_result

            except json.JSONDecodeError as e:
                raise PayloadFillerError(
                    f"Failed to parse JSON response: {e}. Response: {content}"
                )

        except Exception as e:
            # Log the error and re-raise
            logger.error(
                "Failed to integrate user reply",
                user_id=user_id,
                error=str(e),
                error_type=type(e).__name__,
                model=model,
                event="integrate_user_reply_error",
            )
            raise PayloadFillerError(f"Failed to integrate reply: {e}")


def get_payload_filler() -> PayloadFiller:
    """FastAPI dependency that returns a PayloadFiller instance.

    Returns:
        An instance of PayloadFiller with its dependencies injected.
    """
    return PayloadFiller(get_openai_client())
