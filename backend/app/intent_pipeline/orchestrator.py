import asyncio
from typing import Any, Callable

import anyio.to_thread
import structlog
from fastapi import Depends
from pydantic import BaseModel

from app.intent_pipeline.parameter_completion import (
    IncompleteParametersError,
    MoreInfoRequired,
    ParameterCompletionService,
    get_parameter_completion_service,
)
from app.intent_pipeline.resolver import IntentResolver, get_intent_resolver
from app.intent_pipeline.schemas.intent import IntentDefinition

logger = structlog.get_logger(__name__)


class ChatOrchestrator:
    """Orchestrates the intent pipeline components to handle user messages.

    This class coordinates the process of:
    1. Resolving user messages to intents
    2. Filling in missing parameters through user interaction
    3. Executing the appropriate action handler once parameters are complete
    """

    def __init__(
        self,
        resolver: IntentResolver,
        filler: ParameterCompletionService,
        intents: list[IntentDefinition],
        action_handlers: dict[str, Callable[..., Any]],
        schemas: dict[str, type[BaseModel]] = None,
    ):
        """Initialize the ChatOrchestrator.

        Args:
            resolver: The IntentResolver for mapping messages to intents
            filler: The ParameterCompletionService for filling missing parameters
            intents: List of available intent definitions
            action_handlers: Dictionary mapping intent names to action handler functions
            schemas: Optional dictionary mapping intent names to Pydantic schemas
        """
        self.resolver = resolver
        self.filler = filler
        self.intent_catalog = {i.name: i for i in intents}
        self.action_handlers = action_handlers
        self.schemas = schemas or {}

    async def handle_message(
        self,
        *,
        user_id: str,
        user_role: str,
        message: str,
        message_history: list[dict[str, Any]],
        context: dict[str, Any],
    ) -> dict[str, Any]:
        """Handle a user message through the intent pipeline.

        Args:
            user_id: The ID of the user in the conversation
            user_role: The role of the user (e.g., "patient", "clinician")
            message: The user's message text
            message_history: The conversation history
            context: The conversation context for storing state

        Returns:
            A response object with type and additional data based on the outcome:
            - {"type": "fallback"} if no intent was resolved
            - {"type": "ask", "question": str, "missing_field": str} if more info is needed
            - {"type": "action", "outcome": Any} if an action was executed

        Raises:
            Exception: If there's an error in the intent pipeline
        """
        log = logger.bind(
            user_id=user_id,
            user_role=user_role,
            service="chat_orchestrator",
        )

        # Ensure the current message is in the history for parameter extraction
        if not message_history or message_history[-1].get("content") != message:
            message_history = [*message_history, {"role": "user", "content": message}]

        try:
            # Step 1: Resolve the user message to an intent
            log.info("Resolving intent")
            resolution = await self.resolver.resolve(
                user_id=user_id,
                user_role=user_role,
                message=message,
                intents=list(self.intent_catalog.values()),
            )

            # If no intent was resolved, return fallback
            if resolution.intent is None:
                log.info("No intent resolved, falling back to default handler")
                return {"type": "fallback"}

            intent_name = resolution.intent
            log.info(
                "Intent resolved", intent=intent_name, confidence=resolution.confidence
            )

            # Check if we have a handler for this intent
            if intent_name not in self.action_handlers:
                log.error("No handler found for intent", intent=intent_name)
                return {"type": "fallback"}

            # Get the intent definition from our catalog
            intent_def = self.intent_catalog.get(intent_name)
            if not intent_def:
                log.error("Intent definition not found", intent=intent_name)
                return {"type": "fallback"}

            # Get the schema for this intent
            schema = self.schemas.get(intent_name)
            if not schema:
                log.error("Schema not found for intent", intent=intent_name)
                return {"type": "fallback"}

            # Step 2: Fill in missing parameters
            partial_params = resolution.filled

            try:
                log.info(
                    "Filling missing parameters",
                    intent=intent_name,
                    partial_params=partial_params,
                )

                filled_params, missing_fields = await self.filler.fill_missing(
                    user_id=user_id,
                    user_role=user_role,
                    intent=intent_def,
                    partial_params=partial_params,
                    message_history=message_history,
                    context=context,
                    schema=schema,
                )

                log.info(
                    "Parameters filled successfully",
                    intent=intent_name,
                    params=filled_params,
                    missing_fields=missing_fields,  # Log any remaining missing fields for analytics
                )

                # Step 3: Execute the action handler
                handler = self.action_handlers[intent_name]
                log.info(
                    "Executing action handler",
                    intent=intent_name,
                    handler=handler.__name__,
                )

                # Handle both async and sync handlers
                if asyncio.iscoroutinefunction(handler):
                    outcome = await handler(**filled_params)
                else:
                    outcome = await anyio.to_thread.run_sync(handler, **filled_params)

                log.info("Action executed successfully", intent=intent_name)

                return {"type": "action", "outcome": outcome}

            except MoreInfoRequired as e:
                # We need more information from the user
                log.info(
                    "More information required from user",
                    intent=intent_name,
                    missing_field=e.missing_field,
                )

                return {
                    "type": "ask",
                    "question": e.question,
                    "missing_field": e.missing_field,
                }

            except IncompleteParametersError as e:
                # We couldn't complete the parameters after max rounds
                log.error(
                    "Failed to complete parameters after max rounds",
                    intent=intent_name,
                    error=str(e),
                )

                return {"type": "fallback"}

        except Exception as e:
            # Log the error and re-raise
            log.error(
                "Error handling message", error=str(e), error_type=type(e).__name__
            )
            raise


# Example Pydantic schema for testing
class TestSchema(BaseModel):
    """Test schema for the test_intent."""

    foo: str


# FastAPI dependency
async def get_chat_orchestrator(
    resolver: IntentResolver = Depends(get_intent_resolver),
    filler: ParameterCompletionService = Depends(get_parameter_completion_service),
) -> ChatOrchestrator:
    """FastAPI dependency that returns a ChatOrchestrator instance.

    Args:
        resolver: The IntentResolver for mapping messages to intents
        filler: The ParameterCompletionService for filling missing parameters

    Returns:
        A configured ChatOrchestrator instance
    """
    # Define test intent definitions
    intents = [
        IntentDefinition(
            name="test_intent",
            description="Test intent for development and testing",
            required_fields=["foo"],
        ),
    ]

    # Define schemas for each intent
    schemas = {
        "test_intent": TestSchema,
    }

    # Define action handlers
    async def test_intent_handler(**kwargs):
        """Handler for the test_intent."""
        return {"message": "Test intent executed", "params": kwargs}

    action_handlers = {
        "test_intent": test_intent_handler,
    }

    return ChatOrchestrator(
        resolver=resolver,
        filler=filler,
        intents=intents,
        action_handlers=action_handlers,
        schemas=schemas,
    )
