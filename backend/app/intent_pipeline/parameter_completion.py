import json
import time
from typing import Any, TypeVar

import anyio
import structlog
from pydantic import BaseModel, ValidationError

from app.intent_pipeline.schemas.intent import IntentDefinition
from app.services.openai_client import OpenAIClient

logger = structlog.get_logger(__name__)
T = TypeVar("T", bound=BaseModel)


class IncompleteParametersError(Exception):
    """Raised when parameters cannot be completed after max rounds."""

    pass


class MoreInfoRequired(Exception):
    """Raised when more information is needed from the user."""

    def __init__(self, question: str, missing_field: str):
        self.question = question
        self.missing_field = missing_field
        super().__init__(f"More information required for field: {missing_field}")


class ParameterCompletionService:
    """Service for completing missing parameters in intent payloads.

    This class is responsible for filling in missing parameters by asking the user
    for specific information and validating the resulting payload against a schema.

    Attributes:
        openai_client: The OpenAI client for making API calls.
        max_rounds: Maximum number of rounds to attempt parameter completion.
        model: The OpenAI model to use for generating questions.
        temperature: The temperature parameter for the API calls.
    """

    def __init__(
        self,
        openai_client: OpenAIClient,
        *,
        max_rounds: int = 3,
        model: str = "o4-mini",
        temperature: float = 0.3,
    ) -> None:
        """Initialize the ParameterCompletionService with dependencies.

        Args:
            openai_client: The OpenAI client for making API calls.
            max_rounds: Maximum number of rounds to attempt parameter completion.
            model: The OpenAI model to use for generating questions.
            temperature: The temperature parameter for the API calls.
        """
        self.openai_client = openai_client
        self.max_rounds = max_rounds
        self.model = model
        self.temperature = temperature

    async def fill_missing(
        self,
        *,
        user_id: str,
        user_role: str,
        intent: IntentDefinition,
        partial_params: dict[str, Any],
        message_history: list[dict[str, Any]],
        context: dict[str, Any],
        schema: type[T],
    ) -> tuple[dict[str, Any], list[str]]:
        """Fill missing parameters by interacting with the user.

        Args:
            user_id: The ID of the user in the conversation.
            user_role: The role of the user (e.g., "patient", "clinician").
            intent: The intent definition containing required fields.
            partial_params: The parameters that have been collected so far.
            message_history: The conversation history as a list of message dicts.
            context: The context object for storing state.
            schema: The Pydantic model class to validate against.

        Returns:
            A tuple containing the filled parameters and a list of still-missing fields.

        Raises:
            IncompleteParametersError: If parameters cannot be completed after max rounds.
            MoreInfoRequired: If more information is needed from the user.
        """
        logger = structlog.get_logger().bind(
            user_id=user_id, service="parameter_completion", event="fill_missing"
        )

        # Initialize round count in context if not present
        if "parameter_completion_rounds" not in context:
            context["parameter_completion_rounds"] = 0
            logger.info(
                "Initializing parameter completion session",
                intent_name=intent.name,
                max_rounds=self.max_rounds,
            )

        # Get the required fields from the schema
        required_fields = intent.required_fields

        # Try to validate the parameters against the schema
        try:
            valid_model = schema(**partial_params)
            return valid_model.model_dump(), []
        except ValidationError as e:
            # Extract missing fields
            missing_fields = self._extract_missing_fields(e, required_fields)

            # If there are no missing fields but validation failed, it's a different issue
            if not missing_fields:
                raise IncompleteParametersError(
                    "Validation failed but no missing fields identified."
                )

            # Check if the last message is from the user
            if message_history and len(message_history) > 0:
                last_message = message_history[-1]
                if last_message.get("role") == "user":
                    # Extract parameters from the last user message
                    extracted_params = await self._extract_parameters(
                        user_id=user_id,
                        message=last_message.get("content", ""),
                        missing_field=missing_fields[0] if missing_fields else "",
                    )

                    # Merge the extracted parameters with the partial parameters
                    partial_params = {**partial_params, **extracted_params}

                    # Try validation again with the updated parameters
                    try:
                        valid_model = schema(**partial_params)
                        return valid_model.model_dump(), []
                    except ValidationError:
                        # Still missing fields, continue with the process
                        pass

            # Check if we've reached the maximum number of rounds
            if context["parameter_completion_rounds"] >= self.max_rounds:
                # We've reached the maximum number of rounds, raise an error
                raise IncompleteParametersError(
                    f"Failed to complete parameters after {self.max_rounds} rounds. "
                    f"Still missing: {', '.join(missing_fields)}"
                )

            # Increment the round count
            context["parameter_completion_rounds"] += 1

            # Generate a question for the first missing field
            if missing_fields:
                question = await self._generate_question(
                    user_id=user_id,
                    field_name=missing_fields[0],
                    field_description=self._get_field_description(
                        schema, missing_fields[0]
                    ),
                )

                # Raise an exception to indicate that more information is required
                raise MoreInfoRequired(
                    question=question, missing_field=missing_fields[0]
                )

            # If we somehow have no missing fields but validation failed, raise an error
            raise IncompleteParametersError(
                "Validation failed but no missing fields identified."
            )

    def _extract_missing_fields(
        self, validation_error: ValidationError, required_fields: list[str]
    ) -> list[str]:
        """Extract missing field names from a ValidationError.

        Args:
            validation_error: The ValidationError from the schema validation.
            required_fields: The list of required fields from the intent definition.

        Returns:
            A list of field names that are missing or invalid.
        """
        missing_fields = []

        for error in validation_error.errors():
            # Get the field name from the error location
            if error["loc"]:
                field_name = str(
                    error["loc"][0]
                )  # First element of loc tuple is the field name
                if field_name in required_fields and field_name not in missing_fields:
                    missing_fields.append(field_name)

        # If no specific fields were identified, return all required fields
        if not missing_fields:
            return required_fields

        return missing_fields

    def _get_field_description(self, schema: type[BaseModel], field_name: str) -> str:
        """Get the description of a field from the schema.

        Args:
            schema: The Pydantic model class.
            field_name: The name of the field.

        Returns:
            The description of the field, or an empty string if not found.
        """
        try:
            field_info = schema.model_fields.get(field_name)
            if field_info and field_info.description:
                return field_info.description
        except (AttributeError, KeyError):
            pass

        return ""

    async def _generate_question(
        self, *, user_id: str, field_name: str, field_description: str
    ) -> str:
        """Generate a question to ask the user for a specific field.

        Args:
            user_id: The ID of the user in the conversation.
            field_name: The name of the field to ask about.
            field_description: The description of the field from the schema.

        Returns:
            A question to ask the user.
        """
        start_time = time.time()

        # Build the system prompt
        system_prompt = "You are a helpful medical assistant that politely asks for specific information."

        # Build the user prompt
        description_text = f" ({field_description})" if field_description else ""
        user_prompt = (
            f"Generate a polite, concise question asking the user for their "
            f"{field_name}{description_text}. Make it conversational and friendly."
        )

        try:
            response = await self.openai_client.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=100,
                temperature=self.temperature,
                extra={"user": user_id},
            )

            # Extract the question from the response
            question = response.choices[0].message.content.strip()

            # Log the operation
            latency_ms = int((time.time() - start_time) * 1000)
            logger.info(
                "Generated question for missing field",
                user_id=user_id,
                field_name=field_name,
                latency_ms=latency_ms,
                model=self.model,
            )

            return question

        except Exception as e:
            # Log the error
            logger.error(
                "Failed to generate question",
                user_id=user_id,
                field_name=field_name,
                error=str(e),
                error_type=type(e).__name__,
            )
            # Return a generic question as fallback
            return f"Could you please provide your {field_name}?"

    async def _extract_parameters(
        self, *, user_id: str, message: str, missing_field: str
    ) -> dict[str, Any]:
        """Extract parameters from a user message.

        Args:
            user_id: The ID of the user in the conversation.
            message: The user's message to extract parameters from.
            missing_field: The name of the field we're trying to extract.

        Returns:
            A dictionary of extracted parameters.
        """
        start_time = time.time()

        # Build the system prompt
        system_prompt = (
            "You are a data extractor. Given the schema and the last user message, "
            "return JSON with only the field being asked for."
        )

        # Build the user prompt
        user_prompt = f"""Field name: {missing_field}
User message: {message}

Extract the value for this field from the user message and return it as a JSON object with a single key."""

        try:
            response = await self.openai_client.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=100,
                temperature=0.0,  # Use 0.0 for deterministic extraction
                extra={"user": user_id},
            )

            # Extract the JSON from the response
            content = response.choices[0].message.content.strip()

            try:
                # Parse the JSON response
                result = json.loads(content)

                # Log the operation
                latency_ms = int((time.time() - start_time) * 1000)
                logger.info(
                    "Extracted parameters from message",
                    user_id=user_id,
                    field_name=missing_field,
                    latency_ms=latency_ms,
                    model=self.model,
                )

                return result

            except json.JSONDecodeError as e:
                # Log the error
                logger.error(
                    "Failed to parse JSON response",
                    user_id=user_id,
                    field_name=missing_field,
                    error=str(e),
                    response=content,
                )
                # Return an empty dict as fallback
                return {}

        except Exception as e:
            # Log the error
            logger.error(
                "Failed to extract parameters",
                user_id=user_id,
                field_name=missing_field,
                error=str(e),
                error_type=type(e).__name__,
            )
            # Return an empty dict as fallback
            return {}


# FastAPI dependency
def get_parameter_completion_service(
    openai_client: OpenAIClient,
) -> ParameterCompletionService:
    """FastAPI dependency that returns a ParameterCompletionService instance.

    Args:
        openai_client: The OpenAI client for making API calls.

    Returns:
        An instance of ParameterCompletionService.
    """
    return ParameterCompletionService(openai_client)


# Quick test for manual verification
if __name__ == "__main__":
    # Define a simple Pydantic model for testing
    class TestSchema(BaseModel):
        name: str
        age: int
        condition: str

    # Mock OpenAI client for testing
    class MockOpenAIClient:
        async def chat(self, **kwargs):
            # Mock response structure
            class MockResponse:
                class Choice:
                    class Message:
                        def __init__(self, content):
                            self.content = content

                    def __init__(self, content):
                        self.message = self.Message(content)

                def __init__(self, content):
                    self.choices = [self.Choice(content)]

            # Generate mock responses based on the prompt
            if "generate a polite" in kwargs["messages"][1]["content"].lower():
                return MockResponse("Could you please tell me your age?")
            elif "extract the value" in kwargs["messages"][1]["content"].lower():
                return MockResponse('{"age": 42}')
            else:
                return MockResponse("")

    async def run_test():
        # Create the service with the mock client
        service = ParameterCompletionService(MockOpenAIClient())

        # Define test data
        intent_def = IntentDefinition(
            name="test_intent",
            description="Test intent",
            required_fields=["name", "age", "condition"],
        )

        partial_params = {"name": "John Doe", "condition": "Headache"}
        message_history = [{"role": "user", "content": "I'm not feeling well"}]
        context = {}

        try:
            # First call should raise MoreInfoRequired
            filled_params, missing = await service.fill_missing(
                user_id="test_user",
                user_role="patient",
                intent=intent_def,
                partial_params=partial_params,
                message_history=message_history,
                context=context,
                schema=TestSchema,
            )
            print("Unexpected success:", filled_params, missing)
        except MoreInfoRequired as e:
            print(
                f"As expected, more info required: {e.question} (field: {e.missing_field})"
            )

            # Add the user's response to the message history
            message_history.append(
                {
                    "role": "assistant",
                    "content": e.question,
                    "metadata": {"missing_field": e.missing_field},
                }
            )
            message_history.append({"role": "user", "content": "I am 42 years old"})

            # Try again with the updated message history
            try:
                filled_params, missing = await service.fill_missing(
                    user_id="test_user",
                    user_role="patient",
                    intent=intent_def,
                    partial_params=partial_params,
                    message_history=message_history,
                    context=context,
                    schema=TestSchema,
                )
                print("Success! Filled params:", filled_params)
                print("Missing fields:", missing)
            except Exception as e2:
                print(f"Unexpected error on second attempt: {e2}")
        except Exception as e:
            print(f"Unexpected error: {e}")

    # Run the test
    anyio.run(run_test)
