import json
import time

import structlog

from app.intent_pipeline.schemas.intent import IntentDefinition, IntentResolution
from app.services.openai_client import OpenAIClient, get_openai_client

logger = structlog.get_logger(__name__)


class IntentResolverError(Exception):
    """Exception raised when the intent resolver encounters an error."""

    pass


class IntentResolver:
    """Service for resolving user intents from natural language messages.

    This class is responsible for mapping a user's message to a structured intent
    using OpenAI's language models. It builds a prompt containing available intents
    and the user's message, then parses the response into an IntentResolution object.

    Attributes:
        openai_client: The OpenAI client singleton for making API calls.
    """

    def __init__(self, openai_client: OpenAIClient):
        """Initialize the IntentResolver with dependencies.

        Args:
            openai_client: The OpenAI client singleton for making API calls.
        """
        self.openai_client = openai_client

    async def resolve(
        self,
        *,
        user_id: str,
        user_role: str,
        message: str,
        intents: list[IntentDefinition],
        model: str = "o4-mini",
        temperature: float = 0.0,
    ) -> IntentResolution:
        """Resolve a user message to an intent.

        This method builds a prompt containing the available intents and the user's
        message, then sends it to the OpenAI API for resolution. The response is
        parsed into an IntentResolution object.

        Args:
            user_id: The ID of the user sending the message.
            user_role: The role of the user (e.g., "patient", "clinician").
            message: The natural language message from the user.
            intents: List of available intent definitions to match against.
            model: The OpenAI model to use (default: "o4-mini").
            temperature: The temperature parameter for the API call (default: 0.0).

        Returns:
            An IntentResolution object containing the matched intent and parameters.

        Raises:
            IntentResolverError: If there is an error parsing the API response.
        """
        start_time = time.time()

        # Build the intent catalog in YAML format
        intent_catalog = "intents:\n"
        for intent in intents:
            intent_catalog += f"- name: {intent.name}\n"
            intent_catalog += f"  description: {intent.description}\n"
            if intent.required_fields:
                fields_str = ", ".join(intent.required_fields)
                intent_catalog += f"  required_fields: [{fields_str}]\n"

        # Build the user prompt
        user_prompt = f"""# YAML below shows INTENT CATALOG available to the user
{intent_catalog}
---
message: |
  {message}"""

        # Build the system prompt
        system_prompt = "You are a routing engine that maps a user's chat message to an intent schema."

        # Call the OpenAI API
        try:
            response = await self.openai_client.chat(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=256,
                temperature=temperature,
                extra={"user": user_id},
            )

            # Extract the response content
            content = response.choices[0].message.content.strip()

            # Parse the JSON response
            try:
                result = json.loads(content)

                # Create and return the IntentResolution
                resolution = IntentResolution(
                    intent=result.get("intent"),
                    confidence=result.get("confidence", 0.0),
                    filled=result.get("filled", {}),
                    missing=result.get("missing", []),
                )

                # Log the resolution
                latency_ms = int((time.time() - start_time) * 1000)
                logger.info(
                    "Intent resolution completed",
                    user_id=user_id,
                    user_role=user_role,
                    intent=resolution.intent,
                    confidence=resolution.confidence,
                    latency_ms=latency_ms,
                    model=model,
                )

                return resolution

            except json.JSONDecodeError as e:
                raise IntentResolverError(
                    f"Failed to parse JSON response: {e}. Response: {content}"
                )

        except Exception as e:
            # Log the error and re-raise
            logger.error(
                "Intent resolution failed",
                user_id=user_id,
                user_role=user_role,
                error=str(e),
                error_type=type(e).__name__,
                model=model,
            )
            raise IntentResolverError(f"Intent resolution failed: {e}")


def get_intent_resolver() -> IntentResolver:
    """FastAPI dependency that returns an IntentResolver instance.

    Returns:
        An instance of IntentResolver with its dependencies injected.
    """
    return IntentResolver(get_openai_client())
