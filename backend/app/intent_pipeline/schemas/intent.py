from typing import Any, Optional

from pydantic import BaseModel, Field


class IntentDefinition(BaseModel):
    """Definition of an intent that can be resolved from user messages.

    This model represents an intent from the intent catalog that the resolver
    can match against user messages.
    """

    name: str = Field(..., description="Unique identifier for the intent")
    description: str = Field(
        ..., description="Human-readable description of the intent"
    )
    required_fields: list[str] = Field(
        default_factory=list,
        description="Fields that must be filled for the intent to be complete",
    )


class IntentResolution(BaseModel):
    """Result of an intent resolution operation.

    This model represents the output of the intent resolver, including the matched
    intent (if any), confidence score, and parameter information.
    """

    intent: Optional[str] = Field(
        None, description="Name of the matched intent, or None if no match"
    )
    confidence: float = Field(
        ..., description="Confidence score (0-1) for the intent match"
    )
    filled: dict[str, Any] = Field(
        default_factory=dict,
        description="Parameters successfully extracted from the message",
    )
    missing: list[str] = Field(
        default_factory=list, description="Required fields that could not be extracted"
    )


class FillPrompt(BaseModel):
    """LLM prompt sent to user asking for specific missing fields."""

    text: str
    missing: list[str]


class FillResult(BaseModel):
    """Message from filler indicating newly supplied values."""

    filled: dict[str, Any]
    still_missing: list[str] = []
