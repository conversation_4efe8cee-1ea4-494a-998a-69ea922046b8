import anyio
import structlog
from pydantic import BaseModel

from app.intent_pipeline.orchestrator import ChatOrchestrator, IntentDefinition
from app.intent_pipeline.parameter_completion import ParameterCompletionService
from app.intent_pipeline.resolver import IntentResolver

# Configure logging
structlog.configure(
    processors=[
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer(),
    ],
)


class TestSchema(BaseModel):
    """Test schema for the test_intent."""

    foo: str


class MockOpenAIClient:
    """Mock OpenAI client for testing."""

    async def chat(self, **kwargs):
        """Mock chat method that returns a predefined response."""

        # Create a mock response structure
        class MockResponse:
            class Choice:
                class Message:
                    def __init__(self, content):
                        self.content = content

                def __init__(self, content):
                    self.message = self.Message(content)

            def __init__(self, content):
                self.choices = [self.Choice(content)]
                self.usage = None

        # Check what kind of request this is based on the prompt content
        messages = kwargs.get("messages", [])
        prompt_content = ""
        for msg in messages:
            prompt_content += msg.get("content", "")

        # Return a mock intent resolution for the resolver
        if "routing engine" in prompt_content:
            return MockResponse(
                '{"intent": "test_intent", "confidence": 0.9, "filled": {"foo": "bar"}, "missing": []}'
            )

        # Return a mock parameter extraction response for the filler
        return MockResponse('{"foo": "bar"}')

    async def shutdown(self):
        """Mock shutdown method."""
        pass


async def _demo():
    """Smoke test for the ChatOrchestrator."""
    # Create mock clients
    mock_client = MockOpenAIClient()

    # Create test intent definitions
    intents = [
        IntentDefinition(
            name="test_intent",
            description="Test intent for development and testing",
            required_fields=["foo"],
        ),
    ]

    # Define schemas for each intent
    schemas = {
        "test_intent": TestSchema,
    }

    # Define action handlers
    async def test_intent_handler(**kwargs):
        """Handler for the test_intent."""
        return {"message": "Test intent executed", "params": kwargs}

    action_handlers = {
        "test_intent": test_intent_handler,
    }

    # Create the orchestrator directly instead of using the dependency
    resolver = IntentResolver(mock_client)
    filler = ParameterCompletionService(mock_client)
    orch = ChatOrchestrator(
        resolver=resolver,
        filler=filler,
        intents=intents,
        action_handlers=action_handlers,
        schemas=schemas,
    )

    # Test with a message
    print("\nTesting with message: 'My foo is bar'")
    resp = await orch.handle_message(
        user_id="u1",
        user_role="patient",
        message="My foo is bar",
        message_history=[],
        context={},
    )
    print("\nResponse:", resp)


if __name__ == "__main__":
    # Run the demo
    anyio.run(_demo)
