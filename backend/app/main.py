# app/main.py
# Re-export the FastAPI app instance from the root main.py

from app.utils.event_bus import publish_event as event_bus
from app.services.chat_modules.clinician_guidelines_module import (
    ClinicianGuidelinesModule,
)
from app.services.chat_modules.daily_health_coach import DailyHealthCoach
from app.services.chatbot_manager import ChatbotManager
from app.services.event_handlers import register_event_handlers


# Initialize ChatbotManager with the modules
def init_chatbot_manager():
    """Initialize the ChatbotManager with available modules."""

    # Create module instances
    modules = [DailyHealthCoach(), ClinicianGuidelinesModule()]

    # Create ChatbotManager instance
    chatbot_manager = ChatbotManager(modules=modules)

    # Make ChatbotManager available globally
    return chatbot_manager


# Add this to the startup sequence
chatbot_manager = init_chatbot_manager()
register_event_handlers(event_bus)
