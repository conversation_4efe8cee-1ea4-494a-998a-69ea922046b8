# backend/app/models/__init__.py
# This file makes the 'models' directory a Python package and exposes models.

from .access_code import AccessCode
from .appointment import Appointment
from .appointment_request import AppointmentRequest
from .audit_log import AuditLog
from .chat_message import ChatMessage
from .clinic import Clinic
from .clinic_medication_association import ClinicMedicationAssociation
from .clinical_note import ClinicalNote, ClinicalNoteStatus
from .clinician import Clinician, clinician_patient_association
from .content import Content
from .content_chunk import ContentChunk
from .education_material import EducationMaterial, MaterialType
from .education_progress import EducationProgress
from .event_log import EventLog
from .lab_result import LabResult
from .medication import Medication
from .medication_request import MedicationRequest
from .note import Note
from .notification import Notification
from .patient import Patient
from .patient_alert import PatientAlert
from .patient_education_assignment import PatientEducationAssignment, AssignmentPriority, AssignmentStatus
from .scraped_page import ScrapedPage
from .side_effect_report import SideEffectReport
from .template import Template, TemplateRole
from .user_settings import UserSetting
from .weight_log import WeightLog

# Define __all__ to control wildcard imports and explicitly declare public interface
__all__ = [
    "AccessCode",
    "Appointment",
    "AppointmentRequest",
    "AssignmentPriority",
    "AssignmentStatus",
    "AuditLog",
    "ChatMessage",
    "Clinic",
    "ClinicMedicationAssociation",
    "ClinicalNote",
    "ClinicalNoteStatus",
    "Clinician",
    "clinician_patient_association",
    "Content",
    "ContentChunk",
    "EducationMaterial",
    "EducationProgress",
    "EventLog",
    "LabResult",
    "MaterialType",
    "Medication",
    "MedicationRequest",
    "Note",
    "Notification",
    "Patient",
    "PatientAlert",
    "PatientEducationAssignment",
    "ScrapedPage",
    "SideEffectReport",
    "Template",
    "TemplateRole",
    "UserSetting",
    "WeightLog",
]
