from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, ForeignKey, Index, String
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class AccessCode(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'access_codes' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    code = Column(String, unique=True, index=True, nullable=False)
    email = Column(
        String, index=True, nullable=False
    )  # Added email field for invitation
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True)
    is_used = Column(Boolean, default=False, nullable=False, index=True)

    # Link to the clinician who generated the code (optional, depends if Clinician model exists)
    clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )

    # Link to the patient who used the code (nullable until used, should be unique once set)
    # This might become less relevant if we just use the code for Clerk invites,
    # but keep for now for potential tracking.
    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=True, unique=True, index=True
    )

    # Relationships
    clinician = relationship("Clinician", back_populates="access_codes")
    patient = relationship(
        "Patient"
    )  # One-to-one relationship defined by unique patient_id FK

    # Composite index for common query patterns
    __table_args__ = (
        Index("ix_access_codes_clinician_id_created_at", "clinician_id", "created_at"),
    )

    def __repr__(self):
        status = "Used" if self.is_used else "Unused"
        return (
            f"<AccessCode(id='{self.id}', email='{self.email}', "
            f"status='{status}', expires_at='{self.expires_at}')>"
        )
