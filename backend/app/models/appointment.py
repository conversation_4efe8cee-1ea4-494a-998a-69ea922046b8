from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps

from .clinician import Clinician  # noqa: F401 - Used for relationship back_populates

# Assuming Patient and Clinician models are defined in sibling files
# Adjust imports if the actual location differs
from .patient import Patient  # noqa: F401 - Used for relationship back_populates


class Appointment(BaseWithTimestamps):
    """
    SQLAlchemy model for storing Appointment information.
    """

    # __tablename__ is automatically generated as "appointments" by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )
    appointment_datetime = Column(DateTime(timezone=True), nullable=False, index=True)
    duration_minutes = Column(Integer, nullable=False, default=30)
    appointment_type = Column(
        String, nullable=False
    )  # e.g., "Follow-up", "New Patient Visit"
    reason = Column(Text, nullable=True)
    status = Column(
        String, nullable=False, index=True
    )  # e.g., "Scheduled", "Completed", "Cancelled", "No Show"
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_by_id = Column(String, nullable=True)  # Clerk ID of user who cancelled
    cancellation_reason = Column(Text, nullable=True)
    clinician_notes = Column(Text, nullable=True)
    patient_notes = Column(Text, nullable=True)

    # Relationships
    patient = relationship("Patient", back_populates="appointments")
    clinician = relationship("Clinician", back_populates="appointments")
    clinical_notes = relationship("ClinicalNote", back_populates="appointment")

    # Additional indexes can be defined here if needed, for example:
    # __table_args__ = (
    #     Index("ix_appointment_status_datetime", "status", "appointment_datetime"),
    # )
