from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps

from .clinician import Clinician  # noqa: F401 - Used for relationship back_populates
from .patient import Patient  # noqa: F401 - Used for relationship back_populates


class AppointmentRequest(BaseWithTimestamps):
    """
    SQLAlchemy model for storing patient-initiated appointment requests.
    """

    # __tablename__ is automatically generated as "appointment_requests" by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    preferred_datetime = Column(DateTime(timezone=True), nullable=False, index=True)
    reason = Column(Text, nullable=False)
    clinician_preference = Column(String, nullable=True)
    status = Column(
        String, nullable=False, default="pending", index=True
    )  # pending, approved, rejected, cancelled
    reviewed_by_id = Column(
        String, ForeignKey("clinicians.id"), nullable=True, index=True
    )
    reviewed_at = Column(DateTime(timezone=True), nullable=True)
    review_notes = Column(Text, nullable=True)

    # Relationships
    patient = relationship("Patient", back_populates="appointment_requests")
    reviewed_by = relationship("Clinician", foreign_keys=[reviewed_by_id])
