from typing import Optional  # Import Optional for Mapped type hints

from sqlalchemy import Index, String  # Keep Text, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column

# Import the updated base class
from app.db.base_class import BaseWithTimestamps


class AuditLog(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'audit_logs' by BaseWithTimestamps
    # If specific name 'audit_logs' is required, uncomment below:
    # __tablename__ = 'audit_logs'

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps
    # Removed explicit id and timestamp definitions

    actor_user_id: Mapped[Optional[str]] = mapped_column(
        String, index=True, nullable=True
    )  # Assuming user IDs are strings (like Firebase UID or UUID)
    actor_role: Mapped[Optional[str]] = mapped_column(
        String(50), index=True, nullable=True
    )  # e.g., 'patient', 'clinician', 'system'

    action: Mapped[str] = mapped_column(
        String(255), nullable=False, index=True
    )  # e.g., 'LOGIN_SUCCESS', 'VIEW_PATIENT_PROFILE'

    target_resource_type: Mapped[Optional[str]] = mapped_column(
        String(100), index=True, nullable=True
    )  # e.g., 'Patient', 'WeightLog'
    target_resource_id: Mapped[Optional[str]] = mapped_column(
        String, index=True, nullable=True
    )  # ID of the affected resource

    outcome: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True
    )  # e.g., 'SUCCESS', 'FAILURE', 'ATTEMPT'

    details: Mapped[Optional[dict]] = mapped_column(
        JSONB, nullable=True
    )  # Flexible JSONB for extra info
    source_ip: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Store IP address

    # Define composite indexes for common query patterns
    __table_args__ = (
        Index("ix_audit_logs_actor_user_id_created_at", "actor_user_id", "created_at"),
        Index("ix_audit_logs_action_created_at", "action", "created_at"),
        # Consider adding: Index('ix_audit_logs_target_resource_created_at', 'target_resource_type', 'target_resource_id', 'created_at'),
    )

    def __repr__(self):
        # Use created_at inherited from BaseWithTimestamps
        return (
            f"<AuditLog(id='{self.id}', actor_user_id='{self.actor_user_id}', "
            f"action='{self.action}', outcome='{self.outcome}')>"
        )


# Ensure Alembic can see the model by importing it in base.py or a models/__init__.py
# The import in base.py should already exist or be added.
