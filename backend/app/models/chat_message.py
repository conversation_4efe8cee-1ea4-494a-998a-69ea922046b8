from enum import Enum

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    Index,
    String,
    Text,
    inspect,  # Added import
)  # Added Index and String  # Added Boolean, DateTime
from sqlalchemy import (
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.dialects.postgresql import J<PERSON>NB
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class MessageSenderType(str, Enum):
    PATIENT = "PATIENT"
    AGENT = "AGENT"
    CLINICIAN = "CLINICIAN"  # If clinicians can participate in chat
    CLINICAL_NOTE = "CLINICAL_NOTE"  # For clinical notes in chat history


class MessageRouteType(str, Enum):
    PATIENT = "patient"  # Uppercase key maps to lowercase value
    AI = "ai"  # Uppercase key maps to lowercase value
    CLINICIAN = "clinician"  # Direct patient-to-clinician messages


class ChatMessage(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'chat_messages' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Changed to String
    # conversation_id = Column(UUID(as_uuid=True), index=True, nullable=False) # Optional: Group messages by conversation
    sender_type = Column(
        SQLAlchemyEnum(
            MessageSenderType, name="msg_sender_type_enum", create_type=True
        ),
        nullable=False,
    )
    message_content = Column(Text, nullable=False)
    # clinician_id = Column(UUID(as_uuid=True), ForeignKey('clinicians.id'), nullable=True, index=True) # If clinician is sender/recipient

    # Read status fields
    is_read_by_clinician = Column(Boolean, default=False, nullable=False, index=True)
    read_by_clinician_at = Column(DateTime, nullable=True)

    # Message routing field - where the message should be delivered
    message_route = Column(
        SQLAlchemyEnum(
            MessageRouteType,
            name="msg_route_type_enum",
            create_type=True,
            values_callable=lambda obj: [
                e.value for e in obj
            ],  # Use enum values instead of keys
        ),
        nullable=True,
    )
    
    # Message metadata field for storing action results, routing info, etc.
    message_metadata = Column(JSONB, nullable=True, default=dict)

    # Relationship back to Patient
    patient = relationship("Patient", back_populates="chat_messages", lazy="select")
    # clinician = relationship("Clinician", back_populates="chat_messages") # If clinician model exists

    # Composite index for fetching patient chat history ordered by time
    __table_args__ = (
        Index("ix_chat_messages_patient_id_created_at", "patient_id", "created_at"),
        # Add other indexes if needed, e.g., for unread messages
        Index(
            "ix_chat_messages_unread_patient",
            "patient_id",
            "sender_type",
            "is_read_by_clinician",
            postgresql_where=(
                (sender_type == "patient")
                & (is_read_by_clinician.is_(False))  # Corrected comparison
            ),
        ),  # Corrected WHERE clause syntax
    )

    def __repr__(self):
        """Provides a simplified, safe representation for ChatMessage instances,
        avoiding DetachedInstanceError."""
        state = inspect(self)
        cls_name = self.__class__.__name__
        obj_id_repr = "N/A"  # Default representation

        if state.detached:
            # For detached instances, safely try to get id from __dict__
            # This avoids triggering SQLAlchemy's attribute loading mechanism
            obj_id = self.__dict__.get("id", "Unavailable")
            obj_id_repr = f"Detached: {obj_id}"
        elif state.transient:
            # For transient instances (not yet persisted, no ID)
            obj_id_repr = "Transient: No ID"
        elif state.persistent:
            # For persistent instances, check if 'id' is loaded without triggering load
            if "id" in state.unloaded:
                obj_id_repr = "Attached: ID Not Loaded"
            else:
                # 'id' is loaded or accessible directly
                # Accessing self.id should be safe here, but state.identity gives the PK tuple
                obj_id = state.identity[0] if state.identity else "Pending Flush"
                obj_id_repr = f"Attached: {obj_id}"
        else:  # Covers deleted state, etc.
            obj_id_repr = f"State: {state.instance_state}"  # Generic state if not detached/transient/persistent

        return f"<{cls_name}(id={obj_id_repr})>"
