from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Text  # Added DateTime
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps

from .clinic_medication_association import clinic_medication_association


class Clinic(BaseWithTimestamps):
    """
    Represents a healthcare clinic or organization within the system.
    """

    __tablename__ = "clinics"

    name = Column(String(255), nullable=False, index=True)
    address = Column(Text, nullable=True)  # Added address field
    website_url = Column(String(512), nullable=True)
    scraped_data = Column(JSON, nullable=True)  # Added scraped_data JSON field
    last_scraped_at = Column(
        DateTime(timezone=True), nullable=True
    )  # Added last_scraped_at field

    # Placeholder for potential future integration with knowledge base/chatbot specifics
    knowledge_base_id = Column(String(255), nullable=True, index=True)
    chatbot_instance_id = Column(String(255), nullable=True, index=True)

    # Many-to-Many relationship with Clinicians
    clinicians = relationship(
        "Clinician", secondary="clinician_clinic_association", back_populates="clinics"
    )

    # Relationship to ScrapedPages (One-to-Many)
    scraped_pages = relationship(
        "ScrapedPage",
        back_populates="clinic",
        cascade="all, delete-orphan",  # Define cascade behavior
        lazy="selectin",  # Eager loading strategy
    )

    # Many-to-Many relationship with Medications
    medications = relationship(
        "Medication", secondary=clinic_medication_association, back_populates="clinics"
    )

    # Relationship to Templates (One-to-Many)
    templates = relationship(
        "Template",
        back_populates="clinic",
        cascade="all, delete-orphan",  # Define cascade behavior
        lazy="selectin",  # Eager loading strategy
    )

    # Relationship to Education Materials (One-to-Many)
    education_materials = relationship(
        "EducationMaterial",
        back_populates="clinic",
        cascade="all, delete-orphan",
        lazy="select",
    )
