from sqlalchemy import Column, Foreign<PERSON><PERSON>, String, Table

from app.db.base_class import Base

# Association Table for relationship fields (keep for relationship definition)
clinic_medication_association = Table(
    "clinic_medication_association",
    Base.metadata,
    Column("clinic_id", ForeignKey("clinics.id"), primary_key=True),
    Column("medication_id", ForeignKey("medications.id"), primary_key=True),
    Column("notes", String(500), nullable=True),  # Add notes column
)


# ORM Model for CRUD operations, mapped to the same Table
class ClinicMedicationAssociation(Base):
    __table__ = clinic_medication_association
    # Add additional methods or properties if needed
