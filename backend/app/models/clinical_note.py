"""Clinical Note model for AI-generated clinical documentation."""

import uuid
from enum import Enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy.dialects.postgresql import ENUM as PostgreSQLEnum

from app.db.base_class import BaseWithTimestamps


class ClinicalNoteStatus(str, Enum):
    """Status of a clinical note."""
    DRAFT = "draft"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    AMENDED = "amended"


class ClinicalNote(BaseWithTimestamps):
    """Model for AI-generated clinical notes."""
    
    __tablename__ = "clinical_notes"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Core relationships
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    clinician_id = Column(String, ForeignKey("clinicians.clerk_id"), nullable=False, index=True)
    appointment_id = Column(UUID(as_uuid=True), ForeignKey("appointments.id"), nullable=True, index=True)
    chat_session_id = Column(UUID(as_uuid=True), nullable=True)  # For grouping chat messages
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=True)
    
    # Note content
    note_type = Column(String, nullable=False)  # 'SOAP', 'K-SOAP', 'Progress', etc.
    sections = Column(JSONB, nullable=False)  # Structured sections (S/O/A/P fields)
    raw_text = Column(Text, nullable=True)  # Full text version for display/export
    
    # Status and approval
    status = Column(
        PostgreSQLEnum('draft', 'reviewed', 'approved', 'amended', name='clinical_note_status', create_type=False),
        nullable=False,
        default="draft"
    )
    approved_at = Column(DateTime, nullable=True)
    approved_by = Column(String, ForeignKey("clinicians.clerk_id"), nullable=True)
    
    # AI tracking
    ai_generated = Column(Boolean, nullable=False, default=True)
    ai_confidence_score = Column(Float, nullable=True)  # 0.0 to 1.0
    human_edits = Column(JSONB, nullable=True)  # Track manual changes
    
    # Billing support
    suggested_icd10_codes = Column(JSONB, nullable=True)  # List of {code, description, confidence}
    suggested_cpt_codes = Column(JSONB, nullable=True)  # List of {code, description, confidence}
    billing_notes = Column(Text, nullable=True)
    
    # Relationships
    patient = relationship("Patient", back_populates="clinical_notes")
    clinician = relationship("Clinician", foreign_keys=[clinician_id], back_populates="clinical_notes_authored")
    approver = relationship("Clinician", foreign_keys=[approved_by], back_populates="clinical_notes_approved")
    appointment = relationship("Appointment", back_populates="clinical_notes")
    template = relationship("Template", back_populates="clinical_notes")
    
    # Indexes
    __table_args__ = (
        Index("ix_clinical_notes_patient_clinician", "patient_id", "clinician_id"),
        Index("ix_clinical_notes_status", "status"),
        Index("ix_clinical_notes_created_at", "created_at"),
    )
    
    def __repr__(self):
        return f"<ClinicalNote(id={self.id}, patient_id={self.patient_id}, status={self.status})>"