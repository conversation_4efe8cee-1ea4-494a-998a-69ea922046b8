# backend/app/models/clinician.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, String, Table, func, Text, Integer, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps  # Corrected import path if needed

# Association table for many-to-many relationship
# Ensure BaseWithTimestamps.metadata is accessible here. If base_class defines Base, use Base.metadata
# Assuming BaseWithTimestamps inherits from a Base that has metadata
clinician_patient_association = Table(
    "clinician_patient_association",
    BaseWithTimestamps.metadata,  # Make sure this metadata object is correct
    # Changed UUID to String for foreign keys to match updated primary keys
    Column("clinician_id", String, ForeignKey("clinicians.id"), primary_key=True),
    Column("patient_id", String, ForeignKey("patients.id"), primary_key=True),
)


# Association table for Clinician-Clinic many-to-many relationship
clinician_clinic_association = Table(
    "clinician_clinic_association",
    BaseWithTimestamps.metadata,
    Column("clinician_id", String, Foreign<PERSON>ey("clinicians.id"), primary_key=True),
    <PERSON>umn("clinic_id", UUID(as_uuid=True), Foreign<PERSON>ey("clinics.id"), primary_key=True),
)


class Clinician(BaseWithTimestamps):
    __tablename__ = "clinicians"

    # Override the inherited UUID id with a String primary key to store Clerk user ID
    id = Column(String, primary_key=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    specialty = Column(String, nullable=True)
    bio = Column(Text, nullable=True)  # Professional bio/description
    credentials = Column(ARRAY(String), nullable=True)  # Array of credentials (MD, DO, etc.)
    years_experience = Column(Integer, nullable=True)  # Years of professional experience
    is_active = Column(Boolean, nullable=False, default=True)
    clerk_id = Column(
        String, unique=True, index=True, nullable=True
    )  # For Clerk authentication
    photo_url = Column(String, nullable=True)  # URL to profile photo in blob storage
    created_at = Column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )

    # Relationship to patients (many-to-many)
    patients = relationship(
        "Patient",
        secondary=clinician_patient_association,
        back_populates="clinicians",
        lazy="select",  # Consider 'selectin' for performance if often loading patients with clinicians
    )

    # Add missing relationship to SideEffectReport
    side_effect_reports = relationship(
        "SideEffectReport",
        back_populates="clinician",
        cascade="all, delete-orphan",  # Optional: if clinician deletion should delete their reports
        lazy="select",
    )

    # Relationships
    medication_requests = relationship("MedicationRequest", back_populates="clinician")

    access_codes = relationship("AccessCode", back_populates="clinician")
    appointments = relationship(
        "Appointment", back_populates="clinician", lazy="select"
    )
    notifications = relationship(
        "Notification",
        back_populates="recipient_clinician",
        cascade="all, delete-orphan",
        lazy="select",
    )
    notes = relationship(
        "Note", back_populates="clinician", cascade="all, delete-orphan", lazy="select"
    )  # Notes authored by this clinician
    clinical_notes_authored = relationship(
        "ClinicalNote", 
        foreign_keys="[ClinicalNote.clinician_id]",
        back_populates="clinician", 
        cascade="all, delete-orphan", 
        lazy="select"
    )
    clinical_notes_approved = relationship(
        "ClinicalNote",
        foreign_keys="[ClinicalNote.approved_by]",
        back_populates="approver",
        lazy="select"
    )
    ordered_lab_results = relationship(
        "LabResult",
        foreign_keys="[LabResult.clinician_id]",
        back_populates="ordering_clinician",
        lazy="select",
    )

    # Relationship to clinics (many-to-many)
    clinics = relationship(
        "Clinic",
        secondary=clinician_clinic_association,
        back_populates="clinicians",
        lazy="select",
    )

    # Education relationships
    created_education_materials = relationship(
        "EducationMaterial",
        back_populates="creator",
        cascade="all, delete-orphan",
        lazy="select",
    )
    education_assignments = relationship(
        "PatientEducationAssignment",
        back_populates="assigned_by_clinician",
        cascade="all, delete-orphan",
        lazy="select",
    )

    # User settings relationship for LLM/chat config - using a query rather than direct relationship
    @property
    def settings(self):
        # This will be implemented in the code
        # from app.models import UserSetting
        # return db.query(UserSetting).filter(
        #    UserSetting.user_id == self.id,
        #    UserSetting.user_type == 'clinician'
        # ).all()
        pass

    def __repr__(self):
        return f"<Clinician(id={self.id}, email='{self.email}')>"
