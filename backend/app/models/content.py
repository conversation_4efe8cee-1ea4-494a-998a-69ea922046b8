from enum import Enum

from sqlalchemy import (
    Boolean,
    Column,
    Index,
    String,
    Text,
)  # Added Index
from sqlalchemy import (
    Enum as SQLAlchemyEnum,
)

# from sqlalchemy.orm import relationship # No direct relationships defined yet
from app.db.base_class import BaseWithTimestamps


class ContentType(str, Enum):
    ARTICLE = "article"
    FAQ = "faq"
    VIDEO = "video"  # Could store URL or embed code
    RESOURCE_LINK = "resource_link"


class Content(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'contents' by BaseWithTimestamps
    # Note: BaseWithTimestamps pluralizes 'Content' to 'contents'. If 'content' is preferred, override __tablename__.

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    title = Column(String, nullable=False, index=True)  # Keep individual index
    body = Column(
        Text, nullable=True
    )  # Body might be optional if it's just a link/video
    content_type = Column(
        SQLAlchemyEnum(ContentType, name="content_type_enum", create_type=True),
        nullable=False,
        index=True,
    )  # Keep individual index
    url = Column(String, nullable=True)  # For VIDEO or RESOURCE_LINK types
    is_published = Column(
        Boolean, default=False, nullable=False, index=True
    )  # Keep individual index
    category = Column(String, nullable=True, index=True)  # Optional: Categorization
    # target_audience = Column(String, nullable=True, index=True) # Optional: e.g., 'patient', 'clinician', 'all'

    # Composite index for fetching published content by type
    __table_args__ = (
        Index("ix_content_published_type", "is_published", "content_type"),
        # Consider adding: Index('ix_content_published_created', 'is_published', 'created_at'),
    )

    def __repr__(self):
        return (
            f"<Content(id={self.id}, title='{self.title}', type='{self.content_type}')>"
        )
