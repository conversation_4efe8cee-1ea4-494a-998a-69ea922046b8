import uuid

from pgvector.sqlalchemy import Vector  # Import Vector type
from sqlalchemy import Column, ForeignKey, Index, Text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps

# Embedding dimension must match the database schema
# Using sentence-transformers/all-mpnet-base-v2 which produces 768 dimensions
EMBEDDING_DIM = 768


class ContentChunk(BaseWithTimestamps):
    __tablename__ = "content_chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scraped_page_id = Column(
        UUID(as_uuid=True), ForeignKey("scraped_pages.id"), nullable=True, index=True
    )
    chunk_text = Column(Text, nullable=False)
    # Define the embedding column using the VECTOR type from pgvector
    # Specify the dimensions of the vector (must match the embedding model output)
    embedding = Column(
        Vector(EMBEDDING_DIM), nullable=True
    )  # Nullable initially, populated by pipeline
    metadata_ = Column(
        "metadata", JSONB, nullable=True
    )  # Optional metadata (e.g., chunk position, source_type)

    # Define the relationship back to ScrapedPage
    scraped_page = relationship("ScrapedPage", back_populates="content_chunks")

    __table_args__ = (
        # Add an index for the embedding column for efficient similarity search
        # Using HNSW index type is generally recommended for pgvector
        # Note: Index creation might need to be handled carefully in Alembic migrations
        # Index('ix_content_chunks_embedding', embedding, postgresql_using='hnsw', postgresql_with={'m': 16, 'ef_construction': 64}),
        Index("ix_content_chunks_scraped_page_id", "scraped_page_id"),
    )

    def __repr__(self):
        return f"<ContentChunk(id={self.id}, scraped_page_id={self.scraped_page_id}, text='{self.chunk_text[:50]}...')>"
