from sqlalchemy import Column, String, Text, <PERSON><PERSON><PERSON>, Integer, DateTime, Enum as SQLE<PERSON>, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import uuid

from app.db.base_class import Base


class MaterialType(str, enum.Enum):
    PDF = "PDF"
    VIDEO = "VIDEO"
    LINK = "LINK"
    DOCUMENT = "DOCUMENT"


class EducationMaterial(Base):
    __tablename__ = "education_materials"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    type = Column(SQLEnum(MaterialType), nullable=False, index=True)
    category = Column(String(100), nullable=True, index=True)
    content_url = Column(Text, nullable=True)  # URL for links or uploaded files
    file_path = Column(String(500), nullable=True)  # Server file path for uploads
    duration_minutes = Column(Integer, nullable=True)  # For videos/content with duration
    is_public = Column(Boolean, default=False, nullable=False, index=True)  # Public vs clinic-specific
    clinic_id = Column(UUID(as_uuid=True), ForeignKey("clinics.id"), nullable=True, index=True)  # Clinic-specific materials
    created_by = Column(String, ForeignKey("clinicians.id"), nullable=False, index=True)  # Clinician who created it
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Optional analytics fields
    views_count = Column(Integer, default=0, nullable=False)
    assignments_count = Column(Integer, default=0, nullable=False)
    
    # Relationships
    creator = relationship("Clinician", back_populates="created_education_materials")
    clinic = relationship("Clinic", back_populates="education_materials")
    progress_records = relationship("EducationProgress", back_populates="material", cascade="all, delete-orphan")
    assignments = relationship("PatientEducationAssignment", back_populates="material", cascade="all, delete-orphan")