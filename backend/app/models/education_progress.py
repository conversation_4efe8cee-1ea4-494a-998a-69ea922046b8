from sqlalchemy import (
    Column,
    DateTime,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class EducationProgress(BaseWithTimestamps):
    __tablename__ = "education_progress"

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Patient tracking progress
    material_id = Column(
        UUID(as_uuid=True), ForeignKey("education_materials.id"), nullable=False, index=True
    )  # Educational material being tracked
    assignment_id = Column(
        UUID(as_uuid=True), ForeignKey("patient_education_assignments.id"), nullable=False, index=True
    )  # Assignment this progress relates to
    progress_percentage = Column(
        Float, nullable=False, default=0.0, index=True
    )  # 0.0 to 100.0
    time_spent_minutes = Column(
        Integer, nullable=False, default=0
    )  # Total time spent on material
    last_accessed = Column(DateTime, nullable=True, index=True)  # Last time patient accessed material
    completed_at = Column(DateTime, nullable=True, index=True)   # When patient completed material
    patient_feedback = Column(Text, nullable=True)  # Optional feedback from patient
    patient_rating = Column(Integer, nullable=True)  # 1-5 star rating

    # Relationships
    patient = relationship("Patient", back_populates="education_progress")
    material = relationship("EducationMaterial", back_populates="progress_records")
    assignment = relationship("PatientEducationAssignment", back_populates="progress")