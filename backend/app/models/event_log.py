from sqlalchemy import <PERSON>umn, ForeignKey, Index, String
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class EventLog(BaseWithTimestamps):
    """Event log model for tracking LLM-driven actions."""

    __tablename__ = "event_logs"

    actor_user_id = Column(String, index=True, nullable=False)
    actor_role = Column(String(50), index=True, nullable=False)
    action = Column(String(255), nullable=False, index=True)
    target_resource_type = Column(String(100), index=True, nullable=True)
    target_resource_id = Column(String, index=True, nullable=True)
    outcome = Column(String(50), nullable=False, index=True)
    details = Column(JSONB, nullable=True)
    source_ip = Column(String(100), nullable=True)

    # New fields for LLM-driven actions
    original_input = Column(String, nullable=True)
    input_type = Column(String(50), nullable=False, default="text")
    extracted_intent = Column(String(255), nullable=True)
    extracted_parameters = Column(JSONB, nullable=True)
    executed_api_action = Column(String(255), nullable=True)
    clinic_id = Column(UUID(as_uuid=True), ForeignKey("clinics.id"), index=True)

    # Relationships
    clinic = relationship("Clinic")

    # Composite indexes
    __table_args__ = (
        Index("ix_event_logs_actor_created", "actor_user_id", "created_at"),
        Index("ix_event_logs_clinic_created", "clinic_id", "created_at"),
    )

    def __repr__(self) -> str:
        return f"<EventLog {self.action} by {self.actor_role}:{self.actor_user_id} outcome:{self.outcome}>"
