import uuid

from sqlalchemy import Column, Date, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class LabResult(BaseWithTimestamps):
    __tablename__ = "lab_results"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )  # Ordering/responsible clinician
    test_name = Column(String, nullable=False)
    result_date = Column(
        Date, nullable=False, index=True
    )  # Using Date for simplicity, can be DateTime if needed
    status = Column(
        String, nullable=False, default="Pending Review", index=True
    )  # e.g., "Pending Review", "Reviewed"
    result_summary = Column(Text, nullable=True)
    reviewed_at = Column(DateTime(timezone=True), nullable=True)
    reviewed_by_clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=True
    )
    source_document_url = Column(String, nullable=True)

    patient = relationship("Patient", back_populates="lab_results")
    ordering_clinician = relationship(
        "Clinician", foreign_keys=[clinician_id], back_populates="ordered_lab_results"
    )
    reviewing_clinician = relationship(
        "Clinician", foreign_keys=[reviewed_by_clinician_id]
    )  # One-way relationship for reviewer

    __table_args__ = (
        Index("ix_lab_results_patient_id", "patient_id"),
        Index(
            "ix_lab_results_clinician_id", "clinician_id"
        ),  # Index for ordering clinician
        Index("ix_lab_results_result_date", "result_date"),
        Index("ix_lab_results_status", "status"),
    )
