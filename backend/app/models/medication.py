from sqlalchemy import Column, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps

from .clinic_medication_association import clinic_medication_association


class Medication(BaseWithTimestamps):
    """Model for storing medication information."""

    # __tablename__ will be auto-generated as 'medications' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    name = Column(String, nullable=False, unique=True)
    description = Column(Text, nullable=True)
    dosage_guidelines = Column(Text, nullable=True)
    common_side_effects = Column(Text, nullable=True)
    category = Column(String, nullable=True, index=True)

    def __repr__(self):
        return f"<Medication(id={self.id}, name='{self.name}')>"

    # Many-to-Many relationship with Clinics
    clinics = relationship(
        "Clinic", secondary=clinic_medication_association, back_populates="medications"
    )
