from enum import Enum  # Import Enum

from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    Index,
    String,
    Text,
)  # Added Index
from sqlalchemy import (
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


# Define status Enum locally or import from schemas if it exists there
class MedicationRequestStatus(str, Enum):
    PENDING = "Pending"
    APPROVED = "Approved"
    REJECTED = "Rejected"


class MedicationRequest(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'medication_requests' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Changed to String
    medication_name = Column(
        String, nullable=False
    )  # Name of the medication being requested
    dosage = Column(String, nullable=True)  # Dosage information
    frequency = Column(String, nullable=True)  # Frequency of administration
    duration = Column(String, nullable=True)  # Duration of treatment
    notes = Column(Text, nullable=True)  # Optional notes from patient or clinician
    status = Column(
        SQLAlchemyEnum(
            MedicationRequestStatus, name="med_req_status_enum", create_type=True
        ),
        default=MedicationRequestStatus.PENDING,
        nullable=False,
    )  # Removed individual index
    clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=True, index=True
    )  # Changed to String
    resolved_at = Column(
        DateTime(timezone=True), nullable=True
    )  # Optional: Timestamp when resolved

    # Relationship back to Patient
    patient = relationship(
        "Patient", back_populates="medication_requests", lazy="select"
    )
    clinician = relationship(
        "Clinician", back_populates="medication_requests"
    )  # Relationship to Clinician

    # Composite indexes for common query patterns
    __table_args__ = (
        Index("ix_med_req_patient_id_status", "patient_id", "status"),
        Index("ix_med_req_patient_id_created_at", "patient_id", "created_at"),
    )

    def __repr__(self):
        return f"<MedicationRequest(id={self.id}, patient_id={self.patient_id}, medication='{self.medication_name}', status='{self.status}')>"
