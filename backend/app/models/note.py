import uuid

from sqlalchemy import Column, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class Note(BaseWithTimestamps):
    __tablename__ = "notes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )  # Author
    title = Column(String, nullable=True)
    content = Column(Text, nullable=False)
    note_type = Column(
        String, nullable=True, index=True
    )  # e.g., "Clinical", "Administrative"
    associated_entity_type = Column(String, nullable=True)  # e.g., "Appointment"
    associated_entity_id = Column(
        UUID(as_uuid=True), nullable=True
    )  # Simple UUID for now

    patient = relationship("Patient", back_populates="notes")
    clinician = relationship("Clinician", back_populates="notes")  # Author relationship

    __table_args__ = (
        Index("ix_notes_patient_id", "patient_id"),
        Index("ix_notes_clinician_id", "clinician_id"),
        Index("ix_notes_note_type", "note_type"),
        # Optional index on associated entity if frequently queried
        # Index("ix_notes_associated_entity", "associated_entity_type", "associated_entity_id"),
    )
