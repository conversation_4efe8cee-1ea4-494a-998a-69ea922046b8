import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class Notification(BaseWithTimestamps):
    __tablename__ = "notifications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    recipient_clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )
    notification_type = Column(
        String, nullable=False, index=True
    )  # e.g., "New Message", "Task Assigned"
    title = Column(String, nullable=False)
    message = Column(Text, nullable=True)
    related_entity_type = Column(
        String, nullable=True
    )  # e.g., "Patient", "MedicationRequest"
    related_entity_id = Column(UUID(as_uuid=True), nullable=True)  # Simple UUID for now
    is_read = Column(Boolean, default=False, nullable=False, index=True)
    read_at = Column(DateTime(timezone=True), nullable=True)  # Added timezone=True

    recipient_clinician = relationship("Clinician", back_populates="notifications")

    __table_args__ = (
        Index("ix_notifications_recipient_clinician_id", "recipient_clinician_id"),
        Index("ix_notifications_notification_type", "notification_type"),
        Index("ix_notifications_is_read", "is_read"),
        # Optional index on related entity if frequently queried
        # Index("ix_notifications_related_entity", "related_entity_type", "related_entity_id"),
    )
