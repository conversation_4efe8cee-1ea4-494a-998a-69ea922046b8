from sqlalchemy import (
    <PERSON>olean,
    Column,
    Date,
    Float,
    ForeignKey,  # Added ForeignKey
    String,
)
from sqlalchemy.dialects.postgresql import UUID  # Added UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class Patient(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'patients' by BaseWithTimestamps

    # Override the inherited UUID id with a String primary key to store Clerk user ID
    id = Column(String, primary_key=True, index=True, nullable=False)
    # Inherits created_at, updated_at from BaseWithTimestamps

    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    date_of_birth = Column(Date, nullable=True)
    phone_number = Column(
        String, nullable=True, unique=True, index=True
    )  # Added unique/index
    height_cm = Column(Float, nullable=True)  # Store height in cm for consistency
    photo_url = Column(String, nullable=True)  # Added for storing profile photo URL
    goal_weight_kg = Column(Float, nullable=True)  # Target weight in kg
    goal_weight_date = Column(Date, nullable=True)  # Target date to achieve goal weight
    is_active = Column(Boolean, default=True, nullable=False, index=True)  # Added index
    invited_by_clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=True, index=True
    )  # Clerk ID of inviting clinician
    associated_clinic_id = Column(
        UUID(as_uuid=True), ForeignKey("clinics.id"), nullable=True, index=True
    )  # Clinic associated via invitation
    # email_verified = Column(Boolean, default=False, nullable=False) # Consider adding later

    # Relationships (back_populates will be defined in the related models)
    weight_logs = relationship(
        "WeightLog",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )
    side_effect_reports = relationship(
        "SideEffectReport",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )
    medication_requests = relationship(
        "MedicationRequest",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )
    chat_messages = relationship(
        "ChatMessage",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )  # Assuming patient initiates/receives chat
    alerts = relationship(
        "PatientAlert",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )

    appointments = relationship(
        "Appointment", back_populates="patient", cascade="all, delete-orphan"
    )

    appointment_requests = relationship(
        "AppointmentRequest", back_populates="patient", cascade="all, delete-orphan"
    )

    notes = relationship(
        "Note", back_populates="patient", cascade="all, delete-orphan", lazy="select"
    )
    clinical_notes = relationship(
        "ClinicalNote", back_populates="patient", cascade="all, delete-orphan", lazy="select"
    )
    lab_results = relationship(
        "LabResult",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )
    # access_code = relationship("AccessCode", back_populates="patient", uselist=False) # One-to-one if patient uses one code

    # Relationship to clinicians (many-to-many)
    clinicians = relationship(
        "Clinician",
        secondary="clinician_patient_association",  # Matches table defined in clinician.py
        back_populates="patients",
        lazy="select",  # Consider 'selectin' later for performance
    )

    # Relationship to the inviting clinician (one-to-many) - Optional, based on FK above
    # Pass the Column objects directly to foreign_keys
    inviting_clinician = relationship(
        "Clinician", foreign_keys=[invited_by_clinician_id]
    )

    # Relationship to the associated clinic (one-to-many) - Optional, based on FK above
    # Pass the Column objects directly to foreign_keys
    associated_clinic = relationship("Clinic", foreign_keys=[associated_clinic_id])

    # Education relationships
    education_assignments = relationship(
        "PatientEducationAssignment",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )
    education_progress = relationship(
        "EducationProgress",
        back_populates="patient",
        cascade="all, delete-orphan",
        lazy="select",
    )

    # User settings relationship for LLM/chat config - using a query rather than direct relationship
    @property
    def settings(self):
        # This will be implemented in the code
        # from app.models import UserSetting
        # return db.query(UserSetting).filter(
        #    UserSetting.user_id == self.id,
        #    UserSetting.user_type == 'patient'
        # ).all()
        pass

    def __repr__(self):
        return f"<Patient(id={self.id}, email='{self.email}')>"
