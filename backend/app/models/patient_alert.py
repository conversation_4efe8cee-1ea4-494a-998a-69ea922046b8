from sqlalchemy import Column, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class PatientAlert(BaseWithTimestamps):
    __tablename__ = "patient_alerts"

    # id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4) # Inherited from BaseWithTimestamps
    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Changed to String
    alert_type = Column(String, nullable=False, index=True)
    severity = Column(
        String, nullable=False, index=True
    )  # e.g., "High", "Medium", "Low"
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    source = Column(
        String, nullable=False
    )  # e.g., "AI Model v1.2", "Manual Entry", "System Rule"
    status = Column(
        String, nullable=False, default="New", index=True
    )  # e.g., "New", "Acknowledged", "Resolved", "Dismissed"
    resolved_at = Column(DateTime, nullable=True)
    resolved_by_clinician_id = Column(
        String, ForeignKey("clinicians.id"), nullable=True
    )  # Changed to String

    patient = relationship("Patient", back_populates="alerts")
    resolved_by_clinician = relationship(
        "Clinician"
    )  # No back_populates needed if Clinician doesn't need direct access to alerts resolved by them

    __table_args__ = (
        Index("ix_patient_alerts_patient_id", "patient_id"),
        Index("ix_patient_alerts_alert_type", "alert_type"),
        Index("ix_patient_alerts_severity", "severity"),
        Index("ix_patient_alerts_status", "status"),
    )
