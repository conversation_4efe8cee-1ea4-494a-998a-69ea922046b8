from enum import Enum
from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    String,
    Text,
)
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class AssignmentPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class AssignmentStatus(str, Enum):
    ASSIGNED = "assigned"
    VIEWED = "viewed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    OVERDUE = "overdue"


class PatientEducationAssignment(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'patient_education_assignments' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Patient receiving the assignment
    material_id = Column(
        UUID(as_uuid=True), ForeignKey("education_materials.id"), nullable=False, index=True
    )  # Educational material being assigned
    assigned_by = Column(
        String, ForeignKey("clinicians.id"), nullable=False, index=True
    )  # Clinician who made the assignment
    assigned_at = Column(DateTime, nullable=False, index=True)  # When assignment was made
    due_date = Column(DateTime, nullable=True, index=True)     # Optional due date
    priority = Column(
        SQLAlchemyEnum(AssignmentPriority, name="assignment_priority_enum", create_type=True),
        nullable=False,
        default=AssignmentPriority.MEDIUM,
        index=True,
    )
    clinician_notes = Column(Text, nullable=True)  # Notes from clinician about why assigned
    status = Column(
        SQLAlchemyEnum(AssignmentStatus, name="assignment_status_enum", create_type=True),
        nullable=False,
        default=AssignmentStatus.ASSIGNED,
        index=True,
    )

    # Relationships
    patient = relationship("Patient", back_populates="education_assignments")
    material = relationship("EducationMaterial", back_populates="assignments")
    assigned_by_clinician = relationship("Clinician", back_populates="education_assignments")
    progress = relationship(
        "EducationProgress",
        back_populates="assignment",
        uselist=False,  # One-to-one relationship
        cascade="all, delete-orphan",
    )