import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>, UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class ScrapedPage(BaseWithTimestamps):
    __tablename__ = "scraped_pages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    clinic_id = Column(
        UUID(as_uuid=True), ForeignKey("clinics.id"), nullable=False, index=True
    )
    source_url = Column(String, nullable=False, index=True)
    scraped_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    cleaned_content = Column(Text, nullable=True)  # Store cleaned text content
    metadata_ = Column(
        "metadata", JSONB, nullable=True
    )  # Optional metadata (e.g., source type, scrape config)

    # Define the relationship to Clinic (assuming a Clinic model exists)
    clinic = relationship("Clinic", back_populates="scraped_pages")
    # Define the relationship to ContentChunk
    content_chunks = relationship(
        "ContentChunk", back_populates="scraped_page", cascade="all, delete-orphan"
    )

    __table_args__ = (
        Index("ix_scraped_pages_clinic_id_scraped_at", "clinic_id", "scraped_at"),
    )

    def __repr__(self):
        return f"<ScrapedPage(id={self.id}, clinic_id={self.clinic_id}, source_url='{self.source_url[:50]}...')>"
