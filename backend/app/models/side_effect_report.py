import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, Enum, ForeignKey, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType

from app.db.base import Base
from app.schemas.side_effect_report import SeverityLevel, SideEffectStatus


class SideEffectReport(Base):
    __tablename__ = "side_effect_reports"

    id = Column(UUIDType(binary=False), primary_key=True, default=uuid.uuid4)
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False, index=True)
    clinician_id = Column(
        String,
        ForeignKey("clinicians.id"),
        nullable=False,
        index=True,
    )
    reported_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    description = Column(Text, nullable=False)
    severity = Column(
        Enum(
            SeverityLevel,
            native_enum=False,
            values_callable=lambda x: [e.value for e in x],
        ),
        nullable=False,
        index=True,
    )
    status = Column(
        Enum(
            SideEffectStatus,
            native_enum=False,
            name="side_effect_status_enum",
            values_callable=lambda obj: [e.value for e in obj],
        ),
        default=SideEffectStatus.SUBMITTED.value,
        nullable=False,
        index=True,
    )
    resolved_at = Column(DateTime, nullable=True)
    resolution_notes = Column(Text, nullable=True)

    # Define relationships
    patient = relationship("Patient", back_populates="side_effect_reports")
    clinician = relationship("Clinician", back_populates="side_effect_reports")
