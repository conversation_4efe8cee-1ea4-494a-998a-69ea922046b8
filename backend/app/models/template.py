from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class Template(BaseWithTimestamps):
    """Template model for LLM-driven API actions."""

    __tablename__ = "templates"

    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default="1.0")
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    system_prompt = Column(Text, nullable=False)
    default_settings = Column(JSONB, nullable=False, default={})
    actions = Column(JSONB, nullable=False, default=[])
    clinic_id = Column(
        UUID(as_uuid=True), ForeignKey("clinics.id", ondelete="CASCADE"), index=True
    )

    # Relationships
    clinic = relationship("Clinic", back_populates="templates")
    roles = relationship(
        "TemplateRole", back_populates="template", cascade="all, delete-orphan"
    )
    clinical_notes = relationship("ClinicalNote", back_populates="template")

    def __repr__(self) -> str:
        return f"<Template {self.name} v{self.version}>"


class TemplateRole(BaseWithTimestamps):
    """Role association for templates."""

    __tablename__ = "template_roles"

    template_id = Column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    role = Column(String(50), nullable=False)

    # Relationships
    template = relationship("Template", back_populates="roles")

    # Constraints
    __table_args__ = (UniqueConstraint("template_id", "role", name="uq_template_role"),)

    def __repr__(self) -> str:
        return f"<TemplateRole {self.role} for template {self.template_id}>"
