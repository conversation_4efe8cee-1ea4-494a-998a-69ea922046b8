from sqlalchemy import Column, Index, String, Text, UniqueConstraint

from app.db.base_class import BaseWithTimestamps


class UserSetting(BaseWithTimestamps):
    """Model for storing user settings."""

    user_id = Column(String, nullable=False, index=True)
    user_type = Column(String, nullable=False, index=True)  # 'patient' or 'clinician'
    key = Column(String, nullable=False, index=True)
    value = Column(Text, nullable=True)

    # No direct relationship - handled in the parent models

    # Create a composite index on (user_id, user_type)
    __table_args__ = (
        UniqueConstraint("user_id", "user_type", "key", name="uq_user_setting"),
        Index("ix_user_settings_user_id_type", user_id, user_type),
    )

    def __repr__(self):
        return f"<UserSetting(user_id={self.user_id}, type={self.user_type}, key={self.key})>"
