from sqlalchemy import Column, Date, Float, ForeignKey, Index, String
from sqlalchemy.orm import relationship

from app.db.base_class import BaseWithTimestamps


class WeightLog(BaseWithTimestamps):
    # __tablename__ will be auto-generated as 'weight_logs' by BaseWithTimestamps

    # Inherits id (UUID), created_at, updated_at from BaseWithTimestamps

    patient_id = Column(
        String, ForeignKey("patients.id"), nullable=False, index=True
    )  # Changed to String
    weight_kg = Column(Float, nullable=False)
    log_date = Column(
        Date, nullable=False
    )  # Removed individual index, covered by composite

    # Relationship back to Patient
    patient = relationship("Patient", back_populates="weight_logs", lazy="select")

    # Composite index for efficient querying of a patient's logs by date
    __table_args__ = (
        Index("ix_weight_logs_patient_id_log_date", "patient_id", "log_date"),
    )

    def __repr__(self):
        return f"<WeightLog(id={self.id}, patient_id={self.patient_id}, weight_kg={self.weight_kg}, log_date='{self.log_date}')>"
