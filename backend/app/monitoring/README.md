# PulseTrack Monitoring System

The PulseTrack monitoring system provides comprehensive observability for the RAG system with real-time metrics, dashboards, and alerts.

## Components

1. **Prometheus Metrics** - Core metrics exposed via Prometheus client
2. **Grafana Dashboards** - Visual representation of system performance
3. **Alert Rules** - Configurable alerting thresholds 
4. **Quality Evaluation** - Automated assessment of retrieval quality
5. **Cost Tracking** - Monitoring of API usage and costs

## Dashboards

### Retrieval Cache Performance Dashboard

This dashboard provides real-time monitoring of the semantic cache and fallback mechanisms in the retrieval system.

#### Installation

1. Ensure Prometheus is configured to scrape the application metrics
2. Import the `dashboards/retrieval_cache_dashboard.json` file into your Grafana instance

#### Key Metrics

##### Semantic Cache Hit Rate
- **Formula**: `semantic_cache_hit_total / (semantic_cache_hit_total + semantic_cache_miss_total)`
- **Target**: Maintain above 40% for optimal performance
- **Alert**: Configured to trigger if hit rate falls below 40%

##### Semantic Cache Lookup Time
- **Formula**: `rate(semantic_cache_lookup_seconds_sum[5m]) / rate(semantic_cache_lookup_seconds_count[5m])`
- **Target**: Keep below 50ms
- **Alert**: Configured to trigger if average lookup time exceeds 50ms

##### Fallback Rates
- **Metrics**: 
  - `rate(retrieval_fallback_count_total{type="scope_expansion"}[5m])`
  - `rate(retrieval_fallback_count_total{type="sql_fallback"}[5m])`
- **Interpretation**: 
  - Scope expansion fallbacks indicate queries that initially returned no results
  - SQL fallbacks indicate potential Qdrant outages

##### Fallback Rate (% of Cache Misses)
- **Formula**: `sum(rate(retrieval_fallback_count_total[5m])) / sum(rate(semantic_cache_miss_total[5m]))`
- **Target**: Keep below 10%
- **Alert**: Configured to trigger if fallback rate exceeds 10% of cache misses

### Quality Evaluation Dashboard

This dashboard provides insights into the quality of the RAG system's retrieval and response generation:

1. **Retrieval Quality Metrics**:
   - NDCG@5 scores over time
   - Precision@3 metrics
   - Recall@10 trends
   - Mean Reciprocal Rank (MRR)

2. **Hallucination Detection**:
   - Hallucination scores by intent type
   - Trend analysis of hallucination rates
   - Alert thresholds visualization

3. **Empty Retrieval Monitoring**:
   - Empty retrieval rates by intent
   - Trend analysis for retrieval failures
   - Correlation with system changes

4. **Context Token Analysis**:
   - P95 token usage by intent type
   - Token anomaly detection
   - Token budget optimization insights

#### Installation

1. Ensure Prometheus is configured to scrape the application metrics
2. Import the `dashboards/quality_eval.json` file into your Grafana instance

### Cost Tracking Dashboard

This dashboard provides comprehensive monitoring of API costs and token usage:

1. **Cost Overview**:
   - Hourly API cost tracking
   - Cost threshold alerts
   - Weekly cost projections

2. **Provider/Model Breakdown**:
   - Costs by provider (OpenAI, Anthropic, etc.)
   - Costs by model (GPT-4o, Claude, etc.)
   - Operation-specific costs

3. **Token Usage Analysis**:
   - Token consumption by operation
   - Trends in token usage
   - Optimization opportunities

4. **Cache Efficiency**:
   - Semantic cache hit rate impact on costs
   - Cost savings from caching
   - Cache efficiency metrics

#### Installation

1. Ensure Prometheus is configured to scrape the application metrics
2. Import the `dashboards/costs.json` file into your Grafana instance

### RAG Monitoring Dashboard

This comprehensive dashboard provides a complete view of the RAG system's performance, including:

1. **System Overview**:
   - Embedding queue lag
   - Qdrant query performance
   - Token usage and distribution
   - Error rates

2. **Cache Performance**:
   - Semantic cache hit rates
   - Lookup times
   - Cache efficiency metrics

3. **Retrieval Quality**:
   - Empty result rates by intent
   - Hallucination scores
   - NDCG and precision metrics

4. **Fallbacks & Errors**:
   - Fallback usage rates by type
   - API errors by provider
   - Error rate trends

5. **Cost & Performance**:
   - Token usage by operation
   - API cost metrics
   - Cost projections

#### Installation

1. Ensure Prometheus is configured to scrape the application metrics
2. Import the `dashboards/rag_monitoring_dashboard.json` file into your Grafana instance

## Alerts

The system includes predefined alert rules for critical metrics:

1. **EmbeddingQueueLagHigh**: Queue lag > 300s (P1)
2. **QdrantQueryLatencyHigh**: P95 latency > 400ms (P2)
3. **RAGContextTokensAnomaly**: Context tokens > 2000 (Warning)
4. **RetrievalEmptyRateHigh**: Empty rate > 5% (P2)
5. **EmbeddingAPIErrorsHigh**: Error rate > 1% (P1)
6. **RAGHallucinationScoreHigh**: Score > 0.2 (P2)
7. **SemanticCacheHitRateLow**: Hit rate < 40% (Warning)
8. **APIHourlyCostSpike**: Hourly cost > $100 (Warning)

### Installation

1. Add the `alert_rules.yaml` file to your Prometheus configuration
2. Update your Prometheus configuration to include this file
3. Configure alertmanager for your preferred notification channels

## Recording Rules

The system includes Prometheus recording rules for derived metrics to improve query performance and enable alerting on complex expressions:

1. **semantic_cache_hit_rate**: Cache hit rate calculation
2. **retrieval_empty_rate**: Empty retrieval rate by intent type
3. **embedding_api_error_rate**: Embedding API error rate
4. **hourly_api_cost_usd**: Hourly API cost in USD
5. **daily_api_cost_by_provider_model_usd**: Daily API cost by provider and model
6. **hourly_token_usage_by_operation**: Hourly token usage by operation
7. **qdrant_p95_query_latency_seconds**: P95 Qdrant query latency by collection
8. **rag_ctx_tokens_p95**: P95 RAG context tokens by intent type

### Installation

1. Add the `recording_rules.yaml` file to your Prometheus configuration
2. Update your Prometheus configuration to include this file

## Automated Scripts

The system includes automated scripts for quality evaluation and cost tracking:

### Quality Evaluation Script

Runs nightly to evaluate the RAG system against a golden dataset of queries:

```bash
python -m app.scripts.run_quality_evaluation --verbose
```

Features:
- Evaluates retrieval quality using standard IR metrics
- Detects hallucinations in generated responses
- Updates Prometheus metrics
- Logs missed queries for analysis
- Generates detailed reports

### Cost Tracking Script

Tracks API costs and token usage across different providers and models:

```bash
python -m app.scripts.cost_tracker --report --days=30 --output=cost_report.csv
```

Features:
- Tracks token usage by model and operation
- Calculates costs based on current pricing
- Generates detailed cost reports
- Projects future costs based on usage trends

## Quality Evaluation System

The quality evaluation system automatically assesses the performance of the RAG system against a golden dataset of queries:

### Golden Dataset

A curated set of queries with known relevant documents that serves as ground truth for evaluation.

- Location: `backend/app/evaluation/datasets/golden_dataset.json`
- Structure: A collection of queries, each with relevant document IDs
- Management: Use the `quality_evaluation.py` module to create and update datasets

### Metrics

The system calculates standard information retrieval metrics:

1. **NDCG@5**: Normalized Discounted Cumulative Gain at 5 results
2. **Precision@3**: Precision of the top 3 results
3. **Recall@10**: Recall of the top 10 results
4. **MRR**: Mean Reciprocal Rank

### Hallucination Detection

Analyzes responses for statements not supported by the retrieved context:

1. Extracts factual claims from responses
2. Compares claims against the context
3. Scores responses on a 0-1 scale
4. Records and alerts on high hallucination scores

## Cost Tracking System

The cost tracking system monitors token usage and API costs:

### Model Costs

Configured costs per 1K tokens for different models:

- OpenAI embedding models
- OpenAI completion models
- Local fallback models
- Reranking models

### Metrics

1. **Token Usage**: Tracks tokens used by operation and model
2. **API Costs**: Calculates costs based on usage and model rates
3. **Projections**: Provides hourly, daily, and monthly cost projections

### Cost Reports

Use the `cost_tracking.py` module to generate detailed cost reports:

```python
from app.evaluation.cost_tracking import get_cost_tracker

# Get cost report
report = get_cost_tracker().get_cost_report()
print(f"Current monthly cost projection: ${report['monthly_cost_projection']:.2f}")
```

## Troubleshooting

### Low Cache Hit Rate
- Check if `SEMANTIC_CACHE_THRESHOLD` is set too high (default: 0.92)
- Consider decreasing threshold to 0.85-0.90 for more hits
- Verify cache TTL (`SEMANTIC_CACHE_TTL`) is appropriate for your workload

### High Lookup Times
- Check Redis performance and connection pool settings
- Consider implementing Redis Vector or other optimized similarity search
- Verify early-break limit (currently 50 keys) is appropriate for your scale

### High Fallback Rates
- For scope expansion fallbacks: review query patterns and collection coverage
- For SQL fallbacks: check Qdrant health and connectivity
- Review the Redis stream `retrieval_fallbacks` for specific queries that triggered fallbacks

### High Hallucination Scores
- Check context relevance to common queries
- Review embedding quality and chunking strategies
- Consider tuning retrieval parameters for better context quality