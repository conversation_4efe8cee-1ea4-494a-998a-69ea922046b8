groups:
- name: rag_monitoring_alerts
  rules:
  - alert: EmbeddingQueueLagHigh
    expr: embedding_queue_lag_seconds > 300
    for: 5m
    labels:
      severity: p1
    annotations:
      summary: "Embedding queue lag is too high"
      description: "Embedding queue lag is {{ $value }}s (threshold: 300s)"

  - alert: QdrantQueryLatencyHigh
    expr: >
      histogram_quantile(0.95, sum(rate(qdrant_query_duration_seconds_bucket{operation="search"}[5m])) by (le, collection)) > 0.4
    for: 5m
    labels:
      severity: p2
    annotations:
      summary: "Qdrant P95 query latency is too high"
      description: "Qdrant P95 query latency for collection {{ $labels.collection }} is {{ $value }}s (threshold: 0.4s)"

  - alert: RAGContextTokensAnomaly
    expr: histogram_quantile(0.95, sum(rate(rag_ctx_tokens_bucket[5m])) by (le, intent_type)) > 2000
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "RAG context tokens anomaly detected"
      description: "RAG context for intent {{ $labels.intent_type }} has {{ $value }} tokens (threshold: 2000)"

  - alert: RetrievalEmptyRateHigh
    expr: >
      sum(rate(retrieval_empty_total[5m])) by (intent_type) / sum(rate(retrieval_total[5m])) by (intent_type) > 0.05
    for: 5m
    labels:
      severity: p2
    annotations:
      summary: "Retrieval empty rate is too high"
      description: "Retrieval empty rate for intent {{ $labels.intent_type }} is {{ $value | humanizePercentage }} (threshold: 5%)"

  - alert: EmbeddingAPIErrorsHigh
    expr: sum(rate(embedding_api_errors_total[5m])) / sum(rate(token_usage_total{operation="embedding"}[5m])) > 0.01
    for: 5m
    labels:
      severity: p1
    annotations:
      summary: "Embedding API error rate is too high"
      description: "Embedding API error rate is {{ $value | humanizePercentage }} (threshold: 1%)"

  - alert: RAGHallucinationScoreHigh
    expr: rag_hallucination_score > 0.2
    for: 10m
    labels:
      severity: p2
    annotations:
      summary: "RAG hallucination score is too high"
      description: "RAG hallucination score for intent {{ $labels.intent_type }} is {{ $value | humanizePercentage }} (threshold: 20%)"

  - alert: SemanticCacheHitRateLow
    expr: >
      rate(semantic_cache_hit_total[30m]) / (rate(semantic_cache_hit_total[30m]) + rate(semantic_cache_miss_total[30m])) < 0.4
    for: 30m
    labels:
      severity: warning
    annotations:
      summary: "Semantic cache hit rate is too low"
      description: "Semantic cache hit rate is {{ $value | humanizePercentage }} (threshold: 40%)"

  - alert: APIHourlyCostSpike
    expr: >
      sum(rate(api_cost_total[1h])) * 3600 > 100
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "API hourly cost exceeded $100"
      description: "Current API cost rate is ${{ $value | humanize }} per hour"