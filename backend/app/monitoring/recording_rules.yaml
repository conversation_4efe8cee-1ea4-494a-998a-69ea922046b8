groups:
  - name: rag_recording_rules
    interval: 30s
    rules:
      # Semantic cache hit rate recording rule
      - record: semantic_cache_hit_rate
        expr: |
          rate(semantic_cache_hit_total[30m]) / 
          (rate(semantic_cache_hit_total[30m]) + rate(semantic_cache_miss_total[30m]))
        labels:
          job: "pulsetrack"
      
      # Retrieval empty rate by intent type
      - record: retrieval_empty_rate
        expr: |
          sum(rate(retrieval_empty_total[5m])) by (intent_type) / 
          sum(rate(retrieval_total[5m])) by (intent_type)
        labels:
          job: "pulsetrack"
      
      # Embedding API error rate
      - record: embedding_api_error_rate
        expr: |
          sum(rate(embedding_api_errors_total[5m])) / 
          sum(rate(token_usage_total{operation="embedding"}[5m]))
        labels:
          job: "pulsetrack"
      
      # Hourly API cost
      - record: hourly_api_cost_usd
        expr: sum(rate(api_cost_total[1h])) * 3600
        labels:
          job: "pulsetrack"
      
      # Daily API cost by provider and model
      - record: daily_api_cost_by_provider_model_usd
        expr: sum(rate(api_cost_total[24h])) by (provider, model) * 86400
        labels:
          job: "pulsetrack"
      
      # Hourly token usage by operation
      - record: hourly_token_usage_by_operation
        expr: sum(rate(token_usage_total[1h])) by (operation) * 3600
        labels:
          job: "pulsetrack"
      
      # P95 Qdrant query latency by collection
      - record: qdrant_p95_query_latency_seconds
        expr: |
          histogram_quantile(0.95, sum(rate(qdrant_query_duration_seconds_bucket{operation="search"}[5m])) by (le, collection))
        labels:
          job: "pulsetrack"
      
      # P95 RAG context tokens by intent type
      - record: rag_ctx_tokens_p95
        expr: |
          histogram_quantile(0.95, sum(rate(rag_ctx_tokens_bucket[5m])) by (le, intent_type))
        labels:
          job: "pulsetrack"
