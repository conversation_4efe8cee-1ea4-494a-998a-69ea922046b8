# backend/app/schemas/__init__.py
from .appointment import (
    AppointmentCreate,
    AppointmentResponse,
    AppointmentStatusUpdate,
    AppointmentUpdate,
    PaginatedAppointmentResponse,
)
from .audit_log import AuditLog, AuditLogCreate
from .auth import (
    AccessCode,
    AccessCodeCreate,
    AccessCodeResponse,
    MagicCodeRequest,
    MagicCodeResponse,
    TokenPayload,
    TokenResponse,
)
from .chat import ChatMessageCreateInternal, ChatMessageRead, ChatMessageResponse
from .clinical_note import (
    ClinicalNote,
    ClinicalNoteCreate,
    ClinicalNoteUpdate,
    ClinicalNoteApprove,
    ClinicalNoteList,
    ClinicalNoteWithRelations,
    GenerateClinicalNoteRequest,
    GenerateClinicalNoteResponse,
)
from .clinician import Clinician, ClinicianCreate, ClinicianUpdate
from .content import Content, ContentCreate, ContentUpdate
from .dashboard import (
    AIPrioritizedDashboardResponse,
    AISummaryResponse,
    DashboardTaskSummaryResponse,
    InsightItem,
    PrioritizedCard,
)
from .education_material import (
    EducationMaterial,
    EducationMaterialCreate,
    EducationMaterialSummary,
    EducationMaterialUpdate,
    FileUploadResponse,
)
from .education_progress import (
    EducationProgress,
    EducationProgressCreate,
    EducationProgressSummary,
    EducationProgressUpdate,
    EducationProgressWithMaterial,
)
from .event_log import EventLog, EventLogCreate, EventLogSummary, EventLogUpdate
from .events import (
    BaseEvent,
    PatientRegisteredEvent,
    SideEffectReportedEvent,
    WeightLoggedEvent,
)
from .lab_result import LabResultCreate, LabResultRead, LabResultUpdate
from .medication_request import (
    MedicationRequestCreate,
    MedicationRequestResponse,
    MedicationRequestUpdate,
)

# from .note import NoteCreate, NoteRead, NoteUpdate # Assuming note schemas exist
from .notification import NotificationCreate, NotificationRead, NotificationUpdate
from .patient import (
    Patient,
    PatientCreate,
    PatientProfileResponse,
    PatientUpdate,
)
from .patient_alert import PatientAlertCreate, PatientAlertRead, PatientAlertUpdate
from .patient_education_assignment import (
    PatientEducationAssignment,
    PatientEducationAssignmentCreate,
    PatientEducationAssignmentSummary,
    PatientEducationAssignmentUpdate,
    PatientEducationAssignmentWithMaterial,
)
from .search import UniversalSearchResult

# from .search import SearchResult # Assuming search schemas exist
from .side_effect_report import (
    SeverityLevel,
    SideEffectReportCreate,
    SideEffectReportResponse,
    SideEffectReportUpdate,
)
from .template import (
    Template,
    TemplateCreate,
    TemplateRole,
    TemplateRoleCreate,
    TemplateRoleUpdate,
    TemplateUpdate,
    TemplateWithRoles,
)
from .weight_log import WeightLogCreate, WeightLogEntryResponse, WeightLogResponse
from .goal_weight import GoalWeightUpdate, GoalWeightResponse, GoalWeightProgressSummary
from .demo import StandardResponse, DemoStatusResponse, DemoAccountInfo, DataCounts

# Define __all__ to control wildcard imports and explicitly declare public interface
__all__ = [
    "AppointmentCreate",
    "AppointmentResponse",
    "AppointmentUpdate",
    "AppointmentStatusUpdate",
    "PaginatedAppointmentResponse",
    "AuditLogCreate",
    "AuditLog",
    "TokenResponse",
    "TokenPayload",
    "MagicCodeRequest",
    "MagicCodeResponse",
    "AccessCodeCreate",
    "AccessCode",
    "AccessCodeResponse",
    "ChatMessageRead",
    "ChatMessageCreateInternal",
    "ChatMessageResponse",
    "ClinicalNote",
    "ClinicalNoteCreate",
    "ClinicalNoteUpdate",
    "ClinicalNoteApprove",
    "ClinicalNoteList",
    "ClinicalNoteWithRelations",
    "GenerateClinicalNoteRequest",
    "GenerateClinicalNoteResponse",
    "ClinicianCreate",
    "Clinician",
    "ClinicianUpdate",
    "ContentCreate",
    "Content",
    "ContentUpdate",
    "DashboardTaskSummaryResponse",
    "AISummaryResponse",
    "EducationMaterial",
    "EducationMaterialCreate",
    "EducationMaterialSummary",
    "EducationMaterialUpdate",
    "EducationProgress",
    "EducationProgressCreate",
    "EducationProgressSummary",
    "EducationProgressUpdate",
    "EducationProgressWithMaterial",
    "EventLog",
    "FileUploadResponse",
    "EventLogCreate",
    "EventLogSummary",
    "EventLogUpdate",
    "BaseEvent",
    "WeightLoggedEvent",
    "SideEffectReportedEvent",
    "PatientRegisteredEvent",
    "LabResultCreate",
    "LabResultRead",
    "LabResultUpdate",
    "MedicationRequestCreate",
    "MedicationRequestResponse",
    "MedicationRequestUpdate",
    "NotificationCreate",
    "NotificationRead",
    "NotificationUpdate",
    "PatientAlertCreate",
    "PatientAlertRead",
    "PatientAlertUpdate",
    "PatientCreate",
    "PatientEducationAssignment",
    "PatientEducationAssignmentCreate",
    "PatientEducationAssignmentSummary",
    "PatientEducationAssignmentUpdate",
    "PatientEducationAssignmentWithMaterial",
    "Patient",
    "PatientUpdate",
    "PatientProfileResponse",
    "UniversalSearchResult",
    "SideEffectReportCreate",
    "SideEffectReportResponse",
    "SideEffectReportUpdate",
    "SeverityLevel",
    "Template",
    "TemplateCreate",
    "TemplateUpdate",
    "TemplateWithRoles",
    "TemplateRole",
    "TemplateRoleCreate",
    "TemplateRoleUpdate",
    "WeightLogCreate",
    "WeightLogResponse",
    "WeightLogEntryResponse",
    "GoalWeightUpdate",
    "GoalWeightResponse", 
    "GoalWeightProgressSummary",
    "StandardResponse",
    "DemoStatusResponse",
    "DemoAccountInfo",
    "DataCounts",
]
