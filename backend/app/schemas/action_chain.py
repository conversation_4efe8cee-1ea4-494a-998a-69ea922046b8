"""Schema models for action chaining in LLM-driven workflows.

This module defines the data structures for complex multi-step actions,
enabling workflows like "report side effects and schedule appointment"
or "log multiple weight entries" through natural language.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.services.intent_resolver_service import ResolvedIntent


class ExecutionMode(str, Enum):
    """How actions in a chain should be executed."""
    
    SEQUENTIAL = "sequential"  # One after another
    PARALLEL = "parallel"  # Simultaneously (where possible)


class FailureMode(str, Enum):
    """How to handle failures in action chains."""
    
    STOP_ON_FAILURE = "stop_on_failure"  # Halt chain on first failure
    CONTINUE_ON_FAILURE = "continue_on_failure"  # Continue with remaining actions


class ActionDependency(BaseModel):
    """Defines dependencies between actions in a chain."""
    
    depends_on: str = Field(
        ..., 
        description="ID of the action this depends on"
    )
    parameter_mapping: Dict[str, str] = Field(
        default_factory=dict,
        description="Map output parameters to input (e.g., 'appointment_id': 'data.appointment_id')"
    )
    required: bool = Field(
        default=True,
        description="Whether this dependency must succeed for the action to execute"
    )


class ChainedIntent(BaseModel):
    """Extended ResolvedIntent for use in action chains."""
    
    action_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for this action in the chain"
    )
    intent: ResolvedIntent = Field(
        ...,
        description="The resolved intent to execute"
    )
    dependencies: List[ActionDependency] = Field(
        default_factory=list,
        description="Actions this depends on"
    )
    condition: Optional[str] = Field(
        None,
        description="Condition that must be true for execution (e.g., 'severity >= major')"
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Maximum time to wait for this action"
    )


class ChainedAction(BaseModel):
    """Model for defining sequences of related actions."""
    
    chain_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for this chain"
    )
    primary_action: ChainedIntent = Field(
        ...,
        description="The main action to execute first"
    )
    follow_up_actions: List[ChainedIntent] = Field(
        default_factory=list,
        description="Actions to execute after the primary"
    )
    execution_mode: ExecutionMode = Field(
        default=ExecutionMode.SEQUENTIAL,
        description="How to execute the actions"
    )
    failure_mode: FailureMode = Field(
        default=FailureMode.STOP_ON_FAILURE,
        description="How to handle failures"
    )
    context_passing: bool = Field(
        default=True,
        description="Whether to pass data between actions"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional chain metadata (pattern, source, etc.)"
    )
    max_execution_time: Optional[int] = Field(
        default=30,
        description="Maximum seconds for entire chain execution"
    )
    
    @property
    def total_actions(self) -> int:
        """Total number of actions in the chain."""
        return 1 + len(self.follow_up_actions)
    
    def get_action_by_id(self, action_id: str) -> Optional[ChainedIntent]:
        """Get a specific action by its ID."""
        if self.primary_action.action_id == action_id:
            return self.primary_action
        
        for action in self.follow_up_actions:
            if action.action_id == action_id:
                return action
        
        return None
    
    def get_all_actions(self) -> List[ChainedIntent]:
        """Get all actions in execution order."""
        return [self.primary_action] + self.follow_up_actions


class ActionExecutionStatus(str, Enum):
    """Status of an individual action execution."""
    
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"  # Due to dependency or condition
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class ActionResult(BaseModel):
    """Result of executing a single action in a chain."""
    
    action_id: str = Field(
        ...,
        description="ID of the action that was executed"
    )
    action_type: str = Field(
        ...,
        description="Type of action (e.g., 'appointment_create')"
    )
    status: ActionExecutionStatus = Field(
        ...,
        description="Execution status"
    )
    success: bool = Field(
        ...,
        description="Whether the action succeeded"
    )
    message: str = Field(
        ...,
        description="User-friendly message about the result"
    )
    data: Optional[Dict[str, Any]] = Field(
        None,
        description="Action-specific result data"
    )
    error: Optional[str] = Field(
        None,
        description="Error message if failed"
    )
    execution_time_ms: float = Field(
        ...,
        description="Time taken to execute in milliseconds"
    )
    started_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When execution started"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="When execution completed"
    )
    
    @property
    def is_terminal(self) -> bool:
        """Whether this result represents a terminal state."""
        return self.status in [
            ActionExecutionStatus.SUCCESS,
            ActionExecutionStatus.FAILED,
            ActionExecutionStatus.TIMEOUT,
            ActionExecutionStatus.CANCELLED
        ]


class ActionChainResult(BaseModel):
    """Result of executing an entire action chain."""
    
    chain_id: str = Field(
        ...,
        description="ID of the chain that was executed"
    )
    success: bool = Field(
        ...,
        description="Whether the entire chain succeeded"
    )
    total_actions: int = Field(
        ...,
        description="Total number of actions in the chain"
    )
    successful_actions: int = Field(
        ...,
        description="Number of successfully executed actions"
    )
    failed_actions: int = Field(
        ...,
        description="Number of failed actions"
    )
    skipped_actions: int = Field(
        ...,
        description="Number of skipped actions"
    )
    execution_time_ms: float = Field(
        ...,
        description="Total execution time in milliseconds"
    )
    results: List[ActionResult] = Field(
        default_factory=list,
        description="Individual action results in execution order"
    )
    summary: str = Field(
        ...,
        description="Human-readable summary of the chain execution"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the execution"
    )
    started_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When chain execution started"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="When chain execution completed"
    )
    
    def get_result_by_action_id(self, action_id: str) -> Optional[ActionResult]:
        """Get the result for a specific action."""
        for result in self.results:
            if result.action_id == action_id:
                return result
        return None
    
    def get_successful_results(self) -> List[ActionResult]:
        """Get only successful action results."""
        return [r for r in self.results if r.success]
    
    def get_failed_results(self) -> List[ActionResult]:
        """Get only failed action results."""
        return [r for r in self.results if not r.success and r.status == ActionExecutionStatus.FAILED]
    
    @property
    def has_failures(self) -> bool:
        """Whether any actions failed."""
        return self.failed_actions > 0
    
    @property
    def partial_success(self) -> bool:
        """Whether some but not all actions succeeded."""
        return self.successful_actions > 0 and self.failed_actions > 0


class ChainContext(BaseModel):
    """Context passed between actions in a chain."""
    
    chain_id: str = Field(
        ...,
        description="ID of the executing chain"
    )
    user_id: str = Field(
        ...,
        description="ID of the user executing the chain"
    )
    user_role: str = Field(
        ...,
        description="Role of the user"
    )
    chat_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Original chat context"
    )
    shared_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Data shared between actions (action_id -> data)"
    )
    execution_history: List[str] = Field(
        default_factory=list,
        description="Order of action execution"
    )
    
    def get_action_data(self, action_id: str) -> Optional[Dict[str, Any]]:
        """Get data from a previously executed action."""
        return self.shared_data.get(action_id)
    
    def set_action_data(self, action_id: str, data: Dict[str, Any]) -> None:
        """Store data from an executed action."""
        self.shared_data[action_id] = data
        if action_id not in self.execution_history:
            self.execution_history.append(action_id)


class ChainValidationResult(BaseModel):
    """Result of validating an action chain before execution."""
    
    is_valid: bool = Field(
        ...,
        description="Whether the chain is valid for execution"
    )
    errors: List[str] = Field(
        default_factory=list,
        description="List of validation errors"
    )
    warnings: List[str] = Field(
        default_factory=list,
        description="List of validation warnings"
    )
    
    def add_error(self, error: str) -> None:
        """Add a validation error."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add a validation warning."""
        self.warnings.append(warning)


# Common chain patterns for reuse
class ChainPattern(str, Enum):
    """Common patterns for action chains."""
    
    APPOINTMENT_REMINDER = "appointment_reminder"
    SIDE_EFFECT_FOLLOWUP = "side_effect_followup"
    BATCH_LOGGING = "batch_logging"
    CONDITIONAL_SCHEDULING = "conditional_scheduling"
    CUSTOM = "custom"