"""
Simplified schema models for action chaining that match the implementation.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ExecutionMode(str, Enum):
    """How actions in a chain should be executed."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"


class FailureMode(str, Enum):
    """How to handle failures in action chains."""
    STOP_ON_FAILURE = "stop_on_failure"
    CONTINUE_ON_FAILURE = "continue_on_failure"


class ActionDependency(BaseModel):
    """Defines dependencies between actions in a chain."""
    required_action: str = Field(..., description="Action type this depends on")
    require_success: bool = Field(default=True, description="Whether the dependency must succeed")


class ChainedIntent(BaseModel):
    """Simplified intent for use in action chains."""
    action_type: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    dependencies: List[ActionDependency] = Field(default_factory=list)
    context_requirements: List[str] = Field(default_factory=list)


class ChainedAction(BaseModel):
    """Model for defining sequences of related actions."""
    chain_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    primary_action: ChainedIntent
    follow_up_actions: List[ChainedIntent] = Field(default_factory=list)
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    failure_mode: FailureMode = FailureMode.STOP_ON_FAILURE


class ActionResult(BaseModel):
    """Result of executing a single action in a chain."""
    action_type: str
    success: bool
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    executed_at: datetime = Field(default_factory=datetime.utcnow)


class ChainContext(BaseModel):
    """Context passed between actions in a chain."""
    data: Dict[str, Any] = Field(default_factory=dict)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from context."""
        # Support nested keys like "appointment_create.appointment_id"
        if "." in key:
            parts = key.split(".", 1)
            if parts[0] in self.data and isinstance(self.data[parts[0]], dict):
                return self.data[parts[0]].get(parts[1], default)
        return self.data.get(key, default)
    
    def update_from_action_result(self, action_type: str, result_data: Dict[str, Any]):
        """Update context with action result data."""
        if result_data:
            self.data[action_type] = result_data
            # Also add top-level keys for common fields
            for key, value in result_data.items():
                if key in ["appointment_id", "notification_id", "patient_id", "clinician_id"]:
                    self.data[key] = value


class ChainValidationResult(BaseModel):
    """Result of validating a chain configuration."""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)


class ActionChainResult(BaseModel):
    """Result of executing an entire action chain."""
    chain_id: str
    success: bool
    results: List[ActionResult]
    context: ChainContext
    error_message: Optional[str] = None
    started_at: datetime
    completed_at: datetime
    execution_time_ms: Optional[int] = None