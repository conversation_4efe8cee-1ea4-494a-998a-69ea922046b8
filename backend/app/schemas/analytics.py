from typing import List, Dict, Optional, Any
from datetime import datetime
from pydantic import BaseModel
from enum import Enum


class RiskLevel(str, Enum):
    low = "low"
    moderate = "moderate"
    high = "high"
    critical = "critical"


class TreatmentStatus(str, Enum):
    pre_treatment = "pre_treatment"
    active = "active"
    maintenance = "maintenance"
    discontinued = "discontinued"


class MetricTrend(BaseModel):
    current: float
    previous: float
    change_percentage: float
    trend: str  # "up", "down", "stable"


class PopulationOverview(BaseModel):
    total_patients: int
    active_patients: int
    new_patients_this_month: int
    average_weight_loss: MetricTrend
    adherence_rate: MetricTrend
    satisfaction_score: MetricTrend
    side_effect_rate: MetricTrend


class RiskPatient(BaseModel):
    patient_id: str
    patient_name: str
    risk_level: RiskLevel
    risk_factors: List[str]
    days_since_last_interaction: int
    weight_trend: str
    adherence_percentage: float
    urgent_action_required: bool


class RiskStratification(BaseModel):
    high_risk_patients: List[RiskPatient]
    risk_distribution: Dict[str, int]  # risk_level -> count
    top_risk_factors: List[Dict[str, Any]]  # factor, count, percentage


class MedicationAnalytics(BaseModel):
    medication_name: str
    patient_count: int
    average_efficacy: float
    discontinuation_rate: float
    average_side_effects_per_patient: float
    success_rate: float


class TreatmentAnalytics(BaseModel):
    medications_by_usage: List[MedicationAnalytics]
    treatment_phase_distribution: Dict[str, int]
    average_time_to_goal: float
    success_rate_by_medication: Dict[str, float]


class PredictedOutcome(BaseModel):
    patient_id: str
    patient_name: str
    predicted_weight_loss_30d: float
    success_probability: float
    recommended_interventions: List[str]
    risk_of_discontinuation: float


class PredictiveAnalytics(BaseModel):
    at_risk_predictions: List[PredictedOutcome]
    success_predictions: List[PredictedOutcome]
    overall_success_rate_prediction: float
    intervention_impact_analysis: Dict[str, float]


class OptimizationInsight(BaseModel):
    category: str  # "workflow", "engagement", "clinical", "resource"
    title: str
    description: str
    potential_impact: str
    priority: str  # "high", "medium", "low"
    affected_patients: int
    estimated_time_saving: Optional[str] = None


class OptimizationInsights(BaseModel):
    insights: List[OptimizationInsight]
    total_time_savings_per_week: float
    efficiency_score: float
    top_bottlenecks: List[Dict[str, Any]]


class PopulationHealthDashboard(BaseModel):
    overview: PopulationOverview
    risk_stratification: RiskStratification
    treatment_analytics: TreatmentAnalytics
    predictive_analytics: PredictiveAnalytics
    optimization_insights: OptimizationInsights
    last_updated: datetime