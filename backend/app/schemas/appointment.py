# backend/app/schemas/appointment.py
import re
import uuid
from datetime import datetime
from typing import Optional

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from app.core.constants.appointment import (
    ALLOWED_APPOINTMENT_DURATIONS,
    ALLOWED_APPOINTMENT_STATUSES,
    ALLOWED_APPOINTMENT_TYPES,
    CLERK_ID_REGEX,
)
from app.schemas.clinician import ClinicianBasicInfo
from app.schemas.patient import PatientBasicInfo

# --- Base Schemas ---


class AppointmentBase(BaseModel):
    """
    Base schema for appointment data.
    """

    appointment_datetime: datetime = Field(
        ..., description="Date and time of the appointment (must be timezone-aware)"
    )
    duration_minutes: int = Field(
        ...,
        description=f"Duration in minutes. Allowed values: {ALLOWED_APPOINTMENT_DURATIONS}",
    )
    appointment_type: str = Field(
        ...,
        description=f"Type of appointment. Allowed values: {ALLOWED_APPOINTMENT_TYPES}",
    )
    reason: Optional[str] = Field(None, description="Reason for the appointment")
    patient_notes: Optional[str] = Field(None, description="Notes from the patient")

    @field_validator("appointment_datetime")
    @classmethod
    def check_datetime_timezone(cls, value: datetime):
        """Ensure datetime is timezone-aware."""
        if value.tzinfo is None:
            raise ValueError("appointment_datetime must be timezone-aware")
        return value

    @field_validator("duration_minutes")
    @classmethod
    def check_duration(cls, value: int):
        """Validate duration is in allowed list."""
        if value not in ALLOWED_APPOINTMENT_DURATIONS:
            raise ValueError(
                f"Duration must be one of: {ALLOWED_APPOINTMENT_DURATIONS}"
            )
        return value

    @field_validator("appointment_type")
    @classmethod
    def check_appointment_type(cls, value: str):
        """Validate appointment type is in allowed list."""
        if value not in ALLOWED_APPOINTMENT_TYPES:
            raise ValueError(
                f"Appointment type must be one of: {ALLOWED_APPOINTMENT_TYPES}"
            )
        return value


# --- Create Schema ---


class AppointmentCreate(AppointmentBase):
    """
    Schema for creating a new appointment. Requires patient and clinician Clerk IDs.
    """

    patient_id: str = Field(..., description="Clerk User ID of the patient")
    clinician_id: str = Field(..., description="Clerk User ID of the clinician")
    status: str = Field(
        ..., description=f"Initial status. Allowed: {ALLOWED_APPOINTMENT_STATUSES}"
    )

    @field_validator("patient_id", "clinician_id")
    @classmethod
    def check_clerk_id_format(cls, value: str):
        # Restore original logic, but use a slightly more permissive regex
        # Checks for 'user_' prefix and at least one alphanumeric char after it
        clerk_id_pattern = r"^user_[a-zA-Z0-9]+$"
        if not re.match(clerk_id_pattern, value):
            raise ValueError("Invalid clerk ID format")
        return value

    @field_validator("status")
    @classmethod
    def check_initial_status(cls, value: str):
        if value not in ALLOWED_APPOINTMENT_STATUSES:
            raise ValueError(f"Status must be one of: {ALLOWED_APPOINTMENT_STATUSES}")
        if value != "scheduled":
            raise ValueError("Initial status must be 'scheduled'")
        return value

    # Add model validator if needed to check patient/clinician existence or availability


# --- Update Schema ---


class AppointmentUpdate(BaseModel):
    """
    Schema for updating an existing appointment. All fields are optional.
    """

    appointment_datetime: Optional[datetime] = Field(
        None, description="New date and time (must be timezone-aware)"
    )
    duration_minutes: Optional[int] = Field(
        None, description=f"New duration. Allowed: {ALLOWED_APPOINTMENT_DURATIONS}"
    )
    reason: Optional[str] = Field(
        None, description="Updated reason for the appointment"
    )
    patient_notes: Optional[str] = Field(
        None, description="Updated notes from the patient"
    )

    @field_validator("appointment_datetime")
    @classmethod
    def check_update_datetime_timezone(cls, value: Optional[datetime]):
        if value:
            if value.tzinfo is None:
                raise ValueError("appointment_datetime must be timezone-aware")
        return value

    @field_validator("duration_minutes")
    @classmethod
    def check_update_duration(cls, value: Optional[int]):
        if value:
            if value not in ALLOWED_APPOINTMENT_DURATIONS:
                raise ValueError(
                    f"Duration must be one of: {ALLOWED_APPOINTMENT_DURATIONS}"
                )
        return value

    # Add model validator if needed to check conflicts or availability for updates


# --- Status Update Schema ---


class AppointmentStatusUpdate(BaseModel):
    """
    Schema specifically for updating the status of an appointment.
    """

    status: str = Field(
        ..., description=f"New status. Allowed: {ALLOWED_APPOINTMENT_STATUSES}"
    )
    cancelled_by_id: Optional[str] = Field(
        None, description="Clerk User ID of the user cancelling (if applicable)"
    )
    cancellation_reason: Optional[str] = Field(
        None, description="Reason for cancellation (required if status is 'cancelled')"
    )

    @field_validator("status")
    @classmethod
    def check_status(cls, value: str):
        if value not in ALLOWED_APPOINTMENT_STATUSES:
            raise ValueError(f"Status must be one of: {ALLOWED_APPOINTMENT_STATUSES}")
        return value

    @field_validator("cancelled_by_id")
    @classmethod
    def check_cancelled_by_id_format(cls, value: Optional[str]):
        if value:
            if not re.match(CLERK_ID_REGEX, value):
                raise ValueError("Invalid clerk ID format")
        return value

    @model_validator(mode="after")
    def check_cancellation_fields(self) -> "AppointmentStatusUpdate":
        if self.status == "cancelled":
            if not self.cancelled_by_id:
                raise ValueError(
                    "cancelled_by_id is required when status is 'cancelled'"
                )
            if not self.cancellation_reason:
                raise ValueError(
                    "cancellation_reason is required when status is 'cancelled'"
                )
        elif self.cancelled_by_id or self.cancellation_reason:
            raise ValueError(
                "Cancellation fields can only be set when status is 'cancelled'"
            )
        return self


# --- Response Schemas ---


class AppointmentResponse(AppointmentBase):
    """
    Schema for returning appointment details, including related patient/clinician info.
    """

    id: uuid.UUID = Field(..., description="Internal UUID of the appointment")
    status: str = Field(..., description="Current status of the appointment")
    created_at: datetime = Field(
        ..., description="Timestamp when the appointment was created"
    )
    updated_at: datetime = Field(
        ..., description="Timestamp when the appointment was last updated"
    )
    patient_id: str = Field(..., description="Clerk User ID of the patient")
    clinician_id: str = Field(..., description="Clerk User ID of the clinician")
    patient: PatientBasicInfo = Field(
        ..., description="Basic details of the associated patient"
    )
    clinician: ClinicianBasicInfo = Field(
        ..., description="Basic details of the associated clinician"
    )
    cancelled_at: Optional[datetime] = Field(
        None, description="Timestamp when the appointment was cancelled"
    )
    cancelled_by_id: Optional[str] = Field(
        None, description="Clerk User ID of the user who cancelled"
    )
    cancellation_reason: Optional[str] = Field(
        None, description="Reason for cancellation"
    )

    model_config = ConfigDict(from_attributes=True)


class PaginatedAppointmentResponse(BaseModel):
    """
    Response model for a paginated list of appointments.
    """

    items: list[AppointmentResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = ConfigDict(from_attributes=True)


AppointmentList = PaginatedAppointmentResponse
