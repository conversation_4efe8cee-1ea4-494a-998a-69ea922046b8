import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from app.schemas.clinician import ClinicianBasicInfo
from app.schemas.patient import PatientBasicInfo

# --- Base Schemas ---


class AppointmentRequestBase(BaseModel):
    """
    Base schema for appointment request data.
    """

    preferred_datetime: datetime = Field(
        ...,
        description="Preferred date and time for the appointment (must be timezone-aware)",
    )
    reason: str = Field(..., description="Reason for requesting the appointment")
    clinician_preference: Optional[str] = Field(
        None, description="Optional preferred clinician for the appointment"
    )


# --- Create Schema ---


class AppointmentRequestCreate(AppointmentRequestBase):
    """
    Schema for creating a new appointment request.
    """

    patient_id: str = Field(..., description="Clerk User ID of the patient")


# --- Update Schema ---


class AppointmentRequestUpdate(BaseModel):
    """
    Schema for updating an existing appointment request. All fields are optional.
    """

    preferred_datetime: Optional[datetime] = Field(
        None, description="New preferred date and time"
    )
    reason: Optional[str] = Field(
        None, description="Updated reason for the appointment"
    )
    clinician_preference: Optional[str] = Field(
        None, description="Updated preferred clinician"
    )


# --- Status Update Schema ---


class AppointmentRequestStatusUpdate(BaseModel):
    """
    Schema for updating the status of an appointment request.
    """

    status: str = Field(
        ..., description="New status. Allowed: pending, approved, rejected, cancelled"
    )
    reviewed_by_id: Optional[str] = Field(
        None, description="Clerk User ID of the clinician who reviewed the request"
    )
    review_notes: Optional[str] = Field(
        None, description="Optional notes from the reviewer"
    )


# --- Response Schemas ---


class AppointmentRequestResponse(AppointmentRequestBase):
    """
    Schema for returning appointment request details, including related patient/clinician info.
    """

    id: uuid.UUID = Field(..., description="Internal UUID of the appointment request")
    status: str = Field(..., description="Current status of the appointment request")
    created_at: datetime = Field(
        ..., description="Timestamp when the appointment request was created"
    )
    updated_at: datetime = Field(
        ..., description="Timestamp when the appointment request was last updated"
    )
    patient_id: str = Field(..., description="Clerk User ID of the patient")
    patient: PatientBasicInfo = Field(
        ..., description="Basic details of the associated patient"
    )
    reviewed_by_id: Optional[str] = Field(
        None, description="Clerk User ID of the clinician who reviewed the request"
    )
    reviewed_by: Optional[ClinicianBasicInfo] = Field(
        None, description="Basic details of the reviewing clinician"
    )
    reviewed_at: Optional[datetime] = Field(
        None, description="Timestamp when the appointment request was reviewed"
    )
    review_notes: Optional[str] = Field(None, description="Notes from the reviewer")
    preferred_clinician: Optional[ClinicianBasicInfo] = Field(
        None, description="Basic details of the preferred clinician, if specified"
    )  # New field

    model_config = ConfigDict(from_attributes=True)


class PaginatedAppointmentRequestResponse(BaseModel):
    """
    Response model for a paginated list of appointment requests.
    """

    items: list[AppointmentRequestResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = ConfigDict(from_attributes=True)
