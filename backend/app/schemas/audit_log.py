from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict


# Shared properties
class AuditLogBase(BaseModel):
    actor_user_id: Optional[str] = None  # Store as string for flexibility, can be UUID
    actor_role: Optional[str] = None
    action: str
    outcome: str  # e.g., 'SUCCESS', 'FAILURE', 'ATTEMPT'
    target_resource_type: Optional[str] = None
    target_resource_id: Optional[str] = (
        None  # Store as string for flexibility, can be UUID
    )
    details: Optional[dict[str, Any]] = None
    source_ip: Optional[str] = None


# Properties to receive via API on creation (although direct creation via API is unlikely)
# Primarily used internally by the log_audit_event utility which builds the model directly.
# This schema is mainly for the CRUD function's type hinting.
class AuditLogCreate(AuditLogBase):
    # timestamp is set automatically in the model/utility
    pass


# Properties to receive via API on update
class AuditLogUpdate(BaseModel):
    # Audit logs are generally immutable, but providing this class for CRUDBase compatibility
    # No fields are actually updatable
    pass


# Properties shared by models stored in DB
class AuditLogInDBBase(AuditLogBase):
    id: UUID
    timestamp: datetime
    model_config = ConfigDict(from_attributes=True)  # Enable ORM mode


# Properties to return to client
class AuditLog(AuditLogInDBBase):
    pass


# Properties stored in DB
class AuditLogInDB(AuditLogInDBBase):
    pass


# Properties to return as summary to client
class AuditLogSummary(BaseModel):
    id: UUID
    timestamp: datetime
    action: str
    outcome: str
