import uuid
from datetime import datetime
from typing import Optional, Union  # Added List, Union

from pydantic import BaseModel, ConfigDict, EmailStr, Field  # Added EmailStr


class TokenPayload(BaseModel):
    """Schema for the JWT token payload."""

    sub: Optional[str] = None
    exp: Optional[int] = None
    iat: Optional[int] = None  # Issued At timestamp
    role: Optional[Union[str, list[str]]] = "patient"  # Allow string or list of strings
    email: Optional[EmailStr] = None  # Use EmailStr for validation
    public_metadata: Optional[dict] = None  # Added to hold Clerk public metadata


class MagicCodeRequest(BaseModel):
    """
    Request model for validating a magic access code to trigger an invitation.
    """

    code: str = Field(
        ..., min_length=1, description="The magic access code provided to the patient."
    )


# TokenResponse might not be needed for magic code validation anymore
class TokenResponse(BaseModel):
    """
    Response model containing the JWT access token.
    """

    accessToken: str = Field(
        ..., description="The JWT access token for the authenticated session."
    )
    tokenType: str = Field("bearer", description="The type of token.")


class MagicCodeResponse(BaseModel):
    """
    Response model after successfully validating a magic code and triggering an invitation.
    """

    message: str = Field(
        ..., description="Confirmation message indicating the invitation was sent."
    )


class AccessCodeResponse(BaseModel):
    """
    Response model for returning a generated access code.
    """

    access_code: str = Field(
        ...,
        description="The newly generated, unique access code for patient onboarding.",
    )


# class ErrorResponse(BaseModel):
#     detail: str

# --- AccessCode Schemas ---


# Shared properties
class AccessCodeBase(BaseModel):
    code: str
    email: EmailStr  # Added email field
    expires_at: datetime
    is_used: bool = False
    patient_id: Optional[str] = None  # Changed from uuid.UUID to str (Clerk ID)
    clinician_id: Optional[str] = None  # Changed from uuid.UUID to str (Clerk ID)


# Properties to receive via API on creation
class AccessCodeCreate(AccessCodeBase):
    # Inherits code, email, expires_at, is_used, patient_id, clinician_id
    # Ensure all required fields for creation are covered by AccessCodeBase
    pass


# Properties to receive via API on update
class AccessCodeUpdate(BaseModel):
    expires_at: Optional[datetime] = None
    is_used: Optional[bool] = None
    patient_id: Optional[str] = None  # Changed from uuid.UUID to str (Clerk ID)
    clinician_id: Optional[str] = None  # Changed from uuid.UUID to str (Clerk ID)
    # Email is generally not updatable via this schema


# Properties shared by models stored in DB
class AccessCodeInDBBase(AccessCodeBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client (includes email via inheritance)
class AccessCode(AccessCodeInDBBase):
    pass


# Properties stored in DB
class AccessCodeInDB(AccessCodeInDBBase):
    pass
