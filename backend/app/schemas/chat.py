import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field, field_validator

from app.models.chat_message import MessageSenderType

# --- Base Chat Schemas ---


class ChatMessageBase(BaseModel):
    """Base schema for chat messages."""

    message: str = Field(
        ..., min_length=1, max_length=2000, description="The message content."
    )


class MessageRouteEnum(str, Enum):
    """Available message routing options."""

    patient = "patient"
    ai = "ai"
    clinician = "clinician"


class ChatMessageRequest(ChatMessageBase):
    """Schema for sending a chat message."""

    context_id: Optional[str] = Field(
        None, description="Optional conversation context ID for continuing discussions."
    )
    structured_output: Optional[bool] = Field(
        False, description="Whether to request structured output from the model."
    )
    output_schema: Optional[str] = Field(
        None, description="The name of the schema to use for structured output."
    )
    message_route: Optional[MessageRouteEnum] = Field(
        None, description="Who the message should be routed to (patient or AI only)."
    )
    context: Optional[dict[str, Any]] = Field(
        None, description="Additional context data for message processing."
    )


class ChatMessageResponse(BaseModel):
    """Schema for chat message response."""

    message: str = Field(..., description="The response message.")
    message_id: str = Field(..., description="Unique ID for this message.")
    timestamp: str = Field(..., description="ISO format timestamp.")
    structured_data: Optional[dict[str, Any]] = Field(
        None, description="Structured data if requested and available."
    )
    metadata: Optional[dict[str, Any]] = Field(
        None, description="Additional metadata about the response."
    )

    model_config = ConfigDict(from_attributes=True)


# --- Chat History Schemas ---


class ChatHistoryItem(BaseModel):
    """Schema for a single message in chat history."""

    message_id: str
    sender_type: str
    message: str
    timestamp: str
    is_read: bool = True
    metadata: Optional[dict[str, Any]] = None
    # Note: message_route needs special handling between DB enums and Pydantic enums
    # It might come as a string from the DB and needs conversion
    message_route: Optional[str] = None

    @field_validator("message_route")
    def validate_message_route(cls, v):
        """
        Normalize message_route to a string representation.
        This handles cases where the value might come from different enum types or as raw strings.
        """
        if v is None:
            return None

        # If it's already a string, ensure it's lowercase
        if isinstance(v, str):
            return v.lower()

        # If it's an enum with a value attribute (like MessageRouteEnum)
        if hasattr(v, "value"):
            return v.value.lower()

        # For any other case, convert to string and lowercase
        return str(v).lower()

    model_config = ConfigDict(from_attributes=True)


class ChatHistoryResponse(BaseModel):
    """Schema for retrieving chat history."""

    messages: list[ChatHistoryItem]
    total_count: int
    has_more: bool

    model_config = ConfigDict(from_attributes=True)


# --- Clinician Chat Schemas ---


class ClinicianConversationItem(BaseModel):
    """Schema for a single conversation item in a clinician's list."""

    patient_id: str
    patient_first_name: str
    patient_last_name: str
    last_message_at: Optional[datetime] = None
    unread_count: int = 0  # Count of messages from this patient unread by the clinician

    model_config = ConfigDict(from_attributes=True)


class ClinicianConversationsResponse(BaseModel):
    """Schema for listing conversations for a clinician."""

    conversations: list[ClinicianConversationItem]
    total_count: int  # Total number of patients with conversations
    has_more: bool

    model_config = ConfigDict(from_attributes=True)


# --- Advanced Chat Configuration ---


class ChatModelEnum(str, Enum):
    """Available chat models."""

    GPT_3_5_TURBO = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "o4-mini"


class ChatTemperatureEnum(float, Enum):
    """Predefined temperature settings with descriptive names."""

    PRECISE = 0.1
    BALANCED = 0.7
    CREATIVE = 1.0


class ChatConfigUpdate(BaseModel):
    """Schema for updating chat configuration."""

    model: Optional[ChatModelEnum] = None
    temperature: Optional[Union[ChatTemperatureEnum, float]] = None
    max_tokens: Optional[int] = Field(
        None, ge=50, le=4000, description="Maximum tokens to generate in response."
    )
    system_prompt_template: Optional[str] = None

    @field_validator("temperature")
    def validate_temperature(cls, v):
        """Ensure temperature is within valid range."""
        if isinstance(v, float) and not 0.0 <= v <= 1.0:
            raise ValueError("Temperature must be between 0.0 and 1.0")
        return v


class ChatConfigResponse(BaseModel):
    """Schema for retrieving chat configuration."""

    model: ChatModelEnum
    temperature: float
    max_tokens: Optional[int] = None
    system_prompt_template: Optional[str] = None
    available_models: list[ChatModelEnum]
    supports_structured_output: bool = True

    model_config = ConfigDict(from_attributes=True)


# --- Structured Output Schemas ---


class StructuredOutputSchema(BaseModel):
    """Base class for structured output schemas."""

    model_config = ConfigDict(extra="forbid")


class MedicationReminderSchema(StructuredOutputSchema):
    """Schema for medication reminder structured output."""

    medication_name: Optional[str] = None
    dosage: Optional[str] = None
    frequency: Optional[str] = None
    importance: str = Field(..., description="Why taking this medication is important")
    next_steps: list[str] = Field(..., description="Actionable steps for the patient")


class SideEffectReportSchema(StructuredOutputSchema):
    """Schema for side effect report structured output."""

    reported_symptoms: list[str] = Field(
        ..., description="Symptoms reported by patient"
    )
    severity_level: str = Field(..., description="Estimated severity (low/medium/high)")
    recommendation: str = Field(..., description="Recommendation for the patient")
    requires_immediate_attention: bool = Field(
        ..., description="Whether this requires urgent clinician attention"
    )


class AvailableStructuredSchemas(BaseModel):
    """Schema for listing available structured output schemas."""

    schemas: list[dict[str, Any]]

    model_config = ConfigDict(from_attributes=True)


# --- Internal schemas for DB operations ---


class ChatMessageCreateInternal(BaseModel):
    """Internal schema for creating chat message records."""

    patient_id: str
    sender_type: Literal["patient", "clinician", "agent", "clinical_note", "PATIENT", "CLINICIAN", "AGENT", "CLINICAL_NOTE"]
    message_content: str
    message_metadata: Optional[dict[str, Any]] = None  # Changed to match DB column name
    message_route: Optional[Literal["patient", "ai", "clinician"]] = None

    @field_validator("sender_type")
    def validate_sender_type(cls, v):
        """Convert sender type to match database enum values."""
        if v is None:
            return v
        if isinstance(v, MessageSenderType):
            return v.value  # Use the enum value as-is (mixed case)
        if isinstance(v, str):
            # Map string values to correct database enum values (all uppercase)
            mapping = {
                "patient": "PATIENT",
                "agent": "AGENT", 
                "clinician": "CLINICIAN",
                "clinical_note": "CLINICAL_NOTE"
            }
            # If already uppercase, return as-is
            if v in ["PATIENT", "AGENT", "CLINICIAN", "CLINICAL_NOTE"]:
                return v
            return mapping.get(v.lower(), v)
        return str(v)  # Convert any other type to string

    @field_validator("message_route")
    def validate_message_route(cls, v):
        """Convert message route to lowercase string for database compatibility."""
        if v is None:
            return v
        if isinstance(v, MessageRouteEnum):
            return v.value.lower()
        if isinstance(v, str):
            return v.lower()
        return str(v).lower()  # Convert any other type to lowercase string

    model_config = ConfigDict(from_attributes=True)


class ChatMessageUpdate(BaseModel):
    """Schema for updating chat message records."""

    is_read_by_clinician: Optional[bool] = None
    read_by_clinician_at: Optional[datetime] = None
    metadata: Optional[dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class ChatMessageRead(BaseModel):
    """Schema for reading chat message records."""

    id: uuid.UUID
    patient_id: str
    sender_type: MessageSenderType
    message_content: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_read_by_clinician: bool
    read_by_clinician_at: Optional[datetime] = None
    metadata: Optional[dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)
