import uuid
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


# Shared properties
class ClinicBase(BaseModel):
    name: Optional[str] = None
    address: Optional[str] = None
    website_url: Optional[HttpUrl] = None
    scraped_data: Optional[dict[str, Any]] = None


# Properties to receive on item creation
class ClinicCreate(
    BaseModel
):  # Inherit directly from BaseModel to specify required fields
    name: str = Field(..., description="Name of the clinic")
    address: Optional[str] = Field(None, description="Physical address of the clinic")
    website_url: Optional[HttpUrl] = Field(
        None, description="Website URL of the clinic"
    )
    # scraped_data is not expected during creation


# Properties to receive on item update
class ClinicUpdate(ClinicBase):
    pass  # No additional fields for now


# Properties shared by models stored in DB
class ClinicInDBBase(ClinicBase):
    id: uuid.UUID
    name: str
    address: Optional[str] = None
    website_url: Optional[HttpUrl] = None
    scraped_data: Optional[dict[str, Any]] = None
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class ClinicRead(ClinicInDBBase):
    last_scraped_at: Optional[datetime] = None
    pass


# Properties stored in DB
class ClinicInDB(ClinicInDBBase):
    pass


# Request body for ingestion
class ClinicIngestionRequest(BaseModel):
    clinic_url: HttpUrl = Field(..., description="Primary URL of the clinic website")
    supplemental_context: dict[str, Any] = Field(
        ...,
        description="Supplemental context data such as pricing, guidelines, care plans",
    )


# Request body for triggering scraping
class ClinicScrapeRequest(BaseModel):
    url: HttpUrl = Field(..., description="URL of the clinic website to scrape")
    clinic_id: uuid.UUID = Field(..., description="ID of the clinic being scraped")


# Schema for paginated response
class PaginatedClinicResponse(BaseModel):
    items: list[ClinicRead]
    total: int
