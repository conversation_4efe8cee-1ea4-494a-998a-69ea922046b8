from uuid import UUID

from pydantic import BaseModel

# ===============================================================================
# Clinic Medication Association Schemas
# ===============================================================================


# Shared properties
class ClinicMedicationAssociationBase(BaseModel):
    """Base schema containing common fields for clinic-medication association."""

    clinic_id: UUID
    medication_id: UUID


# Properties to receive via API on creation
class ClinicMedicationAssociationRequest(BaseModel):
    """
    Schema for the request body when associating a medication with a clinic.
    'clinic_id' comes from the URL path.
    """

    medication_id: UUID
    notes: str | None = None


# Properties to return via API after creation
class ClinicMedicationAssociationResponse(ClinicMedicationAssociationBase):
    """Schema for the response after successfully associating a medication."""

    message: str

    class Config:
        from_attributes = True  # Pydantic V2 setting


# Properties required for database creation (used internally by CRUD)
class ClinicMedicationAssociationCreate(ClinicMedicationAssociationBase):
    """
    Schema used internally for creating the association record in the database.
    Combines 'clinic_id' from the path and 'medication_id' from the request body.
    """

    notes: str | None = None  # Add notes field


# Properties required for database update (placeholder, not used in this task)
class ClinicMedicationAssociationUpdate(BaseModel):
    """
    Schema used internally for updating an association record.
    Currently empty as associations are typically created/deleted, not updated.
    """

    pass  # Add fields here if association updates become necessary


# Properties stored in DB (can be used for reading)
class ClinicMedicationAssociationInDB(ClinicMedicationAssociationBase):
    """Schema representing the association as stored in the database."""

    # Add any other DB-specific fields if necessary, e.g., created_at
    class Config:
        from_attributes = True
