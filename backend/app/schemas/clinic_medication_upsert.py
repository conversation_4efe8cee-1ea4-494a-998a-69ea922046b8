from typing import Optional

from pydantic import BaseModel, Field


class ClinicMedicationUpsertRequest(BaseModel):
    name: str = Field(..., description="Name of the medication")
    description: Optional[str] = Field(
        None, description="Description of the medication"
    )
    dosage_guidelines: Optional[str] = Field(
        None, description="General dosage guidelines"
    )
    common_side_effects: Optional[str] = Field(None, description="Common side effects")
    category: str = Field(..., description="Category of the medication")
    clinic_specific_notes: Optional[str] = Field(
        None, description="Clinic-specific notes"
    )
    id: Optional[str] = Field(None, description="Medication ID for updates (optional)")
