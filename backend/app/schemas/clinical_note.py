"""Pydantic schemas for Clinical Notes."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, validator

from app.models.clinical_note import ClinicalNoteStatus


class SectionContent(BaseModel):
    """Schema for individual SOAP sections."""
    title: str = Field(..., description="Section title (e.g., 'Subjective', 'Objective')")
    content: str = Field(..., description="Section content")
    subsections: Optional[Dict[str, str]] = Field(None, description="Optional subsections")


class BillingCode(BaseModel):
    """Schema for billing code suggestions."""
    code: str = Field(..., description="ICD-10 or CPT code")
    description: str = Field(..., description="Code description")
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")


class ClinicalNoteBase(BaseModel):
    """Base schema for clinical notes."""
    patient_id: str = Field(..., description="Patient ID (Clerk ID)")
    appointment_id: Optional[UUID] = Field(None, description="Associated appointment ID")
    chat_session_id: Optional[UUID] = Field(None, description="Chat session ID for grouping messages")
    template_id: Optional[UUID] = Field(None, description="Template ID if using a template")
    note_type: str = Field(..., description="Note type (e.g., 'SOAP', 'K-SOAP', 'Progress')")
    sections: Dict[str, Any] = Field(..., description="Structured note sections")
    raw_text: Optional[str] = Field(None, description="Full text version of the note")
    billing_notes: Optional[str] = Field(None, description="Additional billing notes")


class ClinicalNoteCreate(ClinicalNoteBase):
    """Schema for creating a clinical note."""
    ai_confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    suggested_icd10_codes: Optional[List[BillingCode]] = None
    suggested_cpt_codes: Optional[List[BillingCode]] = None


class ClinicalNoteUpdate(BaseModel):
    """Schema for updating a clinical note."""
    sections: Optional[Dict[str, Any]] = None
    raw_text: Optional[str] = None
    status: Optional[ClinicalNoteStatus] = None
    billing_notes: Optional[str] = None
    human_edits: Optional[Dict[str, Any]] = None
    suggested_icd10_codes: Optional[List[BillingCode]] = None
    suggested_cpt_codes: Optional[List[BillingCode]] = None


class ClinicalNoteApprove(BaseModel):
    """Schema for approving a clinical note."""
    approved_by: str = Field(..., description="Clinician ID who approved")


class ClinicalNoteInDBBase(ClinicalNoteBase):
    """Base schema for clinical notes in database."""
    id: UUID
    clinician_id: str
    status: ClinicalNoteStatus
    ai_generated: bool
    ai_confidence_score: Optional[float]
    human_edits: Optional[Dict[str, Any]]
    suggested_icd10_codes: Optional[List[Dict[str, Any]]]
    suggested_cpt_codes: Optional[List[Dict[str, Any]]]
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime]
    approved_by: Optional[str]

    class Config:
        from_attributes = True


class ClinicalNote(ClinicalNoteInDBBase):
    """Schema for clinical note response."""
    pass


class ClinicalNoteWithRelations(ClinicalNote):
    """Schema for clinical note with related data."""
    patient_name: Optional[str] = None
    clinician_name: Optional[str] = None
    approver_name: Optional[str] = None
    appointment_datetime: Optional[datetime] = None
    template_name: Optional[str] = None


class ClinicalNoteList(BaseModel):
    """Schema for paginated clinical notes list."""
    items: List[Union[ClinicalNote, ClinicalNoteWithRelations]]
    total: int
    skip: int
    limit: int


class GenerateClinicalNoteRequest(BaseModel):
    """Request schema for generating a clinical note from chat."""
    patient_id: str = Field(..., description="Patient ID")
    chat_session_id: Optional[UUID] = Field(None, description="Chat session to extract from")
    appointment_id: Optional[UUID] = Field(None, description="Associated appointment")
    template_id: Optional[UUID] = Field(None, description="Template to use")
    note_type: str = Field("SOAP", description="Type of note to generate")
    start_time: Optional[datetime] = Field(None, description="Start of conversation window")
    end_time: Optional[datetime] = Field(None, description="End of conversation window")
    include_system_messages: bool = Field(False, description="Include AI system messages")


class GenerateClinicalNoteResponse(BaseModel):
    """Response schema for generated clinical note."""
    note: ClinicalNote
    confidence_score: float
    messages_processed: int
    generation_time_ms: int
    suggested_edits: Optional[List[str]] = None