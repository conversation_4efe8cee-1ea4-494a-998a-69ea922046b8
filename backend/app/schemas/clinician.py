# backend/app/schemas/clinician.py
import uuid
from typing import Optional, List

from pydantic import BaseModel

from app.schemas.clinic import ClinicRead  # Added import


# Shared properties
class ClinicianBase(BaseModel):
    """
    Base schema for clinicians with shared attributes.
    """

    email: str
    first_name: str
    last_name: str
    specialty: Optional[str] = None
    bio: Optional[str] = None  # Professional bio/description
    credentials: Optional[List[str]] = None  # Array of credentials (MD, DO, etc.)
    years_experience: Optional[int] = None  # Years of professional experience
    is_active: bool = True
    clerk_id: Optional[str] = None  # Allow setting during creation/update
    photo_url: Optional[str] = None  # URL to profile photo in blob storage


# Properties to receive via API on creation
class ClinicianCreate(ClinicianBase):
    """
    Schema for creating a new clinician.
    """

    email: str  # Required on creation
    clerk_id: str  # Required on creation
    id: str  # Add ID field to hold Clerk User ID on creation


# Properties to receive via API on update
class ClinicianUpdate(ClinicianBase):
    """
    Schema for updating an existing clinician.
    All fields are optional to allow partial updates.
    """

    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    specialty: Optional[str] = None
    bio: Optional[str] = None
    credentials: Optional[List[str]] = None
    years_experience: Optional[int] = None
    is_active: Optional[bool] = None
    clerk_id: Optional[str] = None
    photo_url: Optional[str] = None
    assigned_clinic_id: Optional[uuid.UUID] = (
        None  # Added for updating clinic association
    )


# Properties shared by models stored in DB
class ClinicianInDBBase(ClinicianBase):
    """
    Base schema for clinician data from the database.
    """

    id: str  # Changed from uuid.UUID to str to match DB model using Clerk ID as PK
    email: str  # Not optional in DB model
    is_active: bool  # Not optional in DB model
    # Add created_at, updated_at if needed from BaseWithTimestamps, depending on response needs
    # created_at: datetime
    # updated_at: datetime

    class Config:
        from_attributes = True


# Properties to return to client (might exclude sensitive info)
class Clinician(ClinicianInDBBase):
    """
    Schema for returning a clinician.
    """

    # Keep assigned_clinic for potential direct use, though clinics list is preferred
    assigned_clinic: Optional[ClinicRead] = None
    clinics: Optional[list[ClinicRead]] = None  # Eagerly loaded list of clinics
    pass


# Properties stored in DB
class ClinicianInDB(ClinicianInDBBase):
    """
    Schema for clinician data stored in the database.
    """

    pass


# Alias for API response to keep naming consistent with other endpoints
ClinicianResponse = Clinician


# New schema for the list response
class ClinicianListResponse(BaseModel):
    items: list[Clinician]  # Use the updated Clinician schema
    total: int


class ClinicianBasicInfo(BaseModel):
    """
    Basic clinician information for embedding in other responses.
    """

    id: str  # Clerk ID
    first_name: str
    last_name: str
    email: str
    specialty: Optional[str] = None
    bio: Optional[str] = None
    credentials: Optional[List[str]] = None
    years_experience: Optional[int] = None

    class Config:
        from_attributes = True
