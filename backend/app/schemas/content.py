import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

# Import the Enum from the model file
from app.models.content import ContentType


# Shared properties
class ContentBase(BaseModel):
    title: str
    content_type: ContentType
    body: Optional[str] = None
    url: Optional[str] = None
    is_published: bool = False


# Properties to receive via API on creation
class ContentCreate(ContentBase):
    pass  # All fields from ContentBase are required by default


# Properties to receive via API on update
class ContentUpdate(BaseModel):
    title: Optional[str] = None
    content_type: Optional[ContentType] = None
    body: Optional[str] = None
    url: Optional[str] = None
    is_published: Optional[bool] = None


# Properties shared by models stored in DB
class ContentInDBBase(ContentBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class Content(ContentInDBBase):
    pass


# Properties stored in DB
class ContentInDB(ContentInDBBase):
    pass


class PaginatedContentResponse(BaseModel):
    items: list[Content]
    total: int
    page: int
    size: int
    pages: int
    model_config = ConfigDict(from_attributes=True)
