import uuid
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field


# Shared properties
class ContentChunkBase(BaseModel):
    scraped_page_id: Optional[uuid.UUID] = None
    chunk_text: Optional[str] = None
    metadata_: Optional[dict[str, Any]] = Field(None, alias="metadata")

    model_config = ConfigDict(
        populate_by_name=True,  # Allow using 'metadata' in input data
    )


# Properties to receive on creation (embedding is required here)
class ContentChunkCreate(ContentChunkBase):
    scraped_page_id: Optional[uuid.UUID] = None  # Optional for education materials
    chunk_text: str
    embedding: list[float]  # Expecting the embedding vector
    # metadata is optional


# Properties to receive on update (e.g., re-embedding)
class ContentChunkUpdate(ContentChunkBase):
    embedding: Optional[list[float]] = None  # Allow updating the embedding
    chunk_text: Optional[str] = None  # Allow updating text if needed
    metadata_: Optional[dict[str, Any]] = Field(
        None, alias="metadata"
    )  # Allow updating metadata


# Properties shared by models stored in DB
class ContentChunkInDBBase(ContentChunkBase):
    id: uuid.UUID
    embedding: Optional[list[float]] = None  # Embedding might be null initially
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(
        from_attributes=True,  # Enable ORM mode
        populate_by_name=True,
    )


# Properties to return to client (usually without embedding)
class ContentChunkRead(ContentChunkInDBBase):
    # Exclude embedding by default for API responses unless specifically needed
    embedding: Optional[list[float]] = Field(None, exclude=True)


# Properties stored in DB
class ContentChunkInDB(ContentChunkInDBBase):
    pass
