from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


class DashboardTaskSummaryResponse(BaseModel):
    """
    Response model for the dashboard task summary counts.
    """

    pending_medication_requests: int
    pending_lab_results: int
    unread_patient_messages: int
    pending_appointment_requests: int  # Added field

    model_config = ConfigDict(from_attributes=True)


class AISummaryResponse(BaseModel):
    """
    Response model for the AI summary insights.
    """

    summary_text: str

    model_config = ConfigDict(from_attributes=True)


class PrioritizedCard(BaseModel):
    """
    Represents a dashboard card with its priority and reasoning.
    """

    card_type: Literal[
        "quick_actions",
        "todays_appointments",
        "patient_alerts",
        "pending_tasks",
        "side_effect_reports",
        "recent_activity",
    ]
    priority: int = Field(ge=1, le=10, description="Priority score from 1 (highest) to 10 (lowest)")
    reason: Optional[str] = Field(None, description="AI-generated reason for this priority")
    highlighted_data: Optional[Dict[str, Any]] = Field(
        None, description="Specific data to highlight in this card"
    )


class InsightItem(BaseModel):
    """
    Represents an AI-generated insight or recommendation.
    """

    type: Literal["recommendation", "alert", "trend", "summary"]
    message: str
    severity: Literal["info", "warning", "critical"]
    related_card: Optional[str] = Field(None, description="Related dashboard card type")


class AIPrioritizedDashboardResponse(BaseModel):
    """
    Response model for AI-powered dashboard prioritization.
    """

    summary: str = Field(description="AI-generated summary of current priorities")
    prioritized_cards: List[PrioritizedCard] = Field(
        description="Dashboard cards ordered by AI-determined priority"
    )
    insights: List[InsightItem] = Field(
        description="AI-generated insights and recommendations"
    )
    task_counts: DashboardTaskSummaryResponse = Field(
        description="Current task count summary"
    )
    generated_at: str = Field(description="ISO timestamp when prioritization was generated")

    model_config = ConfigDict(from_attributes=True)
