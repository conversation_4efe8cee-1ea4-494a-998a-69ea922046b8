"""Demo management schemas."""

from typing import Dict, Any, Optional
from pydantic import BaseModel


class StandardResponse(BaseModel):
    """Standard response format for demo operations."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class DemoAccountInfo(BaseModel):
    """Information about a demo account."""
    exists: bool
    name: Optional[str] = None


class DataCounts(BaseModel):
    """Counts of various data types in the demo environment."""
    patients: int
    appointments: int
    chat_messages: int
    weight_logs: int
    side_effects: int
    clinical_notes: int


class DemoStatusResponse(BaseModel):
    """Response for demo environment status."""
    is_production: bool
    demo_accounts_exist: bool
    data_counts: DataCounts
    demo_accounts: Dict[str, DemoAccountInfo]
    last_reset: Optional[str] = None  # ISO timestamp of last reset