from typing import Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, field_serializer
from enum import Enum
import uuid


class MaterialType(str, Enum):
    PDF = "PDF"
    VIDEO = "VIDEO"
    LINK = "LINK"
    DOCUMENT = "DOCUMENT"


# Base schema with common fields
class EducationMaterialBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Material title")
    description: Optional[str] = Field(None, description="Material description")
    type: MaterialType = Field(..., description="Type of educational material")
    category: Optional[str] = Field(None, max_length=100, description="Material category")
    content_url: Optional[str] = Field(None, description="URL for links or uploaded files")
    duration_minutes: Optional[int] = Field(None, ge=0, description="Duration in minutes for videos/content")
    is_public: bool = Field(False, description="Whether material is public or clinic-specific")


# Schema for creating education materials
class EducationMaterialCreate(EducationMaterialBase):
    clinic_id: Optional[Union[str, uuid.UUID]] = Field(None, description="Clinic ID for clinic-specific materials")


# Schema for updating education materials
class EducationMaterialUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Material title")
    description: Optional[str] = Field(None, description="Material description")
    type: Optional[MaterialType] = Field(None, description="Type of educational material")
    category: Optional[str] = Field(None, max_length=100, description="Material category")
    content_url: Optional[str] = Field(None, description="URL for links or uploaded files")
    duration_minutes: Optional[int] = Field(None, ge=0, description="Duration in minutes for videos/content")
    is_public: Optional[bool] = Field(None, description="Whether material is public or clinic-specific")
    clinic_id: Optional[Union[str, uuid.UUID]] = Field(None, description="Clinic ID for clinic-specific materials")


# Schema for education material responses
class EducationMaterial(EducationMaterialBase):
    id: Union[str, uuid.UUID] = Field(..., description="Unique identifier")
    file_path: Optional[str] = Field(None, description="Server file path for uploads")
    clinic_id: Optional[Union[str, uuid.UUID]] = Field(None, description="Clinic ID for clinic-specific materials")
    created_by: str = Field(..., description="ID of clinician who created the material")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    views_count: int = Field(0, ge=0, description="Number of views")
    assignments_count: int = Field(0, ge=0, description="Number of assignments")

    model_config = ConfigDict(
        from_attributes=True
    )
    
    @field_serializer('id', 'clinic_id', 'created_by')
    def serialize_uuid_fields(self, value):
        if isinstance(value, uuid.UUID):
            return str(value)
        return value


# Schema for analytics summary
class EducationMaterialsStats(BaseModel):
    total_materials: int = Field(..., ge=0, description="Total number of materials")
    public_materials: int = Field(..., ge=0, description="Number of public materials")
    clinic_materials: int = Field(..., ge=0, description="Number of clinic-specific materials")
    total_assignments: int = Field(..., ge=0, description="Total number of assignments made")
    most_assigned_material: Optional[EducationMaterial] = Field(None, description="Most frequently assigned material")


# Schema for file upload response
class FileUploadResponse(BaseModel):
    material_id: str = Field(..., description="ID of the created education material")
    file_url: str = Field(..., description="Public URL to access the file")
    file_path: str = Field(..., description="Path to the uploaded file")
    message: str = Field(..., description="Success message")
    
    @field_serializer('material_id')
    def serialize_material_id(self, value):
        if isinstance(value, uuid.UUID):
            return str(value)
        return value


# Schema for listing materials with optional filters
class EducationMaterialFilters(BaseModel):
    search: Optional[str] = Field(None, description="Search term for title/description")
    category: Optional[str] = Field(None, description="Filter by category")
    type: Optional[MaterialType] = Field(None, description="Filter by material type")
    is_public: Optional[bool] = Field(None, description="Filter by public/private status")
    clinic_id: Optional[Union[str, uuid.UUID]] = Field(None, description="Filter by clinic ID")
    created_by: Optional[str] = Field(None, description="Filter by creator")
    limit: Optional[int] = Field(20, ge=1, le=100, description="Number of results to return")
    offset: Optional[int] = Field(0, ge=0, description="Number of results to skip")


# Aliases for backward compatibility with existing imports
EducationMaterialSummary = EducationMaterialsStats
EducationMaterialSearchFilters = EducationMaterialFilters