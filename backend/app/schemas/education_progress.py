import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, validator


# Base Schema for common fields
class EducationProgressBase(BaseModel):
    patient_id: str = Field(..., description="Patient ID tracking progress")
    material_id: uuid.UUID = Field(..., description="Educational material being tracked")
    assignment_id: uuid.UUID = Field(..., description="Assignment this progress relates to")
    progress_percentage: float = Field(default=0.0, ge=0.0, le=100.0, description="Progress percentage 0-100")
    time_spent_minutes: int = Field(default=0, ge=0, description="Total time spent on material")


# Schema for creating progress record
class EducationProgressCreate(EducationProgressBase):
    last_accessed: Optional[datetime] = Field(default_factory=datetime.utcnow, description="Last access time")


# Schema for updating progress
class EducationProgressUpdate(BaseModel):
    progress_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    time_spent_minutes: Optional[int] = Field(None, ge=0)
    last_accessed: Optional[datetime] = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    patient_feedback: Optional[str] = Field(None, max_length=1000)
    patient_rating: Optional[int] = Field(None, ge=1, le=5, description="1-5 star rating")

    @validator('completed_at', always=True)
    def validate_completion(cls, v, values):
        """Set completed_at automatically when progress reaches 100%"""
        progress = values.get('progress_percentage')
        if progress == 100.0 and not v:
            return datetime.utcnow()
        elif progress and progress < 100.0 and v:
            # Clear completion if progress goes below 100%
            return None
        return v

    model_config = ConfigDict(extra="forbid")


# Schema representing progress in the DB (used for reading)
class EducationProgressInDBBase(EducationProgressBase):
    id: uuid.UUID
    last_accessed: Optional[datetime]
    completed_at: Optional[datetime]
    patient_feedback: Optional[str]
    patient_rating: Optional[int]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema for returning progress data
class EducationProgress(EducationProgressInDBBase):
    pass


# Schema for progress with material details
class EducationProgressWithMaterial(EducationProgressInDBBase):
    material: Optional["EducationMaterialSummary"] = None
    assignment: Optional["PatientEducationAssignmentSummary"] = None

    model_config = ConfigDict(from_attributes=True)


# Schema for progress summary for analytics
class EducationProgressSummary(BaseModel):
    material_id: uuid.UUID
    material_title: str
    material_type: str
    progress_percentage: float
    time_spent_minutes: int
    last_accessed: Optional[datetime]
    completed_at: Optional[datetime]
    assignment_priority: str
    assigned_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Import here to avoid circular imports
from .education_material import EducationMaterialSummary
from .patient_education_assignment import PatientEducationAssignmentSummary
EducationProgressWithMaterial.model_rebuild()