from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict


# Shared properties
class EventLogBase(BaseModel):
    actor_user_id: str
    actor_role: str
    action: str
    target_resource_type: Optional[str] = None
    target_resource_id: Optional[str] = None
    outcome: str = "SUCCESS"
    details: Optional[dict[str, Any]] = None
    source_ip: Optional[str] = None

    # LLM-specific fields
    original_input: Optional[str] = None
    input_type: str = "text"
    extracted_intent: Optional[str] = None
    extracted_parameters: Optional[dict[str, Any]] = None
    executed_api_action: Optional[str] = None
    clinic_id: Optional[UUID] = None


# Properties to receive via API on creation
# Primarily used internally by the log_event utility which builds the model directly
class EventLogCreate(EventLogBase):
    pass


# Properties to receive via API on update
class EventLogUpdate(BaseModel):
    # Event logs are generally immutable, providing this class for CRUDBase compatibility
    outcome: Optional[str] = None
    details: Optional[dict[str, Any]] = None
    executed_api_action: Optional[str] = None


# Properties shared by models stored in DB
class EventLogInDBBase(EventLogBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class EventLog(EventLogInDBBase):
    pass


# Properties stored in DB
class EventLogInDB(EventLogInDBBase):
    pass


# Properties to return as summary to client
class EventLogSummary(BaseModel):
    id: UUID
    created_at: datetime
    actor_role: str
    action: str
    outcome: str
    extracted_intent: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)
