from datetime import datetime, timezone
from typing import Any, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

# Import SeverityLevel from side_effect_report schema
from app.schemas.side_effect_report import SeverityLevel


class BaseEvent(BaseModel):
    """Base model for all internal events."""

    event_id: UUID = Field(default_factory=uuid4)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    event_name: str  # Should be overridden by subclasses


class WeightLoggedEvent(BaseEvent):
    """Event triggered when a weight log entry is successfully created."""

    event_name: str = "WeightLogged"
    user_id: str
    weight_log_id: str  # ID of the created weight log entry
    weight_kg: float


class SideEffectReportedEvent(BaseEvent):
    """Event triggered when a side effect report is successfully submitted."""

    event_name: str = "SideEffectReported"
    user_id: str
    report_id: str  # ID of the created side effect report
    severity: SeverityLevel


class PatientRegisteredEvent(BaseEvent):
    """Event triggered when a new patient successfully registers (e.g., validates magic code)."""

    event_name: str = "PatientRegistered"
    user_id: str  # The ID assigned to the new patient


# Add more specific event models as needed following this pattern.


class ChatMessageSentEvent(BaseEvent):
    """Event triggered when a chat message (user or agent) is successfully saved."""

    event_name: str = "ChatMessageSent"
    user_id: str  # Patient ID
    message_id: str  # ID of the saved chat message
    sender_type: str  # "PATIENT" or "AGENT"
    clinic_id: str  # Clinic context
    context: Optional[dict[str, Any]] = None
    message: Optional[str] = None
    db: Optional[Any] = None
