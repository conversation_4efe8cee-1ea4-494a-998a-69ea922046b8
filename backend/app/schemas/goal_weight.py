from datetime import date
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class GoalWeightUpdate(BaseModel):
    """
    Schema for updating goal weight information.
    """
    goal_weight_kg: Optional[float] = Field(None, gt=0, lt=1000, description="Target weight in kilograms")
    goal_weight_date: Optional[date] = Field(None, description="Target date to achieve goal weight")

    model_config = ConfigDict(extra="forbid")


class GoalWeightResponse(BaseModel):
    """
    Schema for goal weight information response.
    """
    goal_weight_kg: Optional[float] = Field(None, description="Target weight in kg")
    goal_weight_date: Optional[date] = Field(None, description="Target date to achieve goal weight")
    current_weight_kg: Optional[float] = Field(None, description="Most recent weight log entry")
    progress_to_goal_kg: Optional[float] = Field(
        None, description="Progress towards goal weight (negative = still need to lose, positive = exceeded goal)"
    )
    progress_percentage: Optional[float] = Field(
        None, description="Progress percentage towards goal weight (0-100%+)"
    )
    days_to_goal: Optional[int] = Field(None, description="Days remaining to reach goal date")
    is_goal_achieved: bool = Field(False, description="Whether the goal weight has been reached")

    model_config = ConfigDict(from_attributes=True)


class GoalWeightProgressSummary(BaseModel):
    """
    Schema for goal weight progress summary for dashboard use.
    """
    has_goal: bool = Field(False, description="Whether patient has set a goal weight")
    goal_weight_kg: Optional[float] = Field(None, description="Target weight in kg")
    current_weight_kg: Optional[float] = Field(None, description="Most recent weight")
    progress_percentage: Optional[float] = Field(None, description="Progress percentage (0-100%+)")
    is_goal_achieved: bool = Field(False, description="Whether goal has been reached")
    trend: Optional[str] = Field(None, description="Weight trend direction: 'losing', 'gaining', 'stable'")

    model_config = ConfigDict(from_attributes=True)