# backend/app/schemas/invitation.py

from clerk_backend_api import models as clerk_models  # Import Clerk models
from pydantic import BaseModel, EmailStr, Field  # Added Field


class PatientInvitationRequest(BaseModel):
    """Schema for a clinician requesting a patient invitation."""

    email: EmailStr


class PatientInvitationResponse(BaseModel):
    """Schema for the response after creating a patient invitation."""

    invitation_id: str
    email: EmailStr
    status: str  # e.g., 'pending'


# Schema for listing Clerk invitations (used by clinician listing their sent invites)
class PaginatedClerkInvitationListResponse(BaseModel):
    invitations: list[clerk_models.Invitation] = Field(
        ..., description="List of Clerk invitation objects."
    )
    total_count: int = Field(
        ..., description="Total number of invitations matching the criteria."
    )

    class Config:
        arbitrary_types_allowed = True  # Allow clerk_models.Invitation type
        # Using model_config would be preferred in Pydantic V2, but keeping Config for consistency
