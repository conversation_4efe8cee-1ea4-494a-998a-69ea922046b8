from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


# --- Enums ---
class LabResultStatus(str, Enum):
    PENDING_REVIEW = "pending_review"
    REVIEWED = "reviewed"
    ACTION_REQUIRED = "action_required"
    # Add other statuses as needed


# --- Base Schema ---
class LabResultBase(BaseModel):
    patient_id: str = Field(
        ..., description="ID of the patient associated with the lab result"
    )
    clinician_id: str = Field(
        ..., description="ID of the clinician responsible for reviewing the lab result"
    )
    test_name: str = Field(..., description="Name of the lab test performed")
    result_value: str = Field(..., description="Value or finding of the lab result")
    reference_range: Optional[str] = Field(
        None, description="Reference range for the test, if applicable"
    )
    status: LabResultStatus = Field(
        default=LabResultStatus.PENDING_REVIEW,
        description="Current status of the lab result review",
    )
    report_url: Optional[HttpUrl] = Field(
        None, description="Optional URL to the full lab report PDF or document"
    )
    collected_at: datetime = Field(
        ..., description="Date and time when the lab sample was collected"
    )


# --- Schema for Creation ---
class LabResultCreate(LabResultBase):
    # Status defaults to PENDING_REVIEW
    pass


# --- Schema for Update ---
# Primarily for updating status or potentially adding notes/report URL later
class LabResultUpdate(BaseModel):
    status: Optional[LabResultStatus] = None
    report_url: Optional[HttpUrl] = None
    # reviewed_at and reviewed_by_clinician_id are typically set by specific actions


# --- Schema for Reading/Response ---
class LabResultRead(LabResultBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    reviewed_at: Optional[datetime] = None
    reviewed_by_clinician_id: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
