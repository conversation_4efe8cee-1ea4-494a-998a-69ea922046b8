from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class MedicationBase(BaseModel):
    """Base schema for Medication."""

    name: str = Field(..., description="Name of the medication")
    description: Optional[str] = Field(
        None, description="Description of the medication"
    )
    dosage_guidelines: Optional[str] = Field(
        None, description="General dosage guidelines"
    )
    common_side_effects: Optional[str] = Field(None, description="Common side effects")


class MedicationCreate(MedicationBase):
    """Schema for creating a new medication."""

    pass


class MedicationUpdate(MedicationBase):
    """Schema for updating an existing medication."""

    name: Optional[str] = Field(None, description="Name of the medication")


class MedicationResponse(MedicationBase):
    """Schema for medication response."""

    id: UUID = Field(..., description="Unique identifier for the medication")
    associated_clinic_ids: list[UUID] = Field(
        default_factory=list,
        alias="associatedClinicIds",
        description="List of associated clinic IDs",
    )

    class Config:
        from_attributes = True
        populate_by_name = (
            True  # Updated from allow_population_by_field_name for Pydantic v2
        )
