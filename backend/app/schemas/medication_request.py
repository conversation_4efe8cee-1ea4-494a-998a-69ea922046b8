import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class MedicationRequestCreate(BaseModel):
    """
    Schema for creating a new medication refill request.
    """

    medication_name: str = Field(
        ...,
        description="Name of the medication being requested",
    )
    dosage: Optional[str] = Field(
        None,
        description="Dosage information (e.g., '10mg', '1 tablet')",
    )
    frequency: Optional[str] = Field(
        None,
        description="Frequency of administration (e.g., 'Once daily', 'Twice daily')",
    )
    duration: Optional[str] = Field(
        None,
        description="Duration of treatment (e.g., '7 days', '2 weeks')",
    )
    notes: Optional[str] = Field(
        None,
        description="Additional notes or instructions",
    )
    patient_id: Optional[str] = Field(
        None,
        description="ID of the patient for whom the request is being created. Required when a clinician creates a request.",
    )

    @field_validator("medication_name")
    @classmethod
    def check_medication_name_not_empty(cls, value: str) -> str:
        if not value or not value.strip():
            raise ValueError("Medication name cannot be empty.")
        return value.strip()


class MedicationRequestUpdate(BaseModel):
    """
    Schema for updating a medication request, primarily the status.
    """

    status: Optional[str] = Field(
        None, description="New status for the request (e.g., Approved, Rejected)"
    )
    notes: Optional[str] = Field(
        None,
        description="Optional notes from the clinician regarding the status change.",
    )


class PatientInfo(BaseModel):
    """
    Schema for embedded patient information in medication request responses.
    """

    id: str = Field(..., description="ID of the patient")
    first_name: str = Field(..., description="First name of the patient")
    last_name: str = Field(..., description="Last name of the patient")

    model_config = ConfigDict(
        from_attributes=True,
    )


class MedicationRequestResponse(BaseModel):
    """
    Schema for the response after successfully creating a medication request.
    """

    id: uuid.UUID = Field(..., description="Unique ID of the medication request")
    patient_id: str = Field(
        ..., description="ID of the patient who made the request (Clerk user ID)"
    )
    medication_name: str = Field(..., description="Name of the requested medication")
    dosage: Optional[str] = Field(None, description="Dosage information")
    frequency: Optional[str] = Field(None, description="Frequency of administration")
    duration: Optional[str] = Field(None, description="Duration of treatment")
    notes: Optional[str] = Field(None, description="Additional notes or instructions")
    status: str = Field(
        ...,
        description="Current status of the request (e.g., Pending, Approved, Denied)",
    )
    requested_at: datetime = Field(
        ..., alias="created_at", description="Timestamp when the request was submitted"
    )
    resolved_at: Optional[datetime] = Field(
        None, description="Timestamp when the request was resolved"
    )
    patient: Optional[PatientInfo] = Field(
        None, description="Patient information including name"
    )

    # Fixed: Single model_config definition with all needed settings
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,  # Allow populating fields by attribute name or alias
    )


class PaginatedMedicationRequestResponse(BaseModel):
    """
    Schema for returning a paginated list of medication requests.
    """

    items: list[MedicationRequestResponse]
    total: int
    page: int
    size: int
    pages: int
