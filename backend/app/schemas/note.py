from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# --- Enums ---
class NoteType(str, Enum):
    GENERAL = "general"
    CONSULTATION = "consultation"
    FOLLOW_UP = "follow_up"
    # Add other types as needed


# --- Base Schema ---
class NoteBase(BaseModel):
    patient_id: str = Field(..., description="ID of the patient the note is about")
    title: Optional[str] = Field(None, description="Title of the note")
    content: str = Field(..., description="The content of the note")
    note_type: Optional[NoteType] = Field(
        default=NoteType.GENERAL, description="Type of the note"
    )


# --- Schema for Creation ---
# clinician_id (author) will be passed separately to the CRUD function
class NoteCreate(NoteBase):
    pass


# --- Schema for Update ---
# Typically only content might be updatable, maybe type. Author/Patient shouldn't change.
class NoteUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    note_type: Optional[NoteType] = None


# --- Nested schemas for relationships ---
class PatientBasic(BaseModel):
    id: str
    first_name: str
    last_name: str
    
    model_config = ConfigDict(from_attributes=True)


class ClinicianBasic(BaseModel):
    id: str
    first_name: str
    last_name: str
    
    model_config = ConfigDict(from_attributes=True)


# --- Schema for Reading/Response ---
class NoteRead(NoteBase):
    id: UUID
    clinician_id: str  # Author ID
    created_at: datetime
    updated_at: datetime
    patient: Optional[PatientBasic] = None
    clinician: Optional[ClinicianBasic] = None

    model_config = ConfigDict(from_attributes=True)
