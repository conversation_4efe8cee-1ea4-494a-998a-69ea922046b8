from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# --- Enums ---
class NotificationType(str, Enum):
    NEW_SIDE_EFFECT = "new_side_effect"
    NEW_MED_REQUEST = "new_med_request"
    APPOINTMENT_REMINDER = "appointment_reminder"
    NEW_CHAT_MESSAGE = "new_chat_message"  # If notifying about chat
    LAB_RESULT_READY = "lab_result_ready"
    # Add other types as needed


# --- Base Schema ---
class NotificationBase(BaseModel):
    recipient_clinician_id: str = Field(
        ..., description="ID of the clinician receiving the notification"
    )
    notification_type: NotificationType = Field(
        ..., description="Type of the notification"
    )
    message: str = Field(..., description="Content of the notification message")
    related_entity_id: Optional[str] = Field(
        None,
        description="Optional ID of the related entity (e.g., SideEffectReport, MedicationRequest)",
    )


# --- Schema for Creation ---
# is_read and read_at are typically handled by the system, not provided on creation
class NotificationCreate(NotificationBase):
    pass


# --- Schema for Update ---
# Primarily used internally to mark as read
class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None
    # read_at is set internally when is_read becomes True


# --- Schema for Reading/Response ---
class NotificationRead(NotificationBase):
    id: UUID
    is_read: bool
    read_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime  # Useful to know when it was last touched (e.g., marked read)


# --- Schema for Mark All Read Response ---
class MarkAllReadResponse(BaseModel):
    updated_count: int = Field(
        ..., description="Number of notifications marked as read"
    )

    model_config = ConfigDict(from_attributes=True)
