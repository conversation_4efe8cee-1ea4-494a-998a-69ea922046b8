import uuid  # Import uuid
from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, PositiveInt


# Base Schema for common fields
class PatientBase(BaseModel):
    email: EmailStr = Field(..., json_schema_extra={"index": True})
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[date] = None
    phone_number: Optional[str] = Field(
        None, pattern=r"^\+?[1-9]\d{1,14}$", json_schema_extra={"index": True}
    )
    height_cm: Optional[PositiveInt] = Field(None, gt=0, lt=300)
    is_active: bool = True
    photo_url: Optional[str] = None  # Added for profile photo URL
    goal_weight_kg: Optional[float] = Field(None, gt=0, lt=1000)  # Goal weight in kg
    goal_weight_date: Optional[date] = None  # Target date to achieve goal weight


# Schema for creating a patient
class PatientCreate(PatientBase):
    # Inherits all fields from PatientBase
    id: str  # Added to store Clerk user ID as primary key
    invited_by_clinician_id: Optional[str] = None  # Clerk ID of inviting clinician
    associated_clinic_id: Optional[uuid.UUID] = None  # Clinic associated via invitation


# Schema for updating a patient (all fields optional)
class PatientUpdate(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[date] = None
    phone_number: Optional[str] = Field(None, pattern=r"^\+?[1-9]\d{1,14}$")
    height_cm: Optional[PositiveInt] = Field(None, gt=0, lt=300)
    is_active: Optional[bool] = None
    photo_url: Optional[str] = None  # Added for profile photo URL
    goal_weight_kg: Optional[float] = Field(None, gt=0, lt=1000)  # Goal weight in kg
    goal_weight_date: Optional[date] = None  # Target date to achieve goal weight

    model_config = ConfigDict(extra="forbid")


# Schema representing a patient record in the DB (used for reading)
class PatientInDBBase(PatientBase):
    id: str  # Changed from uuid.UUID to str to match the model
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)  # Enable ORM mode


# Public response schema (can inherit from PatientInDBBase and exclude fields if needed)
class Patient(PatientInDBBase):
    pass  # For now, expose all fields from PatientInDBBase


# --- Specific Response Schemas (Adjusted) ---


class PatientProfileResponse(BaseModel):
    """
    Response model for retrieving patient profile information.
    """

    id: str  # Changed from uuid.UUID to str to match the model
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[date] = None
    phone_number: Optional[str] = None
    height_cm: Optional[PositiveInt] = None
    is_active: bool
    photo_url: Optional[str] = None  # Added for profile photo URL
    goal_weight_kg: Optional[float] = None  # Goal weight in kg
    goal_weight_date: Optional[date] = None  # Target date to achieve goal weight

    model_config = ConfigDict(from_attributes=True)


class PatientListItemResponse(BaseModel):
    """
    Response model for listing patients (minimal details).
    """

    id: str  # Changed from uuid.UUID to str to match the model
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: bool

    model_config = ConfigDict(from_attributes=True)


class PatientBasicInfo(BaseModel):
    """
    Basic patient information for embedding in other responses.
    """

    id: str  # Clerk ID
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: EmailStr

    model_config = ConfigDict(from_attributes=True)


# --- Paginated Response Schema ---


class PaginatedPatientListResponse(BaseModel):
    """
    Response model for a paginated list of patients.
    Matches the structure expected by PatientListPage.tsx.
    """

    items: list[PatientListItemResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = ConfigDict(from_attributes=True)
