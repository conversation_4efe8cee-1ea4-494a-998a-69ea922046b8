from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# --- Enums ---
class PatientAlertType(str, Enum):
    SIDE_EFFECT_REPORTED = "side_effect_reported"
    MEDICATION_REQUEST = "medication_request"
    WEIGHT_THRESHOLD = "weight_threshold"
    MISSED_LOG = "missed_log"
    AI_DETECTED_PATTERN = "ai_detected_pattern"
    # Add other types as needed


class PatientAlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PatientAlertStatus(str, Enum):
    NEW = "new"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    DISMISSED = "dismissed"  # Optional: If alerts can be dismissed without resolution


# --- Base Schema ---
class PatientAlertBase(BaseModel):
    patient_id: str = Field(
        ..., description="ID of the patient associated with the alert"
    )
    alert_type: PatientAlertType = Field(..., description="Type of the alert")
    severity: PatientAlertSeverity = Field(
        ..., description="Severity level of the alert"
    )
    description: str = Field(..., description="Detailed description of the alert")
    status: PatientAlertStatus = Field(
        default=PatientAlertStatus.NEW, description="Current status of the alert"
    )


# --- Schema for Creation ---
class PatientAlertCreate(PatientAlertBase):
    # Creation typically sets status to NEW automatically via default
    pass


# --- Schema for Update ---
# Primarily for updating status or potentially description/severity if allowed
class PatientAlertUpdate(BaseModel):
    status: Optional[PatientAlertStatus] = None
    description: Optional[str] = None
    severity: Optional[PatientAlertSeverity] = None
    # resolved_at and resolved_by_clinician_id are typically set by specific actions, not direct update


# --- Schema for patient info in alerts ---
class PatientBrief(BaseModel):
    id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: str
    
    model_config = ConfigDict(from_attributes=True)


# --- Schema for Reading/Response ---
class PatientAlertRead(PatientAlertBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    resolved_by_clinician_id: Optional[str] = None
    patient: Optional[PatientBrief] = None

    model_config = ConfigDict(from_attributes=True)
