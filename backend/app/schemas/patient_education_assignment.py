import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from app.models.patient_education_assignment import AssignmentPriority, AssignmentStatus


# Base Schema for common fields
class PatientEducationAssignmentBase(BaseModel):
    patient_id: str = Field(..., description="Patient ID receiving the assignment")
    material_id: uuid.UUID = Field(..., description="Educational material being assigned")
    due_date: Optional[datetime] = Field(None, description="Optional due date for completion")
    priority: AssignmentPriority = Field(default=AssignmentPriority.MEDIUM, description="Assignment priority")
    clinician_notes: Optional[str] = Field(None, max_length=1000, description="Notes from clinician")


# Schema for creating assignment
class PatientEducationAssignmentCreate(PatientEducationAssignmentBase):
    assigned_by: str = Field(..., description="Clinician ID who made the assignment")
    assigned_at: datetime = Field(default_factory=datetime.utcnow, description="Assignment timestamp")


# Schema for updating assignment
class PatientEducationAssignmentUpdate(BaseModel):
    due_date: Optional[datetime] = None
    priority: Optional[AssignmentPriority] = None
    clinician_notes: Optional[str] = Field(None, max_length=1000)
    status: Optional[AssignmentStatus] = None

    model_config = ConfigDict(extra="forbid")


# Schema representing assignment in the DB (used for reading)
class PatientEducationAssignmentInDBBase(PatientEducationAssignmentBase):
    id: uuid.UUID
    assigned_by: str
    assigned_at: datetime
    status: AssignmentStatus
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema for returning assignment data
class PatientEducationAssignment(PatientEducationAssignmentInDBBase):
    pass


# Schema for assignment with material details
class PatientEducationAssignmentWithMaterial(PatientEducationAssignmentInDBBase):
    material: Optional["EducationMaterial"] = None

    model_config = ConfigDict(from_attributes=True)


# Schema for assignment summary for lists
class PatientEducationAssignmentSummary(BaseModel):
    id: uuid.UUID
    material_id: uuid.UUID
    priority: AssignmentPriority
    status: AssignmentStatus
    assigned_at: datetime
    due_date: Optional[datetime]
    material_title: Optional[str] = None  # Joined from material
    material_type: Optional[str] = None   # Joined from material

    model_config = ConfigDict(from_attributes=True)


# Import here to avoid circular imports
from .education_material import EducationMaterial
PatientEducationAssignmentWithMaterial.model_rebuild()