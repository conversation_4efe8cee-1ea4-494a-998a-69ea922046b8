import uuid
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


# Shared properties
class ScrapedPageBase(BaseModel):
    source_url: Optional[HttpUrl] = None
    clinic_id: Optional[uuid.UUID] = None
    cleaned_content: Optional[str] = None
    metadata_: Optional[dict[str, Any]] = Field(
        None, alias="metadata"
    )  # Use alias for reserved keyword

    model_config = ConfigDict(
        populate_by_name=True,  # Allow using 'metadata' in input data
    )


# Properties to receive via API on creation
class ScrapedPageCreate(ScrapedPageBase):
    source_url: HttpUrl
    clinic_id: uuid.UUID
    # cleaned_content is optional at creation, might be added later
    # metadata is optional


# Properties to receive via API on update
class ScrapedPageUpdate(ScrapedPageBase):
    # Allow updating content or metadata
    pass


# Properties shared by models stored in DB
class ScrapedPageInDBBase(ScrapedPageBase):
    id: uuid.UUID
    scraped_at: datetime
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(
        from_attributes=True,  # Enable ORM mode
        populate_by_name=True,
    )


# Properties to return to client
class ScrapedPageRead(ScrapedPageInDBBase):
    pass


# Properties stored in DB
class ScrapedPageInDB(ScrapedPageInDBBase):
    pass
