from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class UniversalSearchResult(BaseModel):
    """
    Response model for universal search results.
    """

    entity_type: str = Field(
        ..., description="Type of the entity found (e.g., 'Patient')"
    )
    entity_id: UUID = Field(..., description="UUID of the entity found")
    display_name: str = Field(
        ..., description="Primary display name for the entity (e.g., Patient name)"
    )
    details: Optional[str] = Field(
        None, description="Additional identifying details (e.g., Patient DOB)"
    )

    model_config = ConfigDict(from_attributes=True)
