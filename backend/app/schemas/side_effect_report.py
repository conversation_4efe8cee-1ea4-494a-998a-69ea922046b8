import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class SeverityLevel(str, Enum):
    """
    Enumeration for side effect severity levels.
    """

    MINOR = "minor"
    MODERATE = "moderate"
    MAJOR = "major"

    @classmethod
    def _missing_(cls, value):
        if isinstance(value, str):
            for member in cls:
                if member.value.lower() == value.lower():
                    return member
        return None


class SideEffectStatus(str, Enum):
    """
    Enumeration for side effect report status.
    """

    SUBMITTED = "Submitted"
    REVIEWED = "Reviewed"
    RESOLVED = "Resolved"

    def _generate_next_value_(name, start, count, last_values):
        # Use the capitalized string value
        return name.capitalize()


class SideEffectReportCreate(BaseModel):
    """
    Schema for submitting a new side effect report.
    """

    description: str = Field(
        ..., description="Detailed description of the side effect experienced."
    )
    severity: SeverityLevel = Field(
        ..., description="Severity of the side effect (minor, moderate, or major)."
    )
    clinician_id: Optional[str] = Field(
        None,
        description="Clerk ID of the clinician who submitted the report (if applicable).",
    )
    # Removed patient_id field as it's handled by the endpoint logic
    # patient_id: str = Field(..., description="The Clerk User ID of the patient reporting the side effect.")

    @field_validator("description")
    @classmethod
    def check_description_not_empty(cls, value: str) -> str:
        if not value or not value.strip():
            raise ValueError("Side effect description cannot be empty.")
        return value.strip()


class SideEffectReportUpdate(BaseModel):
    """
    Schema for updating a side effect report.
    Patients can update description and severity.
    Clinicians might update status (handled separately if needed).
    """

    description: Optional[str] = Field(
        None, description="Updated description of the side effect."
    )
    severity: Optional[SeverityLevel] = Field(
        None, description="Updated severity of the side effect."
    )
    status: Optional[str] = Field(
        None, description="New status for the report (e.g., Reviewed, Resolved)"
    )
    clinician_id: Optional[str] = Field(
        None,
        description="Clerk ID of the clinician updating the report (if applicable).",
    )

    # Add validator for description if needed, similar to Create schema
    @field_validator("description")
    @classmethod
    def check_description_not_empty(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ValueError("Side effect description cannot be empty if provided.")
        return value.strip() if value else None


class SideEffectReportResponse(BaseModel):
    """
    Schema for the response after successfully submitting a side effect report.
    """

    id: uuid.UUID = Field(..., description="Unique ID of the side effect report")
    patient_id: str = Field(
        ..., description="ID of the patient who submitted the report"
    )
    clinician_id: Optional[str] = Field(
        None,
        description="Clerk ID of the clinician who submitted/reviewed the report (if applicable)",
    )
    description: str = Field(..., description="Description of the side effect")
    severity: SeverityLevel = Field(..., description="Reported severity")
    status: str = Field(
        ..., description="Current status of the report (e.g., Submitted, Reviewed)"
    )
    reported_at: datetime = Field(
        ..., description="Timestamp when the report was submitted"
    )

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,  # Allow populating fields by attribute name or alias
    )


# Schema for basic patient info needed in the side effect response
class PatientBasicInfo(BaseModel):
    id: str = Field(..., description="Unique ID of the patient (Clerk User ID)")
    first_name: str = Field(..., description="Patient's first name")
    last_name: str = Field(..., description="Patient's last name")

    model_config = ConfigDict(from_attributes=True)


# --- Paginated Response Schema ---
class PaginatedSideEffectReportResponse(BaseModel):
    """
    Response model for a paginated list of side effect reports.
    """

    items: list[SideEffectReportResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = ConfigDict(from_attributes=True)


# New schema including nested patient details for specific endpoints
class SideEffectReportResponseWithPatientDetails(SideEffectReportResponse):
    patient: PatientBasicInfo = Field(
        ..., description="Details of the patient who submitted the report"
    )
    clinician_id: Optional[str] = Field(
        None,
        description="Clerk ID of the clinician who submitted/reviewed the report (if applicable)",
    )

    model_config = ConfigDict(from_attributes=True)


# Need to import List for the paginated response
# from typing import List # Removed from here


class PaginatedSideEffectResponse(BaseModel):
    """Response model for paginated side effect reports."""

    items: list[SideEffectReportResponse]
    total: int


# New schema for severity summary
class SideEffectSeveritySummary(BaseModel):
    minor: int = Field(0, description="Count of minor side effect reports")
    moderate: int = Field(0, description="Count of moderate side effect reports")
    major: int = Field(0, description="Count of major side effect reports")


class SideEffectReportEventData(BaseModel):
    pass
