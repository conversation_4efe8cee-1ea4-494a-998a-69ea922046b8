from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# Shared Template properties
class TemplateBase(BaseModel):
    name: str
    description: Optional[str] = None
    version: str = "1.0"
    is_active: bool = True
    system_prompt: str
    default_settings: dict[str, Any] = Field(default_factory=dict)
    actions: list[dict[str, Any]] = Field(default_factory=list)
    clinic_id: Optional[UUID] = None


# Properties to receive via API on creation
class TemplateCreate(TemplateBase):
    pass


# Properties to receive via API on update
class TemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    is_active: Optional[bool] = None
    system_prompt: Optional[str] = None
    default_settings: Optional[dict[str, Any]] = None
    actions: Optional[list[dict[str, Any]]] = None
    clinic_id: Optional[UUID] = None


# Properties shared by models stored in DB
class TemplateInDBBase(TemplateBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class Template(TemplateInDBBase):
    pass


# Properties stored in DB
class TemplateInDB(TemplateInDBBase):
    pass


# Shared TemplateRole properties
class TemplateRoleBase(BaseModel):
    template_id: UUID
    role: str


# Properties to receive via API on creation
class TemplateRoleCreate(TemplateRoleBase):
    pass


# Properties to receive via API on update
class TemplateRoleUpdate(BaseModel):
    role: Optional[str] = None


# Properties shared by models stored in DB
class TemplateRoleInDBBase(TemplateRoleBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class TemplateRole(TemplateRoleInDBBase):
    pass


# Properties stored in DB
class TemplateRoleInDB(TemplateRoleInDBBase):
    pass


# Properties for retrieving templates with their roles
class TemplateWithRoles(Template):
    roles: list[str] = Field(default_factory=list)
