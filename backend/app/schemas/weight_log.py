import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


# --- Base Schema ---
# Useful for sharing common fields between Create and Update
class WeightLogBase(BaseModel):
    weight_kg: Optional[float] = Field(
        None, gt=0, description="Weight in kilograms (must be positive)"
    )


# --- Create Schema ---
# Inherits from Base, makes required fields non-optional
class WeightLogCreate(WeightLogBase):
    weight_kg: float = Field(
        ..., gt=0, description="Weight in kilograms (must be positive)"
    )
    log_date: datetime = Field(..., description="Date when the weight was measured")


# --- Update Schema ---
# Inherits from Base, all fields remain optional for partial updates
class WeightLogUpdate(WeightLogBase):
    log_date: Optional[datetime] = Field(
        None, description="Date when the weight was measured"
    )


# --- Database Model Schema (for reading from DB) ---
class WeightLogInDBBase(WeightLogBase):
    id: uuid.UUID
    patient_id: str  # Changed from UUID to str to support Clerk user IDs
    logged_at: datetime = Field(
        ..., alias="created_at", description="Timestamp when the weight was logged"
    )
    log_date: datetime  # Added to include the log_date field from the model

    model_config = ConfigDict(
        from_attributes=True,  # Allows creating instance from ORM object
        populate_by_name=True,  # Allow populating by attribute name (for aliases)
    )


# --- Response Schemas ---


class WeightLogResponse(WeightLogInDBBase):
    """
    Schema for the response after successfully logging or retrieving a weight entry.
    Inherits all fields from WeightLogInDBBase.
    """

    pass


class WeightLogEntryResponse(BaseModel):
    """
    Schema for returning a single weight log entry in a history list.
    Includes optional BMI calculation.
    """

    id: uuid.UUID = Field(..., description="Unique ID of the weight log entry")
    weight_kg: float = Field(..., description="Logged weight in kilograms")
    # Alias 'logged_at' to map from the model's 'created_at' field
    logged_at: datetime = Field(
        ..., alias="created_at", description="Timestamp when the weight was logged"
    )
    log_date: datetime = Field(..., description="Date when the weight was measured")
    bmi: Optional[float] = Field(
        None, description="Calculated Body Mass Index (BMI) if height is available"
    )
    goal_weight_kg: Optional[float] = Field(
        None, description="Patient's current goal weight in kg"
    )
    progress_to_goal_kg: Optional[float] = Field(
        None, description="Progress towards goal weight (negative = still need to lose, positive = exceeded goal)"
    )
    progress_percentage: Optional[float] = Field(
        None, description="Progress percentage towards goal weight (0-100%+)"
    )

    model_config = ConfigDict(
        from_attributes=True,  # Allows creating instance from ORM object
        populate_by_name=True,  # Allow populating by attribute name (for aliases)
    )


# --- Request Schema (Kept for potential direct use in API if different from Create) ---
class WeightLogRequest(BaseModel):
    """
    Schema for requesting to log a weight entry (might be identical to Create).
    """

    weight_kg: float = Field(
        ..., gt=0, description="Weight in kilograms (must be positive)"
    )
    log_date: datetime = Field(..., description="Date when the weight was measured")


# --- Paginated Response Schema ---


class PaginatedWeightLogResponse(BaseModel):
    """
    Response model for a paginated list of weight log entries.
    """

    items: list[WeightLogEntryResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = ConfigDict(from_attributes=True)
