import os

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.crud.crud_medication import medication
from app.schemas.medication import MedicationCreate


def seed_medications():
    # Create a local database connection using localhost instead of 'db'
    SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL", "").replace(
        "@db:", "@localhost:"
    )
    if not SQLALCHEMY_DATABASE_URL:
        SQLALCHEMY_DATABASE_URL = "postgresql+psycopg2://pulsetrack_user:supersecretlocalpassword@localhost:5432/pulsetrack_dev"

    print(f"Using database URL: {SQLALCHEMY_DATABASE_URL}")

    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    SessionOverride = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionOverride()

    try:
        # Aesthetic Medications
        aesthetic_meds = [
            MedicationCreate(
                name="Botox",
                description="Botulinum toxin type A for facial wrinkles and muscle relaxation",
                dosage_guidelines="Dosage varies by treatment area. Typically 20-50 units per area",
                common_side_effects="Temporary bruising, swelling, headache, drooping eyelids",
            ),
            MedicationCreate(
                name="Dermal Fillers (Hyaluronic Acid)",
                description="Injectable fillers for facial volume restoration and wrinkle reduction",
                dosage_guidelines="0.5-2ml per treatment area depending on product and area",
                common_side_effects="Swelling, bruising, redness, tenderness at injection site",
            ),
            MedicationCreate(
                name="Kybella",
                description="Deoxycholic acid for submental fat reduction",
                dosage_guidelines="0.2-2ml per treatment session, up to 6 sessions",
                common_side_effects="Swelling, bruising, pain, numbness, redness",
            ),
            MedicationCreate(
                name="Sculptra",
                description="Poly-L-lactic acid for facial volume restoration",
                dosage_guidelines="1-2 vials per treatment, 2-3 treatments recommended",
                common_side_effects="Swelling, bruising, redness, small bumps under skin",
            ),
        ]

        # GLP-1 Weight Management Medications
        glp1_meds = [
            MedicationCreate(
                name="Semaglutide (Wegovy)",
                description="GLP-1 receptor agonist for chronic weight management",
                dosage_guidelines="Start at 0.25mg weekly, titrate up to 2.4mg weekly over 16-20 weeks",
                common_side_effects="Nausea, vomiting, diarrhea, constipation, abdominal pain",
            ),
            MedicationCreate(
                name="Tirzepatide (Zepbound)",
                description="GLP-1 and GIP receptor agonist for chronic weight management",
                dosage_guidelines="Start at 2.5mg weekly, titrate up to 15mg weekly over 20 weeks",
                common_side_effects="Nausea, diarrhea, vomiting, constipation, abdominal pain",
            ),
            MedicationCreate(
                name="Liraglutide (Saxenda)",
                description="GLP-1 receptor agonist for chronic weight management",
                dosage_guidelines="Start at 0.6mg daily, titrate up to 3mg daily over 4 weeks",
                common_side_effects="Nausea, vomiting, diarrhea, constipation, headache",
            ),
        ]

        # Add all medications
        for med in aesthetic_meds + glp1_meds:
            # Check if medication already exists
            existing_med = medication.get_by_name(db=db, name=med.name)
            if not existing_med:
                medication.create(db=db, obj_in=med)
                print(f"Added medication: {med.name}")
            else:
                print(f"Medication already exists: {med.name}")

        db.commit()
        print("Medication seeding completed successfully")
    except Exception as e:
        print(f"Error seeding medications: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    seed_medications()
