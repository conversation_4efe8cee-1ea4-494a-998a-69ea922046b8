"""
Action Chain Executor Service

This service handles the execution of chained actions - multiple related actions
that need to be executed in sequence or parallel based on user intent.
"""

from typing import Dict, List, Optional, Any
import asyncio
import logging
from datetime import datetime
from uuid import uuid4

from sqlalchemy.orm import Session

from app.schemas.action_chain_v2 import (
    ChainedAction,
    ActionResult,
    ActionChainResult,
    ChainContext,
    ExecutionMode,
    FailureMode,
    ChainValidationResult,
    ChainedIntent
)
from app.services.action_executor_service import ActionExecutorService
logger = logging.getLogger(__name__)


class ActionChainExecutorService:
    """Service for executing chains of related actions."""
    
    def __init__(self, db: Session):
        self.db = db
        self.action_executor = ActionExecutorService(db)
        
    async def execute_chain(
        self,
        user_id: str,
        user_role: str,
        chain: ChainedAction,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> ActionChainResult:
        """
        Execute a chain of actions based on the defined execution mode.
        
        Args:
            user_id: The user executing the chain
            user_role: The role of the user (patient/clinician)
            chain: The chain definition
            initial_context: Initial context data
            
        Returns:
            ActionChainResult with all execution results
        """
        logger.info(f"Starting execution of chain {chain.chain_id} for user {user_id}")
        
        # Validate the chain before execution
        validation = self._validate_chain(chain)
        if not validation.is_valid:
            logger.error(f"Chain validation failed: {validation.errors}")
            return ActionChainResult(
                chain_id=chain.chain_id,
                success=False,
                results=[],
                context=ChainContext(data=initial_context or {}),
                error_message=f"Chain validation failed: {', '.join(validation.errors)}",
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow()
            )
        
        # Initialize context
        context = ChainContext(data=initial_context or {})
        results: List[ActionResult] = []
        
        # Execute based on mode
        started_at = datetime.utcnow()
        
        try:
            if chain.execution_mode == ExecutionMode.SEQUENTIAL:
                results = await self._execute_sequential(
                    user_id, user_role, chain, context
                )
            else:  # PARALLEL
                results = await self._execute_parallel(
                    user_id, user_role, chain, context
                )
            
            # Determine overall success
            success = all(r.success for r in results)
            error_message = None
            
            if not success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
                failed_results = [r for r in results if not r.success]
                error_message = f"Chain failed: {failed_results[0].error_message}"
                
        except Exception as e:
            logger.error(f"Chain execution failed with exception: {str(e)}", exc_info=True)
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Chain execution state - results so far: {results}")
            logger.error(f"Chain details: primary={chain.primary_action.action_type}, follow_ups={[a.action_type for a in chain.follow_up_actions]}")
            success = False
            error_message = f"Chain execution failed: {str(e)}"
            
        completed_at = datetime.utcnow()
        
        return ActionChainResult(
            chain_id=chain.chain_id,
            success=success,
            results=results,
            context=context,
            error_message=error_message,
            started_at=started_at,
            completed_at=completed_at,
            execution_time_ms=int((completed_at - started_at).total_seconds() * 1000)
        )
    
    async def _execute_sequential(
        self,
        user_id: str,
        user_role: str,
        chain: ChainedAction,
        context: ChainContext
    ) -> List[ActionResult]:
        """Execute actions sequentially."""
        results = []
        
        # Execute primary action first
        primary_result = await self._execute_single_action(
            user_id, user_role, chain.primary_action, context
        )
        results.append(primary_result)
        
        # Update context with primary result
        if primary_result.success and primary_result.data:
            context.update_from_action_result(
                chain.primary_action.action_type,
                primary_result.data
            )
        
        # Check if we should continue after primary action
        if not primary_result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
            logger.info(f"Stopping chain execution due to primary action failure")
            return results
        
        # Execute follow-up actions
        for action in chain.follow_up_actions:
            # Check dependencies
            if not self._check_dependencies(action, results, context, chain.primary_action.action_type):
                logger.warning(f"Skipping action {action.action_type} due to unmet dependencies")
                continue
            
            result = await self._execute_single_action(
                user_id, user_role, action, context
            )
            results.append(result)
            
            # Update context
            if result.success and result.data:
                context.update_from_action_result(action.action_type, result.data)
            
            # Check if we should continue
            if not result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
                logger.info(f"Stopping chain execution due to action failure: {action.action_type}")
                break
        
        return results
    
    async def _execute_parallel(
        self,
        user_id: str,
        user_role: str,
        chain: ChainedAction,
        context: ChainContext
    ) -> List[ActionResult]:
        """Execute actions in parallel where possible."""
        results = []
        
        # Execute primary action first (it's always first)
        primary_result = await self._execute_single_action(
            user_id, user_role, chain.primary_action, context
        )
        results.append(primary_result)
        
        # Update context
        if primary_result.success and primary_result.data:
            context.update_from_action_result(
                chain.primary_action.action_type,
                primary_result.data
            )
        
        # Check if we should continue
        if not primary_result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
            return results
        
        # Group follow-up actions by dependency level
        action_groups = self._group_actions_by_dependency(chain.follow_up_actions)
        
        # Execute each group in parallel
        for group in action_groups:
            # Create tasks for parallel execution
            tasks = []
            for action in group:
                if self._check_dependencies(action, results, context, chain.primary_action.action_type):
                    task = self._execute_single_action(
                        user_id, user_role, action, context
                    )
                    tasks.append((action, task))
            
            # Execute group in parallel
            if tasks:
                group_results = await asyncio.gather(
                    *[task for _, task in tasks],
                    return_exceptions=True
                )
                
                # Process results
                for i, (action, _) in enumerate(tasks):
                    result = group_results[i]
                    if isinstance(result, Exception):
                        result = ActionResult(
                            action_type=action.action_type,
                            success=False,
                            error_message=str(result),
                            executed_at=datetime.utcnow()
                        )
                    
                    results.append(result)
                    
                    # Update context
                    if result.success and result.data:
                        context.update_from_action_result(action.action_type, result.data)
                    
                    # Check failure mode
                    if not result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
                        return results
        
        return results
    
    async def _execute_single_action(
        self,
        user_id: str,
        user_role: str,
        action: ChainedIntent,
        context: ChainContext
    ) -> ActionResult:
        """Execute a single action within the chain."""
        try:
            # Apply context substitutions to parameters
            enriched_params = self._apply_context_substitutions(
                action.parameters,
                context
            )
            
            logger.info(f"Executing action {action.action_type} with parameters: {enriched_params}")
            
            # Create a new intent with enriched parameters
            enriched_intent = ChainedIntent(
                action_type=action.action_type,
                parameters=enriched_params,
                confidence=action.confidence,
                dependencies=action.dependencies,
                context_requirements=action.context_requirements
            )
            
            # Execute via the standard action executor
            logger.info(f"=== CHAIN EXECUTOR: About to call action_executor.execute_action ===")
            logger.info(f"User ID: {user_id}, Role: {user_role}")
            logger.info(f"Intent type: {type(enriched_intent)}")
            logger.info(f"Context data keys: {list(context.data.keys())}")
            
            response = await self.action_executor.execute_action(
                user_id=user_id,
                user_role=user_role,
                resolved_intent=enriched_intent,
                chat_context={"chain_context": context.data}
            )
            
            logger.info(f"=== CHAIN EXECUTOR: action_executor.execute_action returned ===")
            logger.info(f"Response type: {type(response)}")
            logger.info(f"Success: {response.success}")
            logger.info(f"Message: {response.message}")
            
            return ActionResult(
                action_type=action.action_type,
                success=response.success,
                data=response.data,
                message=response.message if response.success else None,
                error_message=response.message if not response.success else None,
                executed_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error executing action {action.action_type}: {str(e)}", exc_info=True)
            return ActionResult(
                action_type=action.action_type,
                success=False,
                error_message=str(e),
                executed_at=datetime.utcnow()
            )
    
    def _apply_context_substitutions(
        self,
        parameters: Dict[str, Any],
        context: ChainContext
    ) -> Dict[str, Any]:
        """
        Apply context substitutions to action parameters.
        
        Replaces placeholders like {{appointment_id}} with actual values from context.
        """
        enriched = {}
        
        for key, value in parameters.items():
            if isinstance(value, str):
                # Check for embedded placeholders like "Text {{key}} more text"
                import re
                pattern = r'\{\{([^}]+)\}\}'
                
                def replace_placeholder(match):
                    context_key = match.group(1).strip()
                    context_value = context.get(context_key)
                    if context_value is not None:
                        return str(context_value)
                    else:
                        logger.warning(f"Context key '{context_key}' not found")
                        return match.group(0)  # Keep original placeholder
                
                enriched[key] = re.sub(pattern, replace_placeholder, value)
            else:
                enriched[key] = value
        
        return enriched
    
    def _check_dependencies(
        self,
        action: ChainedIntent,
        completed_results: List[ActionResult],
        context: ChainContext,
        primary_action_type: str = None
    ) -> bool:
        """Check if all dependencies for an action are met."""
        if not action.dependencies:
            return True
        
        for dep in action.dependencies:
            # Handle "primary_action" as a special case - map it to the actual primary action type
            required_action = dep.required_action
            if required_action == "primary_action" and primary_action_type:
                required_action = primary_action_type
                
            # Check if required action was executed
            dep_results = [
                r for r in completed_results 
                if r.action_type == required_action
            ]
            
            if not dep_results:
                logger.debug(f"Dependency not met: {required_action} (original: {dep.required_action}) not executed")
                return False
            
            # Check if it was successful (if required)
            if dep.require_success and not all(r.success for r in dep_results):
                logger.debug(f"Dependency not met: {required_action} (original: {dep.required_action}) did not succeed")
                return False
        
        # Check context requirements
        if action.context_requirements:
            for req in action.context_requirements:
                if context.get(req) is None:
                    logger.debug(f"Context requirement not met: {req} not in context")
                    return False
        
        return True
    
    def _group_actions_by_dependency(
        self,
        actions: List[ChainedIntent]
    ) -> List[List[ChainedIntent]]:
        """
        Group actions by dependency level for parallel execution.
        
        Actions with no dependencies or only dependencies on the primary action
        can be executed in parallel. Actions depending on other follow-up actions
        must wait.
        """
        # Simple implementation: group by whether they have dependencies on other follow-ups
        no_deps = []
        with_deps = []
        
        follow_up_types = {a.action_type for a in actions}
        
        for action in actions:
            if not action.dependencies:
                no_deps.append(action)
            else:
                # Check if any dependencies are on other follow-up actions
                has_follow_up_deps = any(
                    dep.required_action in follow_up_types 
                    for dep in action.dependencies
                )
                
                if has_follow_up_deps:
                    with_deps.append(action)
                else:
                    no_deps.append(action)
        
        # Return groups: first group can run in parallel, second group sequential
        groups = []
        if no_deps:
            groups.append(no_deps)
        if with_deps:
            # Add each dependent action as its own group for now
            # Could be optimized to detect independent chains
            for action in with_deps:
                groups.append([action])
        
        return groups
    
    def _validate_chain(self, chain: ChainedAction) -> ChainValidationResult:
        """Validate the chain configuration before execution."""
        errors = []
        warnings = []
        
        # Check for circular dependencies
        all_actions = [chain.primary_action] + chain.follow_up_actions
        action_types = {a.action_type for a in all_actions}
        
        for action in all_actions:
            if action.dependencies:
                for dep in action.dependencies:
                    if dep.required_action not in action_types:
                        warnings.append(
                            f"Action {action.action_type} depends on {dep.required_action} "
                            "which is not in the chain"
                        )
        
        # Check for duplicate action types (might be intentional)
        seen_types = set()
        for action in all_actions:
            if action.action_type in seen_types:
                warnings.append(f"Duplicate action type in chain: {action.action_type}")
            seen_types.add(action.action_type)
        
        # Validate parallel execution compatibility
        if chain.execution_mode == ExecutionMode.PARALLEL:
            # Check if follow-up actions have dependencies on each other
            follow_up_types = {a.action_type for a in chain.follow_up_actions}
            for action in chain.follow_up_actions:
                if action.dependencies:
                    for dep in action.dependencies:
                        if dep.required_action in follow_up_types:
                            warnings.append(
                                f"Parallel execution may be inefficient: "
                                f"{action.action_type} depends on {dep.required_action}"
                            )
        
        return ChainValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )