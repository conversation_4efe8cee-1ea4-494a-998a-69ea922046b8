"""Action Executor Service for LLM-driven API actions.

This service executes resolved intents by:
1. Checking permissions for the requested action
2. Executing the corresponding API action
3. Handling errors and providing appropriate responses
4. Logging actions for audit purposes
"""

import logging
from datetime import datetime
from enum import Enum
from typing import Any, Optional, Union

from fastapi import HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app import crud, schemas
from app.core.constants.appointment import ALLOWED_APPOINTMENT_TYPES  # Added import
from app.services.intent_resolver_service import (
    IntentParameterValue,
    IntentResolutionResult,
    ResolvedIntent,
)
from app.schemas.action_chain_v2 import ChainedIntent
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)


class ActionExecutionResult(str, Enum):
    """Enum representing the possible outcomes of action execution."""

    SUCCESS = "success"
    PERMISSION_DENIED = "permission_denied"
    RESOURCE_NOT_FOUND = "resource_not_found"
    INVALID_INPUT = "invalid_input"
    CONFLICT = "conflict"
    INTERNAL_ERROR = "internal_error"
    SERVICE_UNAVAILABLE = "service_unavailable"


class ActionResponse(BaseModel):
    """Model representing the response from action execution."""

    success: bool
    result: ActionExecutionResult
    message: str
    data: Optional[dict[str, Any]] = None
    error_detail: Optional[str] = None
    action_type: str
    resource_id: Optional[str] = None
    resource_type: Optional[str] = None


class ActionExecutorService:
    """Service for executing resolved LLM intents as API actions."""

    # Action handlers for each supported action type
    ACTION_HANDLERS = {
        # Appointment actions
        "appointment_create": "_handle_appointment_create",
        "appointment_cancel": "_handle_appointment_cancel",
        "appointment_request_create": "_handle_appointment_request_create",
        # Medication request actions
        "medication_request_create": "_handle_medication_request_create",
        "medication_request_update": "_handle_medication_request_update",
        # Side effect report actions
        "side_effect_report_create": "_handle_side_effect_report_create",
        # Note actions
        "note_create": "_handle_note_create",
        # Notification actions
        "notification_create": "_handle_notification_create",
        # Query actions
        "patient_search": "_handle_patient_search",
        "query_history": "_handle_query_history",
        "health_data_retrieve": "_handle_health_data_retrieve",
        # Weight log actions
        "weight_log_create": "_handle_weight_log_create",
    }

    # --- RBAC: Define allowed roles for each action ---
    ACTION_PERMISSIONS = {
        "appointment_create": ["patient", "clinician"],
        "appointment_cancel": ["patient", "clinician"],
        "appointment_request_create": ["patient"],
        "medication_request_create": ["patient", "clinician"],
        "medication_request_update": ["patient", "clinician"],
        "side_effect_report_create": ["patient", "clinician"],
        "health_data_retrieve": ["patient", "clinician"],
        "query_history": ["patient", "clinician"],
        # Clinician/admin only:
        "note_create": ["clinician"],
        "notification_create": ["clinician", "admin"],
        "patient_search": ["clinician", "admin"],
        # Weight log actions
        "weight_log_create": ["patient", "clinician"],
    }

    def __init__(self, db: Session):
        """Initialize the Action Executor Service."""
        self.db = db

    async def execute_action(
        self,
        user_id: str,
        user_role: str,
        resolved_intent: Union[ResolvedIntent, ChainedIntent],
        chat_context: dict = None,
    ) -> ActionResponse:
        """Execute the action specified by the resolved intent.

        Args:
            user_id: The ID of the user executing the action
            user_role: The role of the user executing the action
            resolved_intent: The resolved intent containing action type and parameters
            chat_context: Optional chat context containing additional information like timezone_offset
        """
        # Handle both ResolvedIntent and ChainedIntent
        if isinstance(resolved_intent, ChainedIntent):
            # Convert ChainedIntent to ResolvedIntent format for execution
            action_type = resolved_intent.action_type
            parameters = [
                IntentParameterValue(
                    name=name,
                    value=value,
                    confidence=resolved_intent.confidence,
                    source="chain_context"
                )
                for name, value in resolved_intent.parameters.items()
            ]
            result = IntentResolutionResult.SUCCESS
            error_message = None
        else:
            # Handle normal ResolvedIntent
            action_type = resolved_intent.action_type
            parameters = resolved_intent.parameters
            result = resolved_intent.result
            error_message = resolved_intent.error_message

        # Debug logging for chat context
        if chat_context:
            logger.info(f"TIMEZONE DEBUG: Chat context received: {chat_context}")

            # If timezone_offset is in chat_context but not in parameters, add it
            if "timezone_offset" in chat_context and not any(
                param.name == "timezone_offset" for param in parameters
            ):
                logger.info(
                    f"TIMEZONE DEBUG: Adding timezone_offset={chat_context['timezone_offset']} from chat context to parameters"
                )
                parameters.append(
                    IntentParameterValue(
                        name="timezone_offset",
                        value=float(chat_context["timezone_offset"]),
                        confidence=1.0,
                        source="chat_context",
                    )
                )

        # Skip execution if intent resolution failed
        if result != IntentResolutionResult.SUCCESS:
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params={
                    param.name: param.value for param in parameters
                },
                result=ActionResponse(
                    success=False,
                    result=ActionExecutionResult.INVALID_INPUT,
                    message="Intent resolution failed",
                    error_detail=error_message,
                    action_type=action_type,
                ),
            )
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Intent resolution failed",
                error_detail=error_message,
                action_type=action_type,
            )

        # RBAC: Check permissions for action
        if not self._check_permissions(user_role, action_type):
            msg = f"User role '{user_role}' not permitted for action '{action_type}'"
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params={
                    param.name: param.value for param in parameters
                },
                result=ActionResponse(
                    success=False,
                    result=ActionExecutionResult.PERMISSION_DENIED,
                    message=msg,
                    error_detail=msg,
                    action_type=action_type,
                ),
            )
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.PERMISSION_DENIED,
                message=msg,
                error_detail=msg,
                action_type=action_type,
            )

        # Strict parameter validation
        handler_name = self.ACTION_HANDLERS.get(action_type)
        if not handler_name or not hasattr(self, handler_name):
            msg = f"No handler found for action '{action_type}'"
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params={
                    param.name: param.value for param in parameters
                },
                result=ActionResponse(
                    success=False,
                    result=ActionExecutionResult.INVALID_INPUT,
                    message=msg,
                    error_detail=msg,
                    action_type=action_type,
                ),
            )
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message=msg,
                error_detail=msg,
                action_type=action_type,
            )

        # Validate required parameters (only truly required ones, not optional)
        param_dict = {param.name: param.value for param in parameters}
        
        # Define required parameters for each action type
        required_params = {
            "side_effect_report_create": ["patient_id", "medication_name", "symptoms", "severity"],
            "appointment_create": ["patient_id", "scheduled_time", "appointment_type"],
            "medication_request_create": ["patient_id", "medication_name"],
            "note_create": ["patient_id", "note_content"],
            # Add other actions as needed
        }
        
        required_for_action = required_params.get(action_type, [])
        missing_params = [k for k in required_for_action if param_dict.get(k) is None]
        
        if missing_params:
            msg = f"Missing required parameters: {', '.join(missing_params)}"
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params=param_dict,
                result=ActionResponse(
                    success=False,
                    result=ActionExecutionResult.INVALID_INPUT,
                    message=msg,
                    error_detail=msg,
                    action_type=action_type,
                ),
            )
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message=msg,
                error_detail=msg,
                action_type=action_type,
            )

        try:
            # Execute the action handler
            logger.info(f"=== ACTION EXECUTION START ===")
            logger.info(f"Action: {action_type}, User: {user_id}, Role: {user_role}")
            logger.info(f"Parameters: {param_dict}")
            
            handler = getattr(self, handler_name)
            logger.info(f"Handler found: {handler_name}")
            
            result = await handler(user_id, user_role, param_dict, chat_context)
            
            logger.info(f"Handler execution completed")
            logger.info(f"Result: success={result.success}, message={result.message}")

            # Log the action execution
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params=param_dict,
                result=result,
            )
            
            logger.info(f"=== ACTION EXECUTION END ===")
            return result

        except HTTPException as e:
            # Convert HTTP exceptions to action responses
            result = ActionResponse(
                success=False,
                result=self._map_status_code_to_result(e.status_code),
                message=str(e.detail),
                error_detail=None,
                action_type=action_type,
            )
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params=param_dict,
                result=result,
            )
            return result
        except Exception as e:
            logger.error(
                f"Unexpected error during action execution: {e}", exc_info=True
            )
            result = ActionResponse(
                success=False,
                result=ActionExecutionResult.INTERNAL_ERROR,
                message="An unexpected error occurred while executing the action",
                error_detail=str(e),
                action_type=action_type,
            )
            await self._log_action_execution(
                user_id=user_id,
                user_role=user_role,
                action_type=action_type,
                params=param_dict,
                result=result,
            )
            return result

    def _check_permissions(self, user_role: str, action_type: str) -> bool:
        """Check if the user has permission to perform the action."""
        if action_type not in self.ACTION_PERMISSIONS:
            return False
        allowed_roles = self.ACTION_PERMISSIONS[action_type]
        if isinstance(user_role, list):
            return any(role in allowed_roles for role in user_role)
        else:
            return user_role in allowed_roles

    def _map_status_code_to_result(self, status_code: int) -> ActionExecutionResult:
        """Map HTTP status code to action execution result."""
        if status_code == status.HTTP_403_FORBIDDEN:
            return ActionExecutionResult.PERMISSION_DENIED
        elif status_code == status.HTTP_404_NOT_FOUND:
            return ActionExecutionResult.RESOURCE_NOT_FOUND
        elif status_code == status.HTTP_400_BAD_REQUEST:
            return ActionExecutionResult.INVALID_INPUT
        elif status_code == status.HTTP_409_CONFLICT:
            return ActionExecutionResult.CONFLICT
        elif status_code == status.HTTP_503_SERVICE_UNAVAILABLE:
            return ActionExecutionResult.SERVICE_UNAVAILABLE
        else:
            return ActionExecutionResult.INTERNAL_ERROR

    async def _log_action_execution(
        self,
        user_id: str,
        user_role: str,
        action_type: str,
        params: dict[str, Any],
        result: ActionResponse,
    ) -> None:
        """Log the action execution to the event log."""
        try:
            # Create log entry data
            log_data = {
                "action": action_type,
                "actor_user_id": user_id,
                "actor_role": user_role if isinstance(user_role, str) else user_role[0],
                "target_resource_type": result.resource_type or user_role,
                "target_resource_id": result.resource_id or user_id,
                "outcome": "SUCCESS" if result.success else "FAILURE",
                "details": {
                    "params": params,
                    "message": result.message,
                    "error_detail": result.error_detail,
                },
            }
            # Log to event log and audit log
            # Create the event log entry
            event_log_data = schemas.EventLogCreate(
                action=f"LLM_{action_type.upper()}",
                actor_user_id=user_id,
                actor_role=user_role if isinstance(user_role, str) else user_role[0],
                target_resource_type=result.resource_type or user_role,
                target_resource_id=result.resource_id or user_id,
                status="SUCCESS" if result.success else "FAILURE",
                details=log_data["details"],
            )

            # Try to save to database using the correct method
            try:
                crud.event_log.event_log.create_with_actor(
                    self.db,
                    obj_in=event_log_data,
                    actor_user_id=user_id,
                    actor_role=(
                        user_role if isinstance(user_role, str) else user_role[0]
                    ),
                )
                logger.info(f"Created event log for LLM action: {action_type}")
            except Exception as e:
                logger.error(f"Failed to create event log: {e}", exc_info=True)

            # Also create an audit log entry
            log_audit_event(
                db=self.db,
                action=f"LLM_{action_type.upper()}",
                actor_user_id=user_id,
                actor_role=user_role,
                target_resource_type=result.resource_type or user_role,
                target_resource_id=result.resource_id or user_id,
                details=log_data["details"],
                outcome="SUCCESS" if result.success else "FAILURE",
            )

        except Exception as e:
            logger.error(f"Error logging action execution: {e}", exc_info=True)

    # Implementation of action handlers

    async def _handle_appointment_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle appointment creation."""
        try:
            # Extract parameters
            patient_id = params.get("patient_id")
            
            # Check if patient_id is in chat context first (for clinician actions)
            patient_id_from_context = None
            if chat_context:
                # Check for patient_id in chain_context or direct context
                chain_ctx = chat_context.get("chain_context", {})
                patient_id_from_context = chain_ctx.get("patient_id") or chain_ctx.get("currentPatientId")
                if not patient_id_from_context:
                    # Check direct context
                    patient_id_from_context = chat_context.get("patient_id") or chat_context.get("currentPatientId")
            
            # If patient_id looks like a name (not a UUID), prefer context
            if patient_id and not patient_id.startswith(("user_", "patient_")) and patient_id_from_context:
                logger.info(f"Patient ID '{patient_id}' appears to be a name, using context patient_id instead: {patient_id_from_context}")
                patient_id = patient_id_from_context
            elif not patient_id and patient_id_from_context:
                logger.info(f"Using patient_id from chat context: {patient_id_from_context}")
                patient_id = patient_id_from_context

            # If patient_id is not provided and user is a patient, use their ID
            if not patient_id and user_role == "patient":
                patient_id = user_id
                logger.info(f"Using current user ID as patient_id: {patient_id}")

            patient_name = params.get(
                "patient_name"
            )  # Also get the patient name if available

            # Always use Clerk ID (string) for clinician_id
            clinician_id = params.get("clinician_id")
            if not clinician_id and user_role == "clinician":
                clinician_id = user_id

            # Validate that we have a patient_id
            if not patient_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Patient ID is required for appointment creation",
                )

            # Handle scheduled_time from the LLM output (compatibility with various formats)
            # Support both 'scheduled_time' and 'appointment_datetime' parameter names
            scheduled_time = params.get("scheduled_time") or params.get("appointment_datetime")
            appointment_date = params.get("appointment_date")
            appointment_time = params.get("appointment_time")
            
            # Debug logging to see what parameters we're receiving
            logger.info(f"APPOINTMENT PARAMS DEBUG: Received parameters: {params}")
            logger.info(f"APPOINTMENT PARAMS DEBUG: scheduled_time={scheduled_time}, appointment_datetime={params.get('appointment_datetime')}")

            # --- Simplified Timezone Handling for Appointment Creation ---
            # Assume the time is already in the user's local timezone as returned by the LLM
            import pytz
            from dateutil import parser as dateutil_parser

            # Extract timezone information from parameters
            client_timezone_offset = params.get("timezone_offset")
            # Acceptable keys: 'timezone_offset' (float, in hours), or 'timezone' (string, e.g., 'America/Chicago')
            timezone_name = params.get("timezone")

            # Debug logging to verify timezone_offset is being received
            logger.info(
                f"TIMEZONE DEBUG: Received timezone_offset from parameters: {client_timezone_offset}"
            )
            logger.info(
                f"TIMEZONE DEBUG: Received timezone name from parameters: {timezone_name}"
            )
            logger.info(f"TIMEZONE DEBUG: All parameters: {params}")

            # Determine the actual timezone to use
            local_tz = None
            if client_timezone_offset is not None:
                # Ensure client_timezone_offset is a float
                try:
                    if isinstance(client_timezone_offset, str):
                        client_timezone_offset = float(client_timezone_offset)
                    else:
                        client_timezone_offset = float(client_timezone_offset)

                    logger.info(
                        f"TIMEZONE DEBUG: Using client timezone offset: {client_timezone_offset} hours"
                    )

                    # Create timezone from offset
                    try:
                        # IMPORTANT: Etc/GMT format has INVERTED signs compared to standard notation
                        # Etc/GMT+5 means UTC-5, Etc/GMT-5 means UTC+5
                        # This is a POSIX convention that pytz follows
                        sign = (
                            "-" if client_timezone_offset >= 0 else "+"
                        )  # INVERTED sign for Etc/GMT format
                        abs_offset = abs(client_timezone_offset)
                        hours = int(abs_offset)
                        minutes = int((abs_offset - hours) * 60)

                        # Etc/GMT format doesn't support minutes in the name, only hours
                        # If minutes exist, we'll need to round or use a different approach
                        if minutes > 0:
                            logger.info(
                                f"TIMEZONE DEBUG: Minutes in offset ({minutes}), rounding to nearest hour"
                            )
                            if minutes >= 30:
                                hours += 1  # Round up

                        tz_str = f"Etc/GMT{sign}{hours}"  # No minutes, no padding
                        local_tz = pytz.timezone(tz_str)
                        logger.info(
                            f"TIMEZONE DEBUG: Using timezone from offset: {tz_str} ({client_timezone_offset} hours)"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error creating timezone from offset {client_timezone_offset}: {e}"
                        )
                        local_tz = pytz.UTC  # Fallback to UTC
                except (ValueError, TypeError) as e:
                    logger.error(
                        f"Error converting timezone_offset to float: {e}, value was: {client_timezone_offset}"
                    )
                    local_tz = pytz.UTC  # Fallback to UTC
            elif timezone_name:
                try:
                    local_tz = pytz.timezone(timezone_name)
                    logger.info(f"Using named timezone: {timezone_name}")
                except Exception as e:
                    logger.error(
                        f"Error creating timezone from name {timezone_name}: {e}"
                    )
                    local_tz = pytz.UTC  # Fallback to UTC
            else:
                # Use system timezone if available
                system_offset = (
                    datetime.now().astimezone().utcoffset().total_seconds() / 3600
                )
                logger.info(f"Using system timezone with offset: {system_offset} hours")
                try:
                    local_tz = datetime.now().astimezone().tzinfo
                except Exception:
                    local_tz = pytz.UTC  # Fallback to UTC

            appointment_datetime = None
            if scheduled_time:
                try:
                    logger.info(f"Processing scheduled_time from LLM: {scheduled_time}")

                    # Check if this time is from the LLM with UTC marker but local time value
                    is_from_llm = False
                    if (
                        "+00:00" in scheduled_time
                        and client_timezone_offset is not None
                    ):
                        is_from_llm = True
                        logger.info(
                            f"TIMEZONE DEBUG: Detected time from LLM with UTC marker but local time value: {scheduled_time}"
                        )
                        logger.info(
                            f"TIMEZONE DEBUG: Client timezone offset: {client_timezone_offset}"
                        )

                    # Parse with dateutil to handle the datetime string
                    dt = dateutil_parser.isoparse(scheduled_time)
                    logger.info(
                        f"TIMEZONE DEBUG: After initial parse: {dt} (tzinfo={dt.tzinfo})"
                    )

                    if is_from_llm:
                        # CRITICAL FIX: The LLM returns times with +00:00 UTC marker, but the time value (20:00)
                        # is already in local time. We need to handle this special case correctly.

                        # 1. First, extract just the time components without timezone
                        naive_dt = dt.replace(tzinfo=None)
                        logger.info(
                            f"TIMEZONE DEBUG: Converted to naive datetime: {naive_dt}"
                        )

                        # 2. Now apply the correct timezone WITHOUT adjusting the time value
                        # This is the key fix - we need to preserve the original time value
                        if local_tz:
                            # Use localize which doesn't adjust the time value
                            dt = local_tz.localize(naive_dt)
                            logger.info(
                                f"TIMEZONE DEBUG: Applied correct local timezone: {dt}"
                            )
                            logger.info(
                                f"TIMEZONE DEBUG: Local timezone used: {local_tz}"
                            )

                            # 3. Verify the time is still 8PM (or whatever was requested)
                            logger.info(
                                f"TIMEZONE DEBUG: Time after timezone application: {dt.hour}:{dt.minute}"
                            )
                        else:
                            logger.info(
                                "TIMEZONE DEBUG: No local_tz available, using UTC"
                            )
                            dt = pytz.UTC.localize(naive_dt)
                    elif dt.tzinfo is None:
                        # Regular timezone-naive datetime, apply local timezone
                        if local_tz:
                            dt = local_tz.localize(dt)
                            logger.info(
                                f"Applied local timezone to naive datetime: {dt}"
                            )
                        else:
                            dt = dt.replace(tzinfo=pytz.UTC)
                            logger.info(f"Applied UTC timezone to naive datetime: {dt}")

                    # Always store in UTC
                    # Log the time before conversion to verify it's still correct
                    logger.info(
                        f"TIMEZONE DEBUG: Before UTC conversion: {dt} (hour={dt.hour})"
                    )
                    logger.info(
                        f"TIMEZONE DEBUG: Using timezone: {local_tz} (offset={client_timezone_offset})"
                    )
                    logger.info(
                        f"TIMEZONE DEBUG: Original datetime tzinfo: {dt.tzinfo}"
                    )

                    # Convert to UTC for storage
                    appointment_datetime = dt.astimezone(pytz.utc)

                    # Log the final UTC time to verify the conversion preserved the correct time
                    logger.info(
                        f"TIMEZONE DEBUG: Final UTC datetime for storage: {appointment_datetime}"
                    )
                    logger.info(
                        f"TIMEZONE DEBUG: UTC hour={appointment_datetime.hour}, original hour={dt.hour}"
                    )

                    # Calculate expected hour difference based on timezone offset
                    expected_hour_diff = 0
                    if client_timezone_offset is not None:
                        expected_hour_diff = int(client_timezone_offset)
                        logger.info(
                            f"TIMEZONE DEBUG: Expected hour difference based on offset: {expected_hour_diff}"
                        )
                        logger.info(
                            f"TIMEZONE DEBUG: Actual hour difference: {dt.hour - appointment_datetime.hour}"
                        )

                    # Verify the conversion by converting back to original timezone
                    back_to_local = appointment_datetime.astimezone(dt.tzinfo)
                    logger.info(
                        f"TIMEZONE DEBUG: Converting back to local: {back_to_local} (hour={back_to_local.hour})"
                    )

                except Exception as e:
                    logger.error(f"Error parsing scheduled_time: {e}", exc_info=True)
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid scheduled_time format: {str(e)}",
                    )
            elif appointment_date and appointment_time:
                try:
                    # Parse date and time separately
                    dt_str = f"{appointment_date} {appointment_time}"
                    logger.info(f"Processing date and time separately: {dt_str}")

                    # Parse the combined date and time
                    dt = datetime.strptime(
                        f"{appointment_date} {appointment_time}", "%Y-%m-%d %H:%M"
                    )

                    # Since this wasn't processed by the LLM, just apply the local timezone directly
                    if local_tz:
                        dt = local_tz.localize(dt)
                        logger.info(f"Applied local timezone: {dt}")
                    else:
                        dt = dt.replace(tzinfo=pytz.UTC)
                        logger.info(f"Applied UTC timezone: {dt}")

                    # Convert to UTC for storage
                    appointment_datetime = dt.astimezone(pytz.utc)
                    logger.info(
                        f"Final UTC datetime for storage: {appointment_datetime}"
                    )

                except Exception as e:
                    logger.error(f"Error parsing date and time: {e}", exc_info=True)
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid date or time format. Use YYYY-MM-DD for date and HH:MM for time.",
                    )
            else:
                # Use default time (9 AM) if only date is provided
                try:
                    if appointment_date:
                        logger.info(
                            f"Using date with default time: {appointment_date} 09:00"
                        )

                        # Parse the date with default time
                        dt = datetime.strptime(
                            f"{appointment_date} 09:00", "%Y-%m-%d %H:%M"
                        )

                        # Apply local timezone
                        if local_tz:
                            dt = local_tz.localize(dt)
                            logger.info(f"Applied local timezone: {dt}")
                        else:
                            dt = dt.replace(tzinfo=pytz.UTC)
                            logger.info(f"Applied UTC timezone: {dt}")

                        # Convert to UTC for storage
                        appointment_datetime = dt.astimezone(pytz.utc)
                        logger.info(
                            f"Final UTC datetime for storage: {appointment_datetime}"
                        )
                except Exception as e:
                    logger.error(
                        f"Error using default time with date: {e}", exc_info=True
                    )
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid date format: {str(e)}",
                    )
            # --- End Timezone Handling ---

            # Only error if we failed to parse any datetime
            if not appointment_datetime:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Appointment date and time are required",
                )

            # Extract note from params (support both keys for robustness)
            patient_notes = params.get("patient_notes") or params.get("note")
            appointment_type = params.get("appointment_type")

            # Validate appointment_type
            if (
                not appointment_type
                or appointment_type not in ALLOWED_APPOINTMENT_TYPES
            ):
                # Default to "Initial" if not provided or invalid
                appointment_type = "Initial"
                logger.info(f"Using default appointment_type: {appointment_type}")

            duration_minutes = params.get("duration_minutes")

            # Validate duration_minutes
            if not duration_minutes:
                duration_minutes = 30
                logger.info(f"Using default duration_minutes: {duration_minutes}")
            elif isinstance(duration_minutes, str):
                try:
                    duration_minutes = int(duration_minutes)
                except ValueError:
                    duration_minutes = 30
                    logger.info(
                        f"Invalid duration_minutes format, using default: {duration_minutes}"
                    )

            reason = params.get("reason")

            # --- Patient Name-to-ID Resolution Logic ---
            import re

            from sqlalchemy.orm import Session

            from app.crud import patient as crud_patient

            db: Session = self.db if hasattr(self, "db") else None
            resolved_patient_id = patient_id
            error_message = None
            uuid_regex = r"^[\w-]{21,}$"
            if patient_id and not re.match(uuid_regex, str(patient_id)):
                name_to_resolve = patient_id
                if patient_name:
                    name_to_resolve = patient_name
                # Try to resolve by name (case-insensitive, full name)
                if db:
                    name_parts = name_to_resolve.strip().split()
                    if len(name_parts) >= 2:
                        first_name = name_parts[0]
                        last_name = " ".join(name_parts[1:])
                        patient_obj = crud_patient.get_by_full_name(
                            db, first_name=first_name, last_name=last_name
                        )
                        if patient_obj:
                            resolved_patient_id = patient_obj.id
                        else:
                            error_message = f"Could not resolve patient name '{name_to_resolve}' to a unique patient."
                    else:
                        error_message = f"Patient name '{name_to_resolve}' is incomplete. Provide full name."
                else:
                    error_message = (
                        "Database session unavailable for patient name resolution."
                    )
            if error_message:
                # Removed redundant import: from fastapi import HTTPException
                raise HTTPException(status_code=400, detail=error_message)

            # --- Get Primary Clinician if not provided ---
            if not clinician_id:
                logger.info(
                    f"Clinician ID not provided or resolved from user role. Attempting to find primary clinician for patient {resolved_patient_id}."
                )
                # Ensure crud_patient is available (already imported earlier for name resolution)
                primary_clinician = crud_patient.get_primary_clinician(
                    self.db, patient_id=resolved_patient_id
                )
                if primary_clinician:
                    clinician_id = primary_clinician.id
                    logger.info(f"Found primary clinician: {clinician_id}")
                else:
                    logger.error(
                        f"Could not find primary clinician for patient {resolved_patient_id}."
                    )
                    # Raise error as clinician_id is mandatory for AppointmentCreate
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Could not determine the primary clinician for this patient. Appointment cannot be created without a specified clinician.",
                    )
            # --- End Get Primary Clinician ---

            # --- Build AppointmentCreate object after resolution and validation ---
            from app.schemas.appointment import AppointmentCreate

            # The LLM must resolve appointment_type to a valid value before this point
            appointment_obj = AppointmentCreate(
                patient_id=resolved_patient_id,
                clinician_id=clinician_id,  # clinician_id should now be guaranteed to be non-None
                appointment_datetime=appointment_datetime,
                duration_minutes=duration_minutes,
                appointment_type=appointment_type,
                reason=reason,
                patient_notes=patient_notes,
                status="scheduled",
            )
            appointment = crud.appointment.create(self.db, obj_in=appointment_obj)

            # Final check: Ensure appointment object was created
            if not appointment:
                # This case should theoretically not be reached if crud.create raises exceptions on failure,
                # but adding a safeguard.
                logger.error(
                    "CRUD operation 'appointment.create' returned None without raising an exception."
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create appointment due to an unexpected issue.",
                )

            # Return success response using data from the created appointment object
            return ActionResponse(
                success=True,
                result=ActionExecutionResult.SUCCESS,
                message="Appointment created successfully",
                data={
                    "appointment_id": str(appointment.id),
                    "appointment_time": appointment.appointment_datetime.isoformat(),
                    "status": appointment.status,
                },
                action_type="appointment_create",
                resource_id=str(appointment.id),
                resource_type="appointment",
            )

        except HTTPException:
            # Re-raise HTTP exceptions to be handled by the execute_action method
            raise
        except Exception as e:
            logger.error(f"Error creating appointment: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating appointment: {str(e)}",
            )

    async def _handle_appointment_cancel(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle appointment cancellation."""
        try:
            # Extract parameters
            appointment_id = params.get("appointment_id")
            reason = params.get("reason", "Cancelled through LLM interface")

            # Validate parameters
            if not appointment_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Appointment ID is required",
                )

            # Get the appointment
            appointment = crud.appointment.get(self.db, id=appointment_id)
            if not appointment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Appointment not found",
                )

            # Check permissions
            if user_role == "patient" and appointment.patient_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You can only cancel your own appointments",
                )
            elif user_role == "clinician" and appointment.clinician_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You can only cancel appointments assigned to you",
                )

            # Update appointment status
            appointment_update = schemas.AppointmentUpdate(
                status="cancelled", cancelled_reason=reason, cancelled_by_id=user_id
            )

            # Update the appointment
            updated_appointment = crud.appointment.update(
                self.db, db_obj=appointment, obj_in=appointment_update
            )

            # Return success response
            return ActionResponse(
                success=True,
                result=ActionExecutionResult.SUCCESS,
                message="Appointment cancelled successfully",
                data={
                    "appointment_id": str(updated_appointment.id),
                    "status": updated_appointment.status,
                    "cancelled_reason": updated_appointment.cancelled_reason,
                },
                action_type="appointment_cancel",
                resource_id=str(updated_appointment.id),
                resource_type="appointment",
            )

        except HTTPException:
            # Re-raise HTTP exceptions to be handled by the execute_action method
            raise
        except Exception as e:
            logger.error(f"Error cancelling appointment: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error cancelling appointment: {str(e)}",
            )

    async def _handle_side_effect_report_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle side effect report creation via LLM."""
        import logging

        from fastapi import HTTPException, status

        from app.crud import side_effect_report
        from app.schemas.side_effect_report import SeverityLevel, SideEffectReportCreate
        from app.utils.audit import log_audit_event

        logger = logging.getLogger(__name__)
        
        # Log entry to handler
        logger.info(f"=== SIDE EFFECT HANDLER START ===")
        logger.info(f"User ID: {user_id}, Role: {user_role}")
        logger.info(f"Parameters: {params}")
        logger.info(f"Chat context: {chat_context}")

        # Validate required parameters
        medication_name = params.get("medication_name")
        symptoms = params.get("symptoms")
        severity = params.get("severity", "moderate")
        # Handle both onset_time and onset_date parameters
        onset_time = params.get("onset_time") or params.get("onset_date")
        additional_notes = params.get("additional_notes") or params.get("notes", "")

        if not medication_name:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Please specify which medication is causing side effects.",
                action_type="side_effect_report_create",
            )
        if not symptoms:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Please describe the symptoms you're experiencing.",
                action_type="side_effect_report_create",
            )

        # Map severity values
        severity_mapping = {
            "minor": SeverityLevel.MINOR,
            "mild": SeverityLevel.MINOR,
            "moderate": SeverityLevel.MODERATE,
            "major": SeverityLevel.MAJOR,
            "severe": SeverityLevel.MAJOR,
            "serious": SeverityLevel.MAJOR,
        }
        normalized_severity = severity_mapping.get(
            severity.lower() if severity else "", SeverityLevel.MODERATE
        )

        # Compose description with medication name
        full_description = f"Medication: {medication_name}\nSymptoms: {symptoms}"
        if onset_time:
            full_description += f"\n\nOnset: {onset_time}"
        if additional_notes:
            full_description += f"\n\nAdditional notes: {additional_notes}"

        # Build the SideEffectReportCreate schema with clinician_id included
        # --- Fix: Always inject clinician_id from patient record if not present ---
        clinician_id = params.get("clinician_id")
        
        # First check if patient_id is in chat context (for clinician actions)
        patient_id_from_context = None
        if chat_context:
            # Check for patient_id in chain_context or direct context
            chain_ctx = chat_context.get("chain_context", {})
            patient_id_from_context = chain_ctx.get("patient_id") or chain_ctx.get("currentPatientId")
            if not patient_id_from_context:
                # Check direct context
                patient_id_from_context = chat_context.get("patient_id") or chat_context.get("currentPatientId")
        
        # Log what we're receiving
        logger.info(f"Side effect report - params patient_id: {params.get('patient_id')}, context patient_id: {patient_id_from_context}, user_id: {user_id}")
        
        # Prefer context patient_id, then params, fallback to user_id
        patient_id = patient_id_from_context or params.get("patient_id") or user_id
        
        # If patient_id looks like a name (not a UUID), try to use context instead
        if patient_id and not patient_id.startswith(("user_", "patient_")):
            logger.info(f"Patient ID '{patient_id}' appears to be a name, using context patient_id instead")
            patient_id = patient_id_from_context or user_id
        
        # If user is a clinician creating report on behalf of patient, use their ID as clinician_id
        if user_role == "clinician":
            clinician_id = user_id
            logger.info(f"Clinician {user_id} creating side effect report for patient {patient_id}")
            # Make sure we're not using the clinician's ID as patient_id
            if patient_id == user_id:
                logger.error(f"Patient ID incorrectly set to clinician ID. Using context patient_id: {patient_id_from_context}")
                patient_id = patient_id_from_context
                if not patient_id:
                    return ActionResponse(
                        success=False,
                        result=ActionExecutionResult.INVALID_INPUT,
                        message="Unable to determine which patient this side effect report is for.",
                        action_type="side_effect_report_create",
                    )
        elif not clinician_id:
            # For patients, try to get their assigned clinician
            from app.crud import patient as crud_patient
            
            patient = None
            if patient_id:
                patient = crud_patient.get(self.db, id=patient_id)
            if patient and getattr(patient, "invited_by_clinician_id", None):
                clinician_id = patient.invited_by_clinician_id
            else:
                # Defensive: fail with clear error if clinician_id is required but missing
                logger.warning(f"Could not determine clinician_id for patient {patient_id}")
                # For now, we'll allow this to proceed without a clinician_id
                clinician_id = None

        try:
            logger.info(f"Creating SideEffectReportCreate object...")
            logger.info(f"Description: {full_description}")
            logger.info(f"Severity: {normalized_severity}")
            logger.info(f"Clinician ID: {clinician_id}")
            logger.info(f"Patient ID: {patient_id}")
            
            obj_in = SideEffectReportCreate(
                description=full_description,
                severity=normalized_severity,
                clinician_id=clinician_id,
            )
            
            logger.info(f"SideEffectReportCreate object created successfully")
            logger.info(f"Calling CRUD create method...")
            
            created_report = side_effect_report.create(
                db=self.db, obj_in=obj_in, patient_id=patient_id
            )
            
            logger.info(f"CRUD create successful, report ID: {created_report.id}")
            
        except Exception as e:
            logger.error(f"Error creating side effect report: {str(e)}", exc_info=True)
            logger.error(f"Exception type: {type(e).__name__}")
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.DATABASE_ERROR,
                message=f"Failed to create side effect report: {str(e)}",
                action_type="side_effect_report_create",
            )

        # Audit logging
        log_audit_event(
            db=self.db,
            action="LLM_SIDE_EFFECT_REPORT_CREATE",
            actor_user_id=user_id,
            actor_role=user_role,
            target_resource_type="side_effect_report",
            target_resource_id=str(created_report.id),
            details={
                "medication_name": medication_name,
                "severity": str(normalized_severity.value),
                "symptoms": symptoms,
                "onset_time": onset_time,
                "additional_notes": additional_notes,
            },
            outcome="SUCCESS",
        )

        # Friendly confirmation message
        if normalized_severity == SeverityLevel.MINOR:
            message = "I've recorded your report of minor side effects. Your healthcare team will review this information. If symptoms worsen, please update your report or contact your provider directly."
        elif normalized_severity == SeverityLevel.MODERATE:
            message = "I've recorded your report of moderate side effects. Your healthcare team will be notified. Monitor your symptoms and contact your healthcare provider if they worsen."
        else:
            message = "I've recorded your report of serious side effects. Your healthcare team will be notified immediately. Please contact your healthcare provider right away if you haven't already done so."

        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message=message,
            data={
                "report_id": str(created_report.id),
                "severity": str(created_report.severity.value),
                "status": created_report.status,
            },
            action_type="side_effect_report_create",
            resource_id=str(created_report.id),
            resource_type="side_effect_report",
        )

    async def _handle_medication_request_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle medication request creation."""
        # Extract parameters
        medication_name = params.get("medication_name")
        dosage = params.get("dosage")
        frequency = params.get("frequency")
        duration = params.get("duration")
        notes = params.get("notes")
        
        # Return success with detailed data
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Medication request created successfully",
            data={
                "medication_name": medication_name,
                "dosage": dosage,
                "frequency": frequency,
                "duration": duration,
                "notes": notes,
                "status": "pending",
                "request_id": "temp_" + str(datetime.now().timestamp())  # Temporary ID
            },
            action_type="medication_request_create",
            resource_type="medication_request",
        )

    async def _handle_medication_request_update(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle medication request update."""
        # Placeholder - would implement full logic similar to appointment cancellation
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Medication request updated (placeholder implementation)",
            action_type="medication_request_update",
            resource_type="medication_request",
        )

    async def _handle_note_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle note creation."""
        try:
            # Extract parameters
            patient_id = params.get("patient_id")
            note_content = params.get("note_content")
            note_type = params.get("note_type", "General")
            note_title = params.get("note_title", params.get("title"))
            
            # First check if patient_id is in chat context (for clinician actions)
            patient_id_from_context = None
            if chat_context:
                # Check for patient_id in chain_context or direct context
                chain_ctx = chat_context.get("chain_context", {})
                patient_id_from_context = chain_ctx.get("patient_id") or chain_ctx.get("currentPatientId")
                if not patient_id_from_context:
                    # Check direct context
                    patient_id_from_context = chat_context.get("patient_id") or chat_context.get("currentPatientId")
            
            # If patient_id looks like a name (not a UUID), prefer context
            if patient_id and not patient_id.startswith(("user_", "patient_")) and patient_id_from_context:
                logger.info(f"Patient ID '{patient_id}' appears to be a name, using context patient_id instead: {patient_id_from_context}")
                patient_id = patient_id_from_context
            elif not patient_id and patient_id_from_context:
                logger.info(f"Using patient_id from chat context: {patient_id_from_context}")
                patient_id = patient_id_from_context
            
            # Validate required parameters
            if not patient_id:
                return ActionResponse(
                    success=False,
                    result=ActionExecutionResult.INVALID_INPUT,
                    message="Patient ID is required for note creation",
                    action_type="note_create",
                    resource_type="note",
                )
            
            if not note_content:
                return ActionResponse(
                    success=False,
                    result=ActionExecutionResult.INVALID_INPUT,
                    message="Note content is required",
                    action_type="note_create",
                    resource_type="note",
                )
            
            # Get clinician record for the user
            if user_role == "clinician":
                clinician = crud.clinician.get_clinician_by_clerk_id(
                    self.db, clerk_id=user_id
                )
                if not clinician:
                    return ActionResponse(
                        success=False,
                        result=ActionExecutionResult.RESOURCE_NOT_FOUND,
                        message="Clinician profile not found",
                        action_type="note_create",
                        resource_type="note",
                    )
                clinician_id = clinician.id
            else:
                return ActionResponse(
                    success=False,
                    result=ActionExecutionResult.PERMISSION_DENIED,
                    message="Only clinicians can create notes",
                    action_type="note_create",
                    resource_type="note",
                )
            
            # Verify the clinician is authorized for this patient
            is_assigned = crud.clinician.is_patient_assigned_to_clinician(
                db=self.db, clinician_id=clinician_id, patient_id=patient_id
            )
            if not is_assigned:
                return ActionResponse(
                    success=False,
                    result=ActionExecutionResult.PERMISSION_DENIED,
                    message="Clinician not authorized to create notes for this patient",
                    action_type="note_create",
                    resource_type="note",
                )
            
            # Create the note
            # Normalize note_type to lowercase for schema validation
            normalized_note_type = note_type.lower() if note_type else "general"
            
            note_data = schemas.note.NoteCreate(
                patient_id=patient_id,
                title=note_title,
                content=note_content,
                note_type=normalized_note_type,
            )
            
            created_note = crud.note.create_note(
                db=self.db, obj_in=note_data, clinician_id=clinician_id
            )
            
            # Log the successful note creation
            log_audit_event(
                db=self.db,
                action="CREATE_NOTE",
                outcome="SUCCESS",
                actor_user_id=user_id,
                actor_role=user_role,
                target_resource_type="Note",
                target_resource_id=str(created_note.id),
                details={
                    "patient_id": patient_id,
                    "note_type": note_type,
                    "content_length": len(note_content),
                },
            )
            
            return ActionResponse(
                success=True,
                result=ActionExecutionResult.SUCCESS,
                message=f"Note created successfully for patient",
                data={
                    "note_id": str(created_note.id),
                    "patient_id": patient_id,
                    "note_type": note_type,
                    "title": note_title,
                    "content": note_content,
                    "created_at": created_note.created_at.isoformat(),
                    "clinician_id": clinician_id,
                },
                action_type="note_create",
                resource_type="note",
                resource_id=str(created_note.id),
            )
            
        except Exception as e:
            logger.error(f"Error creating note: {str(e)}", exc_info=True)
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INTERNAL_ERROR,
                message=f"Failed to create note: {str(e)}",
                action_type="note_create",
                resource_type="note",
                error_detail=str(e),
            )

    async def _handle_notification_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle notification creation."""
        # Placeholder - would implement full logic
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Notification created (placeholder implementation)",
            action_type="notification_create",
            resource_type="notification",
        )

    async def _handle_patient_search(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle patient search."""
        # Placeholder - would implement full logic
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Patient search executed (placeholder implementation)",
            action_type="patient_search",
            resource_type="search",
        )

    async def _handle_query_history(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Handle history query."""
        # Placeholder - would implement full logic
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="History query executed (placeholder implementation)",
            action_type="query_history",
            resource_type="query",
        )

    async def _handle_health_data_retrieve(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Retrieve health data for a patient (placeholder)."""

        # This is a placeholder implementation for now
        # In a real implementation, we would retrieve the requested health data from the database

        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Health data retrieved (placeholder implementation)",
            action_type="health_data_retrieve",
            resource_type="health_data",
        )

    async def _handle_appointment_request_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """
        Patient → appointment request (LLM).
        """
        import logging
        from datetime import datetime

        from dateutil import parser as dt_parse
        from dateutil import tz

        from app.crud import appointment_request as crud_appt
        from app.schemas.appointment_request import AppointmentRequestCreate

        logging.getLogger(__name__)

        # --- Validate ------------------------------------------------------------
        missing = [
            p
            for p in ("preferred_date", "preferred_time", "reason")
            if not params.get(p)
        ]
        if missing:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.MISSING_PARAMETERS,
                message=f"Missing: {', '.join(missing)}.",
                action_type="appointment_request_create",
            )

        try:
            date_iso = dt_parse.isoparse(params["preferred_date"]).date()
        except Exception:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Preferred date is not a valid ISO‑8601 date.",
                action_type="appointment_request_create",
            )

        # time handling
        time_str = params.get("preferred_time") or "09:00"
        try:
            time_iso = dt_parse.parse(time_str).time()
        except Exception:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Preferred time is not a valid time.",
                action_type="appointment_request_create",
            )

        # --- Persist ------------------------------------------------------------
        req_in = AppointmentRequestCreate(
            patient_id=user_id,
            reason=params["reason"],
            preferred_datetime=datetime.combine(date_iso, time_iso, tzinfo=tz.UTC),
            clinician_preference=params.get("clinician_preference"),
        )
        record = crud_appt.create(self.db, obj_in=req_in)

        # --- Audit --------------------------------------------------------------
        from app.utils.audit import log_audit_event

        log_audit_event(
            db=self.db,
            actor_user_id=user_id,
            actor_role=user_role,
            action="appointment_request_create",
            outcome="SUCCESS",
            target_resource_type="appointment_request",
            target_resource_id=str(record.id),
        )

        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message="Your appointment request has been submitted.",
            action_type="appointment_request_create",
            resource_id=str(record.id),
            resource_type="appointment_request",
        )

    async def _handle_weight_log_create(
        self, user_id: str, user_role: str, params: dict[str, Any], chat_context: dict = None
    ) -> ActionResponse:
        """Patient weight logging (LLM)."""
        import logging

        from app.crud import weight_log as crud_weight
        from app.schemas.weight_log import WeightLogCreate

        logging.getLogger(__name__)

        # --- Validate & normalise ----------------------------------------------
        weight_value = params.get("weight_value")
        if weight_value is None:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Please tell me your weight.",
                action_type="weight_log_create",
            )

        try:
            weight_value = float(weight_value)
            if weight_value <= 0:
                raise ValueError()
        except ValueError:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Weight must be a positive number.",
                action_type="weight_log_create",
            )

        unit = (params.get("unit") or "lb").lower()
        if unit not in ("lb", "lbs", "kg", "kilograms", "pounds"):
            unit = "lb"

        # convert to kg for storage
        weight_kg = weight_value / 2.20462 if unit.startswith("l") else weight_value
        weight_kg = round(weight_kg, 2)

        # date handling
        from dateutil import parser as dt_parse

        date_str = params.get("date")
        log_date = dt_parse.parse(date_str) if date_str else datetime.utcnow()

        # --- Write DB -----------------------------------------------------------
        # Get patient_id from params (for clinician logging) or use user_id (for patient self-logging)
        patient_id = params.get("patient_id", user_id)
        
        weight_in = WeightLogCreate(weight_kg=weight_kg, log_date=log_date)
        record = crud_weight.create_weight_log(
            self.db, obj_in=weight_in, patient_id=patient_id
        )

        # --- Audit --------------------------------------------------------------
        log_audit_event(
            db=self.db,
            actor_user_id=user_id,
            actor_role=user_role,
            action="weight_log_create",
            outcome="SUCCESS",
            target_resource_type="weight_log",
            target_resource_id=str(record.id),
        )

        # --- Response -----------------------------------------------------------
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message=f"Logged {weight_value} {unit.upper()} for {log_date.date().isoformat()}.",
            data={
                "weight_kg": weight_kg,
                "weight_value": weight_value,
                "unit": unit,
                "log_date": log_date.isoformat(),
                "patient_id": patient_id
            },
            action_type="weight_log_create",
            resource_id=str(record.id),
            resource_type="weight_log",
        )
