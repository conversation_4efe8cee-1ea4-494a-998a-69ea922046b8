"""
Common Action Patterns for the Advanced Actions System

This module defines pre-built patterns for common multi-step workflows
that users frequently request.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import re

from app.schemas.action_chain_v2 import (
    ChainedIntent,
    ChainedAction,
    ExecutionMode,
    FailureMode,
    ActionDependency
)


class ActionPatterns:
    """Collection of common action patterns."""
    
    @staticmethod
    def appointment_with_reminder(
        patient_id: str,
        scheduled_time: str,
        duration_minutes: int = 30,
        appointment_type: str = "Follow-up",
        reminder_days_before: int = 1,
        additional_notes: Optional[str] = None
    ) -> ChainedAction:
        """
        Create an appointment with an automatic reminder.
        
        Pattern: appointment_create → notification_create
        """
        # Primary action: Create appointment
        appointment_action = ChainedIntent(
            action_type="appointment_create",
            parameters={
                "patient_id": patient_id,
                "scheduled_time": scheduled_time,
                "duration_minutes": duration_minutes,
                "appointment_type": appointment_type,
                "additional_notes": additional_notes
            },
            confidence=1.0
        )
        
        # Calculate reminder time
        appointment_dt = datetime.fromisoformat(scheduled_time.replace('Z', '+00:00'))
        reminder_dt = appointment_dt - timedelta(days=reminder_days_before)
        
        # Follow-up action: Create reminder
        reminder_action = ChainedIntent(
            action_type="notification_create",
            parameters={
                "recipient_id": patient_id,
                "type": "appointment_reminder",
                "scheduled_for": reminder_dt.isoformat(),
                "title": f"Upcoming {appointment_type} Appointment",
                "message": f"You have a {appointment_type} appointment scheduled for {{{{scheduled_time}}}}. Appointment ID: {{{{appointment_id}}}}",
                "metadata": {
                    "appointment_id": "{{appointment_id}}",
                    "appointment_type": appointment_type
                }
            },
            confidence=0.95,
            dependencies=[
                ActionDependency(
                    required_action="appointment_create",
                    require_success=True
                )
            ],
            context_requirements=["appointment_id", "scheduled_time"]
        )
        
        return ChainedAction(
            primary_action=appointment_action,
            follow_up_actions=[reminder_action],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.STOP_ON_FAILURE
        )
    
    @staticmethod
    def side_effect_with_followup(
        patient_id: str,
        medication_name: str,
        symptoms: str,
        severity: str,
        onset_time: str,
        followup_days: int = 2,
        notes: Optional[str] = None
    ) -> ChainedAction:
        """
        Report a side effect and schedule a follow-up check-in.
        
        Pattern: side_effect_report_create → notification_create → appointment_request_create
        """
        # Primary action: Report side effect
        side_effect_action = ChainedIntent(
            action_type="side_effect_report_create",
            parameters={
                "patient_id": patient_id,
                "medication_name": medication_name,
                "symptoms": symptoms,
                "severity": severity,
                "onset_time": onset_time,
                "notes": notes
            },
            confidence=1.0
        )
        
        # Follow-up action 1: Immediate notification to clinician
        clinician_notification = ChainedIntent(
            action_type="notification_create",
            parameters={
                "recipient_type": "clinician",
                "type": "side_effect_alert",
                "priority": "high" if severity == "Severe" else "medium",
                "title": f"{severity} Side Effect Reported",
                "message": f"Patient {{patient_name}} reported {severity.lower()} {symptoms} from {medication_name}",
                "metadata": {
                    "side_effect_id": "{{side_effect_id}}",
                    "patient_id": patient_id,
                    "severity": severity
                }
            },
            confidence=0.95,
            dependencies=[
                ActionDependency(
                    required_action="side_effect_report_create",
                    require_success=True
                )
            ]
        )
        
        # Follow-up action 2: Schedule follow-up if severe
        followup_actions = [clinician_notification]
        
        if severity in ["Severe", "Moderate"]:
            followup_date = (datetime.now() + timedelta(days=followup_days)).date()
            
            followup_appointment = ChainedIntent(
                action_type="appointment_request_create",
                parameters={
                    "preferred_date": followup_date.isoformat(),
                    "preferred_time": "09:00",
                    "reason": f"Follow-up for {severity.lower()} side effects: {symptoms}",
                    "clinician_preference": None,
                    "metadata": {
                        "side_effect_id": "{{side_effect_id}}",
                        "auto_scheduled": True
                    }
                },
                confidence=0.9,
                dependencies=[
                    ActionDependency(
                        required_action="side_effect_report_create",
                        require_success=True
                    )
                ]
            )
            followup_actions.append(followup_appointment)
        
        return ChainedAction(
            primary_action=side_effect_action,
            follow_up_actions=followup_actions,
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def medication_request_with_education(
        patient_id: str,
        medication_name: str,
        dosage: str,
        frequency: str,
        duration: Optional[str] = None,
        notes: Optional[str] = None
    ) -> ChainedAction:
        """
        Request medication and automatically assign relevant education materials.
        
        Pattern: medication_request_create → education_assignment_create
        """
        # Primary action: Create medication request
        medication_request = ChainedIntent(
            action_type="medication_request_create",
            parameters={
                "patient_id": patient_id,
                "medication_name": medication_name,
                "dosage": dosage,
                "frequency": frequency,
                "duration": duration,
                "notes": notes
            },
            confidence=1.0
        )
        
        # Follow-up action: Assign education materials
        education_assignment = ChainedIntent(
            action_type="education_assignment_create",
            parameters={
                "patient_id": patient_id,
                "material_tags": [medication_name.lower(), "medication-guide"],
                "assignment_reason": f"Educational materials for {medication_name}",
                "due_date": (datetime.now() + timedelta(days=7)).date().isoformat(),
                "metadata": {
                    "medication_request_id": "{{medication_request_id}}",
                    "auto_assigned": True
                }
            },
            confidence=0.9,
            dependencies=[
                ActionDependency(
                    required_action="medication_request_create",
                    require_success=True
                )
            ]
        )
        
        return ChainedAction(
            primary_action=medication_request,
            follow_up_actions=[education_assignment],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def weight_log_with_milestone_check(
        weight_value: float,
        unit: str,
        date: Optional[str] = None,
        notes: Optional[str] = None,
        milestone_threshold: float = 10.0
    ) -> ChainedAction:
        """
        Log weight and check for milestone achievements.
        
        Pattern: weight_log_create → milestone_check → notification_create (conditional)
        """
        # Primary action: Log weight
        weight_log = ChainedIntent(
            action_type="weight_log_create",
            parameters={
                "weight_value": weight_value,
                "unit": unit,
                "date": date or datetime.now().date().isoformat(),
                "notes": notes
            },
            confidence=1.0
        )
        
        # Follow-up action: Check for milestone (this would be a custom action)
        milestone_check = ChainedIntent(
            action_type="milestone_check",
            parameters={
                "metric_type": "weight_loss",
                "threshold": milestone_threshold,
                "unit": unit
            },
            confidence=0.95,
            dependencies=[
                ActionDependency(
                    required_action="weight_log_create",
                    require_success=True
                )
            ]
        )
        
        # Conditional follow-up: Celebrate milestone if achieved
        milestone_notification = ChainedIntent(
            action_type="notification_create",
            parameters={
                "recipient_id": "{{patient_id}}",
                "type": "achievement",
                "title": "Milestone Achieved! 🎉",
                "message": f"Congratulations! You've reached your {{milestone_type}} milestone of {{milestone_value}} {{unit}}!",
                "metadata": {
                    "achievement_type": "weight_loss_milestone",
                    "value": "{{milestone_value}}"
                }
            },
            confidence=0.9,
            dependencies=[
                ActionDependency(
                    required_action="milestone_check",
                    require_success=True
                )
            ],
            context_requirements=["milestone_achieved", "milestone_value"]
        )
        
        return ChainedAction(
            primary_action=weight_log,
            follow_up_actions=[milestone_check, milestone_notification],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def batch_notifications(
        recipient_ids: List[str],
        notification_type: str,
        title: str,
        message: str,
        priority: str = "medium",
        metadata: Optional[Dict[str, Any]] = None
    ) -> ChainedAction:
        """
        Send the same notification to multiple recipients.
        
        Pattern: Multiple notification_create actions in parallel
        """
        # Create notification actions for each recipient
        notification_actions = []
        
        for i, recipient_id in enumerate(recipient_ids):
            notification = ChainedIntent(
                action_type="notification_create",
                parameters={
                    "recipient_id": recipient_id,
                    "type": notification_type,
                    "title": title,
                    "message": message,
                    "priority": priority,
                    "metadata": metadata or {}
                },
                confidence=0.95
            )
            
            if i == 0:
                primary_action = notification
            else:
                notification_actions.append(notification)
        
        return ChainedAction(
            primary_action=primary_action,
            follow_up_actions=notification_actions,
            execution_mode=ExecutionMode.PARALLEL,  # Send all notifications at once
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def comprehensive_check_in(
        patient_id: str,
        include_weight: bool = True,
        include_side_effects: bool = True,
        include_appointment: bool = True
    ) -> ChainedAction:
        """
        Perform a comprehensive patient check-in with multiple data points.
        
        Pattern: Multiple data collection actions that can run in parallel
        """
        actions = []
        
        # Weight check
        if include_weight:
            weight_prompt = ChainedIntent(
                action_type="prompt_create",
                parameters={
                    "patient_id": patient_id,
                    "prompt_type": "weight_log",
                    "message": "Please log your current weight",
                    "required": True
                },
                confidence=0.9
            )
            actions.append(weight_prompt)
        
        # Side effect check
        if include_side_effects:
            side_effect_prompt = ChainedIntent(
                action_type="prompt_create",
                parameters={
                    "patient_id": patient_id,
                    "prompt_type": "side_effect_check",
                    "message": "Are you experiencing any side effects from your medications?",
                    "required": True
                },
                confidence=0.9
            )
            actions.append(side_effect_prompt)
        
        # Appointment check
        if include_appointment:
            appointment_prompt = ChainedIntent(
                action_type="prompt_create",
                parameters={
                    "patient_id": patient_id,
                    "prompt_type": "appointment_need",
                    "message": "Do you need to schedule a follow-up appointment?",
                    "required": False
                },
                confidence=0.9
            )
            actions.append(appointment_prompt)
        
        if not actions:
            raise ValueError("At least one check-in component must be included")
        
        return ChainedAction(
            primary_action=actions[0],
            follow_up_actions=actions[1:] if len(actions) > 1 else [],
            execution_mode=ExecutionMode.PARALLEL,  # Ask all questions at once
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def batch_weight_logs(
        weight_entries: List[Tuple[str, float, str]]  # List of (date, value, unit)
    ) -> ChainedAction:
        """
        Log multiple weight entries at once.
        
        Pattern: Multiple weight_log_create actions
        Example: "Log my weight: Monday 185, Tuesday 184, Wednesday 183"
        """
        weight_actions = []
        
        for i, (date, value, unit) in enumerate(weight_entries):
            weight_log = ChainedIntent(
                action_type="weight_log_create",
                parameters={
                    "weight_value": value,
                    "unit": unit,
                    "date": date,
                    "notes": f"Batch entry {i+1} of {len(weight_entries)}"
                },
                confidence=0.95
            )
            
            if i == 0:
                primary_action = weight_log
            else:
                weight_actions.append(weight_log)
        
        return ChainedAction(
            primary_action=primary_action,
            follow_up_actions=weight_actions,
            execution_mode=ExecutionMode.SEQUENTIAL,  # Log in order
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
    
    @staticmethod
    def conditional_weight_appointment(
        weight_value: float,
        unit: str,
        threshold: float,
        date: Optional[str] = None
    ) -> ChainedAction:
        """
        Log weight and conditionally schedule appointment if over threshold.
        
        Pattern: weight_log_create → conditional appointment_request_create
        Example: "Log weight 205, and if over 200, schedule nutrition consult"
        """
        # Primary action: Log weight
        weight_log = ChainedIntent(
            action_type="weight_log_create",
            parameters={
                "weight_value": weight_value,
                "unit": unit,
                "date": date or datetime.now().date().isoformat(),
                "notes": f"Checking against threshold: {threshold} {unit}"
            },
            confidence=1.0
        )
        
        # Conditional follow-up: Schedule appointment if over threshold
        appointment_request = ChainedIntent(
            action_type="appointment_request_create",
            parameters={
                "preferred_date": (datetime.now() + timedelta(days=7)).date().isoformat(),
                "preferred_time": "09:00",
                "reason": f"Weight management consultation - current weight {weight_value} {unit} exceeds threshold {threshold} {unit}",
                "clinician_preference": "nutritionist",
                "metadata": {
                    "trigger": "weight_threshold_exceeded",
                    "weight_value": weight_value,
                    "threshold": threshold
                }
            },
            confidence=0.9,
            dependencies=[
                ActionDependency(
                    required_action="weight_log_create",
                    require_success=True
                )
            ]
        )
        
        # Note: In a real implementation, the condition would be evaluated by the executor
        # based on the actual logged weight vs threshold
        return ChainedAction(
            primary_action=weight_log,
            follow_up_actions=[appointment_request],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.STOP_ON_FAILURE
        )


class PatternDetector:
    """Detects common patterns in user input and suggests appropriate action chains."""
    
    PATTERN_KEYWORDS = {
        "appointment_with_reminder": [
            "schedule appointment and remind",
            "book appointment with reminder",
            "appointment and notification",
            "schedule and notify",
            "appointment and remind me",
            "appointment.*and.*notify"
        ],
        "side_effect_with_followup": [
            "report side effect and schedule",
            "side effect and follow up",
            "experiencing symptoms and need appointment",
            "medication reaction and check",
            "side effects and need",
            "nausea from medication and",
            "experiencing .* and need"
        ],
        "medication_request_with_education": [
            "prescribe and educate",
            "medication with information",
            "new prescription and guide",
            "medication and learning",
            "prescription and send",
            "medication and info",
            "request .* and send me information"
        ],
        "weight_log_with_milestone": [
            "log weight and check progress",
            "weight and milestone",
            "track weight and celebrate",
            "weight progress check",
            "log my weight .* and check",
            "weight as .* and check"
        ],
        "batch_notifications": [
            "notify all patients",
            "send to everyone",
            "broadcast message",
            "alert all clinicians",
            "send notification to all",
            "send .* to all patients"
        ],
        "batch_weight_logs": [
            "log my weight.*monday.*tuesday",
            "weight entries.*multiple days",
            "log weights for.*days",
            "weight: \\w+ \\d+.*\\w+ \\d+"
        ],
        "conditional_weight_appointment": [
            "if.*weight.*over.*schedule",
            "weight.*and if.*threshold",
            "log weight.*if over.*appointment",
            "weight.*exceeds.*book.*consult"
        ]
    }
    
    @classmethod
    def detect_pattern(cls, user_input: str) -> Optional[str]:
        """
        Detect if user input matches a known pattern.
        
        Returns the pattern name if detected, None otherwise.
        """
        import re
        
        input_lower = user_input.lower()
        
        for pattern_name, keywords in cls.PATTERN_KEYWORDS.items():
            for keyword in keywords:
                # Check if keyword contains regex pattern (has .* or other regex chars)
                if any(char in keyword for char in ['.*', '.+', '[', ']', '^', '$']):
                    # Use regex matching
                    if re.search(keyword, input_lower):
                        return pattern_name
                else:
                    # Use simple substring matching
                    if keyword in input_lower:
                        return pattern_name
        
        # Check for conditional patterns FIRST (before other patterns)
        if ("if" in input_lower or "over limit" in input_lower) and any(word in input_lower for word in ["weight"]):
            if any(word in input_lower for word in ["schedule", "appointment", "book", "consult", "nutritionist"]):
                return "conditional_weight_appointment"
        
        # Check for more complex patterns
        if "and" in input_lower:
            # Check for appointment + reminder
            if any(word in input_lower for word in ["appointment", "schedule", "book"]) and \
               any(word in input_lower for word in ["remind", "reminder", "notification"]):
                return "appointment_with_reminder"
            
            # Check for side effect + follow-up
            if any(word in input_lower for word in ["side effect", "symptom", "reaction", "nausea", "experiencing"]) and \
               any(word in input_lower for word in ["follow", "appointment", "check", "need", "help", "schedule"]):
                return "side_effect_with_followup"
            
            # Check for medication + education
            if any(word in input_lower for word in ["medication", "prescription", "prescribe", "request"]) and \
               any(word in input_lower for word in ["information", "educate", "guide", "send me"]):
                return "medication_request_with_education"
            
            # Check for weight + milestone
            if any(word in input_lower for word in ["weight", "log", "track"]) and \
               any(word in input_lower for word in ["milestone", "progress", "check", "celebrate"]):
                return "weight_log_with_milestone"
        
        # Check for batch patterns even without "and"
        if any(phrase in input_lower for phrase in ["all patients", "all clinicians", "everyone", "broadcast"]):
            return "batch_notifications"
        
        # Check for batch weight entries (multiple days/dates mentioned)
        if "weight" in input_lower:
            # Check for multiple day names
            days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            day_count = sum(1 for day in days if day in input_lower)
            if day_count >= 2:
                return "batch_weight_logs"
            
            # Check for multiple numbers (potential weight values)
            numbers = re.findall(r'\d+(?:\.\d+)?', input_lower)
            if len(numbers) >= 3 and any(word in input_lower for word in ["log", "track", "record"]):
                return "batch_weight_logs"
        
        return None