"""Chat agent implementation for processing patient and clinician messages with structured output support."""

import logging
import uuid
from typing import Any, Optional, Union

from sqlalchemy.orm import Session
from starlette.concurrency import run_in_threadpool

from app import crud
from app.schemas.chat import (
    ChatMessageCreateInternal,
)
from app.services.chatbot_manager import ChatbotManager
from app.services.message_route_handler import (
    handle_patient_routed_message,
)

# --- Context Enricher Imports ---
from app.utils.context_enricher import (
    enrich_with_rag,
    format_context_for_prompt,
    format_conversation_history,
    get_clinic_context,
    get_clinician_context,
    get_patient_context,
    validate_patient_context,
)

logger = logging.getLogger(__name__)


# --- Custom Exceptions ---
class AgentProcessingError(Exception):
    """Base exception for errors during agent processing."""

    pass


class ContextRetrievalError(AgentProcessingError):
    """Exception raised when context retrieval fails."""

    pass


class LLMConnectionError(AgentProcessingError):
    """Exception raised for issues connecting to the LLM API."""

    pass


class GuardrailViolationError(AgentProcessingError):
    """Exception raised when a response violates safety guardrails."""

    pass


# --- Chat Agent Implementation ---
async def process_chat_message(
    user_id: Union[uuid.UUID, str],
    user_message: str,
    db: Session,
    structured_output: bool = False,
    output_schema: Optional[str] = None,
    context: Optional[dict[str, Any]] = None,
) -> Union[str, dict[str, Any]]:
    """
    Process a chat message using the chatbot manager and specialized modules.
    Supports structured output if requested.

    Args:
        user_id: The UUID or clerk_id of the user sending the message
        user_message: The message content from the user
        db: The database session
        structured_output: Whether to return structured output
        output_schema: Name of the schema to use for structured output
        context: Optional dictionary with additional context information

    Returns:
        Either a string response or a dict with response and metadata
    """
    # Convert user_id to string to ensure consistent handling
    user_id_str = str(user_id)
    """
    Process a chat message using the chatbot manager and specialized modules.
    Supports structured output if requested.

    Args:
        user_id: The UUID of the user sending the message
        user_message: The message content from the user
        db: The database session
        structured_output: Whether to return structured output
        output_schema: Name of the schema to use for structured output

    Returns:
        Either a string response or a dict with response and metadata
    """
    try:
        # Initialize the ChatbotManager
        chatbot_manager = ChatbotManager()

        # Check if user role is provided in context from frontend (trust it if available)
        frontend_user_role = None
        if context and isinstance(context, dict):
            frontend_user_role = context.get("userRole") or context.get("user_role")
            
        if frontend_user_role:
            # Trust the frontend context for user role
            user_role = frontend_user_role.lower()
            logger.debug(f"Using user role from frontend context: {user_role}")
            patient = None
            clinician = None
            
            # Still try to get user objects for additional context
            if user_role == "patient":
                patient = crud.patient.get(db, id=user_id_str)
                if not patient:
                    try:
                        patient = crud.patient.get_by_clerk_id(db, clerk_id=user_id_str)
                    except Exception as e:
                        logger.debug(f"Error getting patient by clerk_id: {e}")
            elif user_role == "clinician":
                try:
                    clinician = crud.clinician.get_clinician_by_clerk_id(
                        db, clerk_id=user_id_str
                    )
                except Exception as e:
                    logger.debug(f"Error getting clinician by clerk_id: {e}")
        else:
            # Fallback to database lookup if no frontend context
            # First, try to find patient by ID (for internal IDs)
            patient = crud.patient.get(db, id=user_id_str)
            clinician = None
            user_role = "patient"

            if not patient:
                # If not found as direct patient ID, try with Clerk ID
                try:
                    patient = crud.patient.get_by_clerk_id(db, clerk_id=user_id_str)
                    logger.debug(
                        f"Found patient by clerk_id: {user_id_str}, internal ID: {patient.id if patient else None}"
                    )
                except Exception as e:
                    logger.debug(f"Error getting patient by clerk_id: {e}")

            if not patient:
                # User is not a patient, check if clinician
                try:
                    clinician = crud.clinician.get_clinician_by_clerk_id(
                        db, clerk_id=user_id_str
                    )
                    if clinician:
                        user_role = "clinician"
                        logger.debug(
                            f"Found clinician by clerk_id: {user_id_str}, internal ID: {clinician.id if clinician else None}"
                        )
                except Exception as e:
                    logger.debug(f"Error getting clinician by clerk_id: {e}")

        # Log the identified user for debugging purposes
        logger.debug(
            f"User identified as: {user_role}, patient: {patient is not None}, clinician: {clinician is not None}"
        )

        # Build context dictionary for chat modules
        backend_context = {
            "user_id": user_id_str,  # Already converted to string above
            "user_role": user_role,
            "db": db,
            "structured_output": structured_output,
            "output_schema": output_schema,
        }

        # Merge frontend context with backend context if provided
        if context is not None:
            # Log the frontend context for debugging
            logger.debug(f"Frontend context: {context}")
            # Create a new context dictionary with frontend context, but don't override backend-specific values
            merged_context = {**context, **backend_context}
            # Log the merged context keys for debugging
            logger.debug(f"Merged context keys: {list(merged_context.keys())}")
        else:
            merged_context = backend_context

        # Use the merged context
        context = merged_context

        # Extract and normalize message_route from context
        message_route = None
        if context and "message_route" in context:
            raw_route = context["message_route"]
            logger.debug(
                f"Raw message_route from context: {raw_route} (type: {type(raw_route)})"
            )

            # Convert to lowercase string regardless of input type
            if hasattr(raw_route, "value"):
                message_route = raw_route.value.lower()
            elif isinstance(raw_route, str):
                message_route = raw_route.lower()
            else:
                message_route = str(raw_route).lower()

            logger.debug(f"Normalized message_route: {message_route}")

        # Save the user's message to the database with message_route if applicable
        # CRITICAL FIX: Make sure we're using the correct patient ID from context when sending as clinician
        # Check if this is actually a clinician sending a message to a patient
        if (
            context
            and context.get("userRole") == "clinician"
            and (context.get("patient_id") or context.get("currentPatientId"))
        ):
            # This is a clinician message intended for a patient
            target_patient_id = context.get("patient_id") or context.get(
                "currentPatientId"
            )
            logger.info(
                f"Detected clinician message to patient {target_patient_id} but user_role is {user_role}"
            )

            # CRITICAL FIX: Actually save the message in the database before returning
            try:
                # Create the clinician message using the CRUD module
                user_msg_data = ChatMessageCreateInternal(
                    patient_id=str(target_patient_id),
                    sender_type="clinician",  # Force clinician as sender type
                    message_content=user_message,
                    message_route=message_route,  # Already normalized to lowercase
                )

                # Save the message to the database
                saved_message = crud.chat_message.create(db, obj_in=user_msg_data)
                logger.info(
                    f"Successfully created clinician message ID {saved_message.id} for patient {target_patient_id}"
                )

                # Check the message_route to determine if we should bypass AI processing
                route_value = None
                if message_route:
                    if hasattr(message_route, "value"):
                        route_value = message_route.value.lower()
                    elif isinstance(message_route, str):
                        route_value = message_route.lower()

                logger.info(f"Message route for clinician message: {route_value}")

                # Only bypass AI processing if this is explicitly a patient-routed message
                if route_value == "patient":
                    logger.info(
                        f"Message {saved_message.id} is explicitly routed to patient - skipping AI processing"
                    )
                    # Return early and skip AI processing
                    return {
                        "response": "Message sent to patient",
                        "action": "patient_message_direct",
                        "metadata": {
                            "module": "message_router",
                            "patient_routed": True,
                        },
                    }
                # For AI-routed messages, continue with normal processing
                logger.info(
                    f"Message {saved_message.id} is routed to AI - continuing with AI processing"
                )
            except Exception as e:
                logger.error(
                    f"Failed to create clinician message for patient {target_patient_id}: {str(e)}"
                )
        elif user_role == "patient" and patient:
            # For patients, save their message
            user_msg_data = ChatMessageCreateInternal(
                patient_id=str(patient.id),
                sender_type="patient",  # Use literal string value
                message_content=user_message,
                message_route=message_route,  # Already normalized to lowercase
            )
            saved_message = crud.chat_message.create(db, obj_in=user_msg_data)
            logger.debug(
                f"Saved patient message to database. Patient ID: {patient.id}, Message route: {message_route}"
            )
        elif user_role == "clinician" and clinician:
            # For clinicians, save their message with the target patient ID
            # First, check what patient ID fields we have in the context
            logger.info(
                f"Context keys for patient ID: {[k for k in context.keys() if 'patient' in k.lower()]}"
            )
            logger.info(f"patient_id from context: {context.get('patient_id')}")
            logger.info(
                f"currentPatientId from context: {context.get('currentPatientId')}"
            )

            # Prioritize patient_id from context if available
            target_patient_id = None
            patient_internal_uuid = None

            # CRITICAL FIX: Always resolve to patient's internal UUID for database storage
            # This resolves the issue where messages aren't persisting after refresh

            # Get the patient ID from context
            lookup_id = None
            if context.get("patient_id"):
                lookup_id = context["patient_id"]
                logger.info(f"Using patient_id from context: {lookup_id}")
            elif context.get("currentPatientId"):
                lookup_id = context["currentPatientId"]
                logger.info(f"Using currentPatientId from context: {lookup_id}")

            if lookup_id:
                # First try direct lookup as internal UUID
                try:
                    patient_by_id = crud.patient.get(db, id=lookup_id)
                    if patient_by_id:
                        # Found by internal UUID
                        patient_internal_uuid = str(patient_by_id.id)
                        logger.info(
                            f"Found patient with direct internal UUID lookup: {patient_internal_uuid}"
                        )
                    else:
                        # Try as Clerk ID
                        try:
                            patient_by_clerk = crud.patient.get_by_clerk_id(
                                db, clerk_id=lookup_id
                            )
                            if patient_by_clerk:
                                patient_internal_uuid = str(patient_by_clerk.id)
                                logger.info(
                                    f"Resolved patient_id as Clerk ID to internal UUID: {patient_internal_uuid}"
                                )
                        except Exception as e:
                            logger.error(
                                f"Error checking patient_id as Clerk ID: {str(e)}",
                                exc_info=True,
                            )
                except Exception as e:
                    logger.error(f"Error in patient lookup: {str(e)}", exc_info=True)

            # Use the resolved internal UUID, or fall back to original ID if resolution failed
            if patient_internal_uuid:
                target_patient_id = patient_internal_uuid
                logger.info(
                    f"Using resolved internal UUID for message creation: {target_patient_id}"
                )
            elif lookup_id:
                target_patient_id = lookup_id
                logger.warning(f"Using unresolved ID as fallback: {target_patient_id}")

            logger.info(
                f"Final target_patient_id for message creation: {target_patient_id}"
            )

            if target_patient_id:
                user_msg_data = ChatMessageCreateInternal(
                    patient_id=str(target_patient_id),
                    sender_type="clinician",  # Use literal string value
                    message_content=user_message,
                    message_route=message_route,  # Already normalized to lowercase
                )

                try:
                    # Detailed logging for message creation
                    logger.info(
                        f"Attempting to save clinician message to database with data: patient_id={target_patient_id}, sender_type=clinician, message_content={user_message[:20]}..., message_route={message_route}"
                    )

                    saved_message = crud.chat_message.create(db, obj_in=user_msg_data)

                    # Additional logging for created message
                    logger.info(
                        f"Successfully saved clinician message to database. Message ID: {saved_message.id}, Patient ID: {target_patient_id}, Route: {message_route}"
                    )

                    # If this is a message specifically routed to patient, return immediately
                    # without sending to AI processing
                    if (
                        message_route == "patient"
                        or context.get("message_route") == "patient"
                    ):
                        logger.info(
                            f"Message {saved_message.id} is routed to patient - skipping AI processing"
                        )
                        return {
                            "response": "Message sent to patient",
                            "action": "patient_message_direct",
                            "metadata": {
                                "module": "message_router",
                                "patient_routed": True,
                            },
                        }

                except Exception as db_error:
                    # Log database errors
                    logger.error(
                        f"Failed to save clinician message to database: {db_error}",
                        exc_info=True,
                    )
                    raise

                # Handle special routing for messages from clinician to patient
                try:
                    # Check if this message should be routed to the patient
                    route_value = None
                    if message_route:
                        # Determine the message route value in a consistent way
                        if hasattr(message_route, "value"):
                            route_value = message_route.value.lower()
                        elif isinstance(message_route, str):
                            route_value = message_route.lower()
                        else:
                            route_value = str(message_route).lower()

                    logger.debug(
                        f"Route value for message routing check: '{route_value}'"
                    )
                    if route_value == "patient":
                        # Call the patient message handling function
                        handle_result = handle_patient_routed_message(
                            db=db,
                            message_id=str(saved_message.id),
                            patient_id=str(target_patient_id),
                        )
                        if handle_result and handle_result.get("success"):
                            logger.info(
                                f"Successfully routed direct message from clinician to patient {target_patient_id}"
                            )
                            # Store the routing result in context for later processing
                            context["patient_message_routing"] = handle_result
                        else:
                            logger.warning(
                                f"Failed to route direct message from clinician to patient {target_patient_id}"
                            )
                except Exception as route_error:
                    # Don't let routing errors crash the whole request
                    logger.error(
                        f"Error during message routing: {route_error}", exc_info=True
                    )

        # Initialize clinic_context to None
        clinic_context = None

        if user_role == "patient" and patient:
            try:
                patient_context = get_patient_context(db, patient)
                context["patient_id"] = str(patient.id)  # For audit logging
                context["patient_context"] = patient_context

                # For LLM Actions - get accessible templates
                from app.services.template_validation_service import (
                    TemplateValidationService,
                )

                templates = await TemplateValidationService.validate_and_get_accessible_templates(
                    db=db, user_id=str(user_id), role=user_role
                )
                if templates:
                    context["template_id"] = str(templates[0].id)

                # Validate patient context for required fields
                validate_patient_context(patient_context)

                # Add clinic context if patient has an associated clinic
                associated_clinic_id = patient_context.get("associated_clinic_id")
                if associated_clinic_id:
                    clinic_context = get_clinic_context(db, associated_clinic_id)
                    logger.debug(
                        f"Retrieved clinic context for patient: {clinic_context}"
                    )
            except Exception as e:
                logger.error(f"Error getting patient context: {e}", exc_info=True)

        elif user_role == "clinician" and clinician:
            try:
                clinician_context = get_clinician_context(db, clinician)
                context["clinician_id"] = str(clinician.id)  # For audit logging
                context["clinician_context"] = clinician_context

                # For LLM Actions - get accessible templates
                from app.services.template_validation_service import (
                    TemplateValidationService,
                )

                templates = await TemplateValidationService.validate_and_get_accessible_templates(
                    db=db, user_id=str(user_id), role=user_role
                )
                if templates:
                    context["template_id"] = str(templates[0].id)

                # Add clinic context if clinician has associated clinics
                associated_clinic_ids = clinician_context.get(
                    "associated_clinic_ids", []
                )
                if associated_clinic_ids:
                    clinic_context = get_clinic_context(db, associated_clinic_ids[0])
                    logger.debug(
                        f"Retrieved clinic context for clinician: {clinic_context}"
                    )
            except Exception as e:
                logger.error(f"Error getting clinician context: {e}", exc_info=True)

        # Add clinic context to the overall context if available
        if clinic_context:
            context["clinic_context"] = clinic_context

        # Get conversation history if available and prepare RAG context
        try:
            # Get recent conversation history (limited to avoid exceeding context limits)
            # First get raw message history
            history = []
            if user_role == "patient":
                # Get patient chat history
                patient_messages = crud.chat_message.get_by_patient(
                    db, patient_id=user_id, limit=5
                )
                if patient_messages:
                    history = [
                        {
                            "sender": (
                                msg.sender_type.value
                                if hasattr(msg, "sender_type")
                                else "unknown"
                            ),
                            "content": (
                                msg.message_content
                                if hasattr(msg, "message_content")
                                else ""
                            ),
                            "timestamp": (
                                msg.created_at.isoformat()
                                if hasattr(msg, "created_at") and msg.created_at
                                else None
                            ),
                        }
                        for msg in patient_messages
                    ]
            elif user_role == "clinician":
                # For clinicians, we might have different types of message history
                # Use an empty list for now if no specific clinician history is available
                pass

            # Format the history for the LLM
            conversation_history = format_conversation_history(history, max_items=5)
            context["conversation_history"] = conversation_history

            # Add RAG context based on the user message if appropriate
            if len(user_message) > 10:  # Only for substantive messages
                rag_context = enrich_with_rag(
                    db=db,
                    user_id=str(user_id),
                    message=user_message,
                    user_role=user_role,
                )
                if rag_context:
                    if (
                        "rag_context_chunks" in rag_context
                        and "rag_chunks" not in rag_context
                    ):
                        rag_context["rag_chunks"] = rag_context["rag_context_chunks"]
                    # Pass the formatted context for LLM consumption
                    context["rag_context"] = format_context_for_prompt(rag_context)
                    # Also pass the metadata separately for the chatbot manager to use
                    if "rag_chunks_metadata" in rag_context:
                        context["rag_chunks_metadata"] = rag_context["rag_chunks_metadata"]

        except Exception as e:
            logger.error(
                f"Error preparing conversation history or RAG context: {e}",
                exc_info=True,
            )
            # Continue without this context rather than failing

        # Log the context keys for debugging (not content to avoid PII)
        logger.debug(f"Context keys for message processing: {list(context.keys())}")

        # Check if this message was routed to a patient and should skip AI processing
        # Check if this is a direct clinician-to-patient message (should skip AI processing)
        is_patient_routed = False
        patient_message = "Message sent to patient"

        # Only skip AI processing for explicit clinician-to-patient routing
        if context.get("patient_message_routing") and context[
            "patient_message_routing"
        ].get("patient_routed"):
            is_patient_routed = True
            patient_message = context["patient_message_routing"].get(
                "message", "Message sent to patient"
            )
            
        # If user_role is 'patient' and message_route is 'patient', this is a normal patient conversation
        # Do NOT skip AI processing - patient messages should get AI responses

        if is_patient_routed:
            logger.info("Message was routed to patient - skipping AI processing")
            # Return a direct response without AI processing
            result = {
                "response": patient_message,
                "action": "patient_message_direct",
                "metadata": {"module": "message_router", "patient_routed": True},
            }
        else:
            # Process the message using the chatbot manager
            result = await chatbot_manager.route_message(user_message, context)

            # Save the AI response to database for persistence
            try:
                logger.info(f"[AI_SAVE_DEBUG] Starting AI message save process")
                # Get target_patient_id if it exists (for clinician conversations)
                target_patient_id_value = locals().get('target_patient_id', 'N/A')
                logger.info(f"[AI_SAVE_DEBUG] user_role={user_role}, patient={patient is not None}, target_patient_id={target_patient_id_value}")
                logger.info(f"[AI_SAVE_DEBUG] context.currentPatientId={context.get('currentPatientId')}")
                logger.info(f"[AI_SAVE_DEBUG] context keys: {list(context.keys())}")
                
                # Determine patient_id for the AI response
                ai_patient_id = None
                
                # For patient conversations, always use the patient's ID
                if user_role == "patient" and patient:
                    ai_patient_id = str(patient.id)
                    logger.info(f"[AI_SAVE_DEBUG] Using patient.id for patient conversation: {ai_patient_id}")
                # For clinician conversations, prioritize current patient context
                elif context.get("currentPatientId"):
                    ai_patient_id = str(context.get("currentPatientId"))
                    logger.info(f"[AI_SAVE_DEBUG] Using context currentPatientId: {ai_patient_id}")
                elif user_role == "clinician" and 'target_patient_id' in locals() and target_patient_id:
                    # Use the target patient's ID, not the clinician's ID
                    ai_patient_id = str(target_patient_id)
                    logger.info(f"[AI_SAVE_DEBUG] Using target_patient_id: {ai_patient_id}")
                else:
                    logger.warning(f"[AI_SAVE_DEBUG] No patient_id found. user_role={user_role}, target_patient_id={'target_patient_id' in locals() and target_patient_id}, context_patient_id={context.get('currentPatientId')}")

                if ai_patient_id:
                    # Check if this is a parameter harvesting response that should NOT be saved
                    is_parameter_harvesting = False
                    if isinstance(result, dict) and result.get("metadata"):
                        metadata = result.get("metadata", {})
                        # Check for parameter harvesting indicators
                        has_missing_params = "missing_parameters" in metadata and metadata["missing_parameters"]
                        has_compound_missing_params = "compound_missing_parameters" in metadata and metadata["compound_missing_parameters"]
                        intent_result = metadata.get("intent_resolution_result", "")
                        
                        # Check for parameter-related errors that should not be saved
                        is_parameter_related_error = False
                        if isinstance(result, dict) and result.get("response"):
                            response_text = result.get("response", "")
                            # Check for specific parameter-related error patterns
                            parameter_error_patterns = [
                                "Patient name",
                                "incomplete",
                                "Provide full name",
                                "Missing required parameters",
                                "Please provide", 
                                "I need some additional information"
                            ]
                            is_parameter_related_error = any(pattern in response_text for pattern in parameter_error_patterns)
                        
                        is_parameter_harvesting = (
                            has_missing_params or 
                            has_compound_missing_params or 
                            intent_result in ["missing_parameters", "MISSING_PARAMETERS"] or
                            is_parameter_related_error
                        )
                    
                    # Save message to database only if it's not parameter harvesting
                    # Note: We removed the clinician action filtering here because we want these messages
                    # to be saved to the database, just with the correct message_route
                    should_save_to_db = not is_parameter_harvesting
                    
                    if is_parameter_harvesting:
                        logger.info(f"[AI_SAVE_DEBUG] Skipping database save for parameter harvesting/error response - this should be ephemeral")
                        logger.info(f"[AI_SAVE_DEBUG] Response text: {result.get('response', '')[:100] if isinstance(result, dict) else str(result)[:100]}...")
                        # Don't save parameter harvesting messages to chat history
                        # They should only exist in the frontend temporarily until parameters are provided
                    
                    if should_save_to_db:
                        # Determine the correct message_route for the AI response
                        if user_role == "clinician" and isinstance(result, dict) and result.get("metadata"):
                            metadata = result.get("metadata", {})
                            # If this is a successful action by a clinician, route to clinician
                            is_action_confirmation = (
                                metadata.get("action_type") is not None and 
                                metadata.get("success") is True
                            )
                            if is_action_confirmation:
                                ai_message_route = "clinician"
                                logger.info(f"[AI_SAVE_DEBUG] Routing clinician action confirmation to 'clinician' chat")
                            else:
                                ai_message_route = context.get("message_route")
                        else:
                            # Use the same message_route as the conversation context
                            ai_message_route = context.get("message_route")
                        
                        logger.info(f"[AI_SAVE_DEBUG] Setting AI response message_route to: {ai_message_route}")
                        
                        ai_msg_data = ChatMessageCreateInternal(
                            patient_id=ai_patient_id,
                            sender_type="agent",
                            message_content=result["response"] if isinstance(result, dict) else result,
                            message_route=ai_message_route,
                            message_metadata=result.get("metadata") if isinstance(result, dict) else None  # Include metadata for action responses
                        )
                        logger.info(f"[AI_SAVE_DEBUG] About to save AI message with patient_id={ai_patient_id}")
                        saved_ai_message = await run_in_threadpool(
                            crud.chat_message.create, db, obj_in=ai_msg_data
                        )
                        logger.info(f"[AI_SAVE_DEBUG] Successfully saved AI response message ID {saved_ai_message.id} for patient {ai_patient_id}")

                        # Update the result to include the real message ID
                        if isinstance(result, dict):
                            result["message_id"] = str(saved_ai_message.id)
                else:
                    logger.error(f"[AI_SAVE_DEBUG] CRITICAL: No patient_id available for AI message - cannot save to database")
            except Exception as e:
                logger.error(f"Failed to save AI response: {str(e)}", exc_info=True)

            # Check if this was an unresolved intent situation that needs fallback handling
            if (
                result.get("metadata", {}).get("intent_resolution_result")
                == "failed_extraction"
                or result.get("metadata", {}).get("intent_resolution_result")
                == "invalid_action"
            ):
                # Import here to avoid circular imports
                from app.services.message_route_handler import (
                    fallback_unresolved_intent,
                )

                # Log the fallback situation
                logger.info(
                    f"LLM intent resolution failed with result: {result.get('metadata', {}).get('intent_resolution_result')}"
                )

                # Call the fallback handler and get the fallback result
                fallback_result = fallback_unresolved_intent(
                    user_message=user_message, user_role=user_role
                )

                # Merge the fallback info into the result
                if fallback_result.get("conversation_fallback", False):
                    # Log that we're using the fallback
                    logger.info("Using conversation fallback for unresolved intent")

                    # Preserve the original result but add fallback metadata
                    result["metadata"]["conversation_fallback"] = True
                    result["metadata"][
                        "fallback_handler"
                    ] = "message_route_handler.fallback_unresolved_intent"

                    # We keep the original response that came from the LLM
                    # This avoids showing an error message to the user

        # Extract message routing information from context
        message_route = context.get("message_route")

        # Prepare message route value for audit logging
        route_value = None
        if message_route:
            # For audit logging, if it's an enum, extract the value
            route_value = (
                message_route.value
                if hasattr(message_route, "value")
                else str(message_route)
            )

        # Log the successful processing with appropriate audit format
        from app.utils.audit import log_audit_event

        log_audit_event(
            db=db,
            action="CHAT_MESSAGE_PROCESSED",
            actor_user_id=str(user_id),
            actor_role=user_role,
            target_resource_type=user_role,
            target_resource_id=str(user_id),
            outcome="SUCCESS",
            details={
                "module": result.get("metadata", {}).get("module", "unknown"),
                "action": result.get("action"),
                "message_length": len(user_message),
                "message_route": (
                    route_value if route_value else "ai"
                ),  # Default to AI if not specified
            },
        )

        # Return the full result structure to preserve metadata
        # Previously this would return just result["response"] which stripped metadata
        if result.get("metadata", {}).get("pending_compound_action"):
            logger.info("CHAT AGENT: Returning result with pending_compound_action metadata")
        return result

    except Exception as e:
        logger.error(f"Error processing chat message: {e}", exc_info=True)

        # Log the failure with appropriate audit format
        try:
            from app.utils.audit import log_audit_event

            log_audit_event(
                db=db,
                action="CHAT_MESSAGE_PROCESSED",
                actor_user_id=str(user_id),
                actor_role=user_role if "user_role" in locals() else "unknown",
                target_resource_type=(
                    user_role if "user_role" in locals() else "unknown"
                ),
                target_resource_id=str(user_id),
                outcome="FAILURE",
                details={
                    "error": str(e),
                    "message_length": len(user_message) if user_message else 0,
                },
            )
        except Exception as log_error:
            logger.error(f"Error logging audit event: {log_error}", exc_info=True)

        raise AgentProcessingError(f"Error processing message: {str(e)}")
