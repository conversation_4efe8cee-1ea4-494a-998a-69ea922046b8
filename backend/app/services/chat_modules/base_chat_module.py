from abc import ABC, abstractmethod
from typing import Any


class BaseChatModule(ABC):
    """
    Abstract base class for all chat modules in the pulsetrack chatbot infrastructure.

    All modules must implement the process_message method, which standardizes how chat messages are processed
    and ensures consistent output for seamless integration and routing.

    Integration & Extensibility:
        - This interface enables the ChatbotManager to dynamically select and invoke any compliant module
          based solely on context, without requiring changes to routing logic.
        - Modules can be added, replaced, or extended by simply inheriting from this base class.
        - Additional context keys or output fields may be added in the future without breaking existing modules,
          supporting long-term maintainability and scalability.

    Recommended Logging Practice:
        - It is strongly recommended to log the module name and key metadata whenever a message is processed.
        - This can be implemented in each module or via a shared utility.
        - Example logging snippet:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(
                "Processing message in %s | user_role=%s | context_keys=%s",
                self.__class__.__name__,
                context.get("user_role"),
                list(context.keys())
            )

    Example Usage:
        class MyModule(BaseChatModule):
            def process_message(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
                # implementation...
                return {...}

    """

    @abstractmethod
    def process_message(self, message: str, context: dict[str, Any]) -> dict[str, Any]:
        """
        Process an incoming chat message using the module’s logic.

        Args:
            message (str): The text of the incoming chat message.
            context (dict): A dictionary containing additional metadata to enable contextualized responses.
                Expected keys include (but are not limited to):
                    - user_role (str): e.g., "patient", "clinician"
                    - conversation_history (list[dict]): List of previous messages, if applicable
                    - rag_context (dict): Any RAG-enriched context (retrieved knowledge, etc.)
                    - additional keys as required by specific modules

        Returns:
            dict: A structured response with at least the following keys:
                - "response" (str): The reply text generated by the module.
                - "action" (Optional[str]): A string indicating any follow-up action (default: None).
                - "metadata" (dict): Supplementary data, e.g., {"module": "ModuleName"}.

        Notes:
            - All modules must strictly adhere to this output structure.
            - Additional output fields may be included as needed, but "response", "action", and "metadata" are required.
            - This interface supports future extension without breaking existing implementations.

        Example context structure:
            {
                "user_role": "patient",
                "conversation_history": [
                    {"sender": "patient", "message": "I feel unwell."},
                    {"sender": "bot", "message": "Can you describe your symptoms?"}
                ],
                "rag_context": {"symptom_info": "..."},
                "custom_key": "custom_value"
            }
        """
        pass
