"""
Clinician Guidelines Module using OpenAI for specialized clinical guideline queries.
Provides evidence-based information specifically for clinicians.
"""

import logging
from typing import Any

from sqlalchemy.orm import Session

from app.core.llm.base import LLMException, LLMResponse, PromptTemplate
from app.core.llm.factory import LLMFactory
from app.core.llm.providers.openai import OpenAIProvider
from app.services.chat_modules.base_chat_module import BaseChatModule
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)


class ClinicianGuidelinesModule(BaseChatModule):
    """
    Clinician Guidelines Module for healthcare provider interactions.

    Provides evidence-based guidance on clinical protocols, weight management
    best practices, and treatment guidelines exclusively for clinicians.

    This module handles specialized guideline requests with emphasis on:
    - Protocol adherence and best practices
    - Guideline-based recommendations
    - Evidence-based medicine references
    - Treatment pathway clarification
    """

    def __init__(self):
        self.system_prompt = """
        You are PulseTrack Clinical Assistant, an AI advisor specifically designed for healthcare providers in the PulseTrack platform. 
        You provide evidence-based clinical guidance, engage in professional medical discussions, and support clinical decision-making.

        ## YOUR ROLE & CAPABILITIES:
        🏥 **Clinical Consultation Partner**: Engage in thoughtful medical discussions with physicians, nurse practitioners, and other healthcare providers
        📚 **Evidence-Based Resource**: Provide current guidelines, protocols, and best practices from authoritative medical sources  
        🤔 **Clinical Reasoning Support**: Help think through complex cases, differential diagnoses, and treatment approaches
        💬 **Professional Peer**: Communicate as a knowledgeable colleague, not as a patient education tool

        ## CORE CLINICAL DOMAINS:
        • **Primary Care & Internal Medicine**: Hypertension, diabetes, cardiovascular disease, preventive care
        • **Weight Management & Metabolic Health**: Obesity treatment, metabolic syndrome, bariatric considerations
        • **Medication Management**: Drug interactions, dosing, side effects, therapeutic monitoring
        • **Care Coordination**: Referral guidelines, specialist communication, care transitions
        • **Quality Measures**: Clinical indicators, population health, quality improvement

        ## INTERACTION GUIDELINES:
        ✅ **DO:**
        - Engage in clinical reasoning and case discussion
        - Reference specific guidelines (ACC/AHA, ADA, USPSTF, etc.) with publication years
        - Present differential considerations and clinical decision trees
        - Acknowledge uncertainty and areas requiring clinical judgment
        - Support both urgent clinical questions and general medical discussions
        - Use appropriate medical terminology while remaining accessible

        ⚠️ **CLINICAL BOUNDARIES:**
        - Provide clinical guidance, not specific patient treatment recommendations
        - Emphasize that all advice requires clinical correlation and provider judgment
        - Recommend specialist referral when appropriate for complex cases
        - Acknowledge limitations of AI in clinical practice

        ## RESPONSE FORMAT:
        **For Clinical Questions**: Structure with Clinical Considerations → Guidelines/Evidence → Recommendations
        **For General Discussion**: Engage conversationally while maintaining clinical accuracy
        **For Complex Cases**: Break down differential diagnosis, risk stratification, and management options

        Remember: You're speaking with experienced healthcare providers who understand medical nuance, clinical uncertainty, and the complexity of patient care. Communicate as a trusted clinical resource and intellectual partner.
        """

        # Clinical consultation prompt template
        self.guidelines_template = PromptTemplate(
            template=(
                "{system_prompt}\n\n"
                "## CLINICAL CONSULTATION\n"
                "**Healthcare Provider Query**: {user_message}\n\n"
                "**Available Clinical Context**: {guidelines_context}\n\n"
                "**Clinical Response**:\n"
                "Provide a comprehensive, evidence-based response that addresses the clinical question while maintaining professional standards."
            ),
            input_variables=["system_prompt", "user_message", "guidelines_context"],
            use_template=True,
        )

    async def _get_llm_provider(self) -> OpenAIProvider:
        """Get configured OpenAI provider instance optimized for guidelines responses"""
        try:
            # Use LLMFactory to create provider with specific settings for clinical guidelines
            provider = LLMFactory.create_provider(
                "openai",
                model="o4-mini",  # Efficient model for clinical guidelines
                temperature=0.2,  # Lower temperature for more factual, consistent responses
                max_tokens=1500,  # Allow comprehensive clinical responses
            )
            return provider
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {str(e)}")
            raise

    async def _retrieve_relevant_guidelines(
        self, query: str, db_session: Session
    ) -> str:
        """
        Retrieve relevant guideline information based on the query.

        In a production implementation, this would use RAG with a vector DB.
        For this implementation, we'll use a simple placeholder approach.
        """
        # Placeholder - In a real implementation, this would query a vector database
        # or other knowledge store for relevant guidelines

        # Expanded clinical guidelines database for comprehensive coverage
        guidelines_data = {
            # Cardiovascular
            "hypertension": "ACC/AHA 2017 Hypertension Guidelines: Target BP <130/80 for most adults. In obesity, consider ACEI/ARB as first-line. Monitor for orthostatic changes during weight loss.",
            "cardiovascular": "ACC/AHA cardiovascular risk assessment using pooled cohort equations. Weight loss of 3-5% improves CV risk factors. Consider cardiology referral for complex cases.",
            "heart": "Heart failure management in obesity: GDMT optimization, sodium restriction <2g/day, weight monitoring. SGLT2 inhibitors beneficial for HFrEF.",
            
            # Endocrine/Diabetes
            "diabetes": "ADA 2024 Standards: A1C goal <7% for most adults. GLP-1 agonists first-line for obesity with T2DM. Monitor for diabetic ketoacidosis during rapid weight loss.",
            "a1c": "A1C targets: <7% general, <6.5% if low hypoglycemia risk, <8% if limited life expectancy. Check every 3-6 months.",
            "insulin": "Insulin adjustment during weight loss: reduce doses by 10-20% preemptively. Monitor closely for hypoglycemia. Consider deprescribing if sustained remission.",
            "metformin": "Metformin: first-line for T2DM, continue during weight loss. Check eGFR, hold if <30. Benefits for PCOS and prediabetes.",
            
            # Weight Management
            "obesity": "Clinical guidelines for obesity management: lifestyle intervention, pharmacotherapy if BMI ≥30 or ≥27 with comorbidities, bariatric surgery consideration.",
            "weight": "Evidence-based weight loss: 5-10% reduction clinically significant. Combine dietary intervention, physical activity, behavioral counseling.",
            "bariatric": "Bariatric surgery criteria: BMI ≥40 or ≥35 with significant comorbidities. Requires multidisciplinary evaluation and long-term follow-up.",
            
            # Medication Management
            "medication": "Medication safety during weight loss: adjust diabetes medications, monitor BP medications, consider drug interactions. Review all medications regularly.",
            "interaction": "Common drug interactions in primary care: warfarin/antibiotics, statins/azoles, ACE inhibitors/NSAIDs. Use interaction checkers routinely.",
            "dosing": "Medication dosing considerations: age, renal function, drug interactions. Start low, go slow for elderly patients.",
            
            # Clinical Protocols
            "protocol": "Standard clinical protocols include systematic medication review, vital signs monitoring, lab surveillance, and coordinated care planning.",
            "monitoring": "Clinical monitoring protocols: quarterly visits, annual labs (CBC, CMP, lipids, A1C), BP checks, weight tracking, medication reconciliation.",
            "follow-up": "Follow-up scheduling: new patients 2-4 weeks, stable patients quarterly, high-risk monthly. Care coordination with specialists as needed.",
            
            # Preventive Care
            "screening": "USPSTF screening recommendations: mammography, colonoscopy, cervical cancer screening, bone density, depression screening per age-appropriate guidelines.",
            "prevention": "Preventive care priorities: vaccinations, cancer screening, cardiovascular risk assessment, falls prevention, substance abuse screening.",
            
            # Mental Health
            "depression": "PHQ-9 screening annually, more frequent if risk factors. Consider collaborative care model. Monitor for medication-induced mood changes.",
            "anxiety": "Anxiety assessment using GAD-7. Consider therapy plus medication. Monitor for substance use disorders and medication interactions.",
            
            # General Clinical
            "assessment": "Comprehensive clinical assessment includes history, physical exam, medication review, social determinants, and care goals discussion.",
            "diagnosis": "Clinical decision-making process: gather data, consider differentials, apply clinical reasoning, involve patient in shared decision-making.",
            "treatment": "Evidence-based treatment approach: consider guidelines, patient preferences, resource availability, and individualized risk-benefit analysis.",
        }

        # Simple keyword matching - would be replaced with vector similarity search
        matched_guidelines = []
        query_lower = query.lower()

        for keyword, guideline in guidelines_data.items():
            if keyword in query_lower:
                matched_guidelines.append(guideline)

        if not matched_guidelines:
            return (
                "Drawing from current evidence-based medicine and clinical practice guidelines. "
                "Available for clinical discussion on primary care, internal medicine, "
                "cardiovascular health, diabetes management, weight management, medication safety, "
                "preventive care, and general clinical decision-making support."
            )

        return "\n\n".join(matched_guidelines)

    async def process_message(
        self, message: str, context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Process an incoming chat message from a clinician using OpenAI.

        Args:
            message: The text of the incoming chat message
            context: Additional metadata for contextualized response
                Expected keys include user_role, clinician_id, db

        Returns:
            Dict containing response, action, and metadata
        """
        logger.info(
            f"Processing message in ClinicianGuidelinesModule | user_role={context.get('user_role')} | context_keys={list(context.keys())}"
        )

        # Extract context values
        clinician_id = context.get("clinician_id") or context.get(
            "user_id"
        )  # Fallback to user_id if clinician_id not specified
        db_session = context.get("db")

        # Verify this is a clinician request
        user_role = context.get("user_role", "").lower()
        if user_role != "clinician":
            logger.warning(
                f"Non-clinician user attempted to use ClinicianGuidelinesModule: {user_role}"
            )
            return {
                "response": "This module is restricted to authorized healthcare "
                "providers. If you are a patient, please use the general chat interface for assistance.",
                "action": None,
                "metadata": {
                    "module": "ClinicianGuidelinesModule",
                    "error": "Unauthorized role",
                    "error_type": "UNAUTHORIZED_ROLE",
                },
            }

        try:
            # Get LLM provider
            llm_provider = await self._get_llm_provider()

            # Retrieve relevant guidelines
            if db_session:
                guidelines_context = await self._retrieve_relevant_guidelines(
                    message, db_session
                )
            else:
                logger.warning("No DB session provided, using generic guidelines")
                guidelines_context = "No specific guideline database available. Providing general information based on standard medical practices."

            # Prepare variables for prompt template
            variables = {
                "system_prompt": self.system_prompt,
                "user_message": message,
                "guidelines_context": guidelines_context,
            }

            # Log the request (be careful not to log sensitive information)
            log_data = {
                "module": "ClinicianGuidelinesModule",
                "message_length": len(message),
                "has_guidelines_context": bool(guidelines_context),
            }
            logger.info(f"Sending request to OpenAI: {log_data}")

            # Generate response using the template
            response: LLMResponse = await llm_provider.generate_with_template(
                template=self.guidelines_template, variables=variables
            )

            # Audit logging for compliance and quality monitoring
            if db_session and clinician_id:
                log_audit_event(
                    db=db_session,
                    action="GUIDELINES_REQUEST",
                    outcome="SUCCESS",
                    actor_user_id=str(clinician_id),
                    actor_role="clinician",
                    details={
                        "module": "ClinicianGuidelinesModule",
                        "token_usage": response.usage,
                        "model": response.model,
                    },
                )

            # Clean up resources
            await llm_provider.close()

            # Return standardized response format
            return {
                "response": response.content,
                "action": None,  # No special actions for guidelines responses
                "metadata": {
                    "module": "ClinicianGuidelinesModule",
                    "model": response.model,
                    "token_usage": response.usage,
                    "had_relevant_guidelines": bool(guidelines_context),
                },
            }

        except LLMException as e:
            logger.error(f"LLM error in ClinicianGuidelinesModule: {str(e)}")
            # Return a graceful fallback response
            return {
                "response": "I'm unable to provide clinical guidelines at "
                "the moment. Please try again shortly or consult the standard clinical reference materials.",
                "action": None,
                "metadata": {
                    "module": "ClinicianGuidelinesModule",
                    "error": str(e),
                    "error_type": "LLM_ERROR",
                },
            }

        except Exception as e:
            logger.error(
                f"Unexpected error in ClinicianGuidelinesModule: {str(e)}",
                exc_info=True,
            )
            return {
                "response": "I'm not able to retrieve the requested information. "
                "Please try again or consult standard reference materials if this continues.",
                "action": None,
                "metadata": {
                    "module": "ClinicianGuidelinesModule",
                    "error": str(e),
                    "error_type": "UNEXPECTED_ERROR",
                },
            }
