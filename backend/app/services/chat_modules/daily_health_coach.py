"""
Daily Health Coach module using OpenAI for patient interactions.
Provides general health guidance and support for patients, including structured output support.
"""

import logging
from typing import Any

from app.core.llm.base import LLMException, LLMResponse, PromptTemplate
from app.core.llm.factory import LLMFactory
from app.core.llm.providers.openai import OpenAIProvider
from app.services.chat_modules.base_chat_module import BaseChatModule
from app.utils.audit import log_audit_event

logger = logging.getLogger(__name__)


class DailyHealthCoach(BaseChatModule):
    """
    Daily Health Coach module for patient interactions.
    Uses OpenAI to provide supportive guidance for patients.

    This module is the default handler for patient messages and provides:
    - Supportive responses for day-to-day health management
    - Encouragement for medication adherence
    - General wellness tips
    - Referrals to clinicians for medical questions

    IMPORTANT: Does NOT provide medical advice, diagnosis, or treatment recommendations
    """

    def __init__(self):
        self.system_prompt = """
        You are a supportive health coach for patients managing weight loss treatments. You provide
        encouragement, general wellness information, and help with using the PulseTrack platform.

        IMPORTANT GUIDELINES:
        1. You may provide general educational information about medications and side effects, but always include appropriate disclaimers.
        2. NEVER provide specific medical advice, diagnosis, or treatment recommendations for individual cases.
        3. NEVER suggest specific medication dosages or changes to prescribed treatments.
        4. When patients report concerning symptoms or ask about specific medical situations, encourage them to contact their healthcare provider.
        5. Be supportive, empathetic, and positive.
        6. Keep responses concise (3-5 sentences maximum).
        7. Use simple, clear language appropriate for all health literacy levels.
        8. For app usage questions, provide helpful guidance on using the PulseTrack platform.
        9. Emphasize the importance of following the treatment plan prescribed by their clinician.

        For general educational questions about medications or side effects, you can provide factual information 
        with appropriate disclaimers like: "This is general information only. Always consult your healthcare 
        provider for personalized medical advice."

        For specific medical concerns or symptoms, encourage patients to: "Contact your clinician through 
        the PulseTrack messaging feature or call their office directly for personalized medical guidance."

        Always provide clear, helpful answers in your own words. Address the patient directly and
        personalize your responses when appropriate.
        """

        # Various prompt templates for different scenarios
        self.prompt_templates = {
            "general": PromptTemplate(
                template=(
                    "{system_prompt}\n\n"
                    "Patient message: {user_message}\n\n"
                    "Patient context: {patient_context}\n\n"
                    "Recent conversation history: {conversation_history}\n\n"
                    "Your response:"
                ),
                input_variables=[
                    "system_prompt",
                    "user_message",
                    "patient_context",
                    "conversation_history",
                ],
                use_template=True,
            ),
            "medication_reminder": PromptTemplate(
                template=(
                    "{system_prompt}\n\n"
                    "The patient may need support with medication adherence.\n\n"
                    "Patient message: {user_message}\n\n"
                    "Patient context: {patient_context}\n\n"
                    "Recent conversation history: {conversation_history}\n\n"
                    "Your response:"
                ),
                input_variables=[
                    "system_prompt",
                    "user_message",
                    "patient_context",
                    "conversation_history",
                ],
                use_template=True,
            ),
            "side_effect": PromptTemplate(
                template=(
                    "{system_prompt}\n\n"
                    "The patient may be asking about side effects or reporting symptoms. You can provide general educational information about common side effects, but if they're reporting specific concerning symptoms, encourage them to contact their healthcare provider.\n\n"
                    "Patient message: {user_message}\n\n"
                    "Patient context: {patient_context}\n\n"
                    "Recent conversation history: {conversation_history}\n\n"
                    "Your response:"
                ),
                input_variables=[
                    "system_prompt",
                    "user_message",
                    "patient_context",
                    "conversation_history",
                ],
                use_template=True,
            ),
        }

        # Structured prompt templates for structured output
        self.structured_prompt_templates = {
            "medication_reminder": PromptTemplate(
                template=(
                    "{system_prompt}\n\n"
                    "The patient may need support with medication adherence.\n\n"
                    "Patient message: {user_message}\n\n"
                    "Patient context: {patient_context}\n\n"
                    "Recent conversation history: {conversation_history}\n\n"
                    "IMPORTANT: Your response should include both a conversational message AND structured data in JSON format.\n"
                    "Provide the structured data as valid JSON in a code block.\n\n"
                    "Structured data should follow this format and be valid for the MedicationReminderSchema:\n"
                    "```json\n"
                    "{\n"
                    '  "medication_name": "string or null",\n'
                    '  "dosage": "string or null",\n'
                    '  "frequency": "string or null",\n'
                    '  "importance": "string explaining why taking this medication is important",\n'
                    '  "next_steps": ["array", "of", "action", "steps"]\n'
                    "}\n"
                    "```\n\n"
                    "First provide your conversational response, followed by the structured JSON data in a code block."
                ),
                input_variables=[
                    "system_prompt",
                    "user_message",
                    "patient_context",
                    "conversation_history",
                ],
                use_template=True,
            ),
            "side_effect_report": PromptTemplate(
                template=(
                    "{system_prompt}\n\n"
                    "The patient may be asking about side effects or reporting symptoms. You can provide general educational information, but if they're reporting specific concerning symptoms, encourage them to contact their healthcare provider and consider using the side effect reporting feature.\n\n"
                    "Patient message: {user_message}\n\n"
                    "Patient context: {patient_context}\n\n"
                    "Recent conversation history: {conversation_history}\n\n"
                    "IMPORTANT: Your response should include both a conversational message AND structured data in JSON format.\n"
                    "Provide the structured data as valid JSON in a code block.\n\n"
                    "Structured data should follow this format and be valid for the SideEffectReportSchema:\n"
                    "```json\n"
                    "{\n"
                    '  "reported_symptoms": ["array", "of", "symptoms"],\n'
                    '  "severity_level": "low|medium|high",\n'
                    '  "recommendation": "string with clear recommendation",\n'
                    '  "requires_immediate_attention": true|false\n'
                    "}\n"
                    "```\n\n"
                    "First provide your conversational response, followed by the structured JSON data in a code block."
                ),
                input_variables=[
                    "system_prompt",
                    "user_message",
                    "patient_context",
                    "conversation_history",
                ],
                use_template=True,
            ),
        }

    async def _get_llm_provider(self) -> OpenAIProvider:
        """Get configured OpenAI provider instance"""
        try:
            # Use LLMFactory to create provider with default settings
            provider = LLMFactory.create_provider(
                "openai",
                model="o4-mini",  # Consider using config for this
                temperature=0.7,
                max_tokens=500,  # Limit response length
            )
            return provider
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {str(e)}")
            raise

    def _determine_prompt_type(self, message: str) -> str:
        """Determine the appropriate prompt type based on message content"""
        message_lower = message.lower()

        # Check for medication-related messages
        if any(
            term in message_lower
            for term in [
                "medication",
                "pill",
                "dose",
                "forgot",
                "take",
                "taking",
                "missed",
            ]
        ):
            return "medication_reminder"

        # Check for side effect questions and reports
        if any(
            term in message_lower
            for term in [
                "side effect",
                "side effects",
                "adverse reaction",
                "experiencing",
                "having symptoms",
                "feel really sick",
                "severe nausea",
                "bad reaction",
                "symptoms from",
                "what are the side effects",
                "side effects of",
                "effects of",
                "reactions to",
            ]
        ):
            return "side_effect"

        # Default prompt type
        return "general"

    def _format_conversation_history(self, history: list) -> str:
        """Format conversation history for the prompt"""
        if not history:
            return "No previous conversation history."

        formatted_history = []
        forbidden_placeholders = {"{assistant_response}", "{response}"}
        for entry in history[-5:]:  # Last 5 messages only
            # Handle cases where entry might be a string or a dictionary
            if isinstance(entry, dict):
                role = entry.get("sender_type", "unknown").capitalize()
                content = entry.get("message_content", "")
            elif isinstance(entry, str):
                # For string entries, assume it's the content with unknown role
                role = "Unknown"
                content = entry
            else:
                # Skip entries that are neither strings nor dictionaries
                logger.warning(
                    f"Skipping conversation history entry of unexpected type: {type(entry)}"
                )
                continue

            # If content is a forbidden placeholder or empty, handle accordingly
            if not content or content.strip() in forbidden_placeholders:
                if role.lower() in {"assistant", "agent", "coach", "ai"}:
                    formatted_history.append(f"{role}: [No previous response]")
                else:
                    # For user or unknown, skip empty/placeholder messages
                    continue
            else:
                formatted_history.append(f"{role}: {content}")

        return "\n".join(formatted_history)

    def _format_patient_context(self, context: dict[str, Any]) -> str:
        """Format patient context for the prompt"""
        logger.info(
            f"Formatting patient context with keys: {list(context.keys() if context else [])}"
        )
        if not context:
            return "No specific patient context available."

        # Select only relevant, non-sensitive information
        relevant_fields = [
            "first_name",
            "treatment_start_date",
            "last_login",
            "medication_adherence_rate",
            "recent_reported_side_effects",
        ]

        formatted_context = []
        for field in relevant_fields:
            if field in context and context[field]:
                formatted_context.append(
                    f"{field.replace('_', ' ').capitalize()}: {context[field]}"
                )

        return (
            "\n".join(formatted_context)
            if formatted_context
            else "Basic patient record available but no specific context to include."
        )

    async def process_message(
        self, message: str, context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Process an incoming chat message using OpenAI.

        Args:
            message: The text of the incoming chat message
            context: Additional metadata for contextualized response
                Expected keys include user_role, patient_id, db, conversation_history

        Returns:
            Dict containing response, action, and metadata
        """
        logger.info(
            f"Processing message in DailyHealthCoach | context_keys={list(context.keys())}"
        )

        # Extract context values
        patient_id = context.get("patient_id")
        db_session = context.get("db")
        conversation_history = context.get("conversation_history", [])
        patient_context = context.get("patient_context", {})

        clinician_context = context.get(
            "clinician_context", {}
        )  # Extract clinician context

        # Determine the appropriate prompt type based on message content
        prompt_type = self._determine_prompt_type(message)
        logger.info(f"Selected prompt type: {prompt_type}")

        # Structured output support
        structured_output = context.get("structured_output", False)
        output_schema = context.get("output_schema")

        try:
            # Get LLM provider
            llm_provider = await self._get_llm_provider()

            # Format conversation history
            formatted_history = self._format_conversation_history(conversation_history)

            # Format patient and clinician context separately
            formatted_patient_str = self._format_patient_context(patient_context)

            # Format clinician context with more details
            formatted_clinician_str = "No clinician context available."
            if clinician_context:
                title = clinician_context.get("title", "Dr.")  # Default title
                first_name = clinician_context.get("first_name", "")
                last_name = clinician_context.get("last_name", "")
                specialty = clinician_context.get(
                    "specialty", "Healthcare Provider"
                )  # Default specialty
                email = clinician_context.get("email")  # Get email

                name_parts = [title, first_name, last_name]
                full_name = " ".join(
                    part for part in name_parts if part and part.strip()
                )  # Join non-empty parts, strip whitespace

                if (
                    full_name and full_name != title
                ):  # Check if we have more than just the default title
                    formatted_clinician_str = f"Clinician: {full_name}"
                    if (
                        specialty and specialty != "Healthcare Provider"
                    ):  # Add specialty if available and not the default
                        formatted_clinician_str += f", {specialty}"
                    if email:  # Add email if available
                        formatted_clinician_str += f" (Email: {email})"
                elif (
                    specialty
                ):  # Fallback if only specialty is known (or name parts were empty/default title)
                    formatted_clinician_str = f"Clinician: {specialty}"
                    if email:  # Add email if available
                        formatted_clinician_str += f" (Email: {email})"
                elif email:  # Fallback if only email is known
                    formatted_clinician_str = f"Clinician contact: {email}"
            # If nothing useful is found, it remains "No clinician context available."

            # Combine formatted strings for the prompt
            combined_context_str = f"{formatted_patient_str}\n{formatted_clinician_str}"
            logger.info(f"Combined context being sent to LLM: {combined_context_str}")

            # Prepare variables for prompt template
            variables = {
                "system_prompt": self.system_prompt,
                "user_message": message,
                "conversation_history": formatted_history,
                "patient_context": combined_context_str,  # Use the combined string
            }

            # Ensure all variables are strings
            for key, value in variables.items():
                if not isinstance(value, str):
                    variables[key] = str(value)

            # Log the actual variable values for debugging
            var_preview = {}
            for k, v in variables.items():
                if isinstance(v, str) and len(v) > 30:
                    var_preview[k] = v[:30] + "..."
                else:
                    var_preview[k] = v
            logger.debug(f"Actual template variable values: {var_preview}")

            # Select appropriate template based on prompt type and structured output
            if structured_output and output_schema:
                if output_schema in self.structured_prompt_templates:
                    template = self.structured_prompt_templates[output_schema]
                    logger.info(f"Using structured template for {output_schema}")
                else:
                    template = self.prompt_templates[prompt_type]
                    logger.warning(
                        f"Requested schema {output_schema} not found, using standard template"
                    )
            else:
                template = self.prompt_templates[prompt_type]

            # Log the request (be careful not to log sensitive information)
            log_data = {
                "module": "DailyHealthCoach",
                "prompt_type": prompt_type,
                "message_length": len(message),
                "conversation_history_length": len(conversation_history),
                "structured_output_requested": structured_output,
                "output_schema": output_schema if structured_output else None,
            }
            logger.info(f"Sending request to OpenAI: {log_data}")

            # Ensure all required variables are present for the template
            for var in template.input_variables:
                if var not in variables:
                    variables[var] = ""

            # --- BEGIN DIAGNOSTIC LOGGING ---
            # Log all variables passed to the template
            logger.info(
                "Prompt variables for LLM formatting: %s",
                {k: (v if v is not None else "<None>") for k, v in variables.items()},
            )
            # Attempt to log the fully formatted prompt string
            formatted_prompt = None
            try:
                if getattr(template, "use_template", False):
                    formatted_prompt = template.template.format(**variables)
                else:
                    formatted_prompt = str(template.template)
                logger.info(
                    "Formatted prompt string (before LLM call):\n%s", formatted_prompt
                )
            except Exception as e:
                logger.error(
                    "Error formatting prompt string for diagnostics: %s",
                    str(e),
                    exc_info=True,
                )
            # --- END DIAGNOSTIC LOGGING ---

            # Generate response using the template
            response: LLMResponse = await llm_provider.generate_with_template(
                template=template, variables=variables
            )
            # --- BEGIN ADDED LOG ---
            logger.info(
                f"Raw LLMResponse received: content='{response.content}', model='{response.model}', usage={response.usage}"
            )
            # --- END ADDED LOG ---

            # Audit logging for compliance and quality monitoring
            if db_session and patient_id:
                log_audit_event(
                    db=db_session,
                    action="CHATBOT_RESPONSE",
                    outcome="SUCCESS",
                    actor_user_id=str(patient_id),
                    actor_role="patient",
                    details={
                        "module": "DailyHealthCoach",
                        "prompt_type": prompt_type,
                        "token_usage": response.usage,
                        "model": response.model,
                    },
                )

            # Determine if any follow-up action is needed
            action = None
            if prompt_type == "side_effect":
                action = "SUGGEST_SIDE_EFFECT_REPORT"

            # Clean up resources
            await llm_provider.close()

            # Return standardized response format
            return {
                "response": response.content,
                "action": action,
                "metadata": {
                    "module": "DailyHealthCoach",
                    "model": response.model,
                    "prompt_type": prompt_type,
                    "token_usage": response.usage,
                    "structured_output_requested": structured_output,
                    "output_schema": output_schema if structured_output else None,
                },
            }

        except LLMException as e:
            logger.error(f"LLM error in DailyHealthCoach: {str(e)}")
            # Return a graceful fallback response
            return {
                "response": "I'm sorry, I'm having trouble connecting to my knowledge system right now. "
                "Please try again in a moment or contact support if this continues.",
                "action": None,
                "metadata": {
                    "module": "DailyHealthCoach",
                    "error": str(e),
                    "error_type": "LLM_ERROR",
                },
            }

        except Exception as e:
            logger.error(
                f"Unexpected error in DailyHealthCoach: {str(e)}", exc_info=True
            )
            return {
                "response": "I apologize, but I encountered an unexpected error. "
                "Please try again or contact support if this continues.",
                "action": None,
                "metadata": {
                    "module": "DailyHealthCoach",
                    "error": str(e),
                    "error_type": "UNEXPECTED_ERROR",
                },
            }
