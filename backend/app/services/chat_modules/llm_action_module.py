"""LLM Action Module for processing command-like messages as structured API actions."""

import json
import logging
from typing import Any

from sqlalchemy.orm import Session

from app.services.action_executor_service import ActionExecutorService
from app.services.chat_modules.base_chat_module import BaseChatModule
from app.services.intent_resolver_service import (  # Added ResolvedIntent
    IntentParameterValue,
    IntentResolverService,
    ResolvedIntent,
)
from app.services.template_validation_service import TemplateValidationService
from app.utils.parameter_persistence import get_parameter_manager

logger = logging.getLogger(__name__)


class LLMActionModule(BaseChatModule):
    """
    Module for processing natural language commands as LLM-driven API actions.

    This module detects when a chat message appears to be a command or action request
    and processes it through the Intent Resolution and Action Execution services.

    Examples of commands this module handles:
    - "Schedule an appointment with <PERSON><PERSON> <PERSON> tomorrow at 2pm"
    - "Cancel my appointment on Friday"
    - "Create a note about the patient's progress"
    """

    def __init__(self):
        # Command prefixes that trigger this module
        self.command_prefixes = [
            "schedule",
            "book",
            "create",
            "add",
            "make",
            "set up",
            "cancel",
            "delete",
            "remove",
            "update",
            "change",
            "modify",
            "get",
            "show",
            "list",
            "find",
            "search",
            "query",
        ]

    def should_handle_message(self, message: str) -> bool:
        """Determine if this module should handle the message based on content."""
        message_lower = message.lower()

        # Check if message starts with a command prefix (strict check)
        if any(message_lower.startswith(prefix) for prefix in self.command_prefixes):
            return True

        # For more complex commands that might not start with a command word
        appointment_related = [
            "appointment",
            "schedule",
            "meeting",
            "session",
            "consultation",
        ]
        date_time_indicators = [
            "tomorrow",
            "next week",
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
            " am",
            " pm",
            ":00",
            "morning",
            "afternoon",
            "evening",
        ]

        # Check for appointment scheduling patterns
        has_appointment_word = any(
            word in message_lower for word in appointment_related
        )
        has_time_indicator = any(
            indicator in message_lower for indicator in date_time_indicators
        )

        # If message contains both appointment words and time indicators, it's likely an appointment request
        if has_appointment_word and has_time_indicator:
            return True

        return False

    def _get_supported_actions_for_role(self, user_role: str) -> list:
        """
        Return the list of supported actions for the given user role.
        Only patient-appropriate actions are returned for patients.
        """
        # All possible actions, from ActionExecutorService.ACTION_HANDLERS
        all_actions = [
            "appointment_create",
            "appointment_cancel",
            "medication_request_create",
            "medication_request_update",
            "side_effect_report_create",
            "health_data_retrieve",
            "query_history",
            # The following are clinician/admin only:
            # "note_create", "notification_create", "patient_search"
        ]
        if user_role == "patient":
            return [
                "appointment_create",
                "appointment_cancel",
                "medication_request_create",
                "medication_request_update",
                "side_effect_report_create",
                "health_data_retrieve",
                "query_history",
            ]
        # Default: return all for clinicians
        return all_actions

    async def process_message(
        self, message: str, context: dict[str, Any]
    ) -> dict[str, Any]:
        logger.info(
            f"Processing potential command in LLMActionModule: {message[:50]}..."
        )
        
        # Debug logging for appointment parameter completion
        if context.get("is_parameter_completion") and context.get("original_action_type") == "appointment_create":
            logger.info("APPOINTMENT DEBUG: Parameter completion detected in LLMActionModule")
            logger.info(f"APPOINTMENT DEBUG: completed_parameters = {context.get('completed_parameters')}")
            logger.info(f"APPOINTMENT DEBUG: Full context keys: {list(context.keys())}")

        # Extract context values
        user_id = context.get("user_id")
        user_role = context.get("user_role")
        db: Session = context.get("db")

        # Default template_id - in a real implementation, this would be configurable or
        # retrieved based on user preferences or context
        template_id = context.get("template_id")

        # Check for pending compound action
        pending_compound_action = context.get("pending_compound_action")
        if pending_compound_action:
            logger.info("Found pending compound action in context - continuing compound action")
            # Extract the stored chain and missing parameter info
            chain_data = pending_compound_action.get("chain")
            failed_action = pending_compound_action.get("failed_action")
            missing_params_info = pending_compound_action.get("missing_params")
            
            # For now, let's add the user's response as the missing parameter
            # In a real implementation, we'd need to parse which parameter they're providing
            if failed_action == "side_effect_report_create" and "medication_name" in missing_params_info:
                # User is providing the medication name
                from app.schemas.action_chain_v2 import ChainedAction
                
                # Reconstruct the chain from the stored data
                if chain_data:
                    # Update the parameters with the user's response
                    chain_data["primary_action"]["parameters"]["medication_name"] = message.strip()
                    
                    # Continue execution with the updated chain
                    chain = ChainedAction.model_validate(chain_data)
                    
                    from app.services.action_chain_executor_service import ActionChainExecutorService
                    
                    chain_executor = ActionChainExecutorService(db)
                    chain_result = await chain_executor.execute_chain(
                        user_id=user_id,
                        user_role=user_role,
                        chain=chain,
                        initial_context=context
                    )
                    
                    # Format response
                    if chain_result.success:
                        action_summaries = []
                        for result in chain_result.results:
                            if result.success:
                                action_summaries.append(f"✓ {result.action_type}: {result.message}")
                            else:
                                action_summaries.append(f"✗ {result.action_type}: {result.error_message}")
                        
                        response_message = f"Completed {len([r for r in chain_result.results if r.success])} of {len(chain_result.results)} actions:\n" + "\n".join(action_summaries)
                    else:
                        response_message = f"I couldn't complete your request: {chain_result.error_message or 'Unknown error'}"
                    
                    return {
                        "response": response_message,
                        "action": {
                            "type": "compound",
                            "chain_id": chain_result.chain_id,
                            "results": [
                                {
                                    "action_type": r.action_type,
                                    "success": r.success,
                                    "message": r.message if r.success else r.error_message,
                                    "data": r.data
                                }
                                for r in chain_result.results
                            ]
                        },
                        "metadata": {
                            "module": "LLMActionModule",
                            "action_type": "compound",  # Add this for frontend detection
                            "success": chain_result.success,
                            "data": {
                                "chain_id": chain_result.chain_id,
                                "results": [
                                    {
                                        "action_type": r.action_type,
                                        "success": r.success,
                                        "message": r.message if r.success else r.error_message,
                                        "data": r.data
                                    }
                                    for r in chain_result.results
                                ]
                            },
                            "message": response_message,
                            "compound_action": True,
                            "resumed_from_pending": True,
                            "execution_time_ms": chain_result.execution_time_ms
                        }
                    }

        # Initialize parameter persistence manager
        param_manager = get_parameter_manager()
        
        # Check if this is a compound parameter completion
        if context.get("is_compound_parameter_completion") and context.get("chain_id"):
            chain_id = context.get("chain_id")
            completed_params = context.get("completed_compound_parameters", {})
            logger.info(f"Handling compound parameter completion for chain: {chain_id}")
            
            # Get saved compound state
            saved_compound = param_manager.get_compound_parameters(user_id, chain_id)
            if saved_compound and saved_compound.get("raw_llm_response"):
                try:
                    # Reconstruct the compound action structure
                    raw_response = saved_compound["raw_llm_response"]
                    
                    # Create a new resolved result with completed parameters
                    from app.schemas.action_chain_v2 import ChainedAction, ChainedIntent, ExecutionMode
                    
                    # Apply context enrichment to compound action parameters
                    patient_id_from_context = context.get("patient_id") or context.get("currentPatientId")
                    
                    # Helper function to enrich parameters with context
                    async def enrich_params_with_context(params: dict, action_type: str) -> dict:
                        updated_params = params.copy()
                        
                        # Only auto-fill patient_id for patient users (this is safe and expected)
                        if "patient_id" not in updated_params or updated_params.get("patient_id") is None:
                            if context.get("user_role") == "patient":
                                updated_params["patient_id"] = user_id
                                logger.info(f"Auto-filled patient_id={user_id} for patient user")
                            elif patient_id_from_context and context.get("user_role") == "clinician":
                                updated_params["patient_id"] = patient_id_from_context
                                logger.info(f"Auto-filled patient_id={patient_id_from_context} from context for clinician")
                        elif "patient_id" in updated_params:
                            # Check if patient_id looks like a name (first name or full name) - replace only for clinicians with context
                            patient_id_value = updated_params["patient_id"]
                            if isinstance(patient_id_value, str) and not patient_id_value.startswith(("user_", "patient_")):
                                # Replace any name-like string that's not already a proper ID
                                if patient_id_from_context and context.get("user_role") == "clinician":
                                    logger.info(f"Replacing patient name '{patient_id_value}' with context patient_id={patient_id_from_context}")
                                    updated_params["patient_id"] = patient_id_from_context
                        
                        return updated_params
                    
                    # Build primary action with completed parameters and apply enrichment
                    primary_params = completed_params.get(f"{raw_response['primary_action']['action_type']}_0", {})
                    enriched_primary_params = await enrich_params_with_context(
                        primary_params,
                        raw_response["primary_action"]["action_type"]
                    )
                    primary_intent = ChainedIntent(
                        action_type=raw_response["primary_action"]["action_type"],
                        parameters=enriched_primary_params
                    )
                    
                    # Build follow-up actions with completed parameters and apply enrichment
                    follow_up_intents = []
                    for idx, follow_up in enumerate(raw_response.get("follow_up_actions", [])):
                        follow_up_params = completed_params.get(f"{follow_up['action_type']}_{idx + 1}", {})
                        enriched_follow_up_params = await enrich_params_with_context(
                            follow_up_params,
                            follow_up["action_type"]
                        )
                        follow_up_intent = ChainedIntent(
                            action_type=follow_up["action_type"],
                            parameters=enriched_follow_up_params
                        )
                        follow_up_intents.append(follow_up_intent)
                    
                    # Create the chain
                    chain = ChainedAction(
                        primary_action=primary_intent,
                        follow_up_actions=follow_up_intents,
                        execution_mode=ExecutionMode(raw_response.get("execution_mode", "sequential"))
                    )
                    
                    # Execute the chain
                    from app.services.action_chain_executor_service import ActionChainExecutorService
                    executor = ActionChainExecutorService(db)
                    chain_result = await executor.execute_chain(
                        user_id=user_id,
                        user_role=user_role,
                        chain=chain,
                        initial_context=context
                    )
                    
                    # Format response based on execution results
                    if chain_result.success:
                        action_summaries = []
                        for result in chain_result.results:
                            if result.success:
                                action_summaries.append(f"✓ {result.action_type}: {result.message}")
                            else:
                                action_summaries.append(f"✗ {result.action_type}: {result.error_message}")
                        
                        response_message = f"Completed {len([r for r in chain_result.results if r.success])} of {len(chain_result.results)} actions:\n" + "\n".join(action_summaries)
                    else:
                        response_message = f"I encountered an issue while processing your actions: {chain_result.error_message}"
                    
                    # Clear the saved compound state after execution
                    param_manager.clear_compound_parameters(user_id, chain_id)
                    
                    return {
                        "response": response_message,
                        "action": None,
                        "metadata": {
                            "module": "LLMActionModule",
                            "compound_action": True,
                            "action_type": "compound_action",
                            "success": chain_result.success,
                            "data": {
                                "chain_id": chain_id,
                                "results": [
                                    {
                                        "action_type": r.action_type,
                                        "success": r.success,
                                        "message": r.message if r.success else r.error_message,
                                        "data": r.data
                                    }
                                    for r in chain_result.results
                                ]
                            },
                            "execution_time_ms": chain_result.execution_time_ms
                        }
                    }
                    
                except Exception as e:
                    logger.error(f"Error executing compound parameter completion: {e}", exc_info=True)
                    return {
                        "response": f"I encountered an error while processing your actions: {str(e)}",
                        "action": None,
                        "metadata": {
                            "module": "LLMActionModule",
                            "error": str(e),
                            "error_type": "COMPOUND_EXECUTION_ERROR"
                        }
                    }
        
        # Get previous parameters and intent action type from persistence or context
        saved_state = param_manager.get_saved_parameters(user_id)
        
        if saved_state:
            # Use saved parameters from persistence
            previous_parameters = saved_state.get("parameters", {})
            current_intent_action_type = saved_state.get("action_type")
            logger.info(f"Loaded saved parameters: {previous_parameters}")
            logger.info(f"Loaded saved action type: {current_intent_action_type}")
        else:
            # Fall back to context (for backward compatibility)
            previous_parameters = context.get(
                "previous_parameters", {}
            )  # Expect dict {"param_name": value}
            current_intent_action_type = context.get(
                "current_intent_action_type"
            )  # Track the action being filled
            logger.info(f"Previous parameters from context: {previous_parameters}")
            logger.info(
                f"Current intent action type from context: {current_intent_action_type}"
            )

        # Check for required context
        if not all([user_id, user_role, db]):
            logger.error(
                "Missing required context (user_id, user_role, or db) in LLMActionModule"
            )
            return {
                "response": "I couldn't process your command due to missing required context information.",
                "action": None,
                "metadata": {
                    "module": "LLMActionModule",
                    "error": "Missing required context",
                    "error_type": "CONTEXT_ERROR",
                },
            }

        try:
            # If no template_id is provided, find an appropriate template for the user
            if not template_id:
                # Get accessible templates
                templates = await TemplateValidationService.validate_and_get_accessible_templates(
                    db=db, user_id=user_id, role=user_role
                )

                if not templates:
                    return {
                        "response": "I couldn't find any action templates you have access to.",
                        "action": None,
                        "metadata": {
                            "module": "LLMActionModule",
                            "error": "No templates found",
                            "error_type": "TEMPLATE_ERROR",
                        },
                    }

                # Use the first template (in a real implementation, this would be more sophisticated)
                template_id = str(templates[0].id)

            # Initialize the intent resolver
            intent_resolver = IntentResolverService()

            # Resolve intent using the service
            logger.info("Calling IntentResolverService.process_user_input...")
            # Call the original intent resolver (without conversation history args)
            resolved_result = await intent_resolver.process_user_input(
                db=db,
                user_id=context.get("user_id"),
                user_role=context.get("user_role"),
                template_id=template_id,
                user_input=message,
                client_timezone_offset=context.get("client_timezone_offset"),
            )
            logger.info(f"Intent resolution result (initial): {resolved_result}")

            # Check if this is a compound action
            from app.schemas.action_chain_v2 import ChainedAction
            if isinstance(resolved_result, ChainedAction):
                logger.info("Detected compound action - executing action chain")
                
                # Apply context enrichment to compound action parameters
                patient_id_from_context = context.get("patient_id") or context.get("currentPatientId")
                
                # Helper function to enrich parameters with context
                async def enrich_params_with_context(params: dict, action_type: str) -> dict:
                    updated_params = params.copy()
                    
                    # Only auto-fill patient_id for patient users (this is safe and expected)
                    if "patient_id" not in updated_params or updated_params.get("patient_id") is None:
                        if context.get("user_role") == "patient":
                            updated_params["patient_id"] = user_id
                            logger.info(f"Auto-filled patient_id={user_id} for patient user")
                        elif patient_id_from_context and context.get("user_role") == "clinician":
                            updated_params["patient_id"] = patient_id_from_context
                            logger.info(f"Auto-filled patient_id={patient_id_from_context} from context for clinician")
                    elif "patient_id" in updated_params:
                        # Check if patient_id looks like a name (first name or full name) - replace only for clinicians with context
                        patient_id_value = updated_params["patient_id"]
                        if isinstance(patient_id_value, str) and not patient_id_value.startswith(("user_", "patient_")):
                            # Replace any name-like string that's not already a proper ID
                            if patient_id_from_context and context.get("user_role") == "clinician":
                                logger.info(f"Replacing patient name '{patient_id_value}' with context patient_id={patient_id_from_context}")
                                updated_params["patient_id"] = patient_id_from_context
                    
                    # Don't auto-fill medication_name - let the system ask for it
                    # This ensures the user confirms which medication they're having issues with
                    
                    return updated_params
                
                # Create new ChainedIntent objects with enriched parameters
                from app.schemas.action_chain_v2 import ChainedIntent
                
                # Update primary action
                enriched_primary_params = await enrich_params_with_context(
                    resolved_result.primary_action.parameters,
                    resolved_result.primary_action.action_type
                )
                updated_primary = ChainedIntent(
                    action_type=resolved_result.primary_action.action_type,
                    parameters=enriched_primary_params,
                    confidence=resolved_result.primary_action.confidence,
                    dependencies=resolved_result.primary_action.dependencies,
                    context_requirements=resolved_result.primary_action.context_requirements
                )
                
                # Update follow-up actions
                updated_follow_ups = []
                for action in resolved_result.follow_up_actions:
                    enriched_follow_up_params = await enrich_params_with_context(
                        action.parameters,
                        action.action_type
                    )
                    updated_action = ChainedIntent(
                        action_type=action.action_type,
                        parameters=enriched_follow_up_params,
                        confidence=action.confidence,
                        dependencies=action.dependencies,
                        context_requirements=action.context_requirements
                    )
                    updated_follow_ups.append(updated_action)
                
                # Create new ChainedAction with updated components
                resolved_result = ChainedAction(
                    chain_id=resolved_result.chain_id,
                    primary_action=updated_primary,
                    follow_up_actions=updated_follow_ups,
                    execution_mode=resolved_result.execution_mode,
                    failure_mode=resolved_result.failure_mode
                )
                
                # First validate all parameters for compound action
                compound_missing_params = []
                all_params_valid = True
                all_actions_info = []  # Include all actions, not just those with missing params
                
                # Get combined actions for validation (same logic as single action)
                try:
                    all_actions_list = []
                    existing_action_types = set()
                    all_templates = await TemplateValidationService.validate_and_get_accessible_templates(
                        db=db,
                        user_id=user_id,
                        role=user_role,
                    )
                    
                    for tmpl in all_templates:
                        tmpl_actions = getattr(tmpl, "actions", None)
                        if tmpl_actions and isinstance(tmpl_actions, list) and tmpl_actions:
                            for action in tmpl_actions:
                                action_type = action.get("action_type")
                                if action_type and action_type not in existing_action_types:
                                    all_actions_list.append(action)
                                    existing_action_types.add(action_type)
                    
                    combined_actions = all_actions_list
                    
                    if not combined_actions:
                        logger.error("No actions could be loaded for compound action validation.")
                        return {
                            "response": "Error: Could not load any actions for validation.",
                            "action": None,
                            "metadata": {},
                        }
                except Exception as val_exc:
                    logger.error(f"Error preparing actions for compound validation: {val_exc}", exc_info=True)
                    return {
                        "response": "Error preparing for validation.",
                        "action": None,
                        "metadata": {},
                    }
                
                # Check primary action
                primary_resolved = ResolvedIntent(
                    action_type=updated_primary.action_type,
                    parameters=[
                        IntentParameterValue(name=k, value=v, source="merged")
                        for k, v in updated_primary.parameters.items()
                    ]
                )
                
                is_valid, error_msg, _ = await intent_resolver.validate_parameters(
                    primary_resolved, combined_actions
                )
                
                missing_params = []
                missing_param_details = []
                all_param_details = []  # Initialize before the if block
                
                if not is_valid:
                    all_params_valid = False
                    if error_msg and "Missing required parameters: " in error_msg:
                        missing_params_str = error_msg.split("Missing required parameters: ")[1]
                        missing_params = [p.strip() for p in missing_params_str.split(",")]
                
                # Get action definition for ALL parameter details (moved outside if block)
                action_def = next((a for a in combined_actions if a.get("action_type") == updated_primary.action_type), None)
                
                if action_def:
                    param_defs = action_def.get("parameters", [])
                    for param_def in param_defs:
                        param_name = param_def.get("name")
                        all_param_details.append({
                            "name": param_name,
                            "type": param_def.get("type", "string"),
                            "description": param_def.get("description", ""),
                            "required": param_def.get("required", True),
                            "nullable": param_def.get("nullable", False),
                            "enum": param_def.get("enum", []),
                            "format": param_def.get("format", ""),
                            "placeholder": param_def.get("placeholder", f"Enter {param_name}"),
                            "is_missing": param_name in missing_params,
                            "current_value": updated_primary.parameters.get(param_name)
                        })
                
                # Always add the action info with ALL parameters
                compound_missing_params.append({
                    "actionType": updated_primary.action_type,
                    "actionIndex": 0,
                    "allParameters": all_param_details,  # Add all parameters
                    "missingParameters": [p for p in all_param_details if p["is_missing"]],  # Keep for backward compatibility
                    "existingParameters": updated_primary.parameters
                })
                
                # Check follow-up actions
                for idx, follow_up in enumerate(updated_follow_ups):
                    follow_up_resolved = ResolvedIntent(
                        action_type=follow_up.action_type,
                        parameters=[
                            IntentParameterValue(name=k, value=v, source="merged")
                            for k, v in follow_up.parameters.items()
                        ]
                    )
                    
                    is_valid, error_msg, _ = await intent_resolver.validate_parameters(
                        follow_up_resolved, combined_actions
                    )
                    
                    missing_params = []
                    missing_param_details = []
                    all_param_details = []  # Initialize before the if block
                    
                    if not is_valid:
                        all_params_valid = False
                        if error_msg and "Missing required parameters: " in error_msg:
                            missing_params_str = error_msg.split("Missing required parameters: ")[1]
                            missing_params = [p.strip() for p in missing_params_str.split(",")]
                    
                    # Get action definition for ALL parameter details (moved outside if block)
                    action_def = next((a for a in combined_actions if a.get("action_type") == follow_up.action_type), None)
                    
                    if action_def:
                        param_defs = action_def.get("parameters", [])
                        for param_def in param_defs:
                            param_name = param_def.get("name")
                            all_param_details.append({
                                "name": param_name,
                                "type": param_def.get("type", "string"),
                                "description": param_def.get("description", ""),
                                "required": param_def.get("required", True),
                                "nullable": param_def.get("nullable", False),
                                "enum": param_def.get("enum", []),
                                "format": param_def.get("format", ""),
                                "placeholder": param_def.get("placeholder", f"Enter {param_name}"),
                                "is_missing": param_name in missing_params,
                                "current_value": follow_up.parameters.get(param_name)
                            })
                    
                    # Always add the action info with ALL parameters
                    compound_missing_params.append({
                        "actionType": follow_up.action_type,
                        "actionIndex": idx + 1,
                        "allParameters": all_param_details,  # Add all parameters
                        "missingParameters": [p for p in all_param_details if p["is_missing"]],  # Keep for backward compatibility
                        "existingParameters": follow_up.parameters
                    })
                
                # If any parameters are missing, return parameter harvesting response
                if not all_params_valid:
                    logger.info(f"Compound action has missing parameters: {len(compound_missing_params)} actions need parameters")
                    
                    # Create raw LLM response structure for reconstruction
                    raw_llm_response = {
                        "primary_action": {
                            "action_type": resolved_result.primary_action.action_type,
                            "parameters": resolved_result.primary_action.parameters
                        },
                        "follow_up_actions": [
                            {
                                "action_type": action.action_type,
                                "parameters": action.parameters
                            }
                            for action in resolved_result.follow_up_actions
                        ],
                        "execution_mode": resolved_result.execution_mode.value
                    }
                    
                    # Save state for compound action
                    param_manager.save_compound_parameters(
                        user_id=user_id,
                        chain_id=resolved_result.chain_id,
                        action_states=compound_missing_params,
                        raw_llm_response=raw_llm_response
                    )
                    
                    return {
                        "response": "I need some additional information to complete these actions.",
                        "action": None,
                        "metadata": {
                            "module": "LLMActionModule",
                            "action_type": "compound_action",
                            "compound_missing_parameters": compound_missing_params,
                            "chain_id": resolved_result.chain_id,
                            "intent_resolution_result": "missing_parameters"
                        }
                    }
                
                # All parameters valid, proceed with execution
                from app.services.action_chain_executor_service import ActionChainExecutorService
                
                chain_executor = ActionChainExecutorService(db)
                chain_result = await chain_executor.execute_chain(
                    user_id=context.get("user_id"),
                    user_role=context.get("user_role"),
                    chain=resolved_result,
                    initial_context=context
                )
                
                # Format response for compound action
                if chain_result.success:
                    action_summaries = []
                    for result in chain_result.results:
                        if result.success:
                            action_summaries.append(f"✓ {result.action_type}: {result.message}")
                        else:
                            action_summaries.append(f"✗ {result.action_type}: {result.error_message}")
                    
                    response_message = f"Completed {len([r for r in chain_result.results if r.success])} of {len(chain_result.results)} actions:\n" + "\n".join(action_summaries)
                else:
                    # Check if the failure is due to missing parameters
                    if chain_result.results and len(chain_result.results) > 0:
                        first_failure = next((r for r in chain_result.results if not r.success), None)
                        if first_failure and "Missing required parameters" in first_failure.error_message:
                            # Extract the missing parameter names
                            response_message = first_failure.error_message
                            # Store partial parameters for next turn
                            if first_failure.action_type == "side_effect_report_create" and "medication_name" in first_failure.error_message:
                                response_message = "Which medication is causing these side effects? Please specify the medication name."
                            elif "patient_id" in first_failure.error_message:
                                response_message = "I need to know which patient this is for. Please specify the patient."
                        else:
                            response_message = f"I encountered an issue: {chain_result.error_message or 'Unknown error'}"
                    else:
                        response_message = f"I couldn't complete your request: {chain_result.error_message or 'Unknown error'}"
                
                # Check if we need to store state for missing parameters
                metadata = {
                    "module": "LLMActionModule",
                    "compound_action": True,
                    "execution_time_ms": chain_result.execution_time_ms
                }
                
                # If failed due to missing parameters, store the compound action for next turn
                if not chain_result.success and chain_result.results and len(chain_result.results) > 0:
                    first_failure = next((r for r in chain_result.results if not r.success), None)
                    if first_failure and "Missing required parameters" in first_failure.error_message:
                        # Store the compound action state
                        metadata["pending_compound_action"] = {
                            "chain": resolved_result.model_dump() if hasattr(resolved_result, 'model_dump') else None,
                            "completed_actions": [r.action_type for r in chain_result.results if r.success],
                            "failed_action": first_failure.action_type,
                            "missing_params": first_failure.error_message  # Contains info about what's missing
                        }
                        logger.info(f"Storing pending compound action in metadata: {metadata['pending_compound_action']}")
                
                logger.info(f"Returning compound action response with metadata: {metadata}")
                
                # Add action_type to metadata for frontend display
                metadata["action_type"] = "compound_action"  # Match frontend expectation
                metadata["success"] = chain_result.success
                metadata["data"] = {
                    "chain_id": chain_result.chain_id,
                    "results": [
                        {
                            "action_type": r.action_type,
                            "success": r.success,
                            "message": r.message if r.success else r.error_message,
                            "data": r.data
                        }
                        for r in chain_result.results
                    ]
                }
                metadata["message"] = response_message
                
                result = {
                    "response": response_message,
                    "action": {
                        "type": "compound",
                        "chain_id": chain_result.chain_id,
                        "results": [
                            {
                                "action_type": r.action_type,
                                "success": r.success,
                                "message": r.message if r.success else r.error_message,
                                "data": r.data
                            }
                            for r in chain_result.results
                        ]
                    },
                    "metadata": metadata
                }
                
                logger.info(f"FULL COMPOUND ACTION RESPONSE: {json.dumps(result, indent=2, default=str)}")
                return result
            
            # Handle single action (existing flow)
            resolved_intent = resolved_result
            
            # --- Parameter Merging & Validation Logic ---
            logger.debug(f"Initial LLM resolved action: {resolved_intent.action_type}")
            logger.debug(f"Tracked intent action type: {current_intent_action_type}")

            # Determine the definitive action type for this turn
            definitive_action_type = (
                current_intent_action_type or resolved_intent.action_type
            )
            if not definitive_action_type:
                # If no intent is tracked and LLM didn't find one, we can't proceed
                # However, we'll pass back the specific resolution result to allow for fallback
                logger.warning(
                    "No intent tracked and LLM did not resolve an action type."
                )
                return {
                    "response": "I'm not sure what action you're trying to perform. Could you please clarify?",
                    "action": None,
                    "metadata": {
                        "module": "LLMActionModule",
                        "error": "No action type identified",
                        "intent_resolution_result": (
                            resolved_intent.result.value
                            if hasattr(resolved_intent, "result")
                            and hasattr(resolved_intent.result, "value")
                            else "failed_extraction"
                        ),
                    },
                }

            logger.info(f"Proceeding with action type: {definitive_action_type}")

            # Check if the LLM's resolved intent conflicts with the tracked intent
            intent_changed = (
                current_intent_action_type
                and definitive_action_type != current_intent_action_type
            )
            if intent_changed:
                logger.warning(
                    f"LLM resolved a different intent ({resolved_intent.action_type}) than the one being tracked ({current_intent_action_type}). Resetting parameters."
                )
                merged_params = {}  # Reset parameters if intent changes
            else:
                merged_params = previous_parameters.copy()

            # Merge parameters extracted from the current message by the LLM
            # Only merge if the LLM's resolved action matches the definitive one (or if no intent was tracked yet)
            if not intent_changed or not current_intent_action_type:
                newly_extracted_params = {
                    param.name: param.value
                    for param in resolved_intent.parameters
                    if param.value is not None  # Only merge non-null values
                }
                merged_params.update(newly_extracted_params)
                logger.info(f"Parameters after merging LLM extraction: {merged_params}")
            else:
                logger.info("Skipping merge from LLM due to intent change.")

            # --- Re-validation Step ---
            # Create a ResolvedIntent object representing the current state with merged parameters
            current_state_intent = ResolvedIntent(
                action_type=definitive_action_type,  # Use the definitive action type
                template_id=resolved_intent.template_id,  # Use template_id from initial resolution
                parameters=[
                    # Create IntentParameterValue objects for validation
                    IntentParameterValue(name=k, value=v, source="merged")
                    for k, v in merged_params.items()
                ],
                # Carry over confidence/raw response if needed, or set defaults
                confidence_score=resolved_intent.confidence_score,
                raw_llm_response=resolved_intent.raw_llm_response,
            )

            # Fetch combined actions for validation context (ensure this logic is robust)
            try:

                # --- Fetch and combine actions from ALL accessible templates ---
                # Initialize correctly
                all_actions_list = []
                existing_action_types = set()
                logger.info(
                    "Fetching all accessible templates for validation context..."
                )
                all_templates = await TemplateValidationService.validate_and_get_accessible_templates(
                    db=db,
                    user_id=user_id,  # Use current user_id
                    role=user_role,  # Use current user_role
                )

                # Loop through ALL templates, including the primary one potentially loaded earlier
                logger.debug(f"Found {len(all_templates)} accessible templates.")
                for tmpl in all_templates:
                    logger.debug(
                        f"Processing template ID: {tmpl.id}, Name: {getattr(tmpl, 'name', 'N/A')}"
                    )
                    # Use the correct attribute 'actions' which is already JSONB
                    tmpl_actions = getattr(tmpl, "actions", None)
                    if tmpl_actions:  # Check if it exists and is not None/empty
                        logger.debug(f"Template {tmpl.id} has 'actions' attribute.")
                        try:
                            # No need for json.loads, actions should already be a list/dict
                            if (
                                isinstance(tmpl_actions, list) and tmpl_actions
                            ):  # Check if it's a non-empty list
                                logger.debug(
                                    f"Found {len(tmpl_actions)} actions in template {tmpl.id}."
                                )
                                for action in tmpl_actions:
                                    action_type = action.get("action_type")
                                    # Add action if its type hasn't been seen yet
                                    if (
                                        action_type
                                        and action_type not in existing_action_types
                                    ):
                                        logger.debug(
                                            f"Adding action '{action_type}' from template {tmpl.id}."
                                        )
                                        all_actions_list.append(action)
                                        existing_action_types.add(action_type)
                                    elif not action_type:
                                        logger.warning(
                                            f"Action in template {tmpl.id} missing 'action_type': {action}"
                                        )
                                    else:
                                        logger.debug(
                                            f"Action type '{action_type}' from template {tmpl.id} already seen, skipping."
                                        )
                            else:
                                logger.warning(
                                    f"'actions' attribute for template {tmpl.id} is not a non-empty list: {tmpl_actions}"
                                )
                        except Exception as parse_exc:
                            logger.error(
                                f"Unexpected error processing actions for template {tmpl.id}: {parse_exc}",
                                exc_info=True,
                            )  # Log other potential errors
                    else:
                        logger.debug(
                            f"Template {tmpl.id} does not have 'actions' attribute or it's empty."
                        )

                combined_actions = all_actions_list  # Use the correctly built list
                if not combined_actions:
                    logger.error("No actions could be loaded for validation.")
                    # Handle error appropriately
                    return {
                        "response": "Error: Could not load any actions for validation.",
                        "action": None,
                        "metadata": {},
                    }

                logger.debug(
                    f"Total unique actions fetched for validation: {len(combined_actions)}"
                )
                logger.debug(
                    f"Action types used for validation: {[a.get('action_type') for a in combined_actions]}"
                )
                # --- End fetching all actions ---

            except Exception as val_exc:
                logger.error(
                    f"Error preparing actions for validation: {val_exc}", exc_info=True
                )
                # Handle error appropriately, maybe return an error response
                return {
                    "response": "Error preparing for validation.",
                    "action": None,
                    "metadata": {},
                }

            # --- Add patient_id before validation if applicable ---
            if user_role == "patient" and "patient_id" not in merged_params:
                logger.info(
                    f"Adding patient_id={user_id} to merged_params before validation."
                )
                merged_params["patient_id"] = user_id
            elif user_role == "clinician" and "patient_id" not in merged_params:
                # For clinicians, check if we have a patient context
                patient_id_from_context = context.get("patient_id") or context.get("currentPatientId")
                if patient_id_from_context:
                    logger.info(
                        f"Adding patient_id={patient_id_from_context} from context for clinician action."
                    )
                    merged_params["patient_id"] = patient_id_from_context
            
            # Check if patient_id looks like a name and replace with context ID
            if "patient_id" in merged_params and merged_params["patient_id"]:
                patient_id_value = merged_params["patient_id"]
                # If it looks like a name (first name or full name) rather than an ID
                if isinstance(patient_id_value, str) and not patient_id_value.startswith(("user_", "patient_")):
                    patient_id_from_context = context.get("patient_id") or context.get("currentPatientId")
                    if patient_id_from_context:
                        logger.info(
                            f"Replacing patient name '{patient_id_value}' with context patient_id={patient_id_from_context}"
                        )
                        merged_params["patient_id"] = patient_id_from_context
                    
            # Update the parameters list in current_state_intent if we added/changed patient_id
            if "patient_id" in merged_params:
                # Find if patient_id already exists (it shouldn't based on the check above, but defensive)
                existing_pid_param = next(
                    (
                        p
                        for p in current_state_intent.parameters
                        if p.name == "patient_id"
                    ),
                    None,
                )
                if not existing_pid_param:
                    current_state_intent.parameters.append(
                        IntentParameterValue(
                            name="patient_id", value=merged_params["patient_id"], source="user_context"
                        )
                    )
                else:  # Should not happen, but update if it does
                    existing_pid_param.value = merged_params["patient_id"]
                    existing_pid_param.source = "user_context"

            # Log the exact state being sent for validation
            logger.debug(
                f"Calling validate_parameters for action '{current_state_intent.action_type}' with parameters:"
            )
            for param in current_state_intent.parameters:
                logger.debug(
                    f"  - {param.name}: {param.value} (source: {param.source})"
                )

            # Validate the complete set of merged parameters against the definitive action type
            is_valid, validation_error_message, validated_intent = (
                await intent_resolver.validate_parameters(
                    current_state_intent,  # Pass the intent representing the current merged state
                    combined_actions,  # Pass the relevant actions for schema lookup
                )
            )
            logger.info(
                f"Validation result for merged parameters: valid={is_valid}, message={validation_error_message}"
            )

            # --- Attempt to auto-fill missing appointment_request_create params ---
            if not is_valid and definitive_action_type == "appointment_request_create":
                missing_params = (
                    set(validation_error_message.split(": ")[1].split(", "))
                    if validation_error_message
                    and "Missing required parameters: " in validation_error_message
                    else set()
                )
                can_autofill = True

                # Check if clinician_preference is missing and try to fill it
                if "clinician_preference" in missing_params:
                    logger.info(
                        "Attempting to auto-fill missing 'clinician_preference' for appointment_request_create."
                    )
                    try:
                        from app.crud import (
                            patient as crud_patient,
                        )  # Import here to avoid circular dependency issues at top level

                        primary_clinician = crud_patient.get_primary_clinician(
                            db, patient_id=user_id
                        )
                        if primary_clinician:
                            merged_params["clinician_preference"] = primary_clinician.id
                            logger.info(
                                f"Auto-filled 'clinician_preference' with primary clinician ID: {primary_clinician.id}"
                            )
                            missing_params.discard("clinician_preference")
                        else:
                            logger.warning(
                                f"Could not find primary clinician for patient {user_id} to auto-fill 'clinician_preference'."
                            )
                            can_autofill = False  # Cannot proceed without a clinician
                    except Exception as auto_fill_exc:
                        logger.error(
                            f"Error auto-filling clinician_preference: {auto_fill_exc}",
                            exc_info=True,
                        )
                        can_autofill = False

                # Check if reason is missing and try to fill it
                if "reason" in missing_params:
                    logger.info(
                        "Auto-filling missing 'reason' for appointment_request_create with default."
                    )
                    merged_params["reason"] = "Patient requested appointment via chat"
                    missing_params.discard("reason")

                # If we could potentially autofill, rebuild intent and re-validate
                if (
                    can_autofill and not missing_params
                ):  # Only re-validate if we potentially filled all missing required ones
                    logger.info("Re-validating parameters after auto-fill attempt.")
                    current_state_intent = ResolvedIntent(
                        action_type=definitive_action_type,
                        template_id=resolved_intent.template_id,
                        parameters=[
                            IntentParameterValue(
                                name=k,
                                value=v,
                                source=(
                                    "autofilled"
                                    if k in ["clinician_preference", "reason"]
                                    else "merged"
                                ),
                            )
                            for k, v in merged_params.items()
                        ],
                        confidence_score=resolved_intent.confidence_score,  # Keep original confidence
                        raw_llm_response=resolved_intent.raw_llm_response,
                    )
                    # Re-validate
                    is_valid, validation_error_message, validated_intent = (
                        await intent_resolver.validate_parameters(
                            current_state_intent, combined_actions
                        )
                    )
                    logger.info(
                        f"Re-validation result after auto-fill: valid={is_valid}, message={validation_error_message}"
                    )
                elif not can_autofill:
                    # If we couldn't autofill clinician, update the error message
                    validation_error_message = "Could not automatically determine your clinician. Please specify which clinician you'd like to request an appointment with."
                    logger.warning(validation_error_message)

            # Update the main resolved_intent with the final validated state (potentially after re-validation)
            resolved_intent = validated_intent

            # --- Parameter Persistence & Response Logic ---
            params_to_store = {}
            intent_action_type_to_store = None

            if not is_valid:
                # Validation failed (even after auto-fill attempt), store state for the next turn
                params_to_store = {
                    param.name: param.value
                    for param in resolved_intent.parameters  # Use parameters from the validated_intent
                    if param.value is not None
                }
                # Store the definitive action type being tracked
                intent_action_type_to_store = definitive_action_type
                logger.info(
                    f"Storing parameters for next turn (validation failed): {params_to_store}"
                )
                logger.info(
                    f"Storing intent action type for next turn: {intent_action_type_to_store}"
                )
                
                # Extract missing parameters from validation error
                missing_params = []
                if validation_error_message:
                    if "Missing required parameters: " in validation_error_message:
                        missing_params_str = validation_error_message.split("Missing required parameters: ")[1]
                        missing_params = [p.strip() for p in missing_params_str.split(",")]
                    elif "Please specify an appointment type" in validation_error_message:
                        # Special case for appointment type
                        missing_params = ["appointment_type"]
                    elif "Please specify how long you need" in validation_error_message:
                        # Special case for duration
                        missing_params = ["duration_minutes"]
                    elif "Please provide your patient ID" in validation_error_message:
                        # Special case for patient ID
                        missing_params = ["patient_id"]
                
                # Save partial parameters using persistence manager
                param_manager.save_partial_parameters(
                    user_id=user_id,
                    action_type=definitive_action_type,
                    parameters=params_to_store,
                    missing_params=missing_params
                )

                # Get action schema information for ALL parameters
                action_def = next((a for a in combined_actions if a.get("action_type") == definitive_action_type), None)
                all_param_details = []
                missing_param_names = missing_params  # Keep track of which are missing
                
                if action_def:
                    # Get parameter definitions from the action
                    param_defs = action_def.get("parameters", [])
                    for param_def in param_defs:
                        param_name = param_def.get("name")
                        # Add all parameters with their current values and whether they're missing
                        all_param_details.append({
                            "name": param_name,
                            "type": param_def.get("type", "string"),
                            "description": param_def.get("description", ""),
                            "required": param_def.get("required", True),
                            "nullable": param_def.get("nullable", False),
                            "enum": param_def.get("enum", []),
                            "format": param_def.get("format", ""),
                            "placeholder": param_def.get("placeholder", f"Enter {param_name}"),
                            "is_missing": param_name in missing_param_names,
                            "current_value": params_to_store.get(param_name)
                        })
                
                # Log parameter details for debugging appointment issues
                if definitive_action_type == "appointment_create":
                    logger.info(f"APPOINTMENT DEBUG: Sending parameter metadata to frontend")
                    logger.info(f"APPOINTMENT DEBUG: all_parameters={all_param_details}")
                    logger.info(f"APPOINTMENT DEBUG: existing_parameters={params_to_store}")
                
                # Return the specific validation error message with ALL parameter details
                return {
                    "response": validation_error_message
                    or "Please provide the missing information.",
                    "action": None,
                    "metadata": {
                        "module": "LLMActionModule",
                        "action_type": definitive_action_type,
                        "all_parameters": all_param_details,  # Send ALL parameters
                        "missing_parameters": [p for p in all_param_details if p["is_missing"]],  # Keep for backward compatibility
                        "existing_parameters": params_to_store,
                        "intent_resolution_result": (
                            resolved_intent.result.value
                            if hasattr(resolved_intent.result, "value")
                            else str(resolved_intent.result)
                        ),  # Should be MISSING_PARAMETERS or INVALID_PARAMETERS - convert to string value for easier comparison
                        "raw_llm_response": resolved_intent.raw_llm_response,
                        "previous_parameters": params_to_store,  # Pass collected params back
                        "current_intent_action_type": intent_action_type_to_store,  # Pass tracked intent back
                    },
                }
            # --- Validation successful, proceed to execution ---
            logger.info(
                f"All required parameters for {resolved_intent.action_type} are present and valid."
            )

            # Execute the resolved action - pass the full chat context so
            # ActionExecutorService can pick up `timezone_offset`, etc.
            action_executor = ActionExecutorService(db)
            execution_result = await action_executor.execute_action(
                user_id=user_id,
                user_role=user_role,
                resolved_intent=resolved_intent,
                chat_context=context,  # <-- Pass full context instead of just timezone_offset
            )
            
            # Clear saved parameters on successful execution
            if execution_result.success:
                param_manager.clear_parameters(user_id, definitive_action_type)
                logger.info(f"Cleared saved parameters after successful execution of {definitive_action_type}")

            # Return the response
            return {
                "response": execution_result.message,
                "action": (
                    execution_result.action_type
                    if hasattr(execution_result, "action_type")
                    else resolved_intent.action_type
                ),
                "metadata": {
                    "module": "LLMActionModule",
                    "success": execution_result.success,
                    "data": execution_result.data,
                    "action_type": resolved_intent.action_type,
                    "parameters": {
                        param.name: param.value for param in resolved_intent.parameters
                    },
                    # Clear stored parameters on successful execution
                    "previous_parameters": {},
                    "current_intent_action_type": None,
                },
            }

        except Exception as e:
            logger.error(f"Error in LLMActionModule: {str(e)}", exc_info=True)
            return {
                "response": "I encountered an error while trying to process your command. Please try again or rephrase your request.",
                "action": None,
                "metadata": {
                    "module": "LLMActionModule",
                    "error": str(e),
                    "error_type": "EXECUTION_ERROR",
                },
            }
