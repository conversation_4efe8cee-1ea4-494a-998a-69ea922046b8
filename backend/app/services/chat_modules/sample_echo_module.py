from typing import Any

from .base_chat_module import BaseChatModule


class SampleEchoModule(BaseChatModule):
    """
    A sample chat module implementation for demonstration and testing purposes.

    This module simply echoes back the incoming message and returns the required output structure.
    It demonstrates how to inherit from BaseChatModule and comply with the standardized interface.

    Usage:
        echo_module = SampleEchoModule()
        response = echo_module.process_message("Hello!", {"user_role": "patient"})
    """

    def process_message(self, message: str, context: dict[str, Any]) -> dict[str, Any]:
        """
        Echoes the incoming message and returns a standardized response.

        Args:
            message (str): The incoming chat message.
            context (dict): Additional context (see BaseChatModule for expected keys).

        Returns:
            dict: Standardized output with "response", "action", and "metadata".
        """
        return {
            "response": f"Echo: {message}",
            "action": None,
            "metadata": {
                "module": "SampleEchoModule",
                "received_context_keys": list(context.keys()),
            },
        }
