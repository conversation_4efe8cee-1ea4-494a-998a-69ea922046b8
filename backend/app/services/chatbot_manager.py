"""Centralized chatbot manager for routing messages to specialized modules."""

import logging
import re
from typing import Any, Optional

from app.services.chat_modules.base_chat_module import BaseChatModule
from app.services.chat_modules.clinician_guidelines_module import (
    ClinicianGuidelinesModule,
)
from app.services.chat_modules.daily_health_coach import DailyHealthCoach
from app.services.chat_modules.llm_action_module import LLMActionModule

logger = logging.getLogger(__name__)


class ChatbotManager:
    """
    ChatbotManager routes incoming chat messages to the appropriate specialized chat module
    based on the message content and associated context.

    Mo<PERSON>les must implement the standardized BaseChatModule interface and return a dict with
    "response", "action", and "metadata" keys from their process_message method.

    Routing Criteria:
        - If message appears to be a command or action request, route to LLMActionModule
        - If context['user_role'] == "clinician" and the message contains guideline-related keywords,
          route to ClinicianGuidelinesModule.
        - Otherwise, route to DailyHealthCoach (default for patients and general inquiries).
    """

    def __init__(self):
        """Initialize the ChatbotManager with available modules."""
        self.modules = {}
        self._register_modules()

    def _register_modules(self):
        """Register available chat modules for routing."""
        self.modules["daily_health_coach"] = DailyHealthCoach()
        self.modules["clinician_guidelines"] = ClinicianGuidelinesModule()
        self.modules["llm_action"] = LLMActionModule()

    def _select_module(self, message: str, context: dict[str, Any]) -> str:
        """Select the appropriate module based on message content and context."""
        logger.debug(
            f"Selecting module for message: {message[:30]}... with context keys: {list(context.keys())}"
        )

        # Get user role (patient or clinician)
        user_role = context.get("user_role", "patient").lower()

        # Get message route if specified
        message_route = None
        if context.get("message_route"):
            if hasattr(context["message_route"], "value"):
                message_route = context["message_route"].value.lower()
            elif isinstance(context["message_route"], str):
                message_route = context["message_route"].lower()

        logger.debug(f"Message route for module selection: {message_route}")

        # Handle direct clinician-to-patient routing (not patient conversations)
        if message_route == "patient" and user_role == "clinician":
            logger.info("Direct clinician-to-patient routing - bypassing module selection")
            return "direct_patient_message"
        
        # Check for pending compound action - route to LLM Action Module to continue
        if context.get("pending_compound_action"):
            logger.info("Found pending compound action - routing to LLM Action Module to continue")
            return "llm_action"
        
        # Check for pending parameter collection from previous message
        if context.get("previous_parameters") or context.get("current_intent_action_type"):
            logger.info(
                f"Found pending parameter collection for action: {context.get('current_intent_action_type')} "
                f"- routing to LLM Action Module to complete"
            )
            return "llm_action"

        # Intelligent routing based on message content and user role
        if user_role == "clinician":
            # Check if this is a RAG request for clinic-specific information
            if self._is_rag_request(message):
                logger.info("Selected RAG Module for clinic information request")
                return "rag_info"
            # Check if this looks like an action/command
            elif self._is_action_request(message):
                logger.info("Selected LLM Action Module for clinician action request")
                return "llm_action"
            else:
                # Route to guidelines module for medical consultation/guidance
                logger.info("Selected Clinician Guidelines Module for clinical guidance")
                return "clinician_guidelines"
        elif user_role == "patient":
            # Check if this is a RAG request for clinic info or education materials
            if self._is_rag_request(message):
                logger.info("Selected RAG Module for patient clinic/education information request")
                return "rag_info"
            # Check if this looks like an action/command
            elif self._is_action_request(message):
                logger.info("Selected LLM Action Module for patient action request")
                return "llm_action"
            else:
                # Route to health coach for general health conversation
                logger.info("Selected Daily Health Coach for patient conversation")
                return "daily_health_coach"

        # Fallback for any other roles
        logger.info("Selected DailyHealthCoach as default module")
        return "daily_health_coach"

    def _is_rag_request(self, message: str) -> bool:
        """Determine if message is requesting clinic-specific information that should use RAG."""
        message_lower = message.lower()
        
        # RAG-specific question patterns
        rag_patterns = [
            # Direct clinic questions
            "what treatments", "what medications", "what services", "what options",
            "what does our clinic", "what do we offer", "what is available",
            "tell me about our", "tell me about the clinic", "tell me about treatments",
            "what are our", "what's available", "what can we",
            
            # Information seeking about clinic
            "clinic offers", "clinic provides", "clinic has", "available at",
            "cost of", "price of", "how much", "fees for",
            
            # Protocol and procedure questions
            "our protocol", "our procedure", "our guidelines", "our approach",
            "how do we", "what's our process", "our standard",
            
            # Staff and resource questions
            "who are our", "our team", "our staff", "our doctors", "our clinicians",
            "our equipment", "our facilities", "doctor", "doctors", "clinician", "clinicians",
            "staff", "team", "provider", "providers",
            
            # Document and education material queries
            "education material", "educational material", "my materials", "assigned material",
            "document", "pdf", "guide", "information about",
            
            # Patient-specific patterns
            "my clinic", "the clinic", "clinic information", "clinic details",
            "in our documents", "in the documents", "in our materials", "in the materials",
            "our educational", "our education", "education materials", "educational content",
            "tell me about", "what is", "explain", "describe",
            "document says", "material says", "according to"
        ]
        
        # Check for RAG patterns
        if any(pattern in message_lower for pattern in rag_patterns):
            return True
            
        # Check for specific question words with clinic/document context
        question_starters = ["what", "which", "how", "where", "when", "who", "tell", "explain", "describe"]
        context_words = ["clinic", "facility", "center", "practice", "our", "we", "here", 
                        "document", "documents", "material", "materials", "education", "educational",
                        "doctor", "doctors", "clinician", "clinicians", "staff", "team", "provider",
                        "treatment", "medication", "service", "appointment"]
        
        starts_with_question = any(message_lower.startswith(q) for q in question_starters)
        mentions_context = any(word in message_lower for word in context_words)
        
        return starts_with_question and mentions_context

    def _is_action_request(self, message: str) -> bool:
        """Determine if message appears to be an action/command request."""
        message_lower = message.lower()
        
        # Action/command indicators
        action_words = [
            "schedule", "book", "create", "add", "make", "set up",
            "cancel", "delete", "remove", "update", "change", "modify",
            "log", "record", "enter", "input"
        ]
        
        # Check if message contains action words (not just starts with)
        if any(word in message_lower for word in action_words):
            return True
            
        # Check for appointment scheduling patterns
        appointment_words = ["appointment", "schedule", "meeting", "session", "consultation"]
        time_words = ["tomorrow", "next week", "monday", "tuesday", "wednesday", 
                     "thursday", "friday", "saturday", "sunday", " am", " pm", 
                     ":00", "morning", "afternoon", "evening"]
        
        has_appointment = any(word in message_lower for word in appointment_words)
        has_time = any(word in message_lower for word in time_words)
        
        return has_appointment and has_time

    async def route_message(
        self, message: str, context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Route the incoming message to the appropriate chat module.

        Routing Criteria:
        - If message is patient-routed, skip to direct patient routing
        - If the message appears to be a command, route to LLMActionModule
        - If the user is a clinician AND the message contains guideline keywords,
          route to ClinicianGuidelinesModule.
        - Otherwise, default to DailyHealthCoach.
        - If no module is found, return a fallback response.

        Suggested Test Cases:
        # 1. context={}, message="Hello" -> DailyHealthCoach
        # 2. context={"user_role": "patient"}, message="Any advice?" -> DailyHealthCoach
        # 3. context={"user_role": "clinician"}, message="guidelines for obesity" -> ClinicianGuidelinesModule
        # 4. context={"user_role": "clinician"}, message="Hello" -> DailyHealthCoach
        # 5. context={}, message="Schedule an appointment" -> LLMActionModule
        """
        # Check first if this is a patient-routed message, which shouldn't go through AI
        message_route = None
        if context.get("message_route"):
            if hasattr(context["message_route"], "value"):
                message_route = context["message_route"].value.lower()
            elif isinstance(context["message_route"], str):
                message_route = context["message_route"].lower()

        # Handle direct routing that should skip AI processing
        if message_route == "patient" and context.get("userRole") != "patient":
            # Clinician-to-patient routing
            logger.info(
                "Message is explicitly routed to patient from clinician - skipping module selection"
            )
            return {
                "response": "Message sent to patient",
                "action": "patient_message_direct",
                "metadata": {"module": "message_router", "patient_routed": True},
            }
        elif message_route == "clinician" and context.get("userRole") == "patient":
            # Patient-to-clinician routing  
            logger.info(
                "Message is explicitly routed to clinician from patient - skipping module selection"
            )
            return {
                "response": "Message sent to your clinician",
                "action": "clinician_message_direct", 
                "metadata": {"module": "message_router", "clinician_routed": True},
            }

        # Select the appropriate module based on message and context
        selected_module_name = self._select_module(message, context)

        # Log the module selection
        logger.info(f"Selected module for routing: {selected_module_name}")

        # Special handling for RAG information requests
        if selected_module_name == "rag_info":
            return await self._handle_rag_request(message, context)

        # Get the module instance
        module: Optional[BaseChatModule] = self.modules.get(selected_module_name)

        # Fallback logic: If no appropriate module instance is found in self.modules
        if module is None:
            logger.warning(
                f"No module instance found for {selected_module_name}. Falling back."
            )
            return {
                "response": f"Configuration error: Module '{selected_module_name}' not registered or found.",
                "action": None,
                "metadata": {},
            }

        # Process the message using the selected module
        try:
            result = await module.process_message(message, context)
            logger.info(f"Message processed by {selected_module_name}.")
            
            # Log if we have pending_compound_action in metadata
            if isinstance(result, dict) and result.get("metadata", {}).get("pending_compound_action"):
                logger.info("CHATBOT MANAGER: Response contains pending_compound_action metadata")
                logger.info(f"CHATBOT MANAGER: Full metadata: {result.get('metadata')}")
            
            return result
        except Exception as e:
            logger.error(
                f"Error processing message in {selected_module_name}: {e}",
                exc_info=True,
            )
            return {
                "response": "An error occurred while processing your request.",
                "action": None,
                "metadata": {"error": str(e)},
            }

    async def _handle_rag_request(self, message: str, context: dict[str, Any]) -> dict[str, Any]:
        """Handle RAG information requests with clinic-specific data using LLM."""
        logger.info("Processing RAG information request")
        
        try:
            # Debug: Log all context keys to see what's available
            logger.info(f"RAG handler context keys: {list(context.keys())}")
            
            # Extract RAG context - the key is 'rag_context' not separate chunks/pages
            rag_context = context.get("rag_context", "")
            
            # Debug: Log what we found
            logger.info(f"Found RAG context: {bool(rag_context)} (length: {len(rag_context) if rag_context else 0})")
            
            if not rag_context:
                logger.warning("No RAG content available for information request")
                return {
                    "response": "I don't have specific information about that topic in our clinic database. Could you provide more details or rephrase your question?",
                    "action": "rag_no_content",
                    "metadata": {"module": "rag_info", "content_found": False},
                }
            
            # Use LLM to generate conversational response from RAG context
            try:
                from app.core.llm.factory import LLMFactory
                from app.core.llm.base import PromptTemplate
                
                # Create LLM provider with kwargs
                provider = LLMFactory.create_provider(
                    "openai", 
                    model="gpt-4o-mini",
                    temperature=0.7,
                    max_tokens=500
                )
                
                # Create prompt for conversational response
                user_type = "patient" if context.get("user_role") == "patient" else "clinician"
                
                system_prompt = f"""You are a helpful medical clinic assistant responding to a {user_type}. 
                Based on the provided clinic information and education materials, answer the user's question in a 
                natural, conversational way. Be specific and helpful while maintaining a professional tone. 
                
                Important guidelines:
                - Provide factual information from the clinic resources and education materials
                - If discussing medical topics, include appropriate disclaimers for patients
                - If the information doesn't fully answer their question, acknowledge what you can provide
                - For medical advice questions from patients, encourage them to consult their healthcare provider
                """
                
                # Format the complete prompt
                formatted_prompt = f"""{system_prompt}

Clinic Information:
{rag_context}

User Question: {message}

Your Response:"""
                
                # Generate response
                llm_response = await provider.generate(formatted_prompt)
                
                return {
                    "response": llm_response.content.strip(),
                    "action": "rag_llm_response",
                    "metadata": {
                        "module": "rag_info", 
                        "content_found": True,
                        "llm_used": True,
                        "rag_context_length": len(rag_context)
                    }
                }
                
            except Exception as llm_error:
                logger.error(f"Error using LLM for RAG response: {llm_error}", exc_info=True)
                # Fall back to the original string manipulation approach
                pass
            
            # Format a concise response with clinic-specific information
            # Extract key pricing information if this is a cost-related query
            message_lower = message.lower()
            
            if any(word in message_lower for word in ["cost", "price", "pricing", "fee", "fees", "how much"]):
                # Extract pricing information from RAG context
                pricing_info = []
                if "£32/week" in rag_context and "mounjaro" in rag_context.lower():
                    pricing_info.append("Mounjaro: £32/week")
                if "£32/week" in rag_context and "wegovy" in rag_context.lower():
                    pricing_info.append("Wegovy: £32/week")
                if "£55/pen" in rag_context and "saxenda" in rag_context.lower():
                    pricing_info.append("Saxenda: £55/pen")
                
                if pricing_info:
                    return {
                        "response": f"Treatment costs at our clinic:\n• {chr(10).join(pricing_info)}",
                        "action": "rag_pricing_info",
                        "metadata": {"module": "rag_info", "content_found": True, "pricing_items": len(pricing_info)},
                    }
            
            # Check if this is a document/education material query
            if any(word in message_lower for word in ["document", "documents", "material", "materials", "docetl", "education"]):
                # For education materials, provide the full context since it's specific technical content
                # Clean up the context to make it more readable
                import re
                
                # Try to get the education material URL from the database
                material_url = None
                material_title = None
                
                # Check if we have chunk metadata from the context enricher
                chunks_metadata = context.get("rag_chunks_metadata", [])
                
                if chunks_metadata and 'db' in context:
                    db = context['db']
                    try:
                        from app.crud.crud_education_material import education_material as crud_education_material
                        
                        # Extract unique education material IDs from chunk metadata
                        material_ids = set()
                        for chunk_data in chunks_metadata:
                            metadata = chunk_data.get("metadata_", {})
                            if metadata.get("source_type") == "education_material":
                                material_id = metadata.get("education_material_id")
                                if material_id:
                                    material_ids.add(material_id)
                        
                        # Get the first material's URL and title
                        for material_id in material_ids:
                            material = crud_education_material.get(db=db, id=material_id)
                            if material:
                                material_url = material.content_url
                                material_title = material.title
                                logger.info(f"Found education material: {material_title} with URL: {material_url}")
                                break
                                
                    except Exception as e:
                        logger.error(f"Error retrieving education material URL: {e}", exc_info=True)
                
                # Split into contexts if multiple are provided
                contexts = rag_context.split("Context ")
                formatted_contexts = []
                
                for ctx in contexts:
                    if ctx.strip():
                        # Remove excessive line breaks and clean up
                        cleaned = re.sub(r'\n+', ' ', ctx.strip())
                        cleaned = re.sub(r'\s+', ' ', cleaned)
                        # Remove the number prefix if present (e.g., "1:" or "2:")
                        cleaned = re.sub(r'^\d+:\s*', '', cleaned)
                        # Remove trailing dash and spaces
                        cleaned = cleaned.rstrip('- ')
                        # Remove leading period if present
                        cleaned = cleaned.lstrip('. ')
                        if len(cleaned) > 50:  # Only include substantial content
                            # Create a cleaner truncation without trailing punctuation issues
                            if len(cleaned) > 500:
                                cleaned = cleaned[:497] + "..."
                            formatted_contexts.append(cleaned)
                
                if formatted_contexts:
                    # Get the first substantial context and clean it up
                    main_context = ""
                    for ctx in formatted_contexts:
                        if len(ctx) > 100:  # Find a meaningful context
                            main_context = ctx
                            break
                    
                    if not main_context and formatted_contexts:
                        main_context = formatted_contexts[0]
                    
                    response = f"Based on the education materials: {main_context}"
                    
                    # Add link to the source document if available
                    if material_url and material_title:
                        # Clean up the title for display
                        display_title = material_title
                        # Remove version numbers like -2410.12189v1
                        import re
                        display_title = re.sub(r'-\d+\.\d+v\d+$', '', display_title)
                        # Remove file extensions
                        display_title = re.sub(r'\.(pdf|docx?|txt)$', '', display_title, re.IGNORECASE)
                        
                        response += f"\n\n📄 Source: {display_title}\n{material_url}"
                    elif material_url:
                        response += f"\n\n📄 View source document:\n{material_url}"
                    
                    return {
                        "response": response.strip(),
                        "action": "rag_education_material",
                        "metadata": {
                            "module": "rag_info", 
                            "content_found": True, 
                            "contexts_used": len(formatted_contexts),
                            "material_url": material_url,
                            "material_title": material_title
                        },
                    }
            
            # For other RAG queries, provide a concise summary
            # Extract first meaningful sentence or key information
            import re
            sentences = re.split(r'[.!?]+', rag_context)
            meaningful_content = []
            
            for sentence in sentences[:5]:  # Check first 5 sentences
                sentence = sentence.strip()
                if len(sentence) > 20 and any(word in sentence.lower() for word in ["treatment", "service", "clinic", "offer", "available", "injection", "medication"]):
                    meaningful_content.append(sentence)
                    if len(meaningful_content) >= 2:  # Limit to 2 key points
                        break
            
            if meaningful_content:
                response = "Based on our clinic information:\n• " + "\n• ".join(meaningful_content)
                return {
                    "response": response,
                    "action": "rag_info_found",
                    "metadata": {"module": "rag_info", "content_found": True, "sentences_used": len(meaningful_content)},
                }
            
            # Fallback if no meaningful content extracted
            return {
                "response": "I found some information but couldn't extract the relevant details. Please contact the clinic directly for specific information.",
                "action": "rag_extraction_failed", 
                "metadata": {"module": "rag_info", "content_found": False},
            }
            
        except Exception as e:
            logger.error(f"Error handling RAG request: {e}", exc_info=True)
            return {
                "response": "I encountered an error retrieving clinic information. Please try again or contact the clinic directly.",
                "action": "rag_error",
                "metadata": {"module": "rag_info", "error": str(e)},
            }
