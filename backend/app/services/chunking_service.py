"""Service for chunking text documents into smaller pieces for embedding.

This service implements various chunking strategies optimized for medical text:
1. Fixed-size chunking with configurable overlap
2. Semantic chunking that preserves sentence and paragraph boundaries
3. Markdown-aware chunking that maintains document structure

It also includes special handling for medical terminology to ensure related
concepts stay together in the same chunk.
"""

import logging
import os
import pathlib
import re
from enum import Enum
from typing import Optional, Union

import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from pydantic import BaseModel, Field

# Configure logging
logger = logging.getLogger(__name__)

# Configure NLTK data path from environment variable
if "NLTK_DATA" in os.environ:
    nltk.data.path.append(os.environ["NLTK_DATA"])

project_nltk_data = pathlib.Path(__file__).parent.parent.parent.parent / "nltk_data"
if project_nltk_data.exists():
    nltk.data.path.append(str(project_nltk_data))

# Download required NLTK resources if not already present
try:
    nltk.data.find("tokenizers/punkt")
except LookupError:
    try:
        logger.info("Downloading NLTK punkt data...")
        nltk.download("punkt", quiet=True)
        logger.info("NLTK punkt data downloaded successfully.")
    except Exception as e:
        logger.warning(f"Failed to download NLTK punkt data: {e}")
        logger.warning(
            "Chunking service will use simple regex-based tokenization as fallback."
        )

# Try to load medical terminology list if available
try:
    from app.utils.medical_terms import MEDICAL_TERMS
except ImportError:
    logger.warning(
        "Medical terms list not found. Medical term preservation will be limited."
    )
    MEDICAL_TERMS = set()


class ChunkingStrategy(str, Enum):
    """Enum for different chunking strategies."""

    FIXED = "fixed"  # Fixed-size chunks with configurable overlap
    SEMANTIC = "semantic"  # Preserve sentence/paragraph boundaries
    MARKDOWN_AWARE = "markdown_aware"  # Maintain tables, lists, and header+content


class DocumentType(str, Enum):
    """Enum for different document types with their default chunk sizes."""

    CLINICAL_NOTES = "clinical_notes"  # [256, 512] tokens
    PATIENT_RECORDS = "patient_records"  # [128, 384] tokens
    CLINIC_PAGES = "clinic_pages"  # [512, 1024] tokens
    GENERAL = "general"  # Default for any other document type


class ChunkingConfig(BaseModel):
    """Configuration for chunking parameters."""

    chunk_sizes: dict[DocumentType, list[int]] = Field(
        default_factory=lambda: {
            DocumentType.CLINICAL_NOTES: [256, 512],
            DocumentType.PATIENT_RECORDS: [128, 384],
            DocumentType.CLINIC_PAGES: [512, 1024],
            DocumentType.GENERAL: [256],
        }
    )
    overlap_percentage: int = Field(
        default=10, ge=0, le=50
    )  # Overlap between chunks as percentage
    default_strategy: ChunkingStrategy = Field(default=ChunkingStrategy.SEMANTIC)
    preserve_medical_terms: bool = Field(
        default=True
    )  # Whether to try to keep medical terms together


class TextChunk(BaseModel):
    """Represents a chunk of text with metadata."""

    text: str
    chunk_index: int
    chunk_size: int  # Size in tokens
    total_chunks: int
    strategy_used: ChunkingStrategy
    document_type: DocumentType


class ChunkingService:
    """Service for chunking text documents using various strategies."""

    def __init__(self, config: Optional[ChunkingConfig] = None):
        """Initialize the chunking service with the given configuration.

        Args:
            config: Optional chunking configuration. If not provided, default values will be used.
        """
        self.config = config or ChunkingConfig()
        self._medical_term_pattern = None
        if MEDICAL_TERMS and self.config.preserve_medical_terms:
            # Create a regex pattern for medical terms (for faster matching)
            # Sort by length (longest first) to ensure longer terms are matched first
            sorted_terms = sorted(MEDICAL_TERMS, key=len, reverse=True)
            pattern = (
                r"\b(" + "|".join(re.escape(term) for term in sorted_terms) + r")\b"
            )
            self._medical_term_pattern = re.compile(pattern, re.IGNORECASE)

    def chunk_document(
        self,
        text: str,
        document_type: Union[DocumentType, str],
        strategy: Optional[ChunkingStrategy] = None,
        custom_chunk_size: Optional[int] = None,
    ) -> list[TextChunk]:
        """Chunk a document into smaller pieces using the specified strategy.

        Args:
            text: The text to chunk
            document_type: The type of document (determines default chunk sizes)
            strategy: The chunking strategy to use (defaults to config.default_strategy)
            custom_chunk_size: Optional custom chunk size to use instead of the default for the document type

        Returns:
            List of TextChunk objects
        """
        # Normalize document_type to enum if string is provided
        if isinstance(document_type, str):
            try:
                document_type = DocumentType(document_type)
            except ValueError:
                logger.warning(
                    f"Unknown document type: {document_type}. Using GENERAL."
                )
                document_type = DocumentType.GENERAL

        # Use default strategy if none specified
        strategy = strategy or self.config.default_strategy

        # Get chunk sizes for this document type
        chunk_sizes = self.config.chunk_sizes.get(
            document_type, self.config.chunk_sizes[DocumentType.GENERAL]
        )

        # If custom chunk size is provided, use only that size
        if custom_chunk_size is not None:
            chunk_sizes = [custom_chunk_size]

        # Apply the appropriate chunking strategy
        all_chunks = []
        for chunk_size in chunk_sizes:
            if strategy == ChunkingStrategy.FIXED:
                chunks = self._fixed_size_chunking(text, chunk_size)
            elif strategy == ChunkingStrategy.SEMANTIC:
                chunks = self._semantic_chunking(text, chunk_size)
            elif strategy == ChunkingStrategy.MARKDOWN_AWARE:
                chunks = self._markdown_aware_chunking(text, chunk_size)
            else:
                logger.warning(
                    f"Unknown chunking strategy: {strategy}. Using SEMANTIC."
                )
                chunks = self._semantic_chunking(text, chunk_size)

            # Create TextChunk objects
            for i, chunk_text in enumerate(chunks):
                all_chunks.append(
                    TextChunk(
                        text=chunk_text,
                        chunk_index=i,
                        chunk_size=self._count_tokens(chunk_text),
                        total_chunks=len(chunks),
                        strategy_used=strategy,
                        document_type=document_type,
                    )
                )

        return all_chunks

    def _count_tokens(self, text: str) -> int:
        """Count the number of tokens in the text.

        This is a simple approximation. For production, consider using
        the same tokenizer as your embedding model.

        Args:
            text: The text to count tokens for

        Returns:
            Approximate token count
        """
        return len(word_tokenize(text))

    def _fixed_size_chunking(self, text: str, chunk_size: int) -> list[str]:
        """Chunk text into fixed-size chunks with configurable overlap.

        Args:
            text: The text to chunk
            chunk_size: Target chunk size in tokens

        Returns:
            List of text chunks
        """
        words = word_tokenize(text)
        if not words:
            return []

        # Calculate overlap in tokens
        overlap = int(chunk_size * self.config.overlap_percentage / 100)
        stride = chunk_size - overlap

        # Ensure stride is at least 1 to avoid infinite loop
        stride = max(1, stride)

        chunks = []
        for i in range(0, len(words), stride):
            # Get chunk_size tokens or remaining tokens if less than chunk_size
            chunk_words = words[i : i + chunk_size]
            if (
                len(chunk_words) < chunk_size * 0.5 and chunks
            ):  # If chunk is less than half the target size and not the first chunk
                # Add these words to the previous chunk instead of creating a tiny chunk
                chunks[-1] = chunks[-1] + " " + " ".join(chunk_words)
            else:
                chunks.append(" ".join(chunk_words))

        return chunks

    def _semantic_chunking(self, text: str, target_chunk_size: int) -> list[str]:
        """Chunk text while preserving sentence and paragraph boundaries.

        Args:
            text: The text to chunk
            target_chunk_size: Target chunk size in tokens

        Returns:
            List of text chunks
        """
        # Split text into paragraphs
        paragraphs = re.split(r"\n\s*\n", text)

        # Split paragraphs into sentences
        sentences = []
        for para in paragraphs:
            para_sentences = sent_tokenize(para)
            # Add paragraph boundary marker
            if para_sentences:
                sentences.extend(para_sentences)
                sentences.append("\n\n")  # Paragraph boundary marker

        # Remove the last paragraph boundary if it exists
        if sentences and sentences[-1] == "\n\n":
            sentences.pop()

        # Preserve medical terms if enabled
        if self.config.preserve_medical_terms and self._medical_term_pattern:
            sentences = self._preserve_medical_terms(sentences)

        chunks = []
        current_chunk = []
        current_token_count = 0

        for sentence in sentences:
            sentence_token_count = self._count_tokens(sentence)

            # If adding this sentence would exceed the target size and we already have content,
            # finish the current chunk and start a new one
            if (
                current_token_count + sentence_token_count > target_chunk_size
                and current_token_count > 0
                and sentence != "\n\n"
            ):
                chunks.append(" ".join(current_chunk))
                current_chunk = []
                current_token_count = 0

            # Add the sentence to the current chunk
            if sentence == "\n\n":
                # Handle paragraph boundary
                if current_chunk:
                    current_chunk.append(sentence.strip())
            else:
                current_chunk.append(sentence)
                current_token_count += sentence_token_count

        # Add the last chunk if it has content
        if current_chunk:
            chunks.append(" ".join(current_chunk))

        return chunks

    def _markdown_aware_chunking(self, text: str, target_chunk_size: int) -> list[str]:
        """Chunk markdown text while maintaining document structure.

        This preserves tables, lists, and header+content relationships.

        Args:
            text: The markdown text to chunk
            target_chunk_size: Target chunk size in tokens

        Returns:
            List of text chunks
        """
        # Split text into markdown blocks
        blocks = self._split_markdown_blocks(text)

        chunks = []
        current_chunk = []
        current_token_count = 0
        header_block = None
        in_list = False
        list_blocks = []

        for block in blocks:
            block_token_count = self._count_tokens(block)

            # Check if this is a header
            is_header = bool(re.match(r"^#{1,6}\s+.+", block))

            # Check if this is a list item
            is_list_item = bool(
                re.match(
                    r"^\s*([*\-+]|\d+\.)\s",
                    block.strip().split("\n")[0] if "\n" in block else block,
                )
            )

            # If we're in a list and this is another list item, add it to list_blocks
            if in_list and is_list_item:
                list_blocks.append(block)
                continue

            # If we're in a list and this is not a list item, end the list
            if in_list and not is_list_item:
                in_list = False
                # Calculate the total token count for all list blocks
                list_token_count = sum(self._count_tokens(b) for b in list_blocks)

                # If adding the entire list would exceed the target size and we already have content,
                # finish the current chunk and start a new one with the list
                if (
                    current_token_count + list_token_count > target_chunk_size
                    and current_token_count > 0
                ):
                    chunks.append("\n\n".join(current_chunk))
                    current_chunk = []
                    current_token_count = 0
                    # If we had a header, add it to the new chunk
                    if header_block:
                        current_chunk.append(header_block)
                        current_token_count += self._count_tokens(header_block)

                # Add all list blocks to the current chunk
                current_chunk.extend(list_blocks)
                current_token_count += list_token_count
                list_blocks = []

            # If this is a list item and we're not in a list, start a new list
            if is_list_item and not in_list:
                in_list = True
                list_blocks = [block]
                continue

            # If adding this block would exceed the target size and we already have content,
            # finish the current chunk and start a new one
            if (
                current_token_count + block_token_count > target_chunk_size
                and current_token_count > 0
                and not self._is_continuation_block(
                    block, current_chunk[-1] if current_chunk else ""
                )
            ):
                chunks.append("\n\n".join(current_chunk))
                current_chunk = []
                current_token_count = 0
                # If we had a header, add it to the new chunk
                if header_block:
                    current_chunk.append(header_block)
                    current_token_count += self._count_tokens(header_block)

            # Update header reference if this is a header
            if is_header:
                header_block = block

            # Add the block to the current chunk
            current_chunk.append(block)
            current_token_count += block_token_count

        # If we're still in a list at the end, add all list blocks to the current chunk
        if in_list and list_blocks:
            current_chunk.extend(list_blocks)

        # Add the last chunk if it has content
        if current_chunk:
            chunks.append("\n\n".join(current_chunk))

        return chunks

    def _split_markdown_blocks(self, text: str) -> list[str]:
        """Split markdown text into logical blocks.

        This keeps tables, lists, and code blocks intact.

        Args:
            text: The markdown text to split

        Returns:
            List of markdown blocks
        """
        lines = text.split("\n")
        blocks = []
        current_block = []
        in_code_block = False
        in_table = False
        in_list = False
        list_type = None  # To track whether we're in a bullet or numbered list

        for line in lines:
            # Check for code block delimiter
            if line.strip().startswith("```"):
                in_code_block = not in_code_block
                current_block.append(line)
                continue

            # If in code block, just add the line
            if in_code_block:
                current_block.append(line)
                continue

            # Check for table markers
            if line.strip().startswith("|") and line.strip().endswith("|"):
                if not in_table:
                    # Start of a new table
                    if current_block:
                        blocks.append("\n".join(current_block))
                        current_block = []
                    in_table = True
                current_block.append(line)
                continue
            elif in_table and line.strip() == "":
                # End of table
                blocks.append("\n".join(current_block))
                current_block = []
                in_table = False
                continue

            # Check for list items
            bullet_list_match = re.match(r"^\s*([*\-+])\s", line)
            numbered_list_match = re.match(r"^\s*(\d+\.)\s", line)

            # Determine the type of list item (if any)
            current_list_type = None
            if bullet_list_match:
                current_list_type = "bullet"
            elif numbered_list_match:
                current_list_type = "numbered"

            # Handle list items
            if current_list_type:
                if not in_list:
                    # Start of a new list
                    if current_block:
                        blocks.append("\n".join(current_block))
                        current_block = []
                    in_list = True
                    list_type = current_list_type
                elif list_type != current_list_type:
                    # Switching between bullet and numbered lists
                    blocks.append("\n".join(current_block))
                    current_block = []
                    list_type = current_list_type
                current_block.append(line)
                continue
            elif in_list:
                # Check if this is a continuation of a list item (indented content)
                if line.strip() == "" or line.startswith("    "):
                    current_block.append(line)
                    # Only end the list if the line is blank
                    if line.strip() == "":
                        in_list = False
                        list_type = None
                    continue
                else:
                    # End of list
                    blocks.append("\n".join(current_block))
                    current_block = []
                    in_list = False
                    list_type = None

            # Check for headers
            if re.match(r"^#{1,6}\s+.+", line):
                # Headers start a new block
                if current_block:
                    blocks.append("\n".join(current_block))
                    current_block = []
                current_block.append(line)
                continue

            # Handle blank lines as potential block separators
            if line.strip() == "" and current_block and current_block[-1].strip() == "":
                # Two consecutive blank lines - end the block
                blocks.append("\n".join(current_block))
                current_block = []
                continue

            # Add the line to the current block
            current_block.append(line)

        # Add the last block if it has content
        if current_block:
            blocks.append("\n".join(current_block))

        # Post-process blocks to combine related list blocks
        processed_blocks = []
        i = 0
        while i < len(blocks):
            current = blocks[i]

            # Check if this is a list block
            is_bullet_list = bool(
                re.match(
                    r"^\s*[*\-+]\s",
                    current.split("\n")[0] if "\n" in current else current,
                )
            )
            is_numbered_list = bool(
                re.match(
                    r"^\s*\d+\.\s",
                    current.split("\n")[0] if "\n" in current else current,
                )
            )

            if is_bullet_list or is_numbered_list:
                # Start a combined list block
                combined_list = [current]
                list_type = "bullet" if is_bullet_list else "numbered"

                # Look ahead for more list blocks of the same type
                j = i + 1
                while j < len(blocks):
                    next_block = blocks[j]
                    next_is_bullet = bool(
                        re.match(
                            r"^\s*[*\-+]\s",
                            (
                                next_block.split("\n")[0]
                                if "\n" in next_block
                                else next_block
                            ),
                        )
                    )
                    next_is_numbered = bool(
                        re.match(
                            r"^\s*\d+\.\s",
                            (
                                next_block.split("\n")[0]
                                if "\n" in next_block
                                else next_block
                            ),
                        )
                    )

                    if (list_type == "bullet" and next_is_bullet) or (
                        list_type == "numbered" and next_is_numbered
                    ):
                        combined_list.append(next_block)
                        j += 1
                    else:
                        break

                # Add the combined list as a single block
                processed_blocks.append("\n\n".join(combined_list))
                i = j  # Skip the blocks we've combined
            else:
                processed_blocks.append(current)
                i += 1

        return processed_blocks

    def _is_continuation_block(self, current_block: str, previous_block: str) -> bool:
        """Check if the current block is a continuation of the previous block.

        Args:
            current_block: The current markdown block
            previous_block: The previous markdown block

        Returns:
            True if the current block is a continuation of the previous block
        """
        # Check if the current block is a list item that continues a list
        if re.match(r"^\s*([*\-+]|\d+\.)\s", current_block) and re.match(
            r"^\s*([*\-+]|\d+\.)\s", previous_block
        ):
            return True

        # Check if the current block is part of a table that continues from the previous block
        if current_block.strip().startswith("|") and previous_block.strip().startswith(
            "|"
        ):
            return True

        return False

    def _preserve_medical_terms(self, sentences: list[str]) -> list[str]:
        """Attempt to keep medical terms together by merging sentences.

        Args:
            sentences: List of sentences to process

        Returns:
            Modified list of sentences with medical terms preserved
        """
        if not self._medical_term_pattern:
            return sentences

        result = []
        i = 0
        while i < len(sentences):
            current = sentences[i]

            # Skip paragraph boundaries
            if current == "\n\n":
                result.append(current)
                i += 1
                continue

            # Look for medical terms that might be split across sentence boundaries
            if i < len(sentences) - 1 and sentences[i + 1] != "\n\n":
                combined = current + " " + sentences[i + 1]
                # Check if combining would preserve a medical term
                current_matches = set(
                    self._medical_term_pattern.findall(current.lower())
                )
                next_matches = set(
                    self._medical_term_pattern.findall(sentences[i + 1].lower())
                )
                combined_matches = set(
                    self._medical_term_pattern.findall(combined.lower())
                )

                # If combining sentences preserves medical terms that would otherwise be split
                if len(combined_matches) > len(current_matches) + len(next_matches):
                    result.append(combined)
                    i += 2  # Skip the next sentence since we combined it
                    continue

            # No special handling needed, add the sentence as is
            result.append(current)
            i += 1

        return result


# Singleton instance
_chunking_service = None


def get_chunking_service(config: Optional[ChunkingConfig] = None) -> ChunkingService:
    """Get or create the chunking service singleton.

    Args:
        config: Optional chunking configuration. If not provided, default values will be used.
            This is only used when creating the service for the first time.

    Returns:
        The chunking service singleton instance
    """
    global _chunking_service
    if _chunking_service is None:
        _chunking_service = ChunkingService(config)
    return _chunking_service
