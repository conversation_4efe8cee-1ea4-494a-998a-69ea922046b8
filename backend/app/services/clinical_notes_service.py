"""Service for AI-powered clinical note generation from chat conversations."""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app.core.llm.factory import LLMFactory
from app.models.chat_message import MessageSenderType
from app.models.clinical_note import ClinicalNote, ClinicalNoteStatus
from app.schemas.clinical_note import (
    ClinicalNoteCreate,
    GenerateClinicalNoteRequest,
    GenerateClinicalNoteResponse,
)
from app.schemas.chat import ChatMessageCreateInternal

logger = logging.getLogger(__name__)


class ClinicalNotesService:
    """Service for generating clinical notes from chat conversations using AI."""
    
    def __init__(self):
        """Initialize the service with LLM provider."""
        self.llm_provider = None
        self._initialize_llm()
        
    def _initialize_llm(self):
        """Initialize the LLM provider."""
        try:
            self.llm_provider = LLMFactory.create_provider("openai", model="gpt-4o-mini")
            logger.info("Initialized LLM provider for clinical notes")
        except Exception as e:
            logger.error(f"Failed to initialize LLM provider: {e}")
            raise
    
    async def generate_clinical_note(
        self,
        db: Session,
        request: GenerateClinicalNoteRequest,
        clinician_id: str
    ) -> GenerateClinicalNoteResponse:
        """
        Generate a clinical note from chat messages.
        
        Args:
            db: Database session
            request: Generation request parameters
            clinician_id: ID of the requesting clinician
            
        Returns:
            Generated clinical note response
        """
        start_time = time.time()
        
        # Get patient information (patient_id is the clerk_id)
        patient = crud.patient.get(db, id=request.patient_id)
        if not patient:
            raise ValueError(f"Patient {request.patient_id} not found")
        
        # Get appointment information if provided
        appointment = None
        if request.appointment_id:
            appointment = crud.appointment.get(db, id=request.appointment_id)
        
        # Fetch chat messages using patient ID
        messages = self._fetch_chat_messages(
            db, 
            patient_id=patient.id,  # patient.id is already the string clerk_id
            start_time=request.start_time,
            end_time=request.end_time,
            chat_session_id=request.chat_session_id
        )
        
        if not messages:
            logger.warning(
                f"No messages found for patient {patient.id} between "
                f"{request.start_time or 'any'} and {request.end_time or 'now'}"
            )
            raise ValueError("No messages found for the specified criteria")
        
        # Extract SOAP sections from chat
        soap_sections, confidence_score = await self._extract_soap_from_chat(
            messages=messages,
            patient=patient,
            appointment=appointment,
            note_type=request.note_type
        )
        
        # Generate billing code suggestions
        billing_suggestions = await self._suggest_billing_codes(
            soap_sections=soap_sections,
            patient=patient
        )
        
        # Create clinical note
        note_create = ClinicalNoteCreate(
            patient_id=request.patient_id,
            appointment_id=request.appointment_id,
            chat_session_id=request.chat_session_id,
            template_id=request.template_id,
            note_type=request.note_type,
            sections=soap_sections,
            raw_text=self._generate_raw_text(soap_sections),
            ai_confidence_score=confidence_score,
            suggested_icd10_codes=billing_suggestions.get("icd10_codes", []),
            suggested_cpt_codes=billing_suggestions.get("cpt_codes", [])
        )
        
        # Save to database
        clinical_note = crud.clinical_note.create_with_clinician(
            db, obj_in=note_create, clinician_id=clinician_id
        )
        
        # Create a chat message for the clinical note
        try:
            # Get clinician info for the message
            clinician = crud.clinician.get(db, id=clinician_id)
            clinician_name = f"Dr. {clinician.last_name}" if clinician else "Clinician"
            
            # Create the chat message
            chat_message_data = ChatMessageCreateInternal(
                patient_id=request.patient_id,
                sender_type="CLINICAL_NOTE",
                message_content=f"Clinical Note Generated: {request.note_type} by {clinician_name}",
                message_metadata={  # Changed from metadata to message_metadata
                    "clinical_note_id": str(clinical_note.id),
                    "note_type": request.note_type,
                    "sections": soap_sections,
                    "confidence_score": confidence_score,
                    "suggested_icd10_codes": billing_suggestions.get("icd10_codes", []),
                    "suggested_cpt_codes": billing_suggestions.get("cpt_codes", []),
                    "clinician_id": clinician_id,
                    "clinician_name": clinician_name,
                    "clinician_only": True,  # Flag for frontend filtering
                    "generated_at": datetime.utcnow().isoformat()
                },
                message_route=None  # Clinical notes are not routed
            )
            
            # Save the chat message
            crud.chat_message.create(db, obj_in=chat_message_data)
            logger.info(f"Created chat message for clinical note {clinical_note.id}")
            
        except Exception as e:
            # Log error but don't fail the clinical note creation
            logger.error(f"Failed to create chat message for clinical note: {e}")
        
        # Calculate generation time
        generation_time_ms = int((time.time() - start_time) * 1000)
        
        # Validate completeness and suggest edits
        suggested_edits = self._validate_note_completeness(soap_sections, request.note_type)
        
        return GenerateClinicalNoteResponse(
            note=clinical_note,
            confidence_score=confidence_score,
            messages_processed=len(messages),
            generation_time_ms=generation_time_ms,
            suggested_edits=suggested_edits
        )
    
    def _fetch_chat_messages(
        self,
        db: Session,
        patient_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        chat_session_id: Optional[UUID] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch chat messages for a patient within the specified time range.
        
        Args:
            db: Database session
            patient_id: Patient ID
            start_time: Start of conversation window
            end_time: End of conversation window
            chat_session_id: Specific chat session ID
            
        Returns:
            List of chat messages
        """
        # Default to last 24 hours if no time range specified
        if not end_time:
            end_time = datetime.utcnow()
        if not start_time:
            start_time = end_time - timedelta(hours=24)
        
        # Query messages
        query = db.query(crud.chat_message.model).filter(
            crud.chat_message.model.patient_id == patient_id,
            crud.chat_message.model.created_at >= start_time,
            crud.chat_message.model.created_at <= end_time
        )
        
        if chat_session_id:
            # For now, we'll filter by time range
            # TODO: Implement chat session grouping
            pass
        
        messages = query.order_by(crud.chat_message.model.created_at).all()
        
        logger.info(f"Found {len(messages)} chat messages for patient {patient_id}")
        
        # Convert to dict format for processing
        return [
            {
                "id": str(msg.id),
                "sender_type": msg.sender_type,
                "content": msg.message_content,
                "timestamp": msg.created_at,
                "metadata": msg.message_metadata or {}
            }
            for msg in messages
        ]
    
    async def _extract_soap_from_chat(
        self,
        messages: List[Dict[str, Any]],
        patient: Any,
        appointment: Optional[Any] = None,
        note_type: str = "SOAP"
    ) -> Tuple[Dict[str, Any], float]:
        """
        Extract SOAP sections from chat messages using LLM.
        
        Args:
            messages: Chat messages
            patient: Patient object
            appointment: Appointment object (optional)
            note_type: Type of note to generate
            
        Returns:
            Tuple of (SOAP sections dict, confidence score)
        """
        # Format messages for LLM
        formatted_messages = self._format_messages_for_llm(messages)
        
        # Build context
        patient_context = f"""
Patient: {patient.first_name} {patient.last_name}
DOB: {patient.date_of_birth}
Height: {patient.height_cm}cm if available
"""
        
        if appointment:
            patient_context += f"""
Appointment Date: {appointment.appointment_datetime}
Appointment Type: {appointment.appointment_type}
"""
        
        # Create prompt based on note type
        if note_type == "SOAP":
            system_prompt = self._get_soap_extraction_prompt()
        elif note_type == "K-SOAP":
            system_prompt = self._get_ksoap_extraction_prompt()
        else:
            system_prompt = self._get_progress_note_extraction_prompt()
        
        # Make LLM call using the correct method
        # Format the prompt for the LLM including JSON schema instructions
        full_prompt = f"""{system_prompt}

{patient_context}

CONVERSATION:
{formatted_messages}

Please extract the clinical information and structure it into {note_type} format.

Return your response as a JSON object with the following structure:
{{
    "sections": {{
        "subjective": "string",
        "objective": "string", 
        "assessment": "string",
        "plan": "string",
        "additional_notes": "string (optional)"
    }},
    "confidence_score": 0.0-1.0,
    "key_findings": ["string", ...],
    "missing_information": ["string", ...]
}}"""
        
        llm_response = await self.llm_provider.generate(prompt=full_prompt)
        
        # Parse response
        try:
            # Clean up response content - remove markdown formatting if present
            content = llm_response.content.strip()
            if content.startswith("```json"):
                content = content[7:]  # Remove ```json
            if content.endswith("```"):
                content = content[:-3]  # Remove trailing ```
            content = content.strip()
            
            result = json.loads(content)
            sections = result.get("sections", {})
            confidence = result.get("confidence_score", 0.8)
            
            # Add metadata
            sections["_metadata"] = {
                "key_findings": result.get("key_findings", []),
                "missing_information": result.get("missing_information", []),
                "extraction_timestamp": datetime.utcnow().isoformat()
            }
            
            return sections, confidence
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            # Return basic structure on error
            return {
                "subjective": "Error extracting clinical information",
                "objective": "",
                "assessment": "",
                "plan": "",
                "_metadata": {"error": str(e)}
            }, 0.0
    
    def _format_messages_for_llm(self, messages: List[Dict[str, Any]]) -> str:
        """Format chat messages for LLM processing."""
        formatted = []
        for msg in messages:
            sender = "Patient" if msg["sender_type"] == MessageSenderType.PATIENT else "AI/Clinician"
            timestamp = msg["timestamp"].strftime("%Y-%m-%d %H:%M")
            formatted.append(f"[{timestamp}] {sender}: {msg['content']}")
        return "\n".join(formatted)
    
    def _get_soap_extraction_prompt(self) -> str:
        """Get the system prompt for SOAP note extraction."""
        return """
You are a medical documentation specialist extracting clinical information from patient-clinician conversations.
Your task is to create a structured SOAP (Subjective, Objective, Assessment, Plan) note from the conversation.

Guidelines:
1. SUBJECTIVE: Include patient's chief complaint, history of present illness, symptoms, patient's own words
2. OBJECTIVE: Include any mentioned vital signs, physical findings, test results, observable data
3. ASSESSMENT: Clinical impression, differential diagnosis, patient progress
4. PLAN: Treatment recommendations, follow-up, patient education, referrals

Important:
- Use medical terminology appropriately
- Be concise but comprehensive
- Flag any critical findings
- Note any missing important information
- Maintain HIPAA compliance
- If information for a section is not available, note "Not documented in conversation"
"""
    
    def _get_ksoap_extraction_prompt(self) -> str:
        """Get the system prompt for K-SOAP note extraction."""
        return """
You are a medical documentation specialist extracting clinical information from patient-clinician conversations.
Your task is to create a structured K-SOAP note (a SOAP note with additional Key Findings section).

Guidelines:
1. KEY FINDINGS: Highlight 3-5 most important clinical findings or changes
2. SUBJECTIVE: Include patient's chief complaint, history of present illness, symptoms
3. OBJECTIVE: Include vital signs, physical findings, test results, weight changes
4. ASSESSMENT: Clinical impression, progress evaluation, risk assessment
5. PLAN: Treatment adjustments, follow-up, monitoring plan

Focus on:
- Weight management specifics
- Medication effects and side effects
- Lifestyle factors
- Patient adherence and barriers
"""
    
    def _get_progress_note_extraction_prompt(self) -> str:
        """Get the system prompt for progress note extraction."""
        return """
You are a medical documentation specialist creating a progress note from patient conversations.
Focus on changes since the last visit, current status, and ongoing management.

Structure the note to include:
- Interval history (what happened since last visit)
- Current symptoms and concerns
- Treatment response
- Adherence to plan
- New issues or complications
- Updated assessment
- Modifications to treatment plan
"""
    
    async def _suggest_billing_codes(
        self,
        soap_sections: Dict[str, Any],
        patient: Any
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Suggest ICD-10 and CPT codes based on the clinical note.
        
        Args:
            soap_sections: SOAP note sections
            patient: Patient object
            
        Returns:
            Dictionary with ICD-10 and CPT code suggestions
        """
        # Combine relevant sections for analysis
        clinical_content = f"""
Assessment: {soap_sections.get('assessment', '')}
Plan: {soap_sections.get('plan', '')}
Key Findings: {soap_sections.get('_metadata', {}).get('key_findings', [])}
"""
        
        # Format prompt for billing code suggestions
        billing_prompt = f"""You are a medical coding specialist. Based on the clinical documentation provided,
suggest appropriate ICD-10 diagnosis codes and CPT procedure codes.

Focus on:
- Weight management and obesity-related codes
- Medication management codes
- Appropriate E/M (Evaluation and Management) codes
- Any mentioned complications or comorbidities

Clinical Content:
{clinical_content}

Return your response as a JSON object with the following structure:
{{
    "icd10_codes": [
        {{
            "code": "string",
            "description": "string",
            "confidence": 0.0-1.0
        }},
        ...
    ],
    "cpt_codes": [
        {{
            "code": "string", 
            "description": "string",
            "confidence": 0.0-1.0
        }},
        ...
    ]
}}"""
        
        llm_response = await self.llm_provider.generate(prompt=billing_prompt)
        
        try:
            # Clean up response content - remove markdown formatting if present
            content = llm_response.content.strip()
            if content.startswith("```json"):
                content = content[7:]  # Remove ```json
            if content.endswith("```"):
                content = content[:-3]  # Remove trailing ```
            content = content.strip()
            
            result = json.loads(content)
            return {
                "icd10_codes": result.get("icd10_codes", []),
                "cpt_codes": result.get("cpt_codes", [])
            }
        except Exception as e:
            logger.error(f"Failed to parse billing codes: {e}")
            logger.error(f"Raw content was: {llm_response.content[:200]}")
            return {"icd10_codes": [], "cpt_codes": []}
    
    def _generate_raw_text(self, sections: Dict[str, Any]) -> str:
        """Generate a formatted text version of the clinical note."""
        text_parts = []
        
        # Main sections
        for section_name in ["subjective", "objective", "assessment", "plan"]:
            if section_name in sections and sections[section_name]:
                text_parts.append(f"{section_name.upper()}:")
                text_parts.append(sections[section_name])
                text_parts.append("")  # Empty line
        
        # Additional notes if present
        if "additional_notes" in sections and sections["additional_notes"]:
            text_parts.append("ADDITIONAL NOTES:")
            text_parts.append(sections["additional_notes"])
        
        return "\n".join(text_parts).strip()
    
    def _validate_note_completeness(
        self, 
        sections: Dict[str, Any], 
        note_type: str
    ) -> Optional[List[str]]:
        """
        Validate the completeness of the clinical note and suggest edits.
        
        Args:
            sections: Note sections
            note_type: Type of note
            
        Returns:
            List of suggested edits or None if complete
        """
        suggestions = []
        
        # Check required sections
        required_sections = ["subjective", "objective", "assessment", "plan"]
        for section in required_sections:
            if not sections.get(section) or sections[section].strip() == "":
                suggestions.append(f"Missing {section} section")
            elif len(sections[section]) < 20:  # Too brief
                suggestions.append(f"{section.capitalize()} section may be too brief")
        
        # Check for specific content based on note type
        if note_type in ["SOAP", "K-SOAP"]:
            assessment = sections.get("assessment", "").lower()
            if "weight" not in assessment and "bmi" not in assessment:
                suggestions.append("Consider documenting weight/BMI status in assessment")
            
            plan = sections.get("plan", "").lower()
            if "follow" not in plan and "appointment" not in plan:
                suggestions.append("Consider specifying follow-up timeline in plan")
        
        # Check for missing key information from metadata
        missing_info = sections.get("_metadata", {}).get("missing_information", [])
        for info in missing_info:
            suggestions.append(f"Missing information: {info}")
        
        return suggestions if suggestions else None


# Service instance (singleton)
_clinical_notes_service = None

def get_clinical_notes_service() -> ClinicalNotesService:
    """Get or create the clinical notes service instance."""
    global _clinical_notes_service
    if _clinical_notes_service is None:
        _clinical_notes_service = ClinicalNotesService()
    return _clinical_notes_service

# For backward compatibility
clinical_notes_service = get_clinical_notes_service()