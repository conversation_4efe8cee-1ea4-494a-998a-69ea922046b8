"""
AI-powered dashboard prioritization service.

This service analyzes various data points to prioritize dashboard cards
and generate insights for clinicians.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.core.config import settings
from app.core.llm.factory import LLMFactory
from app.models.side_effect_report import SeverityLevel, SideEffectReport

logger = logging.getLogger(__name__)


class DashboardAIService:
    """Service for AI-powered dashboard prioritization and insights."""

    def __init__(self, db: Session = None):
        """Initialize the dashboard AI service."""
        # Initialize LLM provider only when needed
        self.llm_provider = None
        self.db = db

    def get_ai_summary_data(self, clinician_id: str) -> Any:
        """Get AI summary data for the dashboard banner."""
        if not self.db:
            raise ValueError("Database session not provided")
            
        # Get clinician
        clinician = crud.clinician.get(self.db, id=clinician_id)
        if not clinician:
            raise ValueError(f"Clinician {clinician_id} not found")
            
        # Get urgent items
        urgent_items = self._get_urgent_items(self.db, clinician)
        
        # Calculate total urgent items (critical + high alerts + critical side effects + overdue appointments)
        total_urgent = (
            urgent_items.get("critical_patient_alerts", 0) +
            urgent_items.get("high_patient_alerts", 0) +
            urgent_items.get("critical_side_effects", 0) +
            urgent_items.get("overdue_appointments", 0)
        )
        
        # Calculate at-risk patients (those with critical or high alerts)
        at_risk_patients = (
            urgent_items.get("critical_patient_alerts", 0) +
            urgent_items.get("high_patient_alerts", 0)
        )
        
        # Get pending reviews
        task_counts = self._get_task_counts(self.db, clinician)
        pending_reviews = (
            task_counts.pending_medication_requests +
            task_counts.pending_lab_results
        )
        
        # Return data in the format expected by the dashboard endpoint
        from types import SimpleNamespace
        return SimpleNamespace(
            urgent_items=total_urgent,
            at_risk_patients=at_risk_patients,
            pending_reviews=pending_reviews
        )

    async def generate_prioritized_dashboard(
        self,
        db: Session,
        clinician: models.Clinician,
    ) -> schemas.dashboard.AIPrioritizedDashboardResponse:
        """
        Generate an AI-prioritized dashboard for the clinician.
        
        Args:
            db: Database session
            clinician: The clinician to generate the dashboard for
            
        Returns:
            AIPrioritizedDashboardResponse with prioritized cards and insights
        """
        try:
            # Gather all relevant data
            task_counts = self._get_task_counts(db, clinician)
            urgent_items = self._get_urgent_items(db, clinician)
            recent_activity = self._get_recent_activity(db, clinician)
            
            # Generate priority scores for each card
            card_priorities = self._calculate_card_priorities(
                task_counts, urgent_items, recent_activity
            )
            
            # Generate AI insights
            insights = await self._generate_insights(
                db, clinician, task_counts, urgent_items, recent_activity
            )
            
            # Generate summary
            summary = await self._generate_summary(
                task_counts, urgent_items, card_priorities
            )
            
            return schemas.dashboard.AIPrioritizedDashboardResponse(
                summary=summary,
                prioritized_cards=card_priorities,
                insights=insights,
                task_counts=task_counts,
                generated_at=datetime.now(timezone.utc).isoformat(),
            )
            
        except Exception as e:
            logger.error(
                f"Error generating prioritized dashboard for clinician {clinician.id}: {str(e)}",
                exc_info=True,
            )
            # Return a default dashboard on error
            return self._get_default_dashboard(task_counts)

    def _get_task_counts(
        self, db: Session, clinician: models.Clinician
    ) -> schemas.dashboard.DashboardTaskSummaryResponse:
        """Get current task counts for the clinician."""
        pending_med_requests = (
            crud.medication_request.get_pending_request_count_for_clinician(
                db=db, clinician_id=clinician.id
            )
        )
        pending_lab_results = crud.lab_result.get_pending_review_count_by_clinician(
            db=db, clinician_id=clinician.id
        )
        unread_messages = crud.chat_message.get_unread_count_for_clinician(
            db=db, clinician_id=clinician.id
        )
        pending_appointments = (
            crud.appointment_request.get_pending_request_count_for_clinician(
                db=db, clinician_id=clinician.id
            )
        )
        
        return schemas.dashboard.DashboardTaskSummaryResponse(
            pending_medication_requests=pending_med_requests,
            pending_lab_results=pending_lab_results,
            unread_patient_messages=unread_messages,
            pending_appointment_requests=pending_appointments,
        )

    def _get_urgent_items(
        self, db: Session, clinician: models.Clinician
    ) -> Dict[str, Any]:
        """Get urgent items requiring immediate attention."""
        urgent_items = {
            "critical_side_effects": 0,
            "critical_patient_alerts": 0,
            "overdue_appointments": 0,
            "high_risk_patients": 0,
            "urgent_messages": 0,
        }
        
        # Get all patient IDs for this clinician
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician.id, limit=10000
        )
        patient_ids = [p.id for p in clinician_patients]
                
        # Get critical side effects from the last 24 hours
        yesterday = datetime.now(timezone.utc) - timedelta(days=1)
        critical_side_effects = (
            db.query(SideEffectReport)
            .filter(
                and_(
                    SideEffectReport.patient_id.in_(patient_ids),
                    SideEffectReport.reported_at >= yesterday,
                    SideEffectReport.severity.in_([
                        SeverityLevel.MODERATE,
                        SeverityLevel.MAJOR,
                    ]),
                )
            )
            .count()
        )
        urgent_items["critical_side_effects"] = critical_side_effects
        
        # Get critical and high patient alerts
        all_alerts = crud.patient_alert.get_by_clinician(
            db=db,
            clinician_id=clinician.id,
            status="new",
            limit=100
        )
        critical_alert_count = sum(1 for alert in all_alerts if alert.severity == "critical")
        high_alert_count = sum(1 for alert in all_alerts if alert.severity == "high")
        urgent_items["critical_patient_alerts"] = critical_alert_count
        urgent_items["high_patient_alerts"] = high_alert_count
        
        # Get overdue appointments (past scheduled time but not completed)
        now = datetime.now(timezone.utc)
        overdue_appointments = (
            db.query(models.Appointment)
            .filter(
                and_(
                    models.Appointment.clinician_id == clinician.id,
                    models.Appointment.appointment_datetime < now,
                    models.Appointment.status.in_(["scheduled", "confirmed"]),
                )
            )
            .count()
        )
        urgent_items["overdue_appointments"] = overdue_appointments
        
        # TODO: Add logic for high-risk patients based on various factors
        # TODO: Add logic for urgent messages based on keywords or patient status
        
        return urgent_items

    def _get_recent_activity(
        self, db: Session, clinician: models.Clinician
    ) -> Dict[str, Any]:
        """Get recent activity metrics."""
        last_hour = datetime.now(timezone.utc) - timedelta(hours=1)
        last_day = datetime.now(timezone.utc) - timedelta(days=1)
        
        # Get all patient IDs for this clinician
        clinician_patients = crud.clinician.get_patients_for_clinician(
            db=db, clinician_id=clinician.id, limit=10000
        )
        patient_ids = [p.id for p in clinician_patients]
                
        return {
            "messages_last_hour": db.query(models.ChatMessage)
            .filter(
                and_(
                    models.ChatMessage.patient_id.in_(patient_ids),
                    models.ChatMessage.created_at >= last_hour,
                )
            )
            .count(),
            "side_effects_last_day": db.query(SideEffectReport)
            .filter(
                and_(
                    SideEffectReport.patient_id.in_(patient_ids),
                    SideEffectReport.reported_at >= last_day,
                )
            )
            .count(),
        }

    def _calculate_card_priorities(
        self,
        task_counts: schemas.dashboard.DashboardTaskSummaryResponse,
        urgent_items: Dict[str, Any],
        recent_activity: Dict[str, Any],
    ) -> List[schemas.dashboard.PrioritizedCard]:
        """Calculate priority scores for each dashboard card."""
        cards = []
        
        # Patient Alerts - highest priority if there are critical or high alerts
        total_urgent_alerts = urgent_items["critical_patient_alerts"] + urgent_items.get("high_patient_alerts", 0)
        alerts_priority = 1 if urgent_items["critical_patient_alerts"] > 0 else (2 if urgent_items.get("high_patient_alerts", 0) > 0 else 4)
        
        alert_reason = ""
        if urgent_items["critical_patient_alerts"] > 0:
            alert_reason = f"{urgent_items['critical_patient_alerts']} critical patient alert{'s' if urgent_items['critical_patient_alerts'] > 1 else ''} need{'s' if urgent_items['critical_patient_alerts'] == 1 else ''} immediate attention"
        elif urgent_items.get("high_patient_alerts", 0) > 0:
            alert_reason = f"{urgent_items['high_patient_alerts']} high priority alert{'s' if urgent_items['high_patient_alerts'] > 1 else ''} require{'s' if urgent_items['high_patient_alerts'] == 1 else ''} attention"
        else:
            alert_reason = "No urgent alerts at this time"
            
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="patient_alerts",
                priority=alerts_priority,
                reason=alert_reason,
                highlighted_data={
                    "critical_count": urgent_items["critical_patient_alerts"],
                    "high_count": urgent_items.get("high_patient_alerts", 0),
                    "critical_patient_alerts": urgent_items["critical_patient_alerts"],
                    "high_patient_alerts": urgent_items.get("high_patient_alerts", 0)
                },
            )
        )
        
        # Today's Appointments - high priority if overdue
        appointments_priority = 1 if urgent_items["overdue_appointments"] > 0 else 3
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="todays_appointments",
                priority=appointments_priority,
                reason=(
                    f"{urgent_items['overdue_appointments']} appointments are overdue"
                    if urgent_items["overdue_appointments"] > 0
                    else "All appointments on schedule"
                ),
                highlighted_data={
                    "overdue_count": urgent_items["overdue_appointments"]
                },
            )
        )
        
        # Pending Tasks - priority based on count
        tasks_priority = 2 if task_counts.pending_medication_requests > 5 else 5
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="pending_tasks",
                priority=tasks_priority,
                reason=f"{task_counts.pending_medication_requests} medication requests pending review",
                highlighted_data={
                    "medication_requests": task_counts.pending_medication_requests,
                    "lab_results": task_counts.pending_lab_results,
                },
            )
        )
        
        # Side Effect Reports - priority based on recent activity
        side_effects_priority = 3 if recent_activity["side_effects_last_day"] > 3 else 6
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="side_effect_reports",
                priority=side_effects_priority,
                reason=(
                    f"{recent_activity['side_effects_last_day']} new reports in last 24 hours"
                ),
                highlighted_data={
                    "recent_count": recent_activity["side_effects_last_day"]
                },
            )
        )
        
        # Quick Actions - always visible but lower priority
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="quick_actions",
                priority=7,
                reason="Quick access to common actions",
            )
        )
        
        # Recent Activity - lowest priority
        cards.append(
            schemas.dashboard.PrioritizedCard(
                card_type="recent_activity",
                priority=8,
                reason="Overview of recent system activity",
            )
        )
        
        # Sort by priority
        cards.sort(key=lambda x: x.priority)
        
        return cards

    async def _generate_insights(
        self,
        db: Session,
        clinician: models.Clinician,
        task_counts: schemas.dashboard.DashboardTaskSummaryResponse,
        urgent_items: Dict[str, Any],
        recent_activity: Dict[str, Any],
    ) -> List[schemas.dashboard.InsightItem]:
        """Generate AI insights based on current data."""
        insights = []
        
        # Critical alerts
        if urgent_items["critical_side_effects"] > 0:
            insights.append(
                schemas.dashboard.InsightItem(
                    type="alert",
                    message=f"{urgent_items['critical_side_effects']} patients reported moderate to major side effects in the last 24 hours",
                    severity="critical",
                    related_card="patient_alerts",
                )
            )
        
        # Overdue appointments
        if urgent_items["overdue_appointments"] > 0:
            insights.append(
                schemas.dashboard.InsightItem(
                    type="alert",
                    message=f"{urgent_items['overdue_appointments']} appointments are past their scheduled time",
                    severity="warning",
                    related_card="todays_appointments",
                )
            )
        
        # Workload recommendations
        if task_counts.pending_medication_requests > 10:
            insights.append(
                schemas.dashboard.InsightItem(
                    type="recommendation",
                    message="Consider batch-reviewing medication requests to improve efficiency",
                    severity="info",
                    related_card="pending_tasks",
                )
            )
        
        # Activity trends
        if recent_activity["messages_last_hour"] > 5:
            insights.append(
                schemas.dashboard.InsightItem(
                    type="trend",
                    message="High patient message volume detected - consider enabling AI auto-responses",
                    severity="info",
                    related_card=None,
                )
            )
        
        # Use LLM for additional insights if configured
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            try:
                # Initialize LLM provider if not already done
                if not self.llm_provider:
                    self.llm_provider = LLMFactory.create_provider(
                        name="openai",
                        api_key=settings.OPENAI_API_KEY,
                        model="gpt-3.5-turbo",
                    )
                
                prompt = f"""
                Based on the following clinician dashboard data, provide one additional insight:
                - Pending medication requests: {task_counts.pending_medication_requests}
                - Unread messages: {task_counts.unread_patient_messages}
                - Critical side effects: {urgent_items['critical_side_effects']}
                - Recent activity: {recent_activity['side_effects_last_day']} side effects in 24h
                
                Provide a brief, actionable insight (max 100 characters).
                """
                
                llm_insight = await self.llm_provider.generate_completion(
                    prompt=prompt,
                    max_tokens=50,
                    temperature=0.7,
                )
                
                if llm_insight:
                    insights.append(
                        schemas.dashboard.InsightItem(
                            type="recommendation",
                            message=llm_insight,
                            severity="info",
                            related_card=None,
                        )
                    )
            except Exception as e:
                logger.warning(f"Failed to generate LLM insight: {str(e)}")
        
        return insights

    async def _generate_summary(
        self,
        task_counts: schemas.dashboard.DashboardTaskSummaryResponse,
        urgent_items: Dict[str, Any],
        card_priorities: List[schemas.dashboard.PrioritizedCard],
    ) -> str:
        """Generate an AI summary of the current dashboard state."""
        # Create a human-readable summary
        urgent_count = sum(
            1 for card in card_priorities if card.priority <= 2
        )
        
        if urgent_count > 0:
            summary = f"🚨 {urgent_count} urgent items need your attention. "
        else:
            summary = "✅ All systems normal. "
        
        # Add task summary
        total_tasks = (
            task_counts.pending_medication_requests
            + task_counts.pending_lab_results
            + task_counts.unread_patient_messages
            + task_counts.pending_appointment_requests
        )
        
        if total_tasks > 0:
            summary += f"You have {total_tasks} pending tasks. "
        
        # Add personalized recommendation
        if urgent_items["critical_side_effects"] > 0:
            summary += "Focus on reviewing critical side effects first."
        elif task_counts.pending_medication_requests > 5:
            summary += "Consider batch-reviewing medication requests."
        else:
            summary += "Great job staying on top of your tasks!"
        
        return summary

    def _get_default_dashboard(
        self, task_counts: Optional[schemas.dashboard.DashboardTaskSummaryResponse] = None
    ) -> schemas.dashboard.AIPrioritizedDashboardResponse:
        """Return a default dashboard configuration."""
        if not task_counts:
            task_counts = schemas.dashboard.DashboardTaskSummaryResponse(
                pending_medication_requests=0,
                pending_lab_results=0,
                unread_patient_messages=0,
                pending_appointment_requests=0,
            )
        
        default_cards = [
            schemas.dashboard.PrioritizedCard(
                card_type="patient_alerts",
                priority=1,
                reason="Patient safety is top priority",
            ),
            schemas.dashboard.PrioritizedCard(
                card_type="todays_appointments",
                priority=2,
                reason="Stay on schedule",
            ),
            schemas.dashboard.PrioritizedCard(
                card_type="pending_tasks",
                priority=3,
                reason="Review pending items",
            ),
            schemas.dashboard.PrioritizedCard(
                card_type="side_effect_reports",
                priority=4,
                reason="Monitor patient reactions",
            ),
            schemas.dashboard.PrioritizedCard(
                card_type="quick_actions",
                priority=5,
                reason="Quick access to common tasks",
            ),
            schemas.dashboard.PrioritizedCard(
                card_type="recent_activity",
                priority=6,
                reason="Stay informed",
            ),
        ]
        
        return schemas.dashboard.AIPrioritizedDashboardResponse(
            summary="Dashboard loaded with default configuration",
            prioritized_cards=default_cards,
            insights=[],
            task_counts=task_counts,
            generated_at=datetime.now(timezone.utc).isoformat(),
        )
    
    async def generate_morning_brief(
        self,
        db: Session,
        clinician: models.Clinician,
    ) -> Dict[str, Any]:
        """
        Generate a morning brief with prioritized tasks and insights for the day.
        
        Args:
            db: Database session
            clinician: The clinician to generate the brief for
            
        Returns:
            Dict containing morning brief data with urgent items and insights
        """
        try:
            # Get all relevant data
            task_counts = self._get_task_counts(db, clinician)
            urgent_items = self._get_urgent_items(db, clinician)
            recent_activity = self._get_recent_activity(db, clinician)
            
            # Calculate total overdue tasks (medication requests past their review window)
            overdue_tasks = urgent_items.get("overdue_appointments", 0)
            
            # Build insights list
            insights = [
                f"You have {overdue_tasks} overdue tasks requiring immediate attention.",
                f"There are {urgent_items['critical_patient_alerts']} critical patient alerts to address today.",
                f"{urgent_items.get('pending_appointments', 0)} appointments need confirmation for today.",
            ]
            
            # Filter out zero-count insights
            insights = [insight for insight in insights if not insight.startswith("You have 0") and not insight.startswith("There are 0")]
            
            return {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "summary": await self._generate_summary(task_counts, urgent_items, []),
                "urgent_items": urgent_items,
                "insights": insights,
                "highlighted_data": {
                    "overdue_count": overdue_tasks,
                    "critical_count": urgent_items["critical_patient_alerts"],
                    "pending_count": urgent_items.get("pending_appointments", 0),
                    # Keep for backward compatibility
                    "critical_side_effects": urgent_items["critical_side_effects"],
                },
            }
            
        except Exception as e:
            logger.error(
                f"Error generating morning brief for clinician {clinician.id}: {str(e)}",
                exc_info=True,
            )
            # Return a default morning brief on error
            return {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "summary": "Good morning! Unable to generate detailed brief at this time.",
                "urgent_items": {},
                "insights": [],
                "highlighted_data": {
                    "overdue_count": 0,
                    "critical_count": 0,
                    "pending_count": 0,
                },
            }