import logging
import uuid
from typing import Optional, Dict, Any
import PyPDF2
from io import BytesIO
import requests
from urllib.parse import urlparse

from sqlalchemy.orm import Session
from sentence_transformers import SentenceTransformer
from azure.storage.blob import BlobServiceClient

from app.models.education_material import EducationMaterial
from app.schemas.content_chunk import ContentChunkCreate
from app.crud.crud_content_chunk import crud_content_chunk
from app.core.cache.rag_cache import rag_cache
from app.services.embedding_pipeline import (
    chunk_text, 
    generate_embeddings,
    CHUNK_SIZE,
    CHUNK_OVERLAP
)
from app.core.config import settings

logger = logging.getLogger(__name__)


def extract_text_from_pdf(file_url: str) -> Optional[str]:
    """Extract text content from a PDF file URL."""
    try:
        # Check if this is an Azure blob URL
        parsed_url = urlparse(file_url)
        if "blob.core.windows.net" in parsed_url.netloc:
            # Extract container and blob name from the URL
            # URL format: https://<account>.blob.core.windows.net/<container>/<blob_path>
            path_parts = parsed_url.path.strip('/').split('/', 1)
            if len(path_parts) != 2:
                logger.error(f"Invalid Azure blob URL format: {file_url}")
                return None
            
            container_name = path_parts[0]
            blob_name = path_parts[1]
            
            # Use Azure blob client to download the file
            try:
                blob_service_client = BlobServiceClient.from_connection_string(
                    settings.AZURE_STORAGE_CONNECTION_STRING
                )
                blob_client = blob_service_client.get_blob_client(
                    container=container_name,
                    blob=blob_name
                )
                
                # Download the blob content
                blob_data = blob_client.download_blob()
                pdf_content = blob_data.readall()
                
                # Read PDF content
                pdf_file = BytesIO(pdf_content)
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                
                # Extract text from all pages
                text_content = []
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
                
                return "\n".join(text_content)
                
            except Exception as e:
                logger.error(f"Error downloading PDF from Azure: {e}")
                return None
        else:
            # For non-Azure URLs, use regular HTTP download
            response = requests.get(file_url, timeout=30)
            response.raise_for_status()
            
            # Read PDF content
            pdf_file = BytesIO(response.content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            # Extract text from all pages
            text_content = []
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                if text:
                    text_content.append(text)
            
            return "\n".join(text_content)
    except Exception as e:
        logger.error(f"Error extracting text from PDF {file_url}: {e}")
        return None


def extract_text_from_document(file_url: str, file_type: str) -> Optional[str]:
    """Extract text from various document types."""
    # For now, we'll just handle PDFs
    # TODO: Add support for DOC, DOCX, HTML, TXT files
    if file_type == "PDF":
        return extract_text_from_pdf(file_url)
    else:
        logger.warning(f"Text extraction not yet implemented for {file_type}")
        return None


def process_education_material_for_rag(
    db: Session, 
    education_material: EducationMaterial
) -> bool:
    """
    Process an education material for RAG by extracting text, 
    chunking it, generating embeddings, and storing in ContentChunk table.
    
    Returns:
        bool: True if successfully processed, False otherwise
    """
    try:
        logger.info(f"Processing education material {education_material.id} for RAG")
        
        # Extract text content based on material type
        text_content = None
        
        if education_material.content_url:
            text_content = extract_text_from_document(
                education_material.content_url,
                education_material.type.value
            )
        
        if not text_content:
            # If no content URL or extraction failed, use title and description
            text_content = f"{education_material.title}\n\n{education_material.description or ''}"
        
        # Chunk the text
        chunks = chunk_text(text_content, size=CHUNK_SIZE, overlap=CHUNK_OVERLAP)
        if not chunks:
            logger.warning(f"No chunks generated for education material {education_material.id}")
            return False
        
        # Generate embeddings
        embeddings = generate_embeddings(chunks)
        if not embeddings or len(embeddings) != len(chunks):
            logger.error(f"Failed to generate embeddings for education material {education_material.id}")
            return False
        
        # Delete existing chunks for this education material
        # Query for chunks with matching education_material_id in metadata
        from app.models.content_chunk import ContentChunk
        existing_chunks = db.query(ContentChunk).filter(
            ContentChunk.metadata_.op('->>')('education_material_id') == str(education_material.id)
        ).all()
        
        for chunk in existing_chunks:
            db.delete(chunk)
        db.commit()
        
        logger.info(f"Deleted {len(existing_chunks)} existing chunks for education material {education_material.id}")
        
        # Create new chunks
        chunks_created = 0
        for i, (text_chunk, embedding) in enumerate(zip(chunks, embeddings)):
            chunk_data = ContentChunkCreate(
                # Note: education materials don't have scraped_page_id
                # We'll use metadata to link to education material
                scraped_page_id=None,  # Explicitly set to None for education materials
                chunk_text=text_chunk,
                embedding=embedding,
                metadata={
                    "source_type": "education_material",
                    "education_material_id": str(education_material.id),
                    "title": education_material.title,
                    "category": education_material.category,
                    "chunk_index": i,
                    "is_public": education_material.is_public,
                    "clinic_id": str(education_material.clinic_id) if education_material.clinic_id else None,
                }
            )
            
            try:
                new_chunk = crud_content_chunk.create(db=db, obj_in=chunk_data)
                chunks_created += 1
                
                # Cache the chunk
                if new_chunk and hasattr(new_chunk, "id"):
                    chunk_cache_data = {
                        "id": str(new_chunk.id),
                        "chunk_text": new_chunk.chunk_text,
                        "embedding": new_chunk.embedding,
                        "metadata": new_chunk.metadata_,
                    }
                    rag_cache.set_chunk(str(new_chunk.id), chunk_cache_data)
                    
            except Exception as e:
                logger.error(f"Failed to store chunk {i} for education material {education_material.id}: {e}")
        
        logger.info(f"Successfully created {chunks_created}/{len(chunks)} chunks for education material {education_material.id}")
        
        # Invalidate cache for the clinic if material is clinic-specific
        if education_material.clinic_id:
            rag_cache.invalidate_clinic_cache(str(education_material.clinic_id))
        
        return chunks_created > 0
        
    except Exception as e:
        logger.error(f"Error processing education material {education_material.id} for RAG: {e}", exc_info=True)
        return False


def process_all_education_materials_for_rag(db: Session, clinic_id: Optional[uuid.UUID] = None):
    """
    Process all education materials for RAG.
    If clinic_id is provided, only process materials for that clinic.
    """
    from app.crud.crud_education_material import education_material as crud_education_material
    
    try:
        # Get materials to process
        if clinic_id:
            materials = crud_education_material.get_by_clinic(
                db=db,
                clinic_id=str(clinic_id),
                limit=1000,
                include_public=True
            )
            logger.info(f"Processing {len(materials)} education materials for clinic {clinic_id}")
        else:
            materials = crud_education_material.get_multi(db=db, limit=1000)
            logger.info(f"Processing {len(materials)} education materials")
        
        success_count = 0
        for material in materials:
            if process_education_material_for_rag(db, material):
                success_count += 1
        
        logger.info(f"Successfully processed {success_count}/{len(materials)} education materials for RAG")
        
    except Exception as e:
        logger.error(f"Error processing education materials for RAG: {e}", exc_info=True)