import logging
import uuid
from typing import Optional

from sentence_transformers import SentenceTransformer
from sqlalchemy.orm import Session

from app.core.cache.rag_cache import rag_cache
from app.crud.crud_content_chunk import crud_content_chunk
from app.crud.crud_scraped_page import scraped_page as crud_scraped_page
from app.db.session import SessionLocal
from app.models.scraped_page import ScrapedPage
from app.schemas.content_chunk import ContentChunkCreate

logger = logging.getLogger(__name__)

# --- Configuration ---
# Choose a sentence-transformer model appropriate for your task
# Using 'all-mpnet-base-v2' which produces 768-dimensional embeddings to match database schema
# Other options: 'all-MiniLM-L6-v2' (384 dimensions), etc.
# Ensure the EMBEDDING_DIM in models/content_chunk.py matches the model's output dimension.
MODEL_NAME = "all-mpnet-base-v2"
# Simple chunking strategy (adjust as needed)
CHUNK_SIZE = 500  # Characters per chunk
CHUNK_OVERLAP = 50  # Characters overlap between chunks
# --- End Configuration ---

# Load the Sentence Transformer model (consider loading once globally if used frequently)
try:
    embedding_model = SentenceTransformer(MODEL_NAME)
    logger.info(f"SentenceTransformer model '{MODEL_NAME}' loaded successfully.")
except Exception as e:
    logger.error(
        f"Failed to load SentenceTransformer model '{MODEL_NAME}': {e}", exc_info=True
    )
    embedding_model = None


def chunk_text(
    text: str, size: int = CHUNK_SIZE, overlap: int = CHUNK_OVERLAP
) -> list[str]:
    """Splits text into overlapping chunks."""
    if not text:
        return []
    chunks = []
    start = 0
    while start < len(text):
        end = start + size
        chunks.append(text[start:end])
        start += size - overlap
        if start >= len(text):  # Avoid infinite loop on very short overlaps/texts
            break
    return chunks


def generate_embeddings(texts: list[str]) -> Optional[list[list[float]]]:
    """Generates embeddings for a list of text chunks with caching."""
    if not embedding_model:
        logger.error("Embedding model not loaded. Cannot generate embeddings.")
        return None

    try:
        logger.info(f"Generating embeddings for {len(texts)} chunks...")

        # Check cache for existing embeddings
        cached_embeddings = rag_cache.get_text_embeddings_batch(texts)

        # Identify texts that need embedding generation
        texts_to_encode = []
        text_indices = []
        embeddings_list = [None] * len(texts)

        for i, text in enumerate(texts):
            if text in cached_embeddings:
                embeddings_list[i] = cached_embeddings[text]
            else:
                texts_to_encode.append(text)
                text_indices.append(i)

        # Generate embeddings for uncached texts
        if texts_to_encode:
            logger.info(
                f"Generating embeddings for {len(texts_to_encode)} uncached texts"
            )
            # The model might return numpy arrays, convert to list for DB storage/JSON serialization
            new_embeddings = embedding_model.encode(
                texts_to_encode, convert_to_numpy=False
            )
            # Ensure embeddings are lists of floats
            new_embeddings_list = [list(map(float, emb)) for emb in new_embeddings]

            # Cache the new embeddings
            new_cache_entries = {}
            for text, embedding, idx in zip(
                texts_to_encode, new_embeddings_list, text_indices
            ):
                embeddings_list[idx] = embedding
                new_cache_entries[text] = embedding

            rag_cache.set_text_embeddings_batch(new_cache_entries)
            logger.info(f"Cached {len(new_cache_entries)} new embeddings")
        else:
            logger.info("All embeddings retrieved from cache")

        return embeddings_list
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}", exc_info=True)
        return None


def process_scraped_page_content(db: Session, scraped_page: ScrapedPage):
    """Chunks, embeds, and stores content for a single scraped page."""
    if not scraped_page or not scraped_page.cleaned_content:
        logger.warning(
            f"Skipping processing for ScrapedPage ID {scraped_page.id}: No content."
        )
        return

    logger.info(
        f"Processing content for ScrapedPage ID: {scraped_page.id}, URL: {scraped_page.source_url}"
    )

    # 1. Chunk the text
    text_chunks = chunk_text(scraped_page.cleaned_content)
    if not text_chunks:
        logger.warning(
            f"No text chunks generated for ScrapedPage ID: {scraped_page.id}"
        )
        return

    # 2. Generate embeddings (with caching)
    embeddings = generate_embeddings(text_chunks)
    if not embeddings or len(embeddings) != len(text_chunks):
        logger.error(
            f"Failed to generate embeddings or mismatch in count for ScrapedPage ID: {scraped_page.id}"
        )
        return

    # 3. Delete existing chunks for this page (optional, depends on update strategy)
    try:
        deleted_count = crud_content_chunk.delete_by_scraped_page(
            db=db, scraped_page_id=scraped_page.id
        )
        logger.info(
            f"Deleted {deleted_count} existing chunks for ScrapedPage ID: {scraped_page.id}"
        )
    except Exception as e:
        logger.error(
            f"Error deleting existing chunks for ScrapedPage ID {scraped_page.id}: {e}",
            exc_info=True,
        )
        # Decide whether to proceed or stop if deletion fails

    # 4. Store new chunks and embeddings
    chunks_created = 0
    for i, (chunk, embedding) in enumerate(zip(text_chunks, embeddings)):
        chunk_data = ContentChunkCreate(
            scraped_page_id=scraped_page.id,
            chunk_text=chunk,
            embedding=embedding,
            metadata_={
                "chunk_index": i,
                "source_url": scraped_page.source_url,
            },  # Example metadata
        )
        try:
            new_chunk = crud_content_chunk.create(db=db, obj_in=chunk_data)
            chunks_created += 1

            # Cache the chunk data for faster retrieval
            if new_chunk and hasattr(new_chunk, "id"):
                chunk_cache_data = {
                    "id": str(new_chunk.id),
                    "chunk_text": new_chunk.chunk_text,
                    "embedding": new_chunk.embedding,
                    "scraped_page_id": str(new_chunk.scraped_page_id),
                    "metadata_": new_chunk.metadata_,
                }
                rag_cache.set_chunk(str(new_chunk.id), chunk_cache_data)

        except Exception as e:
            logger.error(
                f"Failed to store chunk {i} for ScrapedPage ID {scraped_page.id}: {e}",
                exc_info=True,
            )
            # Consider rollback or partial success handling

    logger.info(
        f"Successfully stored {chunks_created}/{len(text_chunks)} chunks for ScrapedPage ID: {scraped_page.id}"
    )

    # Invalidate RAG cache for the clinic to ensure fresh results
    if scraped_page.clinic_id:
        rag_cache.invalidate_clinic_cache(str(scraped_page.clinic_id))


def run_embedding_pipeline_for_clinic(clinic_id: uuid.UUID):
    """Runs the embedding pipeline for all scraped pages of a specific clinic."""
    logger.info(f"Starting embedding pipeline for clinic_id: {clinic_id}")
    processed_pages = 0
    try:
        with SessionLocal() as db:
            # Fetch scraped pages for the clinic (consider pagination for large numbers)
            scraped_pages = crud_scraped_page.get_multi_by_clinic(
                db=db, clinic_id=clinic_id, limit=10000
            )  # Adjust limit as needed
            logger.info(
                f"Found {len(scraped_pages)} scraped pages for clinic_id: {clinic_id}"
            )

            for page in scraped_pages:
                try:
                    process_scraped_page_content(db=db, scraped_page=page)
                    processed_pages += 1
                except Exception as page_exc:
                    logger.error(
                        f"Failed to process page {page.id} (URL: {page.source_url}) for clinic {clinic_id}: {page_exc}",
                        exc_info=True,
                    )
                    # Continue with the next page

    except Exception as e:
        logger.error(
            f"Fatal error in embedding pipeline for clinic_id {clinic_id}: {e}",
            exc_info=True,
        )
    finally:
        logger.info(
            f"Embedding pipeline finished for clinic_id: {clinic_id}. Processed {processed_pages} pages."
        )


# Example of how to trigger this (e.g., from an API endpoint or background task runner)
# if __name__ == "__main__":
#     # Replace with actual clinic ID
#     test_clinic_id = uuid.uuid4()
#     # Ensure you have scraped pages for this clinic in your DB first
#     # run_embedding_pipeline_for_clinic(test_clinic_id)
#     pass
