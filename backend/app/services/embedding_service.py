import logging
import os
from typing import Optional, Union, Dict, Any

import backoff
import httpx
from openai import OpenAI
from sentence_transformers import SentenceTransformer

from app.core.constants.embedding import VECTOR_DIM
from app.core.embedding_models import EmbeddingModel

logger = logging.getLogger(__name__)

# Get the embedding model from environment or use default
MODEL = EmbeddingModel(os.getenv("EMBEDDING_MODEL", EmbeddingModel.OPENAI_SMALL.value))


class EmbeddingService:
    """Service for generating vector embeddings from text.

    Supports multiple embedding models:
    - OpenAI's text-embedding-3-small (1536 dimensions) - primary, cropped to VECTOR_DIM
    - SentenceTransformer's all-MiniLM-L6-v2 (384 dimensions) - fallback
    """

    def __init__(self, model: Optional[Union[str, EmbeddingModel]] = None) -> None:
        """
        Initialize the embedding service with the specified model.

        Args:
            model: Optional model override (defaults to MODEL from environment)
        """
        self._model_name = model if model is not None else MODEL

        # Convert string to enum if needed
        if isinstance(self._model_name, str):
            try:
                self._model_name = EmbeddingModel(self._model_name)
            except ValueError:
                logger.error(f"Unknown embedding model: {self._model_name}")
                # Fall back to default model
                self._model_name = MODEL

        logger.info(
            f"Initializing EmbeddingService with model: {self._model_name.value} (native dim: {self._model_name.dim}, using: {VECTOR_DIM})"
        )

        # Initialize the appropriate client/model
        if self._model_name == EmbeddingModel.OPENAI_SMALL:
            self._client = OpenAI()
            self._model = None
        elif self._model_name == EmbeddingModel.NOMIC_EMBED_TEXT:
            self._client = httpx.AsyncClient(timeout=30.0)
            self._model = None
        else:
            self._client = None
            self._model = SentenceTransformer(self._model_name.value)

    @property
    def dimension(self) -> int:
        """Get the dimension of vectors after any necessary cropping."""
        return VECTOR_DIM

    @property
    def native_dimension(self) -> int:
        """Get the native dimension of the current embedding model before cropping."""
        return self._model_name.dim

    @property
    def model_name(self) -> str:
        """Get the name of the current embedding model."""
        return self._model_name.value

    @property
    def model_version(self) -> str:
        """Get the version string of the current embedding model."""
        return self._model_name.version

    def _crop_vector_if_needed(self, vector: list[float]) -> list[float]:
        """Crop a vector to the target dimension if necessary.

        Args:
            vector: The original vector

        Returns:
            The cropped vector if original is larger than target dimension, otherwise original
        """
        if len(vector) > VECTOR_DIM:
            return vector[:VECTOR_DIM]
        return vector

    @backoff.on_exception(backoff.expo, Exception, max_tries=3)
    def embed(self, texts: list[str]) -> list[list[float]]:
        """
        Generate embeddings for a list of text strings.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors (list of floats) cropped to VECTOR_DIM if necessary

        Raises:
            ValueError: If texts is empty or contains non-string elements
            RuntimeError: If embedding generation fails after retries
        """
        if not texts:
            raise ValueError("No texts provided for embedding")

        if not all(isinstance(text, str) for text in texts):
            raise ValueError("All elements in texts must be strings")

        try:
            if self._model_name == EmbeddingModel.OPENAI_SMALL:
                # Use OpenAI API with dimensions parameter to get vectors of the right size
                response = self._client.embeddings.create(
                    model=self._model_name.value,
                    input=texts,
                    dimensions=VECTOR_DIM,  # Request vectors of the target dimension directly
                    timeout=10.0,
                )
                embeddings = [data.embedding for data in response.data]
            elif self._model_name == EmbeddingModel.NOMIC_EMBED_TEXT:
                # Use Ollama API for nomic-embed-text model
                embeddings = self._get_ollama_embeddings(texts)
            else:
                # Use local SentenceTransformer model
                raw_embeddings = self._model.encode(
                    texts, convert_to_numpy=False
                ).tolist()
                # Crop vectors if needed
                embeddings = [
                    self._crop_vector_if_needed(vec) for vec in raw_embeddings
                ]

            # Validate dimensions
            for i, embedding in enumerate(embeddings):
                if len(embedding) != VECTOR_DIM:
                    error_msg = f"Embedding {i} has dimension {len(embedding)}, expected {VECTOR_DIM}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

            return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise RuntimeError(f"Failed to generate embeddings: {str(e)}") from e

    def validate_embedding_dimension(self, embedding: list[float]) -> bool:
        """
        Validate that an embedding has the correct dimension for storage.

        Args:
            embedding: The embedding vector to validate

        Returns:
            bool: True if the embedding has the correct dimension, False otherwise
        """
        return len(embedding) == VECTOR_DIM

    def _get_ollama_embeddings(self, texts: list[str]) -> list[list[float]]:
        """
        Generate embeddings using the Ollama API.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors

        Raises:
            RuntimeError: If the API call fails
        """
        import asyncio

        # Run the async call in the current event loop or create a new one
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're already in an async context
                embeddings = asyncio.run_coroutine_threadsafe(
                    self._async_get_ollama_embeddings(texts), loop
                ).result()
            else:
                # We're in a synchronous context
                embeddings = loop.run_until_complete(
                    self._async_get_ollama_embeddings(texts)
                )
            return embeddings
        except Exception as e:
            logger.error(f"Error getting Ollama embeddings: {str(e)}")
            raise RuntimeError(f"Failed to get Ollama embeddings: {str(e)}") from e

    async def _async_get_ollama_embeddings(self, texts: list[str]) -> list[list[float]]:
        """
        Asynchronously generate embeddings using the Ollama API.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors

        Raises:
            RuntimeError: If the API call fails
        """
        ollama_url = "http://ollama:11434/api/embeddings"
        all_embeddings = []

        # Process each text individually as Ollama API expects
        for text in texts:
            payload: Dict[str, Any] = {"model": "nomic-embed-text", "prompt": text}

            try:
                response = await self._client.post(ollama_url, json=payload)
                response.raise_for_status()
                data = response.json()

                if "embedding" not in data:
                    raise RuntimeError(
                        f"Unexpected response format from Ollama API: {data}"
                    )

                # Get the embedding and crop if needed
                embedding = self._crop_vector_if_needed(data["embedding"])
                all_embeddings.append(embedding)

            except httpx.HTTPStatusError as e:
                logger.error(
                    f"HTTP error from Ollama API: {e.response.status_code} - {e.response.text}"
                )
                raise RuntimeError(
                    f"Ollama API HTTP error: {e.response.status_code}"
                ) from e
            except httpx.RequestError as e:
                logger.error(f"Request error to Ollama API: {str(e)}")
                raise RuntimeError(f"Ollama API request error: {str(e)}") from e
            except Exception as e:
                logger.error(f"Unexpected error with Ollama API: {str(e)}")
                raise RuntimeError(f"Ollama API error: {str(e)}") from e

        return all_embeddings


# Singleton instance for application-wide use
embedding_service = EmbeddingService()


def get_embedding_service() -> EmbeddingService:
    """Get the singleton embedding service instance."""
    return embedding_service
