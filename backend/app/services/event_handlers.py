from app.services.openai_chat_handler import OpenAIChatHandler

chat_handler = OpenAIChatHandler()


async def handle_chat_message_event(event_data):
    """Handle a chat message event."""
    await chat_handler.handle_event(event_data)


# Register event handlers
def register_event_handlers(event_bus):
    """Register all event handlers with the event bus."""
    event_bus.subscribe("chat.message.sent", handle_chat_message_event)
