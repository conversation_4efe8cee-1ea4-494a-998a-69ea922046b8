"""Service to enrich event logs with human-readable names."""

import logging
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from app import crud, models

logger = logging.getLogger(__name__)


class EventLogEnrichmentService:
    """Service to enrich event logs with user names and other readable data."""
    
    @staticmethod
    def enrich_event_logs(db: Session, event_logs: List[models.EventLog]) -> List[Dict[str, Any]]:
        """
        Enrich event logs with user names instead of IDs.
        
        Args:
            db: Database session
            event_logs: List of EventLog objects
            
        Returns:
            List of enriched event log dictionaries
        """
        enriched_logs = []
        
        # Cache for user lookups to avoid repeated queries
        user_cache = {}
        
        for log in event_logs:
            # Convert to dict first
            log_dict = {
                "id": str(log.id),
                "actor_user_id": log.actor_user_id,
                "actor_role": log.actor_role,
                "action": log.action,
                "target_resource_type": log.target_resource_type,
                "target_resource_id": log.target_resource_id,
                "details": log.details or {},
                "extracted_intent": log.extracted_intent,
                "is_llm_driven": getattr(log, 'is_llm_driven', False),
                "created_at": log.created_at.isoformat() if log.created_at else None,
                "outcome": log.outcome,
            }
            
            # Try to get actor name
            actor_name = None
            cache_key = f"{log.actor_role}:{log.actor_user_id}"
            
            if cache_key in user_cache:
                actor_name = user_cache[cache_key]
            else:
                try:
                    if log.actor_role == "patient":
                        patient = crud.patient.get(db, id=log.actor_user_id)
                        if patient:
                            actor_name = f"{patient.first_name} {patient.last_name}"
                            user_cache[cache_key] = actor_name
                    elif log.actor_role == "clinician":
                        clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=log.actor_user_id)
                        if clinician:
                            actor_name = f"Dr. {clinician.first_name} {clinician.last_name}"
                            user_cache[cache_key] = actor_name
                    elif log.actor_role == "admin":
                        # For now, just use "Admin" as we don't have admin profiles
                        actor_name = "Admin"
                        user_cache[cache_key] = actor_name
                    elif log.actor_role == "system":
                        actor_name = "System"
                        user_cache[cache_key] = actor_name
                except Exception as e:
                    logger.error(f"Error looking up user name for {cache_key}: {e}")
            
            # Add actor name to details if we found it
            if actor_name:
                if log_dict["details"] is None:
                    log_dict["details"] = {}
                log_dict["details"]["actor_name"] = actor_name
            
            # Try to enrich target resource names if applicable
            if log.target_resource_type == "patient" and log.target_resource_id:
                try:
                    target_patient = crud.patient.get(db, id=log.target_resource_id)
                    if target_patient:
                        log_dict["details"]["target_name"] = f"{target_patient.first_name} {target_patient.last_name}"
                except Exception as e:
                    logger.error(f"Error looking up target patient name: {e}")
            
            enriched_logs.append(log_dict)
        
        return enriched_logs


# Create a singleton instance
event_log_enrichment_service = EventLogEnrichmentService()