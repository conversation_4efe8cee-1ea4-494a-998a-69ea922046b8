import json
import logging
import re
from typing import Dict, Any

from app.core.llm.factory import LLMFactory
from app.core.llm.providers.openai import OpenAIConfig
from app.core.llm.base import PromptTemplate

logger = logging.getLogger(__name__)

SYSTEM_PROMPT = (
    "You are a router that analyzes user messages to determine intent and context needs. "
    "For each message, determine: "
    "1. The primary intent (action or conversation) "
    "2. Whether this query would benefit from contextual information (medical history, previous interactions, etc.) "
    "3. Your confidence level in this assessment (0.0-1.0) "
    'Respond with JSON: {"intent": "u003caction_nameu003e|conversation", "needs_context": true|false, "confidence": 0.0-1.0}'
)


class IntentDetectionService:
    """Service for lightweight intent detection without using full context."""

    @staticmethod
    async def detect(message: str) -> Dict[str, Any]:
        """Detect the intent of a message using a lightweight model call.

        Args:
            message: The user message to analyze

        Returns:
            A dictionary containing the detected intent, whether context is needed, and confidence level
            {"intent": "<action_name>|conversation", "needs_context": True|False, "confidence": 0.0-1.0}
        """
        try:
            # Create a lightweight OpenAI provider for intent detection
            provider = LLMFactory.create_provider(
                name="openai",  # Changed from provider_type to name to match factory method signature
                model="o4-mini",  # Use the smallest model for efficiency
                max_tokens=150,  # Increased token limit to prevent truncation
            )

            # Format the messages for the chat completion
            messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": message},
            ]

            # Make the API call - send the messages directly, not as a JSON string
            logger.info(f"Sending intent detection request with message: {message}")

            # Generate the response with the messages directly
            response = await provider.generate(
                prompt=message,
                template=PromptTemplate(
                    template=SYSTEM_PROMPT,
                    input_variables=["message"],
                    use_template=False,
                ),
                direct_messages=messages,  # Pass messages directly
            )

            # Parse the JSON response
            try:
                response_content = response.content.strip()

                # Handle potential JSON formatting issues
                if "```json" in response_content:
                    response_content = (
                        response_content.split("```json")[1].split("```")[0].strip()
                    )
                elif "```" in response_content:
                    response_content = response_content.split("```")[1].strip()

                logger.info(f"Cleaned response content: {response_content}")
                intent_data = json.loads(response_content)
                intent = intent_data.get("intent", "conversation")
                needs_context = intent_data.get(
                    "needs_context", True
                )  # Default to True for safety
                confidence = intent_data.get(
                    "confidence", 0.5
                )  # Default to medium confidence

                logger.info(
                    f"Detected intent: {intent}, needs_context: {needs_context}, confidence: {confidence}"
                )

                return {
                    "intent": intent,
                    "needs_context": needs_context,
                    "confidence": confidence,
                }
            except json.JSONDecodeError:
                logger.warning(
                    f"Failed to parse intent detection response: {response.content}"
                )
                return {
                    "intent": "conversation",
                    "needs_context": True,  # Default to True for safety
                    "confidence": 0.0,  # Zero confidence due to parsing error
                }

        except Exception as e:
            logger.error(f"Error in intent detection: {str(e)}")
            return {
                "intent": "conversation",
                "needs_context": True,  # Default to True for safety
                "confidence": 0.0,  # Zero confidence due to error
            }
