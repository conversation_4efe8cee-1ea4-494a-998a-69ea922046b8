"""Intent Resolution Service for LLM-driven API actions.

This service processes natural language inputs to:
1. Extract user intents
2. Match intents to supported API actions
3. Extract and validate parameters for those actions
"""

import json
import logging
from datetime import datetime
from enum import Enum
from typing import Any, Optional, Union

from pydantic import BaseModel, Field, ValidationError, field_validator

from app import models
from app.core.constants.appointment import ALLOWED_APPOINTMENT_TYPES  # Added import
from app.core.llm.base import LLMException, PromptTemplate
from app.core.llm.factory import LLMFactory
from app.schemas.action_chain_v2 import ChainedAction, ChainedIntent, ExecutionMode, FailureMode
from app.utils.retry import retry_async, RetryError

logger = logging.getLogger(__name__)


class IntentResolutionResult(str, Enum):
    """Enum representing the possible outcomes of intent resolution."""

    SUCCESS = "success"
    FAILED_EXTRACTION = "failed_extraction"
    INVALID_ACTION = "invalid_action"
    MISSING_PARAMETERS = "missing_parameters"
    INVALID_PARAMETERS = "invalid_parameters"
    PERMISSION_DENIED = "permission_denied"


class IntentParameterValue(BaseModel):
    """Model representing a resolved parameter value."""

    name: str
    value: Any
    confidence: float = 1.0
    source: str = "user_input"


class ResolvedIntent(BaseModel):
    """Model representing a resolved user intent."""

    action_type: str
    template_id: Optional[str] = None
    parameters: list[IntentParameterValue] = []
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    result: IntentResolutionResult = IntentResolutionResult.SUCCESS
    error_message: Optional[str] = None
    raw_llm_response: Optional[dict[str, Any]] = None


# Define models for compound action structures
class CompoundActionItem(BaseModel):
    action_type: str
    parameters: list[IntentParameterValue] = []
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    dependencies: list[str] = []
    context_requirements: list[str] = []

# Define a Pydantic model for the expected LLM response structure
class LLMIntentExtractionResponse(BaseModel):
    type: str = Field(default="single")  # "single" or "compound"
    # For single actions
    action_type: Optional[str] = None  
    parameters: list[IntentParameterValue] = []
    # For compound actions
    primary_action: Optional[CompoundActionItem] = None
    follow_up_actions: list[CompoundActionItem] = []
    execution_mode: Optional[str] = None  # "sequential" or "parallel"
    # Common fields
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    reasoning: Optional[str] = None

    @field_validator("type")
    def check_type(cls, v):
        if v not in ["single", "compound"]:
            raise ValueError("type must be 'single' or 'compound'")
        return v
    
    @field_validator("action_type")
    def check_action_type(cls, v):
        # Although the LLM might return null/error, we expect a string or None here before further processing
        if v is not None and not isinstance(v, str):
            raise ValueError("action_type must be a string or None")
        # Allow specific error string if needed, or handle None downstream
        # if v == "error": return v # Example if "error" is a valid action_type string
        return v


class IntentResolverService:
    """Service for resolving user intents from natural language."""

    # Define separate schemas for different intent types
    APPOINTMENT_INTENT_SCHEMA = {
        "type": "object",
        "properties": {
            "patient_id": {"type": "string", "description": "The patient's identifier"},
            "scheduled_time": {
                "type": "string",
                "format": "date-time",
                "description": "The appointment date and time in ISO8601 format with UTC timezone",
            },
            "appointment_datetime": {
                "type": "string",
                "format": "date-time",
                "description": "Alternative parameter name for appointment date and time in ISO8601 format",
            },
            "duration_minutes": {
                "type": "integer",
                "minimum": 15,
                "description": "Duration of the appointment in minutes",
            },
            "appointment_type": {
                "type": "string",
                "enum": ["Initial", "Follow-up", "Procedure", "Consultation"],
                "description": "Type of appointment",
            },
            "additional_notes": {
                "type": "string",
                "nullable": True,
                "description": "Optional notes for the appointment",
            },
        },
        "required": [
            "patient_id",
            "duration_minutes",
            "appointment_type",
        ],
        "additionalProperties": False,
        "anyOf": [
            {"required": ["scheduled_time"]},
            {"required": ["appointment_datetime"]}
        ]
    }

    PATIENT_APPOINTMENT_SCHEMA = {
        "type": "object",
        "properties": {
            "preferred_date": {
                "type": "string",
                "format": "date",
                "description": "The preferred date for the appointment in ISO8601 format (YYYY-MM-DD)",
            },
            "preferred_time": {
                "type": "string",
                "description": "The preferred time for the appointment in 24-hour format (HH:MM)",
            },
            "reason": {
                "type": "string",
                "description": "Reason for requesting the appointment",
            },
            "clinician_preference": {
                "type": "string",
                "nullable": True,
                "description": "Optional preferred clinician for the appointment",
            },
        },
        "required": [
            "preferred_date",
            "preferred_time",
            "reason",
            "clinician_preference",
        ],
        "additionalProperties": False,
    }

    WEIGHT_LOG_INTENT_SCHEMA = {
        "type": "object",
        "properties": {
            "weight_value": {
                "type": "number",
                "description": "The weight value (will be converted to float)",
            },
            "unit": {
                "type": "string",
                "enum": ["kg", "lb"],
                "description": "The weight unit (kg or lb)",
            },
            "date": {
                "type": "string",
                "format": "date",
                "description": "The date of the weight measurement (YYYY-MM-DD)",
                "nullable": True,
            },
            "notes": {
                "type": "string",
                "nullable": True,
                "description": "Optional notes for the weight log",
            },
        },
        "required": ["weight_value", "unit"],
        "additionalProperties": False,
    }

    MEDICATION_REQUEST_INTENT_SCHEMA = {
        "type": "object",
        "properties": {
            "patient_id": {"type": "string", "description": "The patient's identifier"},
            "medication_name": {
                "type": "string",
                "description": "Name of the medication",
            },
            "dosage": {"type": "string", "description": "Medication dosage"},
            "frequency": {
                "type": "string",
                "description": "How often to take the medication",
            },
            "duration": {
                "type": "string",
                "description": "How long to take the medication",
            },
            "notes": {
                "type": "string",
                "nullable": True,
                "description": "Optional notes for the medication request",
            },
        },
        "required": ["patient_id", "medication_name", "dosage", "frequency"],
        "additionalProperties": False,
    }

    SIDE_EFFECT_REPORT_SCHEMA = {
        "type": "object",
        "properties": {
            "patient_id": {"type": "string", "description": "The patient's identifier"},
            "medication_name": {
                "type": "string",
                "description": "Name of the medication causing side effects",
            },
            "symptoms": {
                "type": "string",
                "description": "Description of the side effects",
            },
            "severity": {
                "type": "string",
                "enum": ["Mild", "Moderate", "Severe", "Unspecified"],
                "description": "Severity of the side effects",
            },
            "onset_time": {
                "type": "string",
                "description": "When the side effects started",
            },
            "notes": {
                "type": "string",
                "nullable": True,
                "description": "Optional additional notes",
            },
        },
        "required": [
            "patient_id",
            "medication_name",
            "symptoms",
            "severity",
            "onset_time",
        ],
        "additionalProperties": False,
    }

    # Schema for note creation
    NOTE_CREATE_SCHEMA = {
        "type": "object",
        "properties": {
            "patient_id": {
                "type": "string",
                "description": "ID of the patient for the note",
            },
            "note_content": {
                "type": "string",
                "description": "Content of the note",
            },
            "note_type": {
                "type": "string",
                "enum": ["Progress", "Lab", "Consultation", "General"],
                "nullable": True,
                "description": "Type of note",
            },
        },
        "required": ["patient_id", "note_content"],
        "additionalProperties": False,
    }

    # Map action types to their corresponding schemas
    INTENT_SCHEMAS = {
        "appointment_create": APPOINTMENT_INTENT_SCHEMA,
        "appointment_request_create": PATIENT_APPOINTMENT_SCHEMA,
        "weight_log_create": WEIGHT_LOG_INTENT_SCHEMA,
        "medication_request_create": MEDICATION_REQUEST_INTENT_SCHEMA,
        "side_effect_report_create": SIDE_EFFECT_REPORT_SCHEMA,
        "note_create": NOTE_CREATE_SCHEMA,
    }

    # Base schema for all intent responses from LLM
    BASE_INTENT_SCHEMA = {
        "type": "object",
        "required": ["action_type", "parameters", "confidence_score"],
        "properties": {
            "action_type": {"type": "string", "enum": list(INTENT_SCHEMAS.keys())},
            "parameters": {
                "type": "array",
                "items": {
                    "type": "object",
                    "required": ["name", "value", "confidence", "source"],
                    "properties": {
                        "name": {"type": "string"},
                        "value": {"type": ["string", "number", "boolean", "null"]},
                        "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                        "source": {"type": "string"},
                    },
                },
            },
            "confidence_score": {"type": "number", "minimum": 0, "maximum": 1},
            "reasoning": {"type": "string"},
        },
    }

    # System prompt for detecting compound actions
    COMPOUND_ACTION_DETECTION_PROMPT = """
    You are an AI assistant that analyzes user requests to determine if they contain multiple related actions.
    
    User's input: "{user_input}"
    
    Analyze if this request contains:
    1. A single action (e.g., "schedule an appointment")
    2. Multiple related actions (e.g., "schedule an appointment and set a reminder")
    3. A sequence of dependent actions (e.g., "log my weight, and if it's increased, schedule a nutrition consultation")
    
    Common compound patterns:
    - Appointment + Reminder (e.g., "schedule appointment and remind me the day before")
    - Side Effect + Follow-up (e.g., "report nausea and schedule a call to discuss")
    - Multiple Data Entries (e.g., "log weight 185 and blood pressure 120/80")
    - Conditional Actions (e.g., "if my weight is up, schedule appointment")
    
    Return JSON:
    {{
        "is_compound": true/false,
        "action_count": number,
        "detected_actions": ["action1", "action2"],
        "relationship": "sequential|parallel|conditional",
        "reasoning": "explanation"
    }}
    
    Output only valid JSON without additional explanation.
    """

    # System prompt for the LLM to extract intent
    INTENT_EXTRACTION_PROMPT = """
    You are an AI assistant tasked with extracting structured information from user queries in a healthcare management system.
    Your job is to identify the user's intent and extract relevant parameters for API actions.
    
    IMPORTANT: Users may request multiple related actions in a single query. You should detect these compound requests.

    Today's date and time is: {current_datetime}.
    The local timezone offset from UTC is: {local_timezone_offset} hours.

    Here are the supported actions and their parameters:
{supported_actions}

    User's input: "{user_input}"

    Given the user's input above, determine if this is:
    1. A SINGLE action request
    2. A COMPOUND action request (multiple related actions)

    For COMPOUND requests, identify:
    - The primary action (the main intent)
    - Follow-up actions (related actions that should happen after or alongside the primary)
    - Dependencies between actions (e.g., "schedule appointment THEN send reminder")
    
    Common compound patterns:
    - "Report side effects AND schedule appointment" 
    - "Schedule appointment AND remind me"
    - "Log my weight for Monday, Tuesday, and Wednesday" (batch operation)
    - "If my weight is over 200, schedule a nutrition consultation" (conditional)

    Follow these guidelines:
    1. Only select from the supported actions listed above
    2. Extract all parameters needed for each action
    3. If a parameter is mentioned but ambiguous, assign a confidence score less than 1.0
    4. If a required parameter is missing completely, mark it with a null value
    5. IMPORTANT FOR TIMES: When extracting times, interpret times as being in the user's local timezone,
       but CONVERT them to UTC for storage. Format them as ISO8601 strings WITH the +00:00 UTC timezone suffix.
       For example, if the user asks for 2pm and their timezone is UTC-5, return "{current_year}-XX-YYT19:00:00+00:00"
       (which is 2 PM local time converted to UTC) NOT "{current_year}-XX-YYT14:00:00+00:00" (which would be incorrect).
    6. Do not invent or assume information not provided by the user
    7. If you cannot determine a clear action, return an explanatory error
    8. For appointment-related actions, ALWAYS attempt to identify a date and time, even if implicit
       - If the user mentions "tomorrow", "next week", "this Friday", etc., extract this as a specific date in YYYY-MM-DD format relative to today's date
       - If no specific time is mentioned, use a reasonable default time (e.g., 9:00 AM) in HH:MM format
       - If no specific date is mentioned, default to the next business day
       - MAKE SURE to use the correct year ({current_year}) for all relative dates
       - If user mentions a specific time like "2pm", use that exact time (14:00), not a default time
       - Always return dates and times in the ISO 8601 format (YYYY-MM-DDThh:mm:ss)
       - IMPORTANT: All times provided by the user should be interpreted as local times using the timezone offset provided
       - Always include the timezone offset in returned datetime strings (e.g., YYYY-MM-DDThh:mm:ss+/-HH:MM)
    9. IMPORTANT FOR APPOINTMENT DURATION: When the user mentions duration in minutes (e.g., "30-minute appointment"),
       always extract it as a numeric value for the duration_minutes parameter. Pay close attention to duration phrases like
       "30 minute", "half hour", "hour long", etc.
    10. IMPORTANT FOR APPOINTMENT TYPE: When the user mentions appointment types like "initial", "follow-up", "procedure", "consultation",
          be sure to extract that as the appointment_type parameter. If the user says "intake", map it to "Initial". Make other determinations of appointment type as needed to fit the only acceptable appointment types of 'Initial', 'Follow-up', 'Procedure', and 'Consultation'.
    11. For patient identification:
       - If a patient name (e.g., "Michael Patient") is mentioned, extract it as the patient_id parameter
       - Use the full name exactly as provided in the input
       - For patient name extraction, assign high confidence (0.9+) when a full name is provided
    12. SIDE EFFECT - SEVERITY: For `severity` in side effect reports, map qualitative descriptions (e.g., 'a bit', 'mild', 'some', 'bad', 'severe', 'unbearable') to standardized values like 'Mild', 'Moderate', or 'Severe'. If no description is given or inferable, use 'Unspecified'.
    13. SIDE EFFECT - ONSET TIME: For side effect reports, you can extract the onset timing as either `onset_time` OR `onset_date` parameter (both are accepted by the system):
       - If the user mentions a relative time like 'yesterday', 'this morning', 'last week', extract it as `onset_date` in YYYY-MM-DD format
       - If the user mentions 'since starting [medication]', extract as `onset_time` with value 'Since starting [medication_name]'
       - For other time descriptions, use whichever parameter name feels more natural (`onset_time` or `onset_date`)
       - The system will automatically handle either parameter name
    14. IMPORTANT FOR WEIGHT LOGGING:
       - For weight units, convert any variations (pounds, lbs, kilograms, kgs) to just "lb" or "kg"
       - If a date is mentioned (e.g., "yesterday", "last Monday"), extract it in YYYY-MM-DD format
       - If no date is mentioned, you can omit the date parameter (system will use today's date as default)
       - When extracting weight values, convert to a numeric value (e.g., "one hundred and fifty" → 150)

    Example extraction:
    User: "Schedule a 30-minute initial appointment with Michael Patient today at 8 p.m."
    Expected extraction:
    ```json
    {
      "action_type": "appointment_create",
      "parameters": [
        {
          "name": "patient_id",
          "value": "Michael Patient",
          "confidence": 0.9,
          "source": "user_input"
        },
        {
          "name": "scheduled_time",
          "value": "2025-04-21T01:00:00+00:00",
          "confidence": 1.0,
          "source": "user_input"
        },
        {
          "name": "duration_minutes",
          "value": 30,
          "confidence": 1.0,
          "source": "user_input"
        },
        {
          "name": "appointment_type",
          "value": "Initial",
          "confidence": 1.0,
          "source": "user_input"
        }
      ],
      "confidence_score": 1.0,
      "reasoning": "The user explicitly mentioned scheduling a 30-minute initial appointment with Michael Patient today at 8 PM. Since the local timezone is UTC-5, this converts to 1 AM UTC the next day."
    }
    ```

    Example user: "Log that I weighed 185 pounds this morning."
    Expected JSON:
    {
      "action_type": "weight_log_create",
      "parameters": [
        {"name": "weight_value", "value": 185, "confidence": 1.0, "source": "user_input"},
        {"name": "unit", "value": "lb", "confidence": 1.0, "source": "user_input"}
      ],
      "confidence_score": 0.95,
      "reasoning": "User explicitly gave weight and unit; date will be defaulted to today by the system."
    }

    Return a JSON object with ONE of the following structures:
    
    For SINGLE actions:
    ```json
    {
    "type": "single",
    "action_type": "[one of the supported action types]",
    "parameters": [
    {
    "name": "[parameter name]",
    "value": "[extracted value]",
    "confidence": 0.0-1.0,
    "source": "user_input"
    }
    ],
    "confidence_score": 0.0-1.0,
    "reasoning": "[brief explanation of your reasoning]"
    }
    ```
    
    For COMPOUND actions:
    ```json
    {
    "type": "compound",
    "primary_action": {
        "action_type": "[primary action type]",
        "parameters": [...],
        "confidence": 0.0-1.0
    },
    "follow_up_actions": [
        {
            "action_type": "[follow-up action type]",
            "parameters": [...],
            "confidence": 0.0-1.0,
            "dependencies": ["primary_action"],
            "context_requirements": ["appointment_id", "notification_id"]
        }
    ],
    "execution_mode": "sequential" or "parallel",
    "confidence_score": 0.0-1.0,
    "reasoning": "[explanation of the compound action]"
    }
    ```

    Output only valid JSON without additional explanation or markdown.
    """

    # Template for describing supported actions to the LLM
    ACTION_DESCRIPTION_TEMPLATE = """{action_type}:
    Description: {description}
    Required parameters: {required_parameters}
    Optional parameters: {optional_parameters}
    """

    _prompt_cache = {}
    _prompt_cache_timeout = 300  # seconds

    def _get_prompt_cache_key(self, template_id: str, user_role: str) -> str:
        return f"{template_id}:{user_role}"

    def _get_cached_prompt(self, template_id: str, user_role: str):
        key = self._get_prompt_cache_key(template_id, user_role)
        entry = self._prompt_cache.get(key)
        if entry:
            if (
                datetime.utcnow() - entry["timestamp"]
            ).total_seconds() < self._prompt_cache_timeout:
                return entry["prompt"]
        return None

    def _set_cached_prompt(self, template_id: str, user_role: str, prompt: str):
        key = self._get_prompt_cache_key(template_id, user_role)
        self._prompt_cache[key] = {"prompt": prompt, "timestamp": datetime.utcnow()}

    def _format_supported_actions(
        self, actions: list[dict[str, Any]], template_id: str, user_role: str
    ) -> str:
        # Try to use cache
        cached = self._get_cached_prompt(template_id, user_role)
        if cached:
            return cached
        # Otherwise, format and cache
        formatted = "".join(
            [
                f"- {a['action_type']}: {a.get('description', '')}\n  Parameters: {[p['name'] for p in a.get('parameters', [])]}\n"
                for a in actions
            ]
        )
        self._set_cached_prompt(template_id, user_role, formatted)
        return formatted

    def __init__(self):
        """Initialize the Intent Resolver Service."""
        self.template_prompt = PromptTemplate(
            template=self.INTENT_EXTRACTION_PROMPT,
            input_variables=[
                "supported_actions",
                "user_input",
                "current_datetime",
                "current_year",
                "local_timezone_offset",
            ],
            use_template=False,  # Set to False to ensure direct string replacement
        )

    async def _get_llm_provider(self, model: str = "gpt-4o"):
        """Get an LLM provider for intent extraction."""
        try:
            provider = LLMFactory.create_provider(
                "openai",
                model=model,
                temperature=0.2,  # Low temperature for more predictable outputs
                max_tokens=2000,  # Increased tokens for comprehensive responses
            )
            return provider
        except Exception as e:
            logger.error(f"Failed to initialize LLM provider: {e}")
            raise
    
    async def detect_compound_action(
        self,
        user_input: str,
        model: str = "gpt-4o"
    ) -> dict[str, Any]:
        """Detect if a user input contains multiple related actions.
        
        Args:
            user_input: Natural language input from the user
            model: LLM model to use
            
        Returns:
            Dictionary with compound action analysis
        """
        try:
            provider = await self._get_llm_provider(model)
            
            prompt_template = PromptTemplate(
                template=self.COMPOUND_ACTION_DETECTION_PROMPT,
                input_variables=["user_input"],
                use_template=False
            )
            
            variables = {
                "user_input": user_input.strip()
            }
            
            # Use retry logic for compound action detection
            try:
                llm_response = await self._call_llm_with_retry_for_detection(provider, prompt_template, variables)
            except RetryError as e:
                logger.error(f"Compound action detection failed after {e.attempts} retries")
                raise
            
            # Parse response
            response_content = llm_response.content
            if "```json" in response_content:
                response_content = response_content.split("```json")[1].split("```")[0].strip()
            elif "```" in response_content:
                response_content = response_content.split("```")[1].strip()
            
            result = json.loads(response_content)
            logger.info(f"Compound action detection result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error detecting compound action: {e}")
            return {
                "is_compound": False,
                "action_count": 1,
                "detected_actions": [],
                "relationship": "none",
                "reasoning": f"Detection failed: {str(e)}"
            }
        finally:
            if 'provider' in locals():
                await provider.close()

    async def extract_intent(
        self,
        template: models.Template,
        user_input: str,
        client_timezone_offset: Optional[float] = None,
        user_role: str = None,
        db=None,
    ) -> tuple[Union[ResolvedIntent, ChainedAction], list[dict[str, Any]]]:
        """Extract user intent based on a template and natural language input.

        Args:
            template: The template containing supported actions
            user_input: Natural language input from the user
            client_timezone_offset: Timezone offset in hours from UTC (e.g., -5.0 for EST)
                                   If not provided, falls back to system timezone
            user_role: Role of the user (patient or clinician)
            db: Optional database session for retrieving additional templates

        Returns:
            A tuple containing the ResolvedIntent and the combined list of actions used for prompting.
        """
        actions = []  # Initialize actions list in the outer scope
        try:
            # Extract actions from template - use 'actions' attribute directly (JSONB)
            template_actions = getattr(template, "actions", None)

            # Initialize actions with the template's actions
            actions = (
                template_actions.copy() if isinstance(template_actions, list) else []
            )

            # If no actions in the template or db is provided, try to get actions from all accessible templates
            if (not actions or db is not None) and user_role:
                try:
                    from app.services.template_validation_service import (
                        TemplateValidationService,
                    )

                    # Log that we're fetching additional templates
                    logger.info(
                        f"Fetching additional templates for user with role: {user_role}"
                    )

                    # Get all templates accessible to the user
                    templates = await TemplateValidationService.validate_and_get_accessible_templates(
                        db=db,
                        user_id=None,  # We don't have user_id here, but role-based access should be sufficient
                        role=user_role,
                    )

                    # Extract actions from all templates
                    for tmpl in templates:
                        if (
                            tmpl.id != template.id
                        ):  # Skip the current template to avoid duplicates
                            # Use 'actions' attribute directly
                            tmpl_actions = getattr(tmpl, "actions", None)
                            if isinstance(tmpl_actions, list) and tmpl_actions:
                                # Add unique actions based on action_type
                                existing_action_types = {
                                    a.get("action_type") for a in actions
                                }
                                for action in tmpl_actions:
                                    if (
                                        action.get("action_type")
                                        not in existing_action_types
                                    ):
                                        actions.append(action)
                                        existing_action_types.add(
                                            action.get("action_type")
                                        )

                    logger.info(
                        f"Combined {len(actions)} unique actions from all accessible templates"
                    )
                except Exception as e:
                    logger.warning(
                        f"Error fetching additional templates: {e}. Using only the provided template."
                    )

            if not actions:
                resolved_intent = ResolvedIntent(
                    action_type="",
                    result=IntentResolutionResult.INVALID_ACTION,
                    error_message="No actions defined in the template or accessible templates",
                )
                return resolved_intent, actions  # Return empty actions list

            # Format actions for prompt
            supported_actions_text = self._format_supported_actions(
                actions, template.id, user_role
            )

            # Get LLM provider
            provider = await self._get_llm_provider()

            try:
                # Prepare template variables
                current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                current_year = datetime.now().year

                # Use client timezone offset if provided, otherwise fall back to system timezone
                # This avoids hardcoding timezone values and respects multisite application requirements
                if client_timezone_offset is not None:
                    local_timezone_offset = client_timezone_offset
                    logger.info(
                        f"Using client-provided timezone offset: {local_timezone_offset} hours"
                    )
                else:
                    # Fallback to system timezone offset (should rarely be used)
                    local_timezone_offset = (
                        datetime.now().astimezone().utcoffset().total_seconds() / 3600
                    )
                    logger.info(
                        f"Using system timezone offset: {local_timezone_offset} hours"
                    )

                # Make sure user_input is explicitly logged for debugging
                logger.info(f"Actual user input being processed: '{user_input}'")

                variables = {
                    "supported_actions": supported_actions_text,
                    "user_input": user_input.strip(),  # Ensure user input is stripped of whitespace
                    "current_datetime": current_datetime,
                    "current_year": current_year,
                    "local_timezone_offset": local_timezone_offset,
                }

                # Call the LLM with retry logic
                try:
                    llm_response = await self._call_llm_with_retry(provider, variables)
                except RetryError as e:
                    logger.error(
                        f"LLM intent extraction failed after {e.attempts} attempts",
                        exc_info=True,
                    )
                    resolved_intent = ResolvedIntent(
                        action_type=None,
                        template_id=str(template.id),
                        parameters=[],
                        confidence_score=0.0,
                        result=IntentResolutionResult.FAILED_EXTRACTION,
                        error_message=f"LLM error after {e.attempts} retries: {str(e.last_error)}",
                        raw_llm_response=None,
                    )
                    return resolved_intent, actions

                # Parse response JSON
                response_content = llm_response.content
                # Log the raw response content regardless of format
                logger.info(f"Raw LLM response: {response_content}")

                # Clean up the response if it contains markdown code blocks
                if "```json" in response_content:
                    response_content = (
                        response_content.split("```json")[1].split("```")[0].strip()
                    )
                elif "```" in response_content:
                    response_content = response_content.split("```")[1].strip()

                # Parse the cleaned response
                try:
                    # Parse the JSON response first
                    json_data = json.loads(response_content)
                    # Then validate it with our Pydantic model
                    llm_response_obj = LLMIntentExtractionResponse(**json_data)
                    logger.info(
                        f"Parsed and validated LLM response: {llm_response_obj.model_dump_json(indent=2)}"
                    )
                except (json.JSONDecodeError, ValidationError) as e:
                    logger.error(f"Failed to parse or validate LLM response: {e}")
                    logger.error(f"Raw response content was: {response_content}")
                    resolved_intent = ResolvedIntent(
                        action_type="",
                        result=IntentResolutionResult.FAILED_EXTRACTION,
                        error_message=f"Invalid LLM response format: {str(e)}",
                    )
                    return resolved_intent, actions  # Return combined actions list

                # Check if this is a compound action
                if llm_response_obj.type == "compound":
                    # Handle compound action
                    return self._process_compound_action(
                        llm_response_obj, 
                        template, 
                        actions,
                        client_timezone_offset,
                        variables
                    )
                
                # Handle single action (existing logic)
                action_type = (
                    llm_response_obj.action_type
                    if llm_response_obj.action_type is not None
                    else ""
                )
                template_action = next(
                    (a for a in actions if a.get("action_type") == action_type), None
                )

                if not action_type or not template_action:
                    # Handle cases where action_type is None, empty, or not found in template
                    error_msg = (
                        f"Action '{action_type}' not found in template or is invalid."
                    )
                    logger.warning(error_msg)
                    resolved_intent = ResolvedIntent(
                        action_type=action_type,  # Keep original if needed for logging
                        result=IntentResolutionResult.INVALID_ACTION,
                        confidence_score=llm_response_obj.confidence_score,
                        error_message=error_msg,
                        raw_llm_response=llm_response_obj.model_dump(),  # Log the raw validated data
                    )
                    return resolved_intent, actions  # Return combined actions list

                # Extract parameters from LLM response
                parameters_from_llm = llm_response_obj.parameters

                # Add timezone information from context if not already present in parameters
                param_names = {param.name for param in parameters_from_llm}

                # Add timezone_offset if provided and not already in parameters
                if (
                    client_timezone_offset is not None
                    and "timezone_offset" not in param_names
                ):
                    parameters_from_llm.append(
                        IntentParameterValue(
                            name="timezone_offset",
                            value=float(client_timezone_offset),
                            confidence=1.0,
                            source="client_context",
                        )
                    )
                    logger.info(
                        f"Added timezone_offset={client_timezone_offset} to intent parameters from client context"
                    )

                # Add timezone name if available and not already in parameters
                timezone_name = variables.get("timezone")
                if timezone_name and "timezone" not in param_names:
                    parameters_from_llm.append(
                        IntentParameterValue(
                            name="timezone",
                            value=timezone_name,
                            confidence=1.0,
                            source="client_context",
                        )
                    )
                    logger.info(
                        f"Added timezone={timezone_name} to intent parameters from client context"
                    )

                # Create resolved intent object using data from Pydantic model with updated parameters
                resolved_intent = ResolvedIntent(
                    action_type=action_type,
                    template_id=str(template.id),
                    parameters=parameters_from_llm,  # Use the updated parameters list
                    confidence_score=llm_response_obj.confidence_score,
                    result=IntentResolutionResult.SUCCESS,
                    raw_llm_response=llm_response_obj.model_dump(),  # Log the raw validated data
                )

                # Extract parameters into a dictionary for easy access
                resolved_params = {
                    param.name: param.value for param in resolved_intent.parameters
                }

                # Add default date for weight logs if not provided
                if (
                    resolved_intent.action_type == "weight_log_create"
                    and "date" not in resolved_params
                ):
                    today = datetime.now().strftime("%Y-%m-%d")
                    logger.info(f"Adding default date (today: {today}) for weight log")
                    resolved_params["date"] = today
                    # Add to parameters list as well
                    resolved_intent.parameters.append(
                        IntentParameterValue(
                            name="date",
                            value=today,
                            confidence=1.0,
                            source="system_default",
                        )
                    )

                return resolved_intent, actions  # Return combined actions list

            except LLMException as e:  # Catch LLM specific exceptions
                logger.error(f"LLM error during intent extraction: {e}")
                resolved_intent = ResolvedIntent(
                    action_type="",
                    result=IntentResolutionResult.FAILED_EXTRACTION,
                    error_message=f"LLM error during intent extraction: {str(e)}",
                )
                return resolved_intent, actions  # Return combined actions list
            finally:
                # Clean up provider resources
                await provider.close()

        except Exception as e:
            logger.error(f"Unexpected error in intent extraction: {e}", exc_info=True)
            resolved_intent = ResolvedIntent(
                action_type="",
                result=IntentResolutionResult.FAILED_EXTRACTION,
                error_message=f"Unexpected error in intent extraction: {str(e)}",
            )
            return resolved_intent, actions  # Return combined actions list

    async def validate_parameters(
        self, resolved_intent: ResolvedIntent, combined_actions: list[dict[str, Any]]
    ) -> tuple[bool, Optional[str], ResolvedIntent]:
        """Validate parameters for the resolved intent against action requirements from the combined list."""
        try:
            # Find the matching action in the combined list
            action = next(
                (
                    a
                    for a in combined_actions
                    if a.get("action_type") == resolved_intent.action_type
                ),
                None,
            )
            if not action:
                error_msg = f"Action definition for '{resolved_intent.action_type}' not found in combined actions list during validation."
                logger.error(error_msg)
                resolved_intent.result = IntentResolutionResult.INVALID_ACTION
                resolved_intent.error_message = error_msg
                return False, error_msg, resolved_intent

            # Get the appropriate schema for this action type
            schema = self.INTENT_SCHEMAS.get(resolved_intent.action_type)
            if not schema:
                error_msg = (
                    f"No schema defined for action type '{resolved_intent.action_type}'"
                )
                logger.error(error_msg)
                resolved_intent.result = IntentResolutionResult.INVALID_ACTION
                resolved_intent.error_message = error_msg
                return False, error_msg, resolved_intent

            # Extract parameters with potential overrides
            resolved_params = {
                param.name: param.value for param in resolved_intent.parameters
            }

            # Handle side effect report parameter aliases (onset_date -> onset_time)
            if resolved_intent.action_type == "side_effect_report_create":
                # The action handler accepts both onset_time and onset_date
                # but the schema only defines onset_time as required
                # If we have onset_date but not onset_time, copy it over
                if "onset_date" in resolved_params and "onset_time" not in resolved_params:
                    logger.info(f"Mapping onset_date to onset_time for side effect report: {resolved_params['onset_date']}")
                    resolved_params["onset_time"] = resolved_params["onset_date"]
                    # Also update the parameters list to include onset_time
                    resolved_intent.parameters.append(
                        IntentParameterValue(
                            name="onset_time",
                            value=resolved_params["onset_date"],
                            confidence=1.0,
                            source="mapped_from_onset_date"
                        )
                    )

            # Add default date for weight logs if not provided
            if (
                resolved_intent.action_type == "weight_log_create"
                and "date" not in resolved_params
            ):
                today = datetime.now().strftime("%Y-%m-%d")
                logger.info(f"Adding default date (today: {today}) for weight log")
                resolved_params["date"] = today

            # For appointment_create, set default duration_minutes if not provided
            if (
                resolved_intent.action_type == "appointment_create"
                and "duration_minutes" not in resolved_params
            ):
                logger.info("Setting default duration_minutes=30 for appointment")
                resolved_params["duration_minutes"] = 30

            missing_required = []
            invalid_params = []

            # Validate parameters against schema requirements
            for param_name, param_schema in schema["properties"].items():
                required = param_name in schema.get("required", [])
                param_type = param_schema.get("type")
                enum_values = param_schema.get("enum")

                if param_name not in resolved_params:
                    if required:
                        missing_required.append(param_name)
                    continue

                value = resolved_params[param_name]

                # Handle nullable fields
                if value is None:
                    if param_schema.get("nullable", False):
                        continue
                    if required:
                        missing_required.append(param_name)
                    continue

                # Type validation
                valid = False
                try:
                    if param_type == "string" and isinstance(value, str):
                        # Check enum constraints if they exist
                        if enum_values and value not in enum_values:
                            invalid_params.append(
                                f"{param_name} must be one of: {', '.join(enum_values)}"
                            )
                        else:
                            valid = True
                    elif param_type == "number" and (
                        isinstance(value, (int, float))
                        or (
                            isinstance(value, str)
                            and value.replace(".", "", 1).isdigit()
                        )
                    ):
                        # Convert string numbers to actual numbers
                        if isinstance(value, str):
                            resolved_params[param_name] = (
                                float(value) if "." in value else int(value)
                            )
                        valid = True
                    elif param_type == "integer" and (
                        isinstance(value, int)
                        or (isinstance(value, str) and value.isdigit())
                    ):
                        if isinstance(value, str):
                            resolved_params[param_name] = int(value)
                        valid = True
                        # Check minimum constraint if it exists
                        minimum = param_schema.get("minimum")
                        if (
                            minimum is not None
                            and resolved_params[param_name] < minimum
                        ):
                            valid = False
                            invalid_params.append(
                                f"{param_name} must be at least {minimum}"
                            )
                    elif param_type == "boolean" and (
                        isinstance(value, bool)
                        or value.lower() in ["true", "false", "yes", "no", "1", "0"]
                    ):
                        if isinstance(value, str):
                            resolved_params[param_name] = value.lower() in [
                                "true",
                                "yes",
                                "1",
                            ]
                        valid = True

                    # Check validation pattern if provided
                    validation_pattern = param_schema.get("validation_pattern")
                    if validation_pattern and isinstance(value, str):
                        import re

                        if not re.match(validation_pattern, value):
                            valid = False
                            invalid_params.append(
                                f"{param_name} (does not match pattern {validation_pattern})"
                            )

                except Exception as e:
                    logger.error(f"Error validating parameter {param_name}: {e}")
                    valid = False

                if not valid and param_name not in invalid_params:
                    invalid_params.append(
                        f"{param_name} (expected {param_type}, got {type(value).__name__})"
                    )

            # Update resolved intent with any conversions made during validation
            updated_params = []
            for param in resolved_intent.parameters:
                if param.name in resolved_params:
                    updated_params.append(
                        IntentParameterValue(
                            name=param.name,
                            value=resolved_params[param.name],
                            confidence=param.confidence,
                            source=param.source,
                        )
                    )
                else:
                    updated_params.append(param)

            updated_intent = ResolvedIntent(
                action_type=resolved_intent.action_type,
                template_id=resolved_intent.template_id,
                parameters=updated_params,
                confidence_score=resolved_intent.confidence_score,
                raw_llm_response=resolved_intent.raw_llm_response,
            )

            # Check for validation issues
            if missing_required:
                updated_intent.result = IntentResolutionResult.MISSING_PARAMETERS
                updated_intent.error_message = (
                    f"Missing required parameters: {', '.join(missing_required)}"
                )

                # Special handling for appointment_create to provide more helpful error messages
                if resolved_intent.action_type == "appointment_create":
                    if "patient_id" in missing_required and len(missing_required) == 1:
                        updated_intent.error_message = "Please provide your patient ID or let the system use your current user ID."
                    elif "appointment_type" in missing_required:
                        updated_intent.error_message = f"Please specify an appointment type (one of: {', '.join(ALLOWED_APPOINTMENT_TYPES)})"
                    elif "duration_minutes" in missing_required:
                        updated_intent.error_message = "Please specify how long you need for the appointment (e.g., 30 minutes)"

                return False, updated_intent.error_message, updated_intent

            if invalid_params:
                updated_intent.result = IntentResolutionResult.INVALID_PARAMETERS
                updated_intent.error_message = (
                    f"Invalid parameters: {', '.join(invalid_params)}"
                )
                return False, updated_intent.error_message, updated_intent

            return True, None, updated_intent

        except Exception as e:
            logger.error(f"Error validating parameters: {e}", exc_info=True)
            resolved_intent.result = IntentResolutionResult.INVALID_PARAMETERS
            resolved_intent.error_message = f"Error validating parameters: {str(e)}"
            return False, resolved_intent.error_message, resolved_intent

    @retry_async(
        max_attempts=3,
        base_delay=1.0,
        max_delay=10.0,
        exceptions=(LLMException, json.JSONDecodeError, ValidationError),
        jitter=True
    )
    async def _call_llm_with_retry(self, provider, variables: dict[str, Any]) -> Any:
        """Call LLM with retry logic for resilience.
        
        Args:
            provider: LLM provider instance
            variables: Template variables for the prompt
            
        Returns:
            LLM response object
            
        Raises:
            RetryError: If all retry attempts fail
        """
        # Log the attempt
        logger.info(
            f"Calling LLM for intent extraction with user input: '{variables.get('user_input', '')}'"
        )
        
        # Log the prompt for debugging
        prompt_text = self.template_prompt.format(**variables)
        logger.debug(f"LLM Prompt text:\n{prompt_text}")
        
        # Make the LLM call
        response = await provider.generate_with_template(
            template=self.template_prompt, 
            variables=variables
        )
        
        # Validate response has content
        if not response or not response.content:
            raise LLMException("Empty response from LLM")
            
        return response

    @retry_async(
        max_attempts=3,
        base_delay=0.5,
        max_delay=5.0,
        exceptions=(LLMException, json.JSONDecodeError),
        jitter=True
    )
    async def _call_llm_with_retry_for_detection(self, provider, template, variables: dict[str, Any]) -> Any:
        """Call LLM for compound action detection with retry logic.
        
        Args:
            provider: LLM provider instance
            template: Prompt template
            variables: Template variables
            
        Returns:
            LLM response object
        """
        response = await provider.generate_with_template(
            template=template, 
            variables=variables
        )
        
        if not response or not response.content:
            raise LLMException("Empty response from LLM during compound detection")
            
        return response

    async def extract_compound_intents(
        self,
        template: models.Template,
        user_input: str,
        compound_info: dict[str, Any],
        client_timezone_offset: Optional[float] = None,
        user_role: str = None,
        db=None,
    ) -> list[ResolvedIntent]:
        """Extract multiple intents from a compound action request.
        
        Args:
            template: The template containing supported actions
            user_input: Natural language input from the user
            compound_info: Information about the compound action from detection
            client_timezone_offset: Timezone offset in hours from UTC
            user_role: Role of the user
            db: Database session
            
        Returns:
            List of ResolvedIntent objects for each detected action
        """
        intents = []
        
        # For now, fall back to single intent extraction
        # This is where we'd implement sophisticated multi-intent extraction
        # with dependency tracking and context sharing
        
        single_intent, actions = await self.extract_intent(
            template, user_input, client_timezone_offset, user_role, db
        )
        intents.append(single_intent)
        
        logger.info(f"Extracted {len(intents)} intents from compound action")
        return intents

    def _process_compound_action(
        self,
        llm_response: LLMIntentExtractionResponse,
        template: models.Template,
        actions: list[dict],
        client_timezone_offset: Optional[float],
        variables: dict
    ) -> tuple[Union[ResolvedIntent, ChainedAction], list[dict[str, Any]]]:
        """Process a compound action response from the LLM."""
        
        if not llm_response.primary_action:
            error_msg = "Compound action missing primary_action"
            logger.error(error_msg)
            resolved_intent = ResolvedIntent(
                action_type="",
                result=IntentResolutionResult.FAILED_EXTRACTION,
                error_message=error_msg,
            )
            return resolved_intent, actions
        
        # Convert LLM response to ChainedAction
        try:
            # Convert primary action
            primary_intent = ChainedIntent(
                action_type=llm_response.primary_action.action_type,
                parameters={p.name: p.value for p in llm_response.primary_action.parameters},
                confidence=llm_response.primary_action.confidence
            )
            
            # Convert follow-up actions
            follow_up_intents = []
            for follow_up in llm_response.follow_up_actions:
                # Convert string dependencies to ActionDependency objects
                dependencies = []
                for dep in follow_up.dependencies:
                    if isinstance(dep, str):
                        from app.schemas.action_chain_v2 import ActionDependency
                        dependencies.append(ActionDependency(
                            required_action=dep,
                            require_success=True
                        ))
                    else:
                        dependencies.append(dep)
                
                intent = ChainedIntent(
                    action_type=follow_up.action_type,
                    parameters={p.name: p.value for p in follow_up.parameters},
                    confidence=follow_up.confidence,
                    dependencies=dependencies,
                    context_requirements=follow_up.context_requirements
                )
                follow_up_intents.append(intent)
            
            # Create ChainedAction
            chain = ChainedAction(
                primary_action=primary_intent,
                follow_up_actions=follow_up_intents,
                execution_mode=ExecutionMode(llm_response.execution_mode or "sequential"),
                failure_mode=FailureMode.CONTINUE_ON_FAILURE
            )
            
            logger.info(f"Created ChainedAction with {len(follow_up_intents)} follow-up actions")
            return chain, actions
            
        except Exception as e:
            logger.error(f"Error creating ChainedAction: {e}")
            resolved_intent = ResolvedIntent(
                action_type="",
                result=IntentResolutionResult.FAILED_EXTRACTION,
                error_message=f"Failed to create action chain: {str(e)}",
            )
            return resolved_intent, actions

    async def process_user_input(
        self,
        user_id: str,
        user_role: str,
        template_id: str,
        user_input: str,
        db,
        client_timezone_offset: Optional[float] = None,
    ) -> ResolvedIntent:
        """Process user input to extract and validate intent.

        Args:
            user_id: ID of the user making the request
            user_role: Role of the user (patient or clinician)
            template_id: ID of the template containing supported actions
            user_input: Natural language input from the user
            db: Database session
            client_timezone_offset: Timezone offset from UTC in hours (e.g., -5.0 for EST)
                                   Passed from the client to ensure correct timezone handling
        """
        try:
            # Get the template
            from app.services.template_validation_service import (
                TemplateValidationService,
            )

            # Validate template access
            template = await TemplateValidationService.validate_template_access(
                db=db, template_id=template_id, user_id=user_id, roles=[user_role]
            )

            # Extract intent from user input, passing db to allow fetching all accessible templates
            resolved_intent, combined_actions = await self.extract_intent(
                template, user_input, client_timezone_offset, user_role, db=db
            )

            # Check if this is a compound action (ChainedAction) or single intent (ResolvedIntent)
            if isinstance(resolved_intent, ChainedAction):
                # For compound actions, return the ChainedAction directly
                logger.info(
                    f"Successfully resolved compound action with primary: {resolved_intent.primary_action.action_type} "
                    f"and {len(resolved_intent.follow_up_actions)} follow-up actions"
                )
                return resolved_intent
            
            # For single intents, validate parameters if extraction was successful
            if resolved_intent.result == IntentResolutionResult.SUCCESS:
                valid, error_message, resolved_intent = await self.validate_parameters(
                    resolved_intent, combined_actions
                )  # Pass combined_actions

                # Log the resolution outcome
                if valid:
                    logger.info(
                        f"Successfully resolved intent: {resolved_intent.action_type} with {len(resolved_intent.parameters)} parameters"
                    )
                else:
                    logger.warning(
                        f"Intent parameter validation failed: {error_message}"
                    )

            return resolved_intent
        except Exception as e:
            logger.error(f"Error processing user input: {e}", exc_info=True)
            return ResolvedIntent(
                action_type=None,
                template_id=template_id,
                parameters=[],
                confidence_score=0.0,
                result=IntentResolutionResult.FAILED_EXTRACTION,
                error_message=f"Error processing user input: {str(e)}",
                raw_llm_response=None,
            )
    
    async def process_user_input_with_compound_support(
        self,
        user_id: str,
        user_role: str,
        template_id: str,
        user_input: str,
        db,
        client_timezone_offset: Optional[float] = None,
    ) -> Union[ResolvedIntent, dict[str, Any]]:
        """Process user input with support for compound actions.
        
        This enhanced version detects compound actions and returns either:
        - A single ResolvedIntent for simple actions
        - A dictionary with ChainedAction information for compound actions
        
        Args:
            user_id: ID of the user making the request
            user_role: Role of the user (patient or clinician)
            template_id: ID of the template containing supported actions
            user_input: Natural language input from the user
            db: Database session
            client_timezone_offset: Timezone offset from UTC in hours
            
        Returns:
            Either a ResolvedIntent or a dict with compound action information
        """
        try:
            # First, detect if this is a compound action
            compound_info = await self.detect_compound_action(user_input)
            
            if compound_info.get("is_compound", False):
                logger.info(f"Detected compound action with {compound_info['action_count']} actions")
                
                # Get the template
                from app.services.template_validation_service import TemplateValidationService
                template = await TemplateValidationService.validate_template_access(
                    db=db, template_id=template_id, user_id=user_id, roles=[user_role]
                )
                
                # Extract multiple intents
                intents = await self.extract_compound_intents(
                    template, user_input, compound_info, 
                    client_timezone_offset, user_role, db
                )
                
                # Convert to ChainedAction format
                from app.schemas.action_chain_v2 import ChainedIntent, ChainedAction, ExecutionMode, FailureMode
                
                if intents:
                    # Convert ResolvedIntent to ChainedIntent
                    chained_intents = []
                    for intent in intents:
                        if intent.result == IntentResolutionResult.SUCCESS:
                            # Convert parameters to dict format
                            params = {p.name: p.value for p in intent.parameters}
                            
                            chained_intent = ChainedIntent(
                                action_type=intent.action_type,
                                parameters=params,
                                confidence=intent.confidence_score
                            )
                            chained_intents.append(chained_intent)
                    
                    if chained_intents:
                        # Create ChainedAction
                        chain = ChainedAction(
                            primary_action=chained_intents[0],
                            follow_up_actions=chained_intents[1:] if len(chained_intents) > 1 else [],
                            execution_mode=ExecutionMode.SEQUENTIAL if compound_info.get("relationship") == "sequential" else ExecutionMode.PARALLEL,
                            failure_mode=FailureMode.CONTINUE_ON_FAILURE
                        )
                        
                        return {
                            "type": "compound",
                            "chain": chain,
                            "compound_info": compound_info,
                            "original_input": user_input
                        }
                
                # Fall back to single intent if compound extraction failed
                logger.warning("Compound action detection succeeded but intent extraction failed, falling back to single intent")
            
            # Process as single intent
            resolved_intent = await self.process_user_input(
                user_id, user_role, template_id, user_input, db, client_timezone_offset
            )
            
            return {
                "type": "single",
                "intent": resolved_intent,
                "original_input": user_input
            }
            
        except Exception as e:
            logger.error(f"Error in compound-aware processing: {e}", exc_info=True)
            # Fall back to simple processing
            return await self.process_user_input(
                user_id, user_role, template_id, user_input, db, client_timezone_offset
            )
