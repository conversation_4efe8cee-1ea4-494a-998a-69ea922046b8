"""
Service for managing LLM configuration settings.
Handles persistence and retrieval of OpenAI provider configuration.
"""

import json
import logging
from typing import Any, Union

from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.llm.providers.openai import OpenAIConfig
from app.crud.user_settings import get_setting, update_setting
from app.models.clinician import Clinician
from app.models.patient import Patient
from app.schemas.chat import (
    ChatConfigResponse,
    ChatConfigUpdate,
    ChatModelEnum,
    ChatTemperatureEnum,
)

logger = logging.getLogger(__name__)

# Setting keys
LLM_CONFIG_KEY = "llm_configuration"
DEFAULT_MODEL = ChatModelEnum.GPT_4
DEFAULT_TEMPERATURE = 0.7
DEFAULT_MAX_TOKENS = 500
DEFAULT_SYSTEM_PROMPT = """
You are a helpful assistant for the PulseTrack weight management platform.
Provide supportive and informative responses, but never give medical advice.
Always refer medical questions to healthcare providers.
Keep responses concise, clear, and supportive.
"""

# Available structured output schemas
STRUCTURED_OUTPUT_SCHEMAS = {
    "medication_reminder": "MedicationReminderSchema",
    "side_effect_report": "SideEffectReportSchema",
}


class LLMConfigService:
    """Service for managing LLM configuration."""

    @staticmethod
    async def get_user_config(
        user: Union[Patient, Clinician], db: AsyncSession
    ) -> ChatConfigResponse:
        """
        Get LLM configuration for a user (Patient or Clinician).

        Args:
            user: The user to get configuration for
            db: Database session

        Returns:
            ChatConfigResponse with the user's configuration
        """
        # Try to get user's saved configuration
        config_json = await get_setting(db=db, user_id=str(user.id), key=LLM_CONFIG_KEY)

        # Use default if no saved configuration
        if not config_json:
            logger.info(f"No saved LLM config for user {user.id}, using defaults")
            return ChatConfigResponse(
                model=DEFAULT_MODEL,
                temperature=DEFAULT_TEMPERATURE,
                max_tokens=DEFAULT_MAX_TOKENS,
                system_prompt_template=DEFAULT_SYSTEM_PROMPT,
                available_models=list(ChatModelEnum),
                supports_structured_output=True,
            )

        try:
            # Parse saved configuration
            config = json.loads(config_json)

            # Create response with all required fields
            return ChatConfigResponse(
                model=config.get("model", DEFAULT_MODEL),
                temperature=config.get("temperature", DEFAULT_TEMPERATURE),
                max_tokens=config.get("max_tokens", DEFAULT_MAX_TOKENS),
                system_prompt_template=config.get(
                    "system_prompt_template", DEFAULT_SYSTEM_PROMPT
                ),
                available_models=list(ChatModelEnum),
                supports_structured_output=True,
            )
        except (json.JSONDecodeError, ValidationError) as e:
            logger.error(f"Error parsing LLM config for user {user.id}: {e}")
            # Return defaults if parsing fails
            return ChatConfigResponse(
                model=DEFAULT_MODEL,
                temperature=DEFAULT_TEMPERATURE,
                max_tokens=DEFAULT_MAX_TOKENS,
                system_prompt_template=DEFAULT_SYSTEM_PROMPT,
                available_models=list(ChatModelEnum),
                supports_structured_output=True,
            )

    @staticmethod
    async def update_user_config(
        user: Union[Patient, Clinician],
        config_update: ChatConfigUpdate,
        db: AsyncSession,
    ) -> ChatConfigResponse:
        """
        Update LLM configuration for a user.

        Args:
            user: The user to update configuration for
            config_update: The updated configuration values
            db: Database session

        Returns:
            ChatConfigResponse with the user's updated configuration
        """
        # Get current configuration
        current_config = await LLMConfigService.get_user_config(user, db)

        # Create new configuration by updating fields that are not None
        updated_config = {
            "model": (
                config_update.model
                if config_update.model is not None
                else current_config.model
            ),
            "temperature": (
                config_update.temperature.value
                if isinstance(config_update.temperature, ChatTemperatureEnum)
                else (
                    config_update.temperature
                    if config_update.temperature is not None
                    else current_config.temperature
                )
            ),
            "max_tokens": (
                config_update.max_tokens
                if config_update.max_tokens is not None
                else current_config.max_tokens
            ),
            "system_prompt_template": (
                config_update.system_prompt_template
                if config_update.system_prompt_template is not None
                else current_config.system_prompt_template
            ),
        }

        # Save updated configuration
        await update_setting(
            db=db,
            user_id=str(user.id),
            key=LLM_CONFIG_KEY,
            value=json.dumps(updated_config),
        )

        logger.info(f"Updated LLM config for user {user.id}")

        # Return updated configuration
        return ChatConfigResponse(
            model=updated_config["model"],
            temperature=updated_config["temperature"],
            max_tokens=updated_config["max_tokens"],
            system_prompt_template=updated_config["system_prompt_template"],
            available_models=list(ChatModelEnum),
            supports_structured_output=True,
        )

    @staticmethod
    async def get_available_schemas() -> list[dict[str, Any]]:
        """
        Get available structured output schemas.

        Returns:
            List of schema information
        """
        schemas = []
        for key, schema_name in STRUCTURED_OUTPUT_SCHEMAS.items():
            schemas.append(
                {
                    "id": key,
                    "name": schema_name,
                    "description": f"Schema for {key.replace('_', ' ')} output format",
                }
            )
        return schemas

    @staticmethod
    def create_provider_config(user_config: ChatConfigResponse) -> OpenAIConfig:
        """
        Create OpenAI provider config from user configuration.

        Args:
            user_config: User's chat configuration

        Returns:
            OpenAIConfig for use with the OpenAI provider
        """
        return OpenAIConfig(
            model=user_config.model,
            temperature=user_config.temperature,
            max_tokens=user_config.max_tokens,
        )
