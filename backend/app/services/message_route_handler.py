"""
Message routing handler for clinician-patient communication.

This module provides functions to properly handle message routing
between clinicians, patients, and the AI assistant.

Message Routing Flow:

1. When a clinician sends a message in chat, they can choose to route it to:
   - AI: The message is processed by the AI and gets a response (default)
   - Patient: The message is sent directly to the patient and bypasses AI

2. For patient-routed messages:
   - The message is stored in the database with message_route="patient"
   - handle_patient_routed_message() is called which skips AI processing
   - The frontend displays a confirmation that the message was sent to the patient
   - No AI assistant response is generated for patient-routed messages

3. For AI-routed messages (default):
   - The message is processed normally through the AI pipeline
   - The AI response is displayed in the chat interface
   - If no intent is resolved, the message is handled via fallback_unresolved_intent()
"""

import logging
from enum import Enum
from typing import Any, Optional, Union

from sqlalchemy.orm import Session

from app.models.chat_message import MessageRouteType
from app.schemas.chat import MessageRouteEnum

logger = logging.getLogger(__name__)


def normalize_message_route(
    message_route_value: Optional[Union[str, Enum, Any]],
) -> Optional[MessageRouteType]:
    """
    Normalizes the message_route value from any supported format to a proper MessageRouteType.

    This function handles various formats of the message_route value that might come from
    the API request or context dictionary, and converts it to a valid MessageRouteType enum
    for database storage.

    Args:
        message_route_value: The value to normalize, which could be a string, an enum, or None.

    Returns:
        A MessageRouteType enum value if the input could be converted, None otherwise.
    """
    if message_route_value is None:
        return None

    # If it's already a MessageRouteType, return it directly
    if isinstance(message_route_value, MessageRouteType):
        return message_route_value

    # If it's a MessageRouteEnum, convert to MessageRouteType
    if isinstance(message_route_value, MessageRouteEnum):
        try:
            # Convert the enum value to a string, then to MessageRouteType
            return MessageRouteType(message_route_value.value.lower())
        except (ValueError, AttributeError) as e:
            logger.warning(
                f"Failed to convert MessageRouteEnum to MessageRouteType: {e}"
            )
            return None

    # If it's a string, try to convert directly to MessageRouteType
    if isinstance(message_route_value, str):
        try:
            # Normalize string to lowercase for case-insensitive matching
            normalized_value = message_route_value.lower()

            # Map common values to valid MessageRouteType values
            value_mapping = {
                "patient": MessageRouteType.PATIENT,
                "ai": MessageRouteType.AI,
            }

            # Check if the normalized value is in our mapping
            if normalized_value in value_mapping:
                return value_mapping[normalized_value]

            # If not in mapping, try direct enum conversion
            return MessageRouteType(normalized_value)
        except ValueError as e:
            logger.warning(
                f"Invalid message_route string value: {message_route_value}: {e}"
            )
            return None

    # If it's some other type (which shouldn't happen in normal operation)
    logger.warning(f"Unexpected message_route type: {type(message_route_value)}")
    return None


def handle_patient_routed_message(
    db: Session, message_id: str, patient_id: str
) -> dict:
    """
    Handle special routing for messages that should be delivered to a patient.

    This function is called when a clinician sends a message with route="patient".
    It performs any necessary processing to ensure the message is properly delivered
    to the patient (such as creating a notification, etc).

    Args:
        db: The database session
        message_id: The ID of the message being routed
        patient_id: The ID of the patient who should receive the message

    Returns:
        A dictionary with success status and a patient_routed flag to indicate this message
        was routed to a patient and should not be processed by AI
    """
    # Log that the message was routed to the patient
    logger.info(
        f"Message {message_id} from clinician routed directly to patient {patient_id}"
    )

    # In a future implementation, this could:
    # 1. Create a notification for the patient
    # 2. Send an email/push notification
    # 3. Update some status in the database

    # Return success status and patient_routed flag to signal this message should not go to AI
    return {
        "success": True,
        "patient_routed": True,
        "message": "Message routed directly to patient",
    }


def fallback_unresolved_intent(user_message: str, user_role: str = "clinician") -> dict:
    """
    Handle fallback for when the AI couldn't resolve an intent from the message.

    This function is called when:
    1. A message was routed to AI but no valid intent was resolved
    2. The intent resolution process failed to extract an action

    In these cases, we gracefully handle the failure by treating the message
    as a general conversation instead of a command.

    Args:
        user_message: The original message content from the user
        user_role: The role of the user (clinician, patient)

    Returns:
        A dictionary with success status and a conversation_fallback flag
    """
    # Log the fallback handling
    logger.info(
        f"Falling back to conversation mode for unresolved intent from {user_role}"
    )

    if user_role == "clinician":
        # For clinicians, when intent resolution fails, we need to treat this as
        # a message from the clinician that should be passed along to the
        # conversation system rather than treating it as an intent/action.
        return {
            "success": True,
            "conversation_fallback": True,
            "message": "I couldn't understand that as a command, so I'll treat it as a general message.",
        }
    else:
        # For patients, we can respond with a helpful message explaining the
        # system couldn't understand their request
        return {
            "success": True,
            "conversation_fallback": True,
            "message": "I'm not sure what you're trying to do. Could you try rephrasing your request?",
        }
