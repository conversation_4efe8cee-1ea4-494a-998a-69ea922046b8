"""
OpenAI Chat Handler for processing chat events using the OpenAI provider.
This handler uses the event-driven architecture to process chat messages.
"""

import logging
from typing import Any

from app.core.llm.base import LLMException, PromptTemplate
from app.core.llm.factory import LLMFactory
from app.crud.crud_chat_message import create_chat_message
from app.models.chat_message import MessageSenderType
from app.schemas.chat import ChatMessageCreateInternal

logger = logging.getLogger(__name__)


class OpenAIChatHandler:
    """
    Handler for chat message events using OpenAI.
    Processes chat messages and generates AI responses.
    """

    def __init__(self):
        self.system_prompt = """
        You are a supportive health coach for patients managing weight loss treatments.
        Provide encouragement, general wellness information, and educational content about treatments.
        You may provide general educational information about medications and side effects with appropriate disclaimers.
        NEVER give specific medical advice for individual cases or suggest dosage changes.
        Keep responses concise, empathetic, and focused on supporting the patient's journey.
        Address the patient directly and personalize your responses whenever possible.
        Always write helpful answers in simple, clear language that is easy to understand.
        Include disclaimers like "This is general information only - consult your healthcare provider for personalized advice" when appropriate.

        """

        self.prompt_template = PromptTemplate(
            template=(
                "{system_prompt}\n\nPatient message: {message}\n\nPatient context: {context}\n\nYour response:"
            ),
            input_variables=["system_prompt", "message", "context"],
            use_template=True,
        )

    async def _get_openai_provider(self):
        """Get an OpenAI provider instance"""
        try:
            provider = LLMFactory.create_provider(
                "openai",
                model="o4-mini",  # Consider using config
                temperature=0.7,
                max_tokens=300,  # Limit response length
            )
            return provider
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            raise

    async def handle_event(self, event: dict[str, Any]) -> None:
        """
        Handle a chat message event by generating and storing an AI response.

        Args:
            event: Event data containing message, context, and db session
        """
        logger.info("Handling ChatMessageSentEvent in OpenAIChatHandler")

        try:
            message = event.get("message", "")
            context = event.get("context", {}) or {}
            db = event.get("db")

            patient_id = context.get("patient_id")
            conversation_history = context.get("conversation_history", [])
            rag_context = context.get("rag_context", "")
            rag_metadata = context.get("rag_chunks_metadata", [])
            # Get message_route if provided
            message_route = context.get("message_route")

            if not patient_id:
                logger.error("Missing required 'patient_id' in event context")
                return

            if not db:
                logger.error("Missing required 'db' session in event")
                return

            # Format conversation history and context
            formatted_history = (
                "No previous conversation."
                if not conversation_history
                else "\n".join(
                    [
                        f"{msg.get('sender_type', 'unknown')}: {msg.get('message', '')}"
                        for msg in conversation_history[-3:]
                    ]
                )
            )

            # Check if we have substantial RAG context for document Q&A
            has_document_context = len(rag_metadata) > 0 and any(
                chunk.get("similarity_score", 0) > 0.5 for chunk in rag_metadata
            )

            formatted_context = f"Conversation history: {formatted_history}\n"
            if rag_context:
                if has_document_context:
                    # Add a more prominent header for document context
                    formatted_context += f"\n=== AVAILABLE INFORMATION SOURCES ===\n{rag_context}\n"
                    formatted_context += "\nPlease use the above sources to provide accurate, helpful information to the patient.\n"
                else:
                    formatted_context += f"Additional context: {rag_context}\n"
            
            # Enhance system prompt for document Q&A
            enhanced_system_prompt = self.system_prompt
            if has_document_context:
                document_qa_prompt = """
You have access to specific clinic information and educational materials. When answering questions:

1. Base your answers primarily on the provided source materials when relevant
2. If specific information is provided in the sources, use it in your response
3. If the information isn't in the provided sources but you can provide general guidance, clearly distinguish between the two
4. Be accurate and cite information naturally in your response (e.g., "According to the clinic information...")
5. Synthesize information from multiple sources when relevant

Remember: You have access to clinic-specific information that should be prioritized when answering questions about the clinic, its services, or educational materials.

"""
                enhanced_system_prompt = document_qa_prompt + enhanced_system_prompt

            # Get OpenAI provider
            provider = await self._get_openai_provider()

            try:
                # Prepare variables with proper validation
                template_vars = {
                    "system_prompt": enhanced_system_prompt,
                    "message": message,
                    "context": formatted_context,
                }

                # Ensure all variables are strings
                for key, value in template_vars.items():
                    if not isinstance(value, str):
                        template_vars[key] = str(value)

                # Log the variables being sent to the template
                var_preview = {}
                for k, v in template_vars.items():
                    if isinstance(v, str) and len(v) > 30:
                        var_preview[k] = v[:30] + "..."
                    else:
                        var_preview[k] = v
                logger.debug(f"Sending to template - variables: {var_preview}")

                # Generate response using prompt template
                response = await provider.generate_with_template(
                    template=self.prompt_template, variables=template_vars
                )

                logger.info("Received AI response from OpenAI")

                # Check for placeholder responses
                response_content = response.content
                placeholder_patterns = [
                    "{assistant_response}",
                    "{assistant response}",
                    "{response}",
                    "{{assistant_response}}",
                    "{{response}}",
                    "{{",
                    "}}",
                ]

                if "I'm an AI language model" in response_content or any(
                    pattern in response_content for pattern in placeholder_patterns
                ):
                    logger.warning(
                        "Detected placeholder in response. Using fallback message."
                    )
                    response_content = (
                        "Technical difficulties. Please try again or contact support."
                    )

                # Persist AI response as a new chat message
                create_chat_message(
                    db=db,
                    obj_in=ChatMessageCreateInternal(
                        patient_id=patient_id,
                        sender_type=MessageSenderType.AGENT,
                        message_content=response_content,
                        message_route=message_route,  # Pass the message_route to AI responses as well
                    ),
                )
                logger.info("Persisted AI response as chat message")

                # Clean up provider resources
                await provider.close()

            except LLMException as e:
                logger.error(f"Error generating OpenAI response: {e}")
                # Save a fallback message
                create_chat_message(
                    db=db,
                    obj_in=ChatMessageCreateInternal(
                        patient_id=patient_id,
                        sender_type=MessageSenderType.AGENT,
                        message_content="Please try again in a moment or contact support.",
                        message_route=message_route,  # Pass the message_route to fallback responses as well
                    ),
                )
                logger.info("Persisted fallback response message due to LLM error")

        except Exception as e:
            logger.error(f"Error handling ChatMessageSentEvent: {e}", exc_info=True)
