import os
import random
import time
from collections.abc import Mapping, Sequence
from math import ceil
from typing import Any, Optional, TypeVar

import anyio
import httpx
import openai
import structlog
import tiktoken
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion

T = TypeVar("T")
logger = structlog.get_logger(__name__)


class Singleton(type):
    """Metaclass to implement the Singleton pattern."""

    _instances: dict[type, Any] = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


class OpenAIClient(metaclass=Singleton):
    """A process-wide singleton client for OpenAI API interactions.

    This client handles:
    - API key management
    - Concurrency limiting
    - Retry with exponential backoff
    - Logging of latency and token usage

    Usage:
        client = OpenAIClient()
        response = await client.chat(
            model="gpt-4",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=100
        )
    """

    def __init__(self):
        """Initialize the OpenAI client with configuration from environment variables.

        Environment variables:
        - OPENAI_API_KEY: API key for OpenAI (required)
        - OPENAI_TIMEOUT_SEC: Timeout in seconds (default: 30)
        - OPENAI_MAX_CONCURRENCY: Maximum concurrent requests (default: 4)
        - OPENAI_MAX_CONCURRENCY_TOTAL: Total concurrency across all workers (optional)

        Raises:
            RuntimeError: If OPENAI_API_KEY is not set in production environment
        """
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            # In production, fail fast if API key is missing
            if os.environ.get("ENVIRONMENT", "").lower() in ("production", "prod"):
                raise RuntimeError(
                    "OPENAI_API_KEY environment variable is required in production"
                )
            logger.warning(
                "OPENAI_API_KEY not found in environment variables, using empty string"
            )
            api_key = ""

        self.timeout_sec = int(os.environ.get("OPENAI_TIMEOUT_SEC", "30"))

        # Calculate per-process concurrency limit
        # If OPENAI_MAX_CONCURRENCY_TOTAL is set, distribute across workers
        total_concurrency = os.environ.get("OPENAI_MAX_CONCURRENCY_TOTAL")
        if total_concurrency:
            # Get worker count from common environment variables
            worker_count = int(
                os.environ.get(
                    "UVICORN_WORKERS",
                    os.environ.get(
                        "GUNICORN_WORKERS", os.environ.get("WORKER_COUNT", "1")
                    ),
                )
            )
            max_concurrency = ceil(int(total_concurrency) / worker_count)
            logger.info(
                "Calculated per-worker concurrency",
                total_concurrency=total_concurrency,
                worker_count=worker_count,
                per_worker_concurrency=max_concurrency,
            )
        else:
            max_concurrency = int(os.environ.get("OPENAI_MAX_CONCURRENCY", "4"))

        # Create a shared HTTP client for better connection pooling
        self._http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout_sec, connect=self.timeout_sec),
            limits=httpx.Limits(max_connections=max_concurrency * 2),
        )

        self._client = AsyncOpenAI(api_key=api_key, http_client=self._http_client)
        self._sem = anyio.CapacityLimiter(max_concurrency)
        self._encoders = {}

        logger.info(
            "OpenAI client initialized",
            timeout_sec=self.timeout_sec,
            max_concurrency=max_concurrency,
        )

    async def shutdown(self):
        """Properly close the OpenAI client to avoid connection leaks."""
        # Note: We only need to close the client, which will close the underlying HTTP client
        if hasattr(self, "_client"):
            await self._client.close()
            logger.info("OpenAI client closed")

    def __del__(self):
        """Attempt to clean up resources when the object is garbage collected."""
        # Note: __del__ can't await, so we just log a warning if client wasn't properly shutdown
        if hasattr(self, "_http_client") and not self._http_client.is_closed:
            logger.warning(
                "OpenAI client was not properly shutdown. Call 'await client.shutdown()' "
                "to avoid connection leaks."
            )

    def _count_tokens(self, model: str, messages: Sequence[Mapping[str, str]]) -> int:
        """Count tokens in messages using tiktoken.

        Args:
            model: The model to count tokens for
            messages: The messages to count tokens in

        Returns:
            The number of tokens in the messages
        """
        try:
            # Cache encoders by model
            if model not in self._encoders:
                self._encoders[model] = tiktoken.encoding_for_model(model)

            encoder = self._encoders[model]
            return sum(len(encoder.encode(m.get("content", ""))) for m in messages)
        except Exception as e:
            # Fall back to approximation if tiktoken fails
            logger.warning(
                "Failed to count tokens with tiktoken, using approximation",
                error=str(e),
                model=model,
            )
            return sum(len(m.get("content", "")) // 4 for m in messages)

    async def chat(
        self,
        *,
        model: str,
        messages: Sequence[Mapping[str, str]],
        max_tokens: int,
        extra: Optional[Mapping[str, Any]] = None,
        **kwargs: Any
    ) -> ChatCompletion:
        """Send a chat completion request to OpenAI with retry logic.

        Args:
            model: The OpenAI model to use (e.g., "gpt-4")
            messages: List of message dictionaries with role and content
            max_tokens: Maximum tokens to generate in the response
            extra: Additional parameters to pass to the OpenAI API
            **kwargs: Additional keyword arguments (for backward compatibility)

        Returns:
            The raw OpenAI ChatCompletion response

        Raises:
            Exception: If all retry attempts fail
        """
        start_time = time.time()
        combined_kwargs = {**(extra or {}), **kwargs}
        request_id = combined_kwargs.get("user", "unknown")
        retry_count = 0
        max_retries = 3

        # Count prompt tokens using tiktoken
        prompt_tokens = self._count_tokens(model, messages)

        async with self._sem:
            while True:
                try:
                    logger.debug(
                        "Sending chat request",
                        model=model,
                        request_id=request_id,
                        retry_count=retry_count,
                        prompt_tokens=prompt_tokens,
                    )

                    # Apply per-attempt timeout
                    async with anyio.fail_after(self.timeout_sec):
                        response = await self._client.chat.completions.create(
                            model=model,
                            messages=messages,
                            max_tokens=max_tokens,
                            **combined_kwargs
                        )

                    # Log completion information
                    latency = time.time() - start_time
                    usage = response.usage

                    logger.info(
                        "Chat request completed",
                        request_id=request_id,
                        latency_sec=round(latency, 3),
                        prompt_tokens=usage.prompt_tokens if usage else None,
                        completion_tokens=usage.completion_tokens if usage else None,
                        total_tokens=usage.total_tokens if usage else None,
                        retry_count=retry_count,
                        model=model,
                    )

                    return response

                except (openai.OpenAIError, httpx.HTTPError, Exception) as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(
                            "Max retries exceeded for OpenAI request",
                            request_id=request_id,
                            error=str(e),
                            error_type=type(e).__name__,
                            retry_count=retry_count,
                            model=model,
                        )
                        raise

                    # Extract HTTP status if available
                    status_code = getattr(e, "status_code", None)

                    # Exponential backoff with jitter
                    backoff = (2**retry_count) + random.uniform(0, 1)
                    logger.warning(
                        "OpenAI request failed, retrying",
                        request_id=request_id,
                        error=str(e),
                        error_type=type(e).__name__,
                        status_code=status_code,
                        retry_count=retry_count,
                        backoff_sec=round(backoff, 2),
                        model=model,
                    )

                    await anyio.sleep(backoff)


# FastAPI dependency
def get_openai_client() -> OpenAIClient:
    """FastAPI dependency that returns the OpenAI client singleton.

    Returns:
        The singleton instance of OpenAIClient
    """
    return OpenAIClient()
