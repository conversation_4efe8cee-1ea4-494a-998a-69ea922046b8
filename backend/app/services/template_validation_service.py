"""Template validation service for LLM-driven API actions.

This service validates templates against schema requirements,
ensures actions are valid, and checks that system prompts
meet security and safety requirements.
"""

import datetime
import functools
import logging
from typing import Any, Optional, Union
from uuid import UUID

from fastapi import HTTPEx<PERSON>, status
from pydantic import BaseModel, ValidationError

from app import crud, models, schemas

logger = logging.getLogger(__name__)

# Role constants to match the ones used in the application
ROLE_ADMIN = "admin"
ROLE_CLINICIAN = "clinician"
ROLE_PATIENT = "patient"

# Template action types that the system supports
SUPPORTED_ACTIONS = [
    "appointment_create",
    "appointment_cancel",
    "medication_request_create",
    "medication_request_update",
    "note_create",
    "notification_create",
    "patient_search",
    "query_history",
    "health_data_retrieve",
]

# Role permission mapping for actions
ACTION_ROLE_PERMISSIONS = {
    "appointment_create": [ROLE_PATIENT, ROLE_CLINICIAN, ROLE_ADMIN],
    "appointment_cancel": [ROL<PERSON>_PATIENT, ROL<PERSON>_CLINICIAN, R<PERSON><PERSON>_ADMIN],
    "medication_request_create": [ROLE_CLINICIAN, R<PERSON>E_ADMIN],
    "medication_request_update": [ROLE_CLINICIAN, ROLE_ADMIN],
    "note_create": [ROLE_CLINICIAN, ROLE_ADMIN],
    "notification_create": [ROLE_CLINICIAN, ROLE_ADMIN],
    "patient_search": [ROLE_CLINICIAN, ROLE_ADMIN],
    "query_history": [ROLE_PATIENT, ROLE_CLINICIAN, ROLE_ADMIN],
    "health_data_retrieve": [ROLE_PATIENT, ROLE_CLINICIAN, ROLE_ADMIN],
}

# System prompt safety checks
FORBIDDEN_PROMPT_PATTERNS = [
    "jailbreak",
    "ignore previous",
    "ignore all",
    "bypass",
    "credentials",
    "password",
    "admin access",
    "root access",
    "api key",
    "secret key",
    "token",
    "authentication",
]


# Template schemas
class ActionParameter(BaseModel):
    """Schema for action parameters."""

    name: str
    description: str
    required: bool = False
    type: str
    validation_pattern: Optional[str] = None


class TemplateAction(BaseModel):
    """Schema for template actions."""

    name: str
    description: str
    action_type: str
    parameters: list[ActionParameter]


class TemplateSchema(BaseModel):
    """Schema for validating template structure."""

    name: str
    description: Optional[str] = None
    version: str
    system_prompt: str
    actions: list[TemplateAction]


class TemplateValidationService:
    """Service for validating templates."""

    _template_cache = {}
    _cache_timeout = 300  # seconds

    @staticmethod
    @functools.lru_cache(maxsize=64)
    async def _cached_get_by_roles(db_hash, roles_tuple):
        # NOTE: db_hash should be a hash of the DB connection string or similar unique value
        # roles_tuple: tuple of roles
        from app import crud

        return crud.template.get_by_roles(db_hash, roles=roles_tuple)

    @staticmethod
    def validate_template(
        template_data: Union[dict[str, Any], schemas.TemplateCreate],
    ) -> None:
        """
        Validate a template against schema requirements and safety checks.

        Args:
            template_data: The template data to validate

        Raises:
            HTTPException: If validation fails
        """
        try:
            if isinstance(template_data, schemas.TemplateCreate):
                # Convert to dict if it's a Pydantic model
                template_dict = template_data.dict()
            else:
                template_dict = template_data

            # Convert template to proper schema for validation
            template_schema = {
                "name": template_dict.get("name", ""),
                "description": template_dict.get("description", ""),
                "version": template_dict.get("version", "1.0"),
                "system_prompt": template_dict.get("system_prompt", ""),
                "actions": template_dict.get("actions", []),
            }

            # Validate against schema
            try:
                TemplateSchema(**template_schema)
            except ValidationError as e:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Template validation failed: {str(e)}",
                )

            # Validate system prompt for safety
            TemplateValidationService._validate_system_prompt(
                template_schema["system_prompt"]
            )

            # Validate actions
            TemplateValidationService._validate_actions(template_schema["actions"])

            # Validate role assignments if present
            if "roles" in template_dict and template_dict["roles"]:
                TemplateValidationService._validate_roles(
                    template_dict["roles"], template_schema["actions"]
                )

            logger.info(f"Template '{template_schema['name']}' passed validation")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error during template validation: {str(e)}", exc_info=True
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Template validation error: {str(e)}",
            )

    @staticmethod
    def _validate_system_prompt(system_prompt: str) -> None:
        """
        Validate that a system prompt meets safety requirements.

        Args:
            system_prompt: The system prompt to validate

        Raises:
            HTTPException: If system prompt contains forbidden patterns
        """
        if not system_prompt:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="System prompt is required",
            )

        if len(system_prompt) < 50:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="System prompt is too short (minimum 50 characters)",
            )

        system_prompt_lower = system_prompt.lower()
        for pattern in FORBIDDEN_PROMPT_PATTERNS:
            if pattern in system_prompt_lower:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"System prompt contains forbidden content: '{pattern}'",
                )

        # Additional safety checks
        if (
            "ignore previous instructions" in system_prompt_lower
            or "ignore all instructions" in system_prompt_lower
        ):
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="System prompt contains unauthorized bypass attempts",
            )

    @staticmethod
    def _validate_actions(actions: list[dict[str, Any]]) -> None:
        """
        Validate that template actions are supported and properly structured.

        Args:
            actions: List of action definitions to validate

        Raises:
            HTTPException: If actions validation fails
        """
        if not actions:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Template must define at least one action",
            )

        # Check for duplicate action names
        action_names = [action.get("name", "") for action in actions]
        if len(action_names) != len(set(action_names)):
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Duplicate action names are not allowed",
            )

        for action in actions:
            action_type = action.get("action_type", "")
            # Check if action type is supported
            if action_type not in SUPPORTED_ACTIONS:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Unsupported action type: {action_type}",
                )

            # Validate parameters
            parameters = action.get("parameters", [])
            param_names = [param.get("name", "") for param in parameters]
            if len(param_names) != len(set(param_names)):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Duplicate parameter names in action '{action.get('name', '')}'",
                )

            for param in parameters:
                # Validate parameter type
                param_type = param.get("type", "")
                if param_type not in [
                    "string",
                    "number",
                    "boolean",
                    "array",
                    "object",
                    "date",
                ]:
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail=f"Invalid parameter type '{param_type}' in action '{action.get('name', '')}'",
                    )

    @staticmethod
    def _validate_roles(roles: list[str], actions: list[dict[str, Any]]) -> None:
        """
        Validate that assigned roles have permissions for the template actions.

        Args:
            roles: List of role names assigned to the template
            actions: List of action definitions in the template

        Raises:
            HTTPException: If role permissions validation fails
        """
        valid_roles = [ROLE_ADMIN, ROLE_CLINICIAN, ROLE_PATIENT]
        for role in roles:
            if role not in valid_roles:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Invalid role: {role}",
                )

        # Check if at least one role has permission for each action
        for action in actions:
            action_type = action.get("action_type", "")
            if action_type in ACTION_ROLE_PERMISSIONS:
                allowed_roles = ACTION_ROLE_PERMISSIONS[action_type]
                if not any(role in allowed_roles for role in roles):
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail=f"None of the assigned roles {roles} have permission for action '{action_type}'. Allowed roles: {allowed_roles}",
                    )

    @staticmethod
    async def validate_and_get_accessible_templates(
        db, user_id: str, role: Union[str, list[str]]
    ) -> list[models.Template]:
        """
        Get templates accessible to a user and role, with caching for performance.
        """
        roles = [role] if isinstance(role, str) else role
        # Admin role has access to all templates, no caching
        if ROLE_ADMIN in roles:
            return crud.template.get_multi(db)
        # Compute a cache key based on DB identity and roles
        db_hash = str(hash(str(db)))
        roles_tuple = tuple(sorted(roles))
        cache_key = (db_hash, roles_tuple)
        # Try cache first
        if cache_key in TemplateValidationService._template_cache:
            cached_entry = TemplateValidationService._template_cache[cache_key]
            if (
                datetime.datetime.utcnow() - cached_entry["timestamp"]
            ).total_seconds() < TemplateValidationService._cache_timeout:
                return cached_entry["templates"]
        # Not cached or expired: fetch and cache
        templates = crud.template.get_by_roles(db, roles=roles)
        TemplateValidationService._template_cache[cache_key] = {
            "templates": templates,
            "timestamp": datetime.datetime.utcnow(),
        }
        # For clinicians, also include templates for their clinics (not cached since clinics may change)
        if ROLE_CLINICIAN in roles:
            clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=user_id)
            if clinician:
                clinic_ids = [
                    clinic.id
                    for clinic in crud.clinician.get_clinics_for_clinician(
                        db, clinician_id=clinician.id
                    )
                ]
                if clinic_ids:
                    clinic_templates = []
                    for clinic_id in clinic_ids:
                        clinic_temps = crud.template.get_multi_by_clinic(
                            db, clinic_id=clinic_id
                        )
                        clinic_templates.extend(clinic_temps)
                    # Merge unique templates
                    template_dict = {t.id: t for t in templates}
                    for t in clinic_templates:
                        template_dict[t.id] = t
                    templates = list(template_dict.values())
        return templates

    @staticmethod
    async def validate_template_access(
        db, template_id: UUID, user_id: str, roles: list[str]
    ) -> models.Template:
        """
        Validate if a user has access to a specific template.

        Args:
            db: Database session
            template_id: Template ID to check access for
            user_id: User ID requesting access
            roles: Roles of the user requesting access

        Returns:
            Template model if access is granted

        Raises:
            HTTPException: If access is denied
        """
        # Get template
        template = crud.template.get(db, id=template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
            )

        # Admin has access to all templates
        if ROLE_ADMIN in roles:
            return template

        # Check if template is assigned to any of user's roles
        template_roles = [role.role for role in template.roles]
        if any(role in template_roles for role in roles):
            return template

        # For clinicians, check if template is assigned to their clinics
        if ROLE_CLINICIAN in roles:
            clinician = crud.clinician.get_clinician_by_clerk_id(db, clerk_id=user_id)
            if clinician:
                clinic_ids = [
                    clinic.id
                    for clinic in crud.clinician.get_clinics_for_clinician(
                        db, clinician_id=clinician.id
                    )
                ]
                if template.clinic_id in clinic_ids:
                    return template

        # Access denied
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have access to this template",
        )


class PermissionValidator:
    """RBAC utility for patient/clinician action checks."""

    @staticmethod
    def can_patient(user_id: str, patient_id: str) -> bool:
        # Only allow if user is the patient (Clerk ID match, both not None)
        return bool(user_id and patient_id and user_id == patient_id)
