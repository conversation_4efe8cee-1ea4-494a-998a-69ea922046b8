import logging
import time
import uuid  # Import uuid for type hint

# from sqlalchemy.orm import Session # No longer needed as argument
from urllib.parse import urljoin, urlparse
from urllib.robotparser import RobotFileParser

import redis  # Import synchronous redis library
import requests
from bs4 import BeautifulSoup

# Import settings to get Redis URL
from app.core.config import settings

# Import CRUD and Schema
from app.crud import crud_scraped_page

# Import SessionLocal to create session within the task
from app.db.session import SessionLocal
from app.schemas.scraped_page import ScrapedPageCreate
from app.services.embedding_pipeline import run_embedding_pipeline_for_clinic

logger = logging.getLogger(__name__)

USER_AGENT = "PulseTrackBot/1.0 (+https://pulsetrack.example.com/bot-info)"
HEADERS = {"User-Agent": USER_AGENT}

MAX_DEPTH = 2
MAX_PAGES = 10
REQUEST_DELAY = 1  # seconds


def scrape_clinic_website(
    clinic_url: str,
    clinic_id: uuid.UUID,
    task_id: str,  # Add task_id parameter
    # task_status_dict parameter removed
):
    redis_client = None
    try:
        # Initialize Redis client
        if settings.REDIS_URL:
            redis_client = redis.from_url(
                settings.REDIS_URL, encoding="utf-8", decode_responses=True
            )
            # Update status to RUNNING immediately in Redis with 1-hour expiry
            redis_client.setex(task_id, 3600, "RUNNING")
            logger.info(f"Background Task: Set task {task_id} to RUNNING in Redis")
        else:
            logger.error(
                f"Background Task: REDIS_URL not configured. Cannot update status for task {task_id}."
            )
            # Optionally raise an error or return early if Redis is essential
            return  # Exit if Redis is not configured

        logger.info(
            f"Background Task: Starting scrape for task_id={task_id}, clinic_id={clinic_id}, url={clinic_url}"
        )
        # Create a new database session for this background task
        with SessionLocal() as db:
            # Check robots.txt
            # Ensure clinic_url is a string before parsing
            parsed_url = urlparse(str(clinic_url))
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            rp = RobotFileParser()
            can_fetch_url = True  # Assume we can fetch unless robots.txt says otherwise
            try:
                # Ensure robots_url is a string
                rp.set_url(str(robots_url))
                rp.read()  # Moved inside try block
                # Ensure clinic_url is a string for can_fetch
                if not rp.can_fetch(USER_AGENT, str(clinic_url)):
                    logger.warning(f"Blocked by robots.txt for {clinic_url}")
                    can_fetch_url = False  # Set flag to false if blocked
            except Exception as e:
                logger.warning(
                    f"Could not read or parse robots.txt at {robots_url}: {e}"
                )
                # Decide if we should proceed without robots.txt check or return
                # For now, let's assume we can proceed if robots.txt is unavailable/unreadable
                pass

            if not can_fetch_url:  # Check the flag after the try-except block
                return  # Exit if blocked by robots.txt

            visited = set()
            # Ensure the initial URL is a string
            to_visit = [(str(clinic_url), 0)]
            # Removed scraped_text_blocks list

            while to_visit and len(visited) < MAX_PAGES:
                url_from_list, depth = to_visit.pop(0)
                # Ensure url is treated as a string within the loop
                url = str(url_from_list)
                if url in visited or depth > MAX_DEPTH:
                    continue

                try:
                    logger.info(f"Fetching URL: {url} (depth {depth})")
                    resp = requests.get(url, headers=HEADERS, timeout=10)
                    resp.raise_for_status()
                    content_type = resp.headers.get("Content-Type", "")
                    if "text/html" not in content_type:
                        logger.info(f"Skipping non-HTML content at {url}")
                        continue

                    soup = BeautifulSoup(resp.text, "html.parser")
                    # Extract visible text
                    texts = soup.stripped_strings
                    page_text = "\n".join(texts).strip()
                    if page_text:  # Only save if there's actual content
                        page_data = ScrapedPageCreate(
                            source_url=url,
                            clinic_id=clinic_id,
                            cleaned_content=page_text,
                            # page_metadata could be added here if needed (e.g., {'title': soup.title.string})
                        )
                        # Use the session created with 'with SessionLocal() as db:'
                        try:
                            crud_scraped_page.create_or_update_scraped_page(
                                db=db, obj_in=page_data
                            )
                            logger.info(
                                f"Background Task: Saved/Updated content for {url}"
                            )
                        except Exception as db_exc:
                            logger.error(
                                f"Background Task: Database error saving content for {url}: {db_exc}",
                                exc_info=True,
                            )
                            # Log and continue within the session scope

                    visited.add(url)

                    # Enqueue internal links
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")  # Use .get for safety
                        # Ensure both url and href are strings for urljoin
                        next_url = urljoin(str(url), str(href))
                        # Stay within the same domain
                        # Ensure next_url is string for urlparse
                        if (
                            urlparse(str(next_url)).netloc == parsed_url.netloc
                            and next_url not in visited
                        ):
                            to_visit.append((next_url, depth + 1))

                    time.sleep(REQUEST_DELAY)

                except requests.RequestException as e:
                    logger.warning(f"Request failed for {url}: {e}")
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}", exc_info=True)

            # Log completion and update status
            logger.info(
                f"Background Task: Scraping complete for task_id={task_id}, clinic_id={clinic_id}. Pages visited: {len(visited)}"
            )
            if redis_client:
                redis_client.setex(task_id, 3600, "COMPLETED")  # Update status in Redis
                logger.info(
                    f"Background Task: Set task {task_id} to COMPLETED in Redis"
                )

            # -----------------------------------------------------------------
            # After successful scraping, trigger the embedding pipeline
            # -----------------------------------------------------------------
            try:
                logger.info(
                    f"Background Task: Starting embedding pipeline for clinic_id={clinic_id}"
                )
                run_embedding_pipeline_for_clinic(clinic_id)
                logger.info(
                    f"Background Task: Embedding pipeline finished for clinic_id={clinic_id}"
                )
            except Exception as embed_err:
                logger.error(
                    f"Background Task: Error running embedding pipeline for clinic_id={clinic_id}: {embed_err}",
                    exc_info=True,
                )

    except Exception as e:
        logger.error(
            f"Background Task: Fatal error during scraping for task_id={task_id}, clinic_id={clinic_id}: {e}",
            exc_info=True,
        )
        # Update status to FAILED on fatal error in Redis
        if redis_client:
            redis_client.setex(task_id, 3600, "FAILED")  # Update status in Redis
            logger.info(f"Background Task: Set task {task_id} to FAILED in Redis")
    finally:
        # Close Redis connection if it was opened
        if redis_client:
            try:
                redis_client.close()
                logger.info(
                    f"Background Task: Closed Redis connection for task {task_id}"
                )
            except Exception as redis_close_err:
                logger.error(
                    f"Background Task: Error closing Redis connection for task {task_id}: {redis_close_err}"
                )
