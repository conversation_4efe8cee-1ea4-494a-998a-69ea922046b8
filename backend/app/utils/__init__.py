"""
Utility functions for the PulseTrack application.
"""

import logging
from datetime import datetime, timezone
from typing import Any, Optional


def get_utc_now() -> datetime:
    """
    Get current UTC datetime with timezone information.
    """
    return datetime.now(timezone.utc)


def log_error(
    message: str,
    logger: Optional[logging.Logger] = None,
    exc: Optional[Exception] = None,
    context: Optional[dict[str, Any]] = None,
    level: str = "error",
) -> None:
    """
    Standardized error logging function.

    Args:
        message: The error message
        logger: Optional logger instance (if None, a default logger will be used)
        exc: Optional exception object
        context: Optional contextual information
        level: Log level (default: "error")
    """
    # Create a default logger if none is provided
    if logger is None:
        logger = logging.getLogger(__name__)

    log_data = {
        "message": message,
        "timestamp": get_utc_now().isoformat(),
    }

    if context:
        log_data["context"] = context

    if exc:
        log_data["exception"] = str(exc)
        log_data["exception_type"] = type(exc).__name__

    # Use the appropriate logging level
    if level.lower() == "debug":
        logger.debug(log_data, exc_info=exc is not None)
    elif level.lower() == "info":
        logger.info(log_data, exc_info=exc is not None)
    elif level.lower() == "warning":
        logger.warning(log_data, exc_info=exc is not None)
    else:  # Default to error
        logger.error(log_data, exc_info=exc is not None)
