import logging
from typing import Any, Optional

# Import necessary components
from sqlalchemy.orm import Session

from app.models.audit_log import AuditLog

# Configure logging
logger = logging.getLogger(__name__)


def log_audit_event(
    *,  # Enforce keyword arguments
    db: Session,  # Pass the DB session
    actor_user_id: Optional[str],
    actor_role: Optional[str],
    action: str,
    outcome: str,  # e.g., 'SUCCESS', 'FAILURE', 'ATTEMPT'
    target_resource_type: Optional[str] = None,
    target_resource_id: Optional[str] = None,
    details: Optional[dict[str, Any]] = None,
    source_ip: Optional[
        str
    ] = None,  # Note: Need to implement IP extraction in endpoints if required
) -> None:
    """
    Logs an audit event to the database.

    Relies on the calling endpoint's transaction management (commit/rollback).

    Args:
        db: The database session.
        actor_user_id: ID of the user performing the action (UUID as string).
        actor_role: Role of the user (e.g., 'patient', 'clinician').
        action: Description of the action (e.g., 'LOGIN_ATTEMPT', 'UPDATE_PATIENT_PROFILE').
        outcome: Result of the action ('SUCCESS', 'FAILURE', 'ATTEMPT').
        target_resource_type: Type of resource affected (e.g., 'Patient', 'WeightLog').
        target_resource_id: ID of the resource affected (UUID as string).
        details: Additional context-specific information (should avoid sensitive PII).
        source_ip: IP address of the requestor (optional).
    """
    try:
        # Ensure IDs are strings if they are UUID objects
        if hasattr(actor_user_id, "hex"):
            actor_user_id = str(actor_user_id)
        if hasattr(target_resource_id, "hex"):
            target_resource_id = str(target_resource_id)

        # Check if we're in a testing environment
        # This allows tests to run without needing the audit_logs table in SQLite
        # Set TESTING=1 in conftest.py or the test environment
        import os

        is_test = os.environ.get("TESTING", "").lower() in ("1", "true", "yes")

        # Only try to add to database if not in test mode or if audit_logs table exists
        if not is_test:
            try:
                db_log = AuditLog(
                    actor_user_id=actor_user_id,
                    actor_role=actor_role,
                    action=action,
                    outcome=outcome,
                    target_resource_type=target_resource_type,
                    target_resource_id=target_resource_id,
                    details=(
                        details if details else {}
                    ),  # Ensure details is a dict, even if None
                    source_ip=source_ip,
                )
                db.add(db_log)
                # Let the calling endpoint handle db.commit() or db.rollback()
                logger.debug(
                    f"Audit log added to session: {action} by {actor_user_id or 'System'}"
                )
            except Exception as e:
                logger.error(f"Failed to add audit log to session: {e}", exc_info=True)
                # Do not raise the exception or rollback here, as audit logging failure
                # should generally not interrupt the main operation.
        else:
            # Just log to console in test mode
            logger.debug(
                f"TEST MODE: Audit log for {action} by {actor_user_id or 'System'} (not saved to DB)"
            )

    except Exception as e:
        logger.error(f"Failed to process audit log: {e}", exc_info=True)
