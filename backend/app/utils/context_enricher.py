import logging
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy.orm import Session

from app import crud
from app.core.cache.rag_cache import rag_cache
from app.crud.crud_clinician import get_clinician_by_id, get_clinics_for_clinician
from app.crud.crud_side_effect_report import side_effect_report
from app.crud.crud_weight_log import weight_log
from app.models.clinician import Clinician
from app.models.medication_request import MedicationRequestStatus

logger = logging.getLogger(__name__)


def _filter_none(d: dict[str, Any]) -> dict[str, Any]:
    """Utility to filter out None values from a dictionary."""
    return {k: v for k, v in d.items() if v is not None}


def get_patient_health_metrics(db: Session, patient_id: str) -> dict:
    """
    Retrieve and format the latest weight, weight history (last 3), recent side effect reports (last 2),
    and active medication information for a patient. Each section uses try/except for error handling and logs warnings.
    Returns whatever data is available.
    """
    metrics = {}

    # Latest weight and weight history (last 3 entries)
    try:
        weight_logs = weight_log.get_by_patient(
            db, patient_id=patient_id, skip=0, limit=3
        )
        weight_history = [
            {
                "log_date": (
                    wl.log_date.isoformat()
                    if hasattr(wl.log_date, "isoformat")
                    else str(wl.log_date)
                ),
                "weight_kg": wl.weight_kg,
            }
            for wl in weight_logs
        ]
        metrics["weight_history"] = weight_history
        metrics["latest_weight"] = weight_history[0] if weight_history else None
    except Exception as e:
        logger.warning(
            f"Failed to retrieve weight logs for patient {patient_id}: {e}",
            exc_info=True,
        )
        metrics["weight_history"] = []
        metrics["latest_weight"] = None

    # Recent side effect reports (last 2)
    try:
        side_effects = side_effect_report.get_by_patient(
            db,
            patient_id=patient_id,
            skip=0,
            limit=2,
            sort_by="reported_at",
            sort_desc=True,
        )
        recent_side_effects = [
            {
                "id": se.id,
                "reported_at": (
                    se.reported_at.isoformat()
                    if hasattr(se.reported_at, "isoformat")
                    else str(se.reported_at)
                ),
                "description": se.description,
                "severity": str(se.severity),
                "status": se.status,
            }
            for se in side_effects
        ]
        metrics["recent_side_effects"] = recent_side_effects
    except Exception as e:
        logger.warning(
            f"Failed to retrieve side effect reports for patient {patient_id}: {e}",
            exc_info=True,
        )
        metrics["recent_side_effects"] = []

    # Active medication information (approved requests)
    try:
        med_requests = crud.medication_request.get_medication_requests_by_patient(
            db,
            patient_id=patient_id,
            skip=0,
            limit=10,
            sort_by="created_at:desc",
        )
        active_medications = [
            {
                "id": mr.id,
                "medication_name": mr.medication_name,
                "dosage": mr.dosage,
                "frequency": mr.frequency,
                "duration": mr.duration,
                "status": mr.status,
                "approved_at": (
                    mr.resolved_at.isoformat()
                    if getattr(mr, "resolved_at", None)
                    else None
                ),
            }
            for mr in med_requests
            if mr.status == MedicationRequestStatus.APPROVED
        ]
        metrics["active_medications"] = active_medications
    except Exception as e:
        logger.warning(
            f"Failed to retrieve active medications for patient {patient_id}: {e}",
            exc_info=True,
        )
        metrics["active_medications"] = []

    return metrics


def get_patient_context(db: Session, patient_id: str) -> Optional[dict[str, Any]]:
    """
    Retrieve and return a dictionary with basic patient details and health metrics.
    Filters out all None values.
    Accepts patient_id as a string/UUID only (never a Patient object).
    """
    try:
        # Defensive: If a Patient object is accidentally passed, extract its id
        if hasattr(patient_id, "id") and isinstance(
            getattr(patient_id, "id", None), (str, UUID)
        ):
            patient_id = str(patient_id.id)

        patient = crud.patient.get(db, id=patient_id)
        if not patient:
            logger.warning(f"Patient not found: {patient_id}")
            return None

        treatment_start_date = getattr(patient, "treatment_start_date", None)
        if treatment_start_date:
            treatment_start_date = (
                treatment_start_date.isoformat()
                if hasattr(treatment_start_date, "isoformat")
                else str(treatment_start_date)
            )

        context = {
            "id": str(patient.id),
            "first_name": patient.first_name,
            "last_name": patient.last_name,
            "date_of_birth": (
                str(patient.date_of_birth) if patient.date_of_birth else None
            ),
            "height_cm": patient.height_cm,
            "associated_clinic_id": (
                str(patient.associated_clinic_id)
                if patient.associated_clinic_id
                else None
            ),
            "treatment_start_date": treatment_start_date,
            "health_metrics": get_patient_health_metrics(db, str(patient.id)),
        }
        logger.debug(f"Patient context (pre-filter): {context}")
        filtered = _filter_none(context)
        logger.info(f"Patient context (filtered): {filtered}")
        return filtered

    except Exception as e:
        logger.error(
            f"Error in get_patient_context for {patient_id}: {e}", exc_info=True
        )
        return None


def get_clinician_context(
    db: Session, clinician_id: Union[str, UUID, Clinician]
) -> Optional[dict[str, Any]]:
    """
    Retrieve and return a dictionary with basic clinician details.
    Lists all associated clinic IDs as a list of strings.
    Filters out all None values.

    Args:
        db: Database session
        clinician_id: Can be a string ID, UUID, or a Clinician object
    """
    try:
        # Handle the case where a Clinician object is passed directly
        if isinstance(clinician_id, Clinician):
            clinician = clinician_id
            clinician_id = clinician.id  # Extract the ID for clinic lookup
        else:
            # Otherwise, look up the clinician by ID
            clinician = get_clinician_by_id(db, clinician_id=clinician_id)
            if not clinician:
                logger.warning(f"Clinician not found: {clinician_id}")
                return None

        # Get the clinics for this clinician using the extracted ID
        clinics = get_clinics_for_clinician(db, clinician_id=clinician_id)
        clinic_ids: list[str] = []
        if clinics:
            for clinic in clinics:
                if hasattr(clinic, "id") and clinic.id is not None:
                    clinic_ids.append(str(clinic.id))

        context = {
            "id": clinician.id,
            "email": clinician.email,
            "first_name": clinician.first_name,
            "last_name": clinician.last_name,
            "specialty": getattr(clinician, "specialty", None),
            "is_active": clinician.is_active,
            "clerk_id": getattr(clinician, "clerk_id", None),
            "photo_url": getattr(clinician, "photo_url", None),
            "associated_clinic_ids": clinic_ids,
        }
        logger.debug(f"Clinician context (pre-filter): {context}")
        filtered = _filter_none(context)
        logger.info(f"Clinician context (filtered): {filtered}")
        return filtered

    except Exception as e:
        logger.error(
            f"Error in get_clinician_context for {clinician_id}: {e}", exc_info=True
        )
        return None


def get_clinic_context(db: Session, clinic_id: str) -> Optional[dict[str, Any]]:
    """
    Retrieve and return a dictionary with clinic info (excluding scraped data).
    Filters out all None values.
    """
    try:
        clinic = crud.clinic.get(db, id=clinic_id)
        if not clinic:
            logger.warning(f"Clinic not found: {clinic_id}")
            return None

        last_scraped_at = getattr(clinic, "last_scraped_at", None)
        if last_scraped_at:
            last_scraped_at = (
                last_scraped_at.isoformat()
                if hasattr(last_scraped_at, "isoformat")
                else str(last_scraped_at)
            )

        context = {
            "id": str(getattr(clinic, "id", "")),
            "name": getattr(clinic, "name", None),
            "address": getattr(clinic, "address", None),
            "website_url": getattr(clinic, "website_url", None),
            "last_scraped_at": last_scraped_at,
            "knowledge_base_id": getattr(clinic, "knowledge_base_id", None),
            "chatbot_instance_id": getattr(clinic, "chatbot_instance_id", None),
        }
        logger.debug(f"Clinic context (pre-filter): {context}")
        filtered = _filter_none(context)
        logger.info(f"Clinic context (filtered): {filtered}")
        return filtered

    except Exception as e:
        logger.error(f"Error in get_clinic_context for {clinic_id}: {e}", exc_info=True)
        return None


def enhance_rag_results(
    rag_chunks: list[str], query: str, max_chunks: int = 3
) -> list[str]:
    """
    Enhance RAG results by improving formatting and relevance.
    Works with chunks already retrieved by the embedding pipeline.
    - Deduplicate chunks
    - Sort by relevance (simple: length, or future: similarity)
    - Truncate to max_chunks
    - Format for optimal LLM consumption
    """
    try:
        logger.debug(f"Enhancing RAG results for query: {query}")
        if not rag_chunks:
            logger.info("No RAG chunks provided to enhance.")
            return []

        # Deduplicate and strip whitespace
        unique_chunks = list({chunk.strip(): None for chunk in rag_chunks}.keys())

        # Optionally, sort by length descending (proxy for "density" of info)
        sorted_chunks = sorted(unique_chunks, key=lambda x: len(x), reverse=True)

        # Truncate to max_chunks
        selected_chunks = sorted_chunks[:max_chunks]

        # Format: add numbering and ensure clear separation
        formatted_chunks = [
            f"Context {i + 1}:\n{chunk}" for i, chunk in enumerate(selected_chunks)
        ]

        logger.info(f"Enhanced RAG results: {formatted_chunks}")
        return formatted_chunks

    except Exception as e:
        logger.error(f"Error in enhance_rag_results: {e}", exc_info=True)
        return []


def format_conversation_history(
    history: list[dict[str, Any]], max_items: int = 3
) -> str:
    """
    Format the most recent exchanges from a conversation history for LLM context enrichment.

    Each message includes:
      - Sender type (capitalized; falls back to 'Unknown' if missing)
      - Timestamp (formatted as 'Mon DD, HH:MM' if present and parseable)
      - Content (truncated to 100 characters with ellipsis if longer)

    Args:
        history (List[Dict[str, Any]]): List of conversation messages, each a dict with at least 'sender' and 'content'.
        max_items (int): Number of exchanges (user+assistant) to include (default 3, so up to 6 messages).

    Returns:
        str: Formatted conversation history or "No previous conversation." if empty or malformed.
    """
    import datetime

    if not isinstance(history, list) or not history:
        return "No previous conversation."

    # Defensive copy and filter out non-dict entries
    filtered = [msg for msg in history if isinstance(msg, dict)]
    if not filtered:
        return "No previous conversation."

    # Get the most recent max_items*2 messages (assume history is ordered oldest->newest)
    recent = filtered[-(max_items * 2) :]

    def format_timestamp(ts):
        # Accepts string or datetime; returns formatted string or None
        if not ts:
            return None
        try:
            if isinstance(ts, datetime.datetime):
                return ts.strftime("%b %d, %H:%M")
            # Try parsing ISO string
            dt = datetime.datetime.fromisoformat(ts)
            return dt.strftime("%b %d, %H:%M")
        except Exception:
            return None

    lines = []
    for msg in recent:
        sender = str(msg.get("sender", "Unknown")).capitalize()
        content = str(msg.get("content", ""))
        # Truncate content to 100 chars with ellipsis if longer
        if len(content) > 100:
            content = content[:100] + "…"
        ts = msg.get("timestamp") or msg.get("time") or msg.get("created_at")
        formatted_ts = format_timestamp(ts)
        if formatted_ts:
            line = f"{sender} [{formatted_ts}]: {content}"
        else:
            line = f"{sender}: {content}"
        lines.append(line)

    if not lines:
        return "No previous conversation."
    return "\n".join(lines)


def enrich_with_rag(
    db: Session, user_id: str, message: str, user_role: str
) -> dict[str, Any]:
    """
    Enriches the context for LLM with RAG (Retrieval-Augmented Generation) content.
    - Uses embedding model to encode the user message.
    - For patients, uses their associated_clinic_id; for clinicians, uses the first clinic in their list.
    - Retrieves up to 5 most similar content chunks for the clinic from the database.
    - Optionally includes up to 2 related scraped pages (URL, title, summary truncated to 2000 chars).
    - Formats all results concisely for LLM consumption.
    - If RAG enrichment fails, appends a fallback message.
    - Filters out all None values from the returned context dictionary.
    - Adds detailed logging and robust error handling.
    - Implements caching for improved performance.
    """
    import traceback

    from app.core.config import settings
    from app.core.rag_config import RAGConfig
    from app.crud.crud_content_chunk import crud_content_chunk
    from app.crud.crud_scraped_page import get_scraped_pages_by_clinic
    from app.services.embedding_pipeline import generate_embeddings

    context: dict[str, Any] = {}
    fallback_message = "No relevant content was found in our database for your question, but I'm here to help based on my general knowledge."

    try:
        logger.info(
            f"Starting RAG enrichment for user_id={user_id}, user_role={user_role}"
        )

        # 1. Determine clinic_id based on user_role
        clinic_id = None
        if user_role.lower() == "patient":
            patient_ctx = get_patient_context(db, user_id)
            clinic_id = patient_ctx.get("associated_clinic_id") if patient_ctx else None
            logger.debug(f"Patient clinic_id: {clinic_id}")
        elif user_role.lower() == "clinician":
            clinician_ctx = get_clinician_context(db, user_id)
            clinics = (
                clinician_ctx.get("associated_clinic_ids") if clinician_ctx else []
            )
            clinic_id = clinics[0] if clinics else None
            logger.debug(f"Clinician clinic_id: {clinic_id}")
        else:
            logger.warning(f"Unknown user_role: {user_role}")

        if not clinic_id:
            logger.warning("No clinic_id found; skipping RAG enrichment.")
            context["rag_fallback_message"] = fallback_message
            return _filter_none(context)

        # 2. Check cache first
        cached_result = rag_cache.get_rag_result(message, clinic_id, user_role)
        if cached_result:
            logger.info("Using cached RAG result")
            context["rag_context_chunks"] = enhance_rag_results(
                [chunk["chunk_text"] for chunk in cached_result.chunks],
                message,
                max_chunks=3,
            )
            if cached_result.scraped_pages:
                context["rag_scraped_pages"] = cached_result.scraped_pages
            return _filter_none(context)

        # 3. Generate embedding for the user message
        embeddings = generate_embeddings([message])
        if not embeddings or not embeddings[0]:
            logger.error("Failed to generate embedding for message.")
            context["rag_fallback_message"] = fallback_message
            return _filter_none(context)
        query_embedding = embeddings[0]

        # 4. Get dynamic similarity threshold based on query
        rag_config = RAGConfig()
        dynamic_threshold = rag_config.get_dynamic_threshold(
            message, 
            {"user_role": user_role}
        )
        logger.info(f"Using dynamic similarity threshold: {dynamic_threshold} for query: {message[:50]}...")
        
        # 5. Retrieve up to 5 most similar content chunks for the clinic
        chunks_data = []
        chunk_texts = []
        try:
            chunks = crud_content_chunk.find_similar_content_chunks(
                db, 
                query_embedding=query_embedding, 
                limit=5, 
                clinic_id=clinic_id,
                similarity_threshold=dynamic_threshold
            )
            for chunk in chunks:
                if getattr(chunk, "chunk_text", None):
                    chunk_data = {
                        "id": str(getattr(chunk, "id", "")),
                        "chunk_text": chunk.chunk_text,
                        "scraped_page_id": str(getattr(chunk, "scraped_page_id", "")),
                        "metadata_": getattr(chunk, "metadata_", {}),
                        "similarity_score": getattr(chunk, "similarity_score", 0.0),
                        "source_title": getattr(chunk.scraped_page, "title", "Unknown") if hasattr(chunk, "scraped_page") else "Unknown",
                        "page_number": chunk.metadata_.get("page", 1) if chunk.metadata_ else None,
                    }
                    chunks_data.append(chunk_data)
                    chunk_texts.append(chunk.chunk_text)

            # Sort chunks by similarity score (highest first)
            chunks_data.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            chunk_texts_sorted = [chunk['chunk_text'] for chunk in chunks_data]
            
            context["rag_context_chunks"] = enhance_rag_results(
                chunk_texts_sorted, message, max_chunks=5
            )
            # Also pass the full chunk data for metadata access
            context["rag_chunks_metadata"] = chunks_data
            logger.info(f"Retrieved {len(chunk_texts)} RAG content chunks.")
        except Exception as e_chunks:
            logger.error(
                f"Error retrieving similar content chunks: {e_chunks}", exc_info=True
            )
            context["rag_context_chunks"] = []

        # 6. Optionally include up to 2 related scraped pages
        formatted_pages = []
        try:
            scraped_pages = get_scraped_pages_by_clinic(
                db, clinic_id=clinic_id, limit=2
            )
            for page in scraped_pages:
                summary = getattr(page, "cleaned_content", "") or ""
                formatted_pages.append(
                    {
                        "url": getattr(page, "source_url", ""),
                        "title": getattr(page, "title", "") or "",
                        "summary": summary[:2000],
                    }
                )
            if formatted_pages:
                context["rag_scraped_pages"] = formatted_pages
                logger.info(
                    f"Retrieved {len(formatted_pages)} scraped pages for clinic."
                )
        except Exception as e_pages:
            logger.error(f"Error retrieving scraped pages: {e_pages}", exc_info=True)
            context["rag_scraped_pages"] = []

        # 7. Cache the result
        if chunks_data or formatted_pages:
            rag_cache.set_rag_result(
                query=message,
                clinic_id=clinic_id,
                user_role=user_role,
                chunks=chunks_data,
                scraped_pages=formatted_pages,
                query_embedding=query_embedding,
            )
            logger.info("Cached RAG result for future use")

        # 8. Fallback if no RAG content found
        if not context.get("rag_context_chunks") and not context.get(
            "rag_scraped_pages"
        ):
            context["rag_fallback_message"] = fallback_message

        logger.info("RAG enrichment completed successfully.")
        return _filter_none(context)

    except Exception as e:
        logger.error(
            f"Fatal error in enrich_with_rag: {e}\n{traceback.format_exc()}",
            exc_info=True,
        )
        context["rag_fallback_message"] = fallback_message
        return _filter_none(context)


# === Phase 3: Context Optimization and Formatting ===


def format_context_for_prompt(context: dict[str, Any], max_tokens: int = 2000) -> str:
    """Format a context dictionary for LLM prompts.

    The function understands both ``rag_chunks`` and ``rag_context_chunks`` keys
    for retrieval-augmented results and includes them in the formatted output.
    """
    formatted_parts = []

    # Format patient context
    if "patient_context" in context and context["patient_context"]:
        patient = context["patient_context"]
        patient_info = f"Patient: {patient.get('first_name', 'Unknown')}"

        # Add DOB and height if present
        if patient.get("date_of_birth"):
            patient_info += f", DOB: {patient['date_of_birth']}"
        if patient.get("height_cm"):
            patient_info += f", height: {patient['height_cm']}cm"

        # Treatment start date
        if patient.get("treatment_start_date"):
            patient_info += f", treatment started {patient['treatment_start_date']}"

        # Health metrics (nested)
        health_metrics = patient.get("health_metrics", {})
        if health_metrics:
            latest_weight = health_metrics.get("latest_weight")
            if latest_weight:
                date = (
                    latest_weight.get("log_date")
                    or latest_weight.get("date")
                    or "unknown date"
                )
                weight_val = latest_weight.get("weight_kg", "unknown")
                patient_info += f", current weight {weight_val}kg (as of {date})"

            weight_history = health_metrics.get("weight_history")
            if (
                weight_history
                and isinstance(weight_history, list)
                and len(weight_history) > 1
            ):
                # Show previous weight if available
                prev = weight_history[1]
                prev_date = prev.get("log_date") or prev.get("date") or "unknown date"
                prev_weight = prev.get("weight_kg", "unknown")
                patient_info += f", previous weight {prev_weight}kg (as of {prev_date})"

            recent_side_effects = health_metrics.get("recent_side_effects")
            if (
                recent_side_effects
                and isinstance(recent_side_effects, list)
                and recent_side_effects
            ):
                se = recent_side_effects[0]
                desc = se.get("description", "side effect")
                severity = se.get("severity", "unknown")
                patient_info += (
                    f", recently reported side effect: {desc} (severity: {severity})"
                )

            active_meds = health_metrics.get("active_medications")
            if active_meds and isinstance(active_meds, list) and active_meds:
                med_names = [
                    med.get("medication_name", "Unknown") for med in active_meds
                ]
                patient_info += f", active medications: {', '.join(med_names)}"

        formatted_parts.append(patient_info)

    # Add clinician info if present
    if (
        "patient_context" in context
        and context["patient_context"]
        and context["patient_context"].get("invited_by_clinician_id")
        and "clinician_context" in context
        and context["clinician_context"]
    ):
        clinician = context["clinician_context"]
        clinician_info = f"Clinician: {clinician.get('first_name', 'Unknown')}"
        if clinician.get("specialty"):
            clinician_info += f", {clinician['specialty']} specialist"
        formatted_parts.append(clinician_info)

    # Format clinic context
    if "clinic_context" in context and context["clinic_context"]:
        clinic = context["clinic_context"]
        clinic_info = f"Clinic: {clinic.get('name', 'Unknown')}"
        if clinic.get("address"):
            clinic_info += f", located at {clinic['address']}"
        formatted_parts.append(clinic_info)

    # Format RAG context chunks with FULL content and metadata
    rag_chunks = context.get("rag_chunks") or context.get("rag_context_chunks")
    rag_metadata = context.get("rag_chunks_metadata", [])
    
    if rag_chunks:
        rag_info = "=== Relevant Information from Knowledge Base ===\n"
        
        # Use metadata if available for richer context
        if rag_metadata and len(rag_metadata) == len(rag_chunks):
            for i, (chunk, metadata) in enumerate(zip(rag_chunks, rag_metadata), 1):
                rag_info += f"\n--- Source {i} ---\n"
                rag_info += f"Content: {chunk}\n"  # FULL content, not truncated
                
                # Add source information if available
                if metadata.get("source_title"):
                    rag_info += f"Source: {metadata['source_title']}\n"
                if metadata.get("page_number"):
                    rag_info += f"Page: {metadata['page_number']}\n"
                if metadata.get("similarity_score"):
                    rag_info += f"Relevance: {metadata['similarity_score']:.2%}\n"
        else:
            # Fallback to simple formatting without metadata
            for i, chunk in enumerate(rag_chunks, 1):
                rag_info += f"\n--- Information Chunk {i} ---\n"
                rag_info += f"{chunk}\n"  # FULL content
        
        formatted_parts.append(rag_info)

    formatted_context = "\n\n".join(formatted_parts)

    # Log a sample of the formatted context for debugging
    logger.info(f"Formatted LLM context sample: {formatted_context[:300]}")

    # Truncate if necessary to fit within token limit (approximate)
    if len(formatted_context) > max_tokens * 4:  # Rough estimate: 4 chars per token
        formatted_context = (
            formatted_context[: max_tokens * 4]
            + "...\n[Additional context truncated due to length]"
        )

    return formatted_context


def validate_patient_context(patient_context):
    """
    NEW UTILITY FUNCTION: Validate the patient context structure and log any issues.
    This function does not modify any data, it only performs diagnostic logging.
    """
    if not patient_context:
        logger.warning("Patient context is empty or None.")
        return

    logger.info(f"Patient context top-level keys: {list(patient_context.keys())}")

    if "health_metrics" not in patient_context:
        logger.warning("Missing 'health_metrics' in patient context.")
        return

    health_metrics = patient_context["health_metrics"]
    logger.info(f"Health metrics keys: {list(health_metrics.keys())}")

    expected_keys = [
        "latest_weight",
        "weight_history",
        "recent_side_effects",
        "active_medications",
    ]
    for key in expected_keys:
        if key not in health_metrics:
            logger.warning(f"Missing expected health metric: {key}")
        else:
            logger.info(f"Found {key} with value: {health_metrics[key]}")

    # Check weight history structure
    if "weight_history" in health_metrics and health_metrics["weight_history"]:
        sample_weight = health_metrics["weight_history"][0]
        logger.info(f"Weight history entry structure: {list(sample_weight.keys())}")

        # Verify expected keys in weight entry
        if "log_date" not in sample_weight or "weight_kg" not in sample_weight:
            logger.warning(
                "Weight history entry missing required keys log_date or weight_kg"
            )
