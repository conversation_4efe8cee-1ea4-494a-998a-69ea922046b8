"""Parameter persistence utilities for maintaining context across chat messages."""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Optional

logger = logging.getLogger(__name__)


class ParameterPersistenceManager:
    """Manages parameter persistence across chat messages for intent completion."""
    
    def __init__(self, ttl_minutes: int = 10):
        """Initialize parameter persistence manager.
        
        Args:
            ttl_minutes: Time-to-live for persisted parameters in minutes
        """
        self.ttl_minutes = ttl_minutes
        self._storage = {}  # In-memory storage, could be replaced with Redis
    
    def _get_key(self, user_id: str, action_type: str) -> str:
        """Generate storage key for user and action type."""
        return f"{user_id}:{action_type}"
    
    def save_partial_parameters(
        self, 
        user_id: str, 
        action_type: str, 
        parameters: dict[str, Any],
        missing_params: list[str]
    ) -> None:
        """Save partial parameters for later completion.
        
        Args:
            user_id: User identifier
            action_type: The action type being processed
            parameters: Currently collected parameters
            missing_params: List of missing parameter names
        """
        key = self._get_key(user_id, action_type)
        
        self._storage[key] = {
            "action_type": action_type,
            "parameters": parameters.copy(),
            "missing_params": missing_params.copy(),
            "timestamp": datetime.utcnow(),
            "attempts": 1
        }
        
        logger.info(
            f"Saved partial parameters for user={user_id}, action={action_type}, "
            f"collected={list(parameters.keys())}, missing={missing_params}"
        )
    
    def get_saved_parameters(
        self, 
        user_id: str, 
        action_type: Optional[str] = None
    ) -> Optional[dict[str, Any]]:
        """Retrieve saved parameters for a user and action.
        
        Args:
            user_id: User identifier
            action_type: Optional action type filter
            
        Returns:
            Saved parameter state or None if not found/expired
        """
        # If action_type specified, look for exact match
        if action_type:
            key = self._get_key(user_id, action_type)
            state = self._storage.get(key)
            
            if state and self._is_valid(state):
                return state
            elif state:
                # Expired, clean up
                del self._storage[key]
            return None
        
        # Otherwise, find most recent valid state for user
        user_prefix = f"{user_id}:"
        valid_states = []
        
        for key, state in list(self._storage.items()):
            if key.startswith(user_prefix):
                if self._is_valid(state):
                    valid_states.append(state)
                else:
                    # Clean up expired
                    del self._storage[key]
        
        if valid_states:
            # Return most recent
            return max(valid_states, key=lambda s: s["timestamp"])
        
        return None
    
    def update_parameters(
        self,
        user_id: str,
        action_type: str,
        new_parameters: dict[str, Any]
    ) -> Optional[dict[str, Any]]:
        """Update saved parameters with new values.
        
        Args:
            user_id: User identifier
            action_type: The action type
            new_parameters: New parameter values to merge
            
        Returns:
            Updated parameter state or None if not found
        """
        key = self._get_key(user_id, action_type)
        state = self._storage.get(key)
        
        if not state or not self._is_valid(state):
            return None
        
        # Merge parameters
        state["parameters"].update(new_parameters)
        state["timestamp"] = datetime.utcnow()
        state["attempts"] += 1
        
        # Update missing params list
        state["missing_params"] = [
            param for param in state["missing_params"]
            if param not in new_parameters
        ]
        
        logger.info(
            f"Updated parameters for user={user_id}, action={action_type}, "
            f"new_params={list(new_parameters.keys())}, "
            f"remaining_missing={state['missing_params']}"
        )
        
        return state
    
    def clear_parameters(self, user_id: str, action_type: str) -> None:
        """Clear saved parameters after successful completion.
        
        Args:
            user_id: User identifier
            action_type: The action type
        """
        key = self._get_key(user_id, action_type)
        if key in self._storage:
            del self._storage[key]
            logger.info(f"Cleared parameters for user={user_id}, action={action_type}")
    
    def save_compound_parameters(
        self,
        user_id: str,
        chain_id: str,
        action_states: list[dict[str, Any]],
        raw_llm_response: Optional[dict[str, Any]] = None
    ) -> None:
        """Save compound action parameter states.
        
        Args:
            user_id: User identifier
            chain_id: Chain identifier
            action_states: List of action states with missing parameters
            raw_llm_response: Optional raw LLM response for reconstruction
        """
        key = f"{user_id}:compound:{chain_id}"
        
        self._storage[key] = {
            "chain_id": chain_id,
            "action_states": action_states,
            "raw_llm_response": raw_llm_response,
            "timestamp": datetime.utcnow(),
            "attempts": 1
        }
        
        logger.info(
            f"Saved compound parameters for user={user_id}, chain={chain_id}, "
            f"actions={len(action_states)}"
        )
    
    def get_compound_parameters(
        self,
        user_id: str,
        chain_id: str
    ) -> Optional[dict[str, Any]]:
        """Get saved compound action parameters.
        
        Args:
            user_id: User identifier
            chain_id: Chain identifier
            
        Returns:
            Saved compound state or None
        """
        key = f"{user_id}:compound:{chain_id}"
        state = self._storage.get(key)
        
        if state and self._is_valid(state):
            return state
        elif state:
            # Expired, clean up
            del self._storage[key]
        return None
    
    def clear_compound_parameters(self, user_id: str, chain_id: str) -> None:
        """Clear saved compound parameters after successful completion.
        
        Args:
            user_id: User identifier
            chain_id: Chain identifier
        """
        key = f"{user_id}:compound:{chain_id}"
        if key in self._storage:
            del self._storage[key]
            logger.info(f"Cleared compound parameters for user={user_id}, chain={chain_id}")
    
    def _is_valid(self, state: dict[str, Any]) -> bool:
        """Check if a parameter state is still valid (not expired)."""
        age = datetime.utcnow() - state["timestamp"]
        return age < timedelta(minutes=self.ttl_minutes)
    
    def cleanup_expired(self) -> int:
        """Remove expired parameter states.
        
        Returns:
            Number of expired states removed
        """
        expired_keys = []
        
        for key, state in self._storage.items():
            if not self._is_valid(state):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._storage[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired parameter states")
        
        return len(expired_keys)


# Global instance for easy access
_parameter_manager = None


def get_parameter_manager() -> ParameterPersistenceManager:
    """Get the global parameter persistence manager instance."""
    global _parameter_manager
    if _parameter_manager is None:
        _parameter_manager = ParameterPersistenceManager()
    return _parameter_manager