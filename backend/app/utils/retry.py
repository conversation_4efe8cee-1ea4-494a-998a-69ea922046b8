"""Retry utilities with exponential backoff for resilient service operations."""

import asyncio
import functools
import logging
import random
import time
from typing import Any, Callable, Optional, Type, TypeVar, Union

logger = logging.getLogger(__name__)

T = TypeVar("T")


class RetryError(Exception):
    """Base exception for retry-related errors."""
    
    def __init__(self, message: str, last_error: Optional[Exception] = None, attempts: int = 0):
        super().__init__(message)
        self.last_error = last_error
        self.attempts = attempts


def calculate_backoff(
    attempt: int,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    jitter: bool = True
) -> float:
    """Calculate exponential backoff delay with optional jitter.
    
    Args:
        attempt: Current attempt number (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        jitter: Whether to add random jitter
        
    Returns:
        Delay in seconds
    """
    delay = min(base_delay * (2 ** attempt), max_delay)
    
    if jitter:
        # Add random jitter up to 25% of the delay
        delay += random.uniform(0, delay * 0.25)
    
    return delay


def retry_async(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple[Type[Exception], ...] = (Exception,),
    on_retry: Optional[Callable[[Exception, int], None]] = None,
    jitter: bool = True
):
    """Async decorator for retrying functions with exponential backoff.
    
    Args:
        max_attempts: Maximum number of attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        exceptions: Tuple of exceptions to catch and retry
        on_retry: Optional callback called on each retry with (exception, attempt)
        jitter: Whether to add random jitter to delays
        
    Example:
        @retry_async(max_attempts=3, exceptions=(httpx.HTTPError, LLMException))
        async def make_api_call():
            ...
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        logger.error(
                            f"Max retries ({max_attempts}) exceeded for {func.__name__}. "
                            f"Error: {str(e)}, Error type: {type(e).__name__}, "
                            f"Attempts: {max_attempts}"
                        )
                        raise RetryError(
                            f"Max retries ({max_attempts}) exceeded for {func.__name__}",
                            last_error=e,
                            attempts=max_attempts
                        )
                    
                    # Calculate backoff delay
                    delay = calculate_backoff(attempt, base_delay, max_delay, jitter)
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_attempts} for {func.__name__}. "
                        f"Error: {str(e)}, Error type: {type(e).__name__}, "
                        f"Next attempt in: {round(delay, 2)}s"
                    )
                    
                    # Call retry callback if provided
                    if on_retry:
                        on_retry(e, attempt + 1)
                    
                    # Wait before next attempt
                    await asyncio.sleep(delay)
            
            # Should never reach here
            raise RetryError(
                f"Retry logic error in {func.__name__}",
                last_error=last_exception,
                attempts=max_attempts
            )
        
        return wrapper
    return decorator


def retry_sync(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple[Type[Exception], ...] = (Exception,),
    on_retry: Optional[Callable[[Exception, int], None]] = None,
    jitter: bool = True
):
    """Sync decorator for retrying functions with exponential backoff.
    
    Same parameters as retry_async but for synchronous functions.
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        logger.error(
                            f"Max retries ({max_attempts}) exceeded for {func.__name__}. "
                            f"Error: {str(e)}, Error type: {type(e).__name__}, "
                            f"Attempts: {max_attempts}"
                        )
                        raise RetryError(
                            f"Max retries ({max_attempts}) exceeded for {func.__name__}",
                            last_error=e,
                            attempts=max_attempts
                        )
                    
                    # Calculate backoff delay
                    delay = calculate_backoff(attempt, base_delay, max_delay, jitter)
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_attempts} for {func.__name__}. "
                        f"Error: {str(e)}, Error type: {type(e).__name__}, "
                        f"Next attempt in: {round(delay, 2)}s"
                    )
                    
                    # Call retry callback if provided
                    if on_retry:
                        on_retry(e, attempt + 1)
                    
                    # Wait before next attempt
                    time.sleep(delay)
            
            # Should never reach here
            raise RetryError(
                f"Retry logic error in {func.__name__}",
                last_error=last_exception,
                attempts=max_attempts
            )
        
        return wrapper
    return decorator


class RetryContext:
    """Context manager for manual retry logic with state tracking."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exceptions: tuple[Type[Exception], ...] = (Exception,),
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exceptions = exceptions
        self.jitter = jitter
        self.attempt = 0
        self.last_exception: Optional[Exception] = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type and issubclass(exc_type, self.exceptions):
            self.last_exception = exc_val
            self.attempt += 1
            
            if self.attempt >= self.max_attempts:
                # Don't suppress the exception
                return False
            
            # Calculate delay
            delay = calculate_backoff(
                self.attempt - 1,
                self.base_delay,
                self.max_delay,
                self.jitter
            )
            
            logger.warning(
                f"Retry attempt {self.attempt}/{self.max_attempts}. "
                f"Error: {str(exc_val)}, Error type: {exc_type.__name__}, "
                f"Next attempt in: {round(delay, 2)}s"
            )
            
            # Wait before allowing retry
            await asyncio.sleep(delay)
            
            # Suppress the exception to allow retry
            return True
        
        # Don't suppress other exceptions
        return False
    
    def should_retry(self) -> bool:
        """Check if we should retry based on current state."""
        return self.attempt < self.max_attempts