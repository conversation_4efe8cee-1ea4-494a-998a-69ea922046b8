import logging

logger = logging.getLogger(__name__)


def validate_sort_params(
    sort_by: str,
    allowed_fields: set[str],
    default_field: str = "created_at",
    default_order: str = "desc",
) -> tuple[str, str]:
    """
    Validates and parses sorting parameters.

    Args:
        sort_by: String in format "field:order" (e.g., "created_at:desc")
        allowed_fields: Set of allowed field names
        default_field: Default field to sort by if invalid
        default_order: Default sort order if invalid

    Returns:
        Tuple of (field, order)

    Raises:
        ValueError: If sort_by format is invalid
    """
    try:
        field, order = sort_by.split(":")
        if field not in allowed_fields:
            logger.warning(
                f"Invalid sort field '{field}'. Allowed fields: {allowed_fields}. Using default field '{default_field}'"
            )
            field = default_field
        if order.lower() not in ["asc", "desc"]:
            logger.warning(
                f"Invalid sort order '{order}'. Must be 'asc' or 'desc'. Using default order '{default_order}'"
            )
            order = default_order
        return field, order.lower()
    except (ValueError, AttributeError):
        logger.warning(
            f"Invalid sort_by format '{sort_by}'. Expected 'field:order'. Using defaults: field='{default_field}', order='{default_order}'"
        )
        return default_field, default_order
