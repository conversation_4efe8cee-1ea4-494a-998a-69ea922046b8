#!/usr/bin/env python3
"""
Simple script to check clinic association data in the database.
"""
import os
import sys

# Add the app directory to Python path
sys.path.append('/app')

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Use the database URL from the environment
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************/pulsetrack")

def check_clinic_data():
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check total patients
        result = db.execute(text("SELECT COUNT(*) FROM patients"))
        total_patients = result.scalar()
        print(f"Total patients: {total_patients}")
        
        # Check patients with clinic associations
        result = db.execute(text("SELECT COUNT(*) FROM patients WHERE associated_clinic_id IS NOT NULL"))
        patients_with_clinic = result.scalar()
        print(f"Patients with clinic associations: {patients_with_clinic}")
        
        # Check total clinics
        result = db.execute(text("SELECT COUNT(*) FROM clinics"))
        total_clinics = result.scalar()
        print(f"Total clinics: {total_clinics}")
        
        # Get clinic details
        result = db.execute(text("SELECT id, name FROM clinics"))
        clinics = result.fetchall()
        
        for clinic_id, clinic_name in clinics:
            # Count patients for this clinic
            result = db.execute(text("SELECT COUNT(*) FROM patients WHERE associated_clinic_id = :clinic_id"), 
                              {"clinic_id": clinic_id})
            patient_count = result.scalar()
            
            # Count scraped pages for this clinic
            result = db.execute(text("SELECT COUNT(*) FROM scraped_pages WHERE clinic_id = :clinic_id"), 
                              {"clinic_id": clinic_id})
            scraped_count = result.scalar()
            
            # Count content chunks for this clinic
            result = db.execute(text("""
                SELECT COUNT(*) FROM content_chunks cc 
                JOIN scraped_pages sp ON cc.scraped_page_id = sp.id 
                WHERE sp.clinic_id = :clinic_id
            """), {"clinic_id": clinic_id})
            chunk_count = result.scalar()
            
            print(f"Clinic '{clinic_name}' (ID: {clinic_id}):")
            print(f"  - {patient_count} patients")
            print(f"  - {scraped_count} scraped pages")
            print(f"  - {chunk_count} content chunks")
        
        # Get some sample patient IDs for testing
        result = db.execute(text("SELECT id, first_name, associated_clinic_id FROM patients LIMIT 5"))
        patients = result.fetchall()
        
        print("\nSample patients:")
        for patient_id, first_name, clinic_id in patients:
            print(f"  - {patient_id} ({first_name}): clinic_id = {clinic_id}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_clinic_data()