#!/usr/bin/env python3
"""
Debug the content chunks and similarity search.
"""
import os
import sys
import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to Python path
sys.path.append('/app')

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************/pulsetrack")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def check_content_chunks():
    """Check what content chunks exist and their structure."""
    db = SessionLocal()
    
    try:
        # Get clinic ID
        result = db.execute(text("SELECT id FROM clinics LIMIT 1"))
        clinic_id = result.scalar()
        print(f"Clinic ID: {clinic_id}")
        
        # Check content chunks directly
        result = db.execute(text("""
            SELECT cc.id, cc.chunk_text, cc.embedding IS NOT NULL as has_embedding,
                   sp.source_url
            FROM content_chunks cc
            JOIN scraped_pages sp ON cc.scraped_page_id = sp.id  
            WHERE sp.clinic_id = :clinic_id
            LIMIT 10
        """), {"clinic_id": clinic_id})
        
        chunks = result.fetchall()
        print(f"\nFound {len(chunks)} content chunks:")
        
        for i, (chunk_id, chunk_text, has_embedding, url) in enumerate(chunks, 1):
            print(f"\nChunk {i} (ID: {chunk_id}):")
            print(f"  From: {url}")
            print(f"  Has embedding: {has_embedding}")
            print(f"  Text preview: {chunk_text[:200]}...")
            
        # Test embedding generation
        print("\n" + "="*60)
        print("Testing embedding generation for sample query...")
        
        from app.services.embedding_pipeline import generate_embeddings
        test_query = "weight loss breakfast recommendations"
        
        try:
            embeddings = generate_embeddings([test_query])
            if embeddings and embeddings[0]:
                print(f"✅ Generated embedding for test query (dimension: {len(embeddings[0])})")
                
                # Test similarity search manually
                from app.crud.crud_content_chunk import crud_content_chunk
                similar_chunks = crud_content_chunk.find_similar_content_chunks(
                    db, query_embedding=embeddings[0], limit=5, clinic_id=clinic_id
                )
                
                print(f"✅ Found {len(similar_chunks)} similar chunks via CRUD")
                
                for i, chunk in enumerate(similar_chunks, 1):
                    print(f"  Chunk {i}: {chunk.chunk_text[:100]}...")
                    
            else:
                print("❌ Failed to generate embedding")
                
        except Exception as e:
            print(f"❌ Error testing embeddings: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_content_chunks()