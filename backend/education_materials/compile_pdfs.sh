#!/bin/bash

# Compile all LaTeX files to PDFs
echo "Compiling education materials to PDFs..."

# Check if pdflatex is installed
if ! command -v pdflatex &> /dev/null; then
    echo "Error: pdflatex is not installed. Please install a LaTeX distribution."
    exit 1
fi

# Compile each LaTeX file
for texfile in *.tex; do
    if [ -f "$texfile" ]; then
        echo "Compiling $texfile..."
        # Run pdflatex twice to resolve references
        pdflatex -interaction=nonstopmode "$texfile" > /dev/null 2>&1
        pdflatex -interaction=nonstopmode "$texfile" > /dev/null 2>&1
        
        # Check if PDF was created
        pdffile="${texfile%.tex}.pdf"
        if [ -f "$pdffile" ]; then
            echo "✓ Created $pdffile"
        else
            echo "✗ Failed to create $pdffile"
        fi
    fi
done

# Clean up auxiliary files
echo "Cleaning up auxiliary files..."
rm -f *.aux *.log *.out *.toc

echo "Done! PDFs are ready for upload."

# List generated PDFs
echo -e "\nGenerated PDFs:"
ls -la *.pdf 2>/dev/null || echo "No PDFs found"