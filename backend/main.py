import logging
import time
import uuid
from contextlib import asynccontextmanager

import redis.asyncio as redis
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pythonjsonlogger import jsonlogger
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware

# --- Core Imports --- # Grouping core/config imports
from app.api.v1.api import api_router as api_v1_router
from app.core.config import settings
from app.core.limiter import limiter  # Import the shared limiter instance
from app.db.session import check_db_connection  # Import DB check function

# --- Redis Client ---
# Define redis_client globally but initialize in lifespan
redis_client: redis.Redis | None = None

# Load environment variables from .env file (redundant if config.py does it, but safe)
load_dotenv()

# --- Structured Logging Configuration ---
# Configure the root logger
log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
# Get the root logger
root_logger = logging.getLogger()
root_logger.setLevel(log_level)

# Use JSON Formatter
logHandler = logging.StreamHandler()
# Add request_id to the format string - it will be added via 'extra' in middleware
formatter = jsonlogger.JsonFormatter(
    fmt="%(asctime)s %(levelname)s %(name)s %(message)s %(pathname)s %(lineno)d %(funcName)s %(request_id)s",
    datefmt="%Y-%m-%dT%H:%M:%S%z",
)
logHandler.setFormatter(formatter)

# Remove existing handlers from root logger and add the JSON handler
# This ensures our JSON format is used, overriding default handlers from Gunicorn/Uvicorn if any
root_logger.handlers.clear()
root_logger.addHandler(logHandler)

# Get our application-specific logger instance AFTER root configuration
# It will inherit the level and handlers from the root logger
logger = logging.getLogger("pulsetrack_api")
# No need to set level or add handlers here if inheriting from root

# --- Application Lifecycle ---


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles application startup and shutdown events."""
    global redis_client  # Allow modification of the global variable
    logger.info("Application startup...")
    # --- Add startup logic here ---

    # --- Redis Connection ---
    try:
        if settings.REDIS_URL:
            logger.info(f"Connecting to Redis at {settings.REDIS_URL}...")
            redis_client = redis.from_url(
                settings.REDIS_URL, encoding="utf-8", decode_responses=True
            )
            await redis_client.ping()  # Verify connection
            app.state.redis_client = redis_client  # Store client in app state
            logger.info("Successfully connected to Redis.")
        else:
            logger.warning("REDIS_URL not set. Redis client not initialized.")
            app.state.redis_client = None
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}", exc_info=True)
        app.state.redis_client = None  # Ensure state reflects failure
    # Check DB URL (logging only, connection checked in health endpoint)
    if settings.DATABASE_URL:
        logger.info("DATABASE_URL configured.")
    else:
        logger.warning("DATABASE_URL not set.")

    # Initialize LLM Action Templates
    try:
        from app.db.session import SessionLocal
        from app.initial_data import (
            create_clinician_templates,
            create_patient_templates,
        )

        logger.info("Initializing LLM Action Templates...")
        db = SessionLocal()
        try:
            create_clinician_templates(db)
            create_patient_templates(db)
            logger.info("LLM Action Templates initialized successfully.")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Failed to initialize LLM Action Templates: {e}", exc_info=True)

    # Add limiter to app state
    app.state.limiter = limiter

    yield  # Application runs here

    # --- Add shutdown logic here ---
    # --- Redis Disconnection ---
    if redis_client:
        logger.info("Closing Redis connection...")
        await redis_client.close()
        logger.info("Redis connection closed.")

    logger.info("Application shutdown...")


# --- FastAPI Application ---

# Define tags metadata for OpenAPI documentation
openapi_tags = [
    {
        "name": "System",
        "description": "System-level operations like health checks.",
    },
    # ... (other tags remain the same) ...
    {
        "name": "Authentication",
        "description": "Operations related to user authentication (patient and clinician).",
    },
    {
        "name": "Patients",
        "description": "Operations performed by or related to patients (profile, chat).",
    },
    {
        "name": "Clinicians",
        "description": "Operations performed by or related to clinicians (patient management, access codes).",
    },
    {
        "name": "Weight Tracking",
        "description": "Operations related to logging and retrieving patient weight data.",
    },
    {
        "name": "Medication",
        "description": "Operations related to medication requests.",
    },
    {
        "name": "Side Effects",
        "description": "Operations related to reporting and viewing side effects.",
    },
    {
        "name": "Content",
        "description": "Operations related to retrieving educational content.",
    },
]


app = FastAPI(
    title="Codename Pulsetrack API",
    version="0.1.0",
    description="API for patient and clinician interactions in the Pulsetrack platform.",
    openapi_tags=openapi_tags,
    lifespan=lifespan,
    response_model_by_alias=True,
)

# --- CORS Middleware ---
# IMPORTANT: Add this BEFORE any routers or other middleware that sends responses.
# Define allowed origins based on local development setup
origins = [
    "http://localhost:5173",  # Default Dev Clinician
    "http://localhost:5174",  # Default Dev Patient
    "http://127.0.0.1:5173",  # Alternative localhost
    "http://127.0.0.1:5174",  # Alternative localhost
    "https://*.clerk.accounts.dev",  # Clerk authentication domain
    "https://*.clerk.com",  # Clerk main domain
    "http://localhost:5175",  # Default Dev Admin
    "http://127.0.0.1:5175",  # Alternative localhost Admin
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# --- Rate Limiting Setup ---
# app.state.limiter = limiter # Already set in lifespan, keeping here for clarity before middleware
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# --- Middleware (Structured Request Logging) ---


@app.middleware("http")
async def structured_logging_middleware(request: Request, call_next):
    """Logs request/response details in structured JSON format."""
    start_time = time.time()
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    response = None  # Initialize response to None
    process_time = 0  # Initialize process_time
    status_code = 500  # Default to 500 for unexpected errors

    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        status_code = response.status_code
        logger.info(
            "Request finished",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "status_code": status_code,
                "process_time_ms": round(process_time * 1000, 2),
            },
        )
        return response

    except Exception as e:
        process_time = time.time() - start_time  # Calculate time even on error
        log_level = logging.ERROR
        log_message = f"Exception during request: {type(e).__name__}"

        # Determine status code if it's an HTTPException
        if isinstance(e, HTTPException):
            status_code = e.status_code
            # Use warning for client errors (4xx), error for server errors (5xx)
            if 400 <= status_code < 500:
                log_level = logging.WARNING
                log_message = f"Client error: {status_code}"
            else:
                log_message = f"Server error: {status_code}"

        # Log the exception details regardless of type
        logger.log(
            log_level,
            log_message,
            exc_info=True,  # Always include traceback for exceptions
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "status_code": status_code,
                "process_time_ms": round(process_time * 1000, 2),
                "exception_detail": str(e),  # Add specific exception detail
            },
        )
        # Re-raise the original exception to let FastAPI handle the response
        raise e


# --- Rate Limiting Middleware ---
# IMPORTANT: Add this *after* the logging middleware if you want logs for 429s,
# or *before* if you don't want rate-limited requests logged by the structured logger.
# Adding it after seems more informative.
# Also ensure it comes *after* CORS middleware to handle preflight requests correctly.
app.add_middleware(SlowAPIMiddleware)

# --- API Routers ---
# Include the v1 API router
app.include_router(api_v1_router, prefix="/api/v1")

# --- Root/Health Endpoints ---


@app.get("/health", tags=["System"], status_code=200)
async def health_check():
    """
    Checks if the application is running and can connect to the database.
    Returns 200 OK if healthy, 503 Service Unavailable otherwise.
    """
    logger.debug("Health check endpoint called.")  # Use debug for frequent checks
    db_ok = check_db_connection()
    if db_ok:
        logger.debug("Health check successful.")
        return {"status": "ok", "database": "connected"}
    else:
        logger.error("Health check failed: Database connection error.")
        # Raise HTTPException to ensure correct status code and response body
        raise HTTPException(
            status_code=503, detail={"status": "error", "database": "disconnected"}
        )


# --- Main Execution (for running with uvicorn directly) ---

if __name__ == "__main__":
    import uvicorn

    logger.info("Starting Uvicorn server directly...")
    # Note: For production, use a process manager like Gunicorn with Uvicorn workers
    # Uvicorn will use the root logger configuration we set up
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload to prevent restart loops
        log_config=None,  # Prevent uvicorn from overriding our root logger setup
        # log_level=settings.LOG_LEVEL.lower() # No longer needed if root logger is set
    )
