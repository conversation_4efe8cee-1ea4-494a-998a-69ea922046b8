[project]
name = "pulsetrack-backend"
version = "0.1.0"
dependencies = [
    # Add other project dependencies here if managed via pyproject.toml
    # "flake8", # Flake8 is used for linting, might not be a runtime dep
    "pypdf2 (>=3.0.1,<4.0.0)"
]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "pulsetrack-backend"
version = "0.1.0"
description = "PulseTrack backend application"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11.9"
fastapi = "0.110.0"
uvicorn = {extras = ["standard"], version = "0.29.0"}
pydantic-settings = "2.2.1"
python-dotenv = "1.0.1"
sqlalchemy = "2.0.29"
sqlalchemy-utils = "*"
psycopg2-binary = "2.9.9"
alembic = "1.13.1"
python-jose = {extras = ["cryptography"], version = "3.3.0"}
PyJWT = "^2.10.1"
passlib = {extras = ["bcrypt"], version = "1.7.4"}
email-validator = "2.1.1"
slowapi = "0.1.9"
httpx = "^0.28.1"
gunicorn = "22.0.0"
python-json-logger = "2.0.7"
clerk-backend-api = "2.0.1"
beautifulsoup4 = "4.12.3"
pgvector = "0.2.0"
python-multipart = "0.0.9"
azure-storage-blob = "12.19.1"
redis = "5.0.4"
openai = "^1.0.0"
rich = "^13.7.0"

[tool.poetry.group.dev.dependencies]
pytest = "8.3.5"
pytest-mock = "3.14.0"
ruff = "^0.11.6"
commitizen = "^3.16.0"

[tool.ruff]
line-length = 140

[tool.ruff.lint]
select = [
    "F",   # pyflakes
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "I",   # isort
    "UP",  # pyupgrade
]
ignore = ["E501"]
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    "build",
    "dist"
]

[tool.ruff.lint.isort]
known-first-party = ["app"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.commitizen]
name = "cz_conventional_commits"
version = "0.1.0"
tag_format = "v$version"
bump_message = "bump: version $current_version u2192 $new_version"
version_files = [
    "pyproject.toml:version"
]
update_changelog_on_bump = true
commit_parser = "^(?P<type>build|ci|docs|feat|fix|perf|refactor|style|test|chore|revert|bump)(?:\\((?P<scope>[^()\\r\\n]*)\\)|\\()?(?P<breaking>!)?:\\s(?P<subject>[^\\r\\n]*)"
