fastapi==0.110.0
uvicorn[standard]==0.29.0
pydantic-settings==2.2.1
python-dotenv==1.0.1
sqlalchemy==2.0.29

sqlalchemy-utils
psycopg2-binary==2.9.9
alembic==1.13.1

python-jose[cryptography]==3.3.0
PyJWT>=2.10.1  # Updated to be compatible with clerk-backend-api
passlib[bcrypt]==1.7.4
email-validator==2.1.1
slowapi==0.1.9

httpx>=0.28.1 # Updated to satisfy clerk-backend-api requirement

gunicorn==22.0.0 # Process manager for Uvicorn workers in production
python-json-logger==2.0.7 # For structured JSON logging

pytest==8.3.5 # Already likely installed, but good practice
pytest-mock==3.14.0
python-multipart==0.0.9
azure-storage-blob==12.19.1 # Added for Azure Blob Storage
clerk-backend-api==2.0.1  # Clerk Python SDK for authentication

beautifulsoup4==4.12.3
pgvector==0.2.0 # For vector similarity search in PostgreSQL
numpy~=1.26.4 # Pin NumPy to < 2.0 to resolve compatibility issues
sentence-transformers==2.7.0 # For generating text embeddings
torch==2.2.2 # Dependency for sentence-transformers
transformers==4.41.2 # Pin transformers to potentially compatible version
redis==5.0.4 # Added for task status storage
openai>=1.0.0
rich==13.7.0 # For enhanced terminal output in tools and scripts
pytz>=2024.1
python-dateutil>=2.9.0
typer
pypdf2==3.0.1 # For extracting text from PDF education materials