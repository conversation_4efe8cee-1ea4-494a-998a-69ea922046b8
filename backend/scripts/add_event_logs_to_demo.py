#!/usr/bin/env python
"""
Add event logs to demo data after seeding.
This script creates event logs for existing demo data.
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.session import SessionLocal

# Import the populate_event_logs functions
from populate_event_logs import (
    populate_chat_message_events,
    populate_weight_log_events,
    populate_side_effect_events,
    populate_appointment_events,
    populate_medication_request_events,
)


async def main():
    """Main function to populate event logs for demo data."""
    db = SessionLocal()
    try:
        print("Adding event logs to demo data...")
        
        # Clear existing event logs to avoid duplicates
        result = db.execute(text("DELETE FROM event_logs"))
        db.commit()
        print(f"Cleared {result.rowcount} existing event logs")
        
        # Populate event logs for different types of activities
        chat_count = await populate_chat_message_events(db)
        print(f"Created {chat_count} event logs for chat messages")
        
        weight_count = await populate_weight_log_events(db)
        print(f"Created {weight_count} event logs for weight logs")
        
        side_effect_count = await populate_side_effect_events(db)
        print(f"Created {side_effect_count} event logs for side effect reports")
        
        appointment_count = await populate_appointment_events(db)
        print(f"Created {appointment_count} event logs for appointments")
        
        medication_count = await populate_medication_request_events(db)
        print(f"Created {medication_count} event logs for medication requests")
        
        total = chat_count + weight_count + side_effect_count + appointment_count + medication_count
        print(f"\nTotal event logs created: {total}")
        
        # Show current event log count
        result = db.execute(text("SELECT COUNT(*) FROM event_logs"))
        total_logs = result.scalar()
        print(f"Total event logs in database: {total_logs}")
        
        print("\nDemo data now includes event logs for Patient Activity feeds!")
        
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())