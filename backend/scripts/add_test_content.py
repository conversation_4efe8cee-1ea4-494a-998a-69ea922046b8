from app import crud
from app.db.session import SessionLocal
from app.schemas.content_chunk import ContentChunkCreate
from app.schemas.scraped_page import ScrapedPageCreate


def add_test_content():
    db = SessionLocal()
    clinic_id = "your_clinic_id"  # Replace with actual clinic ID

    # Create scraped page
    page = crud.scraped_page.create(
        db=db,
        obj_in=ScrapedPageCreate(
            clinic_id=clinic_id,
            source_url="https://example.com/weight-management",
            cleaned_content="Weight management is the process of...",
            metadata={"source_type": "website", "category": "weight_management"},
        ),
    )

    # Create content chunks
    chunks = [
        "Weight management is essential for...",
        "Regular exercise helps with weight management by...",
        "A balanced diet is key to successful weight management...",
        # Add more relevant chunks
    ]

    for chunk in chunks:
        crud.content_chunk.create(
            db=db, obj_in=ContentChunkCreate(scraped_page_id=page.id, chunk_text=chunk)
        )

    db.close()


if __name__ == "__main__":
    add_test_content()
