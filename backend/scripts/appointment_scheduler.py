#!/usr/bin/env python3
"""
Realistic appointment scheduler for demo data.
Creates a proper clinician calendar with no conflicts.
"""

from datetime import datetime, timedelta, time
from typing import List, Tuple, Optional
import random


class AppointmentScheduler:
    """Manages realistic appointment scheduling for a clinician."""
    
    # Clinic hours
    CLINIC_START = time(9, 0)  # 9:00 AM
    CLINIC_END = time(17, 0)   # 5:00 PM
    
    # Appointment durations
    INITIAL_CONSULTATION_MINUTES = 45
    FOLLOW_UP_MINUTES = 30
    URGENT_CONSULTATION_MINUTES = 30
    
    # Break times
    LUNCH_START = time(12, 0)
    LUNCH_END = time(13, 0)
    
    # Days clinician works (Monday=0, Sunday=6)
    WORKING_DAYS = [0, 1, 2, 3, 4]  # Monday through Friday
    
    def __init__(self):
        self.booked_slots = set()  # Set of (date, time) tuples
        
    def is_available(self, date: datetime.date, start_time: time, duration_minutes: int) -> bool:
        """Check if a time slot is available."""
        # Check if it's a working day
        if date.weekday() not in self.WORKING_DAYS:
            return False
            
        # Check clinic hours
        if start_time < self.CLINIC_START or start_time >= self.CLINIC_END:
            return False
            
        # Calculate end time
        start_datetime = datetime.combine(date, start_time)
        end_datetime = start_datetime + timedelta(minutes=duration_minutes)
        end_time = end_datetime.time()
        
        # Check if appointment ends after clinic hours
        if end_time > self.CLINIC_END:
            return False
            
        # Check lunch break
        if (start_time < self.LUNCH_END and end_time > self.LUNCH_START):
            return False
            
        # Check for conflicts
        for booked_date, booked_time, booked_duration in self.booked_slots:
            if booked_date != date:
                continue
                
            booked_start = datetime.combine(booked_date, booked_time)
            booked_end = booked_start + timedelta(minutes=booked_duration)
            
            appointment_start = datetime.combine(date, start_time)
            appointment_end = appointment_start + timedelta(minutes=duration_minutes)
            
            # Check for overlap
            if not (appointment_end <= booked_start or appointment_start >= booked_end):
                return False
                
        return True
        
    def find_next_available_slot(
        self, 
        start_date: datetime.date, 
        duration_minutes: int,
        preferred_time: Optional[time] = None,
        max_days_ahead: int = 30
    ) -> Optional[Tuple[datetime.date, time]]:
        """Find the next available appointment slot."""
        current_date = start_date
        end_date = start_date + timedelta(days=max_days_ahead)
        
        # If preferred time specified, try that first for each day
        if preferred_time:
            while current_date <= end_date:
                if self.is_available(current_date, preferred_time, duration_minutes):
                    return current_date, preferred_time
                current_date += timedelta(days=1)
            # Reset for general search
            current_date = start_date
        
        # Search all time slots
        while current_date <= end_date:
            if current_date.weekday() not in self.WORKING_DAYS:
                current_date += timedelta(days=1)
                continue
                
            # Try morning slots first (9 AM - 12 PM)
            for hour in range(9, 12):
                for minute in [0, 30]:
                    slot_time = time(hour, minute)
                    if self.is_available(current_date, slot_time, duration_minutes):
                        return current_date, slot_time
                        
            # Then afternoon slots (1 PM - 5 PM)
            for hour in range(13, 17):
                for minute in [0, 30]:
                    slot_time = time(hour, minute)
                    if self.is_available(current_date, slot_time, duration_minutes):
                        return current_date, slot_time
                        
            current_date += timedelta(days=1)
            
        return None
        
    def book_appointment(
        self, 
        date: datetime.date, 
        start_time: time, 
        duration_minutes: int
    ) -> bool:
        """Book an appointment slot."""
        if self.is_available(date, start_time, duration_minutes):
            self.booked_slots.add((date, start_time, duration_minutes))
            return True
        return False
        
    def schedule_appointment(
        self,
        target_date: datetime.date,
        appointment_type: str,
        prefer_morning: bool = False,
        urgent: bool = False
    ) -> Optional[datetime]:
        """Schedule an appointment around target date."""
        # Determine duration
        if appointment_type == "Initial Consultation":
            duration = self.INITIAL_CONSULTATION_MINUTES
        elif appointment_type == "Follow-up":
            duration = self.FOLLOW_UP_MINUTES
        else:
            duration = self.URGENT_CONSULTATION_MINUTES if urgent else self.FOLLOW_UP_MINUTES
            
        # Set preferred time
        preferred_time = None
        if prefer_morning:
            preferred_time = time(random.choice([9, 10]), random.choice([0, 30]))
        
        # For urgent appointments, search earlier
        if urgent:
            search_start = target_date - timedelta(days=3)
        else:
            search_start = target_date - timedelta(days=2)
            
        # Find available slot
        result = self.find_next_available_slot(
            search_start,
            duration,
            preferred_time=preferred_time,
            max_days_ahead=14 if urgent else 7
        )
        
        if result:
            date, time_slot = result
            self.book_appointment(date, time_slot, duration)
            return datetime.combine(date, time_slot)
            
        return None
        
    def get_schedule_summary(self) -> dict:
        """Get summary of scheduled appointments."""
        total = len(self.booked_slots)
        by_weekday = {}
        
        for date, time_slot, duration in self.booked_slots:
            weekday = date.strftime("%A")
            if weekday not in by_weekday:
                by_weekday[weekday] = 0
            by_weekday[weekday] += 1
            
        return {
            "total_appointments": total,
            "by_weekday": by_weekday,
            "earliest": min((d for d, _, _ in self.booked_slots), default=None),
            "latest": max((d for d, _, _ in self.booked_slots), default=None)
        }