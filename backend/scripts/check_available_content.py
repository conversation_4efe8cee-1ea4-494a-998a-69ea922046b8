#!/usr/bin/env python3
"""
Check what content is available in the database for RAG retrieval.
"""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import func
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
# Import all models to ensure relationships are properly loaded
from app.models import *
from app.models.scraped_page import ScrapedPage
from app.models.content_chunk import ContentChunk
from app.models.education_material import EducationMaterial


def check_available_content():
    """Check what content is available in the database."""
    db = SessionLocal()
    
    try:
        # Check scraped pages
        scraped_pages = db.query(ScrapedPage).all()
        print(f"\n=== SCRAPED PAGES ({len(scraped_pages)} total) ===")
        for page in scraped_pages[:5]:  # Show first 5
            print(f"\nTitle: {page.title}")
            print(f"URL: {page.url}")
            print(f"Clinic: {page.clinic_id}")
            if page.cleaned_content:
                print(f"Content preview: {page.cleaned_content[:200]}...")
        
        # Check content chunks
        chunks_count = db.query(ContentChunk).count()
        print(f"\n=== CONTENT CHUNKS ({chunks_count} total) ===")
        
        # Show sample chunks
        sample_chunks = db.query(ContentChunk).limit(5).all()
        for i, chunk in enumerate(sample_chunks, 1):
            print(f"\nChunk {i}:")
            print(f"Content: {chunk.content[:200]}...")
            if chunk.metadata:
                print(f"Metadata: {chunk.metadata}")
        
        # Check education materials
        education_materials = db.query(EducationMaterial).all()
        print(f"\n=== EDUCATION MATERIALS ({len(education_materials)} total) ===")
        for material in education_materials:
            print(f"\nTitle: {material.title}")
            print(f"Type: {material.material_type}")
            print(f"Category: {material.category}")
            
        # Search for specific keywords
        print("\n=== SEARCHING FOR SPECIFIC CONTENT ===")
        
        keywords = ["doctor", "staff", "team", "physician", "clinician", "hours", "services"]
        for keyword in keywords:
            count = db.query(ContentChunk).filter(
                ContentChunk.content.ilike(f"%{keyword}%")
            ).count()
            print(f"Chunks containing '{keyword}': {count}")
            
        # Show clinic-specific chunk distribution
        print("\n=== CHUNKS BY CLINIC ===")
        clinic_chunks = db.query(
            ScrapedPage.clinic_id,
            func.count(ContentChunk.id)
        ).join(
            ContentChunk, ContentChunk.scraped_page_id == ScrapedPage.id
        ).group_by(ScrapedPage.clinic_id).all()
        
        for clinic_id, count in clinic_chunks:
            print(f"Clinic {clinic_id}: {count} chunks")
            
    finally:
        db.close()


if __name__ == "__main__":
    check_available_content()