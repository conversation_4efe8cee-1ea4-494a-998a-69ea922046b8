from app.db.session import SessionLocal
from app.models.content_chunk import ContentChunk


def main():
    db = SessionLocal()
    try:
        count = db.query(ContentChunk).count()
        print(f"Number of rows in content_chunks table: {count}")
    except Exception as e:
        print(f"Error querying content_chunks table: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    main()
