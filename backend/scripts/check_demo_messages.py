#!/usr/bin/env python3
"""
Check chat messages for <PERSON> to see if there are duplicates.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

def check_messages(db):
    """Check all messages for <PERSON>."""
    
    query = text("""
        SELECT 
            id,
            sender_type,
            LEFT(message_content, 80) as message_preview,
            created_at,
            message_route
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        ORDER BY created_at DESC
        LIMIT 20
    """)
    
    result = db.execute(query)
    messages = result.fetchall()
    
    print("="*100)
    print("RECENT MESSAGES FOR MICHAEL PATIENT (newest first):")
    print("="*100)
    
    for msg in messages:
        print(f"{msg.created_at.strftime('%Y-%m-%d %H:%M')} | {msg.sender_type:10} | {msg.message_route:10} | {msg.message_preview}...")
    
    # Check for exact duplicates
    dup_query = text("""
        SELECT 
            message_content,
            COUNT(*) as count
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        GROUP BY message_content
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 10
    """)
    
    dup_result = db.execute(dup_query)
    duplicates = dup_result.fetchall()
    
    if duplicates:
        print("\n" + "="*100)
        print("EXACT DUPLICATE MESSAGES:")
        print("="*100)
        for dup in duplicates:
            print(f"Count: {dup.count} | Message: {dup.message_content[:100]}...")
    
    # Count by sender and route
    count_query = text("""
        SELECT 
            sender_type,
            message_route,
            COUNT(*) as count
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        GROUP BY sender_type, message_route
        ORDER BY sender_type, message_route
    """)
    
    count_result = db.execute(count_query)
    counts = count_result.fetchall()
    
    print("\n" + "="*100)
    print("MESSAGE COUNTS BY TYPE:")
    print("="*100)
    total = 0
    for c in counts:
        print(f"{c.sender_type:10} -> {c.message_route:10} : {c.count} messages")
        total += c.count
    print(f"\nTOTAL: {total} messages")

def main():
    db = SessionLocal()
    
    try:
        check_messages(db)
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()