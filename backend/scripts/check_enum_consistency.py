#!/usr/bin/env python3
"""Check enum consistency between model, schema, and database."""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.chat_message import MessageSenderType
from app.schemas.chat import ChatMessageCreateInternal

print("=== Enum Consistency Check ===\n")

print("1. Model Enum Values (app.models.chat_message.MessageSenderType):")
for member in MessageSenderType:
    print(f"   {member.name} = '{member.value}'")

print("\n2. Schema Literal Types (app.schemas.chat.ChatMessageCreateInternal):")
print(f"   sender_type: {ChatMessageCreateInternal.model_fields['sender_type'].annotation}")

print("\n3. Recommendations:")
print("   ✓ All enum values are now uppercase in the model")
print("   ✓ Schema accepts both lowercase and uppercase values")
print("   ✓ Validator maps lowercase to uppercase for consistency")
print("   ✓ Database has uppercase CLINICAL_NOTE value")

print("\n4. Future migrations will use uppercase values consistently")