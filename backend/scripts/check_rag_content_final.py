#!/usr/bin/env python3
"""
Final simplified script to check RAG content.
"""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings


def check_rag_content():
    """Check RAG content using direct SQL queries."""
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        print("\n=== RAG CONTENT ANALYSIS ===\n")
        
        # Check scraped pages
        result = conn.execute(text("SELECT COUNT(*) FROM scraped_pages"))
        scraped_count = result.scalar()
        print(f"Total scraped pages: {scraped_count}")
        
        # Check content chunks
        result = conn.execute(text("SELECT COUNT(*) FROM content_chunks"))
        chunk_count = result.scalar()
        print(f"Total content chunks: {chunk_count}")
        
        # Check education materials
        result = conn.execute(text("SELECT COUNT(*) FROM education_materials"))
        edu_count = result.scalar()
        print(f"Total education materials: {edu_count}")
        
        # Analyze chunk sources
        print("\n=== CHUNK SOURCES ===")
        result = conn.execute(text("""
            SELECT 
                CASE 
                    WHEN scraped_page_id IS NOT NULL THEN 'Scraped Pages'
                    WHEN metadata->>'source_type' = 'education_material' THEN 'Education Materials'
                    ELSE 'Unknown'
                END as source_type,
                COUNT(*) as count
            FROM content_chunks
            GROUP BY source_type
        """))
        for row in result:
            print(f"{row.source_type}: {row.count} chunks")
        
        # Search for clinic-related content
        print("\n=== SEARCHING FOR CLINIC CONTENT ===")
        keywords = ["doctor", "staff", "physician", "clinic", "Edinburgh", "Isaac"]
        
        for keyword in keywords:
            result = conn.execute(
                text("SELECT COUNT(*) FROM content_chunks WHERE chunk_text ILIKE :pattern"),
                {"pattern": f"%{keyword}%"}
            )
            count = result.scalar()
            print(f"Chunks containing '{keyword}': {count}")
        
        # Check if any chunks have embeddings
        print("\n=== EMBEDDING STATUS ===")
        result = conn.execute(text("""
            SELECT 
                COUNT(*) as total_chunks,
                COUNT(embedding) as chunks_with_embeddings
            FROM content_chunks
        """))
        row = result.fetchone()
        print(f"Total chunks: {row.total_chunks}")
        print(f"Chunks with embeddings: {row.chunks_with_embeddings}")
        
        # Show sample of what content we DO have
        print("\n=== SAMPLE CONTENT ===")
        result = conn.execute(text("""
            SELECT 
                LEFT(chunk_text, 150) as preview,
                metadata->>'title' as title
            FROM content_chunks 
            WHERE chunk_text ILIKE '%weight%' OR chunk_text ILIKE '%medication%'
            LIMIT 3
        """))
        for row in result:
            print(f"\nTitle: {row.title}")
            print(f"Preview: {row.preview}...")
        
        print("\n=== CONCLUSION ===")
        print(f"The database contains {chunk_count} content chunks, all from {edu_count} education materials.")
        print(f"There are NO scraped pages from clinics (0 pages).")
        print("This explains why queries about 'clinic doctors' return no results.")
        print("\nTo enable clinic-specific queries, you need to:")
        print("1. Run the web scraper to scrape clinic websites")
        print("2. Process the scraped content into chunks")
        print("3. Generate embeddings for the new chunks")


if __name__ == "__main__":
    check_rag_content()