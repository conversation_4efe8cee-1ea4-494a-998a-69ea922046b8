#!/usr/bin/env python3
"""
Simple script to check RAG content directly in the database.
"""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings


def check_rag_content():
    """Check RAG content using direct SQL queries."""
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        print("\n=== SCRAPED PAGES ===")
        result = conn.execute(text("SELECT COUNT(*) FROM scraped_pages"))
        count = result.scalar()
        print(f"Total scraped pages: {count}")
        
        if count > 0:
            result = conn.execute(text("""
                SELECT id, title, url, clinic_id, 
                       LEFT(cleaned_content, 200) as content_preview
                FROM scraped_pages 
                LIMIT 5
            """))
            for row in result:
                print(f"\nPage ID: {row.id}")
                print(f"Title: {row.title}")
                print(f"URL: {row.url}")
                print(f"Clinic: {row.clinic_id}")
                if row.content_preview:
                    print(f"Content preview: {row.content_preview}...")
        
        print("\n\n=== CONTENT CHUNKS ===")
        result = conn.execute(text("SELECT COUNT(*) FROM content_chunks"))
        count = result.scalar()
        print(f"Total content chunks: {count}")
        
        if count > 0:
            result = conn.execute(text("""
                SELECT id, LEFT(chunk_text, 200) as content_preview, 
                       scraped_page_id, metadata
                FROM content_chunks 
                LIMIT 5
            """))
            for row in result:
                print(f"\nChunk ID: {row.id}")
                print(f"Content: {row.content_preview}...")
                print(f"Page ID: {row.scraped_page_id}")
                if row.metadata:
                    print(f"Metadata: {row.metadata}")
        
        print("\n\n=== EDUCATION MATERIALS ===")
        result = conn.execute(text("SELECT COUNT(*) FROM education_materials"))
        count = result.scalar()
        print(f"Total education materials: {count}")
        
        if count > 0:
            result = conn.execute(text("""
                SELECT id, title, material_type, category
                FROM education_materials 
                LIMIT 5
            """))
            for row in result:
                print(f"\n{row.title} ({row.material_type}) - {row.category}")
        
        print("\n\n=== SEARCHING FOR CLINIC/DOCTOR CONTENT ===")
        keywords = ["doctor", "staff", "team", "physician", "clinician", "hours", "services", "clinic"]
        
        for keyword in keywords:
            result = conn.execute(
                text("SELECT COUNT(*) FROM content_chunks WHERE chunk_text ILIKE :pattern"),
                {"pattern": f"%{keyword}%"}
            )
            count = result.scalar()
            print(f"Chunks containing '{keyword}': {count}")
            
            if count > 0:
                # Show a sample
                result = conn.execute(
                    text("""
                        SELECT LEFT(chunk_text, 300) as content_preview 
                        FROM content_chunks 
                        WHERE chunk_text ILIKE :pattern 
                        LIMIT 1
                    """),
                    {"pattern": f"%{keyword}%"}
                )
                row = result.fetchone()
                if row:
                    print(f"  Sample: {row.content_preview}...")
        
        print("\n\n=== CHUNKS BY CLINIC ===")
        result = conn.execute(text("""
            SELECT sp.clinic_id, COUNT(cc.id) as chunk_count
            FROM scraped_pages sp
            LEFT JOIN content_chunks cc ON cc.scraped_page_id = sp.id
            GROUP BY sp.clinic_id
        """))
        for row in result:
            print(f"Clinic {row.clinic_id}: {row.chunk_count} chunks")
            
        print("\n\n=== CHECKING EMBEDDINGS ===")
        result = conn.execute(text("""
            SELECT COUNT(*) as total,
                   COUNT(embedding) as with_embedding,
                   COUNT(*) - COUNT(embedding) as without_embedding
            FROM content_chunks
        """))
        row = result.fetchone()
        print(f"Total chunks: {row.total}")
        print(f"With embeddings: {row.with_embedding}")
        print(f"Without embeddings: {row.without_embedding}")


if __name__ == "__main__":
    check_rag_content()