#!/usr/bin/env python3
"""
Clean all demo data except <PERSON>, <PERSON> (clinician), and their clinic.
This prepares the database for the critical issue detection demo.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import delete, select
from app.db.session import SessionLocal, engine
from app.models import (
    <PERSON><PERSON>, Clinician,
    Appointment, AppointmentRequest,
    ChatMessage, MedicationRequest,
    Note, PatientAlert, Notification,
    EventLog, EducationMaterial,
    PatientEducationAssignment, EducationProgress,
    Template, UserSetting
)
from app.models.clinic import Clinic
from app.models.side_effect_report import SideEffectReport
from app.models.weight_log import WeightLog
from app.models.content_chunk import ContentChunk
from app.models.scraped_page import ScrapedPage
from app.models.clinic_medication_association import ClinicMedicationAssociation

# IDs to preserve
MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
MICHAEL_CLINICIAN_ID = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
CLINIC_ID = "385af354-bfe1-4ead-8651-92110e698e30"

async def clean_demo_data():
    """Delete all data except for core users and clinic."""
    db = SessionLocal()
    
    try:
        print("🧹 Starting demo data cleanup...")
        print(f"Preserving: Patient {MICHAEL_PATIENT_ID}, Clinician {MICHAEL_CLINICIAN_ID}, Clinic {CLINIC_ID}")
        
        # Delete appointments (all - we'll recreate)
        deleted = db.execute(delete(Appointment))
        print(f"✓ Deleted {deleted.rowcount} appointments")
        
        # Delete appointment requests
        deleted = db.execute(delete(AppointmentRequest))
        print(f"✓ Deleted {deleted.rowcount} appointment requests")
        
        # Delete chat messages (all - we'll recreate the concerning pattern)
        deleted = db.execute(delete(ChatMessage))
        print(f"✓ Deleted {deleted.rowcount} chat messages")
        
        # Delete side effect reports (all - we'll recreate)
        deleted = db.execute(delete(SideEffectReport))
        print(f"✓ Deleted {deleted.rowcount} side effect reports")
        
        # Delete medication requests (all - we'll recreate)
        deleted = db.execute(delete(MedicationRequest))
        print(f"✓ Deleted {deleted.rowcount} medication requests")
        
        # Delete weight logs (all - we'll recreate the concerning pattern)
        deleted = db.execute(delete(WeightLog))
        print(f"✓ Deleted {deleted.rowcount} weight logs")
        
        # Delete notes for other patients
        try:
            deleted = db.execute(
                delete(Note).where(Note.patient_id != MICHAEL_PATIENT_ID)
            )
            print(f"✓ Deleted {deleted.rowcount} notes for other patients")
        except Exception as e:
            print(f"⚠️  Skipping notes deletion due to: {e}")
        
        # Delete patient alerts (all - we'll recreate)
        deleted = db.execute(delete(PatientAlert))
        print(f"✓ Deleted {deleted.rowcount} patient alerts")
        
        # Delete notifications (all)
        deleted = db.execute(delete(Notification))
        print(f"✓ Deleted {deleted.rowcount} notifications")
        
        # Delete event logs (all - we'll recreate)
        deleted = db.execute(delete(EventLog))
        print(f"✓ Deleted {deleted.rowcount} event logs")
        
        # Delete education progress (all)
        deleted = db.execute(delete(EducationProgress))
        print(f"✓ Deleted {deleted.rowcount} education progress records")
        
        # Delete patient education assignments (all)
        deleted = db.execute(delete(PatientEducationAssignment))
        print(f"✓ Deleted {deleted.rowcount} patient education assignments")
        
        # Delete education materials (all - we'll recreate)
        deleted = db.execute(delete(EducationMaterial))
        print(f"✓ Deleted {deleted.rowcount} education materials")
        
        # Delete content chunks (all - we'll recreate if needed)
        deleted = db.execute(delete(ContentChunk))
        print(f"✓ Deleted {deleted.rowcount} content chunks")
        
        # Delete scraped pages (all)
        deleted = db.execute(delete(ScrapedPage))
        print(f"✓ Deleted {deleted.rowcount} scraped pages")
        
        # Delete user settings for other users
        deleted = db.execute(
            delete(UserSetting).where(
                (UserSetting.user_id != MICHAEL_PATIENT_ID) & 
                (UserSetting.user_id != MICHAEL_CLINICIAN_ID)
            )
        )
        print(f"✓ Deleted {deleted.rowcount} user settings for other users")
        
        # Delete other patients (keep only Michael)
        deleted = db.execute(
            delete(Patient).where(Patient.id != MICHAEL_PATIENT_ID)
        )
        print(f"✓ Deleted {deleted.rowcount} other patients")
        
        # Delete other clinicians (keep only Michael Isaac)
        deleted = db.execute(
            delete(Clinician).where(Clinician.id != MICHAEL_CLINICIAN_ID)
        )
        print(f"✓ Deleted {deleted.rowcount} other clinicians")
        
        # Verify what's left
        patient_count = db.query(Patient).count()
        clinician_count = db.query(Clinician).count()
        clinic_count = db.query(Clinic).count()
        
        print(f"\n✅ Cleanup complete!")
        print(f"Remaining: {patient_count} patient(s), {clinician_count} clinician(s), {clinic_count} clinic(s)")
        
        # Commit all changes
        db.commit()
        print("✓ Changes committed to database")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(clean_demo_data())