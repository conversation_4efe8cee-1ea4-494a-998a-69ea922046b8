#!/usr/bin/env python3
"""
Simple cleanup script focusing on essential tables.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import Session<PERSON>ocal
from sqlalchemy import text

# IDs to preserve
MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
MICHAEL_CLINICIAN_ID = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
CLINIC_ID = "385af354-bfe1-4ead-8651-92110e698e30"

def clean_demo_data():
    """Delete all data except for core users and clinic using raw SQL."""
    db = SessionLocal()
    
    try:
        print("🧹 Starting demo data cleanup with raw SQL...")
        print(f"Preserving: Patient {MICHAEL_PATIENT_ID}, Clinician {MICH<PERSON>EL_CLINICIAN_ID}, Clinic {CLINIC_ID}")
        
        # Execute cleanup queries
        cleanup_queries = [
            ("appointments", "DELETE FROM appointments"),
            ("appointment_requests", "DELETE FROM appointment_requests"),
            ("chat_messages", "DELETE FROM chat_messages"),
            ("side_effect_reports", "DELETE FROM side_effect_reports"),
            ("medication_requests", "DELETE FROM medication_requests"),
            ("weight_logs", "DELETE FROM weight_logs"),
            ("patient_alerts", "DELETE FROM patient_alerts"),
            ("notifications", "DELETE FROM notifications"),
            ("event_logs", "DELETE FROM event_logs"),
            ("education_progress", "DELETE FROM education_progress"),
            ("patient_education_assignments", "DELETE FROM patient_education_assignments"),
            ("education_materials", "DELETE FROM education_materials"),
            ("content_chunks", "DELETE FROM content_chunks"),
            ("scraped_pages", "DELETE FROM scraped_pages"),
            ("notes", f"DELETE FROM notes WHERE patient_id != '{MICHAEL_PATIENT_ID}'"),
            ("user_settings", f"DELETE FROM user_settings WHERE user_id NOT IN ('{MICHAEL_PATIENT_ID}', '{MICHAEL_CLINICIAN_ID}')"),
            ("clinician_patient_association", f"DELETE FROM clinician_patient_association WHERE patient_id != '{MICHAEL_PATIENT_ID}'"),
            ("patients", f"DELETE FROM patients WHERE id != '{MICHAEL_PATIENT_ID}'"),
            ("clinicians", f"DELETE FROM clinicians WHERE id != '{MICHAEL_CLINICIAN_ID}'"),
        ]
        
        for table_name, query in cleanup_queries:
            try:
                result = db.execute(text(query))
                db.commit()  # Commit each successful deletion
                print(f"✓ Deleted {result.rowcount} rows from {table_name}")
            except Exception as e:
                print(f"⚠️  Skipping {table_name}: {e}")
                db.rollback()  # Rollback failed deletion
        
        # Verify what's left
        patient_count = db.execute(text("SELECT COUNT(*) FROM patients")).scalar()
        clinician_count = db.execute(text("SELECT COUNT(*) FROM clinicians")).scalar()
        clinic_count = db.execute(text("SELECT COUNT(*) FROM clinics")).scalar()
        
        print(f"\n✅ Cleanup complete!")
        print(f"Remaining: {patient_count} patient(s), {clinician_count} clinician(s), {clinic_count} clinic(s)")
        
        # Commit all changes
        db.commit()
        print("✓ Changes committed to database")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    clean_demo_data()