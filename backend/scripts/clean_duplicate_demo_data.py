#!/usr/bin/env python3
"""
Clean up duplicate demo data created by multiple runs of the investor demo script.
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

def find_and_remove_duplicate_messages(db):
    """Find and remove duplicate chat messages, keeping the oldest of each duplicate set."""
    print("Finding duplicate chat messages...")
    
    # Find duplicates based on patient_id, sender_type, message_content, and similar timestamp
    duplicate_query = text("""
        WITH duplicates AS (
            SELECT 
                id,
                patient_id,
                sender_type,
                message_content,
                created_at,
                ROW_NUMBER() OVER (
                    PARTITION BY patient_id, sender_type, message_content, 
                    DATE_TRUNC('minute', created_at)
                    ORDER BY created_at ASC
                ) as rn
            FROM chat_messages
            WHERE patient_id IN (
                SELECT id FROM patients 
                WHERE id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
            )
            AND created_at > NOW() - INTERVAL '2 hours'
        )
        SELECT id, patient_id, sender_type, LEFT(message_content, 50) as msg_preview, created_at, rn
        FROM duplicates
        WHERE rn > 1
        ORDER BY created_at DESC
    """)
    
    result = db.execute(duplicate_query)
    duplicates = result.fetchall()
    
    if not duplicates:
        print("No duplicate messages found.")
        return
    
    print(f"Found {len(duplicates)} duplicate messages to remove:")
    for dup in duplicates[:10]:  # Show first 10
        print(f"  - {dup.msg_preview}... (created: {dup.created_at})")
    
    if len(duplicates) > 10:
        print(f"  ... and {len(duplicates) - 10} more")
    
    # Delete duplicates
    duplicate_ids = [str(dup.id) for dup in duplicates]
    delete_query = text("""
        DELETE FROM chat_messages 
        WHERE id::text = ANY(:ids)
    """)
    
    db.execute(delete_query, {"ids": duplicate_ids})
    db.commit()
    print(f"Deleted {len(duplicates)} duplicate messages.")

def find_and_remove_duplicate_weight_logs(db):
    """Find and remove duplicate weight logs."""
    print("\nFinding duplicate weight logs...")
    
    duplicate_query = text("""
        WITH duplicates AS (
            SELECT 
                id,
                patient_id,
                weight_kg,
                log_date,
                ROW_NUMBER() OVER (
                    PARTITION BY patient_id, log_date
                    ORDER BY created_at ASC
                ) as rn
            FROM weight_logs
            WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
            AND created_at > NOW() - INTERVAL '2 hours'
        )
        SELECT id, weight_kg, log_date, rn
        FROM duplicates
        WHERE rn > 1
        ORDER BY log_date DESC
    """)
    
    result = db.execute(duplicate_query)
    duplicates = result.fetchall()
    
    if not duplicates:
        print("No duplicate weight logs found.")
        return
    
    print(f"Found {len(duplicates)} duplicate weight logs to remove")
    
    # Delete duplicates
    duplicate_ids = [str(dup.id) for dup in duplicates]
    delete_query = text("""
        DELETE FROM weight_logs 
        WHERE id::text = ANY(:ids)
    """)
    
    db.execute(delete_query, {"ids": duplicate_ids})
    db.commit()
    print(f"Deleted {len(duplicates)} duplicate weight logs.")

def find_and_remove_duplicate_side_effects(db):
    """Find and remove duplicate side effect reports."""
    print("\nFinding duplicate side effect reports...")
    
    duplicate_query = text("""
        WITH duplicates AS (
            SELECT 
                id,
                patient_id,
                description,
                severity,
                reported_at,
                ROW_NUMBER() OVER (
                    PARTITION BY patient_id, description, DATE_TRUNC('day', reported_at)
                    ORDER BY reported_at ASC
                ) as rn
            FROM side_effect_reports
            WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
            AND reported_at > NOW() - INTERVAL '35 days'
        )
        SELECT id, LEFT(description, 50) as desc_preview, severity, reported_at, rn
        FROM duplicates
        WHERE rn > 1
        ORDER BY reported_at DESC
    """)
    
    result = db.execute(duplicate_query)
    duplicates = result.fetchall()
    
    if not duplicates:
        print("No duplicate side effect reports found.")
        return
    
    print(f"Found {len(duplicates)} duplicate side effect reports to remove")
    
    # Delete duplicates
    duplicate_ids = [str(dup.id) for dup in duplicates]
    delete_query = text("""
        DELETE FROM side_effect_reports 
        WHERE id::text = ANY(:ids)
    """)
    
    db.execute(delete_query, {"ids": duplicate_ids})
    db.commit()
    print(f"Deleted {len(duplicates)} duplicate side effect reports.")

def find_and_remove_duplicate_alerts(db):
    """Find and remove duplicate patient alerts."""
    print("\nFinding duplicate patient alerts...")
    
    duplicate_query = text("""
        WITH duplicates AS (
            SELECT 
                id,
                patient_id,
                title,
                created_at,
                ROW_NUMBER() OVER (
                    PARTITION BY patient_id, title
                    ORDER BY created_at ASC
                ) as rn
            FROM patient_alerts
            WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
            AND created_at > NOW() - INTERVAL '3 hours'
        )
        SELECT id, title, created_at, rn
        FROM duplicates
        WHERE rn > 1
        ORDER BY created_at DESC
    """)
    
    result = db.execute(duplicate_query)
    duplicates = result.fetchall()
    
    if not duplicates:
        print("No duplicate alerts found.")
        return
    
    print(f"Found {len(duplicates)} duplicate alerts to remove")
    
    # Delete duplicates
    duplicate_ids = [str(dup.id) for dup in duplicates]
    delete_query = text("""
        DELETE FROM patient_alerts 
        WHERE id::text = ANY(:ids)
    """)
    
    db.execute(delete_query, {"ids": duplicate_ids})
    db.commit()
    print(f"Deleted {len(duplicates)} duplicate alerts.")

def check_final_counts(db):
    """Show final counts of demo data."""
    print("\n" + "="*60)
    print("FINAL DATA COUNTS FOR MICHAEL PATIENT:")
    print("="*60)
    
    counts_query = text("""
        SELECT 
            (SELECT COUNT(*) FROM chat_messages 
             WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
             AND created_at > NOW() - INTERVAL '35 days') as chat_messages,
            (SELECT COUNT(*) FROM weight_logs 
             WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as weight_logs,
            (SELECT COUNT(*) FROM side_effect_reports 
             WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as side_effects,
            (SELECT COUNT(*) FROM patient_alerts 
             WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
             AND status = 'New') as active_alerts
    """)
    
    result = db.execute(counts_query).fetchone()
    
    print(f"Chat Messages: {result.chat_messages}")
    print(f"Weight Logs: {result.weight_logs}")
    print(f"Side Effect Reports: {result.side_effects}")
    print(f"Active Alerts: {result.active_alerts}")

def main():
    """Main function to clean duplicate demo data."""
    print("="*60)
    print("CLEANING DUPLICATE DEMO DATA")
    print("="*60)
    
    db = SessionLocal()
    
    try:
        find_and_remove_duplicate_messages(db)
        find_and_remove_duplicate_weight_logs(db)
        find_and_remove_duplicate_side_effects(db)
        find_and_remove_duplicate_alerts(db)
        
        check_final_counts(db)
        
        print("\nCleanup complete!")
        
    except Exception as e:
        print(f"Error during cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()