#!/usr/bin/env python3
"""
Clean up exact duplicate messages keeping only the oldest of each.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

def clean_exact_duplicates(db):
    """Remove exact duplicate messages keeping only the oldest."""
    
    print("Finding and removing exact duplicate messages...")
    
    # First, let's see what we're dealing with
    count_query = text("""
        SELECT 
            patient_id,
            sender_type,
            message_content,
            message_route,
            COUNT(*) as count,
            MIN(created_at) as first_created,
            MAX(created_at) as last_created
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        GROUP BY patient_id, sender_type, message_content, message_route
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
    """)
    
    result = db.execute(count_query)
    duplicates = result.fetchall()
    
    print(f"Found {len(duplicates)} unique messages with duplicates")
    
    total_to_delete = 0
    for dup in duplicates:
        total_to_delete += (dup.count - 1)
    
    print(f"Total duplicate messages to delete: {total_to_delete}")
    
    # Delete duplicates keeping the oldest
    delete_query = text("""
        WITH duplicates AS (
            SELECT 
                id,
                patient_id,
                sender_type,
                message_content,
                message_route,
                created_at,
                ROW_NUMBER() OVER (
                    PARTITION BY patient_id, sender_type, message_content, message_route
                    ORDER BY created_at ASC
                ) as rn
            FROM chat_messages
            WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        ),
        to_delete AS (
            SELECT id 
            FROM duplicates 
            WHERE rn > 1
        )
        DELETE FROM chat_messages
        WHERE id IN (SELECT id FROM to_delete)
    """)
    
    db.execute(delete_query)
    db.commit()
    
    print(f"Deleted {total_to_delete} duplicate messages")
    
    # Show final count
    final_count_query = text("""
        SELECT COUNT(*) as total
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
    """)
    
    result = db.execute(final_count_query)
    total = result.fetchone().total
    
    print(f"\nFinal message count for Michael Patient: {total}")
    
    # Show breakdown
    breakdown_query = text("""
        SELECT 
            sender_type,
            message_route,
            COUNT(*) as count
        FROM chat_messages
        WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        GROUP BY sender_type, message_route
        ORDER BY sender_type, message_route
    """)
    
    result = db.execute(breakdown_query)
    breakdown = result.fetchall()
    
    print("\nMessage breakdown:")
    for b in breakdown:
        print(f"  {b.sender_type:10} -> {b.message_route:10} : {b.count} messages")

def main():
    print("="*60)
    print("CLEANING EXACT DUPLICATE MESSAGES")
    print("="*60)
    
    db = SessionLocal()
    
    try:
        clean_exact_duplicates(db)
        print("\nCleanup complete!")
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()