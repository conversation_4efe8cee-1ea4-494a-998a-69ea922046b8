#!/usr/bin/env python3
"""
Create a draft clinical note directly in the database.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app.db.session import <PERSON>Local
from app.crud.crud_patient import patient as crud_patient
from app.crud.crud_clinician import clinician as crud_clinician
from app.crud.crud_clinical_note import clinical_note as crud_clinical_note
from app.schemas.clinical_note import ClinicalNoteCreate
from app.models.clinical_note import ClinicalNoteStatus
from app.core.logging import logger


def create_draft_note():
    """Create a clinical note directly in draft status."""
    db: Session = SessionLocal()
    
    try:
        # Get demo clinician and patient
        demo_clinician = crud_clinician.get(db, id="user_2waSREJSlduBPyK6Vbv9TU3VhI7")
        demo_patient = crud_patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        
        if not demo_clinician or not demo_patient:
            logger.error("Demo clinician or patient not found")
            return
            
        logger.info(f"Using clinician: {demo_clinician.first_name} {demo_clinician.last_name}")
        logger.info(f"Using patient: {demo_patient.first_name} {demo_patient.last_name}")
        
        # Create a clinical note
        note_data = ClinicalNoteCreate(
            patient_id=demo_patient.id,
            appointment_id=None,
            chat_session_id=None,
            note_type="progress_note",
            chief_complaint="Follow-up for weight management on GLP-1 medication",
            sections={
                "subjective": "Patient reports continued weight loss on Ozempic. Appetite well controlled. No significant side effects.",
                "objective": "Weight: 195 lbs (down 5 lbs from last visit). BP: 120/80. HR: 72.",
                "assessment": "Patient responding well to GLP-1 therapy with steady weight loss and no adverse effects.",
                "plan": "Continue current Ozempic dose. Follow up in 4 weeks. Continue diet and exercise plan."
            },
            metadata={
                "generated_at": datetime.utcnow().isoformat(),
                "test_note": True
            },
            ai_generated=True,
            ai_model="manual-test",
            ai_confidence_score=0.95
        )
        
        # Create the note
        created_note = crud_clinical_note.create_with_clinician(
            db, obj_in=note_data, clinician_id=demo_clinician.id
        )
        
        logger.info(f"\n✓ Clinical note created successfully!")
        logger.info(f"  Note ID: {created_note.id}")
        logger.info(f"  Status: {created_note.status}")
        logger.info(f"  Type: {created_note.note_type}")
        logger.info(f"  Patient: {demo_patient.first_name} {demo_patient.last_name}")
        logger.info(f"  Clinician: {demo_clinician.first_name} {demo_clinician.last_name}")
        logger.info(f"\nThe note is in '{created_note.status}' status and can be deleted!")
        
    except Exception as e:
        logger.error(f"Error creating note: {str(e)}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("Creating Draft Clinical Note")
    logger.info("=" * 60)
    create_draft_note()
    logger.info("=" * 60)