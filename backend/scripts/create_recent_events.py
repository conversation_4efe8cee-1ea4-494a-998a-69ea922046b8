#!/usr/bin/env python
"""
Create some recent event logs for testing the Patient Activity feed.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
from uuid import uuid4

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.session import SessionLocal
from app import crud
from app.schemas.event_log import EventLogCreate

def create_recent_events():
    """Create some recent event logs."""
    db = SessionLocal()
    try:
        # Get a sample patient ID
        result = db.execute(text("""
            SELECT id, first_name, last_name 
            FROM patients 
            WHERE id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
            LIMIT 1
        """))
        patient = result.fetchone()
        
        if not patient:
            print("No patient found!")
            return
            
        patient_id = patient.id
        patient_name = f"{patient.first_name} {patient.last_name}"
        
        print(f"Creating recent events for {patient_name}...")
        
        # Create events from the last few hours
        events = [
            {
                "action": "CREATE",
                "resource_type": "chat_message",
                "time_ago": timedelta(minutes=5),
                "details": {
                    "message_preview": "I've been feeling better since adjusting my medication schedule",
                    "sender_type": "PATIENT",
                    "actor_name": patient_name
                }
            },
            {
                "action": "CREATE",
                "resource_type": "weight_log",
                "time_ago": timedelta(hours=2),
                "details": {
                    "weight_kg": 85.5,
                    "actor_name": patient_name
                }
            },
            {
                "action": "CREATE",
                "resource_type": "side_effect_report",
                "time_ago": timedelta(hours=12),
                "details": {
                    "severity": "minor",
                    "description": "Mild headache after morning dose",
                    "actor_name": patient_name
                }
            },
            {
                "action": "UPDATE",
                "resource_type": "appointment",
                "time_ago": timedelta(days=1),
                "details": {
                    "appointment_type": "Follow-up",
                    "status": "scheduled",
                    "actor_name": patient_name
                }
            }
        ]
        
        # Create the event logs
        for event_data in events:
            event_time = datetime.utcnow() - event_data["time_ago"]
            
            event_log_data = EventLogCreate(
                action=event_data["action"],
                actor_user_id=patient_id,
                actor_role="patient",
                target_resource_type=event_data["resource_type"],
                target_resource_id=str(uuid4()),
                status="SUCCESS",
                outcome="SUCCESS",
                details=event_data["details"]
            )
            
            event_log = crud.event_log.event_log.create_with_actor(
                db,
                obj_in=event_log_data,
                actor_user_id=patient_id,
                actor_role="patient"
            )
            
            # Update the created_at timestamp
            db.execute(
                text("UPDATE event_logs SET created_at = :created_at WHERE id = :id"),
                {"created_at": event_time, "id": event_log.id}
            )
            
            print(f"Created {event_data['resource_type']} event from {event_data['time_ago']} ago")
        
        db.commit()
        print("\nRecent events created successfully!")
        
        # Show total count
        result = db.execute(text("SELECT COUNT(*) FROM event_logs"))
        total = result.scalar()
        print(f"Total event logs in database: {total}")
        
    finally:
        db.close()


if __name__ == "__main__":
    create_recent_events()