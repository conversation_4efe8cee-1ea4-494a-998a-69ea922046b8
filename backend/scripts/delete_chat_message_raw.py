"""Delete a specific clinical note message from chat using raw SQL."""
import sys
sys.path.append(".")

from app.db.session import <PERSON><PERSON>ocal
from sqlalchemy import text

# The chat message ID from the logs
CHAT_MESSAGE_ID = "f3bd056f-ba92-4ca4-a3ce-db5e1ae98c95"

db = SessionLocal()

try:
    # First check if the message exists
    result = db.execute(
        text("SELECT id, sender_type, message_content FROM chat_messages WHERE id = :message_id"),
        {"message_id": CHAT_MESSAGE_ID}
    ).first()
    
    if result:
        print(f"Found message: {result.id}")
        print(f"  Type: {result.sender_type}")
        print(f"  Content: {result.message_content[:100]}...")
        
        # Delete the message
        db.execute(
            text("DELETE FROM chat_messages WHERE id = :message_id"),
            {"message_id": CHAT_MESSAGE_ID}
        )
        db.commit()
        
        print("\nMessage deleted successfully!")
    else:
        print(f"Message {CHAT_MESSAGE_ID} not found")

finally:
    db.close()