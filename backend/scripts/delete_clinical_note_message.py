"""Delete a specific clinical note message from chat."""
import sys
sys.path.append(".")

from app.db.session import SessionLocal
from app.db.base import Base  # Import all models
from app.models import *  # Import all models to ensure relationships are loaded
from app.models.chat_message import ChatMessage

# The chat message ID from the logs
CHAT_MESSAGE_ID = "f3bd056f-ba92-4ca4-a3ce-db5e1ae98c95"

db = SessionLocal()

try:
    # Find and delete the chat message
    message = db.query(ChatMessage).filter(ChatMessage.id == CHAT_MESSAGE_ID).first()
    
    if message:
        print(f"Found message: {message.id}")
        print(f"  Type: {message.sender_type}")
        print(f"  Content: {message.message_content[:100]}...")
        
        # Delete the message
        db.delete(message)
        db.commit()
        
        print("\nMessage deleted successfully!")
    else:
        print(f"Message {CHAT_MESSAGE_ID} not found")

finally:
    db.close()