#!/usr/bin/env python
"""
Demo Reset Script - One-click reset for investor demonstrations

This script safely resets the demo environment to a pristine state:
1. Clears existing demo data
2. Reseeds with fresh demo data
3. Resets all counters and timestamps
4. Ensures consistent state for demos

Safety features:
- Confirms environment before running
- Only affects demo-specific data
- Preserves system configuration
"""

import sys
import os
from datetime import datetime, timedelta
import random
from typing import List, Dict, Any
import asyncio
import subprocess
from sqlalchemy import text

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.core.config import settings
from app.models import *
from app.models.medication_request import MedicationRequestStatus
from app.models.appointment_request import AppointmentRequest
from app.models.chat_message import ChatMessage, MessageSenderType, MessageRouteType
from app.models.event_log import EventLog
from scripts.seed_demo_data import DemoDataSeeder
from scripts.seed_investor_demo_scenario import main as seed_investor_demo_scenario
from scripts.seed_additional_demo_patients import seed_additional_patients
from scripts.shared_appointment_scheduler import reset_scheduler

# Demo user IDs that should be preserved
DEMO_USER_IDS = {
    "clinician": "user_2waSREJSlduBPyK6Vbv9TU3VhI7",  # Dr. Michael Isaac
    "patient": "user_2waTCuGL3kQC9k2rY47INdcJXk5",    # Michael Patient
    "clinic": "385af354-bfe1-4ead-8651-92110e698e30"   # Edinburgh Weight Loss Clinic
}

# Additional demo patient Clerk IDs
DEMO_PATIENT_IDS = [
    "user_demo_patient_sarah",
    "user_demo_patient_james", 
    "user_demo_patient_robert",
    "user_demo_patient_emily"
]


def confirm_environment():
    """Ensure we're in a safe environment for demo reset."""
    print("\n" + "="*60)
    print("🚨 DEMO RESET CONFIRMATION 🚨")
    print("="*60)
    
    # Check if we're in production
    if "prod" in settings.DATABASE_URL.lower():
        print("❌ ERROR: Cannot run demo reset in production environment!")
        print("   Database URL contains 'prod'")
        sys.exit(1)
    
    print(f"\n📊 Current Environment:")
    print(f"   Database: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'local'}")
    print(f"   Environment: {'PRODUCTION' if 'prod' in settings.DATABASE_URL.lower() else 'DEVELOPMENT'}")
    
    print(f"\n👥 Demo Users to Preserve:")
    print(f"   Clinician: {DEMO_USER_IDS['clinician']}")
    print(f"   Patient: {DEMO_USER_IDS['patient']}")
    print(f"   Clinic: {DEMO_USER_IDS['clinic']}")
    
    print("\n⚠️  This will:")
    print("   1. Delete all patient data except demo accounts")
    print("   2. Delete all appointments, messages, and clinical data")
    print("   3. Reseed with fresh demo data")
    print("   4. Reset all timestamps to recent dates")
    
    # Check for automatic confirmation via environment variable (for API usage)
    auto_confirm = os.environ.get('DEMO_RESET_CONFIRM')
    if auto_confirm == 'RESET':
        print("\n✅ Auto-confirmation received from environment variable. Starting demo reset...")
        return True
    
    response = input("\n❓ Are you sure you want to reset the demo? (type 'RESET' to confirm): ")
    
    if response != "RESET":
        print("\n❌ Demo reset cancelled.")
        sys.exit(0)
    
    print("\n✅ Confirmation received. Starting demo reset...")
    return True


def clear_demo_data(db):
    """Clear existing demo data while preserving core demo accounts."""
    print("\n🧹 Clearing existing demo data...")
    
    try:
        # 1. Delete clinical notes (no user dependencies) - FIRST to avoid FK constraints
        count = db.query(ClinicalNote).count()
        deleted = db.query(ClinicalNote).delete(synchronize_session=False)
        db.commit()  # Commit immediately to ensure deletion
        print(f"   ✓ Deleted {deleted}/{count} clinical notes")
        
        # 2. Delete chat messages - SECOND to avoid FK constraints
        count = db.query(ChatMessage).count()
        deleted = db.query(ChatMessage).delete(synchronize_session=False)
        db.commit()  # Commit immediately to ensure deletion
        print(f"   ✓ Deleted {deleted}/{count} chat messages")
        
        # 3. Delete appointments
        count = db.query(Appointment).count()
        deleted = db.query(Appointment).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} appointments")
        
        # 4. Delete appointment requests
        count = db.query(AppointmentRequest).count()
        deleted = db.query(AppointmentRequest).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} appointment requests")
        
        # 5. Delete side effect reports
        count = db.query(SideEffectReport).count()
        deleted = db.query(SideEffectReport).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} side effect reports")
        
        # 6. Delete weight logs
        count = db.query(WeightLog).count()
        deleted = db.query(WeightLog).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} weight logs")
        
        # 7. Delete medication requests
        count = db.query(MedicationRequest).count()
        deleted = db.query(MedicationRequest).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} medication requests")
        
        # 8. Delete patient education assignments
        count = db.query(PatientEducationAssignment).count()
        deleted = db.query(PatientEducationAssignment).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} education assignments")
        
        # 9. Delete event logs
        count = db.query(EventLog).count()
        deleted = db.query(EventLog).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} event logs")
        
        # 10. Delete patient alerts
        count = db.query(PatientAlert).count()
        deleted = db.query(PatientAlert).delete(synchronize_session=False)
        db.commit()
        print(f"   ✓ Deleted {deleted}/{count} patient alerts")
        
        # 11. Clear clinician-patient associations for non-demo patients
        demo_clerk_ids = [DEMO_USER_IDS['patient']] + DEMO_PATIENT_IDS
        result = db.execute(text("""
            DELETE FROM clinician_patient_association 
            WHERE patient_id NOT IN :patient_ids
        """), {"patient_ids": tuple(demo_clerk_ids)})
        print(f"   ✓ Cleared {result.rowcount} clinician-patient associations")
        
        # 12. Delete all patients except demo ones
        count = db.query(Patient).filter(
            ~Patient.id.in_(demo_clerk_ids)
        ).count()
        db.query(Patient).filter(
            ~Patient.id.in_(demo_clerk_ids)
        ).delete(synchronize_session=False)
        print(f"   ✓ Deleted {count} non-demo patients")
        
        # 13. Clear clinician-patient associations except for demo clinician-patient pair
        result = db.execute(text("""
            DELETE FROM clinician_patient_association 
            WHERE NOT (
                clinician_id = :demo_clinician_id 
                AND patient_id = :demo_patient_id
            )
        """), {
            "demo_clinician_id": DEMO_USER_IDS['clinician'],
            "demo_patient_id": DEMO_USER_IDS['patient']
        })
        print(f"   ✓ Cleared {result.rowcount} non-demo clinician-patient associations")
        
        # 14. Ensure demo clinician-patient association exists
        # Check if association already exists
        exists = db.execute(text("""
            SELECT 1 FROM clinician_patient_association 
            WHERE clinician_id = :clinician_id AND patient_id = :patient_id
        """), {
            "clinician_id": DEMO_USER_IDS['clinician'],
            "patient_id": DEMO_USER_IDS['patient']
        }).scalar()
        
        if not exists:
            db.execute(text("""
                INSERT INTO clinician_patient_association (clinician_id, patient_id)
                VALUES (:clinician_id, :patient_id)
            """), {
                "clinician_id": DEMO_USER_IDS['clinician'],
                "patient_id": DEMO_USER_IDS['patient']
            })
            print("   ✓ Created demo clinician-patient association")
        else:
            print("   ✓ Demo clinician-patient association already exists")
        
        # Final commit for association table operations
        db.commit()
        print("\n✅ Demo data cleared successfully!")
        
        # Verify deletion counts
        print("\n🔍 Verifying deletion results:")
        print(f"   Clinical Notes remaining: {db.query(ClinicalNote).count()}")
        print(f"   Chat Messages remaining: {db.query(ChatMessage).count()}")
        print(f"   Appointments remaining: {db.query(Appointment).count()}")
        print(f"   Event Logs remaining: {db.query(EventLog).count()}")
        
    except Exception as e:
        db.rollback()
        print(f"\n❌ Error clearing demo data: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


def reset_demo_timestamps(db):
    """Update all timestamps to be recent for a fresh demo experience."""
    print("\n🕐 Resetting timestamps to recent dates...")
    
    try:
        now = datetime.utcnow()
        
        # Update patient created_at to stagger over last 3 months
        patients = db.query(Patient).all()
        for i, patient in enumerate(patients):
            days_ago = random.randint(30, 90)
            patient.created_at = now - timedelta(days=days_ago)
            patient.updated_at = patient.created_at
        
        print(f"   ✓ Updated timestamps for {len(patients)} patients")
        
        # Update clinician timestamps
        clinicians = db.query(Clinician).all()
        for clinician in clinicians:
            clinician.created_at = now - timedelta(days=180)  # 6 months ago
            clinician.updated_at = now - timedelta(days=1)
        
        print(f"   ✓ Updated timestamps for {len(clinicians)} clinicians")
        
        db.commit()
        
    except Exception as e:
        db.rollback()
        print(f"\n❌ Error resetting timestamps: {str(e)}")
        raise


def update_michael_patient_attributes(db):
    """Update Michael Patient's specific attributes for the demo."""
    print("\n👤 Updating Michael Patient attributes...")
    
    # Get Michael Patient
    michael_patient = db.query(Patient).filter_by(id=DEMO_USER_IDS['patient']).first()
    
    if michael_patient:
        # Update with specific demo values
        michael_patient.date_of_birth = datetime(1969, 4, 12).date()  # April 12, 1969
        michael_patient.phone_number = "************"
        michael_patient.height_cm = 107.0  # 107 cm height
        
        db.commit()
        print(f"   ✓ Updated Michael Patient:")
        print(f"     - Date of Birth: April 12, 1969")
        print(f"     - Phone: ************")
        print(f"     - Height: 107 cm")
    else:
        print("   ⚠️  Michael Patient not found, skipping attribute update")


def add_enhanced_demo_activity(db):
    """Add realistic demo activity including pending medication requests, patient messages, and appointment requests."""
    print("   📋 Adding pending medication requests...")
    
    # Get all demo patients
    demo_clerk_ids = [DEMO_USER_IDS['patient']] + DEMO_PATIENT_IDS
    patients = db.query(Patient).filter(Patient.id.in_(demo_clerk_ids)).all()
    demo_clinician = db.query(Clinician).filter_by(clerk_id=DEMO_USER_IDS['clinician']).first()
    
    if not patients or not demo_clinician:
        print("   ⚠️  Demo patients or clinician not found, skipping enhanced activity")
        return
    
    # 1. Add pending medication requests
    pending_medication_requests = [
        {
            "patient": patients[0] if len(patients) > 0 else None,
            "medication_name": "Ozempic (Semaglutide)",
            "dosage": "1mg",
            "frequency": "Weekly injection",
            "duration": "3 months",
            "notes": "Despite concerning rapid weight loss (37 lbs in 30 days), patient requesting dose increase from 0.5mg to 1mg. Reports feeling 'great' and wants 'faster results'. Has not disclosed full symptom profile during recent conversations.",
            "days_ago": 2
        },
        {
            "patient": patients[1] if len(patients) > 1 else patients[0],
            "medication_name": "Mounjaro (Tirzepatide)",
            "dosage": "5mg",
            "frequency": "Weekly injection",
            "duration": "3 months",
            "notes": "Side effects from current GLP-1 medication. Patient interested in switching to dual GIP/GLP-1 agonist for better efficacy and potentially fewer side effects.",
            "days_ago": 1
        },
        {
            "patient": patients[2] if len(patients) > 2 else patients[0],
            "medication_name": "Saxenda (Liraglutide)",
            "dosage": "3mg",
            "frequency": "Daily injection",
            "duration": "2 months",
            "notes": "New patient requesting prescription after consultation. Has tried lifestyle modifications for 6 months with minimal success.",
            "days_ago": 3
        }
    ]
    
    for req_data in pending_medication_requests:
        if req_data["patient"]:
            created_time = datetime.utcnow() - timedelta(days=req_data["days_ago"])
            med_request = MedicationRequest(
                patient_id=req_data["patient"].id,
                medication_name=req_data["medication_name"],
                dosage=req_data["dosage"],
                frequency=req_data["frequency"],
                duration=req_data["duration"],
                notes=req_data["notes"],
                status=MedicationRequestStatus.PENDING,
                clinician_id=demo_clinician.clerk_id,
                created_at=created_time
            )
            db.add(med_request)
            
            # Create corresponding EventLog entry for dashboard activity
            event_log = EventLog(
                actor_user_id=req_data["patient"].id,
                actor_role="patient",
                action="CREATE_MEDICATION_REQUEST",
                target_resource_type="medication_request",
                target_resource_id=f"pending_med_req_{req_data['medication_name'].replace(' ', '_')}",
                outcome="success",
                details={
                    "medication_name": req_data["medication_name"],
                    "dosage": req_data["dosage"],
                    "frequency": req_data["frequency"],
                    "status": "pending"
                },
                clinic_id=None,
                created_at=created_time
            )
            db.add(event_log)
    
    print(f"   ✓ Added {len(pending_medication_requests)} pending medication requests")
    
    # 2. Add Michael patient symptom messages to clinician
    print("   💬 Adding Michael patient symptom messages...")
    
    michael_patient = db.query(Patient).filter_by(id=DEMO_USER_IDS['patient']).first()
    if michael_patient:
        michael_symptom_messages = [
            {
                "content": "Hi Dr. Isaac, I wanted to update you on how I'm feeling this week. I've been experiencing some mild nausea in the mornings, usually about 30 minutes after taking my Ozempic injection. It typically lasts for about 2-3 hours. I'm also feeling a bit more tired than usual. Should I be concerned? The nausea isn't severe enough to make me vomit, but it's definitely noticeable.",
                "days_ago": 2,
                "hours_ago": 14
            },
            {
                "content": "Following up on my previous message about the nausea - I tried taking my injection in the evening instead of morning like you suggested, and it seems to help! The nausea is much less noticeable when I'm sleeping through the worst of it. My energy levels are also improving. Thanks for the advice!",
                "days_ago": 1,
                "hours_ago": 10
            },
            {
                "content": "Dr. Isaac, I have my weigh-in tomorrow but wanted to give you a heads up - I'm down another 3.2 lbs this week! That brings my total to 37 lbs lost in 8 weeks. I'm feeling amazing and my energy is through the roof. The injection site rotation you taught me is working well too. Looking forward to our appointment tomorrow to discuss next steps.",
                "days_ago": 0,
                "hours_ago": 6
            }
        ]
        
        for msg_data in michael_symptom_messages:
            created_time = datetime.utcnow() - timedelta(days=msg_data["days_ago"], hours=msg_data["hours_ago"])
            message = ChatMessage(
                patient_id=michael_patient.id,
                sender_type=MessageSenderType.PATIENT,
                message_content=msg_data["content"],
                message_route=MessageRouteType.CLINICIAN,
                is_read_by_clinician=False,
                created_at=created_time
            )
            db.add(message)
            
            # Create corresponding EventLog entry for dashboard activity
            event_log = EventLog(
                actor_user_id=michael_patient.id,
                actor_role="patient",
                action="SEND_MESSAGE_TO_CLINICIAN",
                target_resource_type="chat_message",
                target_resource_id=f"michael_message_{msg_data['days_ago']}d_{msg_data['hours_ago']}h",
                outcome="success",
                details={
                    "message_preview": msg_data["content"][:100] + "..." if len(msg_data["content"]) > 100 else msg_data["content"],
                    "route": "clinician",
                    "sender_type": "patient"
                },
                clinic_id=None,
                created_at=created_time
            )
            db.add(event_log)
        
        print(f"   ✓ Added {len(michael_symptom_messages)} symptom messages from Michael")
    
    # 3. Add multiple appointment requests from patients
    print("   📅 Adding appointment requests from patients...")
    
    appointment_requests = [
        {
            "patient": patients[1] if len(patients) > 1 else patients[0],
            "preferred_datetime": datetime.utcnow() + timedelta(days=5, hours=10),  # Next Friday 10 AM
            "reason": "Follow-up appointment for medication adjustment. Experiencing some side effects with current GLP-1 medication and would like to discuss switching to Mounjaro as discussed in my medication request.",
            "clinician_preference": "Dr. Michael Isaac",
            "days_ago": 2
        },
        {
            "patient": patients[2] if len(patients) > 2 else patients[0],
            "preferred_datetime": datetime.utcnow() + timedelta(days=7, hours=14),  # Next Sunday 2 PM
            "reason": "Initial consultation for weight management program. I've reviewed the educational materials and I'm ready to start my journey. Would prefer afternoon appointment if possible.",
            "clinician_preference": "Any available clinician",
            "days_ago": 3
        },
        {
            "patient": patients[3] if len(patients) > 3 else patients[0],
            "preferred_datetime": datetime.utcnow() + timedelta(days=4, hours=9),  # Next Thursday 9 AM
            "reason": "Urgent: Experiencing more severe side effects than expected. Nausea, fatigue, and some dizziness. Need to discuss medication dosage adjustment or alternative treatment options.",
            "clinician_preference": "Dr. Michael Isaac",
            "days_ago": 1
        },
        {
            "patient": patients[0] if len(patients) > 0 else None,
            "preferred_datetime": datetime.utcnow() + timedelta(days=8, hours=11),  # Next Monday 11 AM  
            "reason": "Regular check-in appointment. Weight loss progress has been excellent (37 lbs in 8 weeks!). Want to discuss transitioning to maintenance phase and long-term strategy.",
            "clinician_preference": "Dr. Michael Isaac",
            "days_ago": 0
        }
    ]
    
    for req_data in appointment_requests:
        if req_data["patient"]:
            created_time = datetime.utcnow() - timedelta(days=req_data["days_ago"])
            appt_request = AppointmentRequest(
                patient_id=req_data["patient"].id,
                preferred_datetime=req_data["preferred_datetime"],
                reason=req_data["reason"],
                clinician_preference=req_data["clinician_preference"],
                status="pending",
                created_at=created_time
            )
            db.add(appt_request)
            
            # Create corresponding EventLog entry for dashboard activity
            event_log = EventLog(
                actor_user_id=req_data["patient"].id,
                actor_role="patient",
                action="CREATE_APPOINTMENT_REQUEST",
                target_resource_type="appointment_request",
                target_resource_id=f"appt_req_{req_data['patient'].id[-8:]}_{req_data['days_ago']}d",
                outcome="success",
                details={
                    "reason_preview": req_data["reason"][:100] + "..." if len(req_data["reason"]) > 100 else req_data["reason"],
                    "preferred_datetime": req_data["preferred_datetime"].isoformat(),
                    "clinician_preference": req_data["clinician_preference"],
                    "status": "pending"
                },
                clinic_id=None,
                created_at=created_time
            )
            db.add(event_log)
    
    print(f"   ✓ Added {len(appointment_requests)} appointment requests")
    
    # 4. Add realistic activity patterns matching current demo environment
    print("   🎯 Adding realistic activity patterns...")
    
    # Clean up existing AI messages that have wrong routing
    print("   🧹 Cleaning up incorrect AI message routing...")
    
    # Fix AI messages that have incorrect routing
    # 1. AI messages routed to AI (nonsensical)
    ai_to_ai_messages = db.query(ChatMessage).filter(
        ChatMessage.sender_type == MessageSenderType.AGENT,
        ChatMessage.message_route == MessageRouteType.AI
    ).all()
    
    for msg in ai_to_ai_messages:
        # AI responses should always go back to the patient
        msg.message_route = MessageRouteType.PATIENT
        msg.is_read_by_clinician = True  # AI responses are automatically "read" by clinician
    
    # 2. AI messages routed to clinician (should go to patient unless it's a response to clinician's question)
    ai_to_clinician_messages = db.query(ChatMessage).filter(
        ChatMessage.sender_type == MessageSenderType.AGENT,
        ChatMessage.message_route == MessageRouteType.CLINICIAN
    ).all()
    
    # Most AI messages should go to patients, not clinicians
    # Only keep AI→CLINICIAN if it's immediately after a CLINICIAN→AI message
    for msg in ai_to_clinician_messages:
        # Check if there's a recent clinician→AI message before this
        prior_clinician_msg = db.query(ChatMessage).filter(
            ChatMessage.patient_id == msg.patient_id,
            ChatMessage.sender_type == MessageSenderType.CLINICIAN,
            ChatMessage.message_route == MessageRouteType.AI,
            ChatMessage.created_at < msg.created_at,
            ChatMessage.created_at >= msg.created_at - timedelta(hours=1)  # Within 1 hour
        ).first()
        
        if not prior_clinician_msg:
            # No recent clinician question, so this AI response should go to patient
            msg.message_route = MessageRouteType.PATIENT
            msg.is_read_by_clinician = True
    
    print(f"   ✓ Fixed {len(ai_to_ai_messages)} AI→AI and {len(ai_to_clinician_messages)} AI→CLINICIAN routing issues")
    
    # Ensure Michael's recent patient-to-clinician messages are UNREAD (for red dot)
    print("   💬 Setting correct read status for Michael's messages...")
    
    michael_patient = db.query(Patient).filter_by(id=DEMO_USER_IDS['patient']).first()
    if michael_patient:
        # Get Michael's recent patient-to-clinician messages
        recent_michael_messages = db.query(ChatMessage).filter(
            ChatMessage.patient_id == michael_patient.id,
            ChatMessage.sender_type == MessageSenderType.PATIENT,
            ChatMessage.message_route == MessageRouteType.CLINICIAN,
            ChatMessage.created_at >= datetime.utcnow() - timedelta(days=7)  # Last week
        ).all()
        
        for msg in recent_michael_messages:
            msg.is_read_by_clinician = False  # These should be UNREAD to show red dot
            msg.read_by_clinician_at = None
        
        print(f"   ✓ Set {len(recent_michael_messages)} Michael messages as UNREAD")
    
    # Ensure other patients' older messages are marked as READ for cleaner demo
    print("   📖 Setting older messages as read for cleaner demo...")
    
    older_messages = db.query(ChatMessage).filter(
        ChatMessage.patient_id != DEMO_USER_IDS['patient'],  # Not Michael
        ChatMessage.sender_type == MessageSenderType.PATIENT,
        ChatMessage.message_route == MessageRouteType.CLINICIAN,
        ChatMessage.created_at < datetime.utcnow() - timedelta(days=2)  # Older than 2 days
    ).all()
    
    for msg in older_messages:
        msg.is_read_by_clinician = True
        msg.read_by_clinician_at = msg.created_at + timedelta(hours=2)
    
    print(f"   ✓ Marked {len(older_messages)} older messages as read")
    
    print("   ✓ Added realistic AI conversation patterns")
    
    # Add some approved/rejected medication requests for realistic mix
    if patients:
        approved_created_time = datetime.utcnow() - timedelta(days=15)
        approved_resolved_time = datetime.utcnow() - timedelta(days=14)
        
        approved_med_request = MedicationRequest(
            patient_id=patients[0].id,
            medication_name="Ozempic (Semaglutide)",
            dosage="0.5mg",
            frequency="Weekly injection",
            duration="3 months initial",
            notes="Initial prescription approved. Patient has met all criteria for GLP-1 therapy. Start with 0.25mg for 4 weeks, then increase to 0.5mg as tolerated.",
            status=MedicationRequestStatus.APPROVED,
            clinician_id=demo_clinician.clerk_id,
            resolved_at=approved_resolved_time,
            created_at=approved_created_time
        )
        db.add(approved_med_request)
        
        # Create EventLog for the approval action
        approval_event_log = EventLog(
            actor_user_id=demo_clinician.clerk_id,
            actor_role="clinician",
            action="APPROVE_MEDICATION_REQUEST",
            target_resource_type="medication_request",
            target_resource_id="approved_ozempic_initial",
            outcome="success",
            details={
                "patient_id": patients[0].id,
                "medication_name": "Ozempic (Semaglutide)",
                "dosage": "0.5mg",
                "status": "approved",
                "approval_notes": "Initial prescription approved. Patient meets all criteria."
            },
            clinic_id=None,
            created_at=approved_resolved_time
        )
        db.add(approval_event_log)
    
    print("   ✓ Added historical medication request for context")


def verify_demo_state(db):
    """Verify the demo environment is in the correct state."""
    print("\n🔍 Verifying demo state...")
    
    # Check core demo accounts exist
    clinician = db.query(Clinician).filter_by(clerk_id=DEMO_USER_IDS['clinician']).first()
    if not clinician:
        print("   ❌ Demo clinician not found!")
        return False
    print(f"   ✓ Demo clinician found: {clinician.first_name} {clinician.last_name}")
    
    patient = db.query(Patient).filter_by(id=DEMO_USER_IDS['patient']).first()
    if not patient:
        print("   ❌ Demo patient not found!")
        return False
    print(f"   ✓ Demo patient found: {patient.first_name} {patient.last_name}")
    
    clinic = db.query(Clinic).filter_by(id=DEMO_USER_IDS['clinic']).first()
    if not clinic:
        print("   ❌ Demo clinic not found!")
        return False
    print(f"   ✓ Demo clinic found: {clinic.name}")
    
    # Check data counts
    print("\n📊 Data Summary:")
    print(f"   Patients: {db.query(Patient).count()}")
    print(f"   Appointments: {db.query(Appointment).count()}")
    print(f"   Appointment Requests: {db.query(AppointmentRequest).count()}")
    print(f"   Chat Messages: {db.query(ChatMessage).count()}")
    print(f"   Medication Requests: {db.query(MedicationRequest).count()}")
    print(f"   - Pending: {db.query(MedicationRequest).filter_by(status=MedicationRequestStatus.PENDING).count()}")
    print(f"   Weight Logs: {db.query(WeightLog).count()}")
    print(f"   Side Effect Reports: {db.query(SideEffectReport).count()}")
    print(f"   Clinical Notes: {db.query(ClinicalNote).count()}")
    
    return True


def main():
    """Main demo reset function."""
    print("\n🚀 PulseTrack Demo Reset Tool")
    print("   Version 1.0")
    print("   " + "="*40)
    
    # Confirm environment
    if not confirm_environment():
        return
    
    # Reset the appointment scheduler for clean slate
    reset_scheduler()
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Step 1: Clear existing demo data
        clear_demo_data(db)
        
        # Step 2: Reset timestamps
        reset_demo_timestamps(db)
        
        # Step 3: Reseed demo data
        print("\n🌱 Reseeding demo data...")
        print("   Running seed_demo_data.py...")
        
        # Close current session to avoid conflicts
        db.close()
        
        # Run the seeding scripts directly
        print("\n   Seeding demo data...")
        db = SessionLocal()
        try:
            seeder = DemoDataSeeder(db)
            seeder.seed_all()
            print("   ✓ Demo data seeded successfully")
        except Exception as e:
            print(f"❌ Error seeding demo data: {e}")
            db.rollback()
            raise
        finally:
            db.close()
        
        # Add investor demo scenario
        print("\n   Adding investor demo scenario...")
        seed_investor_demo_scenario()  # This has its own DB session handling
        
        # Update Michael Patient attributes
        print("\n   Updating Michael Patient attributes...")
        db = SessionLocal()
        try:
            update_michael_patient_attributes(db)
        except Exception as e:
            db.rollback()
            print(f"❌ Error updating Michael Patient attributes: {e}")
            raise
        finally:
            db.close()
        
        # Add additional demo patients
        print("\n   Adding additional demo patients...")
        db = SessionLocal()
        try:
            seed_additional_patients(db, DEMO_USER_IDS['clinician'])
            db.commit()
        except Exception as e:
            db.rollback()
            print(f"❌ Error adding additional patients: {e}")
            raise
        finally:
            db.close()
        
        # Step 3.5: Add enhanced demo activity
        print("\n   Adding enhanced demo activity...")
        db = SessionLocal()
        try:
            add_enhanced_demo_activity(db)
            db.commit()
            print("   ✓ Enhanced demo activity added successfully")
        except Exception as e:
            db.rollback()
            print(f"❌ Error adding enhanced demo activity: {e}")
            raise
        finally:
            db.close()
        
        # Step 4: Verify final state
        db = SessionLocal()  # New session for verification
        if verify_demo_state(db):
            # Get appointment schedule summary
            from scripts.shared_appointment_scheduler import get_shared_scheduler
            scheduler = get_shared_scheduler()
            schedule_summary = scheduler.get_schedule_summary()
            
            print("\n" + "="*60)
            print("✅ DEMO RESET COMPLETE!")
            print("="*60)
            print("\n🎯 Ready for investor demonstration:")
            print(f"   • Clinician login: Dr. Michael Isaac")
            print(f"   • Critical patient: Michael Patient (37 lbs in 30 days)")
            print(f"   • {db.query(Patient).count()} patients with diverse scenarios")
            print(f"   • {db.query(ChatMessage).count()} realistic conversations")
            print(f"   • {db.query(Appointment).count()} appointments scheduled")
            print(f"   • Fresh timestamps for live demo")
            
            print("\n📅 Appointment Calendar:")
            print(f"   • Total appointments: {schedule_summary['total_appointments']}")
            if schedule_summary['by_weekday']:
                print("   • Distribution by weekday:")
                for day, count in sorted(schedule_summary['by_weekday'].items()):
                    print(f"     - {day}: {count} appointments")
            
            print("\n🔗 Access Points:")
            print(f"   • Clinician Dashboard: http://localhost:5174")
            print(f"   • Patient Dashboard: http://localhost:5173")
            print(f"   • Admin Panel: http://localhost:5175")
            print("\n💡 Tip: Run this script between each demo for consistency!")
        else:
            print("\n⚠️  Demo reset completed with warnings. Please verify manually.")
            
    except Exception as e:
        print(f"\n❌ Demo reset failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        db.close()


if __name__ == "__main__":
    main()