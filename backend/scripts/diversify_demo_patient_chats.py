#!/usr/bin/env python3
"""Diversify demo patient chat messages to show different health journeys."""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import uuid

sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.session import SessionLocal

# Different conversation patterns for each patient
PATIENT_CONVERSATIONS = {
    "Sarah": [
        # Sarah - struggling with emotional eating
        {
            "patient": "Dr. <PERSON>, I'm having a really tough time with stress eating lately. Work has been overwhelming.",
            "clinician": "I understand. Stress can definitely trigger eating behaviors. Have you tried any of the mindfulness techniques we discussed?",
            "offset": 0
        },
        {
            "patient": "I tried the breathing exercises but ended up ordering takeout anyway 😔",
            "agent": "It's okay to have setbacks. Here are some strategies for managing stress eating:\n\n1. Keep healthy snacks readily available\n2. Practice the 5-minute pause before eating\n3. Journal your feelings instead of eating\n4. Call a friend or go for a walk\n\nRemember, progress isn't always linear!",
            "offset": 30
        },
        {
            "patient": "Actually tried journaling yesterday and it helped! Didn't realize how anxious I was about the project deadline.",
            "clinician": "That's excellent self-awareness! Journaling can be a powerful tool. Keep it up, and remember I'm here if you need support.",
            "offset": 180
        },
        {
            "patient": "Is it normal to have more cravings in the evening? That's when I struggle most.",
            "agent": "Evening cravings are very common! This can be due to:\n- Lower willpower after a long day\n- Boredom or habit\n- Not eating enough during the day\n\nTry having a protein-rich afternoon snack and planning an evening activity like a bath or hobby.",
            "offset": 300
        }
    ],
    "Robert": [
        # Robert - athletic, focused on performance
        {
            "patient": "Hey Dr. Isaac, I've been crushing it at the gym! Down 5 pounds and my running times are improving.",
            "clinician": "That's fantastic, Robert! Your athletic approach is really paying off. How's your energy during workouts?",
            "offset": 0
        },
        {
            "patient": "Energy is good but I'm wondering about pre-workout nutrition. What do you recommend?",
            "agent": "For optimal performance on Wegovy:\n\n- Eat 2-3 hours before exercise\n- Focus on easily digestible carbs + some protein\n- Stay well hydrated\n- Consider timing your injection away from heavy training days\n\nGreat options: banana with almond butter, oatmeal, or whole grain toast.",
            "offset": 45
        },
        {
            "patient": "Noticed some muscle soreness lasting longer than usual. Related to the medication?",
            "clinician": "Good observation. Make sure you're getting adequate protein (aim for 0.8-1g per pound of body weight) and staying hydrated. The medication shouldn't directly cause this, but rapid weight loss can affect recovery.",
            "offset": 120
        },
        {
            "patient": "Training for a 10K next month. Should I adjust my dosing schedule?",
            "agent": "Congratulations on the 10K! Most patients don't need to adjust their Wegovy schedule for races. Key tips:\n- Maintain your regular injection day\n- Test your race-day nutrition during training\n- Stay extra hydrated the week before\n- Don't try anything new on race day\n\nGood luck! 🏃‍♂️",
            "offset": 250
        }
    ],
    "Emily": [
        # Emily - dealing with social situations and family pressure
        {
            "patient": "Hi Dr. Isaac, family dinner was challenging. My mom kept pushing food on me and got offended when I said no.",
            "clinician": "Family dynamics around food can be tricky. Have you considered having a private conversation with your mom about your health journey?",
            "offset": 0
        },
        {
            "patient": "I tried but she says I'm 'getting too thin' even though I'm still in the overweight category 🙄",
            "agent": "Dealing with family food pressure is common. Try these responses:\n- 'I'm following my doctor's advice'\n- 'I'm full but it was delicious'\n- 'I'd love to take some home for later'\n\nRemember, you're making these choices for YOUR health. Stay strong! 💪",
            "offset": 60
        },
        {
            "patient": "Going to my sister's birthday party this weekend. Anxious about all the food there.",
            "clinician": "It's great you're thinking ahead. Remember, special occasions are part of life. Enjoy in moderation and get back on track the next day. No guilt!",
            "offset": 150
        },
        {
            "patient": "Update: Party went well! Had a small slice of cake and focused on socializing instead of food. Feeling proud!",
            "agent": "That's amazing! You've developed such a healthy relationship with food. Celebrating without overindulging is a huge victory! 🎉 Keep using these strategies - they're clearly working for you.",
            "offset": 240
        }
    ],
    "James": [
        # James - dealing with medication side effects but pushing through
        {
            "patient": "Dr. Isaac, the nausea is pretty rough this week. Worse than before. Should I be concerned?",
            "clinician": "Sorry to hear that. Sometimes nausea can fluctuate. Are you eating before your injection? Taking it at the same time each week?",
            "offset": 0
        },
        {
            "patient": "Yes to both. It's mainly in the mornings. Having trouble with breakfast.",
            "agent": "Morning nausea tips:\n- Keep crackers by your bedside\n- Try ginger tea first thing\n- Start with bland foods (toast, rice)\n- Eat smaller amounts more frequently\n- Consider taking your injection in the evening\n\nIf it persists beyond 48 hours after injection, let Dr. Isaac know.",
            "offset": 40
        },
        {
            "patient": "Ginger tea is helping! Also switched to evening injections. Much better. Question - I'm getting some heartburn too.",
            "clinician": "Glad the ginger helps! For heartburn: avoid lying down after eating, limit spicy/acidic foods, and consider an OTC antacid. If it continues, we can discuss prescription options.",
            "offset": 120
        },
        {
            "patient": "Despite the side effects, I'm down 8 pounds! Clothes fitting better. Worth it!",
            "agent": "Your positive attitude is inspiring! 8 pounds is excellent progress. Many patients find side effects decrease over time as your body adjusts. You're doing great - keep pushing forward! 💪",
            "offset": 200
        }
    ]
}

def main():
    db = SessionLocal()
    
    try:
        # First, delete existing chat messages for demo patients
        print("Removing old identical chat messages...")
        db.execute(
            text("""
                DELETE FROM chat_messages 
                WHERE patient_id IN (
                    SELECT id FROM patients 
                    WHERE first_name IN ('Sarah', 'Robert', 'Emily', 'James')
                )
            """)
        )
        db.commit()
        
        # Get demo patients
        patients = db.execute(
            text("""
                SELECT id, first_name FROM patients 
                WHERE first_name IN ('Sarah', 'Robert', 'Emily', 'James')
            """)
        ).fetchall()
        
        clinician_id = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
        base_time = datetime.now() - timedelta(days=20)
        
        for patient_id, first_name in patients:
            print(f"\nCreating unique conversations for {first_name}...")
            conversations = PATIENT_CONVERSATIONS.get(first_name, [])
            
            for conv in conversations:
                msg_time = base_time + timedelta(minutes=conv["offset"])
                
                # Patient message
                if "patient" in conv:
                    db.execute(
                        text("""
                            INSERT INTO chat_messages 
                            (id, patient_id, sender_type, message_content, 
                             created_at, updated_at, is_read_by_clinician, message_route)
                            VALUES 
                            (:id, :patient_id, 'PATIENT', :content, 
                             :created_at, :updated_at, true, 'clinician')
                        """),
                        {
                            "id": str(uuid.uuid4()),
                            "patient_id": patient_id,
                            "content": conv["patient"],
                            "created_at": msg_time,
                            "updated_at": msg_time
                        }
                    )
                
                # Response (either from clinician or agent)
                response_time = msg_time + timedelta(minutes=2)
                if "clinician" in conv:
                    db.execute(
                        text("""
                            INSERT INTO chat_messages 
                            (id, patient_id, sender_type, message_content, 
                             created_at, updated_at, is_read_by_clinician, message_route)
                            VALUES 
                            (:id, :patient_id, 'CLINICIAN', :content, 
                             :created_at, :updated_at, true, 'clinician')
                        """),
                        {
                            "id": str(uuid.uuid4()),
                            "patient_id": patient_id,
                            "content": conv["clinician"],
                            "created_at": response_time,
                            "updated_at": response_time
                        }
                    )
                elif "agent" in conv:
                    db.execute(
                        text("""
                            INSERT INTO chat_messages 
                            (id, patient_id, sender_type, message_content, 
                             created_at, updated_at, is_read_by_clinician, message_route,
                             message_metadata)
                            VALUES 
                            (:id, :patient_id, 'AGENT', :content, 
                             :created_at, :updated_at, false, 'ai',
                             :metadata)
                        """),
                        {
                            "id": str(uuid.uuid4()),
                            "patient_id": patient_id,
                            "content": conv["agent"],
                            "created_at": response_time,
                            "updated_at": response_time,
                            "metadata": '{"ai_response": true}'
                        }
                    )
        
        db.commit()
        print("\n✅ Successfully created diverse conversations for all demo patients!")
        
        # Show summary
        for patient_id, first_name in patients:
            count = db.execute(
                text("""
                    SELECT COUNT(*) FROM chat_messages 
                    WHERE patient_id = :patient_id
                """),
                {"patient_id": patient_id}
            ).scalar()
            print(f"{first_name}: {count} messages")
            
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()