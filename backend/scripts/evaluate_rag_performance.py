"""Script to evaluate RAG system performance and generate metrics."""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.crud.crud_clinic import crud_clinic
from app.crud.crud_content_chunk import crud_content_chunk
from app.crud.crud_scraped_page import scraped_page as crud_scraped_page
from app.services.embedding_pipeline import generate_embeddings
from app.utils.context_enricher import enrich_with_rag

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RAGEvaluator:
    """Evaluator for RAG system performance and quality."""

    def __init__(self, db_url: str):
        """Initialize the evaluator with database connection."""
        self.engine = create_async_engine(db_url)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

    async def evaluate_retrieval_quality(self) -> dict:
        """Evaluate the quality of RAG retrieval."""
        results = {
            "timestamp": datetime.now().isoformat(),
            "metrics": {},
            "test_queries": [],
        }

        test_queries = [
            {
                "query": "diabetes medication management guidelines",
                "expected_topics": ["diabetes", "medication", "glucose"],
            },
            {
                "query": "hypertension blood pressure monitoring",
                "expected_topics": ["hypertension", "blood pressure", "monitoring"],
            },
            {
                "query": "weight loss exercise recommendations",
                "expected_topics": ["weight", "exercise", "physical activity"],
            },
        ]

        async with self.async_session() as session:
            # Get a test clinic
            clinics = await crud_clinic.get_multi(session, limit=1)
            if not clinics:
                logger.error("No clinics found for testing")
                return results

            clinics[0]

            for test_case in test_queries:
                query = test_case["query"]
                expected_topics = test_case["expected_topics"]

                # Measure retrieval time
                start_time = time.time()
                context = enrich_with_rag(
                    db=session,
                    user_id=str(uuid.uuid4()),
                    message=query,
                    user_role="patient",
                )
                retrieval_time = time.time() - start_time

                # Evaluate retrieval quality
                retrieved_chunks = context.get("rag_context_chunks", [])
                relevance_score = self._calculate_relevance_score(
                    retrieved_chunks, expected_topics
                )

                query_result = {
                    "query": query,
                    "retrieval_time": retrieval_time,
                    "num_chunks_retrieved": len(retrieved_chunks),
                    "relevance_score": relevance_score,
                    "chunks": retrieved_chunks[:2],  # First 2 chunks for inspection
                }

                results["test_queries"].append(query_result)

        # Calculate aggregate metrics
        results["metrics"] = self._calculate_aggregate_metrics(results["test_queries"])
        return results

    def _calculate_relevance_score(
        self, chunks: list[str], expected_topics: list[str]
    ) -> float:
        """Calculate relevance score based on topic presence."""
        if not chunks:
            return 0.0

        total_score = 0.0
        for chunk in chunks:
            chunk_lower = chunk.lower()
            topic_count = sum(1 for topic in expected_topics if topic in chunk_lower)
            chunk_score = topic_count / len(expected_topics)
            total_score += chunk_score

        return total_score / len(chunks)

    def _calculate_aggregate_metrics(self, test_queries: list[dict]) -> dict:
        """Calculate aggregate metrics from test results."""
        if not test_queries:
            return {}

        retrieval_times = [q["retrieval_time"] for q in test_queries]
        relevance_scores = [q["relevance_score"] for q in test_queries]
        num_chunks = [q["num_chunks_retrieved"] for q in test_queries]

        return {
            "avg_retrieval_time": np.mean(retrieval_times),
            "max_retrieval_time": np.max(retrieval_times),
            "min_retrieval_time": np.min(retrieval_times),
            "avg_relevance_score": np.mean(relevance_scores),
            "avg_chunks_retrieved": np.mean(num_chunks),
            "retrieval_success_rate": sum(1 for n in num_chunks if n > 0)
            / len(num_chunks),
        }

    async def benchmark_embedding_generation(self) -> dict:
        """Benchmark embedding generation performance."""
        results = {
            "timestamp": datetime.now().isoformat(),
            "benchmarks": [],
        }

        # Test different text sizes
        test_sizes = [100, 500, 1000, 5000]  # Characters
        num_texts_per_batch = [1, 5, 10, 20]

        for size in test_sizes:
            for batch_size in num_texts_per_batch:
                # Generate test texts
                test_texts = [
                    f"Test text of approximately {size} characters. " * (size // 40)
                    for _ in range(batch_size)
                ]

                # Measure embedding generation time
                start_time = time.time()
                embeddings = generate_embeddings(test_texts)
                generation_time = time.time() - start_time

                benchmark = {
                    "text_size": size,
                    "batch_size": batch_size,
                    "generation_time": generation_time,
                    "time_per_text": generation_time / batch_size,
                    "embeddings_generated": len(embeddings) if embeddings else 0,
                }

                results["benchmarks"].append(benchmark)
                logger.info(f"Benchmark: {benchmark}")

        return results

    async def analyze_content_coverage(self) -> dict:
        """Analyze the coverage of content in the RAG system."""
        results = {
            "timestamp": datetime.now().isoformat(),
            "content_stats": {},
        }

        async with self.async_session() as session:
            # Get all clinics
            clinics = await crud_clinic.get_multi(session, limit=100)

            for clinic in clinics:
                # Get scraped pages for clinic
                pages = await crud_scraped_page.get_multi_by_clinic(
                    session, clinic_id=clinic.id, limit=1000
                )

                # Get content chunks for clinic
                total_chunks = 0
                total_chars = 0
                chunk_sizes = []

                for page in pages:
                    chunks = await crud_content_chunk.get_by_scraped_page(
                        session, scraped_page_id=page.id
                    )
                    total_chunks += len(chunks)

                    for chunk in chunks:
                        chunk_size = len(chunk.chunk_text)
                        total_chars += chunk_size
                        chunk_sizes.append(chunk_size)

                clinic_stats = {
                    "clinic_name": clinic.name,
                    "num_pages": len(pages),
                    "num_chunks": total_chunks,
                    "total_characters": total_chars,
                    "avg_chunk_size": np.mean(chunk_sizes) if chunk_sizes else 0,
                    "min_chunk_size": np.min(chunk_sizes) if chunk_sizes else 0,
                    "max_chunk_size": np.max(chunk_sizes) if chunk_sizes else 0,
                }

                results["content_stats"][str(clinic.id)] = clinic_stats

        return results

    async def generate_visualization(self, evaluation_results: dict):
        """Generate visualizations of RAG performance."""
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        # Plot 1: Retrieval Times
        if "test_queries" in evaluation_results:
            queries = [
                q["query"][:20] + "..." for q in evaluation_results["test_queries"]
            ]
            times = [q["retrieval_time"] for q in evaluation_results["test_queries"]]

            ax1.bar(queries, times)
            ax1.set_xlabel("Query")
            ax1.set_ylabel("Retrieval Time (s)")
            ax1.set_title("RAG Retrieval Times")
            ax1.tick_params(axis="x", rotation=45)

        # Plot 2: Relevance Scores
        if "test_queries" in evaluation_results:
            scores = [q["relevance_score"] for q in evaluation_results["test_queries"]]

            ax2.bar(queries, scores)
            ax2.set_xlabel("Query")
            ax2.set_ylabel("Relevance Score")
            ax2.set_title("Query Relevance Scores")
            ax2.tick_params(axis="x", rotation=45)
            ax2.set_ylim(0, 1)

        # Plot 3: Chunks Retrieved
        if "test_queries" in evaluation_results:
            chunks = [
                q["num_chunks_retrieved"] for q in evaluation_results["test_queries"]
            ]

            ax3.bar(queries, chunks)
            ax3.set_xlabel("Query")
            ax3.set_ylabel("Number of Chunks")
            ax3.set_title("Chunks Retrieved per Query")
            ax3.tick_params(axis="x", rotation=45)

        # Plot 4: Aggregate Metrics
        if "metrics" in evaluation_results:
            metrics = evaluation_results["metrics"]
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())

            # Normalize metrics for visualization
            normalized_values = []
            for name, value in zip(metric_names, metric_values):
                if "time" in name:
                    normalized_values.append(value * 10)  # Scale up time metrics
                elif "score" in name or "rate" in name:
                    normalized_values.append(value)  # Keep scores/rates as is
                else:
                    normalized_values.append(value / 10)  # Scale down other metrics

            ax4.bar(metric_names, normalized_values)
            ax4.set_xlabel("Metric")
            ax4.set_ylabel("Normalized Value")
            ax4.set_title("Aggregate Performance Metrics")
            ax4.tick_params(axis="x", rotation=45)

        plt.tight_layout()
        plt.savefig("rag_evaluation_results.png", dpi=300, bbox_inches="tight")
        plt.close()

    async def save_results(self, results: dict, filename: str):
        """Save evaluation results to JSON file."""
        with open(filename, "w") as f:
            json.dump(results, f, indent=2)
        logger.info(f"Results saved to {filename}")

    async def run_complete_evaluation(self):
        """Run complete RAG system evaluation."""
        logger.info("Starting RAG system evaluation...")

        # 1. Evaluate retrieval quality
        logger.info("Evaluating retrieval quality...")
        retrieval_results = await self.evaluate_retrieval_quality()

        # 2. Benchmark embedding generation
        logger.info("Benchmarking embedding generation...")
        embedding_results = await self.benchmark_embedding_generation()

        # 3. Analyze content coverage
        logger.info("Analyzing content coverage...")
        coverage_results = await self.analyze_content_coverage()

        # Combine all results
        complete_results = {
            "evaluation_timestamp": datetime.now().isoformat(),
            "retrieval_quality": retrieval_results,
            "embedding_benchmarks": embedding_results,
            "content_coverage": coverage_results,
        }

        # Save results
        await self.save_results(complete_results, "rag_evaluation_complete.json")

        # Generate visualizations
        await self.generate_visualization(retrieval_results)

        # Print summary
        print("\n=== RAG Evaluation Summary ===")
        if retrieval_results.get("metrics"):
            metrics = retrieval_results["metrics"]
            print(
                f"Average Retrieval Time: {metrics.get('avg_retrieval_time', 0):.3f}s"
            )
            print(
                f"Average Relevance Score: {metrics.get('avg_relevance_score', 0):.2f}"
            )
            print(
                f"Retrieval Success Rate: {metrics.get('retrieval_success_rate', 0):.1%}"
            )

        print("\n=== Content Coverage ===")
        for clinic_id, stats in coverage_results.get("content_stats", {}).items():
            print(f"Clinic: {stats['clinic_name']}")
            print(f"  Pages: {stats['num_pages']}")
            print(f"  Chunks: {stats['num_chunks']}")
            print(f"  Total Characters: {stats['total_characters']:,}")
            print()

        logger.info("RAG evaluation complete!")


async def main():
    """Main function to run the RAG evaluation."""
    # Use your actual database URL
    db_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")

    evaluator = RAGEvaluator(db_url)
    await evaluator.run_complete_evaluation()


if __name__ == "__main__":
    asyncio.run(main())
