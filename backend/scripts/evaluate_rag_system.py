"""
Comprehensive RAG System Evaluation Script

This script evaluates various aspects of the RAG (Retrieval-Augmented Generation) system:
1. Embedding pipeline performance
2. Retrieval accuracy and relevance
3. Context enrichment quality
4. End-to-end latency
5. Edge cases and error handling
"""

import asyncio
import json
import time
import uuid
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sentence_transformers import SentenceTransformer

from app.crud.crud_clinic import clinic as crud_clinic
from app.crud.crud_content_chunk import crud_content_chunk
from app.crud.crud_patient import patient as crud_patient
from app.db.session import SessionLocal
from app.services.chat_agent import process_chat_message
from app.services.embedding_pipeline import (
    CHUNK_OVERLAP,
    CHUNK_SIZE,
    MODEL_NAME,
    chunk_text,
    generate_embeddings,
)
from app.utils.context_enricher import (
    enrich_with_rag,
    format_context_for_prompt,
    get_clinic_context,
    get_patient_context,
)


class RAGEvaluator:
    """Evaluates the RAG system components and performance."""

    def __init__(self):
        self.db = SessionLocal()
        self.model = SentenceTransformer(MODEL_NAME)
        self.results = {
            "embedding_pipeline": {},
            "retrieval_quality": {},
            "context_enrichment": {},
            "performance_metrics": {},
            "edge_cases": {},
            "chat_integration": {},
        }
        self.test_queries = [
            "What are the side effects of GLP-1 medications?",
            "How do I schedule an appointment?",
            "Tell me about weight management strategies",
            "What dosage of semaglutide should I take?",
            "Is there a patient portal available?",
            "What insurance do you accept?",
            "Can I get a refill on my medication?",
            "What are your office hours?",
            "How do I report a side effect?",
            "Tell me about diabetes management",
        ]

    def evaluate_embedding_pipeline(self):
        """Evaluate the embedding pipeline configuration and performance."""
        print("\n=== Evaluating Embedding Pipeline ===")

        # Test different chunk sizes
        test_text = (
            """
        This is a sample medical text that discusses the efficacy of GLP-1 receptor agonists
        in managing type 2 diabetes and obesity. These medications work by mimicking the action
        of the glucagon-like peptide-1 hormone, which helps regulate blood sugar levels and
        appetite. Common side effects include nausea, vomiting, and diarrhea, though these
        typically improve over time. Patients should be monitored regularly for potential
        adverse reactions and dosage adjustments may be necessary based on individual response.
        """
            * 5
        )  # Make it longer

        chunk_sizes = [300, 500, 700, 1000]
        overlaps = [0, 50, 100, 150]

        chunking_results = []

        for size in chunk_sizes:
            for overlap in overlaps:
                chunks = chunk_text(test_text, size=size, overlap=overlap)
                chunking_results.append(
                    {
                        "chunk_size": size,
                        "overlap": overlap,
                        "num_chunks": len(chunks),
                        "avg_chunk_length": np.mean([len(c) for c in chunks]),
                    }
                )

        # Test embedding generation speed
        test_chunks = chunk_text(test_text, size=CHUNK_SIZE, overlap=CHUNK_OVERLAP)

        start_time = time.time()
        embeddings = generate_embeddings(test_chunks)
        embedding_time = time.time() - start_time

        self.results["embedding_pipeline"] = {
            "model_name": MODEL_NAME,
            "embedding_dimensions": len(embeddings[0]) if embeddings else 0,
            "current_chunk_size": CHUNK_SIZE,
            "current_overlap": CHUNK_OVERLAP,
            "chunking_analysis": chunking_results,
            "embedding_speed": {
                "num_chunks": len(test_chunks),
                "total_time": embedding_time,
                "time_per_chunk": (
                    embedding_time / len(test_chunks) if test_chunks else 0
                ),
            },
        }

        print(f"Model: {MODEL_NAME}")
        print(
            f"Embedding dimensions: {self.results['embedding_pipeline']['embedding_dimensions']}"
        )
        print(f"Current config: chunk_size={CHUNK_SIZE}, overlap={CHUNK_OVERLAP}")
        print(
            f"Embedding speed: {self.results['embedding_pipeline']['embedding_speed']['time_per_chunk']:.4f}s per chunk"
        )

    def evaluate_retrieval_quality(self):
        """Test retrieval quality with various queries."""
        print("\n=== Evaluating Retrieval Quality ===")

        # Get a test clinic with content
        clinics = crud_clinic.get_multi(self.db, limit=1)
        if not clinics:
            print("No clinics found for testing")
            return

        clinic = clinics[0]
        retrieval_results = []

        for query in self.test_queries:
            # Generate query embedding
            query_embedding = self.model.encode(query)

            # Time the retrieval
            start_time = time.time()
            chunks = crud_content_chunk.find_similar_content_chunks(
                self.db, query_embedding=query_embedding, clinic_id=clinic.id, limit=5
            )
            retrieval_time = time.time() - start_time

            # Calculate relevance scores (cosine similarity)
            chunk_scores = []
            for chunk in chunks:
                if chunk.embedding:
                    similarity = np.dot(query_embedding, chunk.embedding) / (
                        np.linalg.norm(query_embedding)
                        * np.linalg.norm(chunk.embedding)
                    )
                    chunk_scores.append(similarity)

            retrieval_results.append(
                {
                    "query": query,
                    "num_results": len(chunks),
                    "retrieval_time": retrieval_time,
                    "avg_similarity": np.mean(chunk_scores) if chunk_scores else 0,
                    "max_similarity": np.max(chunk_scores) if chunk_scores else 0,
                    "min_similarity": np.min(chunk_scores) if chunk_scores else 0,
                }
            )

        self.results["retrieval_quality"] = {
            "test_queries": retrieval_results,
            "avg_retrieval_time": np.mean(
                [r["retrieval_time"] for r in retrieval_results]
            ),
            "avg_similarity_score": np.mean(
                [r["avg_similarity"] for r in retrieval_results]
            ),
        }

        print(
            f"Average retrieval time: {self.results['retrieval_quality']['avg_retrieval_time']:.4f}s"
        )
        print(
            f"Average similarity score: {self.results['retrieval_quality']['avg_similarity_score']:.4f}"
        )

    def evaluate_context_enrichment(self):
        """Test context enrichment and formatting."""
        print("\n=== Evaluating Context Enrichment ===")

        # Get test patient
        patients = crud_patient.get_multi(self.db, limit=1)
        if not patients:
            print("No patients found for testing")
            return

        patient = patients[0]
        enrichment_results = []

        for query in self.test_queries[:5]:  # Test subset
            start_time = time.time()

            # Test RAG enrichment
            rag_context = enrich_with_rag(
                self.db, user_id=str(patient.id), message=query, user_role="patient"
            )

            # Test context formatting
            patient_context = get_patient_context(self.db, str(patient.id))
            clinic_context = get_clinic_context(
                self.db, str(patient.associated_clinic_id)
            )

            full_context = {
                "patient_context": patient_context,
                "clinic_context": clinic_context,
                **rag_context,
            }

            formatted_context = format_context_for_prompt(full_context)
            enrichment_time = time.time() - start_time

            enrichment_results.append(
                {
                    "query": query,
                    "enrichment_time": enrichment_time,
                    "rag_chunks_found": len(rag_context.get("rag_context_chunks", [])),
                    "formatted_context_length": len(formatted_context),
                    "has_fallback": "rag_fallback_message" in rag_context,
                }
            )

        self.results["context_enrichment"] = {
            "test_results": enrichment_results,
            "avg_enrichment_time": np.mean(
                [r["enrichment_time"] for r in enrichment_results]
            ),
            "avg_chunks_retrieved": np.mean(
                [r["rag_chunks_found"] for r in enrichment_results]
            ),
            "fallback_rate": sum(1 for r in enrichment_results if r["has_fallback"])
            / len(enrichment_results),
        }

        print(
            f"Average enrichment time: {self.results['context_enrichment']['avg_enrichment_time']:.4f}s"
        )
        print(
            f"Average chunks retrieved: {self.results['context_enrichment']['avg_chunks_retrieved']:.1f}"
        )
        print(
            f"Fallback rate: {self.results['context_enrichment']['fallback_rate']:.1%}"
        )

    async def evaluate_chat_integration(self):
        """Test end-to-end chat flow with RAG."""
        print("\n=== Evaluating Chat Integration ===")

        # Get test patient
        patients = crud_patient.get_multi(self.db, limit=1)
        if not patients:
            print("No patients found for testing")
            return

        patient = patients[0]
        chat_results = []

        for query in self.test_queries[:3]:  # Test subset
            start_time = time.time()

            try:
                response = await process_chat_message(
                    user_id=patient.id,
                    user_message=query,
                    db=self.db,
                    context={"client_timezone_offset": -5},
                )

                end_time = time.time()
                total_time = end_time - start_time

                chat_results.append(
                    {
                        "query": query,
                        "success": True,
                        "response_length": (
                            len(response)
                            if isinstance(response, str)
                            else len(str(response))
                        ),
                        "total_time": total_time,
                        "error": None,
                    }
                )
            except Exception as e:
                chat_results.append(
                    {
                        "query": query,
                        "success": False,
                        "response_length": 0,
                        "total_time": time.time() - start_time,
                        "error": str(e),
                    }
                )

        self.results["chat_integration"] = {
            "test_results": chat_results,
            "success_rate": sum(1 for r in chat_results if r["success"])
            / len(chat_results),
            "avg_response_time": np.mean([r["total_time"] for r in chat_results]),
            "avg_response_length": np.mean(
                [r["response_length"] for r in chat_results if r["success"]]
            ),
        }

        print(f"Success rate: {self.results['chat_integration']['success_rate']:.1%}")
        print(
            f"Average response time: {self.results['chat_integration']['avg_response_time']:.4f}s"
        )

    def evaluate_edge_cases(self):
        """Test edge cases and error handling."""
        print("\n=== Evaluating Edge Cases ===")

        edge_case_results = []

        # Test 1: Empty query
        try:
            rag_context = enrich_with_rag(self.db, "test_user", "", "patient")
            edge_case_results.append(
                {
                    "test": "empty_query",
                    "success": True,
                    "has_fallback": "rag_fallback_message" in rag_context,
                }
            )
        except Exception as e:
            edge_case_results.append(
                {"test": "empty_query", "success": False, "error": str(e)}
            )

        # Test 2: Very long query
        long_query = "test " * 500
        try:
            rag_context = enrich_with_rag(self.db, "test_user", long_query, "patient")
            edge_case_results.append(
                {
                    "test": "long_query",
                    "success": True,
                    "has_fallback": "rag_fallback_message" in rag_context,
                }
            )
        except Exception as e:
            edge_case_results.append(
                {"test": "long_query", "success": False, "error": str(e)}
            )

        # Test 3: Invalid user ID
        try:
            rag_context = enrich_with_rag(
                self.db, str(uuid.uuid4()), "test query", "patient"
            )
            edge_case_results.append(
                {
                    "test": "invalid_user",
                    "success": True,
                    "has_fallback": "rag_fallback_message" in rag_context,
                }
            )
        except Exception as e:
            edge_case_results.append(
                {"test": "invalid_user", "success": False, "error": str(e)}
            )

        # Test 4: Special characters
        special_query = "Test with @#$%^&*() special chars"
        try:
            rag_context = enrich_with_rag(
                self.db, "test_user", special_query, "patient"
            )
            edge_case_results.append(
                {
                    "test": "special_chars",
                    "success": True,
                    "has_fallback": "rag_fallback_message" in rag_context,
                }
            )
        except Exception as e:
            edge_case_results.append(
                {"test": "special_chars", "success": False, "error": str(e)}
            )

        self.results["edge_cases"] = {
            "test_results": edge_case_results,
            "success_rate": sum(1 for r in edge_case_results if r["success"])
            / len(edge_case_results),
        }

        print(
            f"Edge case success rate: {self.results['edge_cases']['success_rate']:.1%}"
        )

    def generate_report(self):
        """Generate comprehensive evaluation report."""
        print("\n=== Generating Evaluation Report ===")

        # Create visualizations
        plt.figure(figsize=(15, 10))

        # Plot 1: Retrieval Performance
        plt.subplot(2, 3, 1)
        queries = [
            r["query"][:20] + "..."
            for r in self.results["retrieval_quality"]["test_queries"]
        ]
        similarities = [
            r["avg_similarity"]
            for r in self.results["retrieval_quality"]["test_queries"]
        ]
        plt.bar(range(len(queries)), similarities)
        plt.xticks(range(len(queries)), queries, rotation=45, ha="right")
        plt.title("Retrieval Similarity Scores")
        plt.ylabel("Average Similarity")

        # Plot 2: Retrieval Times
        plt.subplot(2, 3, 2)
        times = [
            r["retrieval_time"]
            for r in self.results["retrieval_quality"]["test_queries"]
        ]
        plt.bar(range(len(queries)), times)
        plt.xticks(range(len(queries)), queries, rotation=45, ha="right")
        plt.title("Retrieval Times")
        plt.ylabel("Time (seconds)")

        # Plot 3: Context Enrichment
        plt.subplot(2, 3, 3)
        enrichment_queries = [
            r["query"][:20] + "..."
            for r in self.results["context_enrichment"]["test_results"]
        ]
        chunks = [
            r["rag_chunks_found"]
            for r in self.results["context_enrichment"]["test_results"]
        ]
        plt.bar(range(len(enrichment_queries)), chunks)
        plt.xticks(
            range(len(enrichment_queries)), enrichment_queries, rotation=45, ha="right"
        )
        plt.title("Chunks Retrieved per Query")
        plt.ylabel("Number of Chunks")

        # Plot 4: Performance Summary
        plt.subplot(2, 3, 4)
        metrics = ["Retrieval Time", "Enrichment Time", "Chat Response Time"]
        values = [
            self.results["retrieval_quality"]["avg_retrieval_time"],
            self.results["context_enrichment"]["avg_enrichment_time"],
            self.results["chat_integration"]["avg_response_time"],
        ]
        plt.bar(metrics, values)
        plt.title("Average Performance Metrics")
        plt.ylabel("Time (seconds)")

        # Plot 5: Success Rates
        plt.subplot(2, 3, 5)
        categories = ["Chat Integration", "Edge Cases"]
        success_rates = [
            self.results["chat_integration"]["success_rate"],
            self.results["edge_cases"]["success_rate"],
        ]
        plt.bar(categories, success_rates)
        plt.title("Success Rates")
        plt.ylabel("Success Rate")
        plt.ylim(0, 1)

        # Plot 6: Chunk Size Analysis
        plt.subplot(2, 3, 6)
        chunk_data = pd.DataFrame(
            self.results["embedding_pipeline"]["chunking_analysis"]
        )
        for overlap in chunk_data["overlap"].unique():
            subset = chunk_data[chunk_data["overlap"] == overlap]
            plt.plot(
                subset["chunk_size"],
                subset["num_chunks"],
                marker="o",
                label=f"Overlap: {overlap}",
            )
        plt.xlabel("Chunk Size")
        plt.ylabel("Number of Chunks")
        plt.title("Chunk Size vs Number of Chunks")
        plt.legend()

        plt.tight_layout()
        plt.savefig(
            "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_evaluation_plots.png"
        )
        plt.close()

        # Generate text report
        report = f"""
# RAG System Evaluation Report
Generated: {datetime.now().isoformat()}

## Executive Summary
- Model: {self.results['embedding_pipeline']['model_name']}
- Embedding Dimensions: {self.results['embedding_pipeline']['embedding_dimensions']}
- Average Retrieval Time: {self.results['retrieval_quality']['avg_retrieval_time']:.4f}s
- Average Similarity Score: {self.results['retrieval_quality']['avg_similarity_score']:.4f}
- Chat Success Rate: {self.results['chat_integration']['success_rate']:.1%}

## Embedding Pipeline
- Current Chunk Size: {self.results['embedding_pipeline']['current_chunk_size']}
- Current Overlap: {self.results['embedding_pipeline']['current_overlap']}
- Embedding Speed: {self.results['embedding_pipeline']['embedding_speed']['time_per_chunk']:.4f}s per chunk

## Retrieval Quality
- Average Retrieval Time: {self.results['retrieval_quality']['avg_retrieval_time']:.4f}s
- Average Similarity Score: {self.results['retrieval_quality']['avg_similarity_score']:.4f}
- Test Queries Evaluated: {len(self.results['retrieval_quality']['test_queries'])}

## Context Enrichment
- Average Enrichment Time: {self.results['context_enrichment']['avg_enrichment_time']:.4f}s
- Average Chunks Retrieved: {self.results['context_enrichment']['avg_chunks_retrieved']:.1f}
- Fallback Rate: {self.results['context_enrichment']['fallback_rate']:.1%}

## Chat Integration
- Success Rate: {self.results['chat_integration']['success_rate']:.1%}
- Average Response Time: {self.results['chat_integration']['avg_response_time']:.4f}s
- Average Response Length: {self.results['chat_integration']['avg_response_length']:.0f} characters

## Edge Cases
- Success Rate: {self.results['edge_cases']['success_rate']:.1%}
- Tests Performed: {len(self.results['edge_cases']['test_results'])}

## Recommendations
1. Consider adjusting chunk size based on performance analysis
2. Monitor similarity scores and adjust threshold if needed
3. Add more comprehensive edge case testing
4. Consider caching frequently accessed embeddings
5. Optimize embedding model for better performance
"""

        # Save report
        report_path = "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_evaluation_report.md"
        with open(report_path, "w") as f:
            f.write(report)

        # Save detailed results
        results_path = "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_evaluation_results.json"
        with open(results_path, "w") as f:
            json.dump(self.results, f, indent=2, default=str)

        print(f"Report saved to: {report_path}")
        print(f"Detailed results saved to: {results_path}")

    async def run_evaluation(self):
        """Run complete evaluation suite."""
        print("Starting RAG System Evaluation...")

        try:
            self.evaluate_embedding_pipeline()
            self.evaluate_retrieval_quality()
            self.evaluate_context_enrichment()
            await self.evaluate_chat_integration()
            self.evaluate_edge_cases()
            self.generate_report()

            print("\nEvaluation completed successfully!")
        except Exception as e:
            print(f"\nError during evaluation: {e}")
            import traceback

            traceback.print_exc()
        finally:
            self.db.close()


if __name__ == "__main__":
    evaluator = RAGEvaluator()
    asyncio.run(evaluator.run_evaluation())
