#!/usr/bin/env python3
"""Script to fix AI->Clinician messages that were incorrectly visible to patients."""

import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app import crud
from app.db.session import SessionLocal
from app.models.chat_message import ChatMessage, MessageSenderType, MessageRouteType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def fix_ai_clinician_messages():
    """Find and log AI->Clinician messages that contain action completions."""
    db = SessionLocal()
    
    try:
        logger.info("Searching for AI->Clinician messages with action completions...")
        
        # Find all AI messages
        ai_messages = (
            db.query(ChatMessage)
            .filter(ChatMessage.sender_type == MessageSenderType.AGENT)
            .all()
        )
        
        logger.info(f"Found {len(ai_messages)} total AI messages")
        
        # Look for messages that contain action completion text
        action_messages = []
        for msg in ai_messages:
            if any(phrase in msg.message_content for phrase in [
                "Completed", "actions:", "✓", 
                "side_effect_report_create", "appointment_create",
                "weight_log_create", "medication_request_create"
            ]):
                action_messages.append(msg)
                logger.info(f"\nFound action message:")
                logger.info(f"  ID: {msg.id}")
                logger.info(f"  Patient ID: {msg.patient_id}")
                logger.info(f"  Route: {msg.message_route.value if msg.message_route else 'None'}")
                logger.info(f"  Content: {msg.message_content[:100]}...")
                logger.info(f"  Created: {msg.created_at}")
        
        logger.info(f"\nFound {len(action_messages)} AI messages with action completions")
        
        # Check which ones have incorrect routing
        incorrect_messages = []
        for msg in action_messages:
            # These should be routed to 'clinician' not 'patient' or 'ai'
            if msg.message_route != MessageRouteType.CLINICIAN:
                incorrect_messages.append(msg)
                logger.warning(f"\nINCORRECT ROUTING:")
                logger.warning(f"  Message ID: {msg.id}")
                logger.warning(f"  Current route: {msg.message_route.value if msg.message_route else 'None'}")
                logger.warning(f"  Should be: clinician")
        
        if incorrect_messages:
            logger.info(f"\n{len(incorrect_messages)} messages need routing correction")
            logger.info("\nFixing messages automatically...")
            
            for msg in incorrect_messages:
                old_route = msg.message_route.value if msg.message_route else 'None'
                msg.message_route = MessageRouteType.CLINICIAN
                logger.info(f"Fixed message {msg.id}: {old_route} -> clinician")
            
            db.commit()
            logger.info("\nAll messages have been fixed!")
        else:
            logger.info("\nAll action messages already have correct routing!")
            
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    fix_ai_clinician_messages()