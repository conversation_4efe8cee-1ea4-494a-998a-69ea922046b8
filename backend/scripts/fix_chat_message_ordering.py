#!/usr/bin/env python3
"""
Fix chat message ordering for messages that have the same timestamp.
This script adds small time differences to ensure logical ordering.
"""

import sys
from pathlib import Path
from datetime import timed<PERSON><PERSON>
from sqlalchemy import text

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from app.db.session import SessionLocal
from app.models import ChatMessage
from app.models.chat_message import MessageSenderType

def fix_chat_message_ordering():
    """Fix chat messages that have the same timestamp by adding small time differences."""
    db = SessionLocal()
    
    try:
        print("Fixing chat message ordering...")
        
        # Find all patients with messages
        patient_ids = db.query(ChatMessage.patient_id).distinct().all()
        
        for (patient_id,) in patient_ids:
            print(f"\nProcessing messages for patient: {patient_id}")
            
            # Get all messages for this patient ordered by created_at and id
            messages = (
                db.query(ChatMessage)
                .filter(ChatMessage.patient_id == patient_id)
                .order_by(ChatMessage.created_at.asc(), ChatMessage.id.asc())
                .all()
            )
            
            if len(messages) < 2:
                continue
                
            # Group messages by timestamp
            timestamp_groups = {}
            for msg in messages:
                timestamp_key = msg.created_at.replace(microsecond=0)  # Group by second
                if timestamp_key not in timestamp_groups:
                    timestamp_groups[timestamp_key] = []
                timestamp_groups[timestamp_key].append(msg)
            
            # Fix groups with multiple messages at the same timestamp
            for timestamp, group in timestamp_groups.items():
                if len(group) <= 1:
                    continue
                    
                print(f"  Found {len(group)} messages at {timestamp}")
                
                # Sort messages in logical order within the group
                # Patient messages should come before agent/clinician responses
                sorted_group = sorted(group, key=lambda m: (
                    0 if m.sender_type == MessageSenderType.PATIENT else 1,
                    str(m.id)  # Secondary sort by ID for consistency
                ))
                
                # Add small time increments to ensure proper ordering
                for i, msg in enumerate(sorted_group):
                    if i > 0:  # Skip the first message, keep its original timestamp
                        new_timestamp = msg.created_at + timedelta(seconds=i * 2)
                        
                        # Update using raw SQL to avoid ORM issues
                        db.execute(
                            text("UPDATE chat_messages SET created_at = :new_ts WHERE id = :msg_id"),
                            {"new_ts": new_timestamp, "msg_id": str(msg.id)}
                        )
                        
                        print(f"    Updated message {msg.id} ({msg.sender_type}) to {new_timestamp}")
        
        db.commit()
        print("\n✅ Chat message ordering fixed successfully!")
        
    except Exception as e:
        print(f"❌ Error fixing chat message ordering: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    fix_chat_message_ordering()