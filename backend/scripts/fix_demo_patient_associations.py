#!/usr/bin/env python3
"""Fix demo patient associations by adding them to clinician_patient_association table."""

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.session import SessionLocal

def main():
    db = SessionLocal()
    
    try:
        # Get demo patient IDs
        demo_patients = db.execute(
            text("""
                SELECT id FROM patients 
                WHERE first_name IN ('<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>')
            """)
        ).fetchall()
        
        if not demo_patients:
            print("No demo patients found!")
            return
        
        # <PERSON>'s clinician ID
        clinician_id = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
        
        # Add associations
        for patient in demo_patients:
            # Check if association already exists
            existing = db.execute(
                text("""
                    SELECT 1 FROM clinician_patient_association 
                    WHERE clinician_id = :clinician_id AND patient_id = :patient_id
                """),
                {"clinician_id": clinician_id, "patient_id": patient[0]}
            ).first()
            
            if not existing:
                db.execute(
                    text("""
                        INSERT INTO clinician_patient_association (clinician_id, patient_id)
                        VALUES (:clinician_id, :patient_id)
                    """),
                    {"clinician_id": clinician_id, "patient_id": patient[0]}
                )
                print(f"Added association for patient {patient[0]}")
            else:
                print(f"Association already exists for patient {patient[0]}")
        
        db.commit()
        print("Successfully added all clinician-patient associations!")
        
        # Verify the associations
        count = db.execute(
            text("""
                SELECT COUNT(*) FROM clinician_patient_association 
                WHERE clinician_id = :clinician_id
            """),
            {"clinician_id": clinician_id}
        ).scalar()
        
        print(f"\nTotal patients associated with clinician: {count}")
        
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()