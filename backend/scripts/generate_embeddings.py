import asyncio

from sqlalchemy import select

from app.db.session import SessionLocal
from app.models.content_chunk import ContentChunk
from app.services.embedding_pipeline import embedding_model


async def generate_embeddings():
    db = SessionLocal()
    try:
        # Get chunks without embeddings
        stmt = select(ContentChunk).where(ContentChunk.embedding.is_(None))
        result = await db.execute(stmt)
        chunks = result.scalars().all()

        for chunk in chunks:
            # Generate embedding
            embedding = embedding_model.encode(chunk.chunk_text).tolist()
            # Update chunk with embedding
            chunk.embedding = embedding
            db.add(chunk)

        await db.commit()
    finally:
        await db.close()


if __name__ == "__main__":
    asyncio.run(generate_embeddings())
