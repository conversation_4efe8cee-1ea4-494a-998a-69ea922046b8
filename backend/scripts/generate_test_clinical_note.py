#!/usr/bin/env python3
"""
Generate a test clinical note in draft status.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.crud.crud_patient import patient as crud_patient
from app.crud.crud_clinician import clinician as crud_clinician
from app.services.clinical_notes_service import ClinicalNotesService
from app.schemas.clinical_note import GenerateClinicalNoteRequest
from app.core.logging import logger


def generate_draft_note():
    """Generate a clinical note that will be in draft status."""
    db: Session = SessionLocal()
    service = ClinicalNotesService()
    
    try:
        # Get demo clinician and patient
        demo_clinician = crud_clinician.get(db, id="user_2waSREJSlduBPyK6Vbv9TU3VhI7")
        demo_patient = crud_patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        
        if not demo_clinician or not demo_patient:
            logger.error("Demo clinician or patient not found")
            return
            
        logger.info(f"Using clinician: {demo_clinician.first_name} {demo_clinician.last_name}")
        logger.info(f"Using patient: {demo_patient.first_name} {demo_patient.last_name}")
        
        # Generate a clinical note
        request = GenerateClinicalNoteRequest(
            patient_id=demo_patient.id,
            clinician_id=demo_clinician.id,
            note_type="progress_note",
            chief_complaint="Follow-up for GLP-1 medication management",
            visit_context="Patient reports good progress with Ozempic treatment"
        )
        
        logger.info("Generating clinical note...")
        result = asyncio.run(service.generate_clinical_note(db, request, demo_clinician.id))
        
        if result:
            logger.info(f"\n✓ Clinical note generated successfully!")
            logger.info(f"  Note ID: {result.note.id}")
            logger.info(f"  Status: {result.note.status}")
            logger.info(f"  Type: {result.note.note_type}")
            logger.info(f"  Confidence: {result.confidence_score}")
            logger.info(f"\nThe note is in '{result.note.status}' status and can be deleted.")
        else:
            logger.error("✗ Failed to generate clinical note")
            
    except Exception as e:
        logger.error(f"Error during generation: {str(e)}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("Generating Test Clinical Note")
    logger.info("=" * 60)
    generate_draft_note()
    logger.info("=" * 60)