import asyncio
import json

from app import crud
from app.db.session import SessionL<PERSON>al
from app.schemas.chat import ChatModelEnum


async def initialize_user_settings():
    db = SessionLocal()
    try:
        # Get all users without LLM configuration
        users = await crud.patient.get_multi(db)

        for user in users:
            # Check if user has LLM config
            settings = await crud.user_settings.get_setting(
                db=db, user_id=str(user.id), key="llm_configuration"
            )

            if not settings:
                # Initialize default settings
                default_config = {
                    "model": ChatModelEnum.GPT_4,
                    "temperature": 0.7,
                    "max_tokens": 500,
                    "system_prompt_template": "You are a helpful assistant...",
                }

                # Save settings
                await crud.user_settings.update_setting(
                    db=db,
                    user_id=str(user.id),
                    key="llm_configuration",
                    value=json.dumps(default_config),
                )

        await db.close()
    finally:
        await db.close()


if __name__ == "__main__":
    asyncio.run(initialize_user_settings())
