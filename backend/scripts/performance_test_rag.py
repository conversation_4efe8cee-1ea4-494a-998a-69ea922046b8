"""
Performance testing script for RAG system
Focuses on latency, throughput, and concurrent operation performance
"""

import asyncio
import concurrent.futures
import json
import time

import matplotlib.pyplot as plt
import numpy as np

from app.crud.crud_patient import patient as crud_patient
from app.db.session import SessionLocal
from app.services.embedding_pipeline import chunk_text, generate_embeddings
from app.utils.context_enricher import enrich_with_rag


class RAGPerformanceTester:
    """Performance tester for RAG system components."""

    def __init__(self):
        self.db = SessionLocal()
        self.test_queries = [
            "What are the side effects of semaglutide?",
            "How do I schedule an appointment?",
            "What is your cancellation policy?",
            "Tell me about weight loss medications",
            "What are the office hours?",
            "How much does a consultation cost?",
            "What insurance do you accept?",
            "Can I get a prescription refill?",
            "What are the common side effects?",
            "How do I contact my doctor?",
        ]
        self.results = {
            "latency_tests": {},
            "throughput_tests": {},
            "concurrent_tests": {},
            "scalability_tests": {},
        }

    def test_component_latency(self):
        """Test latency of individual RAG components."""
        print("\n=== Testing Component Latency ===")

        # Get test data
        patients = crud_patient.get_multi(self.db, limit=1)
        if not patients:
            print("No patients found for testing")
            return

        patient = patients[0]
        latency_results = []

        # Test each component
        for query in self.test_queries:
            # 1. Chunking latency
            chunk_start = time.time()
            chunk_text(query * 10)  # Make it longer
            chunking_time = time.time() - chunk_start

            # 2. Embedding latency
            embed_start = time.time()
            generate_embeddings([query])
            embedding_time = time.time() - embed_start

            # 3. RAG enrichment latency
            rag_start = time.time()
            enrich_with_rag(
                self.db, user_id=str(patient.id), message=query, user_role="patient"
            )
            rag_time = time.time() - rag_start

            latency_results.append(
                {
                    "query": query,
                    "chunking_time": chunking_time,
                    "embedding_time": embedding_time,
                    "rag_time": rag_time,
                    "total_time": chunking_time + embedding_time + rag_time,
                }
            )

        self.results["latency_tests"] = {
            "component_latencies": latency_results,
            "avg_chunking": np.mean([r["chunking_time"] for r in latency_results]),
            "avg_embedding": np.mean([r["embedding_time"] for r in latency_results]),
            "avg_rag": np.mean([r["rag_time"] for r in latency_results]),
            "avg_total": np.mean([r["total_time"] for r in latency_results]),
        }

        print(
            f"Average chunking time: {self.results['latency_tests']['avg_chunking']:.4f}s"
        )
        print(
            f"Average embedding time: {self.results['latency_tests']['avg_embedding']:.4f}s"
        )
        print(f"Average RAG time: {self.results['latency_tests']['avg_rag']:.4f}s")
        print(f"Average total time: {self.results['latency_tests']['avg_total']:.4f}s")

    def test_throughput(self):
        """Test throughput under various loads."""
        print("\n=== Testing Throughput ===")

        patients = crud_patient.get_multi(self.db, limit=1)
        if not patients:
            print("No patients found for testing")
            return

        patient = patients[0]
        throughput_results = []

        # Test different batch sizes
        batch_sizes = [1, 5, 10, 20, 50]

        for batch_size in batch_sizes:
            batch_queries = self.test_queries[:batch_size] * (
                batch_size // len(self.test_queries) + 1
            )
            batch_queries = batch_queries[:batch_size]

            start_time = time.time()

            # Process batch
            for query in batch_queries:
                enrich_with_rag(
                    self.db, user_id=str(patient.id), message=query, user_role="patient"
                )

            total_time = time.time() - start_time
            throughput = batch_size / total_time

            throughput_results.append(
                {
                    "batch_size": batch_size,
                    "total_time": total_time,
                    "throughput": throughput,
                    "avg_time_per_query": total_time / batch_size,
                }
            )

        self.results["throughput_tests"] = {
            "batch_results": throughput_results,
            "max_throughput": max(r["throughput"] for r in throughput_results),
            "optimal_batch_size": max(
                throughput_results, key=lambda x: x["throughput"]
            )["batch_size"],
        }

        print(
            f"Maximum throughput: {self.results['throughput_tests']['max_throughput']:.2f} queries/second"
        )
        print(
            f"Optimal batch size: {self.results['throughput_tests']['optimal_batch_size']}"
        )

    async def test_concurrent_requests(self):
        """Test performance under concurrent requests."""
        print("\n=== Testing Concurrent Requests ===")

        patients = crud_patient.get_multi(self.db, limit=1)
        if not patients:
            print("No patients found for testing")
            return

        patient = patients[0]
        concurrent_results = []

        # Test different concurrency levels
        concurrency_levels = [1, 5, 10, 20]

        for concurrency in concurrency_levels:

            async def process_query(query):
                start = time.time()
                try:
                    enrich_with_rag(
                        self.db,
                        user_id=str(patient.id),
                        message=query,
                        user_role="patient",
                    )
                    return time.time() - start, True
                except Exception:
                    return time.time() - start, False

            # Create concurrent tasks
            queries_to_process = self.test_queries * (
                concurrency // len(self.test_queries) + 1
            )
            queries_to_process = queries_to_process[:concurrency]

            start_time = time.time()

            # Process concurrently using ThreadPoolExecutor
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=concurrency
            ) as executor:
                futures = []
                for query in queries_to_process:
                    future = executor.submit(
                        enrich_with_rag, self.db, str(patient.id), query, "patient"
                    )
                    futures.append(future)

                [future.result() for future in concurrent.futures.as_completed(futures)]

            total_time = time.time() - start_time

            concurrent_results.append(
                {
                    "concurrency": concurrency,
                    "total_time": total_time,
                    "requests_per_second": concurrency / total_time,
                    "avg_response_time": total_time / concurrency,
                }
            )

        self.results["concurrent_tests"] = {
            "concurrency_results": concurrent_results,
            "max_rps": max(r["requests_per_second"] for r in concurrent_results),
            "optimal_concurrency": max(
                concurrent_results, key=lambda x: x["requests_per_second"]
            )["concurrency"],
        }

        print(f"Maximum RPS: {self.results['concurrent_tests']['max_rps']:.2f}")
        print(
            f"Optimal concurrency: {self.results['concurrent_tests']['optimal_concurrency']}"
        )

    def test_scalability(self):
        """Test scalability with increasing data sizes."""
        print("\n=== Testing Scalability ===")

        scalability_results = []

        # Test with different text sizes
        text_sizes = [100, 500, 1000, 2500, 5000, 10000]

        for size in text_sizes:
            # Generate text of specified size
            test_text = "This is a test sentence. " * (size // 25)

            # Measure chunking time
            chunk_start = time.time()
            chunks = chunk_text(test_text)
            chunking_time = time.time() - chunk_start

            # Measure embedding time
            embed_start = time.time()
            if chunks:  # Only embed if we have chunks
                generate_embeddings(chunks[:10])  # Limit to 10 for consistency
                embedding_time = time.time() - embed_start
            else:
                embedding_time = 0

            scalability_results.append(
                {
                    "text_size": size,
                    "num_chunks": len(chunks),
                    "chunking_time": chunking_time,
                    "embedding_time": embedding_time,
                    "total_time": chunking_time + embedding_time,
                }
            )

        self.results["scalability_tests"] = {
            "size_results": scalability_results,
            "scaling_factor": scalability_results[-1]["total_time"]
            / scalability_results[0]["total_time"],
        }

        print(
            f"Scaling factor (10K vs 100 chars): {self.results['scalability_tests']['scaling_factor']:.2f}x"
        )

    def generate_performance_report(self):
        """Generate performance test report with visualizations."""
        print("\n=== Generating Performance Report ===")

        # Create visualizations
        plt.figure(figsize=(15, 12))

        # Plot 1: Component Latency Breakdown
        plt.subplot(2, 3, 1)
        components = ["Chunking", "Embedding", "RAG"]
        latencies = [
            self.results["latency_tests"]["avg_chunking"],
            self.results["latency_tests"]["avg_embedding"],
            self.results["latency_tests"]["avg_rag"],
        ]
        plt.bar(components, latencies)
        plt.title("Average Component Latencies")
        plt.ylabel("Time (seconds)")

        # Plot 2: Throughput vs Batch Size
        plt.subplot(2, 3, 2)
        batch_results = self.results["throughput_tests"]["batch_results"]
        batch_sizes = [r["batch_size"] for r in batch_results]
        throughputs = [r["throughput"] for r in batch_results]
        plt.plot(batch_sizes, throughputs, marker="o")
        plt.title("Throughput vs Batch Size")
        plt.xlabel("Batch Size")
        plt.ylabel("Queries/Second")

        # Plot 3: Concurrent Performance
        plt.subplot(2, 3, 3)
        concurrent_results = self.results["concurrent_tests"]["concurrency_results"]
        concurrency_levels = [r["concurrency"] for r in concurrent_results]
        rps = [r["requests_per_second"] for r in concurrent_results]
        plt.plot(concurrency_levels, rps, marker="o")
        plt.title("RPS vs Concurrency")
        plt.xlabel("Concurrency Level")
        plt.ylabel("Requests/Second")

        # Plot 4: Response Times
        plt.subplot(2, 3, 4)
        response_times = [r["avg_response_time"] for r in concurrent_results]
        plt.plot(concurrency_levels, response_times, marker="o")
        plt.title("Response Time vs Concurrency")
        plt.xlabel("Concurrency Level")
        plt.ylabel("Average Response Time (s)")

        # Plot 5: Scalability
        plt.subplot(2, 3, 5)
        scale_results = self.results["scalability_tests"]["size_results"]
        text_sizes = [r["text_size"] for r in scale_results]
        total_times = [r["total_time"] for r in scale_results]
        plt.plot(text_sizes, total_times, marker="o")
        plt.title("Processing Time vs Text Size")
        plt.xlabel("Text Size (characters)")
        plt.ylabel("Total Time (s)")

        # Plot 6: Processing Time Breakdown
        plt.subplot(2, 3, 6)
        latency_results = self.results["latency_tests"]["component_latencies"]
        query_indices = range(len(latency_results))
        chunking_times = [r["chunking_time"] for r in latency_results]
        embedding_times = [r["embedding_time"] for r in latency_results]
        rag_times = [r["rag_time"] for r in latency_results]

        plt.bar(query_indices, chunking_times, label="Chunking")
        plt.bar(
            query_indices, embedding_times, bottom=chunking_times, label="Embedding"
        )
        plt.bar(
            query_indices,
            rag_times,
            bottom=[c + e for c, e in zip(chunking_times, embedding_times)],
            label="RAG",
        )
        plt.title("Processing Time Breakdown by Query")
        plt.xlabel("Query Index")
        plt.ylabel("Time (s)")
        plt.legend()

        plt.tight_layout()
        plt.savefig(
            "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_performance_plots.png"
        )
        plt.close()

        # Generate performance report
        report = f"""
# RAG System Performance Report
Generated: {time.strftime("%Y-%m-%d %H:%M:%S")}

## Executive Summary
- Average Total Latency: {self.results['latency_tests']['avg_total']:.4f}s
- Maximum Throughput: {self.results['throughput_tests']['max_throughput']:.2f} queries/second
- Maximum RPS (concurrent): {self.results['concurrent_tests']['max_rps']:.2f}
- Scaling Factor (10K vs 100 chars): {self.results['scalability_tests']['scaling_factor']:.2f}x

## Component Latency
- Chunking: {self.results['latency_tests']['avg_chunking']:.4f}s ({self.results['latency_tests']['avg_chunking']/self.results['latency_tests']['avg_total']*100:.1f}%)
- Embedding: {self.results['latency_tests']['avg_embedding']:.4f}s ({self.results['latency_tests']['avg_embedding']/self.results['latency_tests']['avg_total']*100:.1f}%)
- RAG Enrichment: {self.results['latency_tests']['avg_rag']:.4f}s ({self.results['latency_tests']['avg_rag']/self.results['latency_tests']['avg_total']*100:.1f}%)

## Throughput Analysis
- Optimal Batch Size: {self.results['throughput_tests']['optimal_batch_size']}
- Maximum Throughput: {self.results['throughput_tests']['max_throughput']:.2f} queries/second

## Concurrent Performance
- Optimal Concurrency: {self.results['concurrent_tests']['optimal_concurrency']}
- Maximum RPS: {self.results['concurrent_tests']['max_rps']:.2f}

## Scalability
- Linear scaling observed up to {self.results['scalability_tests']['size_results'][2]['text_size']} characters
- Performance degradation factor: {self.results['scalability_tests']['scaling_factor']:.2f}x for 100x increase in text size

## Performance Bottlenecks
1. RAG enrichment is the largest component ({self.results['latency_tests']['avg_rag']/self.results['latency_tests']['avg_total']*100:.1f}% of total time)
2. Embedding generation shows good performance
3. Chunking is efficient for typical query sizes

## Recommendations
1. Implement caching for frequently accessed embeddings
2. Consider async processing for RAG enrichment
3. Optimize database queries in context enrichment
4. Add connection pooling for better concurrent performance
5. Consider using a faster embedding model for real-time queries
"""

        # Save reports
        report_path = "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_performance_report.md"
        with open(report_path, "w") as f:
            f.write(report)

        results_path = "/Users/<USER>/Documents/projects/pulsetrack/backend/results/rag_performance_results.json"
        with open(results_path, "w") as f:
            json.dump(self.results, f, indent=2, default=str)

        print(f"Performance report saved to: {report_path}")
        print(f"Detailed results saved to: {results_path}")

    async def run_performance_tests(self):
        """Run all performance tests."""
        print("Starting RAG Performance Tests...")

        try:
            self.test_component_latency()
            self.test_throughput()
            await self.test_concurrent_requests()
            self.test_scalability()
            self.generate_performance_report()

            print("\nPerformance testing completed successfully!")
        except Exception as e:
            print(f"\nError during performance testing: {e}")
            import traceback

            traceback.print_exc()
        finally:
            self.db.close()


if __name__ == "__main__":
    tester = RAGPerformanceTester()
    asyncio.run(tester.run_performance_tests())
