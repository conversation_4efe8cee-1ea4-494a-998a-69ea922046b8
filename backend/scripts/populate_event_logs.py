#!/usr/bin/env python
"""
Script to populate event logs from existing activity in the database.
This ensures the Patient Activity feed shows real data.
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
from uuid import UUID

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
from sqlalchemy.orm import Session

from app import crud
from app.db.session import SessionLocal
from app.schemas.event_log import EventLogCreate


def create_event_log(
    db: Session,
    action: str,
    actor_user_id: str,
    actor_role: str,
    target_resource_type: str,
    target_resource_id: str,
    created_at: datetime,
    details: Optional[dict] = None,
) -> None:
    """Create an event log entry."""
    event_log_in = EventLogCreate(
        action=action,
        actor_user_id=actor_user_id,
        actor_role=actor_role,
        target_resource_type=target_resource_type,
        target_resource_id=target_resource_id,
        status="SUCCESS",
        outcome="SUCCESS",
        details=details or {},
    )
    
    # Create the event log  
    event_log = crud.event_log.event_log.create_with_actor(
        db,
        obj_in=event_log_in,
        actor_user_id=actor_user_id,
        actor_role=actor_role
    )
    
    # Update the created_at timestamp to match the original event
    db.execute(
        text("UPDATE event_logs SET created_at = :created_at WHERE id = :id"),
        {"created_at": created_at, "id": event_log.id}
    )
    db.commit()


async def populate_chat_message_events(db: Session) -> int:
    """Populate event logs for chat messages."""
    count = 0
    
    # Get chat messages that don't have corresponding event logs
    result = db.execute(text("""
        SELECT 
            cm.id,
            cm.patient_id,
            cm.sender_type,
            cm.message_content,
            cm.created_at
        FROM chat_messages cm
        WHERE NOT EXISTS (
            SELECT 1 FROM event_logs el 
            WHERE el.target_resource_type = 'chat_message' 
            AND el.target_resource_id = cm.id::text
        )
        ORDER BY cm.created_at DESC
        LIMIT 100
    """))
    
    for row in result:
        actor_id = row.patient_id if row.sender_type == 'PATIENT' else 'system'
        actor_role = 'patient' if row.sender_type == 'PATIENT' else 'system'
        
        create_event_log(
            db=db,
            action="CREATE",
            actor_user_id=actor_id,
            actor_role=actor_role,
            target_resource_type="chat_message",
            target_resource_id=str(row.id),
            created_at=row.created_at,
            details={
                "message_preview": row.message_content[:100] if row.message_content else "",
                "sender_type": row.sender_type,
                "actor_name": "Patient" if row.sender_type == 'PATIENT' else "AI Assistant"
            }
        )
        count += 1
    
    return count


async def populate_weight_log_events(db: Session) -> int:
    """Populate event logs for weight logs."""
    count = 0
    
    result = db.execute(text("""
        SELECT 
            wl.id,
            wl.patient_id,
            wl.weight_kg,
            wl.log_date,
            p.first_name,
            p.last_name
        FROM weight_logs wl
        JOIN patients p ON p.id = wl.patient_id
        WHERE NOT EXISTS (
            SELECT 1 FROM event_logs el 
            WHERE el.target_resource_type = 'weight_log' 
            AND el.target_resource_id = wl.id::text
        )
        ORDER BY wl.log_date DESC
        LIMIT 100
    """))
    
    for row in result:
        create_event_log(
            db=db,
            action="CREATE",
            actor_user_id=row.patient_id,
            actor_role="patient",
            target_resource_type="weight_log",
            target_resource_id=str(row.id),
            created_at=row.log_date,
            details={
                "weight_kg": float(row.weight_kg),
                "actor_name": f"{row.first_name} {row.last_name}"
            }
        )
        count += 1
    
    return count


async def populate_side_effect_events(db: Session) -> int:
    """Populate event logs for side effect reports."""
    count = 0
    
    result = db.execute(text("""
        SELECT 
            ser.id,
            ser.patient_id,
            ser.severity,
            ser.description,
            ser.reported_at,
            p.first_name,
            p.last_name
        FROM side_effect_reports ser
        JOIN patients p ON p.id = ser.patient_id
        WHERE NOT EXISTS (
            SELECT 1 FROM event_logs el 
            WHERE el.target_resource_type = 'side_effect_report' 
            AND el.target_resource_id = ser.id::text
        )
        ORDER BY ser.reported_at DESC
        LIMIT 100
    """))
    
    for row in result:
        create_event_log(
            db=db,
            action="CREATE",
            actor_user_id=row.patient_id,
            actor_role="patient",
            target_resource_type="side_effect_report",
            target_resource_id=str(row.id),
            created_at=row.reported_at,
            details={
                "severity": row.severity,
                "description": row.description[:100] if row.description else "",
                "actor_name": f"{row.first_name} {row.last_name}"
            }
        )
        count += 1
    
    return count


async def populate_appointment_events(db: Session) -> int:
    """Populate event logs for appointments."""
    count = 0
    
    result = db.execute(text("""
        SELECT 
            a.id,
            a.patient_id,
            a.appointment_datetime,
            a.appointment_type,
            a.status,
            a.created_at,
            p.first_name,
            p.last_name
        FROM appointments a
        JOIN patients p ON p.id = a.patient_id
        WHERE NOT EXISTS (
            SELECT 1 FROM event_logs el 
            WHERE el.target_resource_type = 'appointment' 
            AND el.target_resource_id = a.id::text
        )
        ORDER BY a.created_at DESC
        LIMIT 100
    """))
    
    for row in result:
        create_event_log(
            db=db,
            action="CREATE",
            actor_user_id=row.patient_id,
            actor_role="patient",
            target_resource_type="appointment",
            target_resource_id=str(row.id),
            created_at=row.created_at,
            details={
                "appointment_type": row.appointment_type,
                "status": row.status,
                "appointment_datetime": row.appointment_datetime.isoformat(),
                "actor_name": f"{row.first_name} {row.last_name}"
            }
        )
        count += 1
    
    return count


async def populate_medication_request_events(db: Session) -> int:
    """Populate event logs for medication requests."""
    count = 0
    
    result = db.execute(text("""
        SELECT 
            mr.id,
            mr.patient_id,
            mr.medication_name,
            mr.status,
            mr.created_at,
            p.first_name,
            p.last_name
        FROM medication_requests mr
        JOIN patients p ON p.id = mr.patient_id
        WHERE NOT EXISTS (
            SELECT 1 FROM event_logs el 
            WHERE el.target_resource_type = 'medication_request' 
            AND el.target_resource_id = mr.id::text
        )
        ORDER BY mr.created_at DESC
        LIMIT 100
    """))
    
    for row in result:
        create_event_log(
            db=db,
            action="CREATE",
            actor_user_id=row.patient_id,
            actor_role="patient",
            target_resource_type="medication_request",
            target_resource_id=str(row.id),
            created_at=row.created_at,
            details={
                "medication_name": row.medication_name,
                "status": row.status,
                "actor_name": f"{row.first_name} {row.last_name}"
            }
        )
        count += 1
    
    return count


async def main():
    """Main function to populate all event logs."""
    db = SessionLocal()
    try:
        print("Starting event log population...")
        
        # Populate event logs for different types of activities
        chat_count = await populate_chat_message_events(db)
        print(f"Created {chat_count} event logs for chat messages")
        
        weight_count = await populate_weight_log_events(db)
        print(f"Created {weight_count} event logs for weight logs")
        
        side_effect_count = await populate_side_effect_events(db)
        print(f"Created {side_effect_count} event logs for side effect reports")
        
        appointment_count = await populate_appointment_events(db)
        print(f"Created {appointment_count} event logs for appointments")
        
        medication_count = await populate_medication_request_events(db)
        print(f"Created {medication_count} event logs for medication requests")
        
        total = chat_count + weight_count + side_effect_count + appointment_count + medication_count
        print(f"\nTotal event logs created: {total}")
        
        # Show current event log count
        result = db.execute(text("SELECT COUNT(*) FROM event_logs"))
        total_logs = result.scalar()
        print(f"Total event logs in database: {total_logs}")
        
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())