#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to validate CRUD import patterns across the codebase.

This script scans all Python files in the specified directory and validates that
the import statements and usage patterns for CRUD operations follow the expected pattern:
- Import: from app import crud
- Usage: crud.model.method(...)

Initially designed as a refactoring tool, it now serves as a validation script.
"""

import argparse
import sys
from pathlib import Path

from bowler import Query

# Files to exclude from processing
EXCLUDED_FILES = {
    "app/api/v1/endpoints/notes.py",  # Excluding problematic file
    "app/crud/crud_content_chunk.py",  # Excluding file with syntax errors
}


def should_process_file(file_path: Path) -> bool:
    """Return True if the file should be processed, otherwise False."""
    posix_path = file_path.as_posix()
    return not any(excluded in posix_path for excluded in EXCLUDED_FILES)


def log_parse_error(file_path: str, error_msg: str) -> None:
    """Log syntax errors so that users can fix them before refactoring."""
    print(f"\nError parsing {file_path}:")
    print(f"  {error_msg}")
    print("Please fix syntax errors in this file before proceeding.")


def transform_import(node, capture, filename):
    """
    Transform import statements of the form:
        from app import crud
    No transformation needed for these imports since this is the correct pattern.
    This function is kept as a placeholder for potential future transformations.
    """
    # No transformation needed for these imports
    return node


def transform_usage(node, capture, filename):
    """
    Transform usage patterns if needed.
    In this codebase, the pattern crud.patient.method(...) is already correct,
    so no transformation is needed.
    This function is kept as a placeholder for potential future transformations.
    """
    # No transformation needed for these usage patterns
    return node


def get_python_files(target_path: str) -> list[Path]:
    """Return list of Python files under target_path that are eligible."""
    path_obj = Path(target_path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        all_files = [path_obj]
    elif path_obj.is_dir():
        all_files = list(path_obj.rglob("*.py"))
    else:
        all_files = []  # Not a file or directory, or not a .py file
    return [f for f in all_files if should_process_file(f)]


def validate_files(files: list[Path]) -> list[Path]:
    """
    Pre-validate Python files by compiling them.
    Files that contain syntax errors are reported and excluded.
    """
    valid_files = []
    for file_path in files:
        try:
            with open(file_path, encoding="utf-8") as f:
                compile(f.read(), str(file_path), "exec")
            valid_files.append(file_path)
        except SyntaxError as e:
            log_parse_error(str(file_path), f"Syntax error at line {e.lineno}: {e.msg}")
    return valid_files


def main(
    write: bool = False, print_only: bool = False, target_path: str = "./app"
) -> None:
    print("Validating Python files...")
    python_files = get_python_files(target_path)
    if not python_files:
        print("No files to process after exclusions.")
        return

    print(
        f"\nProcessing {len(python_files)} files (excluding {len(EXCLUDED_FILES)} files)..."
    )
    print("Excluded files:", ", ".join(EXCLUDED_FILES))

    valid_files = validate_files(python_files)
    if len(valid_files) != len(python_files):
        print("\nSome files contain syntax errors. Please fix them before proceeding.")
        sys.exit(1)

    # Prepare list of file paths as strings to feed to Bowler
    file_list = [str(f) for f in valid_files]

    print("\nScanning for patterns to validate...")
    print("Files to process:", "\n  ".join([""] + file_list))

    import_count = 0
    usage_count = 0

    # Count import statements:
    print("\nLooking for 'from app import crud' statements...")

    def count_import(node, capture, filename):
        nonlocal import_count
        import_count += 1
        print(f"  Found in: {filename}")
        return node

    (
        Query(file_list)
        .select("import_from< 'from' 'app' 'import' 'crud' >")
        .filter(lambda node, capture, filename: should_process_file(Path(filename)))
        .modify(count_import)
        .execute(write=write, interactive=print_only, silent=False)
    )

    # Count usage patterns for simple access (crud.patient):
    print("\nLooking for 'crud.<model>' usage patterns:")

    def count_usage(node, capture, filename):
        nonlocal usage_count
        model_name = capture["name"].value
        usage_count += 1
        print(f"  Found in: {filename} - Model: {model_name}")
        return node

    (
        Query(file_list)
        .select("power< 'crud' trailer< '.' name=any > >")
        .filter(lambda node, capture, filename: should_process_file(Path(filename)))
        .modify(count_usage)
        .execute(write=write, interactive=print_only, silent=False)
    )

    # Count usage patterns for method calls (crud.patient.get()):
    def count_method_usage(node, capture, filename):
        nonlocal usage_count
        model_name = capture["model"].value
        method_name = capture["method"].value
        usage_count += 1
        print(f"  Found in: {filename} - Model: {model_name}, Method: {method_name}")
        return node

    (
        Query(file_list)
        .select(
            "power< 'crud' trailer< '.' model=NAME > trailer< '.' method=NAME > args=trailer< '(' [any] ')' > >"
        )
        .filter(lambda node, capture, filename: should_process_file(Path(filename)))
        .modify(count_method_usage)
        .execute(write=write, interactive=print_only, silent=False)
    )

    # Report findings
    print("\nSUMMARY:")
    print(f"  Found {import_count} 'from app import crud' import statements")
    print(f"  Found {usage_count} 'crud.<model>' usage patterns")

    if import_count == 0 and usage_count == 0:
        print("\nNo matching patterns found in the specified files.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Validate CRUD imports and usage patterns"
    )
    parser.add_argument(
        "--write",
        action="store_true",
        help="Apply any potential changes to files (usually not needed)",
    )
    parser.add_argument(
        "--print",
        action="store_true",
        help="Preview potential changes interactively (useful for debugging)",
    )
    parser.add_argument(
        "--path", default="./app", help="Target path to process (default: ./app)"
    )
    args = parser.parse_args()

    if args.write and args.print:
        print("Error: Cannot specify both --write and --print")
        sys.exit(1)

    main(write=args.write, print_only=args.print, target_path=args.path)
