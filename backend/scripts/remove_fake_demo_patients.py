#!/usr/bin/env python3
"""
Remove fake demo patients that don't have valid Clerk IDs.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

def remove_fake_patients(db):
    """Remove demo patients with fake IDs."""
    
    print("Removing fake demo patients...")
    
    # First check what we're deleting
    check_query = text("""
        SELECT id, first_name, last_name 
        FROM patients 
        WHERE id LIKE 'demo_%'
    """)
    
    patients = db.execute(check_query).fetchall()
    
    if not patients:
        print("No fake demo patients found.")
        return
    
    print(f"Found {len(patients)} fake demo patients to remove:")
    for p in patients:
        print(f"  - {p.first_name} {p.last_name} (ID: {p.id})")
    
    # Delete all related data first (due to foreign key constraints)
    tables = [
        'chat_messages',
        'weight_logs',
        'appointments',
        'medication_requests',
        'side_effect_reports',
        'patient_alerts',
        'patient_education_assignments',
        'clinician_patient_association'
    ]
    
    for table in tables:
        delete_query = text(f"""
            DELETE FROM {table} 
            WHERE patient_id IN (
                SELECT id FROM patients WHERE id LIKE 'demo_%'
            )
        """)
        result = db.execute(delete_query)
        if result.rowcount > 0:
            print(f"  Deleted {result.rowcount} records from {table}")
    
    # Now delete the patients
    delete_patients = text("""
        DELETE FROM patients 
        WHERE id LIKE 'demo_%'
    """)
    
    result = db.execute(delete_patients)
    db.commit()
    
    print(f"\nRemoved {result.rowcount} fake demo patients.")
    
    # Show remaining patients
    remaining_query = text("""
        SELECT id, first_name, last_name, email
        FROM patients
        ORDER BY created_at
    """)
    
    remaining = db.execute(remaining_query).fetchall()
    
    print("\nRemaining patients in database:")
    for p in remaining:
        print(f"  - {p.first_name} {p.last_name} ({p.email})")
    
    print("\n⚠️  NOTE: To create proper demo patients, you need to:")
    print("1. Create real users in Clerk Dashboard (https://dashboard.clerk.com)")
    print("2. Use their actual Clerk user IDs in the database")
    print("3. Or use the existing Michael Patient for all demo scenarios")

def main():
    print("="*60)
    print("REMOVING FAKE DEMO PATIENTS")
    print("="*60)
    
    db = SessionLocal()
    
    try:
        remove_fake_patients(db)
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()