#!/usr/bin/env python3
"""
Delete existing chat messages and reseed with properly routed messages for investor demo.
This ensures all message routing is correct and conversations look realistic.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.models.patient import Patient
from app.models.clinician import Clinician
from app.models.chat_message import ChatMessage
from app.crud.crud_chat_message import chat_message as crud_chat
from app.schemas.chat import ChatMessageCreateInternal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def clear_existing_messages(db: Session):
    """Delete all existing chat messages."""
    try:
        count = db.query(ChatMessage).count()
        db.query(ChatMessage).delete()
        db.commit()
        logger.info(f"✓ Deleted {count} existing chat messages")
        return True
    except Exception as e:
        logger.error(f"✗ Error deleting messages: {str(e)}")
        db.rollback()
        return False


def seed_patient_conversations(db: Session):
    """Seed realistic conversations for all demo patients."""
    
    # Get the demo clinician
    clinician = db.query(Clinician).filter(Clinician.id == "user_2waSREJSlduBPyK6Vbv9TU3VhI7").first()
    if not clinician:
        logger.error("Demo clinician not found!")
        return
    
    # Define conversations for each patient
    patient_conversations = {
        # James Wilson - Struggling with nausea
        "James": {
            "first_name": "James",
            "last_name": "Wilson",
            "messages": [
                {"days_ago": 7, "sender": "patient", "route": "clinician", "content": "Dr. Isaac, the nausea is pretty rough this week. Worse than before. Should I be concerned?"},
                {"days_ago": 7, "sender": "clinician", "route": "patient", "content": "Sorry to hear that. Sometimes nausea can fluctuate. Are you eating before your injection? Taking it at the same time each week?"},
                {"days_ago": 7, "sender": "patient", "route": "clinician", "content": "Yes to both. It's mainly in the mornings. Having trouble with breakfast."},
                {"days_ago": 7, "sender": "clinician", "route": "ai", "content": "What are the best strategies for managing GLP-1 related morning nausea?"},
                {"days_ago": 7, "sender": "agent", "route": "clinician", "content": "Morning nausea tips:\n- Keep crackers by your bedside\n- Try ginger tea first thing\n- Start with bland foods (toast, rice)\n- Eat smaller amounts more frequently\n- Consider taking your injection in the evening\n\nIf it persists beyond 48 hours after injection, let Dr. Isaac know."},
                {"days_ago": 6, "sender": "patient", "route": "clinician", "content": "Ginger tea is helping! Also switched to evening injections like you suggested."},
                {"days_ago": 6, "sender": "clinician", "route": "patient", "content": "Glad the ginger helps! For heartburn: avoid lying down after eating, try smaller meals, and consider an antacid if needed. Keep me posted."},
                {"days_ago": 2, "sender": "patient", "route": "clinician", "content": "Despite the side effects, I'm down 8 pounds! Clothes are fitting better."},
                {"days_ago": 2, "sender": "agent", "route": "patient", "content": "Your positive attitude is inspiring! 8 pounds is excellent progress. The side effects often improve over time as your body adjusts. Keep up the great work!"},
            ]
        },
        
        # Emily Davis - Weight plateau concerns
        "Emily": {
            "first_name": "Emily",
            "last_name": "Davis",
            "messages": [
                {"days_ago": 10, "sender": "patient", "route": "clinician", "content": "Hi Dr. Isaac, I've been stuck at the same weight for 3 weeks now. Is this normal?"},
                {"days_ago": 10, "sender": "clinician", "route": "patient", "content": "Weight plateaus are completely normal and expected. Your body needs time to adjust. Are you still following the meal plan we discussed?"},
                {"days_ago": 10, "sender": "patient", "route": "clinician", "content": "Yes, tracking everything. Maybe eating 1400-1500 calories daily. Exercise 3x per week."},
                {"days_ago": 9, "sender": "clinician", "route": "ai", "content": "Patient on GLP-1 experiencing 3-week plateau. Compliant with diet and exercise. What adjustments would you recommend?"},
                {"days_ago": 9, "sender": "agent", "route": "clinician", "content": "For GLP-1 plateaus:\n1. Reassure that plateaus are normal (every 4-6 weeks)\n2. Check hydration - aim for 64oz water daily\n3. Consider varying exercise routine\n4. Ensure adequate protein (0.8-1g per kg body weight)\n5. May need dose adjustment after 4+ weeks of plateau\n\nMost patients break through plateaus within 2-4 weeks without intervention."},
                {"days_ago": 9, "sender": "clinician", "route": "patient", "content": "Let's give it another 2 weeks. Meanwhile, try increasing your water intake and adding some strength training. Your body might just need a change in routine."},
                {"days_ago": 3, "sender": "patient", "route": "clinician", "content": "You were right! Down 2 pounds this week after adding weights to my routine!"},
                {"days_ago": 3, "sender": "clinician", "route": "patient", "content": "Fantastic! Sometimes our bodies just need a little shake-up. Keep up the great work with the strength training - it's excellent for metabolism."},
            ]
        },
        
        # Robert Chen - Positive progress
        "Robert": {
            "first_name": "Robert",
            "last_name": "Chen",
            "messages": [
                {"days_ago": 14, "sender": "patient", "route": "clinician", "content": "Dr. Isaac, wanted to share some good news - I'm down 15 pounds and my energy is through the roof!"},
                {"days_ago": 14, "sender": "clinician", "route": "patient", "content": "That's wonderful, Robert! 15 pounds is significant progress. How are you managing with the medication? Any side effects?"},
                {"days_ago": 14, "sender": "patient", "route": "clinician", "content": "Minor nausea the first few days after injection, but nothing major. I've been meal prepping on Sundays which helps a lot."},
                {"days_ago": 13, "sender": "clinician", "route": "patient", "content": "Meal prepping is a game-changer! Keep doing what you're doing. Your progress is exactly what we hope to see."},
                {"days_ago": 5, "sender": "patient", "route": "ai", "content": "What's a healthy rate of weight loss on GLP-1 medications?"},
                {"days_ago": 5, "sender": "agent", "route": "patient", "content": "On GLP-1 medications, a healthy rate is:\n- 1-2 pounds per week on average\n- 5-10% of body weight in the first 3-6 months\n- Weight loss may be faster initially, then slow down\n\nYour 15 pounds is excellent progress! Remember, sustainable loss is better than rapid loss."},
                {"days_ago": 1, "sender": "patient", "route": "clinician", "content": "Just hit the 20-pound mark! My blood pressure is also down. Thank you for getting me started on this journey."},
                {"days_ago": 1, "sender": "clinician", "route": "patient", "content": "Congratulations on reaching 20 pounds! The blood pressure improvement is a fantastic bonus. You're doing an amazing job!"},
            ]
        },
        
        # Sarah Johnson - Stress eating challenges
        "Sarah": {
            "first_name": "Sarah",
            "last_name": "Johnson",
            "messages": [
                {"days_ago": 8, "sender": "patient", "route": "clinician", "content": "Dr. Isaac, I'm having a really tough time with stress eating lately. Work has been overwhelming."},
                {"days_ago": 8, "sender": "clinician", "route": "patient", "content": "I understand. Stress can definitely trigger eating behaviors. Have you tried any of the mindfulness techniques we discussed?"},
                {"days_ago": 8, "sender": "patient", "route": "clinician", "content": "I tried the breathing exercises but ended up ordering takeout anyway 😔"},
                {"days_ago": 8, "sender": "clinician", "route": "ai", "content": "Patient struggling with stress-induced eating despite GLP-1 therapy. What behavioral strategies work best?"},
                {"days_ago": 8, "sender": "agent", "route": "clinician", "content": "It's okay to have setbacks. Here are some strategies for managing stress eating:\n\n1. Keep healthy snacks readily available\n2. Practice the 5-minute pause before eating\n3. Journal your feelings instead of eating\n4. Call a friend or go for a walk\n\nRemember, progress isn't always linear!"},
                {"days_ago": 8, "sender": "clinician", "route": "patient", "content": "That's excellent self-awareness! Journaling can be a powerful tool. Keep it up, and remember I'm here if you need support."},
                {"days_ago": 4, "sender": "patient", "route": "clinician", "content": "Actually tried journaling yesterday and it helped! Didn't realize how anxious I was about the project deadline."},
                {"days_ago": 3, "sender": "patient", "route": "clinician", "content": "Is it normal to have more cravings in the evening? That's when I struggle most."},
                {"days_ago": 3, "sender": "agent", "route": "patient", "content": "Evening cravings are very common! This can be due to:\n- Lower willpower after a long day\n- Boredom or habit\n- Not eating enough during the day\n\nTry having a protein-rich afternoon snack and planning an evening activity like a bath or hobby."},
            ]
        },
        
        # Michael Patient - New to treatment
        "Michael": {
            "first_name": "Michael",
            "last_name": "Patient",
            "messages": [
                {"days_ago": 5, "sender": "patient", "route": "clinician", "content": "Hi Dr. Isaac, just got my first prescription filled. Any tips before I start?"},
                {"days_ago": 5, "sender": "clinician", "route": "patient", "content": "Welcome aboard, Michael! Start with the lowest dose as prescribed. Take it on the same day each week, and don't be discouraged if you have mild side effects initially."},
                {"days_ago": 5, "sender": "patient", "route": "ai", "content": "What should I expect in my first week on GLP-1 medication?"},
                {"days_ago": 5, "sender": "agent", "route": "patient", "content": "First week expectations:\n- Mild nausea (usually improves)\n- Decreased appetite\n- Possible fatigue\n- Some digestive changes\n\nTips: Eat smaller meals, stay hydrated, and be patient. Most side effects improve after 2-3 weeks!"},
                {"days_ago": 2, "sender": "patient", "route": "clinician", "content": "Day 3 update: Definitely less hungry! Some nausea but manageable. Is it normal to feel this full after small meals?"},
                {"days_ago": 2, "sender": "clinician", "route": "patient", "content": "Yes, that's exactly how the medication works! The fullness feeling is your body responding properly. Listen to those signals and stop eating when satisfied."},
                {"days_ago": 1, "sender": "patient", "route": "clinician", "content": "Quick question - can I exercise while on this medication?"},
                {"days_ago": 1, "sender": "clinician", "route": "patient", "content": "Absolutely! Exercise is encouraged and will enhance your results. Just stay hydrated and listen to your body. Start slow if you're new to exercise."},
            ]
        }
    }
    
    # Process each patient's conversations
    for patient_key, conv_data in patient_conversations.items():
        patient = db.query(Patient).filter(
            Patient.first_name == conv_data["first_name"],
            Patient.last_name == conv_data["last_name"]
        ).first()
        
        if not patient:
            logger.warning(f"Patient {conv_data['first_name']} {conv_data['last_name']} not found")
            continue
            
        logger.info(f"Seeding messages for {patient.first_name} {patient.last_name}")
        
        for msg_data in conv_data["messages"]:
            # Calculate timestamp
            timestamp = datetime.utcnow() - timedelta(days=msg_data["days_ago"])
            
            # Create message based on sender type
            try:
                if msg_data["sender"] == "patient":
                    message = ChatMessageCreateInternal(
                        patient_id=patient.id,
                        sender_type="PATIENT",
                        message_content=msg_data["content"],
                        message_route=msg_data["route"]
                    )
                elif msg_data["sender"] == "clinician":
                    message = ChatMessageCreateInternal(
                        patient_id=patient.id,
                        sender_type="CLINICIAN",
                        message_content=msg_data["content"],
                        message_route=msg_data["route"]
                    )
                elif msg_data["sender"] == "agent":
                    message = ChatMessageCreateInternal(
                        patient_id=patient.id,
                        sender_type="AGENT",
                        message_content=msg_data["content"],
                        message_route=msg_data["route"]
                    )
                
                # Create the message
                created_msg = crud_chat.create(db, obj_in=message)
                
                # Update the timestamp (need to do this separately)
                created_msg.created_at = timestamp
                db.add(created_msg)
                db.commit()
                
                logger.debug(f"  - Created {msg_data['sender']} → {msg_data['route']} message")
                
            except Exception as e:
                logger.error(f"  ✗ Error creating message: {str(e)}")
                db.rollback()
                
        logger.info(f"  ✓ Completed seeding for {patient.first_name}")


def main():
    """Main function to reseed chat messages."""
    db = SessionLocal()
    
    try:
        logger.info("=== Starting Chat Message Reseed ===")
        
        # Step 1: Clear existing messages
        logger.info("\n1. Clearing existing messages...")
        if not clear_existing_messages(db):
            logger.error("Failed to clear messages. Aborting.")
            return
            
        # Step 2: Seed new conversations
        logger.info("\n2. Seeding new conversations...")
        seed_patient_conversations(db)
        
        # Summary
        final_count = db.query(ChatMessage).count()
        logger.info(f"\n=== Reseed Complete ===")
        logger.info(f"Total messages created: {final_count}")
        logger.info(f"Messages are properly routed with realistic conversations")
        logger.info(f"Ready for investor demo!")
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()