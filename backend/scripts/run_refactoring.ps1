Write-Host "Starting refactoring process..."

# Create backup
Write-Host "Creating backup..."
git stash push -m "Pre-refactoring backup $(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Run Ruff first to clean up imports
Write-Host "Running Ruff..."
ruff check --fix ./app

# Run Bowler transformation
Write-Host "Running Bowler transformation..."
python scripts/refactor_crud_imports.py --write

# Run Ruff again to clean up any remaining issues
Write-Host "Final Ruff cleanup..."
ruff check --fix ./app

Write-Host "Refactoring complete. Please review changes and run tests."