#!/bin/bash
set -e

echo "Starting refactoring process..."

# Create backup
echo "Creating backup..."
git stash push -m "Pre-refactoring backup $(date +%Y%m%d_%H%M%S)"

# Run Ruff first to clean up imports
echo "Running Ruff..."
ruff check --fix ./app

# Run Bowler transformation
echo "Running Bowler transformation..."
python scripts/refactor_crud_imports.py --write

# Run Ruff again to clean up any remaining issues
echo "Final Ruff cleanup..."
ruff check --fix ./app

echo "Refactoring complete. Please review changes and run tests."