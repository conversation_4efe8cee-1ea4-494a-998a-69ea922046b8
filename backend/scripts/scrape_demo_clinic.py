#!/usr/bin/env python3
"""
Script to trigger scraping of demo clinic website to populate RAG content.
"""

import sys
from pathlib import Path
import requests
import time

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings


def get_demo_clinic_info():
    """Get the demo clinic ID from the database."""
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT id, name, website
            FROM clinics
            WHERE name LIKE '%Edinburgh%'
            LIMIT 1
        """))
        row = result.fetchone()
        
        if not row:
            print("No Edinburgh clinic found!")
            return None, None, None
            
        return str(row.id), row.name, row.website


def trigger_scraping():
    """Trigger scraping of the demo clinic website."""
    clinic_id, clinic_name, clinic_website = get_demo_clinic_info()
    
    if not clinic_id:
        print("Cannot proceed without a clinic.")
        return
        
    print(f"\n=== CLINIC INFORMATION ===")
    print(f"Clinic: {clinic_name}")
    print(f"ID: {clinic_id}")
    print(f"Website: {clinic_website}")
    
    if not clinic_website:
        print("\nNo website URL configured for this clinic!")
        print("You need to update the clinic with a website URL first.")
        print("\nFor demo purposes, you could use:")
        print("- A real weight loss clinic website")
        print("- A demo/test URL with sample content")
        return
    
    # Note: This would require authentication. For now, just showing the process
    print("\n=== SCRAPING PROCESS ===")
    print("To trigger scraping, you would:")
    print(f"1. POST to http://localhost:8000/api/v1/clinics/scrape")
    print(f"2. With payload: {{'url': '{clinic_website}', 'clinic_id': '{clinic_id}'}}")
    print("3. As an authenticated admin user")
    print("\nThe scraping process will:")
    print("- Fetch content from the clinic website")
    print("- Clean and process the HTML")
    print("- Create ScrapedPage records")
    print("- Generate ContentChunk records")
    print("- Create embeddings for each chunk")
    print("- Enable RAG queries about clinic information")
    
    # Show current status
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as conn:
        result = conn.execute(
            text("SELECT last_scraped_at FROM clinics WHERE id = :clinic_id"),
            {"clinic_id": clinic_id}
        )
        row = result.fetchone()
        if row and row.last_scraped_at:
            print(f"\nLast scraped: {row.last_scraped_at}")
        else:
            print("\nThis clinic has never been scraped.")


if __name__ == "__main__":
    trigger_scraping()