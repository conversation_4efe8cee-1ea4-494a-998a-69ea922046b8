#!/usr/bin/env python3
"""
Seed additional demo patients with realistic longitudinal data.
These patients provide the "noise" that makes finding <PERSON>'s critical condition impressive.
"""

import sys
import os
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo
from pathlib import Path
import random
from uuid import uuid4

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.models import (
    Patient, Clinician, ChatMessage, WeightLog, SideEffectReport,
    Appointment, MedicationRequest, Medication, EventLog
)
from app.models.chat_message import MessageSenderType, MessageRouteType
from app.models.medication_request import MedicationRequestStatus as RequestStatus
from app.schemas.side_effect_report import SeverityLevel

# Import the appointment scheduler
from appointment_scheduler import AppointmentScheduler
from shared_appointment_scheduler import get_shared_scheduler

# Appointment status constants
class AppointmentStatus:
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

# Additional demo patients data
ADDITIONAL_PATIENTS = [
    {
        "id": "user_demo_patient_sarah",
        "first_name": "Sarah",
        "last_name": "<PERSON>",
        "email": "<EMAIL>",
        "date_of_birth": datetime(1982, 3, 15).date(),
        "phone_number": "+**********",
        "height_cm": 165,
        "scenario": "steady_progress",
        "starting_weight": 185,
        "current_weight": 172,
        "medication": "Wegovy (Semaglutide)"
    },
    {
        "id": "user_demo_patient_james",
        "first_name": "James",
        "last_name": "Martinez",
        "email": "<EMAIL>",
        "date_of_birth": datetime(1978, 7, 22).date(),
        "phone_number": "+**********",
        "height_cm": 178,
        "scenario": "plateau",
        "starting_weight": 210,
        "current_weight": 195,
        "medication": "Mounjaro (Tirzepatide)"
    },
    {
        "id": "user_demo_patient_robert",
        "first_name": "Robert",
        "last_name": "Chen",
        "email": "<EMAIL>",
        "date_of_birth": datetime(1990, 11, 8).date(),
        "phone_number": "+**********",
        "height_cm": 172,
        "scenario": "mild_side_effects",
        "starting_weight": 198,
        "current_weight": 183,
        "medication": "Ozempic (Semaglutide)"
    },
    {
        "id": "user_demo_patient_emily",
        "first_name": "Emily",
        "last_name": "Thompson",
        "email": "<EMAIL>",
        "date_of_birth": datetime(1995, 1, 30).date(),
        "phone_number": "+**********",
        "height_cm": 158,
        "scenario": "new_patient",
        "starting_weight": 165,
        "current_weight": 162,
        "medication": "Saxenda (Liraglutide)"
    }
]


def create_patient_weight_logs(db: Session, patient: Patient, scenario: str, starting_weight: float, current_weight: float):
    """Create realistic weight logs based on patient scenario."""
    base_time = datetime.now() - timedelta(days=90)
    weight_logs = []
    
    if scenario == "steady_progress":
        # Steady 1-2 lbs per week loss
        weeks = 12
        total_loss = starting_weight - current_weight
        weekly_loss = total_loss / weeks
        
        for week in range(weeks + 1):
            weight = starting_weight - (weekly_loss * week)
            # Add some realistic variation
            weight += random.uniform(-0.5, 0.5)
            
            # Convert lbs to kg (1 lb = 0.453592 kg)
            weight_kg = weight * 0.453592
            
            log = WeightLog(
                patient_id=patient.id,
                weight_kg=round(weight_kg, 1),
                log_date=(base_time + timedelta(weeks=week)).date()

            )
            weight_logs.append(log)
            
    elif scenario == "plateau":
        # Good initial loss, then plateau
        for week in range(13):
            if week < 6:
                # Good loss first 6 weeks
                weight = starting_weight - (week * 2.5)
            else:
                # Plateau with minor fluctuations
                weight = starting_weight - 15 + random.uniform(-1, 1)
            
            weight_kg = weight * 0.453592
            log = WeightLog(
                patient_id=patient.id,
                weight_kg=round(weight_kg, 1),
                log_date=(base_time + timedelta(weeks=week)).date()

            )
            weight_logs.append(log)
            
    elif scenario == "mild_side_effects":
        # Moderate loss with some setbacks
        for week in range(13):
            base_loss = week * 1.2
            if week in [4, 5, 9, 10]:  # Setback weeks
                base_loss -= 2
            weight = starting_weight - base_loss + random.uniform(-0.5, 0.5)
            
            weight_kg = weight * 0.453592
            log = WeightLog(
                patient_id=patient.id,
                weight_kg=round(weight_kg, 1),
                log_date=(base_time + timedelta(weeks=week)).date()

            )
            weight_logs.append(log)
            
    elif scenario == "new_patient":
        # Just started, only 3 weeks of data
        for week in range(4):
            weight = starting_weight - (week * 0.8) + random.uniform(-0.3, 0.3)
            
            weight_kg = weight * 0.453592
            log = WeightLog(
                patient_id=patient.id,
                weight_kg=round(weight_kg, 1),
                log_date=(datetime.now() - timedelta(weeks=3-week)).date()

            )
            weight_logs.append(log)
    
    for log in weight_logs:
        db.add(log)
    
    return len(weight_logs)


def create_patient_appointments(db: Session, patient: Patient, clinician_id: str, scenario: str, scheduler: AppointmentScheduler):
    """Create realistic appointments based on patient scenario with proper scheduling."""
    appointments = []
    
    # Define clinic timezone (US Central Time for Edinburgh clinic)
    clinic_tz = ZoneInfo("America/Chicago")
    
    if scenario == "steady_progress":
        # Regular monthly check-ins
        appointment_specs = [
            (90, "Initial Consultation", AppointmentStatus.COMPLETED, False),
            (60, "1-Month Follow-up", AppointmentStatus.COMPLETED, False),
            (30, "2-Month Follow-up", AppointmentStatus.COMPLETED, True),  # Morning
            (-7, "3-Month Follow-up", AppointmentStatus.SCHEDULED, False),
        ]
    elif scenario == "plateau":
        # More frequent due to plateau
        appointment_specs = [
            (90, "Initial Consultation", AppointmentStatus.COMPLETED, False),
            (60, "1-Month Follow-up", AppointmentStatus.COMPLETED, False),
            (45, "Plateau Discussion", AppointmentStatus.COMPLETED, True),  # Morning - urgent
            (30, "Medication Adjustment", AppointmentStatus.COMPLETED, False),
            (14, "Progress Check", AppointmentStatus.COMPLETED, False),
            (-3, "Follow-up", AppointmentStatus.SCHEDULED, True),  # Morning
        ]
    elif scenario == "mild_side_effects":
        # Extra appointments for side effects
        appointment_specs = [
            (90, "Initial Consultation", AppointmentStatus.COMPLETED, False),
            (75, "Side Effect Evaluation", AppointmentStatus.COMPLETED, True),  # Morning - urgent
            (60, "1-Month Follow-up", AppointmentStatus.COMPLETED, False),
            (40, "Side Effect Follow-up", AppointmentStatus.COMPLETED, True),  # Morning
            (30, "2-Month Check-in", AppointmentStatus.COMPLETED, False),
            (-14, "Regular Follow-up", AppointmentStatus.SCHEDULED, False),
        ]
    else:  # new_patient
        appointment_specs = [
            (21, "Initial Consultation", AppointmentStatus.COMPLETED, True),  # Morning
            (7, "2-Week Check-in", AppointmentStatus.COMPLETED, False),
            (-7, "1-Month Follow-up", AppointmentStatus.SCHEDULED, False),
        ]
    
    for days_offset, reason, status, prefer_morning in appointment_specs:
        # Calculate target date
        if days_offset > 0:
            target_date = (datetime.now() - timedelta(days=days_offset)).date()
        else:
            target_date = (datetime.now() + timedelta(days=abs(days_offset))).date()
        
        # Determine if urgent (for side effect evaluations)
        urgent = "Side Effect" in reason or "Plateau Discussion" in reason
        
        # Schedule the appointment
        appointment_datetime = scheduler.schedule_appointment(
            target_date=target_date,
            appointment_type="Initial Consultation" if "Initial" in reason else "Follow-up",
            prefer_morning=prefer_morning,
            urgent=urgent
        )
        
        if appointment_datetime:
            # Add timezone info to the scheduled time and convert to UTC
            appointment_datetime_tz = appointment_datetime.replace(tzinfo=clinic_tz)
            appointment_datetime_utc = appointment_datetime_tz.astimezone(timezone.utc)
                
            appointment = Appointment(
                patient_id=patient.id,
                clinician_id=clinician_id,
                appointment_datetime=appointment_datetime_utc,
                appointment_type="Follow-up" if "Follow-up" in reason else "Consultation",
                status=status,
                reason=reason
            )
            appointments.append(appointment)
            db.add(appointment)
        else:
            print(f"   ⚠️  Could not schedule {reason} for {patient.first_name} {patient.last_name}")
    
    return len(appointments)


def create_patient_chats(db: Session, patient: Patient, scenario: str):
    """Create realistic chat conversations based on patient scenario."""
    chats = []
    base_time = datetime.now()
    
    if scenario == "steady_progress":
        conversations = [
            {
                "days_ago": 45,
                "messages": [
                    ("Feeling great on Wegovy! Energy levels are up", MessageRouteType.AI),
                    ("That's wonderful to hear! Consistent weight loss often leads to increased energy. Keep up the great work!", MessageRouteType.AI),
                ]
            },
            {
                "days_ago": 20,
                "messages": [
                    ("Down 10 pounds! Is this pace normal?", MessageRouteType.AI),
                    ("Yes, 1-2 pounds per week is a healthy, sustainable rate. You're doing excellently!", MessageRouteType.AI),
                ]
            },
            {
                "days_ago": 5,
                "messages": [
                    ("Clothes are fitting better. Should I increase exercise?", MessageRouteType.AI),
                    ("Great progress! Yes, gradually increasing exercise can help maintain muscle mass during weight loss.", MessageRouteType.AI),
                ]
            }
        ]
    elif scenario == "plateau":
        conversations = [
            {
                "days_ago": 30,
                "messages": [
                    ("Weight hasn't changed in 2 weeks. Is the medication still working?", MessageRouteType.AI),
                    ("Plateaus are common after initial weight loss. Your body may be adjusting. Let's discuss options with your clinician.", MessageRouteType.AI),
                ]
            },
            {
                "days_ago": 15,
                "messages": [
                    ("Still stuck at the same weight. Getting frustrated.", MessageRouteType.AI),
                    ("I understand your frustration. Plateaus can last 2-4 weeks. Focus on non-scale victories like energy and fitness.", MessageRouteType.AI),
                ]
            }
        ]
    elif scenario == "mild_side_effects":
        conversations = [
            {
                "days_ago": 60,
                "messages": [
                    ("Having some nausea after injections", MessageRouteType.AI),
                    ("Mild nausea is common initially. Try eating smaller meals and inject after eating. Let me know if it worsens.", MessageRouteType.AI),
                ]
            },
            {
                "days_ago": 25,
                "messages": [
                    ("Nausea is better but having constipation now", MessageRouteType.AI),
                    ("GI side effects often improve over time. Increase fiber and water intake. I'll note this for your clinician.", MessageRouteType.AI),
                ]
            }
        ]
    else:  # new_patient
        conversations = [
            {
                "days_ago": 14,
                "messages": [
                    ("Just started Saxenda. When will I see results?", MessageRouteType.AI),
                    ("Most patients see initial results within 2-4 weeks. Focus on following the dosing schedule for now.", MessageRouteType.AI),
                ]
            },
            {
                "days_ago": 3,
                "messages": [
                    ("Is it normal to feel less hungry already?", MessageRouteType.AI),
                    ("Yes! Appetite suppression is one of the first effects. This helps you maintain a caloric deficit.", MessageRouteType.AI),
                ]
            }
        ]
    
    for conv in conversations:
        timestamp = base_time - timedelta(days=conv["days_ago"])
        
        for i, (content, route) in enumerate(conv["messages"]):
            if i % 2 == 0:  # Patient message
                msg = ChatMessage(
                    patient_id=patient.id,
                    sender_type=MessageSenderType.PATIENT,
                    message_content=content,
                    created_at=timestamp + timedelta(minutes=i),
                    message_route=route,
                    is_read_by_clinician=True
                )
            else:  # AI response
                msg = ChatMessage(
                    patient_id=patient.id,
                    sender_type=MessageSenderType.AGENT,
                    message_content=content,
                    created_at=timestamp + timedelta(minutes=i+1),
                    message_route=MessageRouteType.PATIENT,
                    is_read_by_clinician=True
                )
            
            chats.append(msg)
            db.add(msg)
    
    return len(chats)


def create_patient_side_effects(db: Session, patient: Patient, clinician_id: str, scenario: str):
    """Create side effect reports based on patient scenario."""
    reports = []
    
    if scenario == "mild_side_effects":
        side_effects_data = [
            (60, "Nausea", SeverityLevel.MINOR, "After each injection, lasts 2-3 hours"),
            (45, "Nausea", SeverityLevel.MINOR, "Getting better, only 1 hour now"),
            (25, "Constipation", SeverityLevel.MODERATE, "Started 3 days ago"),
            (10, "Fatigue", SeverityLevel.MINOR, "Feeling tired in afternoons"),
        ]
        
        for days_ago, effect, severity, notes in side_effects_data:
            report = SideEffectReport(
                patient_id=patient.id,
                clinician_id=clinician_id,
                reported_at=datetime.now() - timedelta(days=days_ago),
                description=f"{effect}: {notes}",
                severity=severity
            )
            reports.append(report)
            db.add(report)
    
    elif scenario == "steady_progress":
        # Minimal side effects
        report = SideEffectReport(
            patient_id=patient.id,
            clinician_id=clinician_id,
            reported_at=datetime.now() - timedelta(days=80),
            description="Mild nausea: Only first week, resolved on its own",
            severity=SeverityLevel.MINOR
        )
        reports.append(report)
        db.add(report)
    
    # Other scenarios have no side effects
    
    return len(reports)


def create_patient_medication_request(db: Session, patient: Patient, clinician_id: str, medication_name: str):
    """Create medication request for the patient."""
    # Get medication
    medication = db.query(Medication).filter(Medication.name == medication_name).first()
    if not medication:
        print(f"Warning: Medication {medication_name} not found")
        return 0
    
    request = MedicationRequest(
        patient_id=patient.id,
        clinician_id=clinician_id,
        medication_name=medication_name,
        status=RequestStatus.APPROVED,
        dosage=medication.dosage_guidelines or "As directed",
        frequency="Weekly" if medication.dosage_guidelines and "weekly" in medication.dosage_guidelines.lower() else "Daily",
        notes="Patient enrolled in weight management program",
        created_at=datetime.now() - timedelta(days=90),
        updated_at=datetime.now() - timedelta(days=89)
    )
    db.add(request)
    
    return 1


def seed_additional_patients(db: Session, clinician_id: str):
    """Main function to seed all additional demo patients."""
    print("\nSeeding additional demo patients...")
    
    # Use the shared appointment scheduler for the clinician
    scheduler = get_shared_scheduler()
    
    total_stats = {
        "patients": 0,
        "weight_logs": 0,
        "appointments": 0,
        "chats": 0,
        "side_effects": 0,
        "med_requests": 0
    }
    
    for patient_data in ADDITIONAL_PATIENTS:
        # Create or update patient
        patient = db.query(Patient).filter(Patient.id == patient_data["id"]).first()
        
        if not patient:
            patient = Patient(
                id=patient_data["id"],
                first_name=patient_data["first_name"],
                last_name=patient_data["last_name"],
                email=patient_data["email"],
                date_of_birth=patient_data["date_of_birth"],
                phone_number=patient_data["phone_number"],
                height_cm=patient_data["height_cm"],
                is_active=True,
                invited_by_clinician_id=clinician_id,
                associated_clinic_id="385af354-bfe1-4ead-8651-92110e698e30"  # Edinburgh clinic
            )
            db.add(patient)
            db.flush()
            total_stats["patients"] += 1
            print(f"   ✓ Created patient: {patient.first_name} {patient.last_name}")
        else:
            print(f"   ✓ Found existing patient: {patient.first_name} {patient.last_name}")
        
        # Add clinician-patient association
        exists = db.execute(text("""
            SELECT 1 FROM clinician_patient_association 
            WHERE clinician_id = :clinician_id AND patient_id = :patient_id
        """), {
            "clinician_id": clinician_id,
            "patient_id": patient.id
        }).scalar()
        
        if not exists:
            db.execute(text("""
                INSERT INTO clinician_patient_association (clinician_id, patient_id)
                VALUES (:clinician_id, :patient_id)
            """), {
                "clinician_id": clinician_id,
                "patient_id": patient.id
            })
        
        # Create patient data
        scenario = patient_data["scenario"]
        
        # Weight logs
        count = create_patient_weight_logs(
            db, patient, scenario, 
            patient_data["starting_weight"], 
            patient_data["current_weight"]
        )
        total_stats["weight_logs"] += count
        
        # Appointments
        count = create_patient_appointments(db, patient, clinician_id, scenario, scheduler)
        total_stats["appointments"] += count
        
        # Chat messages
        count = create_patient_chats(db, patient, scenario)
        total_stats["chats"] += count
        
        # Side effects
        count = create_patient_side_effects(db, patient, clinician_id, scenario)
        total_stats["side_effects"] += count
        
        # Medication request
        count = create_patient_medication_request(
            db, patient, clinician_id, patient_data["medication"]
        )
        total_stats["med_requests"] += count
    
    db.commit()
    
    print("\n   Additional Patient Summary:")
    print(f"   • {total_stats['patients']} new patients created")
    print(f"   • {total_stats['weight_logs']} weight logs")
    print(f"   • {total_stats['appointments']} appointments")
    print(f"   • {total_stats['chats']} chat messages")
    print(f"   • {total_stats['side_effects']} side effect reports")
    print(f"   • {total_stats['med_requests']} medication requests")
    
    return total_stats


if __name__ == "__main__":
    # This is typically called from demo_reset.py
    db = SessionLocal()
    try:
        # Default clinician ID
        clinician_id = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
        seed_additional_patients(db, clinician_id)
    finally:
        db.close()