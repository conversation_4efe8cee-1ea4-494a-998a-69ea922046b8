#!/usr/bin/env python3
"""
Seed complete demo data including appointments, medications, and normal patients.
"""

import os
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
import random
from uuid import uuid4

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

# Import models and schemas
from app import crud, models, schemas
from app.models import Patient, Clinician, Appointment, MedicationRequest
from app.models.medication import Medication
from app.models.weight_log import WeightLog
from app.models.chat_message import ChatMessage, MessageRouteType
from app.models.side_effect_report import SideEffectReport
from app.schemas.side_effect_report import SeverityLevel

# Known user IDs
MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
MICHAEL_CLINICIAN_ID = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
CLINIC_ID = "385af354-bfe1-4ead-8651-92110e698e30"

# Demo patient data (these would need to be real Clerk users in production)
DEMO_PATIENTS = [
    {
        "id": "demo_sarah_johnson",
        "email": "<EMAIL>",
        "first_name": "Sarah",
        "last_name": "Johnson",
        "condition": "Type 2 Diabetes",
        "medication": "Ozempic",
        "weight_loss_rate": 1.5
    },
    {
        "id": "demo_robert_chen",
        "email": "<EMAIL>", 
        "first_name": "Robert",
        "last_name": "Chen",
        "condition": "Obesity",
        "medication": "Mounjaro",
        "weight_loss_rate": 2.0
    },
    {
        "id": "demo_emily_davis",
        "email": "<EMAIL>",
        "first_name": "Emily", 
        "last_name": "Davis",
        "condition": "PCOS with weight management",
        "medication": "Wegovy",
        "weight_loss_rate": 1.0
    },
    {
        "id": "demo_james_wilson",
        "email": "<EMAIL>",
        "first_name": "James",
        "last_name": "Wilson",
        "condition": "Pre-diabetes",
        "medication": "Metformin + Wegovy",
        "weight_loss_rate": 2.5
    }
]


def create_medications(db: Session):
    """Create GLP-1 medications if they don't exist."""
    print("Creating medications...")
    
    medications = [
        {
            "name": "Wegovy (semaglutide) 2.4mg",
            "description": "Weekly GLP-1 receptor agonist for chronic weight management",
            "dosage_guidelines": "Start 0.25mg weekly x4 weeks, then 0.5mg x4 weeks, then 1mg x4 weeks, then 1.7mg x4 weeks, then 2.4mg maintenance",
            "common_side_effects": "Nausea, vomiting, diarrhea, constipation, abdominal pain, headache, fatigue, dyspepsia, dizziness, abdominal distension, belching, hypoglycemia, flatulence, gastroenteritis, and gastroesophageal reflux disease",
            "category": "GLP-1 Agonist"
        },
        {
            "name": "Ozempic (semaglutide) 1mg", 
            "description": "Weekly GLP-1 receptor agonist for type 2 diabetes and cardiovascular risk reduction",
            "dosage_guidelines": "Start 0.25mg weekly x4 weeks, then 0.5mg weekly. May increase to 1mg weekly if additional glycemic control needed",
            "common_side_effects": "Nausea, vomiting, diarrhea, abdominal pain, constipation, decreased appetite",
            "category": "GLP-1 Agonist"
        },
        {
            "name": "Mounjaro (tirzepatide) 15mg",
            "description": "Weekly dual GIP/GLP-1 receptor agonist for type 2 diabetes",
            "dosage_guidelines": "Start 2.5mg weekly x4 weeks, then increase by 2.5mg increments every 4 weeks to target dose (5mg, 7.5mg, 10mg, 12.5mg, or 15mg)",
            "common_side_effects": "Nausea, diarrhea, decreased appetite, vomiting, constipation, dyspepsia, abdominal pain",
            "category": "GLP-1/GIP Agonist"
        },
        {
            "name": "Saxenda (liraglutide) 3mg",
            "description": "Daily GLP-1 receptor agonist for weight management",
            "dosage_guidelines": "Start 0.6mg daily, increase by 0.6mg weekly to 3mg daily maintenance dose",
            "common_side_effects": "Nausea, hypoglycemia, diarrhea, constipation, vomiting, headache, decreased appetite, dyspepsia, fatigue, dizziness, abdominal pain",
            "category": "GLP-1 Agonist"
        },
        {
            "name": "Metformin 500mg",
            "description": "First-line medication for type 2 diabetes that decreases hepatic glucose production",
            "dosage_guidelines": "Start 500mg daily or twice daily with meals. May increase by 500mg weekly to max 2000-2550mg daily in divided doses",
            "common_side_effects": "Diarrhea, nausea, vomiting, flatulence, abdominal discomfort, indigestion, metallic taste",
            "category": "Biguanide"
        }
    ]
    
    created_count = 0
    for med_data in medications:
        # Check if medication already exists
        existing = db.query(Medication).filter(
            Medication.name == med_data["name"]
        ).first()
        
        if not existing:
            medication = Medication(**med_data)
            db.add(medication)
            created_count += 1
    
    db.commit()
    print(f"Created {created_count} new medications")


def create_appointments_for_michael(db: Session, patient_id: str, clinician_id: str):
    """Create appointment history for Michael."""
    print("Creating appointments for Michael...")
    
    appointments = [
        # Past appointments
        {
            "days_ago": 90,
            "type": "Initial",
            "duration": 60,
            "status": "completed",
            "reason": "Weight management consultation",
            "clinician_notes": "Patient interested in GLP-1 therapy. BMI 32, no contraindications."
        },
        {
            "days_ago": 60,
            "type": "Follow-up",
            "duration": 30,
            "status": "completed", 
            "reason": "Wegovy initiation",
            "clinician_notes": "Started Wegovy 0.25mg weekly. Reviewed injection technique."
        },
        {
            "days_ago": 30,
            "type": "Follow-up",
            "duration": 30,
            "status": "completed",
            "reason": "1-month follow-up",
            "clinician_notes": "Tolerating well, mild nausea. Increased to 0.5mg weekly."
        },
        # Upcoming appointment
        {
            "days_ago": -7,  # 7 days in future
            "type": "Follow-up",
            "duration": 30,
            "status": "scheduled",
            "reason": "2-month follow-up",
            "clinician_notes": None
        }
    ]
    
    for appt in appointments:
        appointment_datetime = datetime.utcnow() + timedelta(days=appt["days_ago"])
        
        appointment = Appointment(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appointment_datetime.replace(tzinfo=timezone.utc),
            appointment_type=appt["type"],
            duration_minutes=appt["duration"],
            status=appt["status"],
            reason=appt["reason"],
            clinician_notes=appt["clinician_notes"]
        )
        db.add(appointment)
    
    db.commit()
    print(f"Created {len(appointments)} appointments for Michael")


def create_medication_requests_for_michael(db: Session, patient_id: str, clinician_id: str):
    """Create medication request history for Michael."""
    print("Creating medication requests for Michael...")
    
    requests = [
        {
            "days_ago": 60,
            "medication": "Wegovy",
            "dosage": "0.25mg",
            "frequency": "Once weekly",
            "duration": "4 weeks",
            "status": "Approved",
            "notes": "Initial dose for titration"
        },
        {
            "days_ago": 30,
            "medication": "Wegovy", 
            "dosage": "0.5mg",
            "frequency": "Once weekly",
            "duration": "4 weeks",
            "status": "Approved",
            "notes": "Dose escalation per protocol"
        },
        {
            "days_ago": 5,
            "medication": "Wegovy",
            "dosage": "1mg",
            "frequency": "Once weekly", 
            "duration": "4 weeks",
            "status": "Pending",
            "notes": "Patient requesting dose increase due to good tolerance"
        }
    ]
    
    for req in requests:
        request_date = datetime.utcnow() - timedelta(days=req["days_ago"])
        
        med_request = MedicationRequest(
            patient_id=patient_id,
            clinician_id=clinician_id if req["status"] == "Approved" else None,
            medication_name=req["medication"],
            dosage=req["dosage"],
            frequency=req["frequency"],
            duration=req["duration"],
            status=req["status"],
            notes=req["notes"],
            created_at=request_date
        )
        db.add(med_request)
    
    db.commit()
    print(f"Created {len(requests)} medication requests for Michael")


def create_demo_patients(db: Session, clinician_id: str):
    """Create normal demo patients with realistic data."""
    print("\nCreating demo patients with normal patterns...")
    
    for demo in DEMO_PATIENTS:
        # Check if patient already exists
        existing = db.query(Patient).filter(Patient.id == demo["id"]).first()
        if existing:
            print(f"Patient {demo['first_name']} {demo['last_name']} already exists")
            continue
            
        # Create patient
        patient = Patient(
            id=demo["id"],
            email=demo["email"],
            first_name=demo["first_name"],
            last_name=demo["last_name"],
            is_active=True,
            invited_by_clinician_id=clinician_id,
            associated_clinic_id=CLINIC_ID
        )
        db.add(patient)
        db.flush()
        
        # Create weight logs (normal progression)
        create_normal_weight_logs(db, patient.id, demo["weight_loss_rate"])
        
        # Create appointments
        create_normal_appointments(db, patient.id, clinician_id)
        
        # Create medication requests
        create_normal_medication_requests(db, patient.id, clinician_id, demo["medication"])
        
        # Create normal chat messages
        create_normal_chat_messages(db, patient.id, clinician_id, demo)
        
        # Create mild side effects if any
        if random.random() > 0.5:  # 50% chance of side effects
            create_normal_side_effects(db, patient.id, clinician_id, demo["medication"])
        
        print(f"Created demo patient: {demo['first_name']} {demo['last_name']}")
    
    db.commit()
    print("Demo patients created successfully")


def create_normal_weight_logs(db: Session, patient_id: str, loss_rate: float):
    """Create normal weight loss progression."""
    start_weight = random.uniform(180, 250)  # Random starting weight
    current_weight = start_weight
    
    for week in range(12):  # 12 weeks of data
        log_date = datetime.utcnow().date() - timedelta(weeks=12-week)
        
        # Normal weight loss with some variation
        week_loss = loss_rate * random.uniform(0.5, 1.5)
        current_weight -= week_loss
        
        weight_log = WeightLog(
            patient_id=patient_id,
            weight_kg=current_weight * 0.453592,  # Convert to kg
            log_date=log_date
        )
        db.add(weight_log)


def create_normal_appointments(db: Session, patient_id: str, clinician_id: str):
    """Create normal appointment pattern."""
    appointments = [
        {"weeks_ago": 12, "type": "Initial", "status": "completed"},
        {"weeks_ago": 8, "type": "Follow-up", "status": "completed"},
        {"weeks_ago": 4, "type": "Follow-up", "status": "completed"},
        {"weeks_ago": -2, "type": "Follow-up", "status": "scheduled"}  # Future
    ]
    
    for appt in appointments:
        appointment_datetime = datetime.utcnow() + timedelta(weeks=appt["weeks_ago"])
        
        appointment = Appointment(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appointment_datetime.replace(tzinfo=timezone.utc),
            appointment_type=appt["type"],
            duration_minutes=30,
            status=appt["status"],
            reason="Regular follow-up"
        )
        db.add(appointment)


def create_normal_medication_requests(db: Session, patient_id: str, clinician_id: str, medication: str):
    """Create normal medication requests."""
    med_request = MedicationRequest(
        patient_id=patient_id,
        clinician_id=clinician_id,
        medication_name=medication.split(" + ")[0],  # Take first med if combination
        dosage="Standard dose",
        frequency="As prescribed",
        duration="3 months",
        status="Approved",
        created_at=datetime.utcnow() - timedelta(weeks=8)
    )
    db.add(med_request)


def create_normal_chat_messages(db: Session, patient_id: str, clinician_id: str, demo: dict):
    """Create normal, positive chat interactions."""
    messages = [
        {
            "days_ago": 14,
            "patient_message": f"Hi Dr. Isaac, I'm doing well on the {demo['medication']}. Down {demo['weight_loss_rate']*2:.1f} pounds so far!",
            "clinician_response": "That's excellent progress! Keep up the good work. Any side effects?"
        },
        {
            "days_ago": 7,
            "patient_message": "Just some mild nausea occasionally, but it's manageable. Appetite is definitely reduced.",
            "clinician_response": "That's normal and should improve over time. Make sure to stay hydrated."
        }
    ]
    
    for msg in messages:
        timestamp = datetime.utcnow() - timedelta(days=msg["days_ago"])
        
        # Patient message
        patient_msg = ChatMessage(
            patient_id=patient_id,
            sender_type="patient",
            message_content=msg["patient_message"],
            created_at=timestamp,
            message_route=MessageRouteType.CLINICIAN,
            is_read_by_clinician=True
        )
        db.add(patient_msg)
        
        # Clinician response
        clinician_msg = ChatMessage(
            patient_id=patient_id,
            sender_type="clinician",
            message_content=msg["clinician_response"],
            created_at=timestamp + timedelta(hours=2),
            message_route=MessageRouteType.PATIENT,
            is_read_by_clinician=True
        )
        db.add(clinician_msg)


def create_normal_side_effects(db: Session, patient_id: str, clinician_id: str, medication: str):
    """Create mild, normal side effects."""
    report = SideEffectReport(
        patient_id=patient_id,
        clinician_id=clinician_id,
        description=f"{medication}: Mild nausea after injection, resolves within a few hours",
        severity=SeverityLevel.MINOR,
        reported_at=datetime.utcnow() - timedelta(weeks=4)
    )
    db.add(report)


def main():
    """Main function to seed complete demo data."""
    print("="*60)
    print("SEEDING COMPLETE DEMO DATA")
    print("="*60)
    
    db = SessionLocal()
    
    try:
        # Verify users exist
        patient = db.query(Patient).filter(Patient.id == MICHAEL_PATIENT_ID).first()
        clinician = db.query(Clinician).filter(Clinician.clerk_id == MICHAEL_CLINICIAN_ID).first()
        
        if not patient or not clinician:
            print("ERROR: Michael Patient or Clinician not found!")
            return
            
        # Create all the missing data
        create_medications(db)
        create_appointments_for_michael(db, patient.id, clinician.id)
        create_medication_requests_for_michael(db, patient.id, clinician.id)
        create_demo_patients(db, clinician.id)
        
        print("\n" + "="*60)
        print("DEMO DATA SEEDING COMPLETE!")
        print("="*60)
        print("\nSummary:")
        print("- Created medications (Wegovy, Ozempic, Mounjaro, etc.)")
        print("- Created appointments for Michael (3 past, 1 future)")
        print("- Created medication requests for Michael")
        print("- Created 4 demo patients with normal patterns")
        print("\nThe demo is ready to showcase!")
        
    except Exception as e:
        print(f"Error seeding demo data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()