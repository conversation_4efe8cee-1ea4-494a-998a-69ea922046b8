#!/usr/bin/env python3
"""
Demo Data Seeding Script for PulseTrack Investor Demo

This script creates comprehensive demo data for showcasing PulseTrack's capabilities.
It works with existing Clerk users or can be adapted for demo users.

IMPORTANT: Requires valid Clerk user IDs for patients and clinicians.

Demo User Setup Options:
1. Use existing Clerk users (current approach)
2. Create Clerk users via Clerk Dashboard or API
3. Implement demo mode with bypass authentication (future enhancement)
"""

import asyncio
import logging
import random
import sys
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo  # Python 3.9+ timezone handling
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import uuid4

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import IntegrityError

from app import crud, models, schemas
from app.core.config import settings
from app.db.session import SessionLocal
import os

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    # Replace Docker hostname with localhost for local execution
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    # Reload settings to pick up the change
    from app.core.config import Settings
    settings = Settings()
from app.models import (
    Patient, Clinician, Medication, 
    Appointment, MedicationRequest
)
from app.models.clinic import Clinic
from app.models.weight_log import WeightLog

# Import the shared appointment scheduler
from shared_appointment_scheduler import get_shared_scheduler
from app.models.side_effect_report import SideEffectReport
from app.models.chat_message import ChatMessage, MessageSenderType, MessageRouteType
from app.models.clinic_medication_association import ClinicMedicationAssociation
from app.models.medication_request import MedicationRequestStatus
from app.models.side_effect_report import SeverityLevel, SideEffectStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Demo data configuration
DEMO_CONFIG = {
    "use_existing_users": True,  # Set to False if creating new Clerk users
    "existing_patient_id": "user_2waTCuGL3kQC9k2rY47INdcJXk5",  # Michael Patient
    "existing_clinician_id": "user_2waSREJSlduBPyK6Vbv9TU3VhI7",  # Michael Isaac, MD
    "existing_clinic_id": "385af354-bfe1-4ead-8651-92110e698e30",  # Edinburgh Weight Loss Clinic
}

# If creating new users in Clerk, use these IDs (must match Clerk user IDs)
DEMO_USERS = {
    "patients": [
        {"id": "demo_patient_emma", "first_name": "Emma", "last_name": "Thompson", "email": "<EMAIL>"},
        {"id": "demo_patient_marcus", "first_name": "Marcus", "last_name": "Johnson", "email": "<EMAIL>"},
        {"id": "demo_patient_sarah", "first_name": "Sarah", "last_name": "Kim", "email": "<EMAIL>"},
        {"id": "demo_patient_robert", "first_name": "Robert", "last_name": "Chen", "email": "<EMAIL>"},
    ],
    "clinicians": [
        {"id": "demo_clinician_sarah", "first_name": "Dr. Sarah", "last_name": "Chen", "email": "<EMAIL>"},
        {"id": "demo_clinician_james", "first_name": "Dr. James", "last_name": "Wilson", "email": "<EMAIL>"},
    ]
}

# Medications for the demo
DEMO_MEDICATIONS = [
    {"name": "Wegovy (Semaglutide)", "category": "GLP-1 Agonist", "typical_dosage": "0.25mg-2.4mg weekly"},
    {"name": "Ozempic (Semaglutide)", "category": "GLP-1 Agonist", "typical_dosage": "0.5mg-2mg weekly"},
    {"name": "Mounjaro (Tirzepatide)", "category": "GIP/GLP-1 Agonist", "typical_dosage": "2.5mg-15mg weekly"},
    {"name": "Saxenda (Liraglutide)", "category": "GLP-1 Agonist", "typical_dosage": "0.6mg-3mg daily"},
    {"name": "Contrave (Naltrexone/Bupropion)", "category": "Combination", "typical_dosage": "8mg/90mg twice daily"},
    {"name": "Qsymia (Phentermine/Topiramate)", "category": "Combination", "typical_dosage": "3.75mg/23mg-15mg/92mg daily"},
    {"name": "Xenical (Orlistat)", "category": "Lipase Inhibitor", "typical_dosage": "120mg three times daily"},
    {"name": "Phentermine", "category": "Sympathomimetic", "typical_dosage": "15mg-37.5mg daily"},
]


class DemoDataSeeder:
    def __init__(self, db: Session):
        self.db = db
        self.patients = []
        self.clinicians = []
        self.medications = []
        self.clinic = None
        
    def seed_all(self):
        """Main method to seed all demo data"""
        logger.info("Starting demo data seeding...")
        
        try:
            # Step 1: Setup users and clinic
            self._setup_users_and_clinic()
            
            # Step 2: Seed medications
            self._seed_medications()
            
            # Step 3: Create patient histories
            self._create_patient_histories()
            
            # Step 4: Create appointments
            self._create_appointments()
            
            # Step 5: Create chat conversations
            self._create_chat_conversations()
            
            # Step 6: Create side effect reports
            self._create_side_effect_reports()
            
            # Step 7: Create medication requests
            self._create_medication_requests()
            
            logger.info("Demo data seeding completed successfully!")
            self._print_summary()
            
        except Exception as e:
            logger.error(f"Error seeding demo data: {e}")
            self.db.rollback()
            raise
    
    def _setup_users_and_clinic(self):
        """Setup users and clinic based on configuration"""
        if DEMO_CONFIG["use_existing_users"]:
            # Use existing users
            logger.info("Using existing Clerk users...")
            
            # Get existing patient
            patient = self.db.query(Patient).filter_by(id=DEMO_CONFIG["existing_patient_id"]).first()
            if patient:
                self.patients.append(patient)
                logger.info(f"Found existing patient: {patient.first_name} {patient.last_name}")
            
            # Get existing clinician
            clinician = self.db.query(Clinician).filter_by(id=DEMO_CONFIG["existing_clinician_id"]).first()
            if clinician:
                self.clinicians.append(clinician)
                logger.info(f"Found existing clinician: {clinician.first_name} {clinician.last_name}")
            
            # Get existing clinic
            self.clinic = self.db.query(Clinic).filter_by(id=DEMO_CONFIG["existing_clinic_id"]).first()
            if self.clinic:
                logger.info(f"Found existing clinic: {self.clinic.name}")
        
        else:
            # Create demo users (requires corresponding Clerk users)
            logger.info("Creating demo users (ensure Clerk users exist)...")
            
            # Create demo patients
            for patient_data in DEMO_USERS["patients"]:
                patient = Patient(**patient_data)
                self.db.add(patient)
                self.patients.append(patient)
            
            # Create demo clinicians
            for clinician_data in DEMO_USERS["clinicians"]:
                clinician = Clinician(**clinician_data, clerk_id=clinician_data["id"])
                self.db.add(clinician)
                self.clinicians.append(clinician)
            
            # Create demo clinic
            self.clinic = Clinic(
                id=uuid4(),
                name="PulseTrack Demo Clinic",
                address="123 Demo Street, Demo City, DC 12345",
                phone="******-DEMO-123",
                email="<EMAIL>"
            )
            self.db.add(self.clinic)
            
            self.db.commit()
    
    def _seed_medications(self):
        """Seed medications database"""
        logger.info("Seeding medications...")
        
        for med_data in DEMO_MEDICATIONS:
            # Check if medication already exists
            existing = self.db.query(Medication).filter_by(name=med_data["name"]).first()
            if not existing:
                medication = Medication(
                    id=uuid4(),
                    name=med_data["name"],
                    category=med_data["category"],
                    description=f"Typical dosage: {med_data['typical_dosage']}"
                )
                self.db.add(medication)
                self.medications.append(medication)
                logger.info(f"Added medication: {med_data['name']}")
            else:
                self.medications.append(existing)
                logger.info(f"Medication already exists: {med_data['name']}")
        
        self.db.commit()
        
        # Associate medications with clinic
        for medication in self.medications:
            assoc = self.db.query(ClinicMedicationAssociation).filter_by(
                clinic_id=self.clinic.id,
                medication_id=medication.id
            ).first()
            if not assoc:
                assoc = ClinicMedicationAssociation(
                    clinic_id=self.clinic.id,
                    medication_id=medication.id,
                    notes=f"Available at {self.clinic.name} - £{random.uniform(25.0, 150.0):.2f}/week"
                )
                self.db.add(assoc)
        
        self.db.commit()
    
    def _create_patient_histories(self):
        """Create comprehensive patient histories with weight logs"""
        logger.info("Creating patient histories...")
        
        if not self.patients:
            logger.warning("No patients available for history creation")
            return
        
        # For each patient, create 3-6 months of weight logs
        for patient in self.patients:
            # Skip Michael Patient - investor demo scenario will handle his weight logs
            if patient.id == "user_2waTCuGL3kQC9k2rY47INdcJXk5":
                logger.info(f"Skipping weight logs for Michael Patient - will be created by investor demo scenario")
                continue
                
            start_date = datetime.now(timezone.utc) - timedelta(days=180)
            initial_weight = random.uniform(180, 280)  # Starting weight in lbs
            current_weight = initial_weight
            
            # Create weight logs showing progress
            for days_ago in range(180, -1, -7):  # Weekly logs
                log_date = datetime.now(timezone.utc) - timedelta(days=days_ago)
                
                # Simulate weight loss progress with some fluctuation
                weight_change = random.uniform(-2.5, -0.5) if days_ago > 30 else random.uniform(-1.5, 0.5)
                current_weight += weight_change
                current_weight = max(current_weight, initial_weight * 0.85)  # Max 15% loss
                
                weight_log = WeightLog(
                    id=uuid4(),
                    patient_id=patient.id,
                    weight_kg=current_weight * 0.453592,  # Convert lbs to kg
                    log_date=log_date.date()
                )
                self.db.add(weight_log)
            
            logger.info(f"Created weight history for patient: {patient.first_name}")
        
        self.db.commit()
    
    
    def _create_appointments(self):
        """Create past and future appointments with realistic scheduling"""
        logger.info("Creating appointments with proper scheduling...")
        
        if not self.patients or not self.clinicians:
            logger.warning("Need both patients and clinicians for appointments")
            return
        
        # Get the shared scheduler
        scheduler = get_shared_scheduler()
        appointment_count = 0
        
        # Skip Michael Patient appointments - investor demo scenario will handle them
        michael_patient = next((p for p in self.patients if p.id == "user_2waTCuGL3kQC9k2rY47INdcJXk5"), None)
        if michael_patient:
            logger.info("Skipping appointments for Michael Patient - will be created by investor demo scenario")
            return
        
        # For other patients, create appointments
        if self.clinicians:
            clinician = self.clinicians[0]
            
            # Define clinic timezone (US Central Time for Edinburgh clinic)
            clinic_tz = ZoneInfo("America/Chicago")
            
            # Past appointments for Michael (showing regular care)
            past_appointments = [
                (90, "Initial Weight Management Consultation", "Completed"),
                (60, "1-Month Follow-up", "Completed"),
                (30, "2-Month Progress Review", "Completed"),
                (7, "Urgent: Side Effect Evaluation", "Completed"),  # Recent concerning visit
            ]
            
            for days_ago, reason, status in past_appointments:
                target_date = (datetime.now() - timedelta(days=days_ago)).date()
                
                # For past appointments, use proper clinic times (on the hour or half-hour)
                # Distribute across clinic hours: 9:00, 9:30, 10:00, 10:30, etc.
                hour = 9 + (days_ago % 8)  # Spread across 9 AM to 4 PM
                minute = 0 if days_ago % 2 == 0 else 30  # Alternate between :00 and :30
                
                # Create appointment in clinic's timezone
                appointment_datetime = datetime.combine(
                    target_date,
                    datetime.strptime(f"{hour}:{minute:02d}", "%H:%M").time(),
                    tzinfo=clinic_tz
                )
                
                # Convert to UTC for storage
                appointment_datetime_utc = appointment_datetime.astimezone(timezone.utc)
                
                appointment = Appointment(
                    id=uuid4(),
                    patient_id=michael_patient.id,
                    clinician_id=clinician.id,
                    appointment_datetime=appointment_datetime_utc,
                    duration_minutes=45 if "Initial" in reason else 30,
                    appointment_type="Consultation" if "Initial" in reason else "Follow-up",
                    status=status,
                    reason=reason,
                    clinician_notes="Patient discussed concerning symptoms" if "Urgent" in reason else None
                )
                self.db.add(appointment)
                appointment_count += 1
            
            # Future appointment for Michael (urgent follow-up)
            future_date = (datetime.now() + timedelta(days=2)).date()
            future_datetime = scheduler.schedule_appointment(
                target_date=future_date,
                appointment_type="Follow-up",
                prefer_morning=True,
                urgent=True
            )
            
            if future_datetime:
                # Add timezone info to the scheduled time and convert to UTC
                future_datetime_tz = future_datetime.replace(tzinfo=clinic_tz)
                future_datetime_utc = future_datetime_tz.astimezone(timezone.utc)
                    
                appointment = Appointment(
                    id=uuid4(),
                    patient_id=michael_patient.id,
                    clinician_id=clinician.id,
                    appointment_datetime=future_datetime_utc,
                    duration_minutes=30,
                    appointment_type="Follow-up",
                    status="Scheduled",
                    reason="URGENT: Follow-up for rapid weight loss"
                )
                self.db.add(appointment)
                appointment_count += 1
        
        self.db.commit()
        logger.info(f"Created {appointment_count} appointments for Michael Patient")
    
    def _create_chat_conversations(self):
        """Create realistic chat conversations"""
        logger.info("Creating chat conversations...")
        
        if not self.patients or not self.clinicians:
            logger.warning("Need both patients and clinicians for chat")
            return
        
        # Sample conversation templates
        # Some conversations include follow-up patient messages to show unread/pending states
        conversations = [
            {
                "messages": [
                    {"type": "patient", "content": "How much does Mounjaro cost at your clinic?"},
                    {"type": "agent", "content": "Mounjaro at our clinic costs £32 per week. This includes:\n- Weekly medication supply\n- Ongoing clinical monitoring\n- Side effect management support\n- Dosage adjustments as needed\n\nWould you like to schedule a consultation to discuss if Mounjaro is right for you?"},
                    {"type": "patient", "content": "Yes, I'd like to schedule a consultation. Do you have anything available this week?"}  # Last message from patient
                ]
            },
            {
                "messages": [
                    {"type": "patient", "content": "I've been feeling nauseous after my Wegovy injections"},
                    {"type": "agent", "content": "I understand the nausea can be uncomfortable. Here are some strategies that often help:\n\n1. **Eat smaller, more frequent meals**\n2. **Avoid fatty or spicy foods**\n3. **Stay hydrated with clear fluids**\n4. **Try ginger tea or peppermint**\n\nIf the nausea is severe or persists, we may need to adjust your dose. How long have you been experiencing this?"},
                    {"type": "patient", "content": "It's been about 3 days now. The nausea is worse in the mornings."}  # Last message from patient
                ]
            },
            {
                "messages": [
                    {"type": "patient", "content": "Can I schedule an appointment for next week?"},
                    {"type": "agent", "content": "I'd be happy to help you schedule an appointment. I have the following slots available next week:\n- Tuesday at 2:00 PM\n- Wednesday at 10:00 AM\n- Thursday at 3:30 PM\n\nWhich time works best for you?"}
                ]  # This conversation ends with agent response
            }
        ]
        
        # Use raw SQL for precise timestamp control
        from sqlalchemy import text
        
        # Create conversations for multiple patients to show red dots
        patients_with_unread = self.patients[:2] if len(self.patients) >= 2 else self.patients
        
        for patient_idx, patient in enumerate(patients_with_unread):
            clinician = self.clinicians[0] if self.clinicians else None
            
            for conv_idx, conv in enumerate(conversations):
                # Calculate base timestamp for this conversation
                # Space conversations across different days
                days_ago = 30 - (conv_idx * 10) - (patient_idx * 5)  # Vary by patient too
                hours_offset = random.randint(8, 17)
                base_timestamp = datetime.now(timezone.utc) - timedelta(days=days_ago, hours=hours_offset)
                
                # Process each message in the conversation
                current_timestamp = base_timestamp
                for msg_idx, msg in enumerate(conv["messages"]):
                    msg_id = str(uuid4())
                    
                    if msg["type"] == "patient":
                        self.db.execute(
                            text("""
                                INSERT INTO chat_messages 
                                (id, patient_id, sender_type, message_content, 
                                 created_at, updated_at, is_read_by_clinician, message_route)
                                VALUES 
                                (:id, :patient_id, 'PATIENT', :content, 
                                 :created_at, :updated_at, false, 'ai')
                            """),
                            {
                                "id": msg_id,
                                "patient_id": patient.id,
                                "content": msg["content"],
                                "created_at": current_timestamp,
                                "updated_at": current_timestamp
                            }
                        )
                    else:  # agent message
                        self.db.execute(
                            text("""
                                INSERT INTO chat_messages 
                                (id, patient_id, sender_type, message_content, 
                                 created_at, updated_at, is_read_by_clinician, message_route,
                                 message_metadata)
                                VALUES 
                                (:id, :patient_id, 'AGENT', :content, 
                                 :created_at, :updated_at, false, 'patient',
                                 :metadata)
                            """),
                            {
                                "id": msg_id,
                                "patient_id": patient.id,
                                "content": msg["content"],
                                "created_at": current_timestamp,
                                "updated_at": current_timestamp,
                                "metadata": '{"ai_response": true}'
                            }
                        )
                    
                    # Add 2-5 minutes between messages
                    current_timestamp += timedelta(minutes=random.randint(2, 5))
        
        self.db.commit()
        logger.info("Chat conversations created with proper timestamps")
    
    def _create_side_effect_reports(self):
        """Create side effect reports with varying severity"""
        logger.info("Creating side effect reports...")
        
        if not self.patients:
            logger.warning("No patients available for side effect reports")
            return
        
        side_effects = [
            {"symptom": "Mild nausea", "severity": SeverityLevel.MINOR},
            {"symptom": "Dizziness when standing", "severity": SeverityLevel.MODERATE},
            {"symptom": "Persistent headache", "severity": SeverityLevel.MODERATE},
            {"symptom": "Severe abdominal pain", "severity": SeverityLevel.MAJOR},
        ]
        
        for patient in self.patients:
            # 50% chance of having side effects
            if random.random() < 0.5:
                effect = random.choice(side_effects)
                medication = random.choice(self.medications) if self.medications else None
                
                clinician = self.clinicians[0] if self.clinicians else None
                if clinician:
                    report = SideEffectReport(
                        id=uuid4(),
                        patient_id=patient.id,
                        clinician_id=clinician.id,
                        description=effect["symptom"],
                        severity=effect["severity"],
                        reported_at=datetime.now(timezone.utc) - timedelta(days=random.randint(0, 7)),
                        status=SideEffectStatus.SUBMITTED
                    )
                self.db.add(report)
        
        self.db.commit()
        logger.info("Side effect reports created")
    
    def _create_medication_requests(self):
        """Create medication requests in various states"""
        logger.info("Creating medication requests...")
        
        if not self.patients or not self.medications:
            logger.warning("Need patients and medications for requests")
            return
        
        for patient in self.patients:
            medication = random.choice(self.medications)
            clinician = self.clinicians[0] if self.clinicians else None
            
            # Create an approved request
            request = MedicationRequest(
                id=uuid4(),
                patient_id=patient.id,
                medication_name=medication.name,
                dosage="Starting dose",
                frequency="Once weekly",
                duration="3 months",
                notes="Weight management - Patient meets criteria, approved for treatment",
                status=MedicationRequestStatus.APPROVED,
                clinician_id=clinician.id if clinician else None
            )
            self.db.add(request)
            
            # Some patients might have a pending request
            if random.random() < 0.3:
                new_request = MedicationRequest(
                    id=uuid4(),
                    patient_id=patient.id,
                    medication_name=random.choice(self.medications).name,
                    dosage="Dose adjustment",
                    frequency="Once weekly",
                    duration="3 months",
                    notes="Increase dose due to plateau",
                    status=MedicationRequestStatus.PENDING
                )
                self.db.add(new_request)
        
        self.db.commit()
        logger.info("Medication requests created")
    
    def _print_summary(self):
        """Print summary of seeded data"""
        logger.info("\n=== Demo Data Summary ===")
        logger.info(f"Patients: {len(self.patients)}")
        logger.info(f"Clinicians: {len(self.clinicians)}")
        logger.info(f"Medications: {len(self.medications)}")
        
        # Count created records
        weight_logs = self.db.query(WeightLog).count()
        appointments = self.db.query(Appointment).count()
        chat_messages = self.db.query(ChatMessage).count()
        side_effects = self.db.query(SideEffectReport).count()
        med_requests = self.db.query(MedicationRequest).count()
        
        logger.info(f"Weight Logs: {weight_logs}")
        logger.info(f"Appointments: {appointments}")
        logger.info(f"Chat Messages: {chat_messages}")
        logger.info(f"Side Effect Reports: {side_effects}")
        logger.info(f"Medication Requests: {med_requests}")
        logger.info("========================\n")


def main():
    """Main function to run the demo data seeding"""
    print("""
    ╔═══════════════════════════════════════════════════════════════════╗
    ║              PulseTrack Demo Data Seeder                          ║
    ╠═══════════════════════════════════════════════════════════════════╣
    ║  This script will seed comprehensive demo data for the investor   ║
    ║  demonstration. It requires valid Clerk user IDs.                 ║
    ║                                                                   ║
    ║  Current Configuration:                                           ║
    ║  - Use Existing Users: {}                                    ║
    ║  - Existing Patient ID: {}...                     ║
    ║  - Existing Clinician ID: {}...                   ║
    ╚═══════════════════════════════════════════════════════════════════╝
    """.format(
        DEMO_CONFIG["use_existing_users"],
        DEMO_CONFIG["existing_patient_id"][:20],
        DEMO_CONFIG["existing_clinician_id"][:20]
    ))
    
    response = input("\nProceed with demo data seeding? (yes/no): ")
    if response.lower() != 'yes':
        print("Seeding cancelled.")
        return
    
    # Create database session
    db = SessionLocal()
    
    try:
        seeder = DemoDataSeeder(db)
        seeder.seed_all()
        print("\n✅ Demo data seeding completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()