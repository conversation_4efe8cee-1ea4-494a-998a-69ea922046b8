#!/usr/bin/env python3
"""
Create demo patients with Clerk-like IDs that will work in the database
but won't be able to authenticate. Perfect for demo purposes where only
the clinician logs in to see multiple patients.
"""

import os
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
import random
import string

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

# Import models and schemas to ensure all dependencies are loaded
from app import crud, models, schemas

# Import specific models
from app.models import Patient, Clinician
from app.models.weight_log import WeightLog
from app.models.chat_message import ChatMessage, MessageRouteType
from app.models.side_effect_report import SideEffectReport
from app.models.medication_request import MedicationRequest
from app.models.appointment import Appointment
from app.schemas.side_effect_report import SeverityLevel

# Real IDs
MICHAEL_CLINICIAN_ID = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
CLINIC_ID = "385af354-bfe1-4ead-8651-92110e698e30"

def generate_clerk_like_id():
    """Generate an ID that looks like a Clerk user ID."""
    # Clerk IDs are in format: user_2waTCuGL3kQC9k2rY47INdcJXk5
    # Starting with user_2, then 28 random characters
    chars = string.ascii_letters + string.digits
    random_part = ''.join(random.choice(chars) for _ in range(28))
    return f"user_2{random_part}"

# Demo patients with Clerk-like IDs
DEMO_PATIENTS = [
    {
        "id": generate_clerk_like_id(),
        "email": "<EMAIL>",
        "first_name": "Sarah",
        "last_name": "Johnson",
        "condition": "Type 2 Diabetes",
        "medication": "Ozempic",
        "weight_loss_rate": 1.5,
        "start_weight": 195
    },
    {
        "id": generate_clerk_like_id(),
        "email": "<EMAIL>", 
        "first_name": "Robert",
        "last_name": "Chen",
        "condition": "Obesity",
        "medication": "Mounjaro",
        "weight_loss_rate": 2.0,
        "start_weight": 245
    },
    {
        "id": generate_clerk_like_id(),
        "email": "<EMAIL>",
        "first_name": "Emily", 
        "last_name": "Davis",
        "condition": "PCOS with weight management",
        "medication": "Wegovy",
        "weight_loss_rate": 1.0,
        "start_weight": 170
    },
    {
        "id": generate_clerk_like_id(),
        "email": "<EMAIL>",
        "first_name": "James",
        "last_name": "Wilson",
        "condition": "Pre-diabetes",
        "medication": "Wegovy",
        "weight_loss_rate": 2.5,
        "start_weight": 220
    }
]


def create_demo_patient(db: Session, demo: dict, clinician_id: str):
    """Create a single demo patient with all their data."""
    
    print(f"\nCreating demo patient: {demo['first_name']} {demo['last_name']}")
    print(f"  ID: {demo['id']}")
    
    # Create patient
    patient = Patient(
        id=demo["id"],
        email=demo["email"],
        first_name=demo["first_name"],
        last_name=demo["last_name"],
        is_active=True,
        invited_by_clinician_id=clinician_id,
        associated_clinic_id=CLINIC_ID,
        height_cm=170 + random.randint(-10, 10),  # Random height around 170cm
        date_of_birth=datetime.utcnow().date() - timedelta(days=365 * random.randint(30, 60))
    )
    db.add(patient)
    db.flush()
    
    # Create weight logs showing normal progression
    create_normal_weight_logs(db, demo)
    
    # Create appointments
    create_normal_appointments(db, demo["id"], clinician_id)
    
    # Create medication requests
    create_normal_medication_requests(db, demo, clinician_id)
    
    # Create positive chat interactions
    create_normal_chat_messages(db, demo, clinician_id)
    
    # Create mild side effects for some patients
    if random.random() > 0.5:
        create_mild_side_effects(db, demo["id"], clinician_id, demo["medication"])
    
    db.commit()
    print(f"  ✅ Created successfully with normal health progression")


def create_normal_weight_logs(db: Session, demo: dict):
    """Create weight logs showing healthy, steady progress."""
    current_weight = demo["start_weight"]
    
    # Create 12 weeks of weight logs
    for week in range(12):
        # Calculate date (12 weeks ago to now)
        log_date = datetime.utcnow().date() - timedelta(weeks=12-week)
        
        # Normal weight loss with realistic variation
        weekly_loss = demo["weight_loss_rate"] * random.uniform(0.5, 1.2)
        
        # Some weeks might have no loss or slight gain (realistic)
        if random.random() < 0.15:  # 15% chance
            weekly_loss = -random.uniform(0, 0.5)  # Small gain
        
        current_weight -= weekly_loss
        
        weight_log = WeightLog(
            patient_id=demo["id"],
            weight_kg=current_weight * 0.453592,  # Convert lbs to kg
            log_date=log_date
        )
        db.add(weight_log)


def create_normal_appointments(db: Session, patient_id: str, clinician_id: str):
    """Create a normal appointment pattern."""
    appointments = [
        {
            "weeks_ago": 12,
            "type": "Initial",
            "status": "completed",
            "duration": 45,
            "notes": "Initial consultation for weight management. Patient motivated and ready to start treatment."
        },
        {
            "weeks_ago": 8,
            "type": "Follow-up",
            "status": "completed",
            "duration": 30,
            "notes": "Good progress, tolerating medication well. Mild nausea resolved."
        },
        {
            "weeks_ago": 4,
            "type": "Follow-up",
            "status": "completed",
            "duration": 30,
            "notes": "Excellent adherence. Weight loss on track. No significant side effects."
        },
        {
            "weeks_ago": -2,  # 2 weeks in future
            "type": "Follow-up",
            "status": "scheduled",
            "duration": 30,
            "notes": None
        }
    ]
    
    for appt in appointments:
        appointment_datetime = datetime.utcnow() + timedelta(weeks=appt["weeks_ago"])
        
        appointment = Appointment(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appointment_datetime.replace(tzinfo=timezone.utc),
            appointment_type=appt["type"],
            duration_minutes=appt["duration"],
            status=appt["status"],
            reason="Routine follow-up for weight management",
            clinician_notes=appt["notes"]
        )
        db.add(appointment)


def create_normal_medication_requests(db: Session, demo: dict, clinician_id: str):
    """Create approved medication requests."""
    requests = [
        {
            "weeks_ago": 12,
            "status": "Approved",
            "dosage": "Starting dose",
            "notes": "Initial prescription as discussed"
        },
        {
            "weeks_ago": 8,
            "status": "Approved",
            "dosage": "Increased dose",
            "notes": "Dose escalation per protocol"
        },
        {
            "weeks_ago": 1,
            "status": "Approved",
            "dosage": "Maintenance dose",
            "notes": "Continuing current dose, good response"
        }
    ]
    
    for req in requests:
        med_request = MedicationRequest(
            patient_id=demo["id"],
            clinician_id=clinician_id,
            medication_name=demo["medication"],
            dosage=req["dosage"],
            frequency="As prescribed",
            duration="3 months",
            status=req["status"],
            notes=req["notes"],
            created_at=datetime.utcnow() - timedelta(weeks=req["weeks_ago"])
        )
        db.add(med_request)


def create_normal_chat_messages(db: Session, demo: dict, clinician_id: str):
    """Create positive, normal chat interactions."""
    
    # Patient-clinician conversations
    conversations = [
        {
            "days_ago": 21,
            "patient": f"Hi Dr. Isaac, just wanted to update you. I'm down {int(demo['weight_loss_rate']*3)} pounds since starting {demo['medication']}!",
            "clinician": "That's wonderful progress! How are you feeling overall? Any side effects?"
        },
        {
            "days_ago": 14,
            "patient": "Feeling great! Energy levels are up and clothes are fitting better. Just mild nausea occasionally.",
            "clinician": "Excellent! The nausea should continue to improve. Keep up the great work!"
        },
        {
            "days_ago": 7,
            "patient": "Quick question - is it okay to increase my exercise routine? I'm feeling more energetic.",
            "clinician": "Absolutely! Gradual increases in activity are encouraged. Start slow and listen to your body."
        }
    ]
    
    for conv in conversations:
        timestamp = datetime.utcnow() - timedelta(days=conv["days_ago"])
        
        # Patient message to clinician
        patient_msg = ChatMessage(
            patient_id=demo["id"],
            sender_type="patient",
            message_content=conv["patient"],
            created_at=timestamp,
            message_route=MessageRouteType.CLINICIAN,
            is_read_by_clinician=True
        )
        db.add(patient_msg)
        
        # Clinician response
        clinician_msg = ChatMessage(
            patient_id=demo["id"],
            sender_type="clinician",
            message_content=conv["clinician"],
            created_at=timestamp + timedelta(hours=2),
            message_route=MessageRouteType.PATIENT,
            is_read_by_clinician=True
        )
        db.add(clinician_msg)
    
    # Patient-AI conversations (positive tone)
    ai_conversations = [
        {
            "days_ago": 10,
            "patient": "Can you remind me about the best time to take my injection?",
            "ai": f"For {demo['medication']}, it's best to take your weekly injection on the same day each week. Many patients find morning injections work well, but you can choose any time that fits your schedule. The key is consistency!"
        },
        {
            "days_ago": 3,
            "patient": "I'm going to a wedding next week. Any tips for managing my diet?",
            "ai": "Congratulations on the invitation! For special events: eat a healthy snack before going, stay hydrated, enjoy small portions of your favorites, and focus on socializing. Your medication will help with appetite control. Have fun!"
        }
    ]
    
    for conv in ai_conversations:
        timestamp = datetime.utcnow() - timedelta(days=conv["days_ago"])
        
        # Patient to AI
        patient_msg = ChatMessage(
            patient_id=demo["id"],
            sender_type="patient",
            message_content=conv["patient"],
            created_at=timestamp,
            message_route=MessageRouteType.AI,
            is_read_by_clinician=False
        )
        db.add(patient_msg)
        
        # AI response
        ai_msg = ChatMessage(
            patient_id=demo["id"],
            sender_type="agent",
            message_content=conv["ai"],
            created_at=timestamp + timedelta(minutes=1),
            message_route=MessageRouteType.PATIENT,
            is_read_by_clinician=False,
            message_metadata={"context_type": "patient_education", "sentiment": "positive"}
        )
        db.add(ai_msg)


def create_mild_side_effects(db: Session, patient_id: str, clinician_id: str, medication: str):
    """Create mild, manageable side effects."""
    report = SideEffectReport(
        patient_id=patient_id,
        clinician_id=clinician_id,
        description=f"{medication}: Mild nausea for 1-2 hours after injection. Improving each week.",
        severity=SeverityLevel.MINOR,
        reported_at=datetime.utcnow() - timedelta(weeks=6)
    )
    db.add(report)


def main():
    """Main function to create demo patients."""
    print("="*70)
    print("CREATING DEMO PATIENTS (NO AUTH REQUIRED)")
    print("="*70)
    
    db = SessionLocal()
    
    try:
        # Verify clinician exists
        clinician = db.query(Clinician).filter(Clinician.clerk_id == MICHAEL_CLINICIAN_ID).first()
        if not clinician:
            print("ERROR: Michael Clinician not found!")
            return
        
        print(f"Found clinician: Dr. {clinician.first_name} {clinician.last_name}")
        
        # Create each demo patient
        for demo in DEMO_PATIENTS:
            # Check if a patient with this email already exists
            existing = db.query(Patient).filter(Patient.email == demo["email"]).first()
            if existing:
                print(f"\nSkipping {demo['first_name']} {demo['last_name']} - email already exists")
                continue
                
            create_demo_patient(db, demo, clinician.id)
        
        print("\n" + "="*70)
        print("DEMO PATIENTS CREATED SUCCESSFULLY!")
        print("="*70)
        print("\nThese patients:")
        print("✅ Will appear in the clinician's patient list")
        print("✅ Have realistic health data and conversations")
        print("✅ Show normal progression for contrast with Michael's critical case")
        print("❌ Cannot log in (no real Clerk accounts)")
        print("\nPerfect for investor demo where only the clinician logs in!")
        
    except Exception as e:
        print(f"Error creating demo patients: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()