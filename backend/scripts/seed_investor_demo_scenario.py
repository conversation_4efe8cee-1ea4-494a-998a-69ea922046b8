#!/usr/bin/env python3
"""
Seed investor demo scenario showing AI detection of concerning patient patterns.

This script creates a scenario where:
1. <PERSON> has concerning symptoms he mentions casually to AI
2. His conversations with the clinician are routine (no mention of serious symptoms)
3. AI detects the pattern and surfaces it as an urgent alert for the clinician
"""

import os
import sys
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import random
from typing import List, Dict, Any
from uuid import UUID
import uuid

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    # Replace Docker hostname with localhost for local execution
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    # Reload settings to pick up the change
    from app.core.config import Settings
    settings = Settings()

# Import models and schemas
from app import crud, models, schemas

# Import specific models we need
from app.models import Patient, Clinician
from app.models.chat_message import ChatMessage, MessageRouteType, MessageSenderType
from app.models.patient_alert import PatientAlert
from app.models.event_log import EventLog
from app.models.weight_log import WeightLog
from app.models.side_effect_report import SideEffectReport
from app.schemas.side_effect_report import SeverityLevel, SideEffectStatus
from app.models.appointment import Appointment

# Known user IDs from existing Clerk users
MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
MICHAEL_CLINICIAN_ID = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
CLINIC_ID = "385af354-bfe1-4ead-8651-92110e698e30"

# Other demo patients (you may need to create these in Clerk first)
DEMO_PATIENTS = [
    {"id": "demo_patient_1", "name": "Sarah Johnson"},
    {"id": "demo_patient_2", "name": "Robert Chen"},
    {"id": "demo_patient_3", "name": "Emily Davis"},
    {"id": "demo_patient_4", "name": "James Wilson"}
]


def create_michael_ai_chats(db: Session, patient_id: str):
    """Create concerning chats between Michael and AI that reveal symptoms."""
    print("Creating Michael's AI chat messages revealing concerning symptoms...")
    
    base_time = datetime.utcnow() - timedelta(days=30)
    
    # Series of messages showing escalating symptoms
    chat_messages = [
        # Week 1 - Initial positive messages
        {
            "time_offset": timedelta(days=0, hours=9),
            "patient_message": "Hey! Just wanted to check in. Started the Wegovy last week and feeling pretty good so far.",
            "ai_response": "That's great to hear! Starting a new medication can be an adjustment. How has your appetite been? Any side effects you've noticed?",
            "metadata": {"action_type": "health_check", "symptoms_detected": []}
        },
        {
            "time_offset": timedelta(days=2, hours=14),
            "patient_message": "Appetite is definitely down, which I guess is the point! Had a bit of nausea the first day but it's better now.",
            "ai_response": "Some nausea is common when starting GLP-1 medications. It often improves as your body adjusts. Make sure to eat smaller, more frequent meals and stay hydrated. Let me know if it worsens.",
            "metadata": {"action_type": "symptom_monitoring", "symptoms_detected": ["mild_nausea"]}
        },
        
        # Week 2 - Weight loss starts
        {
            "time_offset": timedelta(days=7, hours=10),
            "patient_message": "Down 8 pounds already! This stuff really works. Though I've been feeling a bit tired lately.",
            "ai_response": "Congratulations on the weight loss! That's about 2 pounds per week, which is a healthy rate. Fatigue can occur with rapid weight loss. Are you eating enough protein and staying hydrated?",
            "metadata": {"action_type": "weight_monitoring", "symptoms_detected": ["fatigue", "rapid_weight_loss"], "weight_loss_rate": 2.0}
        },
        
        # Week 3 - Symptoms escalate
        {
            "time_offset": timedelta(days=14, hours=11),
            "patient_message": "Another 10 pounds down! Though the nausea is back and worse than before. Sometimes I feel my heart racing after meals.",
            "ai_response": "That's significant weight loss - over 4 pounds per week. The return of nausea and heart palpitations are concerning. Have you discussed these symptoms with Dr. Isaac? Consider scheduling an appointment soon.",
            "metadata": {"action_type": "symptom_alert", "symptoms_detected": ["severe_nausea", "palpitations", "rapid_weight_loss"], "weight_loss_rate": 4.5, "risk_level": "moderate"}
        },
        {
            "time_offset": timedelta(days=16, hours=15),
            "patient_message": "I've been getting these weird dizzy spells when I stand up. Is that normal? Also noticed my hands shake sometimes.",
            "ai_response": "Dizziness upon standing (orthostatic hypotension) and hand tremors can be serious side effects. Combined with your rapid weight loss and other symptoms, I strongly recommend contacting Dr. Isaac immediately. These symptoms need medical evaluation.",
            "metadata": {"action_type": "urgent_symptom_alert", "symptoms_detected": ["orthostatic_hypotension", "tremors", "severe_nausea", "palpitations"], "risk_level": "high"}
        },
        
        # Week 4 - Critical symptoms
        {
            "time_offset": timedelta(days=21, hours=9),
            "patient_message": "Lost another 12 pounds this week! But I've been having chest tightness, especially at night. Should I be worried?",
            "ai_response": "Chest tightness is a serious symptom that requires immediate medical attention. With your rapid weight loss (12 pounds in one week) and other symptoms, please contact Dr. Isaac immediately or consider visiting the emergency room if symptoms worsen.",
            "metadata": {"action_type": "critical_symptom_alert", "symptoms_detected": ["chest_tightness", "extreme_weight_loss", "orthostatic_hypotension", "tremors"], "weight_loss_rate": 12.0, "risk_level": "critical"}
        },
        {
            "time_offset": timedelta(days=23, hours=20),
            "patient_message": "The chest tightness comes and goes. I'm also really cold all the time now, and my wife says I look pale. But hey, down 35 pounds total!",
            "ai_response": "These symptoms - chest tightness, feeling cold, pallor, combined with losing 35 pounds in less than 4 weeks - indicate potentially serious complications. This is beyond typical side effects. Please seek immediate medical care. I'm flagging this for urgent clinician review.",
            "metadata": {
                "action_type": "emergency_alert", 
                "symptoms_detected": ["chest_tightness", "hypothermia", "pallor", "extreme_weight_loss"],
                "weight_loss_rate": 8.75,
                "risk_level": "critical",
                "clinician_alert_generated": True
            }
        },
        
        # Additional follow-up messages for enhanced logical flow
        {
            "time_offset": timedelta(days=25, hours=14),
            "patient_message": "Just checking in again. The chest tightness happened twice yesterday, and I'm getting worried. My wife is really concerned too. Should I go to urgent care?",
            "ai_response": "Yes, please go to urgent care or the emergency room immediately. Recurring chest tightness with your other symptoms (37 lb weight loss, dizziness, tremors) requires immediate medical evaluation. Don't wait - this combination of symptoms needs urgent attention.",
            "metadata": {"action_type": "urgent_care_recommendation", "symptoms_detected": ["recurring_chest_tightness", "extreme_weight_loss", "family_concern"], "risk_level": "critical", "immediate_care_advised": True}
        },
        {
            "time_offset": timedelta(days=27, hours=10),
            "patient_message": "Still haven't made it to urgent care - got busy with work. But the symptoms are getting worse. Should I be more worried?",
            "ai_response": "Michael, this is extremely concerning. Your symptoms are escalating and you've lost nearly 40 pounds in a month. This requires IMMEDIATE medical attention - please go to the ER today. I am alerting Dr. Isaac about this urgent situation that needs his immediate review.",
            "metadata": {
                "action_type": "emergency_escalation",
                "symptoms_detected": ["worsening_symptoms", "extreme_weight_loss", "patient_delay"],
                "weight_loss_rate": 9.2,
                "risk_level": "critical",
                "clinician_alert_generated": True,
                "emergency_flag": True
            }
        }
    ]
    
    for chat in chat_messages:
        timestamp = base_time + chat["time_offset"]
        
        # Patient message
        patient_msg = ChatMessage(
            patient_id=patient_id,
            sender_type=MessageSenderType.PATIENT,
            message_content=chat["patient_message"],
            created_at=timestamp,
            message_route=MessageRouteType.AI,
            is_read_by_clinician=True
        )
        db.add(patient_msg)
        db.flush()
        
        # AI response
        ai_msg = ChatMessage(
            patient_id=patient_id,
            sender_type=MessageSenderType.AGENT,
            message_content=chat["ai_response"],
            created_at=timestamp + timedelta(minutes=1),
            message_route=MessageRouteType.PATIENT,
            is_read_by_clinician=True,
            message_metadata=chat["metadata"]
        )
        db.add(ai_msg)
    
    db.commit()
    print(f"Created {len(chat_messages) * 2} AI chat messages for Michael")
    print("Enhanced chat flow includes:")
    print("  - Follow-up about chest tightness with family concern")
    print("  - Patient delay in seeking care (realistic behavior)")
    print("  - AI escalation with explicit clinician alert")


def create_michael_clinician_chats(db: Session, patient_id: str, clinician_id: str):
    """Create routine chats between Michael and clinician (no serious symptoms mentioned)."""
    print("Creating routine Michael-Clinician chat messages...")
    
    base_time = datetime.utcnow() - timedelta(days=25)
    
    # Routine check-ins that don't reveal the serious symptoms
    chat_messages = [
        {
            "time_offset": timedelta(days=0),
            "clinician_message": "Hi Michael, how are you doing with the Wegovy so far?",
            "patient_response": "Hi Dr. Isaac! Going well, I'm definitely eating less. Thanks for prescribing it!"
        },
        {
            "time_offset": timedelta(days=10),
            "patient_message": "Quick question - is it okay to take Tylenol while on Wegovy? Had a headache yesterday.",
            "clinician_response": "Yes, Tylenol (acetaminophen) is safe to take with Wegovy. Make sure you're staying well hydrated, as dehydration can cause headaches."
        },
        {
            "time_offset": timedelta(days=18),
            "clinician_message": "Just checking in - your weight log shows great progress! How are you feeling overall?",
            "patient_response": "Thanks! Yeah, the weight is coming off nicely. Feeling good overall, just trying to keep up with the diet changes."
        },
        
        # Additional recent routine exchange (showing patient doesn't mention serious symptoms to clinician)
        {
            "time_offset": timedelta(days=24),
            "patient_message": "Dr. Isaac, quick question - is it normal to feel more tired than usual? I'm getting plenty of sleep but feel low energy.",
            "clinician_response": "Some fatigue is common with rapid weight loss. Make sure you're eating enough protein and staying hydrated. If it persists or worsens, let me know and we can schedule a check-in."
        }
    ]
    
    for chat in chat_messages:
        timestamp = base_time + chat["time_offset"]
        
        if "clinician_message" in chat:
            # Clinician initiates
            clinician_msg = ChatMessage(
                patient_id=patient_id,
                sender_type=MessageSenderType.CLINICIAN,
                message_content=chat["clinician_message"],
                created_at=timestamp,
                message_route=MessageRouteType.PATIENT,
                is_read_by_clinician=True
            )
            db.add(clinician_msg)
            
            if "patient_response" in chat:
                patient_msg = ChatMessage(
                    patient_id=patient_id,
                    sender_type=MessageSenderType.PATIENT,
                    message_content=chat["patient_response"],
                    created_at=timestamp + timedelta(hours=2),
                    message_route=MessageRouteType.CLINICIAN,
                    is_read_by_clinician=True
                )
                db.add(patient_msg)
        else:
            # Patient initiates
            patient_msg = ChatMessage(
                patient_id=patient_id,
                sender_type=MessageSenderType.PATIENT,
                message_content=chat["patient_message"],
                created_at=timestamp,
                message_route=MessageRouteType.CLINICIAN,
                is_read_by_clinician=True
            )
            db.add(patient_msg)
            
            if "clinician_response" in chat:
                clinician_msg = ChatMessage(
                    patient_id=patient_id,
                    sender_type=MessageSenderType.CLINICIAN,
                    message_content=chat["clinician_response"],
                    created_at=timestamp + timedelta(hours=1),
                    message_route=MessageRouteType.PATIENT,
                    is_read_by_clinician=True
                )
                db.add(clinician_msg)
    
    db.commit()
    print(f"Created {len(chat_messages) * 2} routine clinician chat messages")
    print("Enhanced clinician chat flow includes:")
    print("  - Recent fatigue question (patient minimizes symptoms to clinician)")
    print("  - Stark contrast to serious symptoms shared with AI")


def create_michael_weight_logs(db: Session, patient_id: str):
    """Create weight logs showing dangerous rapid weight loss."""
    print("Creating concerning weight loss pattern for Michael...")
    
    # First, delete any existing weight logs for Michael to avoid conflicts
    existing_logs = db.query(WeightLog).filter(WeightLog.patient_id == patient_id).all()
    if existing_logs:
        print(f"  Removing {len(existing_logs)} existing weight logs for consistency...")
        for log in existing_logs:
            db.delete(log)
        db.commit()
    
    start_date = datetime.utcnow() - timedelta(days=30)
    start_weight = 285.0  # Starting weight in pounds
    
    # Accelerating weight loss pattern
    weight_loss_pattern = [
        # Week 1: 2-3 lbs (normal)
        {"day": 0, "weight": 285.0},
        {"day": 3, "weight": 283.5},
        {"day": 7, "weight": 282.0},
        
        # Week 2: 4-5 lbs (concerning)
        {"day": 10, "weight": 279.5},
        {"day": 14, "weight": 277.0},
        
        # Week 3: 8-10 lbs (dangerous)
        {"day": 17, "weight": 272.0},
        {"day": 21, "weight": 267.0},
        
        # Week 4: 12+ lbs (critical)
        {"day": 24, "weight": 260.0},
        {"day": 28, "weight": 250.0},
        {"day": 30, "weight": 248.0}
    ]
    
    for entry in weight_loss_pattern:
        log_date = start_date + timedelta(days=entry["day"])
        # Convert pounds to kg for storage (1 lb = 0.453592 kg)
        weight_kg = entry["weight"] * 0.453592
        weight_log = WeightLog(
            patient_id=patient_id,
            weight_kg=weight_kg,
            log_date=log_date.date()  # Use date only, not datetime
        )
        db.add(weight_log)
    
    db.commit()
    print(f"Created {len(weight_loss_pattern)} weight logs showing 37 lbs loss in 30 days")


def create_michael_side_effects(db: Session, patient_id: str, clinician_id: str):
    """Create escalating side effect reports with realistic status progression."""
    print("Creating realistic side effect reports for Michael...")
    
    # First, delete any existing side effect reports for Michael to avoid duplicates
    existing_reports = db.query(SideEffectReport).filter(SideEffectReport.patient_id == patient_id).all()
    if existing_reports:
        print(f"  Removing {len(existing_reports)} existing side effect reports for consistency...")
        for report in existing_reports:
            db.delete(report)
        db.commit()
    
    base_time = datetime.utcnow()
    
    side_effects = [
        {
            # Early side effect - resolved during 1-week follow-up
            "days_offset": -27,  # 27 days ago (3 days after starting)
            "medication": "Wegovy",
            "side_effect": "Mild nausea after injection",
            "severity": SeverityLevel.MINOR,
            "frequency": "After weekly injections",
            "notes": "Manageable, subsides within 2-3 hours",
            "status": SideEffectStatus.RESOLVED,
            "resolved_days_offset": -24,  # Resolved at 1-week appointment
            "resolution_notes": "Common initial side effect. Advised to eat smaller meals before injection. Patient reports improvement with this approach."
        },
        {
            # Mid-treatment side effect - reviewed but concerning
            "days_offset": -20,  # 20 days ago (10 days after starting)
            "medication": "Wegovy",
            "side_effect": "Increased fatigue and persistent nausea",
            "severity": SeverityLevel.MODERATE,
            "frequency": "Daily fatigue, nausea 3-4 times per week",
            "notes": "Fatigue interfering with daily activities, nausea more persistent",
            "status": SideEffectStatus.REVIEWED,
            "resolved_days_offset": None,  # Not resolved, just reviewed
            "resolution_notes": "Reviewed during patient message exchange. Advised hydration and rest. Should have scheduled follow-up but patient cancelled."
        },
        {
            # Serious side effect - reported but appointment cancelled
            "days_offset": -13,  # 13 days ago (17 days after starting)
            "medication": "Wegovy",
            "side_effect": "Heart palpitations and severe nausea",
            "severity": SeverityLevel.MAJOR,
            "frequency": "Multiple times daily, especially after meals",
            "notes": "Heart racing sensation lasting 10-15 minutes, severe nausea limiting food intake",
            "status": SideEffectStatus.SUBMITTED,  # Never reviewed due to cancelled appointment
            "resolved_days_offset": None,
            "resolution_notes": None
        },
        {
            # Critical side effect - just reported, not yet seen
            "days_offset": -6,  # 6 days ago (24 days after starting)
            "medication": "Wegovy",
            "side_effect": "Chest tightness, orthostatic hypotension, tremors",
            "severity": SeverityLevel.MAJOR,
            "frequency": "Daily episodes, worsening",
            "notes": "Chest pressure sensation, dizziness on standing, visible hand tremors. Patient reports feeling 'very unwell'",
            "status": SideEffectStatus.SUBMITTED,  # Urgent, not yet addressed
            "resolved_days_offset": None,
            "resolution_notes": None
        }
    ]
    
    created_count = 0
    for effect in side_effects:
        report_date = base_time + timedelta(days=effect["days_offset"])
        
        report = SideEffectReport(
            patient_id=patient_id,
            clinician_id=clinician_id,
            description=f"{effect['medication']}: {effect['side_effect']} - {effect['notes']}",
            severity=effect["severity"],
            reported_at=report_date,
            status=effect["status"]
        )
        
        # Add resolution data if applicable
        if effect["resolved_days_offset"] is not None:
            report.resolved_at = base_time + timedelta(days=effect["resolved_days_offset"])
            report.resolution_notes = effect["resolution_notes"]
        
        db.add(report)
        created_count += 1
    
    db.commit()
    print(f"Created {created_count} side effect reports with realistic progression:")
    print(f"  - 1 RESOLVED (early, normal adaptation)")
    print(f"  - 1 REVIEWED (concerning but not escalated)")
    print(f"  - 2 SUBMITTED (serious symptoms not yet addressed)")


def create_michael_appointments(db: Session, patient_id: str, clinician_id: str):
    """Create appointment history that aligns with Michael's 30-day weight loss journey."""
    print("Creating Michael's appointment history aligned with weight loss timeline...")
    
    # First, delete any existing appointments for Michael to avoid conflicts
    existing_appts = db.query(Appointment).filter(Appointment.patient_id == patient_id).all()
    if existing_appts:
        print(f"  Removing {len(existing_appts)} existing appointments for consistency...")
        for appt in existing_appts:
            db.delete(appt)
        db.commit()
    
    base_time = datetime.utcnow()
    
    # Appointment history that tells the story:
    # 1. Initial consultation before starting Wegovy
    # 2. Wegovy initiation appointment
    # 3. 2-week check-in (early in treatment)
    # 4. NO appointment during critical weight loss period (missed opportunity)
    # 5. Emergency appointment scheduled after AI alert
    
    appointments = [
        # 35 days ago - Initial consultation (before Wegovy)
        {
            "days_ago": 35,
            "appointment_type": "Initial",
            "duration_minutes": 60,
            "status": "Completed",
            "reason": "Weight management consultation - considering GLP-1 therapy",
            "clinician_notes": "Patient interested in medical weight loss. BMI 31.5. Discussed Wegovy. No contraindications identified. Will start next week."
        },
        # 31 days ago - Wegovy initiation (just before rapid weight loss begins)
        {
            "days_ago": 31,
            "appointment_type": "Consultation",
            "duration_minutes": 30,
            "status": "Completed",
            "reason": "Wegovy initiation visit",
            "clinician_notes": "Started Wegovy 0.25mg weekly. Reviewed injection technique. Discussed common side effects. Patient understood instructions."
        },
        # 24 days ago - 1-week check-in (early side effects)
        {
            "days_ago": 24,
            "appointment_type": "Follow-up",
            "duration_minutes": 15,
            "status": "Completed",
            "reason": "1-week Wegovy follow-up",
            "clinician_notes": "Mild nausea reported, as expected. Weight down 3 lbs. Tolerating well. Continue current dose."
        },
        # 17 days ago - Scheduled but CANCELLED (critical miss)
        {
            "days_ago": 17,
            "appointment_type": "Follow-up",
            "duration_minutes": 30,
            "status": "Cancelled",
            "reason": "2-week follow-up",
            "cancelled_at": base_time - timedelta(days=18),
            "cancellation_reason": "Patient called - feeling fine, wants to reschedule to next month",
            "clinician_notes": None  # Never seen
        },
        # TOMORROW - Emergency appointment after AI alert
        {
            "days_ago": -1,  # Future appointment
            "appointment_type": "Follow-up",
            "duration_minutes": 45,
            "status": "Scheduled",
            "reason": "URGENT: Rapid weight loss and concerning symptoms detected by AI",
            "clinician_notes": None  # Not yet seen
        }
    ]
    
    for appt_data in appointments:
        days_offset = appt_data["days_ago"]
        appt_datetime = base_time - timedelta(days=days_offset)
        
        # For past appointments, set reasonable clinic hours
        if days_offset > 0:
            # Set to morning or afternoon appointments
            hour = 9 if days_offset % 2 == 0 else 14
            appt_datetime = appt_datetime.replace(hour=hour, minute=0, second=0, microsecond=0)
        else:
            # Future appointment - urgent morning slot
            appt_datetime = appt_datetime.replace(hour=9, minute=0, second=0, microsecond=0)
        
        appointment = Appointment(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appt_datetime,
            duration_minutes=appt_data["duration_minutes"],
            appointment_type=appt_data["appointment_type"],
            status=appt_data["status"],
            reason=appt_data["reason"],
            clinician_notes=appt_data.get("clinician_notes"),
            cancelled_at=appt_data.get("cancelled_at"),
            cancellation_reason=appt_data.get("cancellation_reason")
        )
        db.add(appointment)
    
    db.commit()
    print(f"Created {len(appointments)} appointments showing missed opportunities in Michael's care")


def create_patient_alert(db: Session, patient_id: str, clinician_id: str):
    """Create AI-generated urgent alert for the clinician."""
    print("Creating AI-generated urgent alert...")
    
    alert = PatientAlert(
        patient_id=patient_id,
        alert_type="ai_detected_pattern",
        severity="critical",
        title="Critical: Dangerous Weight Loss Pattern Detected",
        description=(
            "AI has detected a concerning pattern for Michael Patient:\n\n"
            "• Extreme weight loss: 37 lbs in 30 days (8.5 lbs/week average)\n"
            "• Escalating symptoms: chest tightness, orthostatic hypotension, tremors\n"
            "• Patient mentioned symptoms to AI but not during clinician conversations\n"
            "• Risk of severe dehydration, electrolyte imbalance, or cardiac issues\n\n"
            "IMMEDIATE ACTION REQUIRED: Contact patient for urgent evaluation"
        ),
        source="AI Pattern Detection v2.0",
        status="new",
        created_at=datetime.utcnow() - timedelta(hours=2)  # Created 2 hours ago
    )
    db.add(alert)
    db.commit()
    print("Created critical AI-generated alert")


def create_clinician_ai_chats(db: Session, clinician_id: str, patient_id: str):
    """Create general clinician-AI conversations within patient context."""
    print("Creating clinician-AI general conversations...")
    
    base_time = datetime.utcnow() - timedelta(days=20)
    
    conversations = [
        {
            "time_offset": timedelta(days=0),
            "clinician_message": "What's the latest guidance on GLP-1 agonist dosing escalation?",
            "ai_response": "Current guidelines recommend starting with the lowest dose and escalating every 4 weeks to minimize GI side effects. For Wegovy: start at 0.25mg weekly for 4 weeks, then 0.5mg for 4 weeks, then 1mg for 4 weeks, then 1.7mg for 4 weeks, finally reaching the maintenance dose of 2.4mg weekly."
        },
        {
            "time_offset": timedelta(days=8),
            "clinician_message": "I have several patients asking about intermittent fasting with GLP-1s. What's the evidence?",
            "ai_response": "While no direct contraindication exists, combining intermittent fasting with GLP-1 agonists requires caution. Key considerations: 1) Increased hypoglycemia risk if combined with other diabetes meds, 2) May exacerbate GI side effects, 3) Could lead to inadequate nutrition during eating windows. Most experts recommend establishing GLP-1 tolerance first before adding IF."
        },
        {
            "time_offset": timedelta(days=15),
            "clinician_message": "Seeing more patients with significant fatigue on semaglutide. Normal or concerning?",
            "ai_response": "Fatigue is reported in 11% of patients on semaglutide. While common, evaluate for: 1) Excessive caloric restriction (<1200 cal/day), 2) Dehydration, 3) Nutrient deficiencies (especially B12, iron), 4) Too rapid dose escalation. Consider slower titration and nutritional counseling. Severe or persistent fatigue warrants further investigation."
        }
    ]
    
    for conv in conversations:
        timestamp = base_time + conv["time_offset"]
        
        # Clinician message
        clinician_msg = ChatMessage(
            patient_id=patient_id,  # Within patient context but filtered from patient view
            sender_type=MessageSenderType.CLINICIAN,
            message_content=conv["clinician_message"],
            created_at=timestamp,
            message_route=MessageRouteType.AI,
            is_read_by_clinician=True
        )
        db.add(clinician_msg)
        
        # AI response
        ai_msg = ChatMessage(
            patient_id=patient_id,
            sender_type=MessageSenderType.AGENT,
            message_content=conv["ai_response"],
            created_at=timestamp + timedelta(minutes=1),
            message_route=MessageRouteType.CLINICIAN,
            is_read_by_clinician=True,
            message_metadata={"context_type": "clinical_guidance", "topic": "glp1_agonists"}
        )
        db.add(ai_msg)
    
    db.commit()
    print(f"Created {len(conversations) * 2} clinician-AI chat messages")


def create_normal_patient_data(db: Session, clinician_id: str):
    """Create data for other patients with normal interactions."""
    print("Creating normal patient data for contrast...")
    
    # You would need to create these patients in Clerk first or use existing ones
    # For demo purposes, we'll create placeholder data
    normal_patterns = [
        {
            "name": "Sarah Johnson",
            "weight_loss_rate": 1.5,  # Normal rate
            "side_effects": ["mild nausea"],
            "chat_tone": "positive"
        },
        {
            "name": "Robert Chen", 
            "weight_loss_rate": 2.0,  # Good rate
            "side_effects": ["decreased appetite"],
            "chat_tone": "routine"
        },
        {
            "name": "Emily Davis",
            "weight_loss_rate": 1.0,  # Slow but steady
            "side_effects": ["mild fatigue"],
            "chat_tone": "questioning"
        },
        {
            "name": "James Wilson",
            "weight_loss_rate": 2.5,  # Good rate
            "side_effects": ["mild GI issues"],
            "chat_tone": "satisfied"
        }
    ]
    
    # Add event logs for these patients
    for i, pattern in enumerate(normal_patterns):
        event = EventLog(
            actor_user_id=clinician_id,
            actor_role="clinician",
            action="demo_patient_created",
            target_resource_type="patient",
            target_resource_id=f"demo_patient_{i+1}",
            outcome="success",
            details={
                "name": pattern["name"],
                "pattern": "normal",
                "weight_loss_rate": pattern["weight_loss_rate"],
                "risk_level": "low"
            },
            created_at=datetime.utcnow() - timedelta(days=30)
        )
        db.add(event)
    
    db.commit()
    print("Created normal patient data markers")


def main():
    """Main function to seed the investor demo scenario."""
    print("="*60)
    print("SEEDING INVESTOR DEMO SCENARIO")
    print("="*60)
    
    db = SessionLocal()
    
    try:
        # Verify Michael Patient exists
        patient = db.query(Patient).filter(Patient.id == MICHAEL_PATIENT_ID).first()
        if not patient:
            print(f"ERROR: Michael Patient not found with ID {MICHAEL_PATIENT_ID}")
            return
        print(f"Found patient: {patient.first_name} {patient.last_name}")
        
        # Verify Michael Clinician exists
        clinician = db.query(Clinician).filter(Clinician.clerk_id == MICHAEL_CLINICIAN_ID).first()
        if not clinician:
            print(f"ERROR: Michael Clinician not found with ID {MICHAEL_CLINICIAN_ID}")
            return
        print(f"Found clinician: Dr. {clinician.first_name} {clinician.last_name}")
        
        # Create the concerning scenario data
        create_michael_ai_chats(db, patient.id)
        create_michael_clinician_chats(db, patient.id, clinician.id)
        create_michael_weight_logs(db, patient.id)
        create_michael_appointments(db, patient.id, clinician.id)
        create_michael_side_effects(db, patient.id, clinician.id)
        create_patient_alert(db, patient.id, clinician.id)
        
        # Create clinician's general AI chats (within patient context)
        create_clinician_ai_chats(db, clinician.id, patient.id)
        
        # Create normal patient patterns for contrast
        create_normal_patient_data(db, clinician.id)
        
        print("\n" + "="*60)
        print("INVESTOR DEMO SCENARIO COMPLETE!")
        print("="*60)
        print("\nKey Demo Points:")
        print("1. Michael lost 37 lbs in 30 days (dangerous rate)")
        print("2. He mentioned serious symptoms to AI but not to clinician")
        print("3. AI detected the pattern and created a CRITICAL alert")
        print("4. Dashboard will show this as top priority for clinician")
        print("5. Other patients show normal patterns for contrast")
        
    except Exception as e:
        print(f"Error seeding demo data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()