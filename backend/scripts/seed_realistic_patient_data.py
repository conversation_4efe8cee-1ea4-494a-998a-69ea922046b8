#!/usr/bin/env python3
"""
Seed realistic patient data for all patients in the system.
This ensures each patient has a comprehensive set of data for demo purposes.
"""

import random
from datetime import datetime, timedelta, timezone
from typing import List
import uuid

from sqlalchemy.orm import Session

from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app import crud, models
from app.schemas.weight_log import WeightLogCreate
from app.schemas.side_effect_report import SideEffectReportCreate, SeverityLevel
from app.schemas.medication_request import MedicationRequestCreate
from app.schemas.appointment import AppointmentCreate
from app.schemas.appointment_request import AppointmentRequestCreate
from app.schemas.chat import ChatMessageCreateInternal
from app.models.chat_message import MessageSenderType, MessageRouteType
from app.schemas.patient_alert import Patient<PERSON>lertCreate, PatientAlertType, PatientAlertSeverity, PatientAlertStatus


def get_random_date_in_range(start_days_ago: int, end_days_ago: int = 0) -> datetime:
    """Get a random datetime between start_days_ago and end_days_ago."""
    start_date = datetime.now(timezone.utc) - timedelta(days=start_days_ago)
    end_date = datetime.now(timezone.utc) - timedelta(days=end_days_ago)
    
    time_between = end_date - start_date
    random_seconds = random.randint(0, int(time_between.total_seconds()))
    
    return start_date + timedelta(seconds=random_seconds)


def seed_weight_logs(db: Session, patient_id: str):
    """Create realistic weight log entries over the past 3 months."""
    print(f"  Creating weight logs...")
    
    # Start with a base weight
    base_weight = random.uniform(150, 250)  # pounds
    current_weight = base_weight
    
    # Create entries every 3-7 days for the past 90 days
    days_ago = 90
    entries_created = 0
    
    while days_ago > 0:
        # Weight fluctuation
        weight_change = random.uniform(-2, 1)  # Slight downward trend
        current_weight = max(base_weight - 20, min(base_weight + 10, current_weight + weight_change))
        
        # Convert pounds to kg for the schema
        weight_kg = round(current_weight * 0.453592, 1)
        log_date = get_random_date_in_range(days_ago, days_ago - 1)
        
        weight_log = WeightLogCreate(
            weight_kg=weight_kg,
            log_date=log_date,
            notes=random.choice([
                None,
                "Feeling good today",
                "Had a big meal yesterday",
                "Been exercising regularly",
                "Medication seems to be helping",
                "Some water retention"
            ])
        )
        
        # Create the log with a specific timestamp
        from app.crud.crud_weight_log import weight_log as weight_log_crud
        log_entry = weight_log_crud.create(
            db=db,
            obj_in=weight_log,
            patient_id=patient_id
        )
        
        # Update the created_at timestamp
        log_entry.created_at = get_random_date_in_range(days_ago, days_ago - 1)
        db.add(log_entry)
        
        entries_created += 1
        days_ago -= random.randint(3, 7)
    
    db.commit()
    print(f"    Created {entries_created} weight log entries")


def seed_side_effects(db: Session, patient_id: str, clinician_id: str):
    """Create realistic side effect reports."""
    print(f"  Creating side effect reports...")
    
    side_effects_data = [
        ("Mild nausea after taking medication", SeverityLevel.MINOR, 30),
        ("Headache in the morning", SeverityLevel.MODERATE, 20),
        ("Dizziness when standing up", SeverityLevel.MODERATE, 15),
        ("Fatigue throughout the day", SeverityLevel.MINOR, 10),
        ("Upset stomach", SeverityLevel.MINOR, 5),
    ]
    
    entries_created = 0
    for description, severity, days_ago in random.sample(side_effects_data, random.randint(2, 4)):
        report = SideEffectReportCreate(
            description=description,
            severity=severity,
            clinician_id=clinician_id  # Include the clinician_id
        )
        
        side_effect = crud.side_effect_report.create(
            db=db,
            obj_in=report,
            patient_id=patient_id
        )
        
        side_effect.reported_at = get_random_date_in_range(days_ago, days_ago - 1)
        db.add(side_effect)
        entries_created += 1
    
    db.commit()
    print(f"    Created {entries_created} side effect reports")


def seed_medication_requests(db: Session, patient_id: str):
    """Create medication requests."""
    print(f"  Creating medication requests...")
    
    # Get some medications from the database
    from app.crud.crud_medication import medication as medication_crud
    medications = medication_crud.get_multi(db, limit=10)
    if not medications:
        print("    No medications found in database. Skipping medication requests.")
        return
    
    requests_data = [
        ("I'm running low on my medication", "pending", 7),
        ("Need refill for next month", "approved", 30),
        ("Requesting dose adjustment", "pending", 3),
    ]
    
    entries_created = 0
    for reason, status, days_ago in random.sample(requests_data, random.randint(1, 2)):
        medication = random.choice(medications)
        
        request = MedicationRequestCreate(
            medication_name=medication.name,
            dosage="As prescribed",
            frequency="Once daily",
            duration="30 days",
            notes=reason
        )
        
        from app.crud.crud_medication_request import medication_request as med_request_crud
        med_request = med_request_crud.create_medication_request(
            db=db,
            obj_in=request,
            patient_id=patient_id
        )
        
        med_request.created_at = get_random_date_in_range(days_ago, days_ago - 1)
        
        # Update status if needed
        if status == "approved":
            from app.models.medication_request import MedicationRequestStatus
            med_request.status = MedicationRequestStatus.APPROVED
            med_request.resolved_at = med_request.created_at
        
        db.add(med_request)
        entries_created += 1
    
    db.commit()
    print(f"    Created {entries_created} medication requests")


def seed_appointments(db: Session, patient_id: str, clinician_id: str):
    """Create past and future appointments."""
    # Skip appointments for mock patient IDs that don't match Clerk format
    if not patient_id.startswith("user_"):
        print(f"  Skipping appointments (mock patient ID)")
        return
    
    print(f"  Creating appointments...")
    
    appointment_types = ["Follow-up", "Consultation", "Check-up", "Medication Review"]
    
    entries_created = 0
    
    # Past appointments - we'll create them as scheduled and then update their status
    for days_ago in [60, 30, 14]:
        appointment_datetime = get_random_date_in_range(days_ago, days_ago - 1)
        
        appointment = AppointmentCreate(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appointment_datetime,
            duration_minutes=30,  # Required field
            appointment_type=random.choice(appointment_types),
            status="scheduled",  # Must start as scheduled
            patient_notes=f"Regular {appointment_types[0].lower()}"
        )
        
        from app.crud.crud_appointment import appointment as appointment_crud
        appt = appointment_crud.create(db=db, obj_in=appointment)
        
        # Now update the status to completed
        from app.models.appointment import AppointmentStatus
        appt.status = AppointmentStatus.COMPLETED
        appt.clinician_notes = "Patient doing well. Continue current treatment plan."
        
        db.add(appt)
        entries_created += 1
    
    # Future appointments
    for days_ahead in [7, 30, 60]:
        appointment_date = datetime.now(timezone.utc) + timedelta(days=days_ahead)
        
        # Add some random hour between 9 AM and 5 PM
        appointment_date = appointment_date.replace(
            hour=random.randint(9, 16),
            minute=random.choice([0, 30]),
            second=0,
            microsecond=0
        )
        
        appointment = AppointmentCreate(
            patient_id=patient_id,
            clinician_id=clinician_id,
            appointment_datetime=appointment_date,
            duration_minutes=30,
            appointment_type=random.choice(appointment_types),
            status="scheduled",
            patient_notes=None
        )
        
        appt = appointment_crud.create(db=db, obj_in=appointment)
        entries_created += 1
    
    db.commit()
    print(f"    Created {entries_created} appointments")


def seed_chat_messages(db: Session, patient_id: str):
    """Create realistic chat conversation history."""
    print(f"  Creating chat messages...")
    
    conversations = [
        # Conversation 1 - 10 days ago
        [
            (MessageSenderType.PATIENT, "Hi, I've been experiencing some mild nausea after taking my medication. Is this normal?", MessageRouteType.CLINICIAN, 10),
            (MessageSenderType.AGENT, "I understand you're experiencing nausea after taking your medication. This can be a common side effect. Have you tried taking it with food?", MessageRouteType.PATIENT, 10),
            (MessageSenderType.PATIENT, "No, I usually take it first thing in the morning on an empty stomach.", MessageRouteType.CLINICIAN, 10),
            (MessageSenderType.AGENT, "Try taking your medication with a light meal or snack. This often helps reduce nausea. If it persists for more than a week, please let your clinician know.", MessageRouteType.PATIENT, 10),
        ],
        # Conversation 2 - 5 days ago
        [
            (MessageSenderType.PATIENT, "Can I schedule a follow-up appointment?", MessageRouteType.AI, 5),
            (MessageSenderType.AGENT, "I'd be happy to help you schedule a follow-up appointment. What dates work best for you?", MessageRouteType.PATIENT, 5),
            (MessageSenderType.PATIENT, "Next week Tuesday or Wednesday would be ideal.", MessageRouteType.AI, 5),
            (MessageSenderType.AGENT, "I've submitted your appointment request for next week. Your clinician will confirm the exact time soon.", MessageRouteType.PATIENT, 5),
        ],
        # Conversation 3 - 2 days ago
        [
            (MessageSenderType.PATIENT, "My weight has been stable for the past two weeks. Is this normal?", MessageRouteType.AI, 2),
            (MessageSenderType.AGENT, "Weight plateaus are common and completely normal. Your body may be adjusting to the changes. Continue with your current plan and track your progress.", MessageRouteType.PATIENT, 2),
        ]
    ]
    
    entries_created = 0
    for conversation in random.sample(conversations, random.randint(1, len(conversations))):
        for sender_type, content, route, days_ago in conversation:
            message = ChatMessageCreateInternal(
                patient_id=patient_id,
                sender_type=sender_type,
                message_content=content,
                message_route=route,
                is_read_by_clinician=True if days_ago > 3 else False
            )
            
            chat_msg = crud.chat_message.create(db=db, obj_in=message)
            chat_msg.created_at = get_random_date_in_range(days_ago, days_ago - 1)
            
            if chat_msg.is_read_by_clinician:
                chat_msg.read_by_clinician_at = chat_msg.created_at + timedelta(hours=random.randint(1, 24))
            
            db.add(chat_msg)
            entries_created += 1
    
    db.commit()
    print(f"    Created {entries_created} chat messages")


def seed_patient_alerts(db: Session, patient_id: str):
    """Create patient alerts."""
    print(f"  Creating patient alerts...")
    
    alerts_data = [
        (PatientAlertType.MISSED_LOG, "Patient hasn't logged weight in 7 days", PatientAlertSeverity.MEDIUM, "new", 2),
        (PatientAlertType.WEIGHT_THRESHOLD, "Weight gain of 5 lbs in past week", PatientAlertSeverity.HIGH, "acknowledged", 5),
        (PatientAlertType.SIDE_EFFECT_REPORTED, "Reported moderate side effects", PatientAlertSeverity.LOW, "resolved", 10),
    ]
    
    entries_created = 0
    for alert_type, message, severity, status, days_ago in random.sample(alerts_data, random.randint(0, 2)):
        alert = PatientAlertCreate(
            patient_id=patient_id,
            alert_type=alert_type,
            severity=severity,
            status=status,
            description=message
        )
        
        from app.crud.crud_patient_alert import patient_alert as patient_alert_crud
        patient_alert = patient_alert_crud.create(db=db, obj_in=alert)
        patient_alert.created_at = get_random_date_in_range(days_ago, days_ago - 1)
        
        if status == PatientAlertStatus.ACKNOWLEDGED:
            patient_alert.acknowledged_at = patient_alert.created_at + timedelta(hours=random.randint(1, 12))
        elif status == PatientAlertStatus.RESOLVED:
            patient_alert.acknowledged_at = patient_alert.created_at + timedelta(hours=random.randint(1, 6))
            patient_alert.resolved_at = patient_alert.acknowledged_at + timedelta(hours=random.randint(1, 24))
        
        db.add(patient_alert)
        entries_created += 1
    
    db.commit()
    print(f"    Created {entries_created} patient alerts")


def main():
    """Main function to seed data for all patients."""
    db = SessionLocal()
    
    try:
        # Get all patients except the clinician who is also in the patients table
        patients = db.query(models.Patient).filter(
            models.Patient.email != "<EMAIL>"  # Exclude the clinician if they're in patients table
        ).all()
        
        # Get the clinician
        clinician = db.query(models.Clinician).filter(
            models.Clinician.email == "<EMAIL>"
        ).first()
        
        if not clinician:
            print("Error: Could not find clinician Michael Isaac")
            return
        
        print(f"Found {len(patients)} patients to seed data for")
        print(f"Using clinician: {clinician.first_name} {clinician.last_name}")
        
        for i, patient in enumerate(patients, 1):
            print(f"\n[{i}/{len(patients)}] Seeding data for {patient.first_name} {patient.last_name} (ID: {patient.id})")
            
            # Skip if patient already has substantial data
            from app.crud.crud_weight_log import weight_log as weight_log_crud
            existing_weight_logs = weight_log_crud.get_by_patient(
                db=db, patient_id=patient.id, limit=1
            )
            if len(existing_weight_logs) > 0:
                print("  Patient already has data, skipping...")
                continue
            
            # Seed all data types
            seed_weight_logs(db, patient.id)
            seed_side_effects(db, patient.id, clinician.id)
            seed_medication_requests(db, patient.id)
            seed_appointments(db, patient.id, clinician.id)
            seed_chat_messages(db, patient.id)
            seed_patient_alerts(db, patient.id)
            
            print(f"  ✓ Completed seeding for {patient.first_name} {patient.last_name}")
        
        print("\n✅ Data seeding completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during seeding: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()