#!/usr/bin/env python3
"""
Singleton appointment scheduler to be shared across all demo seeding scripts.
This ensures no appointment conflicts across different patient seeding operations.
"""

from appointment_scheduler import AppointmentScheduler


# Global scheduler instance
_scheduler_instance = None


def get_shared_scheduler() -> AppointmentScheduler:
    """Get or create the shared appointment scheduler instance."""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = AppointmentScheduler()
    return _scheduler_instance


def reset_scheduler():
    """Reset the scheduler (for demo reset operations)."""
    global _scheduler_instance
    _scheduler_instance = AppointmentScheduler()
    return _scheduler_instance