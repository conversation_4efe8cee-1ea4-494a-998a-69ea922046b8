#!/usr/bin/env python
"""Integration test for action chain functionality."""

import asyncio
import json
import requests
from datetime import datetime, timedelta

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
# These tokens are for testing only - replace with valid tokens
PATIENT_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6Imluc18yU2l1UlBKY1pROTBFZDM1VGZUa0ZQTjRTMEoiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.example"

# Test cases
TEST_CASES = [
    {
        "name": "Simple Side Effect Report + Appointment",
        "text": "I'm having severe headaches from Ozempic and need to see <PERSON><PERSON> <PERSON> tomorrow at 2pm",
        "template_id": "ea21e6ba-c83f-47f5-becc-ea1f5bb18e9e"  # Default template ID
    },
    {
        "name": "Batch Weight Logging",
        "text": "Log my weight for the past 3 days: Monday 185 pounds, Tuesday 184 pounds, Wednesday 183 pounds",
        "template_id": "ea21e6ba-c83f-47f5-becc-ea1f5bb18e9e"
    }
]

def test_action_chain():
    """Test action chain processing through the API."""
    
    headers = {
        "Authorization": f"Bearer {PATIENT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    for test_case in TEST_CASES:
        print(f"\n{'='*60}")
        print(f"Testing: {test_case['name']}")
        print(f"Input: {test_case['text']}")
        
        # Call the LLM actions endpoint
        payload = {
            "text": test_case["text"],
            "template_id": test_case["template_id"],
            "context": {
                "timezone_offset": -5.0  # EST
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/llm-actions/process-text",
            json=payload,
            headers=headers,
            params={"include_debug": True}
        )
        
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            print(f"Action Type: {data.get('action_type', 'Unknown')}")
            print(f"Message: {data.get('message', 'No message')}")
            
            # Check if it's a compound action
            if data.get('action_type') == 'compound_action':
                print("\n✅ Compound action detected!")
                if 'data' in data and 'results' in data['data']:
                    print("\nAction Results:")
                    for result in data['data']['results']:
                        status = "✅" if result['success'] else "❌"
                        print(f"  {status} {result['action_type']}: {result.get('message', 'No message')}")
                
                # Print debug info if available
                if 'debug_info' in data and data['debug_info']:
                    print("\nDebug Info:")
                    print(json.dumps(data['debug_info'], indent=2))
            else:
                print("❌ Single action detected - compound action not recognized")
        else:
            print(f"Error: {response.text}")

def test_simple_action():
    """Test that simple actions still work."""
    
    headers = {
        "Authorization": f"Bearer {PATIENT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print(f"\n{'='*60}")
    print("Testing: Simple Weight Log")
    
    payload = {
        "text": "Log my weight as 185 pounds",
        "template_id": "ea21e6ba-c83f-47f5-becc-ea1f5bb18e9e",
        "context": {
            "timezone_offset": -5.0
        }
    }
    
    response = requests.post(
        f"{API_BASE_URL}/llm-actions/process-text",
        json=payload,
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Success: {data.get('success', False)}")
        print(f"Action Type: {data.get('action_type', 'Unknown')}")
        print(f"Message: {data.get('message', 'No message')}")

if __name__ == "__main__":
    print("Starting Action Chain Integration Tests...")
    
    # Test simple action first
    test_simple_action()
    
    # Test compound actions
    test_action_chain()
    
    print("\n\nTests completed!")