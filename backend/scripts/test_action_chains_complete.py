#!/usr/bin/env python
"""Test the complete action chain implementation."""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from app.db.session import SessionLocal
from app.services.intent_resolver_service import IntentResolverService
from app.models import Template
from app.schemas.action_chain_v2 import ChainedAction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test cases for compound actions
TEST_CASES = [
    {
        "name": "Side Effect + Appointment",
        "input": "I'm having severe headaches from the medication and need to see the doctor tomorrow at 2pm",
        "expected_actions": ["side_effect_report", "appointment_request"]
    },
    {
        "name": "Appointment + Reminder",
        "input": "Schedule an appointment for next Monday at 10am and remind me the day before",
        "expected_actions": ["appointment_request", "notification_create"]
    },
    {
        "name": "Batch Weight Logging",
        "input": "Log my weight: Monday 185 pounds, Tuesday 184 pounds, Wednesday 183 pounds",
        "expected_actions": ["weight_log_create", "weight_log_create", "weight_log_create"]
    }
]

async def test_compound_action_detection():
    """Test that the intent resolver can detect compound actions."""
    
    db = SessionLocal()
    intent_resolver = IntentResolverService()
    
    try:
        # Get a template (we'll use the default one)
        template = db.query(Template).filter(Template.name == "Default Template").first()
        if not template:
            logger.error("Default template not found!")
            return
        
        for test_case in TEST_CASES:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {test_case['name']}")
            logger.info(f"Input: {test_case['input']}")
            
            # Extract intent
            result, actions = await intent_resolver.extract_intent(
                template=template,
                user_input=test_case['input'],
                client_timezone_offset=-5.0,  # EST
                user_role="patient",
                db=db
            )
            
            # Check if compound action was detected
            if isinstance(result, ChainedAction):
                logger.info("✅ Compound action detected!")
                logger.info(f"  Primary action: {result.primary_action.action_type}")
                logger.info(f"  Follow-up actions: {[a.action_type for a in result.follow_up_actions]}")
                logger.info(f"  Execution mode: {result.execution_mode}")
                
                # Verify expected actions
                detected_actions = [result.primary_action.action_type]
                detected_actions.extend([a.action_type for a in result.follow_up_actions])
                
                if detected_actions == test_case['expected_actions']:
                    logger.info("✅ All expected actions detected correctly!")
                else:
                    logger.warning(f"⚠️  Expected: {test_case['expected_actions']}")
                    logger.warning(f"⚠️  Detected: {detected_actions}")
            else:
                logger.warning("❌ Single action detected instead of compound")
                logger.warning(f"  Action: {result.action_type if hasattr(result, 'action_type') else 'Unknown'}")
                
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
    finally:
        db.close()

async def test_action_chain_execution():
    """Test executing a simple action chain."""
    
    from app.services.action_chain_executor_service import ActionChainExecutorService
    from app.schemas.action_chain_v2 import ChainedIntent, ExecutionMode, FailureMode
    
    db = SessionLocal()
    executor = ActionChainExecutorService(db)
    
    try:
        # Create a simple test chain
        chain = ChainedAction(
            primary_action=ChainedIntent(
                action_type="weight_log_create",
                parameters={
                    "weight_value": 185,
                    "unit": "lb",
                    "date": datetime.now().strftime("%Y-%m-%d")
                },
                confidence=1.0
            ),
            follow_up_actions=[
                ChainedIntent(
                    action_type="notification_create",
                    parameters={
                        "title": "Weight Logged",
                        "message": "Your weight of 185 lb has been recorded",
                        "notification_type": "info"
                    },
                    confidence=1.0
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
        
        logger.info("\nTesting chain execution...")
        logger.info(f"Chain: {chain.primary_action.action_type} -> {[a.action_type for a in chain.follow_up_actions]}")
        
        # Execute the chain
        result = await executor.execute_chain(
            user_id="test_user",
            user_role="patient",
            chain=chain,
            initial_context={}
        )
        
        logger.info(f"\nExecution completed!")
        logger.info(f"Success: {result.success}")
        logger.info(f"Execution time: {result.execution_time_ms}ms")
        
        for action_result in result.results:
            status = "✅" if action_result.success else "❌"
            logger.info(f"{status} {action_result.action_type}: {action_result.message or action_result.error_message}")
        
    except Exception as e:
        logger.error(f"Execution test failed: {e}", exc_info=True)
    finally:
        db.close()

async def main():
    """Run all tests."""
    logger.info("Starting Action Chain Tests...")
    
    # Test compound action detection
    await test_compound_action_detection()
    
    # Test chain execution
    # await test_action_chain_execution()
    
    logger.info("\nTests completed!")

if __name__ == "__main__":
    asyncio.run(main())