"""
Simple test for Advanced Actions System core functionality
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.services.action_patterns import ActionPatterns, PatternDetector
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.schemas.action_chain_v2 import ChainContext

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_pattern_detection():
    """Test pattern detection."""
    logger.info("\n=== Testing Pattern Detection ===")
    
    test_cases = [
        ("Schedule an appointment tomorrow at 2pm and remind me the day before", "appointment_with_reminder"),
        ("I need to report side effects and schedule a follow-up", "side_effect_with_followup"),
        ("Please log my weight and check milestones", "weight_log_with_milestone"),
        ("Send notification to all patients", "batch_notifications"),
    ]
    
    for input_text, expected_pattern in test_cases:
        detected = PatternDetector.detect_pattern(input_text)
        status = "✅" if detected == expected_pattern else "❌"
        logger.info(f"{status} '{input_text}' -> {detected} (expected: {expected_pattern})")


def test_appointment_pattern():
    """Test appointment with reminder pattern."""
    logger.info("\n=== Testing Appointment Pattern ===")
    
    pattern = ActionPatterns.appointment_with_reminder(
        patient_id="patient_123",
        scheduled_time=(datetime.now() + timedelta(days=3)).isoformat() + "+00:00",
        duration_minutes=30,
        appointment_type="Follow-up",
        reminder_days_before=1
    )
    
    logger.info(f"✅ Created pattern with chain_id: {pattern.chain_id}")
    logger.info(f"   Primary action: {pattern.primary_action.action_type}")
    logger.info(f"   Follow-up actions: {[a.action_type for a in pattern.follow_up_actions]}")
    logger.info(f"   Execution mode: {pattern.execution_mode}")
    
    # Test context substitution
    db = SessionLocal()
    executor = ActionChainExecutorService(db)
    
    # Simulate appointment creation result
    context = ChainContext()
    context.update_from_action_result("appointment_create", {
        "appointment_id": "apt_12345",
        "patient_id": "patient_123",
        "scheduled_time": "2025-04-24T14:00:00+00:00"
    })
    # The update_from_action_result also adds top-level keys for common fields
    # Let's also manually add scheduled_time as a top-level key
    context.data["scheduled_time"] = "2025-04-24T14:00:00+00:00"
    
    # Test parameter substitution
    reminder_params = pattern.follow_up_actions[0].parameters.copy()
    enriched = executor._apply_context_substitutions(reminder_params, context)
    
    logger.info(f"\n   Context substitution test:")
    logger.info(f"   Original message: {reminder_params['message']}")
    logger.info(f"   After substitution: {enriched['message']}")
    
    db.close()


def test_side_effect_pattern():
    """Test side effect reporting pattern."""
    logger.info("\n=== Testing Side Effect Pattern ===")
    
    # Test severe side effect (should create notification + appointment)
    severe = ActionPatterns.side_effect_with_followup(
        patient_id="patient_456",
        medication_name="Ozempic",
        symptoms="severe nausea and vomiting",
        severity="Severe",
        onset_time="2 hours after dose"
    )
    
    logger.info(f"✅ Severe side effect pattern:")
    logger.info(f"   Actions: {[severe.primary_action.action_type] + [a.action_type for a in severe.follow_up_actions]}")
    logger.info(f"   Total actions: {1 + len(severe.follow_up_actions)}")
    
    # Test mild side effect (should only create notification)
    mild = ActionPatterns.side_effect_with_followup(
        patient_id="patient_789",
        medication_name="Wegovy",
        symptoms="mild headache",
        severity="Mild",
        onset_time="This morning"
    )
    
    logger.info(f"\n✅ Mild side effect pattern:")
    logger.info(f"   Actions: {[mild.primary_action.action_type] + [a.action_type for a in mild.follow_up_actions]}")
    logger.info(f"   Total actions: {1 + len(mild.follow_up_actions)}")


def test_batch_notifications():
    """Test batch notification pattern."""
    logger.info("\n=== Testing Batch Notifications ===")
    
    batch = ActionPatterns.batch_notifications(
        recipient_ids=["patient_1", "patient_2", "patient_3"],
        notification_type="clinic_update",
        title="Holiday Hours",
        message="Clinic will be closed Dec 25-26",
        priority="high"
    )
    
    logger.info(f"✅ Batch notification pattern:")
    logger.info(f"   Total notifications: {1 + len(batch.follow_up_actions)}")
    logger.info(f"   Execution mode: {batch.execution_mode}")
    logger.info(f"   Recipients: {[batch.primary_action.parameters['recipient_id']] + [a.parameters['recipient_id'] for a in batch.follow_up_actions]}")


def test_context_operations():
    """Test context operations."""
    logger.info("\n=== Testing Context Operations ===")
    
    context = ChainContext()
    
    # Test adding action results
    context.update_from_action_result("appointment_create", {
        "appointment_id": "apt_123",
        "patient_id": "patient_456",
        "clinician_id": "clinician_789"
    })
    
    # Test direct access
    assert context.get("appointment_id") == "apt_123"
    logger.info("✅ Direct context access works")
    
    # Test nested access
    assert context.get("appointment_create.appointment_id") == "apt_123"
    logger.info("✅ Nested context access works")
    
    # Test default values
    assert context.get("missing_key", "default") == "default"
    logger.info("✅ Default values work")


def main():
    """Run all tests."""
    logger.info("🚀 Starting Advanced Actions System Tests\n")
    
    try:
        test_pattern_detection()
        test_appointment_pattern()
        test_side_effect_pattern()
        test_batch_notifications()
        test_context_operations()
        
        logger.info("\n✨ All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"\n❌ Test failed: {e}", exc_info=True)


if __name__ == "__main__":
    main()