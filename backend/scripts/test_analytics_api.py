#!/usr/bin/env python3
"""Test the Population Health Analytics API endpoints"""

import os
import sys
import requests
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# API configuration
API_BASE_URL = "http://localhost:8000/api/v1"

# Test clinician token (replace with actual token)
# This should be the token from your test clinician user
AUTH_TOKEN = "your_clerk_jwt_token_here"

def test_analytics_endpoints():
    """Test all analytics endpoints"""
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}",
        "Content-Type": "application/json"
    }
    
    endpoints = [
        "/analytics/population-dashboard",
        "/analytics/population-overview",
        "/analytics/risk-stratification",
        "/analytics/treatment-analytics",
        "/analytics/predictive-analytics",
        "/analytics/optimization-insights"
    ]
    
    print("Testing Population Health Analytics Endpoints")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = f"{API_BASE_URL}{endpoint}"
        print(f"\nTesting: {endpoint}")
        print("-" * 30)
        
        try:
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Response keys: {list(data.keys())}")
                
                # Print sample data for the main dashboard endpoint
                if endpoint == "/analytics/population-dashboard":
                    print(f"Total patients: {data['overview']['total_patients']}")
                    print(f"Active patients: {data['overview']['active_patients']}")
                    print(f"High-risk patients: {len(data['risk_stratification']['high_risk_patients'])}")
                    print(f"Optimization insights: {len(data['optimization_insights']['insights'])}")
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Testing complete!")

if __name__ == "__main__":
    print("\nNOTE: You need to set a valid AUTH_TOKEN in this script first!")
    print("Get the token from your browser's developer tools when logged in as a clinician.")
    print("\nPress Ctrl+C to cancel or any key to continue...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\nCancelled.")
        sys.exit(0)
    
    test_analytics_endpoints()