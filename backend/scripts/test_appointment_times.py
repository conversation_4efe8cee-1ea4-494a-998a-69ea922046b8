#!/usr/bin/env python3
"""Test that appointment times are scheduled correctly on hour/half-hour boundaries."""

import sys
from pathlib import Path
from datetime import datetime, timedelta, timezone

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from appointment_scheduler import AppointmentScheduler

def test_appointment_scheduling():
    """Test that appointments are scheduled at proper clinic times."""
    scheduler = AppointmentScheduler()
    
    # Test scheduling various appointments
    test_cases = [
        ("Morning appointment", datetime.now().date() + timedelta(days=1), True),
        ("Afternoon appointment", datetime.now().date() + timedelta(days=2), False),
        ("Urgent appointment", datetime.now().date() + timedelta(days=3), True),
    ]
    
    print("Testing appointment scheduling...")
    print("-" * 60)
    
    for description, target_date, prefer_morning in test_cases:
        appt_time = scheduler.schedule_appointment(
            target_date=target_date,
            appointment_type="Follow-up",
            prefer_morning=prefer_morning,
            urgent="Urgent" in description
        )
        
        if appt_time:
            print(f"{description}:")
            print(f"  Date: {appt_time.date()}")
            print(f"  Time: {appt_time.strftime('%I:%M %p')}")
            print(f"  Minute: {appt_time.minute} (should be 0 or 30)")
            assert appt_time.minute in [0, 30], f"Invalid minute: {appt_time.minute}"
            print("  ✓ Valid clinic time")
        else:
            print(f"{description}: No available slot found")
        print()
    
    # Test that lunch break is respected
    lunch_date = datetime.now().date() + timedelta(days=4)
    lunch_time = scheduler.schedule_appointment(
        target_date=lunch_date,
        appointment_type="Follow-up",
        prefer_morning=False
    )
    
    if lunch_time:
        hour = lunch_time.hour
        print(f"Lunch break test:")
        print(f"  Time: {lunch_time.strftime('%I:%M %p')}")
        print(f"  Hour: {hour}")
        assert not (12 <= hour < 13), f"Appointment scheduled during lunch: {lunch_time}"
        print("  ✓ Lunch break respected")
    
    print("\n✅ All tests passed!")

if __name__ == "__main__":
    test_appointment_scheduling()