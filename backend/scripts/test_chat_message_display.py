#!/usr/bin/env python3
"""Test script to create various message types for testing chat UI display."""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from sqlalchemy.orm import Session

from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.patient import Patient
from app.models.clinician import Clinician
from app.crud.crud_chat_message import chat_message as crud_chat
from app.schemas.chat import ChatMessageCreateInternal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_chat_display():
    """Create test messages for verifying chat UI display."""
    db = SessionLocal()
    
    try:
        # Get demo patient and clinician
        patient = db.query(Patient).filter(Patient.id == "user_2waTCuGL3kQC9k2rY47INdcJXk5").first()
        clinician = db.query(Clinician).filter(Clinician.id == "user_2waSREJSlduBPyK6Vbv9TU3VhI7").first()
        
        if not patient or not clinician:
            logger.error("Demo patient or clinician not found")
            return
            
        logger.info(f"Testing with patient: {patient.first_name} {patient.last_name} and clinician: Dr. {clinician.last_name}")
        
        # Create a series of test messages to demonstrate all types
        test_messages = [
            # Patient message
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="patient",
                message_content="I've been experiencing some headaches with the medication. They usually happen in the morning.",
                message_route=None
            ),
            # Clinician message routed to AI
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="clinician",
                message_content="What are the common side effects of GLP-1 medications regarding headaches?",
                message_route="ai"
            ),
            # AI response
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="agent",
                message_content="Headaches are a common side effect of GLP-1 medications, reported in about 14-18% of patients. They typically occur early in treatment and often improve over time. Key management strategies include:\n\n1. Ensure adequate hydration\n2. Consider taking the medication with food\n3. Gradual dose escalation can help minimize side effects\n4. Over-the-counter pain relievers can be used as needed",
                message_route=None
            ),
            # Clinician message routed to patient
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="clinician",
                message_content="Hi there! I see you're experiencing headaches. This is a common side effect that often improves over time. Make sure you're drinking plenty of water and consider taking the medication with food. If the headaches persist or worsen, please let me know.",
                message_route="patient"
            ),
            # Patient response
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="patient",
                message_content="Thank you for the advice! I'll try taking it with breakfast and increase my water intake.",
                message_route=None
            ),
            # System message
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="agent",
                message_content="[System: Patient has been added to headache monitoring protocol]",
                message_route=None,
                metadata={"system_message": True}
            ),
        ]
        
        logger.info("Creating test messages...")
        for i, msg in enumerate(test_messages):
            created_msg = crud_chat.create(db, obj_in=msg)
            logger.info(f"Created message {i+1}: {msg.sender_type} → {msg.message_route or 'N/A'}")
        
        db.commit()
        logger.info("✓ Test messages created successfully")
        
        # Summary
        logger.info("\n=== TEST SUMMARY ===")
        logger.info("Created messages with different routing:")
        logger.info("- Patient messages (no routing)")
        logger.info("- Clinician → AI (blue styling)")
        logger.info("- Clinician → Patient (green styling)")
        logger.info("- Agent responses")
        logger.info("- System messages")
        logger.info("\nCheck the clinician chat UI to verify:")
        logger.info("1. All messages show sender labels")
        logger.info("2. Clinician messages show routing direction")
        logger.info("3. Visual styling differentiates routing (green for patient, blue for AI)")
        logger.info("4. Gradients and borders enhance visual distinction")
        
    except Exception as e:
        logger.error(f"Error during test: {str(e)}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    test_chat_display()