#!/usr/bin/env python3
"""
Test script to verify chat messages API returns metadata properly.
"""

import requests
import json
from datetime import datetime

# API configuration
BASE_URL = "http://localhost:8000/api/v1"

# Demo user credentials (you'll need a valid JWT token)
# For testing, you can get this from browser DevTools after logging in
TOKEN = "YOUR_JWT_TOKEN_HERE"  # Replace with actual token

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

def test_chat_history():
    """Test fetching chat history with metadata."""
    
    # Demo patient ID
    patient_id = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
    
    # Fetch chat history
    response = requests.get(
        f"{BASE_URL}/chat/history",
        headers=headers,
        params={"patient_id_filter": patient_id}
    )
    
    if response.status_code == 200:
        data = response.json()
        messages = data.get("messages", [])
        
        print(f"Found {len(messages)} messages")
        
        # Look for clinical note messages
        for msg in messages:
            if msg.get("sender_type") == "CLINICAL_NOTE":
                print("\n" + "="*60)
                print("Clinical Note Message Found!")
                print(f"Message ID: {msg.get('message_id')}")
                print(f"Content: {msg.get('message')}")
                print(f"Timestamp: {msg.get('timestamp')}")
                
                metadata = msg.get("metadata", {})
                if metadata:
                    print("\nMetadata:")
                    print(f"  clinical_note_id: {metadata.get('clinical_note_id')}")
                    print(f"  note_type: {metadata.get('note_type')}")
                    print(f"  confidence_score: {metadata.get('confidence_score')}")
                    print(f"  clinician_name: {metadata.get('clinician_name')}")
                    print(f"  clinician_only: {metadata.get('clinician_only')}")
                    
                    if metadata.get('clinical_note_id'):
                        print(f"\n✓ Clinical note ID is present: {metadata['clinical_note_id']}")
                        print("  Frontend should be able to navigate to this note!")
                    else:
                        print("\n✗ No clinical_note_id in metadata")
                else:
                    print("\n✗ No metadata in message")
                print("="*60)
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    print("Testing Chat Messages API")
    print("=" * 60)
    print("NOTE: You need to set a valid JWT token in the script!")
    print("Get it from browser DevTools > Network > Request Headers > Authorization")
    print("=" * 60)
    # test_chat_history()  # Uncomment after setting TOKEN