#!/usr/bin/env python3
"""
Test script to verify clinical note metadata is properly saved and retrieved.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app.db.session import <PERSON>Local
from app.crud.crud_patient import patient as crud_patient
from app.crud.crud_clinician import clinician as crud_clinician
from app.crud.crud_chat_message import chat_message as crud_chat_message
from app.services.clinical_notes_service import ClinicalNotesService
from app.schemas.clinical_note import GenerateClinicalNoteRequest
from app.core.logging import logger


def test_clinical_note_metadata():
    """Test that clinical note metadata is properly saved."""
    db: Session = SessionLocal()
    service = ClinicalNotesService()
    
    try:
        # Get demo clinician and patient
        demo_clinician = crud_clinician.get(db, id="user_2waSREJSlduBPyK6Vbv9TU3VhI7")
        demo_patient = crud_patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        
        if not demo_clinician or not demo_patient:
            logger.error("Demo clinician or patient not found")
            return
            
        logger.info(f"Using clinician: {demo_clinician.first_name} {demo_clinician.last_name}")
        logger.info(f"Using patient: {demo_patient.first_name} {demo_patient.last_name}")
        
        # Generate a clinical note
        request = GenerateClinicalNoteRequest(
            patient_id=demo_patient.id,
            clinician_id=demo_clinician.id,
            note_type="progress_note",
            chief_complaint="Testing metadata storage",
            visit_context="Test to verify clinical note metadata is saved correctly"
        )
        
        logger.info("Generating clinical note...")
        result = asyncio.run(service.generate_clinical_note(db, request, demo_clinician.id))
        
        if result:
            logger.info(f"✓ Clinical note generated successfully: ID {result.note.id}")
            
            # Now fetch the chat messages to check metadata
            messages = crud_chat_message.get_by_patient(
                db, patient_id=demo_patient.id
            )
            
            # Find the most recent clinical note message
            clinical_note_messages = [
                msg for msg in messages 
                if msg.sender_type == "CLINICAL_NOTE"
            ]
            
            if clinical_note_messages:
                # Get the most recent one
                latest_msg = sorted(clinical_note_messages, 
                                  key=lambda x: x.created_at, 
                                  reverse=True)[0]
                
                logger.info(f"\n✓ Found clinical note message: ID {latest_msg.id}")
                logger.info(f"  Message content: {latest_msg.message_content}")
                logger.info(f"  Has message_metadata attribute: {hasattr(latest_msg, 'message_metadata')}")
                
                if hasattr(latest_msg, 'message_metadata'):
                    logger.info(f"  Metadata content: {latest_msg.message_metadata}")
                    
                    if latest_msg.message_metadata:
                        logger.info("\n  Metadata fields:")
                        for key, value in latest_msg.message_metadata.items():
                            if key == 'sections':
                                logger.info(f"    - {key}: <SOAP sections present>")
                            elif key == 'clinical_note_id':
                                logger.info(f"    - {key}: {value} ✓")
                            else:
                                logger.info(f"    - {key}: {value}")
                    else:
                        logger.error("  ✗ Metadata is empty!")
                else:
                    logger.error("  ✗ No message_metadata attribute found!")
                    
            else:
                logger.error("✗ No clinical note messages found after generation")
                
        else:
            logger.error("✗ Failed to generate clinical note")
            
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("Testing Clinical Note Metadata Storage")
    logger.info("=" * 60)
    test_clinical_note_metadata()
    logger.info("=" * 60)
    logger.info("Test completed")