#!/usr/bin/env python3
"""Test script to verify clinical notes appear in chat history correctly."""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.models.patient import Patient
from app.models.clinician import Clinician
from app.models.clinical_note import ClinicalNote
from app.models.chat_message import ChatMessage
from app.schemas.clinical_note import GenerateClinicalNoteRequest
from app.services.clinical_notes_service import ClinicalNotesService
from app.crud.crud_chat_message import chat_message as crud_chat_message

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_clinical_notes_in_chat():
    """Test that clinical notes appear in chat history with proper filtering."""
    db = SessionLocal()
    
    try:
        # Get demo patient and clinician
        patient = db.query(Patient).filter(Patient.id == "user_2waTCuGL3kQC9k2rY47INdcJXk5").first()
        clinician = db.query(Clinician).filter(Clinician.id == "user_2waSREJSlduBPyK6Vbv9TU3VhI7").first()
        
        if not patient or not clinician:
            logger.error("Demo patient or clinician not found")
            return
            
        logger.info(f"Testing with patient: {patient.first_name} {patient.last_name} and clinician: Dr. {clinician.last_name}")
        
        # First, let's create some chat messages to generate the note from
        from app.crud.crud_chat_message import chat_message as crud_chat
        from app.schemas.chat import ChatMessageCreateInternal
        
        # Create a couple of test messages
        test_messages = [
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="patient",
                message_content="I've been feeling much better since starting the GLP-1 medication. My energy levels are up.",
                message_route=None
            ),
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="clinician",
                message_content="That's great to hear! Have you noticed any changes in your weight?",
                message_route=None
            ),
            ChatMessageCreateInternal(
                patient_id=patient.id,
                sender_type="patient",
                message_content="Yes, I'm down 3 pounds from my last visit!",
                message_route=None
            )
        ]
        
        for msg in test_messages:
            crud_chat.create(db, obj_in=msg)
        db.commit()
        
        logger.info("Created test chat messages")
        
        # Create a test clinical note
        service = ClinicalNotesService()
        note_request = GenerateClinicalNoteRequest(
            patient_id=patient.id,
            note_type="progress"
        )
        
        logger.info("Generating clinical note...")
        response = await service.generate_clinical_note(db, note_request, clinician_id=clinician.id)
        clinical_note = response.note
        logger.info(f"Clinical note created with ID: {clinical_note.id}")
        
        # Check if chat message was created
        logger.info("\nChecking chat messages...")
        
        # Get all messages for this patient (clinician view)
        all_messages = crud_chat_message.get_by_patient(db, patient_id=str(patient.id))
        clinical_note_messages = [msg for msg in all_messages if msg.sender_type == "CLINICAL_NOTE"]
        
        if clinical_note_messages:
            logger.info(f"✓ Found {len(clinical_note_messages)} clinical note message(s) in chat history")
            for msg in clinical_note_messages:
                logger.info(f"  - Message: {msg.message_content[:100]}...")
                logger.info(f"    Sender Type: {msg.sender_type}")
                logger.info(f"    Metadata: {msg.metadata}")
        else:
            logger.warning("✗ No clinical note messages found in chat history")
            
        # Test filtered view (patient view)
        logger.info("\nTesting patient-filtered view...")
        filtered_messages = crud_chat_message.get_by_patient_filtered(db, patient_id=str(patient.id))
        filtered_clinical_notes = [msg for msg in filtered_messages if msg.sender_type == "CLINICAL_NOTE"]
        
        if filtered_clinical_notes:
            logger.error("✗ Clinical notes visible in patient view (should be hidden)")
        else:
            logger.info("✓ Clinical notes properly hidden from patient view")
            
        # Summary
        logger.info("\n=== TEST SUMMARY ===")
        logger.info(f"Total messages for patient: {len(all_messages)}")
        logger.info(f"Clinical note messages: {len(clinical_note_messages)}")
        logger.info(f"Messages in patient view: {len(filtered_messages)}")
        logger.info(f"Clinical notes in patient view: {len(filtered_clinical_notes)} (should be 0)")
        
        # Verify the enum value
        logger.info("\n=== ENUM VERIFICATION ===")
        if clinical_note_messages:
            msg = clinical_note_messages[0]
            logger.info(f"Database sender_type value: '{msg.sender_type}'")
            logger.info(f"Is uppercase: {msg.sender_type == 'CLINICAL_NOTE'}")
            logger.info(f"Is lowercase: {msg.sender_type == 'clinical_note'}")
            
    except Exception as e:
        logger.error(f"Error during test: {str(e)}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_clinical_notes_in_chat())