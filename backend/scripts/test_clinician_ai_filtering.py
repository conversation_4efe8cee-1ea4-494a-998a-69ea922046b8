#!/usr/bin/env python3
"""Test script to verify AI->Clinician messages are filtered from patient view."""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app import crud, schemas
from app.db.session import SessionLocal
from app.schemas.chat import ChatMessageCreateInternal
from app.models.chat_message import MessageSenderType, MessageRouteType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Demo patient ID (<PERSON>'s internal UUID)
DEMO_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"


async def test_clinician_ai_filtering():
    """Test that AI->Clinician messages are filtered from patient view."""
    db = SessionLocal()
    
    try:
        # First, let's try to get patient directly by the ID
        patient = crud.patient.get(db, id=DEMO_PATIENT_ID)
        if not patient:
            # If not found, search through all patients to find by clerk_id
            all_patients = db.query(crud.patient.model).all()
            patient = None
            for p in all_patients:
                if p.clerk_id == DEMO_PATIENT_ID:
                    patient = p
                    break
            
            if not patient:
                logger.error(f"Patient with ID {DEMO_PATIENT_ID} not found!")
                return
            
        patient_internal_id = str(patient.id)
        logger.info(f"Found patient: {patient.first_name} {patient.last_name} (Internal ID: {patient_internal_id})")
        
        # Create a clinician->AI message (simulating clinician asking about patient)
        clinician_msg_data = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="clinician",
            message_content="I'm experiencing severe side effects and need immediate help",
            message_route="ai"
        )
        
        logger.info("Creating clinician->AI message...")
        clinician_msg = crud.chat_message.create(db, obj_in=clinician_msg_data)
        logger.info(f"Created clinician message ID: {clinician_msg.id}")
        
        # Create an AI->Clinician response (like the action completion message)
        ai_clinician_response = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="agent",
            message_content="Completed 2 of 2 actions: ✓ side_effect_report_create: I've recorded your report of serious side effects. ✓ appointment_create: Appointment created successfully",
            message_route="clinician"  # This is the key - it's routed to clinician
        )
        
        logger.info("Creating AI->Clinician response...")
        ai_clinician_msg = crud.chat_message.create(db, obj_in=ai_clinician_response)
        logger.info(f"Created AI->Clinician response ID: {ai_clinician_msg.id}")
        
        # Also create a normal patient->AI and AI->patient exchange for comparison
        patient_msg_data = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="patient",
            message_content="What are the side effects of my medication?",
            message_route="ai"
        )
        
        logger.info("Creating patient->AI message...")
        patient_msg = crud.chat_message.create(db, obj_in=patient_msg_data)
        logger.info(f"Created patient message ID: {patient_msg.id}")
        
        # Create an AI->Patient response
        ai_patient_response = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="agent",
            message_content="Common side effects include nausea and headache. Contact your doctor if severe.",
            message_route="patient"
        )
        
        logger.info("Creating AI->Patient response...")
        ai_patient_msg = crud.chat_message.create(db, obj_in=ai_patient_response)
        logger.info(f"Created AI->Patient response ID: {ai_patient_msg.id}")
        
        # Now retrieve messages using the patient filter
        logger.info("\n=== Testing Patient View Filter ===")
        filtered_messages = crud.chat_message.get_by_patient_filtered(
            db, 
            patient_id=patient_internal_id,
            skip=0,
            limit=100
        )
        
        logger.info(f"Retrieved {len(filtered_messages)} filtered messages")
        
        # Check what messages are visible
        clinician_msg_visible = False
        ai_clinician_msg_visible = False
        patient_msg_visible = False
        ai_patient_msg_visible = False
        
        for msg in filtered_messages:
            if msg.id == clinician_msg.id:
                clinician_msg_visible = True
                logger.error(f"❌ CLINICIAN->AI message IS visible (should be filtered): {msg.message_content[:50]}...")
            elif msg.id == ai_clinician_msg.id:
                ai_clinician_msg_visible = True
                logger.error(f"❌ AI->CLINICIAN message IS visible (should be filtered): {msg.message_content[:50]}...")
            elif msg.id == patient_msg.id:
                patient_msg_visible = True
                logger.info(f"✓ Patient message is visible (correct): {msg.message_content[:50]}...")
            elif msg.id == ai_patient_msg.id:
                ai_patient_msg_visible = True
                logger.info(f"✓ AI->Patient message is visible (correct): {msg.message_content[:50]}...")
        
        # Verify filtering is working correctly
        logger.info("\n=== Filter Verification ===")
        if not clinician_msg_visible:
            logger.info("✓ Clinician->AI message correctly filtered out")
        else:
            logger.error("✗ Clinician->AI message should NOT be visible to patient!")
            
        if not ai_clinician_msg_visible:
            logger.info("✓ AI->Clinician message correctly filtered out")
        else:
            logger.error("✗ AI->Clinician message should NOT be visible to patient!")
            
        if patient_msg_visible:
            logger.info("✓ Patient message correctly visible")
        else:
            logger.error("✗ Patient message should be visible!")
            
        if ai_patient_msg_visible:
            logger.info("✓ AI->Patient message correctly visible")
        else:
            logger.error("✗ AI->Patient message should be visible!")
        
        # Clean up test messages
        logger.info("\nCleaning up test messages...")
        crud.chat_message.remove(db, id=clinician_msg.id)
        crud.chat_message.remove(db, id=ai_clinician_msg.id)
        crud.chat_message.remove(db, id=patient_msg.id)
        crud.chat_message.remove(db, id=ai_patient_msg.id)
        logger.info("Test messages cleaned up")
        
        # Final result
        if not clinician_msg_visible and not ai_clinician_msg_visible and patient_msg_visible and ai_patient_msg_visible:
            logger.info("\n✓ TEST PASSED: Clinician-AI conversations are correctly filtered from patient view!")
        else:
            logger.error("\n✗ TEST FAILED: Filtering is not working correctly!")
            
    except Exception as e:
        logger.error(f"Error during test: {str(e)}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_clinician_ai_filtering())