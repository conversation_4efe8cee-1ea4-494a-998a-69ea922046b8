#!/usr/bin/env python
"""
Test what event logs a clinician can see.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.session import <PERSON>L<PERSON>al
from app import crud

def test_clinician_access():
    """Test clinician access to event logs."""
    db = SessionLocal()
    try:
        # Get the clinician
        clinician = crud.clinician.get_clinician_by_clerk_id(
            db, clerk_id="user_2waSREJSlduBPyK6Vbv9TU3VhI7"
        )
        
        if not clinician:
            print("Clinician not found!")
            return
            
        print(f"Testing as clinician: {clinician.first_name} {clinician.last_name}")
        print(f"Clinician ID: {clinician.id}")
        
        # Check assigned patients
        assigned_patients = db.execute(text("""
            SELECT p.id, p.first_name, p.last_name
            FROM patients p
            JOIN clinician_patient_association cpa ON p.id = cpa.patient_id
            WHERE cpa.clinician_id = :clinician_id
        """), {"clinician_id": clinician.id})
        
        print("\nAssigned patients:")
        patient_ids = []
        for patient in assigned_patients:
            patient_ids.append(patient.id)
            print(f"  - {patient.first_name} {patient.last_name} (ID: {patient.id})")
        
        # Get event logs different ways
        print("\n1. Getting logs by actor_role='patient':")
        logs = crud.event_log.get_by_actor_role(db, actor_role="patient", limit=5)
        print(f"   Found {len(logs)} logs")
        for log in logs[:3]:
            print(f"   - {log.created_at}: {log.action} {log.target_resource_type}")
        
        print("\n2. Getting all logs (no filter):")
        logs = crud.event_log.get_multi(db, limit=5)
        print(f"   Found {len(logs)} logs")
        for log in logs[:3]:
            print(f"   - {log.created_at}: {log.action} {log.target_resource_type}")
            
        # Check if any logs are for assigned patients
        print("\n3. Checking which logs are for assigned patients:")
        all_patient_logs = crud.event_log.get_by_actor_role(db, actor_role="patient", limit=20)
        assigned_count = 0
        for log in all_patient_logs:
            if log.actor_user_id in patient_ids:
                assigned_count += 1
                print(f"   - Log for assigned patient: {log.actor_user_id}")
        
        print(f"\n   Total: {assigned_count} logs for assigned patients out of {len(all_patient_logs)} patient logs")
        
    finally:
        db.close()


if __name__ == "__main__":
    test_clinician_access()