#!/usr/bin/env python3
"""Direct test of compound action parameter enrichment."""

import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.schemas.action_chain_v2 import ChainedAction, ChainedIntent, ExecutionMode, FailureMode, ActionDependency
from app import crud

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test patient and medication data
TEST_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"  # Michael <PERSON>ient 
TEST_MEDICATION_NAME = "Ozempic"

async def ensure_patient_has_medication(db: Session, patient_id: str, medication_name: str):
    """Ensure the test patient has an approved medication request."""
    # Check if patient has an approved medication request
    existing_meds = crud.medication_request.get_active_by_patient(
        db=db,
        patient_id=patient_id,
        limit=1
    )
    
    if existing_meds and existing_meds[0].medication_name == medication_name:
        logger.info(f"✓ Patient already has approved {medication_name}")
        return
    
    # Create an approved medication request
    from app.schemas.medication_request import MedicationRequestCreate
    from app.models.medication_request import MedicationRequestStatus
    
    med_request = MedicationRequestCreate(
        medication_name=medication_name,
        dosage="0.5mg",
        frequency="Weekly",
        duration="Ongoing",
        notes="Test medication for compound action"
    )
    
    created_request = crud.medication_request.create_medication_request(
        db=db,
        obj_in=med_request,
        patient_id=patient_id
    )
    
    # Update status to approved
    created_request.status = MedicationRequestStatus.APPROVED
    db.add(created_request)
    db.commit()
    db.refresh(created_request)
    
    logger.info(f"✓ Created approved medication request for {medication_name}")

async def test_compound_action_directly():
    """Test compound action execution directly without going through chat."""
    db = SessionLocal()
    
    try:
        # Ensure patient has medication
        await ensure_patient_has_medication(db, TEST_PATIENT_ID, TEST_MEDICATION_NAME)
        
        logger.info("\n=== Direct Compound Action Test ===")
        logger.info(f"Patient: {TEST_PATIENT_ID}")
        logger.info("Simulating: 'I'm having severe nausea from my medication and need to schedule an appointment'")
        
        # Create a compound action manually
        # Primary action: side effect report (missing medication_name)
        primary_action = ChainedIntent(
            action_type="side_effect_report_create",
            parameters={
                "symptoms": "severe nausea",
                "severity": "Severe",
                # medication_name intentionally omitted to test prompting
            },
            confidence=0.9
        )
        
        # Follow-up action: appointment request
        follow_up_action = ChainedIntent(
            action_type="appointment_request_create",
            parameters={
                "preferred_date": "2025-05-29",
                "preferred_time": "14:00",
                "reason": "Follow-up for severe nausea side effects"
            },
            confidence=0.9,
            dependencies=[ActionDependency(
                required_action="side_effect_report_create",
                require_success=True
            )]
        )
        
        # Create the chained action
        chain = ChainedAction(
            primary_action=primary_action,
            follow_up_actions=[follow_up_action],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.STOP_ON_FAILURE
        )
        
        logger.info("\nCreated chain with:")
        logger.info(f"  Primary: {primary_action.action_type} - params: {primary_action.parameters}")
        logger.info(f"  Follow-up: {follow_up_action.action_type} - params: {follow_up_action.parameters}")
        
        # Create context that mimics what the LLM module would pass
        context = {
            "user_id": TEST_PATIENT_ID,
            "user_role": "patient",
            "client_timezone_offset": -6.0,
            "db": db  # Include db in context
        }
        
        # Test without enrichment to see error handling
        logger.info("\n=== Testing WITHOUT Parameter Enrichment ===")
        
        # Execute the chain directly without enrichment
        chain_executor = ActionChainExecutorService(db)
        
        result = await chain_executor.execute_chain(
            user_id=TEST_PATIENT_ID,
            user_role="patient",
            chain=chain,
            initial_context=context
        )
        
        logger.info(f"\nChain execution result:")
        logger.info(f"  Success: {result.success}")
        logger.info(f"  Chain ID: {result.chain_id}")
        logger.info(f"  Execution time: {result.execution_time_ms}ms")
        
        if result.results:
            logger.info(f"\n  Action results:")
            for action_result in result.results:
                status = "✓" if action_result.success else "✗"
                logger.info(f"    {status} {action_result.action_type}:")
                logger.info(f"       Message: {action_result.message if action_result.success else action_result.error_message}")
                if action_result.data:
                    logger.info(f"       Data: {action_result.data}")
        
        if not result.success:
            logger.error(f"  Error: {result.error_message}")
            
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
    finally:
        db.close()

async def main():
    """Run the test."""
    logger.info("Testing compound action parameter enrichment...")
    await test_compound_action_directly()

if __name__ == "__main__":
    asyncio.run(main())