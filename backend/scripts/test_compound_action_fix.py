#!/usr/bin/env python3
"""Test script to verify compound action functionality after dependency fix."""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.services.intent_resolver_service import IntentResolverService
from app.schemas.action_chain_v2 import ChainedAction, ChainedIntent, ActionDependency


async def test_compound_action_parsing():
    """Test that compound actions are correctly parsed with ActionDependency objects."""
    
    # Test input that should trigger compound action
    test_input = "<PERSON> reported severe nausea from <PERSON><PERSON><PERSON> this morning. Schedule him for a follow-up appointment tomorrow at 2pm to discuss alternative medications."
    
    # Mock LLM response that would cause the validation error
    mock_llm_response = {
        "type": "compound",
        "primary_action": {
            "action_type": "side_effect_report",
            "parameters": [
                {"name": "patient_id", "value": "patient_michael"},
                {"name": "medication_name", "value": "Ozempic"},
                {"name": "symptoms", "value": "severe nausea"},
                {"name": "severity", "value": "Severe"},
                {"name": "onset_time", "value": "this morning"}
            ],
            "confidence": 0.95
        },
        "follow_up_actions": [
            {
                "action_type": "appointment_create",
                "parameters": [
                    {"name": "patient_id", "value": "patient_michael"},
                    {"name": "appointment_time", "value": "tomorrow at 2pm"},
                    {"name": "appointment_type", "value": "follow_up"},
                    {"name": "reason", "value": "discuss alternative medications for nausea"},
                    {"name": "duration_minutes", "value": 30}
                ],
                "confidence": 0.9,
                "dependencies": ["primary_action"],  # This was causing the validation error
                "context_requirements": ["appointment_time"]
            }
        ],
        "execution_mode": "sequential"
    }
    
    print("Testing compound action parsing with dependency conversion...")
    print(f"Test input: {test_input}")
    print("-" * 80)
    
    # Test the conversion logic directly
    try:
        # Simulate the conversion that happens in _process_compound_action
        follow_up = mock_llm_response["follow_up_actions"][0]
        dependencies = []
        
        for dep in follow_up["dependencies"]:
            if isinstance(dep, str):
                print(f"Converting string dependency '{dep}' to ActionDependency object")
                dependencies.append(ActionDependency(
                    required_action=dep,
                    require_success=True
                ))
            else:
                dependencies.append(dep)
        
        # Create ChainedIntent with converted dependencies
        intent = ChainedIntent(
            action_type=follow_up["action_type"],
            parameters={p["name"]: p["value"] for p in follow_up["parameters"]},
            confidence=follow_up["confidence"],
            dependencies=dependencies,
            context_requirements=follow_up.get("context_requirements", [])
        )
        
        print(f"\n✅ Successfully created ChainedIntent with {len(dependencies)} dependencies")
        print(f"   Dependencies: {[d.required_action for d in dependencies]}")
        
        # Create the full ChainedAction
        primary_intent = ChainedIntent(
            action_type=mock_llm_response["primary_action"]["action_type"],
            parameters={p["name"]: p["value"] for p in mock_llm_response["primary_action"]["parameters"]},
            confidence=mock_llm_response["primary_action"]["confidence"]
        )
        
        chain = ChainedAction(
            primary_action=primary_intent,
            follow_up_actions=[intent],
            execution_mode="sequential"
        )
        
        print(f"\n✅ Successfully created ChainedAction")
        print(f"   Primary action: {chain.primary_action.action_type}")
        print(f"   Follow-up actions: {[a.action_type for a in chain.follow_up_actions]}")
        print(f"   Execution mode: {chain.execution_mode}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print(f"   Type: {type(e).__name__}")
        return False


async def test_full_service():
    """Test the full IntentResolverService with a compound action."""
    
    print("\n" + "=" * 80)
    print("Testing compound action detection patterns...")
    
    # Test compound action detection
    test_queries = [
        "Michael Patient reported severe nausea from Ozempic this morning. Schedule him for a follow-up appointment tomorrow at 2pm to discuss alternative medications.",
        "Log weight of 185 lbs for patient_123 and schedule a nutrition consultation if BMI is over 30",
        "Report headache from Metformin and request a different medication"
    ]
    
    for query in test_queries:
        print(f"\nTesting: {query[:80]}...")
        try:
            # Test that the query would be recognized as compound
            compound_indicators = ["and", "also", "then", "after that", ". ", "schedule", "report"]
            is_compound = any(indicator in query.lower() for indicator in compound_indicators)
            print(f"   Detected as compound: {is_compound}")
            
            # Analyze the query structure
            sentences = query.split(". ")
            if len(sentences) > 1:
                print(f"   Found {len(sentences)} sentences - likely compound action")
            
            # Look for action keywords
            action_keywords = ["report", "schedule", "log", "request", "book", "create"]
            found_actions = [kw for kw in action_keywords if kw in query.lower()]
            if len(found_actions) > 1:
                print(f"   Found multiple action keywords: {found_actions}")
                
        except Exception as e:
            print(f"   Error: {e}")


async def main():
    """Run all tests."""
    print("Starting compound action tests...")
    print("=" * 80)
    
    # Test 1: Direct parsing test
    parsing_success = await test_compound_action_parsing()
    
    # Test 2: Full service test
    await test_full_service()
    
    print("\n" + "=" * 80)
    print(f"Test Results:")
    print(f"  Parsing test: {'✅ PASSED' if parsing_success else '❌ FAILED'}")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())