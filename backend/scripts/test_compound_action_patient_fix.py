#!/usr/bin/env python3
"""Test script to verify compound action auto-fill for patient users."""

import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.services.intent_resolver_service import IntentResolverService
from app.services.chat_modules.llm_action_module import LLMActionModule  
from app.models.template import Template
from app import crud

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test patient and medication data
TEST_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"  # Michael <PERSON>ient 
TEST_MEDICATION_NAME = "Ozempic"

async def ensure_patient_has_medication(db: Session, patient_id: str, medication_name: str):
    """Ensure the test patient has an approved medication request."""
    # Check if patient has an approved medication request
    existing_meds = crud.medication_request.get_active_by_patient(
        db=db,
        patient_id=patient_id,
        limit=1
    )
    
    if existing_meds and existing_meds[0].medication_name == medication_name:
        logger.info(f"✓ Patient already has approved {medication_name}")
        return
    
    # Create an approved medication request
    from app.schemas.medication_request import MedicationRequestCreate
    from app.models.medication_request import MedicationRequestStatus
    
    med_request = MedicationRequestCreate(
        medication_name=medication_name,
        dosage="0.5mg",
        frequency="Weekly",
        duration="Ongoing",
        notes="Test medication for compound action"
    )
    
    created_request = crud.medication_request.create_medication_request(
        db=db,
        obj_in=med_request,
        patient_id=patient_id
    )
    
    # Update status to approved
    created_request.status = MedicationRequestStatus.APPROVED
    db.add(created_request)
    db.commit()
    db.refresh(created_request)
    
    logger.info(f"✓ Created approved medication request for {medication_name}")

async def test_compound_action_with_llm_module():
    """Test compound action through the LLM Action Module."""
    db = SessionLocal()
    
    try:
        # Ensure patient has medication
        await ensure_patient_has_medication(db, TEST_PATIENT_ID, TEST_MEDICATION_NAME)
        
        # Test message
        test_message = "I'm having severe nausea from my medication and need to schedule an appointment"
        
        # Initialize LLM Action Module
        llm_module = LLMActionModule()
        
        # Check if module should handle this message
        should_handle = llm_module.should_handle_message(test_message)
        logger.info(f"\nShould LLM Module handle message: {should_handle}")
        
        if not should_handle:
            logger.error("LLM Module did not recognize this as a command!")
            return
        
        # Create context for patient user
        context = {
            "user_id": TEST_PATIENT_ID,
            "user_role": "patient",
            "db": db,
            "client_timezone_offset": -6.0,  # CST
            "template_id": None,  # Let it find appropriate template
        }
        
        logger.info("\n=== Testing Compound Action Processing ===")
        logger.info(f"User: {TEST_PATIENT_ID} (patient)")
        logger.info(f"Message: {test_message}")
        
        # Process the message
        result = await llm_module.process_message(test_message, context)
        
        # Check result
        logger.info("\n=== Result ===")
        logger.info(f"Response: {result.get('response')}")
        logger.info(f"Action: {result.get('action')}")
        
        if result.get('metadata'):
            metadata = result['metadata']
            logger.info(f"Success: {metadata.get('success')}")
            logger.info(f"Module: {metadata.get('module')}")
            
            if metadata.get('compound_action'):
                logger.info("✓ Compound action detected!")
                logger.info(f"Execution time: {metadata.get('execution_time_ms')}ms")
                
                # Check action results
                if result.get('action', {}).get('results'):
                    logger.info("\n=== Action Results ===")
                    for action_result in result['action']['results']:
                        status = "✓" if action_result['success'] else "✗"
                        logger.info(f"{status} {action_result['action_type']}: {action_result['message']}")
                        if action_result.get('data'):
                            logger.info(f"   Data: {action_result['data']}")
            else:
                logger.warning("⚠ Not detected as compound action")
                
                # Check if it's a validation error
                if metadata.get('error_type') == 'VALIDATION_ERROR':
                    logger.info("Validation error - checking missing parameters...")
                    if metadata.get('previous_parameters'):
                        logger.info(f"Collected parameters: {metadata['previous_parameters']}")
                    
        else:
            logger.warning("No metadata in result")
            
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
    finally:
        db.close()

async def test_intent_resolution_directly():
    """Test intent resolution directly to see what's being extracted."""
    db = SessionLocal()
    
    try:
        # Get a template for the test
        templates = crud.template.get_multi(db)  # Not async
        if not templates:
            logger.error("No templates found!")
            return
            
        template = templates[0]
        
        # Initialize intent resolver
        resolver = IntentResolverService()
        
        # Test message
        test_message = "I'm having severe nausea from my medication and need to schedule an appointment"
        
        logger.info("\n=== Direct Intent Resolution Test ===")
        logger.info(f"Message: {test_message}")
        
        # Extract intent
        result, actions = await resolver.extract_intent(
            template=template,
            user_input=test_message,
            client_timezone_offset=-6.0,
            user_role="patient",
            db=db
        )
        
        # Check if it's a compound action
        from app.schemas.action_chain_v2 import ChainedAction
        if isinstance(result, ChainedAction):
            logger.info("✓ Detected as ChainedAction!")
            logger.info(f"Primary action: {result.primary_action.action_type}")
            logger.info(f"Primary params: {result.primary_action.parameters}")
            logger.info(f"Follow-up actions: {len(result.follow_up_actions)}")
            for i, action in enumerate(result.follow_up_actions):
                logger.info(f"  {i+1}. {action.action_type}: {action.parameters}")
        else:
            logger.info(f"Single action detected: {result.action_type}")
            logger.info(f"Parameters: {[(p.name, p.value) for p in result.parameters]}")
            
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
    finally:
        db.close()

async def main():
    """Run all tests."""
    logger.info("Starting compound action tests for patient users...")
    
    # Test intent resolution directly
    await test_intent_resolution_directly()
    
    # Test through LLM module
    await test_compound_action_with_llm_module()

if __name__ == "__main__":
    asyncio.run(main())