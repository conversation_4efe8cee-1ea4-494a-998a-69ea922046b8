#!/usr/bin/env python
"""Test script to verify compound action state persistence fix."""

import asyncio
import json
import os
import sys
import uuid
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session

from app import crud
from app.core.config import settings
from app.db.session import SessionLocal
from app.models.patient import Patient
from app.schemas.chat import ChatMessageRequest
from app.services.chat_agent import process_chat_message


async def test_compound_action_with_state():
    """Test compound action with state persistence between messages."""
    db = SessionLocal()
    
    try:
        # Use the demo patient
        demo_patient_id = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
        
        print("=" * 80)
        print("TESTING COMPOUND ACTION STATE PERSISTENCE")
        print("=" * 80)
        
        # First message: compound action with missing parameter
        print("\n1. Sending initial compound action request...")
        print("   Message: 'I'm having severe nausea from my medication and need to schedule an appointment'")
        
        context1 = {
            "user_role": "patient",
            "userRole": "patient",
            "conversationType": "patient",
            "message_route": "ai"
        }
        
        response1 = await process_chat_message(
            user_id=demo_patient_id,
            user_message="I'm having severe nausea from my medication and need to schedule an appointment",
            db=db,
            structured_output=True,
            context=context1
        )
        
        print(f"\n   Response: {response1.get('response', response1)}")
        print(f"   Metadata: {json.dumps(response1.get('metadata', {}), indent=2)}")
        
        # Check if we got pending compound action in metadata
        pending_action = response1.get('metadata', {}).get('pending_compound_action')
        if pending_action:
            print("\n   ✓ Pending compound action detected in response metadata!")
            print(f"   Failed action: {pending_action.get('failed_action')}")
            print(f"   Missing params: {pending_action.get('missing_params')}")
        else:
            print("\n   ✗ No pending compound action in response metadata!")
        
        # Second message: provide the missing parameter
        print("\n2. Providing missing parameter...")
        print("   Message: 'tirzepatide, i think'")
        
        # Include the pending compound action in context
        context2 = {
            "user_role": "patient",
            "userRole": "patient", 
            "conversationType": "patient",
            "message_route": "ai",
            "pending_compound_action": pending_action  # Pass the pending action
        }
        
        response2 = await process_chat_message(
            user_id=demo_patient_id,
            user_message="tirzepatide, i think",
            db=db,
            structured_output=True,
            context=context2
        )
        
        print(f"\n   Response: {response2.get('response', response2)}")
        print(f"   Action: {response2.get('action', {})}")
        print(f"   Metadata: {json.dumps(response2.get('metadata', {}), indent=2)}")
        
        # Check if compound action was completed
        if response2.get('action', {}).get('type') == 'compound':
            print("\n   ✓ Compound action executed successfully!")
            results = response2.get('action', {}).get('results', [])
            for result in results:
                status = "✓" if result.get('success') else "✗"
                print(f"   {status} {result.get('action_type')}: {result.get('message')}")
        else:
            print(f"\n   ✗ Compound action not executed. Module: {response2.get('metadata', {}).get('module')}")
        
        print("\n" + "=" * 80)
        print("TEST COMPLETE")
        print("=" * 80)
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_compound_action_with_state())