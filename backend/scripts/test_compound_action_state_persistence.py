#!/usr/bin/env python3
"""Test compound action state persistence across messages."""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from app.core.config import settings
from app.db.session import SessionLocal
from app.services.chatbot_manager import Cha<PERSON><PERSON><PERSON>ana<PERSON>


async def test_compound_action_persistence():
    """Test that compound action state persists across messages."""
    print("\n=== Testing Compound Action State Persistence ===\n")
    
    db = SessionLocal()
    chatbot = ChatbotManager(db)
    
    try:
        # Demo patient ID
        patient_id = "385b5f8e-5e03-4b2f-9b6a-1f6c7b8e9014"
        
        # First message: Request compound action without medication name
        print("1. Sending initial compound action request...")
        context = {
            "user_id": patient_id,
            "user_role": "patient",
            "conversationType": "patient",
            "message_route": "ai",
            "timezone_offset": -5,
        }
        
        response1 = await chatbot.process_message(
            user_id=patient_id,
            message="I'm having terrible headaches and feeling dizzy. Can you report these side effects and schedule an appointment with my doctor?",
            context=context
        )
        
        print(f"Response 1: {response1}")
        print(f"Metadata 1: {json.dumps(response1.get('metadata', {}), indent=2)}")
        
        # Check if pending_compound_action is in metadata
        pending_action = response1.get("metadata", {}).get("pending_compound_action")
        if pending_action:
            print(f"\n✓ Found pending_compound_action in response metadata!")
            print(f"  - Failed action: {pending_action.get('failed_action')}")
            print(f"  - Missing params: {pending_action.get('missing_params')}")
        else:
            print("\n✗ No pending_compound_action found in response metadata!")
            return
        
        # Second message: Provide missing medication name with pending action in context
        print("\n2. Sending follow-up with medication name and pending action...")
        context_with_pending = {
            **context,
            "pending_compound_action": pending_action
        }
        
        print(f"Context for message 2: {json.dumps(context_with_pending, indent=2)}")
        
        response2 = await chatbot.process_message(
            user_id=patient_id,
            message="Ozempic",
            context=context_with_pending
        )
        
        print(f"\nResponse 2: {response2}")
        print(f"Metadata 2: {json.dumps(response2.get('metadata', {}), indent=2)}")
        
        # Check if the compound action was completed
        if response2.get("action", {}).get("type") == "compound":
            print("\n✓ Compound action successfully completed!")
            results = response2.get("action", {}).get("results", [])
            for result in results:
                status = "✓" if result.get("success") else "✗"
                print(f"  {status} {result.get('action_type')}: {result.get('message')}")
        else:
            print("\n✗ Compound action was not completed")
            
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_compound_action_persistence())