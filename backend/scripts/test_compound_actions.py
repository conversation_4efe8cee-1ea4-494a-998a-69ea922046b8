"""
Integration test for the Advanced Actions System

This script tests the compound action functionality end-to-end.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
from uuid import uuid4

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.services.intent_resolver_service import IntentResolverService
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.services.action_patterns import ActionPatterns, PatternDetector
from app.schemas.action_chain_v2 import ChainedIntent, ChainedAction, ExecutionMode, FailureMode, ChainContext
from app.models import Template
from app.crud.crud_template import template as crud_template

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_pattern_detection():
    """Test pattern detection from natural language."""
    test_inputs = [
        "Schedule an appointment tomorrow at 2pm and remind me the day before",
        "I'm experiencing nausea from the medication and need to schedule a follow-up",
        "Log my weight as 185 pounds and check if I hit any milestones",
        "Send a notification to all patients about the clinic closure",
        "Request Ozempic prescription and send me information about it"
    ]
    
    logger.info("Testing pattern detection...")
    for input_text in test_inputs:
        pattern = PatternDetector.detect_pattern(input_text)
        logger.info(f"Input: '{input_text}'")
        logger.info(f"Detected pattern: {pattern}\n")


async def test_appointment_with_reminder(db: Session):
    """Test the appointment + reminder pattern."""
    logger.info("Testing appointment with reminder pattern...")
    
    # Create a pattern
    pattern = ActionPatterns.appointment_with_reminder(
        patient_id="test_patient_123",
        scheduled_time=(datetime.now() + timedelta(days=7)).isoformat() + "+00:00",
        duration_minutes=30,
        appointment_type="Follow-up",
        reminder_days_before=1,
        additional_notes="Discuss medication side effects"
    )
    
    logger.info(f"Created pattern with {len(pattern.follow_up_actions) + 1} actions")
    logger.info(f"Primary action: {pattern.primary_action.action_type}")
    for action in pattern.follow_up_actions:
        logger.info(f"Follow-up action: {action.action_type}")
    
    # Test context substitution
    executor = ActionChainExecutorService(db)
    test_context = {"appointment_id": "apt_123", "scheduled_time": "2025-04-21T14:00:00+00:00"}
    
    # Test parameter substitution
    follow_up_params = pattern.follow_up_actions[0].parameters
    enriched_params = executor._apply_context_substitutions(follow_up_params, 
        ChainContext(data=test_context))
    
    logger.info(f"Original message: {follow_up_params['message']}")
    logger.info(f"Enriched message: {enriched_params['message']}")


async def test_compound_intent_detection(db: Session):
    """Test detecting compound intents from natural language."""
    logger.info("\nTesting compound intent detection...")
    
    resolver = IntentResolverService()
    
    # Test compound action detection
    test_input = "Schedule an appointment tomorrow at 2pm and remind me the day before"
    compound_info = await resolver.detect_compound_action(test_input)
    
    logger.info(f"Input: '{test_input}'")
    logger.info(f"Is compound: {compound_info['is_compound']}")
    logger.info(f"Action count: {compound_info['action_count']}")
    logger.info(f"Detected actions: {compound_info['detected_actions']}")
    logger.info(f"Relationship: {compound_info['relationship']}")


async def test_manual_chain_creation():
    """Test creating and validating a chain manually."""
    logger.info("\nTesting manual chain creation...")
    
    # Create a chain with dependencies
    appointment = ChainedIntent(
        action_type="appointment_create",
        parameters={
            "patient_id": "patient_123",
            "scheduled_time": "2025-04-22T14:00:00+00:00",
            "duration_minutes": 30,
            "appointment_type": "Follow-up"
        }
    )
    
    reminder = ChainedIntent(
        action_type="notification_create",
        parameters={
            "recipient_id": "{{patient_id}}",
            "message": "Appointment {{appointment_id}} scheduled for {{scheduled_time}}"
        },
        dependencies=[{
            "required_action": "appointment_create",
            "require_success": True
        }]
    )
    
    confirm_email = ChainedIntent(
        action_type="email_send",
        parameters={
            "to": "<EMAIL>",
            "subject": "Appointment Confirmation",
            "body": "Your appointment {{appointment_id}} is confirmed"
        },
        dependencies=[{
            "required_action": "appointment_create",
            "require_success": True
        }]
    )
    
    chain = ChainedAction(
        primary_action=appointment,
        follow_up_actions=[reminder, confirm_email],
        execution_mode=ExecutionMode.PARALLEL,  # Send reminder and email at same time
        failure_mode=FailureMode.STOP_ON_FAILURE
    )
    
    logger.info(f"Created chain: {chain.chain_id}")
    logger.info(f"Execution mode: {chain.execution_mode}")
    logger.info(f"Total actions: {len(chain.follow_up_actions) + 1}")


async def test_side_effect_pattern(db: Session):
    """Test the side effect with follow-up pattern."""
    logger.info("\nTesting side effect with follow-up pattern...")
    
    # Create pattern for severe side effect
    severe_pattern = ActionPatterns.side_effect_with_followup(
        patient_id="patient_456",
        medication_name="Ozempic",
        symptoms="severe nausea and vomiting",
        severity="Severe",
        onset_time="2 hours after taking medication",
        followup_days=1,
        notes="Unable to keep food down"
    )
    
    logger.info(f"Severe side effect pattern has {len(severe_pattern.follow_up_actions)} follow-up actions")
    
    # Create pattern for mild side effect
    mild_pattern = ActionPatterns.side_effect_with_followup(
        patient_id="patient_789",
        medication_name="Wegovy",
        symptoms="mild headache",
        severity="Mild",
        onset_time="This morning",
        followup_days=3
    )
    
    logger.info(f"Mild side effect pattern has {len(mild_pattern.follow_up_actions)} follow-up actions")


async def test_batch_notifications():
    """Test batch notification pattern."""
    logger.info("\nTesting batch notification pattern...")
    
    # Create batch notification
    batch_pattern = ActionPatterns.batch_notifications(
        recipient_ids=["patient_1", "patient_2", "patient_3", "patient_4"],
        notification_type="clinic_update",
        title="Clinic Hours Change",
        message="Our clinic will have new hours starting next month",
        priority="medium",
        metadata={"effective_date": "2025-05-01"}
    )
    
    logger.info(f"Batch pattern will send {len(batch_pattern.follow_up_actions) + 1} notifications")
    logger.info(f"Execution mode: {batch_pattern.execution_mode}")


async def test_template_integration(db: Session):
    """Test integration with template system."""
    logger.info("\nTesting template integration...")
    
    # Get or create a test template
    test_template = crud_template.get_by_name(db, name="Advanced Actions Test")
    
    if not test_template:
        # Create a template with compound action support
        from app.schemas.template import TemplateCreate
        
        template_data = TemplateCreate(
            name="Advanced Actions Test",
            description="Template for testing compound actions",
            category="test",
            system_prompt="You are a helpful assistant for testing compound actions.",
            role_restrictions=["patient", "clinician"],
            actions=[
                {
                    "action_type": "appointment_create",
                    "description": "Create an appointment",
                    "parameters": []
                },
                {
                    "action_type": "notification_create", 
                    "description": "Create a notification",
                    "parameters": []
                },
                {
                    "action_type": "side_effect_report_create",
                    "description": "Report side effects",
                    "parameters": []
                },
                {
                    "action_type": "weight_log_create",
                    "description": "Log weight",
                    "parameters": []
                }
            ]
        )
        
        test_template = crud_template.create(db, obj_in=template_data)
        logger.info(f"Created test template: {test_template.id}")
    else:
        logger.info(f"Using existing template: {test_template.id}")
    
    # Test with intent resolver
    resolver = IntentResolverService()
    
    # Process a compound request
    result = await resolver.process_user_input_with_compound_support(
        user_id="test_user",
        user_role="patient",
        template_id=str(test_template.id),
        user_input="Schedule appointment tomorrow at 3pm and send reminder",
        db=db,
        client_timezone_offset=-5.0
    )
    
    logger.info(f"Result type: {result.get('type') if isinstance(result, dict) else 'single'}")
    
    if isinstance(result, dict) and result.get('type') == 'compound':
        chain = result['chain']
        logger.info(f"Detected compound action with {len(chain.follow_up_actions) + 1} actions")


def main():
    """Run all integration tests."""
    db = SessionLocal()
    
    try:
        # Run async tests
        asyncio.run(test_pattern_detection())
        asyncio.run(test_appointment_with_reminder(db))
        asyncio.run(test_compound_intent_detection(db))
        asyncio.run(test_manual_chain_creation())
        asyncio.run(test_side_effect_pattern(db))
        asyncio.run(test_batch_notifications())
        asyncio.run(test_template_integration(db))
        
        logger.info("\n✅ All integration tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    main()