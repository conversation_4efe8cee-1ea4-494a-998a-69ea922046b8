"""Test script for compound action execution after parameter completion."""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from uuid import UUID

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session

from app import crud
from app.db.session import SessionLocal
from app.services.chat_modules.llm_action_module import LLMActionModule
from app.services.intent_resolver_service import IntentResolverService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_compound_execution():
    """Test compound action execution with completed parameters."""
    db = SessionLocal()
    
    try:
        # Get demo patient
        patient = crud.patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        if not patient:
            logger.error("Demo patient not found")
            return
            
        patient_id = str(patient.id)
        logger.info(f"Testing with patient: {patient.first_name} {patient.last_name} (ID: {patient_id})")
        
        # Initialize LLM Action Module
        llm_module = LLMActionModule()
        
        # Test: Simulate compound parameter completion
        logger.info("\n=== Test: Compound Action Execution with Completed Parameters ===")
        
        # Create a test chain ID (normally this would come from previous interaction)
        chain_id = "test-chain-12345"
        
        # Simulate the completed parameters
        completed_params = {
            "side_effect_report_create_0": {
                "medication_name": "Semaglutide",
                "symptoms": "headache",
                "severity": "Moderate",
                "onset_time": "Yesterday evening",
                "additional_notes": "Started after increasing dose",
                "patient_id": patient_id
            },
            "appointment_create_1": {
                "patient_id": patient_id,
                "scheduled_time": "2025-05-30T14:00:00+00:00",
                "duration_minutes": 30,
                "appointment_type": "Follow-up",
                "notes": "Side effect follow-up"
            }
        }
        
        # Create context simulating parameter completion
        context = {
            "user_id": patient_id,
            "user_role": "patient",
            "db": db,
            "client_timezone_offset": -300,  # EST
            "is_compound_parameter_completion": True,
            "chain_id": chain_id,
            "completed_compound_parameters": completed_params
        }
        
        # First, we need to save the compound state (normally done by first call)
        from app.utils.parameter_persistence import get_parameter_manager
        param_manager = get_parameter_manager()
        
        # Save the raw LLM response for reconstruction
        raw_llm_response = {
            "primary_action": {
                "action_type": "side_effect_report_create",
                "parameters": {}
            },
            "follow_up_actions": [{
                "action_type": "appointment_create",
                "parameters": {}
            }],
            "execution_mode": "sequential"
        }
        
        param_manager.save_compound_parameters(
            user_id=patient_id,
            chain_id=chain_id,
            action_states=[{
                "actionType": "side_effect_report_create",
                "actionIndex": 0
            }, {
                "actionType": "appointment_create", 
                "actionIndex": 1
            }],
            raw_llm_response=raw_llm_response
        )
        
        # Now process the parameter completion message
        message = "I've provided the following information:\n\n1. Side Effect Report Create:\n  • Medication Name: Semaglutide\n  • Symptoms: headache\n  • Severity: Moderate\n  • Onset Time: Yesterday evening\n  • Additional Notes: Started after increasing dose\n\n2. Appointment Create:\n  • Scheduled Time: May 30, 2025 at 2:00 PM\n  • Duration Minutes: 30\n  • Appointment Type: Follow-up\n  • Notes: Side effect follow-up"
        
        result = await llm_module.process_message(message, context)
        
        logger.info(f"\nResponse: {result['response']}")
        logger.info(f"\nAction: {result.get('action')}")
        logger.info(f"\nMetadata: {json.dumps(result['metadata'], indent=2)}")
        
        # Check if execution was successful
        if result['metadata'].get('success'):
            logger.info("\n✅ Compound action execution successful!")
            if result['metadata'].get('data', {}).get('results'):
                for action_result in result['metadata']['data']['results']:
                    logger.info(f"\n  Action: {action_result['action_type']}")
                    logger.info(f"  Success: {action_result['success']}")
                    logger.info(f"  Message: {action_result['message']}")
                    if action_result.get('data'):
                        logger.info(f"  Data: {action_result['data']}")
        else:
            logger.error(f"\n❌ Compound action execution failed: {result['response']}")
                        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_compound_execution())