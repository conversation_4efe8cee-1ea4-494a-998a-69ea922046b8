"""Test script for compound parameter harvesting functionality."""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from uuid import UUID

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session

from app import crud
from app.db.session import SessionLocal
from app.services.chat_modules.llm_action_module import LLMActionModule
from app.services.intent_resolver_service import IntentResolverService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_compound_parameter_harvesting():
    """Test compound parameter harvesting with multiple action requests."""
    db = SessionLocal()
    
    try:
        # Get demo patient
        patient = crud.patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        if not patient:
            logger.error("Demo patient not found")
            return
            
        patient_id = str(patient.id)
        logger.info(f"Testing with patient: {patient.first_name} {patient.last_name} (ID: {patient_id})")
        
        # Initialize LLM Action Module
        llm_module = LLMActionModule()
        
        # Test 1: Compound action with multiple missing parameters
        logger.info("\n=== Test 1: Compound Action - Side Effect + Appointment ===")
        context = {
            "user_id": patient_id,
            "user_role": "patient",
            "db": db,
            "client_timezone_offset": -300,  # EST
        }
        
        message = "Report side effect headache and schedule appointment tomorrow"
        
        logger.info(f"Should handle message: {llm_module.should_handle_message(message)}")
        
        if llm_module.should_handle_message(message):
            result = await llm_module.process_message(message, context)
            logger.info(f"Response: {result['response']}")
            logger.info(f"Metadata: {json.dumps(result['metadata'], indent=2)}")
            
            if result['metadata'].get('compound_missing_parameters'):
                logger.info("Compound parameters detected!")
                for action_group in result['metadata']['compound_missing_parameters']:
                    logger.info(f"\nAction: {action_group['actionType']} (index: {action_group['actionIndex']})")
                    logger.info(f"Missing parameters: {len(action_group['missingParameters'])}")
                    logger.info(f"Existing parameters: {action_group.get('existingParameters', {})}")
        
        # Test 2: Another compound action
        logger.info("\n=== Test 2: Compound Action - Weight Log + Side Effect ===")
        message = "I weighed 185 pounds this morning and I'm having bad headaches from Semaglutide"
        
        if llm_module.should_handle_message(message):
            result = await llm_module.process_message(message, context)
            logger.info(f"Response: {result['response']}")
            logger.info(f"Metadata: {json.dumps(result['metadata'], indent=2)}")
            
            if result['metadata'].get('compound_missing_parameters'):
                logger.info("Compound parameters detected!")
                for action_group in result['metadata']['compound_missing_parameters']:
                    logger.info(f"\nAction: {action_group['actionType']} (index: {action_group['actionIndex']})")
                    logger.info(f"Missing parameters: {[p['name'] for p in action_group['missingParameters']]}")
                    logger.info(f"Existing parameters: {action_group.get('existingParameters', {})}")
                        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_compound_parameter_harvesting())