#!/usr/bin/env python3
"""Test the demo API endpoints."""

import httpx
import json
import sys

def test_demo_status():
    """Test the demo status endpoint."""
    print("Testing demo status endpoint...")
    
    # This would normally use a real JWT token from <PERSON> auth
    # For testing, we'll demonstrate the expected behavior
    headers = {
        "Content-Type": "application/json",
        # "Authorization": "Bearer <real-jwt-token>"
    }
    
    try:
        response = httpx.get(
            "http://localhost:8000/api/v1/demo/status",
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 401:
            print("✓ Expected 401 - Authentication required")
            print("  The endpoint is working correctly and requires authentication")
        else:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
        
    return True

def test_demo_reset_validation():
    """Test the demo reset endpoint validation."""
    print("\nTesting demo reset endpoint validation...")
    
    headers = {
        "Content-Type": "application/json",
    }
    
    # Test without confirmation
    try:
        response = httpx.post(
            "http://localhost:8000/api/v1/demo/reset",
            headers=headers,
            json={"confirm": False}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 401:
            print("✓ Expected 401 - Authentication required")
        else:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
        
    return True

def main():
    """Run all tests."""
    print("Demo API Endpoint Tests")
    print("=" * 50)
    
    tests = [
        test_demo_status,
        test_demo_reset_validation,
    ]
    
    results = []
    for test in tests:
        results.append(test())
        
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed < total:
        sys.exit(1)

if __name__ == "__main__":
    main()