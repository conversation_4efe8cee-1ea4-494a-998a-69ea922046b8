#!/usr/bin/env python3
"""
Test the demo reset functionality directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import engine, SessionLocal
# Import all models to ensure relationships are loaded
import app.models
from app.models import <PERSON>ient, Clinician, ChatMessage, Appointment, WeightLog, SideEffectReport, ClinicalNote

def check_demo_data():
    """Check current state of demo data"""
    db = SessionLocal()
    try:
        # Count data
        patients = db.query(Patient).count()
        clinicians = db.query(Clinician).count()
        messages = db.query(ChatMessage).count()
        appointments = db.query(Appointment).count()
        weight_logs = db.query(WeightLog).count()
        side_effects = db.query(SideEffectReport).count()
        clinical_notes = db.query(ClinicalNote).count()
        
        # Check for demo accounts
        demo_clinician = db.query(Clinician).filter(
            Clinician.clerk_id == "user_2waSREJSlduBPyK6Vbv9TU3VhI7"
        ).first()
        
        demo_patient = db.query(Patient).filter(
            Patient.id == "user_2waTCuGL3kQC9k2rY47INdcJXk5"
        ).first()
        
        print("\n=== Demo Data Status ===")
        print(f"Patients: {patients}")
        print(f"Clinicians: {clinicians}")
        print(f"Chat Messages: {messages}")
        print(f"Appointments: {appointments}")
        print(f"Weight Logs: {weight_logs}")
        print(f"Side Effects: {side_effects}")
        print(f"Clinical Notes: {clinical_notes}")
        print(f"\nDemo Clinician: {'✓' if demo_clinician else '✗'} {'(' + demo_clinician.first_name + ' ' + demo_clinician.last_name + ')' if demo_clinician else ''}")
        print(f"Demo Patient: {'✓' if demo_patient else '✗'} {'(' + demo_patient.first_name + ' ' + demo_patient.last_name + ')' if demo_patient else ''}")
        
        return {
            "total_records": patients + messages + appointments + weight_logs + side_effects + clinical_notes,
            "has_demo_accounts": bool(demo_clinician and demo_patient)
        }
        
    finally:
        db.close()

if __name__ == "__main__":
    print("Checking demo data status...")
    status_before = check_demo_data()
    
    if status_before["total_records"] > 100:
        print("\n⚠️  Warning: Large amount of data detected. Consider running demo_reset.py to clean up.")
    
    if not status_before["has_demo_accounts"]:
        print("\n⚠️  Warning: Demo accounts missing. Run seed_demo_data.py to create them.")