#!/usr/bin/env python3
"""
Test script for enhanced RAG system with full chunk context and dynamic thresholds.
Tests the improvements made to document Q&A capabilities.
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List
from uuid import UUID

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session

from app.core.rag_config import RAGConfig
from app.crud.crud_content_chunk import crud_content_chunk
from app.db.session import SessionLocal
from app.services.embedding_pipeline import generate_embeddings
from app.utils.context_enricher import enrich_with_rag, format_context_for_prompt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RAGSystemTester:
    """Test harness for enhanced RAG system."""
    
    def __init__(self, db: Session):
        self.db = db
        self.rag_config = RAGConfig()
        self.test_results = []
        
    async def test_query(
        self, 
        query: str, 
        user_id: str, 
        user_role: str = "patient",
        expected_threshold_type: str = "default"
    ) -> Dict:
        """Test a single query through the enhanced RAG system."""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing query: '{query}'")
        logger.info(f"User role: {user_role}")
        
        # Get dynamic threshold
        dynamic_threshold = self.rag_config.get_dynamic_threshold(
            query, 
            {"user_role": user_role}
        )
        logger.info(f"Dynamic threshold: {dynamic_threshold} (expected type: {expected_threshold_type})")
        
        # Enrich with RAG
        start_time = datetime.now()
        rag_context = await enrich_with_rag(self.db, user_id, query, user_role)
        end_time = datetime.now()
        
        retrieval_time = (end_time - start_time).total_seconds()
        logger.info(f"Retrieval time: {retrieval_time:.3f} seconds")
        
        # Format context for prompt
        formatted_context = format_context_for_prompt(rag_context)
        
        # Analyze results
        chunks_metadata = rag_context.get("rag_chunks_metadata", [])
        chunks_count = len(chunks_metadata)
        
        logger.info(f"Retrieved {chunks_count} chunks")
        
        # Log chunk details
        for i, chunk in enumerate(chunks_metadata, 1):
            logger.info(f"\nChunk {i}:")
            logger.info(f"  Similarity: {chunk.get('similarity_score', 0):.3f}")
            logger.info(f"  Source: {chunk.get('source_title', 'Unknown')}")
            logger.info(f"  Content preview: {chunk.get('chunk_text', '')[:100]}...")
            
        # Check if full content is passed (not truncated)
        if chunks_metadata:
            first_chunk_text = chunks_metadata[0].get("chunk_text", "")
            is_truncated = "..." in formatted_context and len(first_chunk_text) > 150
            logger.info(f"\nFull chunk content passed: {'Yes' if not is_truncated else 'No'}")
            
        # Store results
        result = {
            "query": query,
            "user_role": user_role,
            "dynamic_threshold": dynamic_threshold,
            "expected_threshold_type": expected_threshold_type,
            "chunks_retrieved": chunks_count,
            "retrieval_time": retrieval_time,
            "chunks_metadata": chunks_metadata,
            "formatted_context_length": len(formatted_context)
        }
        
        self.test_results.append(result)
        return result
        
    async def run_test_suite(self):
        """Run a comprehensive test suite for the enhanced RAG system."""
        logger.info("Starting Enhanced RAG System Test Suite")
        
        # Test user IDs (use your actual test user IDs)
        test_patient_id = "user_2waTCuGL3kQC9k2rY47INdcJXk5"  # Michael Patient
        test_clinician_id = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"  # Michael Isaac, MD
        
        # Test queries covering different threshold types
        test_cases = [
            # Specific information queries (high threshold: 0.45)
            {
                "query": "What are the clinic hours?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "specific"
            },
            {
                "query": "What is the phone number for the clinic?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "specific"
            },
            
            # Clinical queries (medium-high threshold: 0.40)
            {
                "query": "What is the typical dosage for Ozempic?",
                "user_id": test_clinician_id,
                "user_role": "clinician",
                "expected_threshold_type": "clinical"
            },
            {
                "query": "What are the side effects of semaglutide?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "clinical"
            },
            
            # Exploratory queries (low threshold: 0.25)
            {
                "query": "Tell me about weight loss medications",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "exploratory"
            },
            {
                "query": "What information do you have about GLP-1 agonists?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "exploratory"
            },
            
            # General queries (default threshold: 0.35)
            {
                "query": "How do I manage nausea?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "default"
            },
            {
                "query": "What should I eat while on this medication?",
                "user_id": test_patient_id,
                "user_role": "patient",
                "expected_threshold_type": "default"
            }
        ]
        
        # Run all test cases
        for test_case in test_cases:
            await self.test_query(**test_case)
            
        # Generate summary report
        self.generate_report()
        
    def generate_report(self):
        """Generate a summary report of test results."""
        logger.info(f"\n{'='*60}")
        logger.info("ENHANCED RAG SYSTEM TEST REPORT")
        logger.info(f"{'='*60}")
        
        # Overall statistics
        total_tests = len(self.test_results)
        avg_retrieval_time = sum(r["retrieval_time"] for r in self.test_results) / total_tests
        avg_chunks = sum(r["chunks_retrieved"] for r in self.test_results) / total_tests
        
        logger.info(f"\nOverall Statistics:")
        logger.info(f"  Total tests: {total_tests}")
        logger.info(f"  Average retrieval time: {avg_retrieval_time:.3f} seconds")
        logger.info(f"  Average chunks retrieved: {avg_chunks:.1f}")
        
        # Threshold distribution
        threshold_counts = {}
        for result in self.test_results:
            threshold = result["dynamic_threshold"]
            threshold_counts[threshold] = threshold_counts.get(threshold, 0) + 1
            
        logger.info(f"\nThreshold Distribution:")
        for threshold, count in sorted(threshold_counts.items()):
            logger.info(f"  {threshold}: {count} queries")
            
        # Performance by query type
        logger.info(f"\nPerformance by Expected Threshold Type:")
        for threshold_type in ["specific", "clinical", "exploratory", "default"]:
            type_results = [r for r in self.test_results if r["expected_threshold_type"] == threshold_type]
            if type_results:
                avg_time = sum(r["retrieval_time"] for r in type_results) / len(type_results)
                avg_chunks = sum(r["chunks_retrieved"] for r in type_results) / len(type_results)
                logger.info(f"  {threshold_type}:")
                logger.info(f"    Average retrieval time: {avg_time:.3f}s")
                logger.info(f"    Average chunks: {avg_chunks:.1f}")
                
        # Check for improvements
        logger.info(f"\nEnhancement Verification:")
        
        # Check if chunks are > 150 chars (not truncated)
        full_content_count = 0
        for result in self.test_results:
            if result["chunks_metadata"]:
                first_chunk = result["chunks_metadata"][0]
                if len(first_chunk.get("chunk_text", "")) > 150:
                    full_content_count += 1
                    
        logger.info(f"  Full content chunks (>150 chars): {full_content_count}/{total_tests}")
        
        # Check if we're retrieving up to 5 chunks
        max_chunks_tests = sum(1 for r in self.test_results if r["chunks_retrieved"] > 3)
        logger.info(f"  Tests retrieving >3 chunks: {max_chunks_tests}")
        
        # Check similarity scores
        scores_present = sum(1 for r in self.test_results 
                           if r["chunks_metadata"] and 
                           all(c.get("similarity_score") is not None for c in r["chunks_metadata"]))
        logger.info(f"  Tests with similarity scores: {scores_present}/{total_tests}")
        
        # Save detailed results to file
        report_path = Path(__file__).parent / f"rag_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        logger.info(f"\nDetailed results saved to: {report_path}")


async def main():
    """Main test execution."""
    db = SessionLocal()
    try:
        tester = RAGSystemTester(db)
        await tester.run_test_suite()
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())