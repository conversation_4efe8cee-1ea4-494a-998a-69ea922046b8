#!/usr/bin/env python3
"""
Test script to verify medication request consistency for <PERSON>
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models import Patient, MedicationRequest

def test_medication_request_consistency():
    """Check if <PERSON>'s medication requests are consistent with the alert scenario"""
    
    db = SessionLocal()
    
    try:
        # Get <PERSON>
        michael_patient = db.query(Patient).filter_by(id="user_2waTCuGL3kQC9k2rY47INdcJXk5").first()
        if not michael_patient:
            print("❌ <PERSON> not found!")
            return False
        
        print(f"✓ Found patient: {michael_patient.first_name} {michael_patient.last_name}")
        
        # Get all medication requests for Michael
        medication_requests = db.query(MedicationRequest).filter_by(patient_id=michael_patient.id).all()
        
        print(f"\n📋 Found {len(medication_requests)} medication requests for <PERSON>:")
        
        plateau_mentions = 0
        rapid_weight_loss_mentions = 0
        
        for i, request in enumerate(medication_requests, 1):
            print(f"\n{i}. {request.medication_name} - {request.status}")
            print(f"   Dosage: {request.dosage}")
            print(f"   Notes: {request.notes[:100]}...")
            
            # Check for contradictions
            if request.notes and "plateau" in request.notes.lower():
                plateau_mentions += 1
                print("   ⚠️  Contains 'plateau' mention")
            
            if request.notes and ("rapid weight loss" in request.notes.lower() or "37 lbs" in request.notes):
                rapid_weight_loss_mentions += 1
                print("   ✓ Consistent with rapid weight loss scenario")
        
        print(f"\n📊 Summary:")
        print(f"   - Plateau mentions: {plateau_mentions}")
        print(f"   - Rapid weight loss mentions: {rapid_weight_loss_mentions}")
        
        if plateau_mentions > 0:
            print("\n❌ INCONSISTENCY DETECTED: Found plateau mentions that contradict the rapid weight loss scenario!")
            return False
        else:
            print("\n✅ All medication requests are consistent with the rapid weight loss alert scenario!")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 Testing medication request consistency for Michael Patient...")
    print("=" * 60)
    
    result = test_medication_request_consistency()
    
    if result:
        print("\n✅ TEST PASSED: No contradictions found!")
    else:
        print("\n❌ TEST FAILED: Please fix the contradictions!")