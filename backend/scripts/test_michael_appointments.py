#!/usr/bin/env python3
"""
Test script to verify <PERSON>'s appointments match his weight loss timeline.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta, timezone

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

from app.models import Patient, Appointment, WeightLog, ChatMessage, PatientAlert

MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"


def check_michael_timeline():
    """Check <PERSON>'s complete timeline: appointments, weight logs, and key events."""
    db = SessionLocal()
    try:
        # Get <PERSON>'s patient record
        michael = db.query(Patient).filter_by(id=MICHAEL_PATIENT_ID).first()
        if not michael:
            print("❌ Michael Patient not found!")
            return
        
        print(f"✅ Found patient: {michael.first_name} {michael.last_name}")
        print("\n" + "="*60)
        print("MICHAEL PATIENT TIMELINE ANALYSIS")
        print("="*60)
        
        # Get appointments
        appointments = db.query(Appointment).filter_by(patient_id=MICHAEL_PATIENT_ID).order_by(Appointment.appointment_datetime).all()
        
        print(f"\n📅 Appointments Found: {len(appointments)}")
        print("-" * 60)
        print(f"{'Date':<12} {'Type':<20} {'Status':<12} {'Reason':<40}")
        print("-" * 60)
        
        for appt in appointments:
            # Make both datetimes timezone-aware for comparison
            current_time = datetime.now(timezone.utc)
            days_from_now = (appt.appointment_datetime - current_time).days
            date_str = appt.appointment_datetime.strftime("%Y-%m-%d")
            print(f"{date_str:<12} {appt.appointment_type:<20} {appt.status:<12} {appt.reason[:40]:<40}")
            if appt.clinician_notes:
                print(f"{'':12} Notes: {appt.clinician_notes[:60]}...")
            if appt.cancellation_reason:
                print(f"{'':12} Cancelled: {appt.cancellation_reason}")
        
        # Get weight logs
        weight_logs = db.query(WeightLog).filter_by(patient_id=MICHAEL_PATIENT_ID).order_by(WeightLog.log_date).all()
        
        if weight_logs:
            print(f"\n⚖️ Weight Loss Timeline:")
            print("-" * 40)
            first_weight = weight_logs[0].weight_kg / 0.453592
            last_weight = weight_logs[-1].weight_kg / 0.453592
            days_span = (weight_logs[-1].log_date - weight_logs[0].log_date).days
            total_loss = first_weight - last_weight
            
            print(f"Start: {first_weight:.1f} lbs ({weight_logs[0].log_date})")
            print(f"End: {last_weight:.1f} lbs ({weight_logs[-1].log_date})")
            print(f"Total Loss: {total_loss:.1f} lbs over {days_span} days")
            print(f"Rate: {total_loss/days_span*7:.1f} lbs/week")
        
        # Check for cancelled appointment during critical period
        cancelled_appts = [a for a in appointments if a.status == "Cancelled"]
        if cancelled_appts:
            print(f"\n⚠️ CRITICAL FINDING: {len(cancelled_appts)} cancelled appointment(s)")
            for appt in cancelled_appts:
                print(f"   - {appt.appointment_datetime.date()}: {appt.reason}")
                print(f"     Cancellation: {appt.cancellation_reason}")
        
        # Check for gap in care
        completed_appts = sorted([a for a in appointments if a.status == "Completed"], 
                                key=lambda x: x.appointment_datetime)
        if len(completed_appts) >= 2:
            max_gap = 0
            gap_start = None
            gap_end = None
            for i in range(1, len(completed_appts)):
                gap = (completed_appts[i].appointment_datetime - completed_appts[i-1].appointment_datetime).days
                if gap > max_gap:
                    max_gap = gap
                    gap_start = completed_appts[i-1].appointment_datetime
                    gap_end = completed_appts[i].appointment_datetime
            
            if max_gap > 14:  # More than 2 weeks
                print(f"\n🚨 Care Gap Detected: {max_gap} days between appointments")
                print(f"   From: {gap_start.date()} to {gap_end.date()}")
                print(f"   This coincides with the period of rapid weight loss!")
        
        # Check for AI alert
        alert = db.query(PatientAlert).filter_by(
            patient_id=MICHAEL_PATIENT_ID,
            alert_type="ai_detected_pattern"
        ).first()
        
        if alert:
            print(f"\n🚨 AI Alert Generated: {alert.created_at.date()}")
            print(f"   Severity: {alert.severity}")
            print(f"   Title: {alert.title}")
        
        # Summary
        print("\n" + "="*60)
        print("TIMELINE SUMMARY")
        print("="*60)
        print("✓ Initial consultation and Wegovy start: ~35-31 days ago")
        print("✓ Early follow-up completed: ~24 days ago") 
        print("✗ MISSED: Critical 2-week follow-up (cancelled ~17 days ago)")
        print("✓ Rapid weight loss occurs: 30-day period")
        print("✓ AI detects pattern and generates alert: ~2 hours ago")
        print("✓ Emergency appointment scheduled: Tomorrow")
        print("\nThis timeline shows how routine care missed the critical symptoms!")
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🔍 Checking Michael Patient's complete timeline...\n")
    check_michael_timeline()