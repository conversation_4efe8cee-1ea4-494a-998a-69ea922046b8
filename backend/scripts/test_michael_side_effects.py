#!/usr/bin/env python3
"""
Test script to verify <PERSON>'s side effects match his clinical scenario.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

from app.models import Patient, SideEffectReport
from app.schemas.side_effect_report import SideEffectStatus

MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"


def check_michael_side_effects():
    """Check <PERSON>'s side effect reports for logical progression."""
    db = SessionLocal()
    try:
        # Get <PERSON>'s patient record
        michael = db.query(Patient).filter_by(id=MICHAEL_PATIENT_ID).first()
        if not michael:
            print("❌ Michael Patient not found!")
            return
        
        print(f"✅ Found patient: {michael.first_name} {michael.last_name}")
        print("\n" + "="*70)
        print("MICHAEL PATIENT SIDE EFFECTS ANALYSIS")
        print("="*70)
        
        # Get side effect reports
        reports = db.query(SideEffectReport).filter_by(
            patient_id=MICHAEL_PATIENT_ID
        ).order_by(SideEffectReport.reported_at).all()
        
        print(f"\n📋 Side Effect Reports Found: {len(reports)}")
        print("-" * 70)
        print(f"{'Date':<12} {'Severity':<10} {'Status':<10} {'Description':<45}")
        print("-" * 70)
        
        status_counts = {
            SideEffectStatus.SUBMITTED: 0,
            SideEffectStatus.REVIEWED: 0,
            SideEffectStatus.RESOLVED: 0
        }
        
        for report in reports:
            # Handle timezone-aware datetime comparison
            current_time = datetime.now(timezone.utc)
            report_time = report.reported_at.replace(tzinfo=timezone.utc) if report.reported_at.tzinfo is None else report.reported_at
            days_ago = (current_time - report_time).days
            date_str = report.reported_at.strftime("%Y-%m-%d")
            
            # Extract main symptom from description
            symptom = report.description.split(": ")[1].split(" - ")[0] if ": " in report.description else report.description[:40]
            
            # Get the actual enum value for display
            status_display = report.status.value if hasattr(report.status, 'value') else str(report.status)
            
            print(f"{date_str:<12} {report.severity.value:<10} {status_display:<10} {symptom[:45]:<45}")
            
            # Count statuses
            for status in status_counts:
                if report.status == status:
                    status_counts[status] += 1
            
            # Show resolution details if resolved
            if report.resolved_at:
                resolved_time = report.resolved_at.replace(tzinfo=timezone.utc) if report.resolved_at.tzinfo is None else report.resolved_at
                resolution_days_ago = (current_time - resolved_time).days
                print(f"{'':12} → Resolved {resolution_days_ago} days ago")
                if report.resolution_notes:
                    print(f"{'':12}   Notes: {report.resolution_notes[:50]}...")
        
        # Summary statistics
        print(f"\n📊 Status Summary:")
        print("-" * 40)
        for status, count in status_counts.items():
            print(f"{status.value:<15}: {count} report(s)")
        
        # Check progression logic
        print(f"\n🔍 Scenario Validation:")
        print("-" * 40)
        
        if reports:
            current_time = datetime.now(timezone.utc)
            # Check if early reports are resolved
            early_reports = []
            for r in reports:
                r_time = r.reported_at.replace(tzinfo=timezone.utc) if r.reported_at.tzinfo is None else r.reported_at
                if (current_time - r_time).days > 20:
                    early_reports.append(r)
            early_resolved = [r for r in early_reports if r.status == SideEffectStatus.RESOLVED]
            print(f"✓ Early side effects resolved: {len(early_resolved)}/{len(early_reports)}")
            
            # Check if serious reports are unresolved
            serious_reports = [r for r in reports if r.severity.value.lower() == "major"]
            serious_unresolved = [r for r in serious_reports if r.status == SideEffectStatus.SUBMITTED]
            print(f"✓ Serious side effects unaddressed: {len(serious_unresolved)}/{len(serious_reports)}")
            
            # Check timeline alignment
            latest_report = reports[-1]
            latest_time = latest_report.reported_at.replace(tzinfo=timezone.utc) if latest_report.reported_at.tzinfo is None else latest_report.reported_at
            days_since_latest = (current_time - latest_time).days
            print(f"✓ Most recent report: {days_since_latest} days ago")
            latest_status = latest_report.status.value if hasattr(latest_report.status, 'value') else str(latest_report.status)
            print(f"  Status: {latest_status} - {latest_report.severity.value}")
        
        print("\n" + "="*70)
        print("ANALYSIS COMPLETE")
        print("="*70)
        
        if status_counts[SideEffectStatus.SUBMITTED] >= 2:
            print("⚠️  Multiple unaddressed side effects support the narrative of missed symptoms!")
        else:
            print("✅ Side effect progression shows realistic clinical management")
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🔍 Checking Michael Patient's side effect reports...\n")
    check_michael_side_effects()