#!/usr/bin/env python3
"""
Test script to verify <PERSON>'s weight logs match the alert after demo reset.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

from app.models import Patient, WeightLog, PatientAlert

MICHAEL_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"


def check_michael_weight_logs():
    """Check <PERSON>'s weight logs and compare with alert."""
    db = SessionLocal()
    try:
        # Get <PERSON>'s patient record
        michael = db.query(Patient).filter_by(id=MICHAEL_PATIENT_ID).first()
        if not michael:
            print("❌ <PERSON> Patient not found!")
            return
        
        print(f"✅ Found patient: {michael.first_name} {michael.last_name}")
        
        # Get all weight logs
        weight_logs = db.query(WeightLog).filter_by(patient_id=MICHAEL_PATIENT_ID).order_by(WeightLog.log_date).all()
        
        if not weight_logs:
            print("❌ No weight logs found for Michael!")
            return
        
        print(f"\n📊 Weight Logs Found: {len(weight_logs)}")
        print("Date         | Weight (lbs) | Weight (kg)")
        print("-" * 40)
        
        for log in weight_logs:
            weight_lbs = log.weight_kg / 0.453592  # Convert kg to lbs
            print(f"{log.log_date} | {weight_lbs:11.1f} | {log.weight_kg:10.1f}")
        
        # Calculate total weight loss
        if len(weight_logs) >= 2:
            start_weight_lbs = weight_logs[0].weight_kg / 0.453592
            end_weight_lbs = weight_logs[-1].weight_kg / 0.453592
            total_loss = start_weight_lbs - end_weight_lbs
            days_span = (weight_logs[-1].log_date - weight_logs[0].log_date).days
            
            print(f"\n📈 Weight Loss Summary:")
            print(f"   Start: {start_weight_lbs:.1f} lbs")
            print(f"   End: {end_weight_lbs:.1f} lbs")
            print(f"   Total Loss: {total_loss:.1f} lbs over {days_span} days")
            print(f"   Average: {total_loss/days_span*7:.1f} lbs/week")
        
        # Check for patient alert
        alert = db.query(PatientAlert).filter_by(
            patient_id=MICHAEL_PATIENT_ID,
            alert_type="ai_detected_pattern"
        ).first()
        
        if alert:
            print(f"\n🚨 Patient Alert Found:")
            print(f"   Title: {alert.title}")
            print(f"   Severity: {alert.severity}")
            print(f"   Description Preview: {alert.description[:100]}...")
            
            # Check if alert matches weight logs
            if "37 lbs in 30 days" in alert.description and abs(total_loss - 37) < 1:
                print("\n✅ Alert description matches weight logs!")
            else:
                print(f"\n⚠️  Alert says '37 lbs in 30 days' but logs show {total_loss:.1f} lbs in {days_span} days")
        else:
            print("\n⚠️  No AI-generated alert found for Michael")
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🔍 Checking Michael Patient's weight logs...\n")
    check_michael_weight_logs()