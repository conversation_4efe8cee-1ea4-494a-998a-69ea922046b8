#!/usr/bin/env python3
"""Test script for new action patterns: batch and conditional operations."""

import asyncio
import logging
from datetime import datetime, timedelta
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.action_patterns import ActionPatterns, PatternDetector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)


def test_new_pattern_detection():
    """Test detection of new patterns."""
    logger.info("=== Testing New Pattern Detection ===")
    
    test_cases = [
        # Batch weight logs
        ("Log my weight: Monday 185, Tuesday 184, Wednesday 183", "batch_weight_logs"),
        ("Track my weight for the past 3 days: 185, 184, 183 pounds", "batch_weight_logs"),
        ("Record weight entries: Monday 185 lbs, Wednesday 183 lbs, Friday 182 lbs", "batch_weight_logs"),
        
        # Conditional weight appointment
        ("Log my weight as 205 and if it's over 200, schedule a nutrition consultation", "conditional_weight_appointment"),
        ("If my weight exceeds 200 pounds, book an appointment with the nutritionist", "conditional_weight_appointment"),
        ("Track weight 210, and if above threshold 200, schedule consult", "conditional_weight_appointment"),
    ]
    
    for user_input, expected_pattern in test_cases:
        detected = PatternDetector.detect_pattern(user_input)
        status = "✅" if detected == expected_pattern else "❌"
        logger.info(f"{status} '{user_input[:60]}...' -> {detected} (expected: {expected_pattern})")


def test_batch_weight_logs_pattern():
    """Test batch weight logs pattern creation."""
    logger.info("\n=== Testing Batch Weight Logs Pattern ===")
    
    # Create sample weight entries
    weight_entries = [
        ("2025-05-25", 185.0, "lb"),
        ("2025-05-26", 184.5, "lb"),
        ("2025-05-27", 183.8, "lb"),
    ]
    
    pattern = ActionPatterns.batch_weight_logs(weight_entries)
    
    logger.info(f"✅ Created batch pattern with chain_id: {pattern.chain_id}")
    logger.info(f"   Total weight logs: {1 + len(pattern.follow_up_actions)}")
    logger.info(f"   Execution mode: {pattern.execution_mode}")
    logger.info(f"   Dates: {[entry[0] for entry in weight_entries]}")
    logger.info(f"   Values: {[entry[1] for entry in weight_entries]}")


def test_conditional_weight_appointment_pattern():
    """Test conditional weight appointment pattern."""
    logger.info("\n=== Testing Conditional Weight Appointment Pattern ===")
    
    # Test case 1: Weight over threshold
    pattern1 = ActionPatterns.conditional_weight_appointment(
        weight_value=205.0,
        unit="lb",
        threshold=200.0
    )
    
    logger.info(f"✅ Over-threshold pattern:")
    logger.info(f"   Primary action: {pattern1.primary_action.action_type}")
    logger.info(f"   Follow-up action: {pattern1.follow_up_actions[0].action_type if pattern1.follow_up_actions else 'None'}")
    logger.info(f"   Weight: {pattern1.primary_action.parameters['weight_value']} {pattern1.primary_action.parameters['unit']}")
    logger.info(f"   Threshold: 200.0 lb")
    logger.info(f"   Appointment reason: Weight management consultation...")
    
    # Test case 2: Weight under threshold (in real implementation, appointment wouldn't be scheduled)
    pattern2 = ActionPatterns.conditional_weight_appointment(
        weight_value=195.0,
        unit="lb",
        threshold=200.0
    )
    
    logger.info(f"\n✅ Under-threshold pattern:")
    logger.info(f"   Primary action: {pattern2.primary_action.action_type}")
    logger.info(f"   Note: In real execution, appointment would NOT be scheduled")
    logger.info(f"   Weight: {pattern2.primary_action.parameters['weight_value']} {pattern2.primary_action.parameters['unit']}")
    logger.info(f"   Threshold: 200.0 lb")


def test_pattern_combinations():
    """Test combinations of patterns."""
    logger.info("\n=== Testing Pattern Combinations ===")
    
    # Test appointment + reminder (existing pattern)
    appointment_pattern = ActionPatterns.appointment_with_reminder(
        patient_id="patient_123",
        scheduled_time=datetime.now().replace(hour=14, minute=0).isoformat(),
        reminder_days_before=1
    )
    
    # Test side effect with varying severity
    severe_pattern = ActionPatterns.side_effect_with_followup(
        patient_id="patient_123",
        medication_name="Ozempic",
        symptoms="severe nausea and dizziness",
        severity="Severe",
        onset_time="2 days ago"
    )
    
    mild_pattern = ActionPatterns.side_effect_with_followup(
        patient_id="patient_123",
        medication_name="Metformin",
        symptoms="mild stomach upset",
        severity="Mild",
        onset_time="yesterday"
    )
    
    logger.info(f"✅ Appointment + Reminder: {len(appointment_pattern.follow_up_actions) + 1} actions")
    logger.info(f"✅ Severe side effect: {len(severe_pattern.follow_up_actions) + 1} actions")
    logger.info(f"✅ Mild side effect: {len(mild_pattern.follow_up_actions) + 1} actions")


def main():
    """Run all tests."""
    logger.info("🚀 Starting New Pattern Tests\n")
    
    test_new_pattern_detection()
    test_batch_weight_logs_pattern()
    test_conditional_weight_appointment_pattern()
    test_pattern_combinations()
    
    logger.info("\n✨ All new pattern tests completed!")


if __name__ == "__main__":
    main()