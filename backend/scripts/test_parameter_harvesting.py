"""Test script for parameter harvesting functionality."""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from uuid import UUID

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session

from app import crud
from app.db.session import SessionLocal
from app.services.chat_modules.llm_action_module import LLMActionModule
from app.services.intent_resolver_service import IntentResolverService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_parameter_harvesting():
    """Test parameter harvesting with incomplete action requests."""
    db = SessionLocal()
    
    try:
        # Get demo patient
        patient = crud.patient.get(db, id="user_2waTCuGL3kQC9k2rY47INdcJXk5")
        if not patient:
            logger.error("Demo patient not found")
            return
            
        patient_id = str(patient.id)
        logger.info(f"Testing with patient: {patient.first_name} {patient.last_name} (ID: {patient_id})")
        
        # Initialize LLM Action Module
        llm_module = LLMActionModule()
        
        # Test 1: Schedule appointment with missing parameters
        logger.info("\n=== Test 1: Schedule appointment (missing date/time) ===")
        context = {
            "user_id": patient_id,
            "user_role": "patient",
            "db": db,
            "client_timezone_offset": -300,  # EST
        }
        
        message = "I need to schedule an appointment"
        
        if llm_module.should_handle_message(message):
            result = await llm_module.process_message(message, context)
            logger.info(f"Response: {result['response']}")
            logger.info(f"Metadata: {json.dumps(result['metadata'], indent=2)}")
            
            if result['metadata'].get('missing_parameters'):
                logger.info(f"Missing parameters detected: {[p['name'] for p in result['metadata']['missing_parameters']]}")
        
        # Test 2: Report side effect with missing details
        logger.info("\n=== Test 2: Report side effect (missing severity/medication) ===")
        message = "I'm having headaches"
        
        if llm_module.should_handle_message(message):
            result = await llm_module.process_message(message, context)
            logger.info(f"Response: {result['response']}")
            logger.info(f"Metadata: {json.dumps(result['metadata'], indent=2)}")
            
            if result['metadata'].get('missing_parameters'):
                logger.info(f"Missing parameters detected: {[p['name'] for p in result['metadata']['missing_parameters']]}")
        
        # Test 3: Provide some parameters and see what's still missing
        logger.info("\n=== Test 3: Partial appointment scheduling ===")
        message = "Schedule appointment for tomorrow at 2pm"
        
        if llm_module.should_handle_message(message):
            result = await llm_module.process_message(message, context)
            logger.info(f"Response: {result['response']}")
            logger.info(f"Metadata: {json.dumps(result['metadata'], indent=2)}")
            
            if result['metadata'].get('missing_parameters'):
                logger.info(f"Missing parameters detected: {[p['name'] for p in result['metadata']['missing_parameters']]}")
                logger.info("Parameter details:")
                for param in result['metadata']['missing_parameters']:
                    logger.info(f"  - {param['name']}: {param.get('description', 'No description')}")
                    if param.get('enum'):
                        logger.info(f"    Options: {param['enum']}")
                        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_parameter_harvesting())