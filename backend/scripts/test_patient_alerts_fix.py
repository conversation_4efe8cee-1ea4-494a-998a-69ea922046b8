#!/usr/bin/env python
"""Test patient alerts filtering by patient_id"""

import requests
import sys

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
CLINICIAN_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6Imluc18yU2l1UlBKY1pROTBFZDM1VGZUa0ZQTjRTMEoiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1YQOq-xPB6o7w1Z9YTiXqLCZjBa5Av-CvvZNdaOT3CKnO3PYGQANzMfDXnvOa7n1Gs2M1PBdH0e2XFXZ_yYmQq4HRWMnKOOd-4bq1DmQQXs-XnoFgxHe1hT7TT3UzTQnQZD-eJFnYMJR_Bc1JZxOKKlhHKJMUKVqKrz2H2Uu0Rrm5n11eJB2kKMfXMZfRmPQ8v0RcfGGKdB8xH7gRZEsF36xOJN8lrxWUQmgYKTe6lJUNSh2vgvppCx9TY8pOvMYT-YSHPE3NV7EJrJP3xpHQsM6H8YQeELOJNYRWoJGqwqm0EXPKuGiQP0-xQGJLEgQu9F--8fOGk0G_CWsN67Yxw"

headers = {
    "Authorization": f"Bearer {CLINICIAN_TOKEN}",
    "Content-Type": "application/json"
}

# Test 1: Get all alerts (no patient_id filter)
print("Test 1: Getting all patient alerts...")
response = requests.get(f"{API_BASE_URL}/patient-alerts", headers=headers, params={"limit": 50})
if response.status_code == 200:
    data = response.json()
    print(f"✓ Success! Total alerts: {data['total']}")
    for alert in data['items']:
        patient = alert.get('patient', {})
        print(f"  - Alert for: {patient.get('first_name', 'Unknown')} {patient.get('last_name', 'Unknown')} (ID: {alert['patient_id']})")
else:
    print(f"✗ Failed: {response.status_code} - {response.text}")

print("\n" + "="*50 + "\n")

# Test 2: Get alerts for specific patient (Michael Patient)
michael_patient_id = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
print(f"Test 2: Getting alerts for Michael Patient (ID: {michael_patient_id})...")
response = requests.get(f"{API_BASE_URL}/patient-alerts", headers=headers, params={"patient_id": michael_patient_id, "limit": 50})
if response.status_code == 200:
    data = response.json()
    print(f"✓ Success! Total alerts for Michael: {data['total']}")
    for alert in data['items']:
        print(f"  - {alert['alert_type']}: {alert['description'][:50]}...")
else:
    print(f"✗ Failed: {response.status_code} - {response.text}")

print("\n" + "="*50 + "\n")

# Test 3: Get alerts for different patient (Sarah Johnson)
sarah_patient_id = "user_demo_patient_sarah"
print(f"Test 3: Getting alerts for Sarah Johnson (ID: {sarah_patient_id})...")
response = requests.get(f"{API_BASE_URL}/patient-alerts", headers=headers, params={"patient_id": sarah_patient_id, "limit": 50})
if response.status_code == 200:
    data = response.json()
    print(f"✓ Success! Total alerts for Sarah: {data['total']}")
    if data['total'] == 0:
        print("  - No alerts (as expected)")
    else:
        print(f"  ⚠️  WARNING: Found {data['total']} alerts for Sarah when expecting 0!")
else:
    print(f"✗ Failed: {response.status_code} - {response.text}")

print("\n✅ Patient alerts filtering test completed!")