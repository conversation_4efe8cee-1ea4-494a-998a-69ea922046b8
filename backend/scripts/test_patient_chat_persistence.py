#!/usr/bin/env python3
"""Test script to verify patient chat messages are being saved and retrieved correctly."""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.orm import Session
from app import crud, schemas
from app.db.session import SessionLocal
from app.schemas.chat import ChatMessageCreateInternal
from app.models.chat_message import MessageSenderType, MessageRouteType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Demo patient ID (<PERSON>'s internal UUID)
DEMO_PATIENT_ID = "user_2waTCuGL3kQC9k2rY47INdcJXk5"


async def test_patient_chat_persistence():
    """Test patient chat message persistence and retrieval."""
    db = SessionLocal()
    
    try:
        # First, let's try to get patient directly by the ID (it might be internal UUID already)
        patient = crud.patient.get(db, id=DEMO_PATIENT_ID)
        if not patient:
            # If not found, search through all patients to find by clerk_id
            all_patients = db.query(crud.patient.model).all()
            patient = None
            for p in all_patients:
                if p.clerk_id == DEMO_PATIENT_ID:
                    patient = p
                    break
            
            if not patient:
                logger.error(f"Patient with ID {DEMO_PATIENT_ID} not found!")
                return
            
        patient_internal_id = str(patient.id)
        logger.info(f"Found patient: {patient.first_name} {patient.last_name} (Internal ID: {patient_internal_id})")
        
        # Create a test patient message
        test_message = "Test message from patient - checking persistence"
        patient_msg_data = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="patient",
            message_content=test_message,
            message_route="ai"  # Patient sends to AI
        )
        
        logger.info("Creating patient message...")
        patient_msg = crud.chat_message.create(db, obj_in=patient_msg_data)
        logger.info(f"Created patient message ID: {patient_msg.id}")
        
        # Create an AI response
        ai_response = "This is a test AI response to verify persistence"
        ai_msg_data = ChatMessageCreateInternal(
            patient_id=patient_internal_id,
            sender_type="agent",
            message_content=ai_response,
            message_route="patient"  # AI responds to patient
        )
        
        logger.info("Creating AI response...")
        ai_msg = crud.chat_message.create(db, obj_in=ai_msg_data)
        logger.info(f"Created AI response ID: {ai_msg.id}")
        
        # Now retrieve messages using the patient filter
        logger.info("\nRetrieving filtered messages for patient...")
        filtered_messages = crud.chat_message.get_by_patient_filtered(
            db, 
            patient_id=patient_internal_id,
            skip=0,
            limit=100
        )
        
        logger.info(f"Retrieved {len(filtered_messages)} filtered messages")
        
        # Check if our test messages are in the results
        found_patient_msg = False
        found_ai_msg = False
        
        for msg in filtered_messages:
            if msg.id == patient_msg.id:
                found_patient_msg = True
                logger.info(f"✓ Found patient message: {msg.message_content[:50]}...")
            elif msg.id == ai_msg.id:
                found_ai_msg = True
                logger.info(f"✓ Found AI response: {msg.message_content[:50]}...")
        
        if not found_patient_msg:
            logger.error("✗ Patient message NOT found in filtered results!")
        if not found_ai_msg:
            logger.error("✗ AI response NOT found in filtered results!")
            
        # Also check the count
        logger.info("\nChecking message count...")
        msg_count = crud.chat_message.get_count_by_patient(db, patient_id=patient_internal_id)
        logger.info(f"Message count for patient: {msg_count}")
        
        # Clean up test messages
        logger.info("\nCleaning up test messages...")
        crud.chat_message.remove(db, id=patient_msg.id)
        crud.chat_message.remove(db, id=ai_msg.id)
        logger.info("Test messages cleaned up")
        
        if found_patient_msg and found_ai_msg:
            logger.info("\n✓ TEST PASSED: Patient messages are being saved and retrieved correctly!")
        else:
            logger.error("\n✗ TEST FAILED: Patient messages are not being retrieved correctly!")
            
    except Exception as e:
        logger.error(f"Error during test: {str(e)}", exc_info=True)
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_patient_chat_persistence())