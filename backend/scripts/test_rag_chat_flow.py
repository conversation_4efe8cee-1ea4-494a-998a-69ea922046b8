"""<PERSON><PERSON><PERSON> to test the complete RAG chat flow end-to-end."""

import asyncio
import json
import logging
import time
from typing import Any

from app.db.session import SessionLocal
from app.services.chat_agent import process_chat_message
from app.utils.context_enricher import enrich_with_rag

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RAGChatTester:
    """Test the complete RAG-enabled chat flow."""

    def __init__(self):
        """Initialize the chat tester."""
        self.test_cases = self._load_test_cases()
        self.results = []

    def _load_test_cases(self) -> list[dict[str, Any]]:
        """Load test cases for RAG chat testing."""
        return [
            {
                "id": "test_diabetes_query",
                "user_message": "What are the guidelines for managing my diabetes? I need help with monitoring my blood sugar.",
                "expected_topics": ["diabetes", "blood glucose", "monitoring"],
                "user_role": "patient",
                "context": {
                    "patient_context": {
                        "first_name": "Test",
                        "last_name": "Patient",
                        "health_metrics": {
                            "active_medications": [
                                {"medication_name": "Metformin", "dosage": "500mg"}
                            ]
                        },
                    }
                },
            },
            {
                "id": "test_hypertension_query",
                "user_message": "My blood pressure has been high lately. What should I do to manage it better?",
                "expected_topics": ["blood pressure", "hypertension", "management"],
                "user_role": "patient",
                "context": {
                    "patient_context": {
                        "first_name": "Test",
                        "last_name": "Patient",
                        "health_metrics": {
                            "latest_weight": {"weight_kg": 85, "log_date": "2024-01-01"}
                        },
                    }
                },
            },
            {
                "id": "test_weight_query",
                "user_message": "I'm trying to lose weight. Can you give me some advice on diet and exercise?",
                "expected_topics": ["weight", "diet", "exercise", "caloric"],
                "user_role": "patient",
                "context": {
                    "patient_context": {
                        "first_name": "Test",
                        "last_name": "Patient",
                        "health_metrics": {
                            "weight_history": [
                                {"weight_kg": 90, "log_date": "2024-01-01"},
                                {"weight_kg": 88, "log_date": "2024-01-15"},
                            ]
                        },
                    }
                },
            },
            {
                "id": "test_medication_query",
                "user_message": "Can you tell me about the side effects of Metformin? I'm experiencing some stomach issues.",
                "expected_topics": ["metformin", "side effects", "medication"],
                "user_role": "patient",
                "context": {
                    "patient_context": {
                        "first_name": "Test",
                        "last_name": "Patient",
                        "health_metrics": {
                            "active_medications": [
                                {"medication_name": "Metformin", "dosage": "1000mg"}
                            ],
                            "recent_side_effects": [
                                {
                                    "description": "Stomach discomfort",
                                    "severity": "mild",
                                }
                            ],
                        },
                    }
                },
            },
            {
                "id": "test_appointment_query",
                "user_message": "I'd like to schedule an appointment with my doctor to discuss my diabetes management.",
                "expected_topics": ["appointment", "schedule", "diabetes"],
                "user_role": "patient",
                "context": {
                    "patient_context": {
                        "first_name": "Test",
                        "last_name": "Patient",
                        "associated_clinic_id": "test-clinic-id",
                    }
                },
            },
        ]

    async def test_single_chat_flow(
        self, test_case: dict[str, Any], db_session
    ) -> dict[str, Any]:
        """Test a single chat flow with RAG."""
        result = {
            "test_id": test_case["id"],
            "user_message": test_case["user_message"],
            "timing": {},
            "rag_analysis": {},
            "response_analysis": {},
            "errors": [],
        }

        try:
            # Simulate user ID for the test
            user_id = "test-user-id"

            # Measure RAG enrichment time
            start_time = time.time()
            rag_context = enrich_with_rag(
                db=db_session,
                user_id=user_id,
                message=test_case["user_message"],
                user_role=test_case["user_role"],
            )
            rag_time = time.time() - start_time
            result["timing"]["rag_enrichment"] = rag_time

            # Analyze RAG results
            result["rag_analysis"] = {
                "chunks_retrieved": len(rag_context.get("rag_context_chunks", [])),
                "has_fallback": "rag_fallback_message" in rag_context,
                "chunk_preview": rag_context.get("rag_context_chunks", [])[:1],
            }

            # Prepare full context for chat
            full_context = {
                **test_case["context"],
                "user_id": user_id,
                "user_role": test_case["user_role"],
                "db": db_session,
                "rag_context": rag_context,
            }

            # Measure chat processing time
            start_time = time.time()
            response = await process_chat_message(
                user_id=user_id,
                user_message=test_case["user_message"],
                db=db_session,
                context=full_context,
            )
            chat_time = time.time() - start_time
            result["timing"]["chat_processing"] = chat_time
            result["timing"]["total"] = rag_time + chat_time

            # Analyze response
            response_text = (
                response if isinstance(response, str) else response.get("response", "")
            )

            result["response_analysis"] = {
                "response_length": len(response_text),
                "response_preview": (
                    response_text[:200] + "..."
                    if len(response_text) > 200
                    else response_text
                ),
                "contains_expected_topics": self._check_topics(
                    response_text, test_case["expected_topics"]
                ),
                "response_type": type(response).__name__,
            }

            if isinstance(response, dict):
                result["response_analysis"]["metadata"] = response.get("metadata", {})
                result["response_analysis"]["action"] = response.get("action")

        except Exception as e:
            result["errors"].append(str(e))
            logger.error(f"Error in test {test_case['id']}: {e}", exc_info=True)

        return result

    def _check_topics(self, text: str, expected_topics: list[str]) -> dict[str, bool]:
        """Check if expected topics are present in the text."""
        text_lower = text.lower()
        return {topic: topic.lower() in text_lower for topic in expected_topics}

    async def run_all_tests(self) -> dict[str, Any]:
        """Run all test cases and compile results."""
        logger.info("Starting RAG chat flow tests...")

        async with SessionLocal() as db:
            for test_case in self.test_cases:
                logger.info(f"Running test: {test_case['id']}")
                result = await self.test_single_chat_flow(test_case, db)
                self.results.append(result)
                logger.info(f"Completed test: {test_case['id']}")

        # Compile summary statistics
        summary = self._compile_summary()

        # Save detailed results
        full_results = {
            "summary": summary,
            "detailed_results": self.results,
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }

        with open("rag_chat_test_results.json", "w") as f:
            json.dump(full_results, f, indent=2)

        return summary

    def _compile_summary(self) -> dict[str, Any]:
        """Compile summary statistics from test results."""
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if not r["errors"])

        # Timing statistics
        rag_times = [
            r["timing"]["rag_enrichment"]
            for r in self.results
            if "rag_enrichment" in r["timing"]
        ]
        chat_times = [
            r["timing"]["chat_processing"]
            for r in self.results
            if "chat_processing" in r["timing"]
        ]
        total_times = [
            r["timing"]["total"] for r in self.results if "total" in r["timing"]
        ]

        # RAG statistics
        chunks_retrieved = [
            r["rag_analysis"]["chunks_retrieved"]
            for r in self.results
            if "chunks_retrieved" in r["rag_analysis"]
        ]

        # Response analysis
        topic_coverage = []
        for result in self.results:
            if "contains_expected_topics" in result["response_analysis"]:
                topics = result["response_analysis"]["contains_expected_topics"]
                coverage = sum(topics.values()) / len(topics) if topics else 0
                topic_coverage.append(coverage)

        summary = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "timing": {
                "avg_rag_time": sum(rag_times) / len(rag_times) if rag_times else 0,
                "avg_chat_time": sum(chat_times) / len(chat_times) if chat_times else 0,
                "avg_total_time": (
                    sum(total_times) / len(total_times) if total_times else 0
                ),
                "max_total_time": max(total_times) if total_times else 0,
                "min_total_time": min(total_times) if total_times else 0,
            },
            "rag_performance": {
                "avg_chunks_retrieved": (
                    sum(chunks_retrieved) / len(chunks_retrieved)
                    if chunks_retrieved
                    else 0
                ),
                "rag_success_rate": (
                    sum(1 for c in chunks_retrieved if c > 0) / len(chunks_retrieved)
                    if chunks_retrieved
                    else 0
                ),
            },
            "response_quality": {
                "avg_topic_coverage": (
                    sum(topic_coverage) / len(topic_coverage) if topic_coverage else 0
                ),
                "topic_coverage_by_test": {
                    result["test_id"]: coverage
                    for result, coverage in zip(self.results, topic_coverage)
                    if "test_id" in result
                },
            },
        }

        return summary

    def print_summary(self, summary: dict[str, Any]):
        """Print a formatted summary of test results."""
        print("\n" + "=" * 50)
        print("RAG CHAT FLOW TEST SUMMARY")
        print("=" * 50)

        print(f"\nTotal Tests Run: {summary['total_tests']}")
        print(f"Successful Tests: {summary['successful_tests']}")
        print(f"Success Rate: {summary['success_rate']:.1%}")

        print("\nTiming Performance:")
        print(f"  Average RAG Time: {summary['timing']['avg_rag_time']:.3f}s")
        print(f"  Average Chat Time: {summary['timing']['avg_chat_time']:.3f}s")
        print(f"  Average Total Time: {summary['timing']['avg_total_time']:.3f}s")
        print(f"  Max Total Time: {summary['timing']['max_total_time']:.3f}s")

        print("\nRAG Performance:")
        print(
            f"  Average Chunks Retrieved: {summary['rag_performance']['avg_chunks_retrieved']:.1f}"
        )
        print(
            f"  RAG Success Rate: {summary['rag_performance']['rag_success_rate']:.1%}"
        )

        print("\nResponse Quality:")
        print(
            f"  Average Topic Coverage: {summary['response_quality']['avg_topic_coverage']:.1%}"
        )

        print("\nTopic Coverage by Test:")
        for test_id, coverage in summary["response_quality"][
            "topic_coverage_by_test"
        ].items():
            print(f"  {test_id}: {coverage:.1%}")

        print("\n" + "=" * 50)


async def main():
    """Main function to run the RAG chat tests."""
    tester = RAGChatTester()
    summary = await tester.run_all_tests()
    tester.print_summary(summary)


if __name__ == "__main__":
    asyncio.run(main())
