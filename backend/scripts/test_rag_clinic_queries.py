#!/usr/bin/env python3
"""
Test RAG system with clinic-specific queries after scraping.
"""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.db.session import SessionLocal
from app.services.embedding_pipeline import generate_embeddings
from app.crud.crud_content_chunk import crud_content_chunk


def test_clinic_queries():
    """Test various clinic-specific queries."""
    db = SessionLocal()
    
    queries = [
        "Tell me about our clinic doctors",
        "Who is Dr <PERSON>?",
        "What services does the clinic offer?",
        "What are the clinic hours?",
        "Where is the clinic located?",
        "What weight loss medications are available?"
    ]
    
    print("=== TESTING CLINIC-SPECIFIC RAG QUERIES ===\n")
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        print("-" * 60)
        
        # Generate embedding for query
        query_embedding = generate_embeddings([query])[0]
        
        # Search with different thresholds
        thresholds = [0.25, 0.35, 0.45]
        
        for threshold in thresholds:
            chunks = crud_content_chunk.find_similar_content_chunks(
                db=db,
                query_embedding=query_embedding,
                similarity_threshold=threshold,
                limit=3
            )
            
            print(f"\nThreshold {threshold}: Found {len(chunks)} chunks")
            
            if chunks:
                # Show the best match
                best_chunk = chunks[0]
                similarity = getattr(best_chunk, 'similarity_score', 'N/A')
                print(f"Best match (similarity: {similarity}):")
                print(f"Content preview: {best_chunk.chunk_text[:150]}...")
                
                # Check metadata
                if best_chunk.metadata:
                    if 'title' in best_chunk.metadata:
                        print(f"Source: {best_chunk.metadata.get('title', 'Unknown')}")
    
    # Summary statistics
    print("\n\n=== CONTENT SUMMARY ===")
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as conn:
        # Total scraped content
        result = conn.execute(text("""
            SELECT COUNT(DISTINCT sp.id) as page_count,
                   COUNT(cc.id) as chunk_count
            FROM scraped_pages sp
            LEFT JOIN content_chunks cc ON cc.scraped_page_id = sp.id
        """))
        row = result.fetchone()
        print(f"Scraped pages: {row.page_count}")
        print(f"Scraped content chunks: {row.chunk_count}")
        
        # Education materials
        result = conn.execute(text("""
            SELECT COUNT(*) as edu_chunks
            FROM content_chunks
            WHERE metadata->>'source_type' = 'education_material'
        """))
        row = result.fetchone()
        print(f"Education material chunks: {row.edu_chunks}")
        
        # Check for specific content
        keywords = ["doctor", "Dr", "Abbs", "staff", "hours", "location", "address"]
        print("\nKeyword matches in scraped content:")
        for keyword in keywords:
            result = conn.execute(text("""
                SELECT COUNT(*) 
                FROM content_chunks cc
                JOIN scraped_pages sp ON cc.scraped_page_id = sp.id
                WHERE cc.chunk_text ILIKE :pattern
            """), {"pattern": f"%{keyword}%"})
            count = result.scalar()
            print(f"  '{keyword}': {count} chunks")
    
    db.close()


if __name__ == "__main__":
    test_clinic_queries()