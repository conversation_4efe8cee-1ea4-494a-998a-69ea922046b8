#!/usr/bin/env python
"""Test Redis connection with improved configuration."""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.cache.redis_manager import redis_manager

print("Testing Redis connection...")
print(f"Redis host: {redis_manager.host}")
print(f"Redis port: {redis_manager.port}")
print(f"Redis db: {redis_manager.db}")

if redis_manager.is_connected():
    print("✅ Redis is connected!")
    
    # Test basic operations
    test_key = "test:connection"
    test_value = {"status": "working", "timestamp": "2025-05-27"}
    
    # Set a value
    if redis_manager.set(test_key, test_value, ttl=60):
        print("✅ Successfully set test value")
    else:
        print("❌ Failed to set test value")
    
    # Get the value back
    retrieved = redis_manager.get(test_key)
    if retrieved == test_value:
        print("✅ Successfully retrieved test value")
    else:
        print(f"❌ Retrieved value mismatch: {retrieved}")
    
    # Clean up
    if redis_manager.delete(test_key):
        print("✅ Successfully deleted test key")
    else:
        print("❌ Failed to delete test key")
    
else:
    print("❌ Redis is NOT connected")
    print("This means RAG caching will be disabled, but the system will continue to work.")