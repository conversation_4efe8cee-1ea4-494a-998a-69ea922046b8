#!/usr/bin/env python3
"""Test timezone handling for appointments."""

import sys
from pathlib import Path
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from app.db.session import SessionLocal
from app.models import Appointment

def test_timezone_handling():
    """Test that appointments are stored and retrieved with proper timezone info."""
    
    # Define clinic timezone
    clinic_tz = ZoneInfo("America/Chicago")
    
    # Create a test appointment time (9:00 AM Central Time)
    test_date = datetime.now().date()
    appointment_time_local = datetime.combine(
        test_date,
        datetime.strptime("09:00", "%H:%M").time(),
        tzinfo=clinic_tz
    )
    
    # Convert to UTC
    appointment_time_utc = appointment_time_local.astimezone(timezone.utc)
    
    print("Timezone Handling Test")
    print("=" * 60)
    print(f"Clinic timezone: {clinic_tz}")
    print(f"Local appointment time: {appointment_time_local}")
    print(f"UTC appointment time: {appointment_time_utc}")
    print(f"ISO format (sent to frontend): {appointment_time_utc.isoformat()}")
    
    # Test database query
    db = SessionLocal()
    try:
        # Get a sample appointment
        appointment = db.query(Appointment).filter(
            Appointment.status == "Scheduled"
        ).first()
        
        if appointment:
            print(f"\nSample appointment from DB:")
            print(f"  ID: {appointment.id}")
            print(f"  Patient: {appointment.patient.first_name} {appointment.patient.last_name}")
            print(f"  Stored datetime: {appointment.appointment_datetime}")
            print(f"  ISO format: {appointment.appointment_datetime.isoformat()}")
            print(f"  Timezone info: {appointment.appointment_datetime.tzinfo}")
            
            # Convert to different timezones for display
            print(f"\nDisplay in different timezones:")
            for tz_name in ["America/Chicago", "America/New_York", "Europe/London"]:
                tz = ZoneInfo(tz_name)
                local_time = appointment.appointment_datetime.astimezone(tz)
                print(f"  {tz_name}: {local_time.strftime('%Y-%m-%d %I:%M %p %Z')}")
        else:
            print("\nNo scheduled appointments found in database.")
            
    finally:
        db.close()

if __name__ == "__main__":
    test_timezone_handling()