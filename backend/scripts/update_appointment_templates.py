#!/usr/bin/env python3
"""
<PERSON>ript to update templates with appointment actions
"""

import sys
from pathlib import Path

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.initial_data import create_clinician_templates, create_patient_templates
from app.db.session import SessionLocal


def main():
    """Update templates with appointment actions"""
    db = SessionLocal()
    try:
        print("Updating clinician templates...")
        create_clinician_templates(db)
        
        print("Updating patient templates...")
        create_patient_templates(db)
        
        db.commit()
        print("Templates updated successfully!")
        
    except Exception as e:
        print(f"Error updating templates: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()