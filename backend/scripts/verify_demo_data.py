#!/usr/bin/env python3
"""
Verify all demo data is properly set up.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.config import settings

# Override database URL for local execution
if "db:5432" in settings.DATABASE_URL:
    local_db_url = settings.DATABASE_URL.replace("@db:", "@localhost:")
    os.environ["DATABASE_URL"] = local_db_url
    from app.core.config import Settings
    settings = Settings()

def verify_demo_data(db):
    """Verify all demo data is in place."""
    
    print("="*70)
    print("DEMO DATA VERIFICATION")
    print("="*70)
    
    # Check <PERSON>'s data
    print("\n📊 MICHAEL PATIENT (Critical Case):")
    print("-" * 50)
    
    michael_query = text("""
        SELECT 
            (SELECT COUNT(*) FROM chat_messages WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as messages,
            (SELECT COUNT(*) FROM weight_logs WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as weight_logs,
            (SELECT COUNT(*) FROM side_effect_reports WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as side_effects,
            (SELECT COUNT(*) FROM patient_alerts WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5' AND status = 'New') as alerts,
            (SELECT COUNT(*) FROM appointments WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as appointments,
            (SELECT COUNT(*) FROM medication_requests WHERE patient_id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5') as med_requests
    """)
    
    result = db.execute(michael_query).fetchone()
    print(f"✅ Chat Messages: {result.messages} (showing concerning symptoms to AI)")
    print(f"✅ Weight Logs: {result.weight_logs} (37 lbs in 30 days - dangerous!)")
    print(f"✅ Side Effects: {result.side_effects} (escalating severity)")
    print(f"⚠️  Critical Alerts: {result.alerts}")
    print(f"✅ Appointments: {result.appointments}")
    print(f"✅ Medication Requests: {result.med_requests}")
    
    # Check demo patients
    print("\n👥 DEMO PATIENTS (Normal Cases):")
    print("-" * 50)
    
    demo_patients_query = text("""
        SELECT 
            p.first_name || ' ' || p.last_name as name,
            COUNT(DISTINCT cm.id) as messages,
            COUNT(DISTINCT wl.id) as weight_logs,
            COUNT(DISTINCT a.id) as appointments,
            COUNT(DISTINCT mr.id) as med_requests
        FROM patients p
        LEFT JOIN chat_messages cm ON p.id = cm.patient_id
        LEFT JOIN weight_logs wl ON p.id = wl.patient_id
        LEFT JOIN appointments a ON p.id = a.patient_id
        LEFT JOIN medication_requests mr ON p.id = mr.patient_id
        WHERE p.id LIKE 'demo_%'
        GROUP BY p.id, p.first_name, p.last_name
        ORDER BY p.first_name
    """)
    
    demo_results = db.execute(demo_patients_query).fetchall()
    for patient in demo_results:
        print(f"✅ {patient.name}: {patient.messages} msgs, {patient.weight_logs} weights, "
              f"{patient.appointments} appts, {patient.med_requests} med requests")
    
    # Check medications
    print("\n💊 MEDICATIONS:")
    print("-" * 50)
    
    meds_query = text("""
        SELECT name, category, COUNT(*) OVER() as total
        FROM medications
        ORDER BY category, name
    """)
    
    meds = db.execute(meds_query).fetchall()
    categories = {}
    for med in meds:
        if med.category not in categories:
            categories[med.category] = []
        categories[med.category].append(med.name)
    
    for category, med_list in categories.items():
        print(f"✅ {category}: {', '.join(med_list)}")
    
    print(f"\nTotal medications: {meds[0].total if meds else 0}")
    
    # Key demo scenario summary
    print("\n🎯 DEMO SCENARIO SUMMARY:")
    print("-" * 50)
    print("1. Michael Patient has been losing weight dangerously fast (37 lbs/30 days)")
    print("2. He casually mentioned concerning symptoms to AI (chest tightness, dizziness)")
    print("3. His conversations with the clinician were routine (no serious symptoms mentioned)")
    print("4. AI detected the pattern and created a CRITICAL alert for the clinician")
    print("5. Other patients show normal, healthy progression for contrast")
    
    print("\n✨ Demo is ready for investor presentation!")

def main():
    db = SessionLocal()
    
    try:
        verify_demo_data(db)
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()