"""
End-to-end integration tests for Action Chains
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.schemas.action_chain_v2 import ChainedAction, ChainedIntent, ExecutionMode
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.services.action_patterns import ActionPatterns, PatternDetector
from app.services.intent_resolver_service import IntentResolverService
from app.api.v1.endpoints.llm_actions import process_text_action
from app.schemas.auth import TokenPayload


@pytest.mark.integration
class TestActionChainsE2E:
    """End-to-end tests for action chains."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def mock_current_user(self):
        """Mock current user token."""
        return TokenPayload(
            sub="test_patient_123",
            role=["patient"],
            exp=int((datetime.utcnow() + timedelta(hours=1)).timestamp())
        )
    
    @pytest.mark.asyncio
    async def test_appointment_reminder_pattern_e2e(self, mock_db_session):
        """Test appointment with reminder pattern end-to-end."""
        # Create pattern
        pattern = ActionPatterns.appointment_with_reminder(
            patient_id="test_patient",
            scheduled_time=(datetime.utcnow() + timedelta(days=7)).isoformat(),
            duration_minutes=30,
            appointment_type="Follow-up",
            reminder_days_before=1
        )
        
        # Mock action executor responses
        with patch('app.services.action_executor_service.ActionExecutorService.execute_action') as mock_execute:
            mock_execute.side_effect = [
                Mock(success=True, action_type="appointment_create", 
                     message="Appointment created", 
                     data={"appointment_id": "apt_123", "patient_id": "test_patient"}),
                Mock(success=True, action_type="notification_create",
                     message="Reminder scheduled",
                     data={"notification_id": "notif_456"})
            ]
            
            # Execute chain
            executor = ActionChainExecutorService(mock_db_session)
            result = await executor.execute_chain(
                user_id="test_user",
                user_role="patient",
                chain=pattern
            )
            
            assert result.success is True
            assert len(result.results) == 2
            assert result.results[0].action_type == "appointment_create"
            assert result.results[1].action_type == "notification_create"
            assert mock_execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_pattern_detection_to_execution(self, mock_db_session):
        """Test from pattern detection to execution."""
        user_input = "Schedule an appointment tomorrow at 2pm and remind me the day before"
        
        # Test pattern detection
        detected = PatternDetector.detect_pattern(user_input)
        assert detected == "appointment_with_reminder"
        
        # Test intent resolution with compound support
        with patch('app.services.intent_resolver_service.IntentResolverService.detect_compound_action') as mock_detect:
            mock_detect.return_value = {
                "is_compound": True,
                "action_count": 2,
                "detected_actions": ["schedule appointment", "set reminder"],
                "relationship": "sequential"
            }
            
            resolver = IntentResolverService()
            # Would continue with full resolution in real test
            assert resolver is not None
    
    @pytest.mark.asyncio
    async def test_conditional_action_execution(self, mock_db_session):
        """Test conditional action execution based on data."""
        # Create conditional pattern
        pattern = ActionPatterns.conditional_weight_appointment(
            weight_value=205.0,
            unit="lb",
            threshold=200.0
        )
        
        # Mock weight log success
        with patch('app.services.action_executor_service.ActionExecutorService.execute_action') as mock_execute:
            mock_execute.side_effect = [
                Mock(success=True, action_type="weight_log_create",
                     message="Weight logged",
                     data={"weight_id": "weight_123", "value": 205.0}),
                Mock(success=True, action_type="appointment_request_create",
                     message="Appointment requested",
                     data={"request_id": "req_456"})
            ]
            
            executor = ActionChainExecutorService(mock_db_session)
            result = await executor.execute_chain(
                user_id="test_user",
                user_role="patient",
                chain=pattern
            )
            
            # Both actions should execute since weight > threshold
            assert len(result.results) == 2
            assert result.results[1].action_type == "appointment_request_create"
    
    @pytest.mark.asyncio
    async def test_batch_operations_parallel(self, mock_db_session):
        """Test batch operations execute in parallel."""
        # Create batch notification pattern
        recipients = ["patient_1", "patient_2", "patient_3"]
        pattern = ActionPatterns.batch_notifications(
            recipient_ids=recipients,
            notification_type="announcement",
            title="Test Notice",
            message="Test message",
            priority="medium"
        )
        
        assert pattern.execution_mode == ExecutionMode.PARALLEL
        
        # Mock parallel execution
        with patch('app.services.action_executor_service.ActionExecutorService.execute_action') as mock_execute:
            mock_execute.return_value = Mock(
                success=True, 
                action_type="notification_create",
                message="Notification sent"
            )
            
            executor = ActionChainExecutorService(mock_db_session)
            result = await executor.execute_chain(
                user_id="test_admin",
                user_role="admin",
                chain=pattern
            )
            
            # All notifications should be sent
            assert len(result.results) == len(recipients)
            assert all(r.success for r in result.results)
    
    @pytest.mark.asyncio
    async def test_error_recovery_continue_on_failure(self, mock_db_session):
        """Test that chains can continue after failures when configured."""
        # Create chain with multiple actions
        entries = [
            ("2025-05-25", 185.0, "lb"),
            ("invalid-date", 184.5, "lb"),  # This will fail
            ("2025-05-27", 183.8, "lb")
        ]
        pattern = ActionPatterns.batch_weight_logs(entries)
        
        with patch('app.services.action_executor_service.ActionExecutorService.execute_action') as mock_execute:
            # Second entry will fail
            mock_execute.side_effect = [
                Mock(success=True, action_type="weight_log_create"),
                Mock(success=False, action_type="weight_log_create", 
                     error_message="Invalid date format"),
                Mock(success=True, action_type="weight_log_create")
            ]
            
            executor = ActionChainExecutorService(mock_db_session)
            result = await executor.execute_chain(
                user_id="test_user",
                user_role="patient",
                chain=pattern
            )
            
            # Should have all 3 results despite one failure
            assert len(result.results) == 3
            assert result.results[0].success is True
            assert result.results[1].success is False
            assert result.results[2].success is True
            assert not result.success  # Overall failure due to one failed action
    
    @pytest.mark.asyncio
    async def test_context_passing_between_actions(self, mock_db_session):
        """Test that context is properly passed between actions."""
        # Create pattern that requires context passing
        pattern = ActionPatterns.side_effect_with_followup(
            patient_id="test_patient",
            medication_name="TestMed",
            symptoms="severe reaction",
            severity="Severe",
            onset_time="1 hour ago"
        )
        
        captured_contexts = []
        
        def capture_context(*args, **kwargs):
            # Capture the resolved intent to check context substitution
            resolved_intent = kwargs.get('resolved_intent')
            if resolved_intent:
                captured_contexts.append(resolved_intent.parameters)
            
            # Return appropriate response based on action type
            action_type = resolved_intent.action_type if resolved_intent else "unknown"
            if action_type == "side_effect_report_create":
                return Mock(success=True, action_type=action_type,
                           data={"report_id": "report_123", "severity": "Severe"})
            else:
                return Mock(success=True, action_type=action_type)
        
        with patch('app.services.action_executor_service.ActionExecutorService.execute_action', 
                  side_effect=capture_context) as mock_execute:
            
            executor = ActionChainExecutorService(mock_db_session)
            result = await executor.execute_chain(
                user_id="test_user",
                user_role="patient",
                chain=pattern
            )
            
            # Check that context was available to follow-up actions
            assert len(captured_contexts) >= 2
            # The notification should have access to the side effect report data
            # This would be more detailed in a real implementation
            assert result.success is True


@pytest.mark.integration
@pytest.mark.asyncio
async def test_full_api_flow(mock_db_session):
    """Test the full API flow from text input to chain execution."""
    from app.schemas.auth import TokenPayload
    from app.api.deps import get_db, get_current_user
    
    # Mock dependencies
    mock_user = TokenPayload(
        sub="test_patient_123",
        role=["patient"],
        exp=int((datetime.utcnow() + timedelta(hours=1)).timestamp())
    )
    
    # This would be a more complete test in a real implementation
    # including mocking the template validation, intent resolution, etc.
    pass