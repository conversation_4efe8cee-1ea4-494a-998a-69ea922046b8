#!/usr/bin/env python3
"""Test the full compound action flow to verify dependency checking works."""

import asyncio
import sys
import os
from datetime import datetime, timezone, timedelta
from pydantic import ValidationError

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.intent_resolver_service import IntentResolverService
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.services.action_executor_service import ActionExecutorService
from app.core.llm.factory import LLMFactory
from app.schemas.action_chain_v2 import ChainContext


async def test_compound_action_flow():
    """Test the full compound action execution flow."""
    print("🔬 Testing Compound Action Dependency Flow")
    print("=" * 50)
    
    # Test message that should create a side effect report + appointment
    message = "<PERSON> reported severe nausea from Ozempic this morning. Schedule him for a follow-up appointment tomorrow at 2pm to discuss alternative medications."
    
    print(f"📝 Test Message: {message}")
    print()
    
    # Step 1: Test intent detection
    print("🧠 Step 1: Intent Detection")
    try:
        resolver = IntentResolverService()
        detection_result = await resolver.detect_compound_action(message)
        
        if isinstance(detection_result, dict) and not detection_result.get('is_compound_action', False):
            print("❌ Not detected as compound action")
            return
        
        print("✅ Detected as compound action")
        print()
        
    except Exception as e:
        print(f"❌ Intent detection failed: {e}")
        return
    
    # Step 2: Test LLM extraction
    print("🤖 Step 2: LLM Chain Extraction")
    try:
        llm = LLMFactory.create_llm()
        resolver = IntentResolverService()
        
        # Manually call the LLM extraction to test
        chain = await resolver._extract_action_chain_with_llm(message)
        
        print(f"✅ Extracted chain:")
        print(f"   Primary: {chain.primary_action.action_type}")
        print(f"   Follow-ups: {[a.action_type for a in chain.follow_up_actions]}")
        
        # Check dependencies
        for i, action in enumerate(chain.follow_up_actions):
            deps = [dep.required_action for dep in action.dependencies] if action.dependencies else []
            print(f"   Action {i+1} dependencies: {deps}")
        print()
        
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")
        return
    
    # Step 3: Test execution with mock context
    print("⚡ Step 3: Chain Execution Test")
    try:
        # Create executor
        action_executor = ActionExecutorService()
        chain_executor = ActionChainExecutorService(action_executor)
        
        # Create mock context with patient ID
        context = ChainContext()
        context.data["patient_id"] = "user_2waTCuGL3kQC9k2rY47INdcJXk5"  # Demo patient
        context.data["clinician_id"] = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"  # Demo clinician
        
        # Mock user details
        user_id = "user_2waSREJSlduBPyK6Vbv9TU3VhI7"  # Clinician
        user_role = "clinician"
        
        print(f"👤 User: {user_id} (role: {user_role})")
        print(f"🏥 Patient context: {context.data['patient_id']}")
        print()
        
        # Execute the chain
        print("🔄 Executing chain...")
        result = await chain_executor.execute_action_chain(
            user_id=user_id,
            user_role=user_role,
            chain=chain,
            context=context
        )
        
        print(f"✅ Chain execution completed!")
        print(f"   Success: {result.success}")
        print(f"   Actions executed: {len(result.action_results)}")
        
        for i, action_result in enumerate(result.action_results):
            print(f"   Action {i+1}: {action_result.action_type} -> {'✅' if action_result.success else '❌'}")
            if not action_result.success:
                print(f"     Error: {action_result.message}")
        
        if result.error_message:
            print(f"   Error: {result.error_message}")
        
        print()
        
        # Expected: 2 actions (side effect report + appointment)
        if len(result.action_results) >= 2:
            print("🎉 SUCCESS: Both actions executed!")
        else:
            print("⚠️  WARNING: Expected 2 actions, but only got", len(result.action_results))
            
    except Exception as e:
        print(f"❌ Chain execution failed: {e}")
        import traceback
        traceback.print_exc()
        return


if __name__ == "__main__":
    asyncio.run(test_compound_action_flow())