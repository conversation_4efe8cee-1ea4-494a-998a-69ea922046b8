#!/usr/bin/env python3
"""
Test the new RAG intent detection system.
"""
import sys
sys.path.append('/app')

from app.services.chatbot_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_rag_intent_detection():
    """Test RAG intent detection patterns."""
    manager = ChatbotManager()
    
    test_cases = [
        # Should trigger RAG
        ("Tell me about the treatments at the Edinburgh clinic.", True),
        ("What treatments does our clinic offer?", True),
        ("What medications are available here?", True),
        ("What are our clinic's services?", True),
        ("How much do treatments cost?", True),
        ("What is our protocol for weight loss?", True),
        ("Who are our clinicians?", True),
        ("What does the clinic provide?", True),
        
        # Should NOT trigger RAG (clinical guidance)
        ("Tell me about side effects of tirzepatide", False),
        ("How should I manage diabetes?", False),
        ("What are the latest guidelines for GLP-1?", False),
        ("How do I counsel patients about weight loss?", False),
        
        # Should trigger actions
        ("Schedule an appointment for tomorrow", False),  # Should be action
        ("Log weight for patient today", False),  # Should be action
    ]
    
    print("Testing RAG Intent Detection")
    print("=" * 50)
    
    for message, expected_rag in test_cases:
        is_rag = manager._is_rag_request(message)
        is_action = manager._is_action_request(message)
        
        # Simulate routing decision
        context = {"user_role": "clinician"}
        selected_module = manager._select_module(message, context)
        
        status = "✅" if (is_rag == expected_rag) else "❌"
        
        print(f"{status} \"{message}\"")
        print(f"    RAG: {is_rag} (expected: {expected_rag})")
        print(f"    Action: {is_action}")
        print(f"    Module: {selected_module}")
        print()

if __name__ == "__main__":
    test_rag_intent_detection()