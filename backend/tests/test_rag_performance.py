#!/usr/bin/env python3
"""Test RAG system performance and integration."""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add the backend directory to the path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from app.db.session import SessionLocal
    from app.utils.context_enricher import enrich_with_rag
    from app.services.embedding_pipeline import generate_embeddings
    from app.crud.crud_content_chunk import crud_content_chunk
    from app.crud.crud_scraped_page import get_scraped_pages_by_clinic
    from app.core.cache.rag_cache import rag_cache
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the backend directory with proper environment")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RAGPerformanceTester:
    """Test RAG system performance and integration."""
    
    def __init__(self):
        self.db = SessionLocal()
        self.test_queries = [
            "What are the side effects of tirzepatide?",
            "How should I manage my diabetes medication?", 
            "Tell me about hypertension treatment options",
            "What are the clinic's hours and location?",
            "How do I schedule an appointment?",
            "What weight loss programs do you offer?",
            "Tell me about bariatric surgery options",
            "How often should I check my blood pressure?",
        ]
        
    def test_content_availability(self):
        """Check what content is available in the database."""
        print("\n🔍 CHECKING CONTENT AVAILABILITY")
        print("=" * 50)
        
        try:
            # Check scraped pages
            pages = self.db.execute("SELECT COUNT(*) FROM scraped_pages").scalar()
            print(f"📄 Scraped Pages: {pages}")
            
            # Check content chunks  
            chunks = self.db.execute("SELECT COUNT(*) FROM content_chunks").scalar()
            print(f"📝 Content Chunks: {chunks}")
            
            # Check embeddings
            chunks_with_embeddings = self.db.execute(
                "SELECT COUNT(*) FROM content_chunks WHERE embedding IS NOT NULL"
            ).scalar()
            print(f"🔢 Chunks with Embeddings: {chunks_with_embeddings}")
            
            # Get sample chunk
            if chunks > 0:
                sample = self.db.execute(
                    "SELECT chunk_text FROM content_chunks LIMIT 1"
                ).scalar()
                print(f"📋 Sample Chunk: {sample[:100]}...")
                
        except Exception as e:
            print(f"❌ Error checking content: {e}")
            
    def test_embedding_generation(self):
        """Test embedding generation performance."""
        print("\n🧠 TESTING EMBEDDING GENERATION")
        print("=" * 50)
        
        test_texts = [
            "What are the side effects of tirzepatide?",
            "How should I manage diabetes?",
            "Tell me about weight loss options"
        ]
        
        try:
            start_time = time.time()
            embeddings = generate_embeddings(test_texts)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000  # Convert to ms
            
            print(f"✅ Generated {len(embeddings)} embeddings")
            print(f"⏱️  Duration: {duration:.2f}ms")
            print(f"📊 Avg per embedding: {duration/len(embeddings):.2f}ms")
            
            if embeddings and embeddings[0]:
                print(f"📏 Embedding dimension: {len(embeddings[0])}")
                
        except Exception as e:
            print(f"❌ Error generating embeddings: {e}")
            
    def test_rag_retrieval_performance(self):
        """Test RAG retrieval performance."""
        print("\n🔎 TESTING RAG RETRIEVAL PERFORMANCE")  
        print("=" * 50)
        
        results = []
        
        for query in self.test_queries[:5]:  # Test first 5 queries
            try:
                print(f"\n🔍 Testing: '{query[:50]}...'")
                
                start_time = time.time()
                rag_context = enrich_with_rag(
                    db=self.db,
                    user_id="test_user_123",  # Test user
                    message=query,
                    user_role="patient"
                )
                end_time = time.time()
                
                duration = (end_time - start_time) * 1000
                
                print(f"   ⏱️  Duration: {duration:.2f}ms")
                
                # Check what was retrieved
                chunks = rag_context.get("rag_context_chunks", [])
                pages = rag_context.get("rag_scraped_pages", [])
                fallback = rag_context.get("rag_fallback_message")
                
                print(f"   📝 Chunks retrieved: {len(chunks)}")
                print(f"   📄 Pages retrieved: {len(pages)}")
                
                if fallback:
                    print(f"   ⚠️  Fallback used: {fallback[:50]}...")
                
                results.append({
                    "query": query,
                    "duration_ms": duration,
                    "chunks_count": len(chunks),
                    "pages_count": len(pages),
                    "used_fallback": bool(fallback)
                })
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                results.append({
                    "query": query,
                    "error": str(e)
                })
                
        # Summary
        successful_results = [r for r in results if "error" not in r]
        if successful_results:
            avg_duration = sum(r["duration_ms"] for r in successful_results) / len(successful_results)
            print(f"\n📊 PERFORMANCE SUMMARY")
            print(f"   Average duration: {avg_duration:.2f}ms")
            print(f"   Target: <100ms")
            print(f"   Status: {'✅ PASS' if avg_duration < 100 else '❌ FAIL'}")
            
        return results
        
    def test_cache_performance(self):
        """Test cache hit/miss performance."""
        print("\n💾 TESTING CACHE PERFORMANCE")
        print("=" * 50)
        
        test_query = "What are the side effects of diabetes medication?"
        
        try:
            # First call (cache miss)
            print("🔄 First call (cache miss)...")
            start_time = time.time()
            result1 = enrich_with_rag(
                db=self.db,
                user_id="test_user_123",
                message=test_query,
                user_role="patient"
            )
            miss_duration = (time.time() - start_time) * 1000
            
            # Second call (cache hit)
            print("⚡ Second call (cache hit)...")
            start_time = time.time()
            result2 = enrich_with_rag(
                db=self.db,
                user_id="test_user_123", 
                message=test_query,
                user_role="patient"
            )
            hit_duration = (time.time() - start_time) * 1000
            
            print(f"   Cache miss: {miss_duration:.2f}ms")
            print(f"   Cache hit: {hit_duration:.2f}ms")
            print(f"   Speedup: {miss_duration/hit_duration:.1f}x")
            
            # Check cache stats
            stats = rag_cache.get_cache_stats()
            print(f"   Cache connected: {stats.get('connected', 'unknown')}")
            
        except Exception as e:
            print(f"❌ Error testing cache: {e}")
            
    def run_all_tests(self):
        """Run all RAG performance tests."""
        print("🚀 STARTING RAG SYSTEM VALIDATION")
        print("=" * 60)
        
        self.test_content_availability()
        self.test_embedding_generation()
        self.test_rag_retrieval_performance()
        self.test_cache_performance()
        
        print("\n✅ RAG VALIDATION COMPLETE")
        self.db.close()

if __name__ == "__main__":
    tester = RAGPerformanceTester()
    tester.run_all_tests()