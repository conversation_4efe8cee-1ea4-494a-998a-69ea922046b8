#!/usr/bin/env python3
"""Test RAG system with real user IDs from database."""

import logging
import time
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from app.db.session import SessionLocal
from app.utils.context_enricher import enrich_with_rag

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_rag_with_real_users():
    """Test RAG system with actual user IDs from database."""
    
    print("🚀 RAG SYSTEM TEST WITH REAL USERS")
    print("=" * 50)
    
    # Real user IDs from database
    test_users = [
        {
            "id": "user_2waTCuGL3kQC9k2rY47INdcJXk5",
            "name": "<PERSON>", 
            "role": "patient"
        },
        {
            "id": "user_2waSREJSlduBPyK6Vbv9TU3VhI7", 
            "name": "<PERSON>, <PERSON>",
            "role": "clinician"
        }
    ]
    
    test_queries = [
        "What are the side effects of tirzepatide?",
        "How should I manage my diabetes medication?",
        "What are the clinic's hours and location?",
    ]
    
    db = SessionLocal()
    
    try:
        for user in test_users:
            print(f"\n👤 Testing as {user['role']}: {user['name']}")
            print("-" * 40)
            
            for query in test_queries:
                print(f"\n🔍 Query: '{query[:50]}...'")
                
                try:
                    start_time = time.time()
                    rag_context = enrich_with_rag(
                        db=db,
                        user_id=user["id"],
                        message=query,
                        user_role=user["role"]
                    )
                    duration = (time.time() - start_time) * 1000
                    
                    # Analyze results
                    chunks = rag_context.get("rag_context_chunks", [])
                    pages = rag_context.get("rag_scraped_pages", [])
                    fallback = rag_context.get("rag_fallback_message")
                    
                    print(f"   ⏱️  Duration: {duration:.2f}ms")
                    print(f"   📝 Chunks: {len(chunks)}")
                    print(f"   📄 Pages: {len(pages)}")
                    
                    if fallback:
                        print(f"   ⚠️  Fallback used")
                    else:
                        print(f"   ✅ RAG content retrieved")
                        
                        # Show sample content
                        if chunks:
                            print(f"   📋 Sample chunk: {str(chunks[0])[:100]}...")
                            
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    
        print(f"\n📊 DATABASE CONTENT VERIFICATION")
        print("-" * 40)
        
        # Check clinic associations  
        clinic_query = """
        SELECT p.id as patient_id, p.first_name, c.id as clinic_id, c.name as clinic_name
        FROM patients p 
        LEFT JOIN clinics c ON p.associated_clinic_id = c.id 
        WHERE p.id = 'user_2waTCuGL3kQC9k2rY47INdcJXk5'
        UNION
        SELECT cl.id as clinician_id, cl.first_name, c.id as clinic_id, c.name as clinic_name  
        FROM clinicians cl
        LEFT JOIN clinician_clinic_association cca ON cl.id = cca.clinician_id
        LEFT JOIN clinics c ON cca.clinic_id = c.id
        WHERE cl.id = 'user_2waSREJSlduBPyK6Vbv9TU3VhI7'
        """
        
        result = db.execute(clinic_query).fetchall()
        for row in result:
            print(f"   User: {row[1]} -> Clinic: {row[3] or 'None'}")
            
        # Check scraped content for clinics
        content_query = """
        SELECT c.id, c.name, 
               COUNT(sp.id) as scraped_pages,
               COUNT(cc.id) as content_chunks
        FROM clinics c
        LEFT JOIN scraped_pages sp ON c.id = sp.clinic_id  
        LEFT JOIN content_chunks cc ON sp.id = cc.scraped_page_id
        GROUP BY c.id, c.name
        """
        
        content_result = db.execute(content_query).fetchall()
        print(f"\n   CLINIC CONTENT SUMMARY:")
        for row in content_result:
            print(f"   - {row[1]}: {row[2]} pages, {row[3]} chunks")
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()
        
    print(f"\n✅ RAG REAL USER TEST COMPLETE")

if __name__ == "__main__":
    test_rag_with_real_users()