#!/usr/bin/env python3
"""Simple RAG system performance test."""

import logging
import time
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from app.db.session import SessionLocal
from app.utils.context_enricher import enrich_with_rag
from app.services.embedding_pipeline import generate_embeddings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_rag_system():
    """Test RAG system components."""
    
    print("🚀 RAG SYSTEM PERFORMANCE TEST")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Test queries
        test_queries = [
            "What are the side effects of tirzepatide?",
            "How should I manage my diabetes medication?", 
            "Tell me about hypertension treatment options",
            "What are the clinic's hours and location?",
            "How do I schedule an appointment?",
        ]
        
        print(f"📊 Testing {len(test_queries)} clinical queries...")
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Testing: '{query[:50]}...'")
            
            try:
                # Test embedding generation
                start_time = time.time()
                embeddings = generate_embeddings([query])
                embedding_time = (time.time() - start_time) * 1000
                
                print(f"   🧠 Embedding generation: {embedding_time:.2f}ms")
                
                # Test RAG enrichment
                start_time = time.time()
                rag_context = enrich_with_rag(
                    db=db,
                    user_id="test_user_123",
                    message=query,
                    user_role="patient"
                )
                rag_time = (time.time() - start_time) * 1000
                
                print(f"   🔍 RAG retrieval: {rag_time:.2f}ms")
                
                # Check results
                chunks = rag_context.get("rag_context_chunks", [])
                pages = rag_context.get("rag_scraped_pages", [])
                fallback = rag_context.get("rag_fallback_message")
                
                print(f"   📝 Chunks retrieved: {len(chunks)}")
                print(f"   📄 Pages retrieved: {len(pages)}")
                
                if fallback:
                    print(f"   ⚠️  Used fallback: Yes")
                else:
                    print(f"   ✅ Found relevant content")
                
                results.append({
                    "query": query,
                    "embedding_time_ms": embedding_time,
                    "rag_time_ms": rag_time,
                    "total_time_ms": embedding_time + rag_time,
                    "chunks_found": len(chunks),
                    "pages_found": len(pages),
                    "used_fallback": bool(fallback)
                })
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                
        # Performance summary
        successful_results = [r for r in results if "error" not in r]
        
        if successful_results:
            avg_embedding = sum(r["embedding_time_ms"] for r in successful_results) / len(successful_results)
            avg_rag = sum(r["rag_time_ms"] for r in successful_results) / len(successful_results)
            avg_total = sum(r["total_time_ms"] for r in successful_results) / len(successful_results)
            
            chunks_with_content = sum(1 for r in successful_results if r["chunks_found"] > 0)
            fallback_used = sum(1 for r in successful_results if r["used_fallback"])
            
            print(f"\n📊 PERFORMANCE SUMMARY")
            print(f"   Successful queries: {len(successful_results)}/{len(test_queries)}")
            print(f"   Average embedding time: {avg_embedding:.2f}ms")
            print(f"   Average RAG retrieval: {avg_rag:.2f}ms")
            print(f"   Average total time: {avg_total:.2f}ms")
            print(f"   Target: <100ms retrieval")
            print(f"   Status: {'✅ PASS' if avg_rag < 100 else '❌ FAIL'}")
            print(f"   Content found: {chunks_with_content}/{len(successful_results)} queries")
            print(f"   Fallback used: {fallback_used}/{len(successful_results)} queries")
            
        else:
            print("❌ No successful tests")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        
    finally:
        db.close()
        
    print("\n✅ RAG PERFORMANCE TEST COMPLETE")

if __name__ == "__main__":
    test_rag_system()