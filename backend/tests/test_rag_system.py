"""Test suite for evaluating the RAG (Retrieval-Augmented Generation) system."""

import logging
import time
import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.crud_clinic import crud_clinic
from app.crud.crud_content_chunk import crud_content_chunk
from app.crud.crud_scraped_page import scraped_page as crud_scraped_page
from app.services.chat_agent import process_chat_message
from app.services.embedding_pipeline import (
    chunk_text,
    generate_embeddings,
    process_scraped_page_content,
)
from app.utils.context_enricher import (
    enrich_with_rag,
    format_context_for_prompt,
    get_patient_context,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestRAGSystem:
    """Test cases for the RAG system components."""

    @pytest.fixture
    async def setup_test_data(self, db: AsyncSession):
        """Set up test data for RAG system testing."""
        # Create a test clinic
        clinic = await crud_clinic.create(
            db,
            obj_in={
                "name": "Test Clinic for RAG",
                "address": "123 Test Street",
                "website_url": "https://testclinic.com",
            },
        )

        # Create scraped pages with medical content
        scraped_pages = []
        for i in range(3):
            page = await crud_scraped_page.create(
                db,
                obj_in={
                    "clinic_id": clinic.id,
                    "source_url": f"https://testclinic.com/page{i}",
                    "title": f"Medical Content Page {i}",
                    "cleaned_content": self._generate_test_content(i),
                    "full_html": "<html>test</html>",
                },
            )
            scraped_pages.append(page)

        # Process scraped pages to generate embeddings
        for page in scraped_pages:
            process_scraped_page_content(db, page)

        yield {
            "clinic": clinic,
            "scraped_pages": scraped_pages,
        }

    def _generate_test_content(self, index: int) -> str:
        """Generate test medical content for RAG testing."""
        contents = [
            """
            Diabetes Management Guidelines:
            - Monitor blood glucose levels regularly
            - Maintain a balanced diet with controlled carbohydrate intake
            - Regular physical activity of at least 150 minutes per week
            - Take medications as prescribed
            - Regular check-ups with healthcare provider
            """,
            """
            Hypertension Treatment Protocols:
            - Monitor blood pressure daily
            - Reduce sodium intake to less than 2g per day
            - DASH diet recommendations
            - Regular cardiovascular exercise
            - Stress management techniques
            - Medication compliance
            """,
            """
            Weight Management Strategies:
            - Caloric deficit for sustainable weight loss
            - Balanced macronutrient distribution
            - Portion control techniques
            - Regular physical activity
            - Behavioral modification strategies
            - Sleep hygiene importance
            """,
        ]
        return contents[index % len(contents)]

    async def test_embedding_generation(self):
        """Test that embeddings are generated correctly."""
        test_texts = [
            "Diabetes management requires daily monitoring",
            "Hypertension is a chronic condition",
            "Weight loss needs lifestyle changes",
        ]

        embeddings = generate_embeddings(test_texts)

        assert embeddings is not None
        assert len(embeddings) == len(test_texts)
        assert all(isinstance(emb, list) for emb in embeddings)
        assert all(len(emb) == 768 for emb in embeddings)  # all-mpnet-base-v2 dimension

    async def test_text_chunking(self):
        """Test text chunking functionality."""
        test_text = "A" * 1000
        chunks = chunk_text(test_text, size=300, overlap=50)

        assert len(chunks) > 1
        assert all(len(chunk) <= 300 for chunk in chunks)
        # Check overlap
        for i in range(len(chunks) - 1):
            assert chunks[i][-50:] == chunks[i + 1][:50]

    async def test_rag_enrichment(self, db: AsyncSession, setup_test_data):
        """Test RAG context enrichment for chat messages."""
        clinic = setup_test_data["clinic"]
        patient_id = str(uuid.uuid4())  # Mock patient ID

        # Mock patient context
        class MockPatient:
            id = patient_id
            associated_clinic_id = clinic.id

        # Test RAG enrichment
        test_queries = [
            "What are the guidelines for diabetes management?",
            "How should I monitor my blood pressure?",
            "What's the best approach for weight loss?",
        ]

        for query in test_queries:
            context = enrich_with_rag(
                db=db,
                user_id=patient_id,
                message=query,
                user_role="patient",
            )

            assert "rag_context_chunks" in context or "rag_fallback_message" in context
            if "rag_context_chunks" in context:
                assert len(context["rag_context_chunks"]) > 0

    async def test_context_formatting(self):
        """Test context formatting for LLM prompts."""
        test_context = {
            "patient_context": {
                "first_name": "John",
                "last_name": "Doe",
                "health_metrics": {
                    "latest_weight": {"weight_kg": 85, "log_date": "2024-01-01"},
                    "active_medications": [
                        {"medication_name": "Metformin", "dosage": "500mg"},
                    ],
                },
            },
            "rag_context_chunks": [
                "Diabetes requires regular monitoring",
                "Exercise is important for health",
            ],
        }

        formatted = format_context_for_prompt(test_context)

        assert "John" in formatted
        assert "85kg" in formatted
        assert "Metformin" in formatted
        assert "Diabetes requires regular monitoring" in formatted

    async def test_similarity_search(self, db: AsyncSession, setup_test_data):
        """Test similarity search for content chunks."""
        clinic = setup_test_data["clinic"]

        # Generate embedding for a test query
        query = "diabetes blood glucose monitoring"
        query_embedding = generate_embeddings([query])[0]

        # Search for similar chunks
        similar_chunks = crud_content_chunk.find_similar_content_chunks(
            db=db,
            query_embedding=query_embedding,
            limit=3,
            clinic_id=clinic.id,
        )

        assert len(similar_chunks) > 0
        # First result should be most relevant to diabetes
        assert "diabetes" in similar_chunks[0].chunk_text.lower()

    async def test_chat_with_rag(self, db: AsyncSession, setup_test_data):
        """Test full chat flow with RAG integration."""
        clinic = setup_test_data["clinic"]
        patient_id = str(uuid.uuid4())

        # Mock patient with clinic association
        class MockPatient:
            id = patient_id
            associated_clinic_id = clinic.id
            first_name = "Test"
            last_name = "Patient"

        # Process a chat message
        response = await process_chat_message(
            user_id=patient_id,
            user_message="What are the recommended guidelines for managing diabetes?",
            db=db,
            context={
                "user_role": "patient",
                "patient_context": get_patient_context(db, patient_id),
            },
        )

        assert isinstance(response, (str, dict))
        if isinstance(response, dict):
            assert "response" in response

    async def test_performance_benchmarks(self, db: AsyncSession, setup_test_data):
        """Benchmark RAG system performance."""
        clinic = setup_test_data["clinic"]

        # Generate test embeddings
        test_queries = [
            "diabetes management",
            "blood pressure monitoring",
            "weight loss strategies",
            "medication compliance",
            "exercise recommendations",
        ]

        # Measure embedding generation time
        start_time = time.time()
        embeddings = generate_embeddings(test_queries)
        embedding_time = time.time() - start_time

        # Measure similarity search time
        search_times = []
        for embedding in embeddings:
            start_time = time.time()
            crud_content_chunk.find_similar_content_chunks(
                db=db,
                query_embedding=embedding,
                limit=3,
                clinic_id=clinic.id,
            )
            search_times.append(time.time() - start_time)

        avg_search_time = sum(search_times) / len(search_times)

        logger.info(
            f"Embedding generation time: {embedding_time:.3f}s for {len(test_queries)} queries"
        )
        logger.info(f"Average similarity search time: {avg_search_time:.3f}s")

        # Assert reasonable performance bounds
        assert embedding_time < 1.0  # Should be under 1 second for 5 queries
        assert avg_search_time < 0.1  # Should be under 100ms per search

    async def test_rag_accuracy(self, db: AsyncSession, setup_test_data):
        """Test RAG retrieval accuracy."""
        clinic = setup_test_data["clinic"]

        # Test cases with expected content matches
        test_cases = [
            ("diabetes glucose monitoring", "Diabetes Management Guidelines"),
            ("blood pressure reduction", "Hypertension Treatment Protocols"),
            ("caloric deficit weight loss", "Weight Management Strategies"),
        ]

        accuracy_scores = []
        for query, expected_content in test_cases:
            query_embedding = generate_embeddings([query])[0]
            results = crud_content_chunk.find_similar_content_chunks(
                db=db,
                query_embedding=query_embedding,
                limit=1,
                clinic_id=clinic.id,
            )

            if results:
                chunk_text = results[0].chunk_text
                # Check if expected content appears in retrieved chunk
                accuracy_scores.append(expected_content in chunk_text)
            else:
                accuracy_scores.append(False)

        accuracy = sum(accuracy_scores) / len(accuracy_scores)
        logger.info(f"RAG accuracy: {accuracy:.2%}")

        assert accuracy >= 0.8  # Expect at least 80% accuracy

    async def test_error_handling(self, db: AsyncSession):
        """Test error handling in RAG system."""
        # Test with invalid clinic ID
        invalid_context = enrich_with_rag(
            db=db,
            user_id=str(uuid.uuid4()),
            message="test query",
            user_role="patient",
        )

        assert "rag_fallback_message" in invalid_context

        # Test with empty message
        empty_context = enrich_with_rag(
            db=db,
            user_id=str(uuid.uuid4()),
            message="",
            user_role="patient",
        )

        assert "rag_fallback_message" in empty_context


if __name__ == "__main__":
    # Run specific tests
    pytest.main([__file__, "-v", "-s"])
