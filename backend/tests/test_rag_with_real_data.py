#!/usr/bin/env python3
"""
Test RAG system with real patient and clinic data.
"""
import asyncio
import os
import sys
import time
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

# Add the app directory to Python path
sys.path.append('/app')

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************/pulsetrack")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_rag_enrichment():
    """Test RAG enrichment with real patient data."""
    from app.utils.context_enricher import enrich_with_rag
    
    db = SessionLocal()
    
    # Test queries related to weight loss
    test_queries = [
        "What should I eat for breakfast while losing weight?",
        "I'm feeling hungry between meals, what snacks are recommended?", 
        "How often should I weigh myself?",
        "What are normal side effects of weight loss medication?",
        "I'm having trouble sleeping, is this related to my treatment?"
    ]
    
    # Real patient ID from the database (Michael user)
    patient_id = "user_2waTCuGL3kQC9k2rY47INdcJXk5"
    
    print(f"Testing RAG enrichment for patient: {patient_id}")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}: {query}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            # Test patient RAG enrichment
            result = enrich_with_rag(db, patient_id, query, "patient")
            
            retrieval_time = (time.time() - start_time) * 1000
            
            print(f"Retrieval time: {retrieval_time:.2f}ms")
            
            if "rag_context_chunks" in result:
                chunks = result["rag_context_chunks"]
                print(f"Retrieved {len(chunks)} RAG chunks:")
                for j, chunk in enumerate(chunks, 1):
                    preview = chunk[:150] + "..." if len(chunk) > 150 else chunk
                    print(f"  Chunk {j}: {preview}")
            else:
                print("No RAG chunks retrieved")
                
            if "rag_scraped_pages" in result:
                pages = result["rag_scraped_pages"]
                print(f"Retrieved {len(pages)} scraped pages:")
                for page in pages:
                    print(f"  - {page.get('title', 'No title')}: {page.get('url', 'No URL')}")
            else:
                print("No scraped pages retrieved")
                
            if "rag_fallback_message" in result:
                print(f"Fallback message: {result['rag_fallback_message']}")
                
        except Exception as e:
            print(f"Error during RAG enrichment: {e}")
            import traceback
            traceback.print_exc()
            
        print()
    
    db.close()

async def test_clinician_rag():
    """Test RAG enrichment with clinician context."""
    from app.utils.context_enricher import enrich_with_rag
    
    db = SessionLocal()
    
    # Get a clinician ID from the database
    from sqlalchemy import text
    result = db.execute(text("SELECT id, first_name FROM clinicians LIMIT 1"))
    clinician_data = result.fetchone()
    
    if not clinician_data:
        print("No clinicians found in database")
        db.close()
        return
        
    clinician_id, clinician_name = clinician_data
    
    print(f"\nTesting RAG enrichment for clinician: {clinician_id} ({clinician_name})")
    print("=" * 60)
    
    test_queries = [
        "What are the latest guidelines for GLP-1 medications?",
        "How should I counsel patients about weight loss expectations?",
        "What are common side effects I should monitor for?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}: {query}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = enrich_with_rag(db, clinician_id, query, "clinician")
            retrieval_time = (time.time() - start_time) * 1000
            
            print(f"Retrieval time: {retrieval_time:.2f}ms")
            
            if "rag_context_chunks" in result:
                chunks = result["rag_context_chunks"]
                print(f"Retrieved {len(chunks)} RAG chunks")
            else:
                print("No RAG chunks retrieved")
                
            if "rag_fallback_message" in result:
                print(f"Fallback message: {result['rag_fallback_message']}")
                
        except Exception as e:
            print(f"Error: {e}")
            
    db.close()

def main():
    """Run all RAG tests."""
    print("Starting comprehensive RAG system validation...")
    print("Testing with real patient and clinic data from database")
    
    # Run patient tests
    asyncio.run(test_rag_enrichment())
    
    # Run clinician tests  
    asyncio.run(test_clinician_rag())
    
    print("\nRAG system validation completed!")

if __name__ == "__main__":
    main()