#!/usr/bin/env python3
"""
Test different similarity thresholds to understand why no chunks are returned.
"""
import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the app directory to Python path
sys.path.append('/app')

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************/pulsetrack")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_similarity_thresholds():
    """Test different similarity thresholds to see what we get."""
    db = SessionLocal()
    
    try:
        # Get clinic ID
        from sqlalchemy import text
        result = db.execute(text("SELECT id FROM clinics LIMIT 1"))
        clinic_id = result.scalar()
        
        # Generate embedding for test query
        from app.services.embedding_pipeline import generate_embeddings
        test_query = "weight loss breakfast recommendations"
        embeddings = generate_embeddings([test_query])
        
        if not embeddings or not embeddings[0]:
            print("Failed to generate embedding")
            return
            
        query_embedding = embeddings[0]
        print(f"Testing query: '{test_query}'")
        print(f"Clinic ID: {clinic_id}")
        print(f"Query embedding dimension: {len(query_embedding)}")
        print("=" * 60)
        
        # Test different thresholds
        thresholds = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        from app.crud.crud_content_chunk import crud_content_chunk
        
        for threshold in thresholds:
            try:
                similar_chunks = crud_content_chunk.find_similar_content_chunks(
                    db, 
                    query_embedding=query_embedding, 
                    limit=5, 
                    clinic_id=str(clinic_id),
                    similarity_threshold=threshold
                )
                
                print(f"Threshold {threshold:.1f}: {len(similar_chunks)} chunks")
                
                if similar_chunks:
                    # Show the first chunk for reference
                    chunk = similar_chunks[0]
                    preview = chunk.chunk_text[:100] + "..." if len(chunk.chunk_text) > 100 else chunk.chunk_text
                    print(f"  Sample: {preview}")
                    
            except Exception as e:
                print(f"Threshold {threshold:.1f}: ERROR - {e}")
                
        print("\n" + "=" * 60)
        print("Testing without clinic_id filter...")
        
        # Test without clinic_id to see if that's the issue
        for threshold in [0.0, 0.3, 0.5, 0.7]:
            try:
                similar_chunks = crud_content_chunk.find_similar_content_chunks(
                    db, 
                    query_embedding=query_embedding, 
                    limit=5, 
                    clinic_id=None,  # No clinic filter
                    similarity_threshold=threshold
                )
                
                print(f"No clinic filter, threshold {threshold:.1f}: {len(similar_chunks)} chunks")
                
            except Exception as e:
                print(f"No clinic filter, threshold {threshold:.1f}: ERROR - {e}")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_similarity_thresholds()