"""
Unit tests for Action Chain Executor Service
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4

from app.schemas.action_chain_v2 import (
    ChainedAction,
    ChainedIntent,
    ActionResult,
    ActionChainResult,
    ChainContext,
    ExecutionMode,
    FailureMode,
    ActionDependency
)
from app.services.action_chain_executor_service import ActionChainExecutorService
from app.services.action_executor_service import ActionResponse


@pytest.fixture
def mock_db():
    """Mock database session."""
    return Mock()


@pytest.fixture
def action_chain_executor(mock_db):
    """Create an ActionChainExecutorService instance."""
    return ActionChainExecutorService(mock_db)


@pytest.fixture
def sample_chained_intent():
    """Create a sample ChainedIntent."""
    return ChainedIntent(
        action_type="appointment_create",
        parameters={
            "patient_id": "test_patient",
            "scheduled_time": "2025-04-21T14:00:00+00:00",
            "duration_minutes": 30,
            "appointment_type": "Follow-up"
        },
        confidence=0.95
    )


@pytest.fixture
def sample_chain(sample_chained_intent):
    """Create a sample ChainedAction."""
    follow_up = ChainedIntent(
        action_type="notification_create",
        parameters={
            "recipient_id": "{{patient_id}}",
            "message": "Appointment scheduled for {{scheduled_time}}",
            "type": "appointment_reminder"
        },
        confidence=0.9,
        dependencies=[
            ActionDependency(
                required_action="appointment_create",
                require_success=True
            )
        ]
    )
    
    return ChainedAction(
        primary_action=sample_chained_intent,
        follow_up_actions=[follow_up],
        execution_mode=ExecutionMode.SEQUENTIAL,
        failure_mode=FailureMode.STOP_ON_FAILURE
    )


class TestActionChainExecutorService:
    """Test cases for ActionChainExecutorService."""
    
    @pytest.mark.asyncio
    async def test_execute_chain_sequential_success(self, action_chain_executor, sample_chain):
        """Test successful sequential chain execution."""
        # Mock the action executor
        mock_response = ActionResponse(
            success=True,
            action_type="appointment_create",
            message="Appointment created successfully",
            data={"appointment_id": "123", "patient_id": "test_patient"}
        )
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            return_value=mock_response
        ) as mock_execute:
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="clinician",
                chain=sample_chain
            )
            
            assert result.success is True
            assert result.chain_id == sample_chain.chain_id
            assert len(result.results) == 2  # Primary + follow-up
            assert mock_execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_execute_chain_with_context_substitution(self, action_chain_executor, sample_chain):
        """Test that context substitutions work correctly."""
        # Mock responses
        appointment_response = ActionResponse(
            success=True,
            action_type="appointment_create",
            message="Appointment created",
            data={"appointment_id": "123", "patient_id": "test_patient", "scheduled_time": "2025-04-21T14:00:00+00:00"}
        )
        
        notification_response = ActionResponse(
            success=True,
            action_type="notification_create",
            message="Notification sent",
            data={"notification_id": "456"}
        )
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            side_effect=[appointment_response, notification_response]
        ) as mock_execute:
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="clinician",
                chain=sample_chain
            )
            
            # Check that the second call had substituted parameters
            second_call = mock_execute.call_args_list[1]
            resolved_intent = second_call[1]['resolved_intent']
            
            # The {{patient_id}} should have been replaced with actual value
            assert resolved_intent.parameters['recipient_id'] == "test_patient"
            assert "{{patient_id}}" not in str(resolved_intent.parameters)
    
    @pytest.mark.asyncio
    async def test_execute_chain_stop_on_failure(self, action_chain_executor, sample_chain):
        """Test that chain stops on failure when configured."""
        # Mock a failure response
        failure_response = ActionResponse(
            success=False,
            action_type="appointment_create",
            message="",
            error_message="Failed to create appointment"
        )
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            return_value=failure_response
        ):
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="clinician",
                chain=sample_chain
            )
            
            assert result.success is False
            assert len(result.results) == 1  # Only primary action executed
            assert result.error_message is not None
    
    @pytest.mark.asyncio
    async def test_execute_chain_continue_on_failure(self, action_chain_executor):
        """Test that chain continues on failure when configured."""
        # Create chain with CONTINUE_ON_FAILURE mode
        chain = ChainedAction(
            primary_action=ChainedIntent(
                action_type="weight_log_create",
                parameters={"weight_value": 185, "unit": "lb"}
            ),
            follow_up_actions=[
                ChainedIntent(
                    action_type="side_effect_report_create",
                    parameters={"symptoms": "nausea", "severity": "Mild"}
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            failure_mode=FailureMode.CONTINUE_ON_FAILURE
        )
        
        # Mock responses: first fails, second succeeds
        responses = [
            ActionResponse(
                success=False,
                action_type="weight_log_create",
                message="",
                error_message="Invalid weight value"
            ),
            ActionResponse(
                success=True,
                action_type="side_effect_report_create",
                message="Side effect reported",
                data={"report_id": "789"}
            )
        ]
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            side_effect=responses
        ):
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="patient",
                chain=chain
            )
            
            assert result.success is False  # Overall failure due to first action
            assert len(result.results) == 2  # Both actions executed
            assert result.results[0].success is False
            assert result.results[1].success is True
    
    @pytest.mark.asyncio
    async def test_execute_chain_parallel_mode(self, action_chain_executor):
        """Test parallel execution mode."""
        # Create chain with parallel execution
        chain = ChainedAction(
            primary_action=ChainedIntent(
                action_type="appointment_create",
                parameters={"scheduled_time": "2025-04-21T14:00:00+00:00"}
            ),
            follow_up_actions=[
                ChainedIntent(
                    action_type="notification_create",
                    parameters={"message": "Reminder 1"}
                ),
                ChainedIntent(
                    action_type="notification_create",
                    parameters={"message": "Reminder 2"}
                )
            ],
            execution_mode=ExecutionMode.PARALLEL
        )
        
        # Mock responses
        responses = [
            ActionResponse(success=True, action_type="appointment_create", message="Created"),
            ActionResponse(success=True, action_type="notification_create", message="Sent 1"),
            ActionResponse(success=True, action_type="notification_create", message="Sent 2")
        ]
        
        call_count = 0
        async def mock_execute(*args, **kwargs):
            nonlocal call_count
            result = responses[call_count]
            call_count += 1
            return result
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            side_effect=mock_execute
        ):
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="clinician",
                chain=chain
            )
            
            assert result.success is True
            assert len(result.results) == 3
            # In parallel mode, follow-ups should be executed concurrently
            assert result.execution_time_ms is not None
    
    @pytest.mark.asyncio
    async def test_validate_chain(self, action_chain_executor):
        """Test chain validation."""
        # Valid chain
        valid_chain = ChainedAction(
            primary_action=ChainedIntent(
                action_type="appointment_create",
                parameters={}
            )
        )
        
        validation = action_chain_executor._validate_chain(valid_chain)
        assert validation.is_valid is True
        assert len(validation.errors) == 0
        
        # Chain with circular dependency warning
        chain_with_deps = ChainedAction(
            primary_action=ChainedIntent(action_type="action1", parameters={}),
            follow_up_actions=[
                ChainedIntent(
                    action_type="action2",
                    parameters={},
                    dependencies=[ActionDependency(required_action="action3")]
                )
            ]
        )
        
        validation = action_chain_executor._validate_chain(chain_with_deps)
        assert validation.is_valid is True  # Still valid, just has warnings
        assert len(validation.warnings) > 0
    
    @pytest.mark.asyncio
    async def test_context_updates(self, action_chain_executor, sample_chain):
        """Test that context is properly updated throughout execution."""
        # Mock responses with data
        responses = [
            ActionResponse(
                success=True,
                action_type="appointment_create",
                message="Created",
                data={"appointment_id": "123", "patient_name": "John Doe"}
            ),
            ActionResponse(
                success=True,
                action_type="notification_create",
                message="Sent",
                data={"notification_id": "456"}
            )
        ]
        
        with patch.object(
            action_chain_executor.action_executor,
            'execute_action',
            new_callable=AsyncMock,
            side_effect=responses
        ):
            initial_context = {"clinic_id": "clinic123"}
            result = await action_chain_executor.execute_chain(
                user_id="test_user",
                user_role="clinician",
                chain=sample_chain,
                initial_context=initial_context
            )
            
            # Check final context contains all data
            assert result.context.data["clinic_id"] == "clinic123"
            assert "appointment_create" in result.context.data
            assert result.context.data["appointment_create"]["appointment_id"] == "123"
            assert "notification_create" in result.context.data
    
    def test_apply_context_substitutions(self, action_chain_executor):
        """Test context substitution logic."""
        context = ChainContext(data={
            "patient_id": "patient123",
            "appointment_create": {
                "appointment_id": "apt456",
                "scheduled_time": "2025-04-21T14:00:00+00:00"
            }
        })
        
        parameters = {
            "recipient": "{{patient_id}}",
            "appointment_ref": "{{appointment_id}}",
            "message": "Your appointment is at {{scheduled_time}}",
            "static_value": "unchanged"
        }
        
        result = action_chain_executor._apply_context_substitutions(parameters, context)
        
        assert result["recipient"] == "patient123"
        assert result["appointment_ref"] == "apt456"
        assert result["message"] == "Your appointment is at 2025-04-21T14:00:00+00:00"
        assert result["static_value"] == "unchanged"
    
    def test_check_dependencies(self, action_chain_executor):
        """Test dependency checking logic."""
        # Create some mock results
        completed_results = [
            ActionResult(
                action_type="appointment_create",
                success=True,
                executed_at=datetime.utcnow()
            ),
            ActionResult(
                action_type="weight_log_create",
                success=False,
                executed_at=datetime.utcnow()
            )
        ]
        
        context = ChainContext(data={"patient_id": "123"})
        
        # Action with met dependencies
        action_with_met_deps = ChainedIntent(
            action_type="notification_create",
            parameters={},
            dependencies=[
                ActionDependency(
                    required_action="appointment_create",
                    require_success=True
                )
            ]
        )
        
        assert action_chain_executor._check_dependencies(
            action_with_met_deps, completed_results, context
        ) is True
        
        # Action with unmet dependencies (requires success but action failed)
        action_with_unmet_deps = ChainedIntent(
            action_type="follow_up",
            parameters={},
            dependencies=[
                ActionDependency(
                    required_action="weight_log_create",
                    require_success=True
                )
            ]
        )
        
        assert action_chain_executor._check_dependencies(
            action_with_unmet_deps, completed_results, context
        ) is False
        
        # Action with context requirements
        action_with_context_req = ChainedIntent(
            action_type="reminder",
            parameters={},
            context_requirements=["patient_id", "appointment_id"]
        )
        
        # Missing appointment_id in context
        assert action_chain_executor._check_dependencies(
            action_with_context_req, completed_results, context
        ) is False