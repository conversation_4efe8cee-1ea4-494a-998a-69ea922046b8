"""
Unit tests for Action Patterns
"""

import pytest
from datetime import datetime, timedelta
import re

from app.services.action_patterns import ActionPatterns, PatternDetector
from app.schemas.action_chain_v2 import ExecutionMode, FailureMode


class TestPatternDetector:
    """Test cases for PatternDetector."""
    
    def test_detect_appointment_with_reminder(self):
        """Test detection of appointment with reminder patterns."""
        test_cases = [
            "Schedule an appointment tomorrow at 2pm and remind me the day before",
            "Book appointment with reminder",
            "Schedule appointment and notify me",
            "appointment and remind me please"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "appointment_with_reminder", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_side_effect_with_followup(self):
        """Test detection of side effect with follow-up patterns."""
        test_cases = [
            "I'm experiencing nausea from the medication and need to schedule a follow-up",
            "Report side effect and schedule appointment",
            "Having bad side effects and need help",
            "Experiencing severe symptoms and need to see doctor"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "side_effect_with_followup", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_medication_request_with_education(self):
        """Test detection of medication request with education patterns."""
        test_cases = [
            "Request Ozempic prescription and send me information about it",
            "Prescribe metformin and educate me",
            "New prescription and guide please",
            "Request medication and send me the information"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "medication_request_with_education", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_weight_log_with_milestone(self):
        """Test detection of weight log with milestone patterns."""
        test_cases = [
            "Log my weight and check milestones",
            "Track weight and check progress",
            "Log my weight as 185 and check if I hit any milestones",
            "Weight progress check please"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "weight_log_with_milestone", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_batch_notifications(self):
        """Test detection of batch notification patterns."""
        test_cases = [
            "Send notification to all patients",
            "Notify all clinicians about the update",
            "Send message to everyone",
            "Broadcast announcement to all patients"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "batch_notifications", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_batch_weight_logs(self):
        """Test detection of batch weight log patterns."""
        test_cases = [
            "Log my weight: Monday 185, Tuesday 184, Wednesday 183",
            "Track weight for monday 185 and tuesday 184",
            "Record weight entries for the past 3 days: 185, 184, 183",
            "Weight logs: Monday 185 lbs, Wednesday 183 lbs"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "batch_weight_logs", f"Failed to detect pattern in: {input_text}"
    
    def test_detect_conditional_weight_appointment(self):
        """Test detection of conditional weight appointment patterns."""
        test_cases = [
            "If my weight is over 200, schedule a nutrition consultation",
            "Log weight and if it exceeds 190, book appointment",
            "If weight above threshold 200, schedule consult",
            "Weight check and if over limit, book nutritionist"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected == "conditional_weight_appointment", f"Failed to detect pattern in: {input_text}"
    
    def test_no_pattern_detected(self):
        """Test cases where no pattern should be detected."""
        test_cases = [
            "Just schedule an appointment",
            "Log my weight",
            "Report side effects",
            "Send a notification"
        ]
        
        for input_text in test_cases:
            detected = PatternDetector.detect_pattern(input_text)
            assert detected is None, f"Unexpected pattern detected in: {input_text}"


class TestActionPatterns:
    """Test cases for ActionPatterns."""
    
    def test_appointment_with_reminder(self):
        """Test appointment with reminder pattern creation."""
        scheduled_time = "2025-04-21T14:00:00+00:00"
        pattern = ActionPatterns.appointment_with_reminder(
            patient_id="patient_123",
            scheduled_time=scheduled_time,
            duration_minutes=45,
            appointment_type="Initial",
            reminder_days_before=2,
            additional_notes="First consultation"
        )
        
        assert pattern.primary_action.action_type == "appointment_create"
        assert pattern.primary_action.parameters["patient_id"] == "patient_123"
        assert pattern.primary_action.parameters["duration_minutes"] == 45
        assert pattern.primary_action.parameters["appointment_type"] == "Initial"
        
        assert len(pattern.follow_up_actions) == 1
        reminder = pattern.follow_up_actions[0]
        assert reminder.action_type == "notification_create"
        assert reminder.parameters["type"] == "appointment_reminder"
        assert "{{appointment_id}}" in reminder.parameters["message"]
        
        assert pattern.execution_mode == ExecutionMode.SEQUENTIAL
        assert pattern.failure_mode == FailureMode.STOP_ON_FAILURE
    
    def test_side_effect_with_followup_severe(self):
        """Test side effect with follow-up pattern for severe symptoms."""
        pattern = ActionPatterns.side_effect_with_followup(
            patient_id="patient_123",
            medication_name="Ozempic",
            symptoms="severe nausea and vomiting",
            severity="Severe",
            onset_time="2 hours ago",
            followup_days=1
        )
        
        assert pattern.primary_action.action_type == "side_effect_report_create"
        assert pattern.primary_action.parameters["severity"] == "Severe"
        
        # Should have 2 follow-up actions for severe: notification + appointment
        assert len(pattern.follow_up_actions) == 2
        
        notification = pattern.follow_up_actions[0]
        assert notification.action_type == "notification_create"
        assert notification.parameters["priority"] == "high"
        
        appointment = pattern.follow_up_actions[1]
        assert appointment.action_type == "appointment_request_create"
        assert "severe side effects" in appointment.parameters["reason"]
    
    def test_side_effect_with_followup_mild(self):
        """Test side effect with follow-up pattern for mild symptoms."""
        pattern = ActionPatterns.side_effect_with_followup(
            patient_id="patient_123",
            medication_name="Metformin",
            symptoms="mild stomach upset",
            severity="Mild",
            onset_time="yesterday"
        )
        
        assert pattern.primary_action.action_type == "side_effect_report_create"
        assert pattern.primary_action.parameters["severity"] == "Mild"
        
        # Should have only 1 follow-up action for mild: notification only
        assert len(pattern.follow_up_actions) == 1
        assert pattern.follow_up_actions[0].action_type == "notification_create"
        assert pattern.follow_up_actions[0].parameters["priority"] == "medium"
    
    def test_medication_request_with_education(self):
        """Test medication request with education pattern."""
        pattern = ActionPatterns.medication_request_with_education(
            patient_id="patient_123",
            medication_name="Wegovy",
            dosage="0.25mg",
            frequency="weekly",
            duration="3 months",
            notes="Starting dose"
        )
        
        assert pattern.primary_action.action_type == "medication_request_create"
        assert pattern.primary_action.parameters["medication_name"] == "Wegovy"
        
        assert len(pattern.follow_up_actions) == 1
        education = pattern.follow_up_actions[0]
        assert education.action_type == "education_assignment_create"
        assert "wegovy" in education.parameters["material_tags"]
        assert "medication-guide" in education.parameters["material_tags"]
    
    def test_weight_log_with_milestone_check(self):
        """Test weight log with milestone check pattern."""
        pattern = ActionPatterns.weight_log_with_milestone_check(
            weight_value=175.5,
            unit="lb",
            date="2025-04-21",
            notes="Morning weight",
            milestone_threshold=10.0
        )
        
        assert pattern.primary_action.action_type == "weight_log_create"
        assert pattern.primary_action.parameters["weight_value"] == 175.5
        
        # Should have milestone check and conditional notification
        assert len(pattern.follow_up_actions) == 2
        
        milestone_check = pattern.follow_up_actions[0]
        assert milestone_check.action_type == "milestone_check"
        assert milestone_check.parameters["threshold"] == 10.0
        
        notification = pattern.follow_up_actions[1]
        assert notification.action_type == "notification_create"
        assert notification.parameters["type"] == "achievement"
    
    def test_batch_notifications(self):
        """Test batch notifications pattern."""
        recipients = ["patient_1", "patient_2", "patient_3"]
        pattern = ActionPatterns.batch_notifications(
            recipient_ids=recipients,
            notification_type="announcement",
            title="Clinic Holiday Hours",
            message="The clinic will be closed on Monday",
            priority="high",
            metadata={"category": "schedule"}
        )
        
        # Total actions should be number of recipients
        assert 1 + len(pattern.follow_up_actions) == len(recipients)
        assert pattern.execution_mode == ExecutionMode.PARALLEL
        assert pattern.failure_mode == FailureMode.CONTINUE_ON_FAILURE
        
        # Check all recipients are covered
        all_recipients = [pattern.primary_action.parameters["recipient_id"]]
        all_recipients.extend([
            action.parameters["recipient_id"] 
            for action in pattern.follow_up_actions
        ])
        assert set(all_recipients) == set(recipients)
    
    def test_comprehensive_check_in(self):
        """Test comprehensive check-in pattern."""
        pattern = ActionPatterns.comprehensive_check_in(
            patient_id="patient_123",
            include_weight=True,
            include_side_effects=True,
            include_appointment=False
        )
        
        # Should have 2 actions (weight + side effects)
        total_actions = 1 + len(pattern.follow_up_actions)
        assert total_actions == 2
        assert pattern.execution_mode == ExecutionMode.PARALLEL
        
        # Check action types
        action_types = [pattern.primary_action.action_type]
        action_types.extend([a.action_type for a in pattern.follow_up_actions])
        assert all(t == "prompt_create" for t in action_types)
    
    def test_batch_weight_logs(self):
        """Test batch weight logs pattern."""
        entries = [
            ("2025-04-19", 185.0, "lb"),
            ("2025-04-20", 184.5, "lb"),
            ("2025-04-21", 183.8, "lb")
        ]
        
        pattern = ActionPatterns.batch_weight_logs(entries)
        
        total_actions = 1 + len(pattern.follow_up_actions)
        assert total_actions == len(entries)
        assert pattern.execution_mode == ExecutionMode.SEQUENTIAL
        
        # Check all dates are covered
        all_dates = [pattern.primary_action.parameters["date"]]
        all_dates.extend([
            action.parameters["date"] 
            for action in pattern.follow_up_actions
        ])
        assert all_dates == [e[0] for e in entries]
    
    def test_conditional_weight_appointment(self):
        """Test conditional weight appointment pattern."""
        pattern = ActionPatterns.conditional_weight_appointment(
            weight_value=205.0,
            unit="lb",
            threshold=200.0,
            date="2025-04-21"
        )
        
        assert pattern.primary_action.action_type == "weight_log_create"
        assert pattern.primary_action.parameters["weight_value"] == 205.0
        
        assert len(pattern.follow_up_actions) == 1
        appointment = pattern.follow_up_actions[0]
        assert appointment.action_type == "appointment_request_create"
        assert "weight management consultation" in appointment.parameters["reason"].lower()
        assert "205" in appointment.parameters["reason"]
        assert "200" in appointment.parameters["reason"]
        
        # Check dependencies
        assert len(appointment.dependencies) == 1
        assert appointment.dependencies[0].required_action == "weight_log_create"
        assert appointment.dependencies[0].require_success is True
    
    def test_pattern_with_invalid_severity(self):
        """Test that patterns handle edge cases gracefully."""
        # Test with moderate severity (should still create appointment)
        pattern = ActionPatterns.side_effect_with_followup(
            patient_id="patient_123",
            medication_name="Test Drug",
            symptoms="moderate symptoms",
            severity="Moderate",
            onset_time="today"
        )
        
        # Moderate should also trigger appointment
        assert len(pattern.follow_up_actions) == 2
        notification = pattern.follow_up_actions[0]
        assert notification.parameters["priority"] == "medium"