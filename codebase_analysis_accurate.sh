#!/bin/bash

# Accurate codebase analysis for PulseTrack
cd /Users/<USER>/Documents/projects/pulsetrack

echo "======================================"
echo "PulseTrack Codebase Analysis Report"
echo "Date: $(date)"
echo "======================================"
echo

# Function to count files and lines
count_stats() {
    local path="$1"
    local pattern="$2"
    local desc="$3"
    
    if [ ! -d "$path" ]; then
        echo "$desc: Directory not found"
        return
    fi
    
    # Count files
    local file_count=$(find "$path" -name "$pattern" -not -path "*/__pycache__/*" -not -path "*/node_modules/*" -not -path "*/.venv/*" -not -path "*/venv/*" -not -path "*/dist/*" -not -path "*/build/*" -type f 2>/dev/null | wc -l | tr -d ' ')
    
    # Count lines - handle empty results
    local line_count=0
    if [ "$file_count" -gt 0 ]; then
        line_count=$(find "$path" -name "$pattern" -not -path "*/__pycache__/*" -not -path "*/node_modules/*" -not -path "*/.venv/*" -not -path "*/venv/*" -not -path "*/dist/*" -not -path "*/build/*" -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    fi
    
    printf "%-35s %6d files, %8d lines\n" "$desc:" "$file_count" "$line_count"
}

# Backend Analysis
echo "BACKEND (Python):"
echo "=================="
count_stats "backend" "*.py" "Total Python files"
echo
echo "Breakdown by directory:"
count_stats "backend/app" "*.py" "  app/ (core application)"
count_stats "backend/alembic" "*.py" "  alembic/ (migrations)"
count_stats "backend/tests" "*.py" "  tests/"
count_stats "backend/scripts" "*.py" "  scripts/"
echo

# Frontend Analysis
echo "FRONTEND (TypeScript/JavaScript):"
echo "================================="
# Patient app
if [ -d "frontend-patient" ]; then
    ts_count=$(find frontend-patient/src \( -name "*.ts" -o -name "*.tsx" \) -type f 2>/dev/null | wc -l | tr -d ' ')
    ts_lines=$(find frontend-patient/src \( -name "*.ts" -o -name "*.tsx" \) -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    printf "%-35s %6d files, %8d lines\n" "frontend-patient (TS/TSX):" "$ts_count" "$ts_lines"
fi

# Clinician app
if [ -d "frontend-clinician" ]; then
    ts_count=$(find frontend-clinician/src \( -name "*.ts" -o -name "*.tsx" \) -type f 2>/dev/null | wc -l | tr -d ' ')
    ts_lines=$(find frontend-clinician/src \( -name "*.ts" -o -name "*.tsx" \) -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    printf "%-35s %6d files, %8d lines\n" "frontend-clinician (TS/TSX):" "$ts_count" "$ts_lines"
fi

# Admin app
if [ -d "frontend-admin" ]; then
    ts_count=$(find frontend-admin/src \( -name "*.ts" -o -name "*.tsx" \) -type f 2>/dev/null | wc -l | tr -d ' ')
    ts_lines=$(find frontend-admin/src \( -name "*.ts" -o -name "*.tsx" \) -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
    printf "%-35s %6d files, %8d lines\n" "frontend-admin (TS/TSX):" "$ts_count" "$ts_lines"
fi
echo

# Documentation
echo "DOCUMENTATION:"
echo "=============="
count_stats "." "*.md" "Total Markdown files"
count_stats "docs" "*.md" "  docs/"
count_stats "knowledge" "*.md" "  knowledge/"
echo

# Configuration
echo "CONFIGURATION:"
echo "=============="
count_stats "." "*.json" "JSON files"
count_stats "." "*.yaml" "YAML files"
count_stats "." "*.yml" "YML files"
count_stats "." "docker-compose.yml" "Docker Compose"
count_stats "." "Dockerfile*" "Dockerfiles"
echo

# Other files
echo "OTHER FILES:"
echo "============"
count_stats "." "*.css" "CSS files"
count_stats "." "*.sh" "Shell scripts"
count_stats "." "*.sql" "SQL files"
count_stats "backend/education_materials" "*.tex" "LaTeX files"
echo

# Summary
echo "======================================"
echo "SUMMARY BY LANGUAGE:"
echo "======================================"

# Calculate totals
backend_total=$(find backend -name "*.py" -not -path "*/__pycache__/*" -not -path "*/.venv/*" -not -path "*/venv/*" -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
frontend_total=$(find frontend-*/src \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
docs_total=$(find . -name "*.md" -not -path "*/node_modules/*" -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
config_total=$(find . \( -name "*.json" -o -name "*.yaml" -o -name "*.yml" \) -not -path "*/node_modules/*" -not -path "*/packages/*" -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
other_total=$(find . \( -name "*.css" -o -name "*.sh" -o -name "*.sql" -o -name "Dockerfile*" \) -not -path "*/node_modules/*" -type f -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')

grand_total=$((backend_total + frontend_total + docs_total + config_total + other_total))

echo "Backend (Python):          $backend_total lines"
echo "Frontend (TS/TSX/JS/JSX):  $frontend_total lines"
echo "Documentation (Markdown):  $docs_total lines"
echo "Configuration:             $config_total lines"
echo "Other (CSS/SQL/Shell):     $other_total lines"
echo "--------------------------------------"
echo "GRAND TOTAL:               $grand_total lines"
echo

# Calculate percentages
if [ $grand_total -gt 0 ]; then
    echo "Percentage Breakdown:"
    echo "--------------------"
    backend_pct=$(awk "BEGIN {printf \"%.1f\", $backend_total * 100 / $grand_total}")
    frontend_pct=$(awk "BEGIN {printf \"%.1f\", $frontend_total * 100 / $grand_total}")
    docs_pct=$(awk "BEGIN {printf \"%.1f\", $docs_total * 100 / $grand_total}")
    config_pct=$(awk "BEGIN {printf \"%.1f\", $config_total * 100 / $grand_total}")
    other_pct=$(awk "BEGIN {printf \"%.1f\", $other_total * 100 / $grand_total}")
    
    echo "Backend:        ${backend_pct}%"
    echo "Frontend:       ${frontend_pct}%"
    echo "Documentation:  ${docs_pct}%"
    echo "Configuration:  ${config_pct}%"
    echo "Other:          ${other_pct}%"
fi

echo
echo "======================================"
echo "CODE METRICS:"
echo "======================================"
echo "Total production code (Backend + Frontend): $((backend_total + frontend_total)) lines"
echo "Code-to-documentation ratio: $(awk "BEGIN {printf \"%.2f:1\", ($backend_total + $frontend_total) / $docs_total}")"
echo "Average lines per Python file: $(awk "BEGIN {printf \"%.0f\", $backend_total / 354}")"
echo "Average lines per TypeScript file: $(awk "BEGIN {printf \"%.0f\", $frontend_total / 216}")"