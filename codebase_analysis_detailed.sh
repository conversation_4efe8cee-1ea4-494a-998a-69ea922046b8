#!/bin/bash

# Detailed analysis with percentages and better formatting
PROJECT_ROOT="/Users/<USER>/Documents/projects/pulsetrack"
cd "$PROJECT_ROOT"

echo "=== Detailed PulseTrack Codebase Analysis ==="
echo "Date: $(date)"
echo "============================================="
echo

# Arrays to store data for percentage calculation
declare -A file_counts
declare -A line_counts
declare -a categories

# Function to count and store data
analyze_files() {
    local pattern="$1"
    local category="$2"
    
    local files=$(find . \
        -name "$pattern" \
        -not -path "*/node_modules/*" \
        -not -path "*/.git/*" \
        -not -path "*/__pycache__/*" \
        -not -path "*/dist/*" \
        -not -path "*/build/*" \
        -not -path "*/packages/*" \
        -not -path "*/.next/*" \
        -not -path "*/coverage/*" \
        -not -path "*/.cache/*" \
        -not -path "*/.venv/*" \
        -not -path "*/venv/*" \
        -not -path "*/env/*" \
        -not -path "*/.env/*" \
        -not -path "*/.pytest_cache/*" \
        -not -path "*/.mypy_cache/*" \
        -not -path "*/.ruff_cache/*" \
        -type f)
    
    if [ -z "$files" ]; then
        file_counts["$category"]=0
        line_counts["$category"]=0
        return
    fi
    
    local file_count=$(echo "$files" | wc -l | tr -d ' ')
    local line_count=$(echo "$files" | xargs wc -l 2>/dev/null | tail -n 1 | awk '{print $1}')
    
    if [ -z "$line_count" ]; then
        line_count=0
    fi
    
    file_counts["$category"]=$file_count
    line_counts["$category"]=$line_count
    categories+=("$category")
}

# Analyze all file types
analyze_files "*.py" "Python"
analyze_files "*.ts" "TypeScript"
analyze_files "*.tsx" "TypeScript React"
analyze_files "*.js" "JavaScript"
analyze_files "*.jsx" "JavaScript React"
analyze_files "*.css" "CSS"
analyze_files "*.json" "JSON"
analyze_files "*.yaml" "YAML"
analyze_files "*.yml" "YML"
analyze_files "*.md" "Markdown"
analyze_files "*.toml" "TOML"
analyze_files "Dockerfile*" "Dockerfile"
analyze_files "*.sh" "Shell Script"
analyze_files "*.sql" "SQL"
analyze_files "*.tex" "LaTeX"

# Calculate totals
total_files=0
total_lines=0
for cat in "${categories[@]}"; do
    total_files=$((total_files + ${file_counts[$cat]}))
    total_lines=$((total_lines + ${line_counts[$cat]}))
done

# Display results with percentages
echo "File Type Analysis:"
echo "==================="
printf "%-20s %10s %12s %10s %12s\n" "Type" "Files" "%" "Lines" "%"
printf "%-20s %10s %12s %10s %12s\n" "----" "-----" "-" "-----" "-"

# Sort by lines of code (descending)
for cat in "${categories[@]}"; do
    if [ ${line_counts[$cat]} -gt 0 ]; then
        file_pct=$(awk "BEGIN {printf \"%.1f\", ${file_counts[$cat]} * 100 / $total_files}")
        line_pct=$(awk "BEGIN {printf \"%.1f\", ${line_counts[$cat]} * 100 / $total_lines}")
        printf "%-20s %10d %11s%% %10d %11s%%\n" \
            "$cat" \
            "${file_counts[$cat]}" \
            "$file_pct" \
            "${line_counts[$cat]}" \
            "$line_pct"
    fi
done | sort -k4 -nr

echo
printf "%-20s %10d %12s %10d %12s\n" "TOTAL" "$total_files" "100.0%" "$total_lines" "100.0%"

echo
echo "Language Category Summary:"
echo "=========================="

# Calculate language categories
python_lines=${line_counts["Python"]}
typescript_lines=$((${line_counts["TypeScript"]} + ${line_counts["TypeScript React"]}))
javascript_lines=$((${line_counts["JavaScript"]} + ${line_counts["JavaScript React"]}))
frontend_total=$((typescript_lines + javascript_lines))
config_lines=$((${line_counts["JSON"]} + ${line_counts["YAML"]} + ${line_counts["YML"]} + ${line_counts["TOML"]}))
docs_lines=${line_counts["Markdown"]}
other_lines=$((${line_counts["CSS"]} + ${line_counts["Dockerfile"]} + ${line_counts["Shell Script"]} + ${line_counts["SQL"]} + ${line_counts["LaTeX"]}))

echo "Backend (Python):              $python_lines lines ($(awk "BEGIN {printf \"%.1f\", $python_lines * 100 / $total_lines}")%)"
echo "Frontend (TS/JS):              $frontend_total lines ($(awk "BEGIN {printf \"%.1f\", $frontend_total * 100 / $total_lines}")%)"
echo "  - TypeScript:                $typescript_lines lines"
echo "  - JavaScript:                $javascript_lines lines"
echo "Configuration:                 $config_lines lines ($(awk "BEGIN {printf \"%.1f\", $config_lines * 100 / $total_lines}")%)"
echo "Documentation:                 $docs_lines lines ($(awk "BEGIN {printf \"%.1f\", $docs_lines * 100 / $total_lines}")%)"
echo "Other (CSS/Docker/SQL/etc):    $other_lines lines ($(awk "BEGIN {printf \"%.1f\", $other_lines * 100 / $total_lines}")%)"

echo
echo "Code vs Non-Code Breakdown:"
echo "==========================="
code_lines=$((python_lines + frontend_total + ${line_counts["CSS"]} + ${line_counts["SQL"]}))
non_code_lines=$((total_lines - code_lines))
echo "Code (Python/TS/JS/CSS/SQL):   $code_lines lines ($(awk "BEGIN {printf \"%.1f\", $code_lines * 100 / $total_lines}")%)"
echo "Non-Code (Config/Docs/etc):    $non_code_lines lines ($(awk "BEGIN {printf \"%.1f\", $non_code_lines * 100 / $total_lines}")%)"

echo
echo "Backend Structure (backend/):"
echo "============================="
# Analyze backend subdirectories
for dir in app alembic tests scripts; do
    if [ -d "backend/$dir" ]; then
        dir_lines=$(find "backend/$dir" -name "*.py" -not -path "*/__pycache__/*" -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
        if [ -n "$dir_lines" ] && [ "$dir_lines" -gt 0 ]; then
            printf "  %-20s %8d lines\n" "$dir/:" "$dir_lines"
        fi
    fi
done

echo
echo "Frontend Apps Comparison:"
echo "========================="
echo "  App                    Files       Lines"
echo "  ---                    -----       -----"
for frontend in frontend-patient frontend-clinician frontend-admin; do
    if [ -d "$frontend" ]; then
        app_files=$(find "$frontend/src" \
            -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) \
            -not -path "*/node_modules/*" \
            2>/dev/null | wc -l | tr -d ' ')
        app_lines=$(find "$frontend/src" \
            -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) \
            -not -path "*/node_modules/*" \
            -exec wc -l {} + 2>/dev/null | tail -n 1 | awk '{print $1}')
        printf "  %-22s %5d       %5d\n" "${frontend#frontend-}:" "$app_files" "$app_lines"
    fi
done