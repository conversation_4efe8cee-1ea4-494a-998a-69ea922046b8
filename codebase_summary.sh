#!/bin/bash

# Simple codebase analysis for PulseTrack
cd /Users/<USER>/Documents/projects/pulsetrack

echo "======================================"
echo "PulseTrack Codebase Summary"
echo "Date: $(date)"
echo "======================================"
echo

# Function to safely count lines
count_files() {
    local pattern="$1"
    local desc="$2"
    local exclude_args="-not -path '*/node_modules/*' -not -path '*/.git/*' -not -path '*/__pycache__/*' -not -path '*/dist/*' -not -path '*/build/*' -not -path '*/packages/*'"
    
    local count=$(find . -name "$pattern" $exclude_args -type f 2>/dev/null | wc -l | tr -d ' ')
    local lines=$(find . -name "$pattern" $exclude_args -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
    
    if [ "$count" -gt 0 ]; then
        printf "%-30s %6d files %10d lines\n" "$desc" "$count" "$lines"
    fi
}

echo "BACKEND CODE:"
echo "-------------"
# Python files in backend
backend_py_files=$(find backend -name "*.py" -not -path "*/__pycache__/*" -type f 2>/dev/null | wc -l | tr -d ' ')
backend_py_lines=$(find backend -name "*.py" -not -path "*/__pycache__/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
printf "%-30s %6d files %10d lines\n" "Python (backend/):" "$backend_py_files" "$backend_py_lines"

# Backend subdirectories
echo "  Breakdown:"
for dir in app alembic tests scripts; do
    if [ -d "backend/$dir" ]; then
        lines=$(find backend/$dir -name "*.py" -not -path "*/__pycache__/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
        printf "    %-26s %10d lines\n" "$dir/:" "$lines"
    fi
done

echo
echo "FRONTEND CODE:"
echo "--------------"
# Frontend apps
for app in patient clinician admin; do
    if [ -d "frontend-$app" ]; then
        ts_files=$(find frontend-$app/src -name "*.ts" -o -name "*.tsx" -type f 2>/dev/null | wc -l | tr -d ' ')
        ts_lines=$(find frontend-$app/src \( -name "*.ts" -o -name "*.tsx" \) -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
        js_files=$(find frontend-$app/src -name "*.js" -o -name "*.jsx" -type f 2>/dev/null | wc -l | tr -d ' ')
        js_lines=$(find frontend-$app/src \( -name "*.js" -o -name "*.jsx" \) -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
        total_files=$((ts_files + js_files))
        total_lines=$((ts_lines + js_lines))
        printf "%-30s %6d files %10d lines\n" "frontend-$app:" "$total_files" "$total_lines"
    fi
done

echo
echo "CONFIGURATION FILES:"
echo "--------------------"
# Config files
json_files=$(find . -name "*.json" -not -path "*/node_modules/*" -not -path "*/packages/*" -type f 2>/dev/null | wc -l | tr -d ' ')
json_lines=$(find . -name "*.json" -not -path "*/node_modules/*" -not -path "*/packages/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
yaml_files=$(find . \( -name "*.yaml" -o -name "*.yml" \) -not -path "*/node_modules/*" -type f 2>/dev/null | wc -l | tr -d ' ')
yaml_lines=$(find . \( -name "*.yaml" -o -name "*.yml" \) -not -path "*/node_modules/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')

printf "%-30s %6d files %10d lines\n" "JSON:" "$json_files" "$json_lines"
printf "%-30s %6d files %10d lines\n" "YAML:" "$yaml_files" "$yaml_lines"

echo
echo "DOCUMENTATION:"
echo "--------------"
# Documentation
md_files=$(find . -name "*.md" -not -path "*/node_modules/*" -not -path "*/packages/*" -type f 2>/dev/null | wc -l | tr -d ' ')
md_lines=$(find . -name "*.md" -not -path "*/node_modules/*" -not -path "*/packages/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
printf "%-30s %6d files %10d lines\n" "Markdown:" "$md_files" "$md_lines"

# Docs breakdown
docs_lines=$(find docs -name "*.md" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
knowledge_lines=$(find knowledge -name "*.md" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
printf "  %-28s %10d lines\n" "docs/:" "$docs_lines"
printf "  %-28s %10d lines\n" "knowledge/:" "$knowledge_lines"

echo
echo "OTHER FILES:"
echo "------------"
# Other important files
css_files=$(find . -name "*.css" -not -path "*/node_modules/*" -type f 2>/dev/null | wc -l | tr -d ' ')
css_lines=$(find . -name "*.css" -not -path "*/node_modules/*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
docker_files=$(find . -name "Dockerfile*" -type f 2>/dev/null | wc -l | tr -d ' ')
docker_lines=$(find . -name "Dockerfile*" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
sh_files=$(find . -name "*.sh" -type f 2>/dev/null | wc -l | tr -d ' ')
sh_lines=$(find . -name "*.sh" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
sql_files=$(find . -name "*.sql" -type f 2>/dev/null | wc -l | tr -d ' ')
sql_lines=$(find . -name "*.sql" -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')

printf "%-30s %6d files %10d lines\n" "CSS:" "$css_files" "$css_lines"
printf "%-30s %6d files %10d lines\n" "Dockerfiles:" "$docker_files" "$docker_lines"
printf "%-30s %6d files %10d lines\n" "Shell scripts:" "$sh_files" "$sh_lines"
printf "%-30s %6d files %10d lines\n" "SQL:" "$sql_files" "$sql_lines"

echo
echo "======================================"
echo "SUMMARY:"
echo "======================================"

# Calculate totals
total_backend=$backend_py_lines
total_frontend=$(find frontend-*/src \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) -type f -exec cat {} \; 2>/dev/null | wc -l | tr -d ' ')
total_config=$((json_lines + yaml_lines))
total_docs=$md_lines
total_other=$((css_lines + docker_lines + sh_lines + sql_lines))
grand_total=$((total_backend + total_frontend + total_config + total_docs + total_other))

echo "Backend (Python):         $total_backend lines"
echo "Frontend (TS/TSX/JS/JSX): $total_frontend lines"
echo "Configuration:            $total_config lines"
echo "Documentation:            $total_docs lines"
echo "Other:                    $total_other lines"
echo "--------------------------------------"
echo "GRAND TOTAL:              $grand_total lines"

echo
echo "Percentage Breakdown:"
echo "--------------------"
# Calculate percentages manually
backend_pct=$((total_backend * 100 / grand_total))
frontend_pct=$((total_frontend * 100 / grand_total))
config_pct=$((total_config * 100 / grand_total))
docs_pct=$((total_docs * 100 / grand_total))
other_pct=$((total_other * 100 / grand_total))

echo "Backend:      ${backend_pct}%"
echo "Frontend:     ${frontend_pct}%"
echo "Configuration: ${config_pct}%"
echo "Documentation: ${docs_pct}%"
echo "Other:         ${other_pct}%"