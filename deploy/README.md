# PulseTrack Production Deployment Guide

This guide covers deploying PulseTrack to the EDINBURGH server with public domain access.

## 🏗️ Architecture Overview

**Services:**
- **Backend API**: FastAPI with PostgreSQL + Redis
- **Frontend Apps**: 3 React apps (Clinician, Patient, Admin)
- **Reverse Proxy**: <PERSON><PERSON><PERSON><PERSON> with automatic Let's Encrypt SSL
- **Database**: PostgreSQL with pgvector extension
- **Cache**: Redis

**Domains:**
- `pulsetrack-api.synapsedx.co.uk` - Backend API
- `pulsetrack-clinician.synapsedx.co.uk` - Clinician interface
- `pulsetrack-patient.synapsedx.co.uk` - Patient interface  
- `pulsetrack-admin.synapsedx.co.uk` - Admin interface
- `pulsetrack-traefik.synapsedx.co.uk` - Traefik dashboard

## 🚀 Quick Deployment

### Prerequisites
- Docker & Docker Compose installed
- DNS records pointing to your server
- Ports 80, 443, 8080 open

### Steps

1. **<PERSON>lone and switch to deployment branch:**
```bash
git clone <repository-url>
cd pulsetrack
git checkout deployment/edinburgh-demo
```

2. **Configure environment variables:**
```bash
# Copy templates and fill in values
cp .env.prod.template .env.prod
cp .env.db.prod.template .env.db.prod
cp frontend-clinician/.env.prod.template frontend-clinician/.env.prod
cp frontend-patient/.env.prod.template frontend-patient/.env.prod
cp frontend-admin/.env.prod.template frontend-admin/.env.prod

# Edit each file with your actual values
nano .env.prod
nano .env.db.prod
# etc...
```

3. **Deploy:**
```bash
cd deploy
./deploy.sh
```

## ⚙️ Configuration Details

### Environment Variables

#### Backend (.env.prod)
```bash
DATABASE_URL=**************************************************/pulsetrack_prod
JWT_SECRET_KEY=your-secret-key
CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
OPENAI_API_KEY=sk-...
REDIS_URL=redis://redis:6379/0
```

#### Database (.env.db.prod)
```bash
POSTGRES_DB=pulsetrack_prod
POSTGRES_USER=pulsetrack_user
POSTGRES_PASSWORD=your-secure-password
```

#### Frontend Apps (.env.prod for each)
```bash
VITE_API_BASE_URL=https://pulsetrack-api.synapsedx.co.uk/api/v1
VITE_CLERK_PUBLISHABLE_KEY=pk_live_...
```

### Performance Optimizations

**Backend Configuration (optimized for 8-core i9, 128GB RAM):**
- 6 Gunicorn workers
- DB pool size: 15, max overflow: 30
- PostgreSQL shared buffers: 2GB
- Effective cache size: 96GB

## 🔧 Management Commands

### View Service Status
```bash
docker-compose -f docker-compose.prod.yml ps
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f traefik
```

### Restart Services
```bash
# All services
docker-compose -f docker-compose.prod.yml restart

# Specific service
docker-compose -f docker-compose.prod.yml restart backend
```

### Database Management
```bash
# Access database
docker-compose -f docker-compose.prod.yml exec db psql -U pulsetrack_user -d pulsetrack_prod

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Backup database
docker-compose -f docker-compose.prod.yml exec db pg_dump -U pulsetrack_user pulsetrack_prod > backup.sql
```

### Stop All Services
```bash
docker-compose -f docker-compose.prod.yml down
```

### Complete Rebuild
```bash
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

## 🛡️ Security Features

- **SSL/TLS**: Automatic Let's Encrypt certificates
- **Security Headers**: XSS protection, content type validation
- **CORS**: Configured for your domains only  
- **Non-root Containers**: All services run as non-root users
- **Network Isolation**: Services communicate via internal Docker network

## 📊 Monitoring

### Health Checks
- Backend: `https://pulsetrack-api.synapsedx.co.uk/health`
- Traefik Dashboard: `https://pulsetrack-traefik.synapsedx.co.uk`

### Service Health Status
```bash
# Check backend health
docker-compose -f docker-compose.prod.yml exec backend curl -f http://localhost:8000/health

# Check all container health
docker-compose -f docker-compose.prod.yml ps
```

## 🚨 Troubleshooting

### Common Issues

**SSL Certificate Issues:**
- Certificates take 1-2 minutes to provision
- Check Traefik logs: `docker-compose -f docker-compose.prod.yml logs traefik`
- Ensure ports 80/443 are accessible from internet

**Backend Not Starting:**
- Check environment variables in `.env.prod`
- Verify database connection
- Check logs: `docker-compose -f docker-compose.prod.yml logs backend`

**Frontend Build Failures:**
- Node memory issues: builds use 4GB max heap
- Check logs: `docker-compose -f docker-compose.prod.yml logs frontend-clinician`

**Database Connection Issues:**
- Verify credentials in `.env.db.prod`
- Check database health: `docker-compose -f docker-compose.prod.yml exec db pg_isready`

### Log Locations
- Application logs: `docker-compose logs`
- Traefik access logs: Available in Traefik dashboard
- SSL certificate logs: `docker-compose logs traefik`

## 🔄 Updates & Maintenance

### Updating the Application
```bash
# Pull latest code
git pull origin deployment/edinburgh-demo

# Rebuild and restart
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

### Database Backups
```bash
# Create backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U pulsetrack_user pulsetrack_prod | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

# Restore backup
gunzip -c backup_file.sql.gz | docker-compose -f docker-compose.prod.yml exec -T db psql -U pulsetrack_user -d pulsetrack_prod
```

## 📞 Support

For deployment issues:
1. Check logs first: `docker-compose -f docker-compose.prod.yml logs`
2. Verify environment configuration
3. Check DNS and firewall settings
4. Review Traefik dashboard for routing issues

---

**Note**: This is a demo environment. For production use, consider additional security hardening, monitoring, and backup strategies.