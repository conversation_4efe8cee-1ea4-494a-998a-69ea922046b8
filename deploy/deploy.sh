#!/bin/bash

# PulseTrack Production Deployment Script for EDINBURGH
# Run this script on your EDINBURGH server

set -e

echo "🚀 Starting PulseTrack deployment to EDINBURGH..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (recommended for server deployment)
if [[ $EUID -eq 0 ]]; then
   print_warning "Running as root - this is acceptable for server deployment"
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if environment files exist
ENV_FILES=(
    ".env.prod"
    ".env.db.prod"
    "frontend-clinician/.env.prod"
    "frontend-patient/.env.prod" 
    "frontend-admin/.env.prod"
)

print_status "Checking environment configuration files..."
for file in "${ENV_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "Environment file $file is missing!"
        print_status "Please copy $file.template to $file and configure with your values"
        exit 1
    fi
done

print_status "All environment files found ✓"

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# Pull latest images
print_status "Pulling latest base images..."
docker-compose -f docker-compose.prod.yml pull

# Build images
print_status "Building application images..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Start services
print_status "Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
print_status "Waiting for services to become healthy..."
sleep 30

# Check service health
print_status "Checking service health..."

# Function to check if a URL is responding
check_url() {
    local url=$1
    local name=$2
    if curl -f -s -o /dev/null "$url"; then
        print_status "$name is responding ✓"
    else
        print_warning "$name is not responding yet (may take a few minutes for SSL certificates)"
    fi
}

# Check backend health endpoint
if docker-compose -f docker-compose.prod.yml exec -T backend curl -f http://localhost:8000/health > /dev/null 2>&1; then
    print_status "Backend health check passed ✓"
else
    print_warning "Backend health check failed - check logs with: docker-compose -f docker-compose.prod.yml logs backend"
fi

# Check if Traefik dashboard is accessible
check_url "http://localhost:8080" "Traefik Dashboard"

print_status "Deployment complete! 🎉"
echo ""
print_status "Your PulseTrack deployment should be available at:"
echo "  🔗 API: https://pulsetrack-api.synapsedx.co.uk"
echo "  👩‍⚕️ Clinician: https://pulsetrack-clinician.synapsedx.co.uk"
echo "  🏥 Patient: https://pulsetrack-patient.synapsedx.co.uk"
echo "  ⚙️ Admin: https://pulsetrack-admin.synapsedx.co.uk"
echo "  📊 Traefik Dashboard: https://pulsetrack-traefik.synapsedx.co.uk"
echo ""
print_warning "Note: SSL certificates may take a few minutes to be issued by Let's Encrypt"
echo ""
print_status "Useful commands:"
echo "  📋 View logs: docker-compose -f docker-compose.prod.yml logs -f [service]"
echo "  🔄 Restart: docker-compose -f docker-compose.prod.yml restart [service]"
echo "  🛑 Stop all: docker-compose -f docker-compose.prod.yml down"
echo "  📊 Status: docker-compose -f docker-compose.prod.yml ps"