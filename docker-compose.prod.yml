version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--global.sendanonymoususage=false"
      # Redirect HTTP to HTTPS
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
    restart: unless-stopped
    networks:
      - pulsetrack-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`pulsetrack-traefik.synapsedx.co.uk`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - PORT=8000
      - WORKERS=6  # Optimized for 8-core i9
      - DB_POOL_SIZE=15
      - DB_MAX_OVERFLOW=30
      - LOG_LEVEL=info
    env_file:
      - ./.env.prod
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pulsetrack-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pulsetrack-api.rule=Host(`pulsetrack-api.synapsedx.co.uk`)"
      - "traefik.http.routers.pulsetrack-api.tls=true"
      - "traefik.http.routers.pulsetrack-api.tls.certresolver=letsencrypt"
      - "traefik.http.services.pulsetrack-api.loadbalancer.server.port=8000"
      # Enable CORS headers
      - "traefik.http.middlewares.cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.cors.headers.accesscontrolalloworiginlist=https://pulsetrack-clinician.synapsedx.co.uk,https://pulsetrack-patient.synapsedx.co.uk,https://pulsetrack-admin.synapsedx.co.uk"
      - "traefik.http.middlewares.cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.cors.headers.addvaryheader=true"
      - "traefik.http.routers.pulsetrack-api.middlewares=cors"

  frontend-clinician:
    build:
      context: ./frontend-clinician
      dockerfile: Dockerfile.prod
    env_file:
      - ./frontend-clinician/.env.prod
    restart: unless-stopped
    networks:
      - pulsetrack-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pulsetrack-clinician.rule=Host(`pulsetrack-clinician.synapsedx.co.uk`)"
      - "traefik.http.routers.pulsetrack-clinician.tls=true"
      - "traefik.http.routers.pulsetrack-clinician.tls.certresolver=letsencrypt"
      - "traefik.http.services.pulsetrack-clinician.loadbalancer.server.port=80"

  frontend-patient:
    build:
      context: ./frontend-patient
      dockerfile: Dockerfile.prod
    env_file:
      - ./frontend-patient/.env.prod
    restart: unless-stopped
    networks:
      - pulsetrack-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pulsetrack-patient.rule=Host(`pulsetrack-patient.synapsedx.co.uk`)"
      - "traefik.http.routers.pulsetrack-patient.tls=true"
      - "traefik.http.routers.pulsetrack-patient.tls.certresolver=letsencrypt"
      - "traefik.http.services.pulsetrack-patient.loadbalancer.server.port=80"

  frontend-admin:
    build:
      context: ./frontend-admin
      dockerfile: Dockerfile.prod
    env_file:
      - ./frontend-admin/.env.prod
    restart: unless-stopped
    networks:
      - pulsetrack-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pulsetrack-admin.rule=Host(`pulsetrack-admin.synapsedx.co.uk`)"
      - "traefik.http.routers.pulsetrack-admin.tls=true"
      - "traefik.http.routers.pulsetrack-admin.tls.certresolver=letsencrypt"
      - "traefik.http.services.pulsetrack-admin.loadbalancer.server.port=80"

  db:
    image: pgvector/pgvector:pg15
    env_file:
      - ./.env.db.prod
    environment:
      - POSTGRES_LOG_STATEMENT=none  # Disable query logging in production
      - POSTGRES_LOG_DURATION=off
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - pulsetrack-net

  redis:
    image: redis:alpine
    volumes:
      - redis_data_prod:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - pulsetrack-net

volumes:
  postgres_data_prod:
  redis_data_prod:
  traefik_letsencrypt:

networks:
  pulsetrack-net:
    driver: bridge