services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      # Mount backend source code to the correct WOR<PERSON>DI<PERSON> from Dockerfile for hot-reloading
      - ./backend:/home/<USER>/app
      # Mount the config directory inside the WORKDIR (ensure ./backend/config exists locally)
      # Listed AFTER the main mount to ensure it takes precedence for the config subdirectory
      - ./backend/config:/home/<USER>/app/config
      - ./packages:/packages # Mount shared packages
    env_file:
      - ./.env # Load environment variables for the backend service
    environment:
      # Defaults can be set here if not present in .env
      # These will override defaults set in the Dockerfile or config.py if present in .env
      - PORT=${PORT:-8000}
      - WORKERS=${WORKERS:-4}
      - DB_POOL_SIZE=${DB_POOL_SIZE:-5}
      - DB_MAX_OVERFLOW=${DB_MAX_OVERFLOW:-10}
      # Pass other necessary env vars from .env
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - ALLOWED_CLINICIAN_DOMAINS=${ALLOWED_CLINICIAN_DOMAINS}
      - AZURE_STORAGE_CONTAINER_NAME=${AZURE_STORAGE_CONTAINER_NAME}
      - LOG_LEVEL=${LOG_LEVEL:-warning}
    ports:
      - "${PORT:-8000}:8000"
    depends_on:
      db:
        condition: service_healthy # Wait for DB healthcheck
      redis: # Add dependency on the new Redis service
        condition: service_healthy # Wait for Redis healthcheck
    restart: unless-stopped # Production restart policy
    healthcheck:
      # Use curl inside the container to check the /health endpoint
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s # Give time for the app to start before checking
    # Use production gunicorn command from Dockerfile
    command: gunicorn -k uvicorn.workers.UvicornWorker -w 2 -b "0.0.0.0:8000" main:app --forwarded-allow-ips='*'
    networks: # Assign backend to the custom network
      - pulsetrack-net

  db:
    image: pgvector/pgvector:pg15 # Use official image with pgvector extension included
    env_file:
      - ./.env.db # Load database credentials
    environment:
      - POSTGRES_LOG_STATEMENT=all
      - POSTGRES_LOG_DURATION=on
    volumes:
      - postgres_data:/var/lib/postgresql/data # Persist data using a named volume
    ports:
      # Optionally expose DB port to host for debugging, remove for stricter security
      - "5432:5432"
    healthcheck:
      # Use $$ to escape $ for docker-compose variable interpolation if needed
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: postgres -c log_statement=all # Added command to explicitly enable query logging
    networks: # Assign db to the custom network
      - pulsetrack-net
    restart: unless-stopped # Production restart policy

  redis: # Add the Redis service definition
    image: redis:alpine
    ports:
      - "6379:6379" # Expose Redis port to host (optional, for debugging)
    volumes:
      - redis_data:/data # Persist Redis data
    networks:
      - pulsetrack-net
    restart: unless-stopped # Production restart policy
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  frontend-clinician:
    build:
      context: ./frontend-clinician
      dockerfile: Dockerfile
    ports:
      - "5173:5173" # Changed to 5173:5173 to match Vite's default port
    volumes:
      - ./frontend-clinician:/app
      - ./packages:/packages
      - frontend-clinician-node-modules:/app/node_modules
      # Add this volume to prevent permission issues
      - /app/node_modules/.vite
    environment:
      - WATCHPACK_POLLING=true # Enable polling for Windows compatibility
      - CHOKIDAR_USEPOLLING=true # Enable polling for file watching
    command: >
      sh -c "
      yarn install &&
      cd /packages/shared-frontend && 
      yarn install --force && 
      yarn add vite@6.2.0 react-dom@19.0.0 --dev --exact && 
      sed -i 's/\"types\": \[\"vite\/client\"\]/\"types\": \[\]/' tsconfig.json &&
      sed -i 's/--external react/--external react,react-dom/g' package.json &&
      echo 'Building shared frontend package...' &&
      yarn build &&
      echo 'Shared frontend build complete, starting clinician app...' &&
      cd /app &&
      yarn dev --host 0.0.0.0 --port 5173
      "
    env_file:
      - ./frontend-clinician/.env
    depends_on:
      - backend
    networks:
      - pulsetrack-net

  frontend-patient:
    build:
      context: ./frontend-patient
      dockerfile: Dockerfile
    ports:
      - "5174:5174" # Changed to 5174:5174 for consistency
    volumes:
      - ./frontend-patient:/app
      - ./packages:/packages
      - frontend-patient-node-modules:/app/node_modules
      # Add this volume to prevent permission issues
      - /app/node_modules/.vite
    environment:
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    command: >
      sh -c "
      yarn install &&
      yarn dev --host 0.0.0.0 --port 5174
      "
    env_file:
      - ./frontend-patient/.env
    depends_on:
      - backend
    networks:
      - pulsetrack-net

  frontend-admin:
    build:
      context: ./frontend-admin
      dockerfile: Dockerfile
    ports:
      - "5175:5175" # Changed to 5175:5175 for consistency
    volumes:
      - ./frontend-admin:/app
      - ./packages:/packages
      - frontend-admin-node-modules:/app/node_modules
      # Add this volume to prevent permission issues
      - /app/node_modules/.vite
    environment:
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    command: >
      sh -c "
      yarn install &&
      yarn dev --host 0.0.0.0 --port 5175
      "
    env_file:
      - ./frontend-admin/.env
    depends_on:
      - backend
    networks:
      - pulsetrack-net

volumes:
  postgres_data: # Define the named volume for database persistence
  redis_data: # Define the named volume for Redis persistence
  frontend-clinician-node-modules:
  frontend-patient-node-modules:
  frontend-admin-node-modules: # Added volume for frontend-admin

networks: # Define the custom network
  pulsetrack-net:
    driver: bridge
