# Chat Intent Resolution Architecture

## Overview
The chat intent resolution system enables natural language understanding and action execution through a sophisticated pipeline that maintains context across multiple messages. This document describes the complete architecture after implementing fixes for parameter collection and validation.

## Key Components

### 1. Frontend Layer
- **ChatPage.tsx** (clinician & patient apps)
  - Preserves metadata from AI responses
  - Includes `previous_parameters` and `current_intent_action_type` in subsequent messages
  - Maintains conversation context for multi-step interactions

### 2. Routing Layer
- **ChatbotManager** (`app/services/chatbot_manager.py`)
  - Routes messages to appropriate chat modules
  - Checks for pending parameter collection from previous messages
  - Prevents fallback to other modules during active parameter collection

### 3. Action Processing Layer
- **LLMActionModule** (`app/services/chat_modules/llm_action_module.py`)
  - Orchestrates intent extraction and parameter collection
  - Integrates with ParameterPersistenceManager
  - Handles multi-step parameter collection flows

### 4. Intent Resolution Layer
- **IntentResolverService** (`app/services/intent_resolver_service.py`)
  - `extract_intent()`: Calls LLM to extract intent and parameters
  - `validate_parameters()`: Validates against action schemas
  - Implements parameter aliasing (onset_date → onset_time)
  - Uses retry logic with exponential backoff

### 5. Persistence Layer
- **ParameterPersistenceManager** (`app/utils/parameter_persistence.py`)
  - In-memory storage with TTL (10 minutes)
  - Thread-safe implementation
  - Stores partial parameters between messages

### 6. Execution Layer
- **ActionExecutorService** (`app/services/action_executor_service.py`)
  - Executes validated actions
  - Handles both `onset_time` and `onset_date` parameters
  - Returns structured responses for UI display

### 7. Utility Layer
- **Retry Utilities** (`app/utils/retry.py`)
  - Provides resilience against transient LLM failures
  - Exponential backoff with jitter
  - Configurable retry attempts and delays

## Complete Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant ChatbotManager
    participant LLMActionModule
    participant IntentResolver
    participant ParameterPersistence
    participant ActionExecutor
    
    User->>Frontend: "Report side effects from Ozempic"
    Frontend->>API: POST /chat/send
    API->>ChatbotManager: route_message()
    ChatbotManager->>LLMActionModule: generate_response()
    LLMActionModule->>IntentResolver: extract_intent()
    IntentResolver->>IntentResolver: LLM call with retry
    IntentResolver-->>LLMActionModule: ResolvedIntent (missing onset_time)
    LLMActionModule->>IntentResolver: validate_parameters()
    IntentResolver->>IntentResolver: Parameter aliasing logic
    IntentResolver-->>LLMActionModule: Missing parameters
    LLMActionModule->>ParameterPersistence: save_partial_parameters()
    LLMActionModule-->>Frontend: "When did symptoms start?"
    
    User->>Frontend: "yesterday"
    Frontend->>API: POST /chat/send (with metadata)
    API->>ChatbotManager: route_message()
    ChatbotManager->>ChatbotManager: Check pending parameters
    ChatbotManager->>LLMActionModule: Route to same module
    LLMActionModule->>ParameterPersistence: get_saved_parameters()
    LLMActionModule->>IntentResolver: extract_intent("yesterday")
    IntentResolver-->>LLMActionModule: onset_date: "2025-05-28"
    LLMActionModule->>IntentResolver: validate_parameters()
    IntentResolver->>IntentResolver: Map onset_date to onset_time
    IntentResolver-->>LLMActionModule: Valid parameters
    LLMActionModule->>ActionExecutor: execute_action()
    ActionExecutor-->>LLMActionModule: Success response
    LLMActionModule->>ParameterPersistence: clear_parameters()
    LLMActionModule-->>Frontend: Action completed
```

## Key Features

### 1. Parameter Aliasing
The system handles LLM parameter name variations gracefully:
```python
# In validate_parameters()
if resolved_intent.action_type == "side_effect_report_create":
    if "onset_date" in resolved_params and "onset_time" not in resolved_params:
        resolved_params["onset_time"] = resolved_params["onset_date"]
```

### 2. Retry Logic
All LLM calls are wrapped with retry logic:
```python
@retry_async(
    max_attempts=3,
    base_delay=1.0,
    max_delay=10.0,
    exceptions=(LLMException, json.JSONDecodeError, ValidationError),
    jitter=True
)
async def _call_llm_with_retry(self, provider, variables):
    # LLM call implementation
```

### 3. Context Preservation
Frontend preserves metadata for parameter collection:
```typescript
const parameterCollectionMetadata = {};
if (lastAIMessage?.metadata) {
    if (lastAIMessage.metadata.previous_parameters) {
        parameterCollectionMetadata['previous_parameters'] = 
            lastAIMessage.metadata.previous_parameters;
    }
}
```

### 4. Intelligent Routing
ChatbotManager checks for pending parameter collection:
```python
if context.get("previous_parameters") or context.get("current_intent_action_type"):
    return "llm_action"  # Continue with same module
```

## Production Readiness

The system is now production-ready with:
- ✅ Resilient LLM calls with retry logic
- ✅ Context preservation across messages
- ✅ Flexible parameter name handling
- ✅ Thread-safe parameter persistence
- ✅ Comprehensive error handling
- ✅ Audit logging for all actions
- ✅ Clear user feedback during multi-step flows

## Future Enhancements

1. **Enhanced Action Cards** (GitHub #27)
   - Show specific action details in completion cards
   - Display appointment times, medication names, etc.

2. **Persistent Storage**
   - Move parameter persistence to Redis for distributed systems
   - Add database backup for parameter recovery

3. **Advanced Patterns**
   - Support for conditional actions
   - Batch operations
   - Action chaining with dependencies