# Chat Intent Resolution & Parameter Collection Fix Summary

## Problem Statement
The chat intent resolution system was failing at random places during parameter collection:
- System would forget it was collecting parameters and fall back to LLM
- Context was lost between messages during multi-step parameter collection
- Example: Clinician reporting side effects, system asks for onset time, user provides "yesterday", system forgets context and provides clinical guidelines instead

## Root Cause Analysis
1. **Backend Issues:**
   - No retry logic for LLM calls - transient failures caused immediate fallback
   - No parameter persistence between messages - collected parameters were lost
   - ChatbotManager not checking for pending parameter collection in routing logic

2. **Frontend Issues:**
   - Metadata from AI responses (containing `previous_parameters` and `current_intent_action_type`) was not being passed back in subsequent messages
   - Frontend was only including `sender`, `message`, and `timestamp` in conversation history, excluding crucial `metadata`

## Solutions Implemented

### 1. Retry Logic with Exponential Backoff
**File:** `/backend/app/utils/retry.py`
- Created comprehensive retry utilities with exponential backoff and jitter
- Supports both async and sync functions
- Configurable max attempts, delays, and exception handling
- Fixed logging to use standard logging module format

**File:** `/backend/app/services/intent_resolver_service.py`
- Added `@retry_async` decorator to LLM calls
- 3 attempts with exponential backoff for resilience against transient failures

### 2. Parameter Persistence System
**File:** `/backend/app/utils/parameter_persistence.py`
- Created `ParameterPersistenceManager` class
- In-memory storage with TTL (10 minutes)
- Thread-safe implementation using locks
- Methods: `save_partial_parameters`, `get_saved_parameters`, `clear_parameters`

**File:** `/backend/app/services/chat_modules/llm_action_module.py`
- Integrated parameter persistence manager
- Saves partial parameters on validation failure
- Loads saved parameters on new messages
- Clears parameters on successful action execution

### 3. Enhanced ChatbotManager Routing
**File:** `/backend/app/services/chatbot_manager.py`
- Added check for `previous_parameters` and `current_intent_action_type` in context
- Routes to LLM Action Module when parameter collection is in progress
- Prevents incorrect routing to other modules during parameter collection

### 4. Frontend Metadata Preservation
**File:** `/frontend-clinician/src/pages/ChatPage.tsx`
- Modified `sendMessage` function to extract metadata from last AI message
- Includes `previous_parameters`, `current_intent_action_type`, and `pending_compound_action` in context
- Added metadata to conversation history items

**File:** `/frontend-patient/src/pages/ChatPage.tsx`
- Same fixes as clinician frontend
- Ensures parameter collection state is preserved across messages

## Testing
- Created `test_retry_logic.py` to verify retry functionality
- Tested with scenarios that succeed on 3rd attempt and always fail
- Confirmed proper exponential backoff and error handling

## Result
The system now properly maintains context during multi-step parameter collection:
1. When AI asks for missing parameters, it includes metadata in the response
2. Frontend preserves this metadata and includes it in the next message
3. Backend uses saved parameters + new input to complete the action
4. System doesn't fall back to LLM unnecessarily during parameter collection

This ensures a smooth, production-ready chat experience where complex actions requiring multiple inputs work reliably.