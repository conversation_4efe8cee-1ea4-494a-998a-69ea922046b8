# PulseTrack Codebase Analysis

*Generated: May 30, 2025*

## 📊 Overall Statistics

- **Total Lines of Code**: 168,569 lines (excluding dependencies)
- **Total Files**: 818 files
- **Production Code**: 110,454 lines (Backend + Frontend)

## 💻 Code Distribution by Language

### Backend (Python)
- **Total**: 73,017 lines across 354 files (43.3% of codebase)
- **Breakdown**:
  - `app/` (core application): 50,889 lines (207 files)
  - `alembic/` (migrations): 5,101 lines (53 files)
  - `tests/`: 2,817 lines (13 files)
  - `scripts/`: 13,738 lines (78 files)
- **Average**: 206 lines per Python file

### Frontend (TypeScript/React)
- **Total**: 37,437 lines across 216 files (22.2% of codebase)
- **Breakdown**:
  - `frontend-patient`: 10,016 lines (67 files)
  - `frontend-clinician`: 22,550 lines (110 files)
  - `frontend-admin`: 4,871 lines (39 files)
- **Average**: 173 lines per TypeScript file

## 📚 Documentation
- **Total**: 24,313 lines across 144 Markdown files (14.4% of codebase)
- **Breakdown**:
  - `docs/`: 5,189 lines (26 files)
  - `knowledge/`: 14,192 lines (101 files)
  - Other locations: 4,932 lines

## ⚙️ Configuration & Infrastructure
- **Total**: 27,662 lines (16.4% of codebase)
- **Breakdown**:
  - JSON files: 2,179 lines (29 files)
  - YAML files: 473 lines (4 files)
  - Docker files: 321 lines (5 files)
  - Poetry/Package files: ~25,000 lines (mostly lock files)

## 🎨 Other Files
- **Total**: 6,140 lines (3.6% of codebase)
- **Breakdown**:
  - CSS: 576 lines (8 files)
  - Shell scripts: 711 lines (8 files)
  - SQL: 4,027 lines (1 file)
  - LaTeX: 866 lines (4 files)

## 📈 Key Metrics

| Metric | Value |
|--------|-------|
| Code-to-Documentation Ratio | 4.5:1 |
| Backend-to-Frontend Ratio | 1.95:1 |
| Test Coverage | 2,817 lines (3.9% of backend) |
| Largest Frontend App | frontend-clinician (60% of frontend) |
| Smallest Frontend App | frontend-admin (13% of frontend) |

## 🔍 Analysis Insights

1. **Backend Dominance**: The backend comprises the largest portion at 43.3%, indicating a robust API and business logic layer
2. **Clinician-Focused**: The clinician frontend is the most complex UI with 22,550 lines, reflecting the primary user focus
3. **Well-Documented**: Strong documentation presence at 14.4% shows commitment to maintainability
4. **Configuration Overhead**: 16.4% configuration (mostly lock files) ensures reproducible builds
5. **Modular Architecture**: Clear separation between three frontend apps allows independent development

## 📁 Directory Structure Overview

```
pulsetrack/
├── backend/             (73,017 lines)
│   ├── app/            (50,889 lines) - Core application logic
│   ├── alembic/        (5,101 lines)  - Database migrations
│   ├── scripts/        (13,738 lines) - Utility scripts
│   └── tests/          (2,817 lines)  - Test suite
├── frontend-patient/    (10,016 lines) - Patient portal
├── frontend-clinician/  (22,550 lines) - Clinician dashboard
├── frontend-admin/      (4,871 lines)  - Admin interface
├── docs/               (5,189 lines)   - Technical documentation
└── knowledge/          (14,192 lines)  - Domain knowledge & PRDs
```

## 🚀 Analysis Scripts

The codebase includes three analysis scripts for ongoing monitoring:

1. `analyze_codebase.sh` - Basic file type analysis
2. `codebase_analysis_detailed.sh` - Detailed breakdown with percentages
3. `codebase_summary.sh` - Quick summary by major categories
4. `codebase_analysis_accurate.sh` - Most accurate analysis (created during this session)

Run any of these scripts to get updated metrics:
```bash
bash analyze_codebase.sh
```