# PulseTrack Date/Time Handling Audit Report

## Executive Summary

This audit reveals **significant inconsistencies** in date/time handling across the PulseTrack codebase. While the backend properly stores timestamps with timezone information, the frontend implementations vary widely in how they display these timestamps, creating potential confusion for users in different timezones.

## Key Findings

### 1. Backend (Python/SQLAlchemy)

#### ✅ Strengths
- **Database models consistently use `DateTime(timezone=True)`** in `BaseWithTimestamps`
- **UTC timestamps are properly generated** using `get_utc_now()` function
- **ISO format serialization** is used consistently with `.isoformat()`

#### 📍 Patterns Found
```python
# Good practice in base_class.py
created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

# Proper UTC handling in utils/__init__.py
def get_utc_now() -> datetime:
    return datetime.now(timezone.utc)

# Consistent serialization in chat.py
timestamp = msg.created_at.isoformat()
```

### 2. Frontend Date/Time Display Inconsistencies

#### ❌ Major Issues

**Multiple different date formatting approaches:**

1. **Native JavaScript Date methods (problematic)**:
   ```typescript
   // Found in multiple files - uses browser's locale
   date.toLocaleString()
   date.toLocaleDateString() 
   date.toLocaleTimeString()
   ```

2. **date-fns library (better but inconsistent)**:
   ```typescript
   format(date, 'PPP p')  // Different format strings used
   format(date, 'yyyy-MM-dd')
   format(date, 'MMM d, yyyy')
   ```

3. **Manual timezone handling (incomplete)**:
   ```typescript
   // Only in frontend-clinician/src/lib/dateUtils.ts
   export function convertUTCToLocal(utcDateString: string): Date {
     const date = parseISO(utcDateString);
     return date;
   }
   ```

#### 🔴 Critical Problem Areas

1. **Chat Messages** (`frontend-patient/src/pages/ChatPage.tsx`):
   ```typescript
   // Line 67-68: Uses browser locale without timezone consideration
   return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
   return date.toLocaleDateString();
   ```

2. **Appointments** (multiple files):
   - Patient app: Uses `format(parseISO(date), 'PPP p')`
   - Clinician app: Has `convertUTCToLocal` but not consistently used
   - Admin app: No timezone utilities found

3. **Clinical Notes** timestamps:
   - Inconsistent formatting between list view and detail view
   - No clear timezone indication to users

4. **Event Logs & Audit Trails**:
   - Backend sends UTC ISO strings
   - Frontend displays without timezone context

## Specific Files Requiring Attention

### High Priority (User-facing timestamps)
1. `frontend-patient/src/pages/ChatPage.tsx` - Chat message timestamps
2. `frontend-clinician/src/pages/ChatPage.tsx` - Chat message timestamps
3. `frontend-patient/src/pages/AppointmentsPage.tsx` - Appointment scheduling
4. `frontend-clinician/src/pages/AppointmentsPage.tsx` - Appointment management
5. `frontend-clinician/src/components/clinical-notes/ClinicalNoteViewModal.tsx` - Clinical note timestamps

### Medium Priority
1. `frontend-patient/src/pages/WeightTrackingPage.tsx` - Weight log dates
2. `frontend-clinician/src/pages/SideEffectsPage.tsx` - Side effect report times
3. `frontend-clinician/src/pages/AlertsPage.tsx` - Alert timestamps
4. `frontend-admin/src/pages/AdminDashboardPage.tsx` - Activity timestamps

### Backend Improvements Needed
1. `backend/app/schemas/chat.py` - Add timezone info to response schemas
2. `backend/app/api/v1/endpoints/appointments.py` - Ensure timezone-aware responses

## Inconsistency Examples

### Example 1: Chat Timestamps
- **Patient app**: `date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })`
- **Clinician app**: Same approach but has unused `convertUTCToLocal` utility
- **Issue**: A message sent at 3pm EST shows as 3pm PST for a California user

### Example 2: Appointment Times
- **Backend**: Stores as `2024-01-15T15:00:00+00:00` (UTC)
- **Patient frontend**: Shows as browser's local time without indication
- **Clinician frontend**: Has timezone utilities but not consistently applied

### Example 3: Clinical Notes
- **Created at**: Uses `format()` from date-fns
- **Approved at**: Uses `toLocaleDateString()`
- **Issue**: Same note shows different date formats in different views

## Recommendations

### 1. Immediate Actions
1. **Standardize on date-fns** library for all date formatting
2. **Create shared date utilities** in a common package
3. **Always display timezone** information to users
4. **Use consistent format strings** across all apps

### 2. Proposed Solution Architecture

Create a shared utility module:
```typescript
// packages/shared-frontend/src/utils/dateTime.ts
import { format, parseISO } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';

export const formatDateTime = (dateString: string): string => {
  const date = parseISO(dateString);
  return format(date, 'PPP p'); // Jan 15, 2024 3:00 PM
};

export const formatDateTimeWithTimezone = (dateString: string): string => {
  const date = parseISO(dateString);
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  return formatInTimeZone(date, userTimezone, 'PPP p zzz'); // Jan 15, 2024 3:00 PM EST
};

export const formatRelativeTime = (dateString: string): string => {
  // Implementation for "2 hours ago" style timestamps
};
```

### 3. Priority Implementation Order
1. Fix chat message timestamps (highest user impact)
2. Fix appointment display times (critical for scheduling)
3. Standardize clinical notes timestamps
4. Update remaining timestamp displays

### 4. Testing Requirements
- Test with users in different timezones
- Verify daylight saving time transitions
- Ensure consistency between list and detail views
- Validate appointment scheduling across timezones

## Conclusion

The current implementation risks confusing users about when events occurred, especially for:
- Cross-timezone consultations
- Appointment scheduling
- Time-sensitive medication or side effect reports

Implementing consistent timezone-aware date handling should be prioritized to ensure reliable healthcare delivery across all time zones.