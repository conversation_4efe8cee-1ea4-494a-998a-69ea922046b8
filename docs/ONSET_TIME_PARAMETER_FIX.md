# Onset Time Parameter Fix

## Problem
The system was stuck in a loop asking for "onset_time" even after the user provided "yesterday" because:
1. The LLM correctly extracted "onset_date: 2025-05-28" from "yesterday"
2. The action handler already accepts both "onset_time" and "onset_date" parameters
3. The validation logic only checked for "onset_time" in required parameters
4. This caused validation to fail with "Missing required parameters: onset_time"

## Solution Implemented

### 1. Updated Parameter Validation Logic
**File:** `/backend/app/services/intent_resolver_service.py` (lines 960-975)

Added parameter aliasing logic in the `validate_parameters` method:
```python
# Handle side effect report parameter aliases (onset_date -> onset_time)
if resolved_intent.action_type == "side_effect_report_create":
    # The action handler accepts both onset_time and onset_date
    # but the schema only defines onset_time as required
    # If we have onset_date but not onset_time, copy it over
    if "onset_date" in resolved_params and "onset_time" not in resolved_params:
        logger.info(f"Mapping onset_date to onset_time for side effect report: {resolved_params['onset_date']}")
        resolved_params["onset_time"] = resolved_params["onset_date"]
        # Also update the parameters list to include onset_time
        resolved_intent.parameters.append(
            IntentParameterValue(
                name="onset_time",
                value=resolved_params["onset_date"],
                confidence=1.0,
                source="mapped_from_onset_date"
            )
        )
```

### 2. Updated LLM Prompt
**File:** `/backend/app/services/intent_resolver_service.py` (lines 380-384)

Updated the LLM extraction prompt to clarify both parameter names are acceptable:
```
13. SIDE EFFECT - ONSET TIME: For side effect reports, you can extract the onset timing as either `onset_time` OR `onset_date` parameter (both are accepted by the system):
   - If the user mentions a relative time like 'yesterday', 'this morning', 'last week', extract it as `onset_date` in YYYY-MM-DD format
   - If the user mentions 'since starting [medication]', extract as `onset_time` with value 'Since starting [medication_name]'
   - For other time descriptions, use whichever parameter name feels more natural (`onset_time` or `onset_date`)
   - The system will automatically handle either parameter name
```

## Result
Now when the LLM extracts "onset_date" from user input like "yesterday", the validation logic will:
1. Detect that we have "onset_date" but not "onset_time"
2. Copy the value from "onset_date" to "onset_time"
3. Pass validation since "onset_time" is now present
4. The action handler continues to accept both parameter names

This eliminates the infinite loop where the system kept asking for onset_time even though it had already extracted the date information.