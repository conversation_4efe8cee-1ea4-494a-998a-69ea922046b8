# RAG System Testing Guide

This guide provides comprehensive instructions for evaluating the RAG (Retrieval-Augmented Generation) system in PulseTrack.

## Overview

The RAG system in PulseTrack consists of:
1. **Content ingestion**: Scraping clinic websites and storing content
2. **Embedding generation**: Creating vector embeddings for content chunks
3. **Similarity search**: Finding relevant content based on user queries
4. **Context enrichment**: Adding relevant content to chat prompts
5. **Response generation**: Using enriched context for better answers

## Testing Scripts

### 1. Unit Tests: `/backend/tests/test_rag_system.py`

Run comprehensive unit tests for RAG components:

```bash
cd backend
pytest tests/test_rag_system.py -v
```

Tests include:
- Embedding generation
- Text chunking
- RAG enrichment
- Context formatting
- Similarity search
- Chat with RAG integration
- Performance benchmarks
- Error handling

### 2. Performance Evaluation: `/backend/scripts/evaluate_rag_performance.py`

Evaluate RAG system performance and generate metrics:

```bash
cd backend
python scripts/evaluate_rag_performance.py
```

This script:
- Evaluates retrieval quality
- Benchmarks embedding generation
- Analyzes content coverage
- Generates visualizations
- Saves results to JSON

Output files:
- `rag_evaluation_complete.json`: Detailed metrics
- `rag_evaluation_results.png`: Performance visualizations

### 3. End-to-End Chat Testing: `/backend/scripts/test_rag_chat_flow.py`

Test the complete RAG-enabled chat flow:

```bash
cd backend
python scripts/test_rag_chat_flow.py
```

Tests include:
- Diabetes management queries
- Hypertension queries
- Weight management queries
- Medication queries
- Appointment scheduling

Output: `rag_chat_test_results.json`

## Manual Testing Steps

### 1. Set Up Test Data

First, ensure you have test data in your database:

```python
# Create a test clinic with scraped content
from app.crud.crud_clinic import crud_clinic
from app.crud.crud_scraped_page import scraped_page

# Create clinic
clinic = await crud_clinic.create(db, obj_in={
    "name": "Test Medical Clinic",
    "website_url": "https://testclinic.com"
})

# Add scraped pages with medical content
page = await scraped_page.create(db, obj_in={
    "clinic_id": clinic.id,
    "source_url": "https://testclinic.com/diabetes",
    "title": "Diabetes Management",
    "cleaned_content": "Comprehensive diabetes management guidelines..."
})

# Generate embeddings
from app.services.embedding_pipeline import process_scraped_page_content
process_scraped_page_content(db, page)
```

### 2. Test Chat Endpoint

Use curl or a tool like Postman to test the chat endpoint:

```bash
curl -X POST http://localhost:8000/api/v1/chat/messages \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the guidelines for managing diabetes?",
    "structured_output": false
  }'
```

### 3. Monitor RAG Performance

Check the logs for RAG-related activity:

```bash
# Backend logs
docker-compose logs -f backend | grep -i rag

# Look for:
# - "Starting RAG enrichment"
# - "Retrieved X RAG content chunks"
# - "RAG enrichment completed"
```

## Key Metrics to Monitor

### 1. Retrieval Performance
- **Retrieval Time**: Should be < 100ms
- **Chunks Retrieved**: 1-3 chunks per query
- **Relevance Score**: > 0.7 for good matches

### 2. Content Coverage
- **Pages per Clinic**: Number of scraped pages
- **Chunks per Page**: Average chunk count
- **Total Content**: Characters indexed

### 3. Response Quality
- **Topic Coverage**: % of expected topics in response
- **Response Time**: Total time including RAG
- **Success Rate**: % of successful queries

## Debugging RAG Issues

### 1. No RAG Results

If queries return no RAG results:

```python
# Check if content exists
from app.crud.crud_content_chunk import crud_content_chunk

chunks = await crud_content_chunk.get_multi(db, limit=10)
print(f"Total chunks in database: {len(chunks)}")

# Check embeddings
for chunk in chunks:
    print(f"Chunk has embedding: {chunk.embedding is not None}")
    print(f"Embedding dimension: {len(chunk.embedding) if chunk.embedding else 0}")
```

### 2. Poor Relevance

If retrieved content isn't relevant:

```python
# Test similarity directly
from app.services.embedding_pipeline import generate_embeddings

query = "diabetes management"
query_embedding = generate_embeddings([query])[0]

similar_chunks = crud_content_chunk.find_similar_content_chunks(
    db, query_embedding=query_embedding, limit=5
)

for chunk in similar_chunks:
    print(f"Chunk text: {chunk.chunk_text[:100]}...")
    print(f"Similarity score: {chunk.similarity_score}")
```

### 3. Performance Issues

If RAG is too slow:

```python
import time

# Measure each step
start = time.time()
embedding = generate_embeddings([query])[0]
embedding_time = time.time() - start

start = time.time()
chunks = crud_content_chunk.find_similar_content_chunks(db, embedding)
search_time = time.time() - start

print(f"Embedding time: {embedding_time:.3f}s")
print(f"Search time: {search_time:.3f}s")
```

## Configuration Tuning

### 1. Embedding Model

Current: `all-mpnet-base-v2` (768 dimensions)

To change:
```python
# In app/services/embedding_pipeline.py
MODEL_NAME = "all-MiniLM-L6-v2"  # Faster, 384 dimensions
```

### 2. Chunk Settings

```python
# In app/services/embedding_pipeline.py
CHUNK_SIZE = 500  # Characters per chunk
CHUNK_OVERLAP = 50  # Overlap between chunks
```

### 3. Retrieval Limits

```python
# In app/utils/context_enricher.py
chunks = crud_content_chunk.find_similar_content_chunks(
    db, query_embedding=query_embedding, 
    limit=3,  # Adjust number of chunks retrieved
    clinic_id=clinic_id
)
```

## Best Practices

1. **Content Quality**: Ensure scraped content is clean and relevant
2. **Chunk Size**: Balance between context and precision (300-500 chars)
3. **Embedding Updates**: Re-generate embeddings when content changes
4. **Cache Strategy**: Consider caching frequent queries
5. **Monitoring**: Log RAG performance metrics for analysis

## Next Steps

1. Run the test scripts to establish baseline metrics
2. Monitor production performance
3. Tune configuration based on results
4. Add more comprehensive test cases
5. Consider A/B testing different models/settings

## Troubleshooting Checklist

- [ ] Database has scraped content
- [ ] Embeddings are generated for content
- [ ] Embedding dimensions match model output
- [ ] Patient/Clinician has associated clinic
- [ ] Similarity search returns results
- [ ] Context enricher formats properly
- [ ] Chat agent includes RAG context
- [ ] Response time is acceptable

## Support

For issues or questions:
1. Check logs for error messages
2. Run diagnostic scripts
3. Review configuration settings
4. Test with simplified queries