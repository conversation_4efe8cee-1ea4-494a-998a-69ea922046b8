# PulseTrack Documentation

This directory contains all documentation for the PulseTrack project.

## Directory Structure

- **architecture/** - System architecture diagrams and descriptions
- **planning/** - Project planning documents, roadmaps, and timelines
- **prds/** - Product Requirements Documents for different features and components

## Documentation Guidelines

1. All documentation should be written in Markdown format (.md)
2. Use clear, concise language
3. Include diagrams when applicable (using Mermaid syntax where possible)
4. Keep documentation up-to-date with the current state of the project

## Knowledge Integration

This documentation is integrated with the PulseTrack knowledge system, which automatically:

1. Tracks progress from PRD implementation
2. Generates repository snapshots
3. Maintains git context information
4. Creates AI-powered digests of project status

The knowledge system is managed through the CI pipeline, which updates the knowledge repository automatically on each commit.

## Legacy Documentation

Previously, documentation was stored in the `.memory-bank` and `.xgen` directories. These are being gradually migrated to the new structure. Please add new documentation to the appropriate directory in the new structure.
