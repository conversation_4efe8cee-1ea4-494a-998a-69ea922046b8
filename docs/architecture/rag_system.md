# RAG Architecture Plan for Scraped Content & Chat History (v1.0)

**Date:** 2025-04-06

**Context:** Enhance the chat feature by using scraped web content and user chat history as context for an LLM, employing Retrieval-Augmented Generation (RAG). This plan outlines strategies for persistent storage, processing, and retrieval, considering multi-tenancy and administrative needs.

## 1. Storage Strategy

Distinct storage solutions are needed for raw/cleaned scraped text and the processed vector embeddings.

*   **A. Raw/Cleaned Scraped Content:**
    *   **Recommendation:** Utilize the existing **PostgreSQL** database. Create a new table: `scraped_pages`.
    *   **Schema:** `id` (PK), `clinic_id` (FK), `source_url`, `scraped_at`, `cleaned_content` (TEXT/JSONB), `metadata` (JSONB - e.g., `source_type`).
    *   **Rationale:** Leverages existing infrastructure and aligns with current DB practices. `clinic_id` ensures multi-tenant data isolation.

*   **B. Processed Data (Text Chunks & Embeddings):**
    *   **Recommendation:** Use the **`pgvector` extension** within the existing PostgreSQL database. Create a new table: `content_chunks`.
    *   **Schema:** `id` (PK), `scraped_page_id` (FK to `scraped_pages`), `chunk_text`, `embedding` (VECTOR type), `metadata` (JSONB - e.g., chunk position, `source_type`).
    *   **Rationale:** Keeps data consolidated initially. Performance can be monitored, and migration to a dedicated vector DB is possible later if needed.

*   **C. Chat History:**
    *   **Recommendation:** Continue using the existing **PostgreSQL** `ChatMessage` table.

## 2. Processing Strategy

A background processing pipeline transforms raw scraped content into searchable vector embeddings.

*   **Pipeline Steps:**
    1.  **Extraction:** Post-scrape (`services/web_scraper.py`), extract clean text and store in `scraped_pages`.
    2.  **Chunking:** Retrieve cleaned content; split into suitable chunks.
    3.  **Embedding:** Convert text chunks into vector embeddings using a chosen model.
    4.  **Storage:** Store chunk text, embedding, and metadata in `content_chunks` (using `pgvector`).
*   **Implementation:** Start with FastAPI `BackgroundTasks`. Plan for migration to a robust task queue (Celery/RQ) for scalability.
*   **Update Strategy:** Define re-scraping (manual/scheduled) and re-processing triggers. Decide on handling updates (overwrite/version).
*   **Admin View:** Configuration for chunking strategy, embedding model/keys, update frequency. Manual triggers. Status indicators per clinic (last scraped, chunks embedded).

## 3. Retrieval Strategy (RAG)

Fetch relevant context during chat interactions.

1.  **Embed User Message:** Generate embedding for the user's query using the *same* model.
2.  **Vector Search (Scraped Content):** Query `content_chunks` via `pgvector` similarity search (filtered by `clinic_id`) for top-k chunks. Apply a similarity threshold to filter weak matches. Log match scores.
3.  **Keyword/Recency Search (Chat History):** Query `ChatMessage` table for recent messages in the current conversation (filtered by `patient_id`/`clinic_id`).
4.  **Context Assembly:** Combine retrieved chunks and chat messages.
5.  **Prompt Formulation:** Construct the LLM prompt with the original query and assembled context.
6.  **LLM Interaction:** Send the augmented prompt.
7.  **Return Response:** Display the response. Add UI disclaimer (informational, not medical advice) per `chat_security.md`.
*   **Admin View:** Configuration for `k` (number of chunks), chat history lookback, relevance thresholds.

## 4. High-Level Diagram (Mermaid)

```mermaid
graph LR
    A[Web Scraper] --> B(Storage: Raw/Cleaned Text<br/>[Postgres: scraped_pages]);
    B --> C{Processing Pipeline<br/>(Background Task/Queue)};
    C -- Chunking --> D;
    C -- Embedding --> D[Storage: Chunks & Embeddings<br/>[Postgres + pgvector: content_chunks]];
    E[User Chat Input] --> F{RAG Retriever};
    D -- Vector Search (Top-k, Filtered by Clinic) --> F;
    G[Storage: Chat History<br/>[Postgres: ChatMessage]] -- Recency Search --> F;
    F -- Formulate Prompt w/ Context --> H[LLM];
    H --> I[Chat Interface w/ Disclaimer];

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#ccf,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#ff9,stroke:#333,stroke-width:2px
    style F fill:#9cf,stroke:#333,stroke-width:2px
```

## 5. RAG Pipeline Enhancements

The updated RAG architecture includes:
 • A new scraped_pages model that captures source_url, cleaned_content, and metadata
 • A new content_chunks model employing pgvector for embedding storage and retrieval
 • Details on enabling the pgvector extension and its role in powering AI-driven search
 • The use of last_scraped_at in the Clinic model to track content freshness

## 6. Suggested Implementation Order

1.  Create `scraped_pages` + `content_chunks` models/migrations (including `clinic_id` FKs).
2.  Extend `web_scraper.py` to store cleaned content post-scrape into `scraped_pages`.
3.  Implement chunking & embedding pipeline (start as CLI/admin-triggered job).
4.  Install and test `pgvector` integration.
5.  Extend `chat_agent.py` retrieval logic to include `pgvector` top-k results + recent chat history, respecting multi-tenancy.
6.  Build basic UI/admin tools for scrape/embedding status and triggers.
7.  Add UI disclaimer.