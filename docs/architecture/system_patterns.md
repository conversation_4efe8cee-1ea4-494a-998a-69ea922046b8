# System Patterns

This file documents recurring patterns and standards used in the project.

## Authentication Patterns

### Authentication & Authorization
* **Unified Clerk Authentication** - Clerk is now used for all authentication across all frontends:
  - Replaces previously planned magic code (patient) and Firebase (clinician) systems
  - Provides consistent authentication mechanism across the application
  - Uses JWT templates for secure token generation and validation

* **Dependency Chain Pattern** - Authentication follows a clear dependency chain:
  - `get_current_user` -> Basic token validation
  - `get_current_patient`/`get_current_clinician`/`get_current_admin` -> Role-specific validation
  - `get_current_admin_or_clinician` -> Multi-role validation

* **Automatic Record Creation** - User records are automatically created/updated on first login:
  - Patient/clinician records created with Clerk user data (first_name, last_name)
  - Clinician-clinic associations handled during record creation
  - Patient-clinician relationships established during invitation flow

* **Error Handling Pattern** - Authentication errors follow consistent pattern:
  - 401 for invalid/missing tokens
  - 403 for invalid roles
  - 500 for unexpected processing errors

* **Logging Pattern** - Authentication flows include detailed logging:
  - Debug logs for dependency entry/exit
  - Info logs for important operations
  - Error logs with full stack traces

* **Token Management** - Token handling follows consistent pattern:
  - Frontend always requests JWT with specific template: `getToken({ template: "CodenamePulsetrack" })`
  - Backend validates tokens using Clerk's `authenticate_request` method
  - Token payload is parsed and validated consistently

## Database Patterns

### CRUD Operations
* **Class-Based CRUD Pattern** - All CRUD modules follow class-based pattern:
  - Each model has dedicated CRUD class inheriting from `CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType]`
  - Standard methods include `create`, `get`, `get_multi`, `update`, and `delete`
  - Model-specific methods added as needed (e.g., `get_by_patient`, `check_weekly_log_interval`)
  - Module-level function exports maintain backward compatibility (e.g., `create_weight_log = weight_log.create`)
  - Comprehensive error handling with try/except blocks and proper logging
  - Type annotations for improved IDE support and code safety

* **ID Type Consistency** - ID fields follow consistent pattern:
  - `patient_id` and `clinician_id` use `String` type to match Clerk user IDs
  - Other IDs (like `note_id`, `lab_result_id`, etc.) use `UUID` type
  - Foreign keys reference these IDs with the correct type
  - All ID access patterns handle their respective types consistently

* **Eager Loading Pattern** - SQLAlchemy's eager loading prevents N+1 query issues:
  - Use `selectinload()` for related entities in CRUD operations
  - Override base methods like `get()` and `get_multi()` to include eager loading
  - Prefer `selectinload()` over `joinedload()` for better performance with collections
  - Apply eager loading consistently across all query methods in a CRUD class

* **Index Optimization** - Use appropriate indexes on frequently queried fields:
  - Foreign keys (e.g., `patient_id`, `clinician_id`)
  - Datetime fields used for filtering (e.g., `appointment_datetime`)
  - Status fields used in queries (e.g., appointment `status`)

### Data Models
* **SQLAlchemy Enum Handling** - Proper enum handling in SQLAlchemy models:
  - Set `native_enum=False` for enum fields to avoid database type mapping issues
  - In CRUD operations, pass the string value (`obj_in.severity.value`) instead of enum object
  - This ensures correct mapping between Python enums and database string values

* **Relationship Definition** - Clear and consistent relationship definitions:
  - One-to-many relationships use `relationship()` on the "one" side
  - Many-to-many relationships use association tables
  - Bidirectional relationships include `back_populates` parameter
  - Cascade deletes configured appropriately for dependent records

## API Patterns

* **API-First Development** - The backend API layer is the core product, with frontend applications as consumers
* **Pydantic Schema Validation** - Use strict Pydantic models for API request/response validation
* **Dependency Injection** - Use FastAPI dependency injection for database sessions and authentication
* **Background Tasks** - Use FastAPI BackgroundTasks for asynchronous operations like event processing and web scraping
* **Azure Blob Storage** - Use for file storage, particularly for patient profile pictures
* **Protected Routes Pattern** - Secure API endpoints using appropriate role-based dependencies
* **Statelessness** - Backend services should be stateless wherever feasible, with token-based session management

### Appointment Management
* **Validation Chain Pattern** - Appointment operations follow a strict validation chain:
  - Time validation (future dates, business hours)
  - Availability checking (no conflicts)
  - Patient limits (maximum appointments per day)
  - Status transition validation (allowed state changes)
  - Cancellation policy enforcement
* **Role-Based Access Control** - Appointment endpoints enforce strict access rules:
  - Patients can only access their own appointments
  - Clinicians can access their patients' appointments
  - Admins have full access to all appointments
* **Status Management** - Appointment status changes follow a defined workflow:
  - Scheduled -> Confirmed -> Completed/Cancelled
  - Cancellation requires proper notice period
  - Status changes are logged and audited
* **Response Structure** - Appointment responses include nested user information:
  - Patient basic info for clinician views
  - Clinician basic info for patient views
  - Full details for admin views

### Audit Logging
* **Comprehensive Audit Pattern** - Critical operations are logged with detailed information:
  - User ID who performed the action
  - Action type (create, update, delete, cancel)
  - Entity type and ID affected
  - Timestamp of the action
  - Previous and new values for updates
  - Reason for action when applicable
* **Error Logging Integration** - Audit logging integrated with error handling:
  - Separate audit entries for successful and failed operations
  - Error details captured in audit logs
  - Background tasks used for async logging operations

## Frontend Patterns

### Authentication UI
* **Shared Authentication Provider** - A common `AuthProvider` component used across frontends:
  - Wraps Clerk's authentication components
  - Exposes a simplified `useAuth` hook
  - Manages token retrieval with correct template
  - Handles authentication state and redirects

### React Router Configuration
* **Protected Route Pattern** - Secure route handling in React Router:
  - Public routes accessible without authentication
  - Protected routes require authentication
  - Role-specific routes check user role
  - Avoid nested index routes that can cause refresh redirection issues
  - Prevent catch-all routes that override specific routes

### UI Components
* **Modular Dashboard Card Pattern** - Dashboard UI composed of modular card components:
  - Each feature area has a dedicated card component (e.g., weight tracking, medication tracking)
  - Cards handle their own data fetching and state management
  - Consistent card styling and layout patterns
  - Responsive design for mobile compatibility

* **Shadcn/UI Component Integration** - Consistent use of shadcn/ui components:
  - Base components like Button, Card, Dialog used throughout
  - Consistent styling applied via Tailwind classes
  - Accessibility considerations incorporated in all components
  - Modal patterns follow responsive width/height guidelines (80vw/80vh)

### Data Handling
* **Direct vs. Wrapped Responses** - Handling both response patterns:
  - Direct array pattern: `response.data as SomeType[]`
  - Wrapped object pattern: `response.data.items as SomeType[]`
  - Proper typing for both patterns

* **Loading/Error State Management** - Consistent handling of async operations:
  - Use separate state variables for loading and error states
  - Display appropriate loading indicators (skeletons, spinners)
  - Show user-friendly error messages
  - Implement empty state handling for lists

## API Response Handling Patterns

### Field Naming Consistency
* **Backend-Frontend Field Mapping** - Field names match between backend and frontend:
  - Backend fields match schema definitions (e.g., `appointment_datetime`)
  - Frontend interfaces match backend fields exactly
  - When renaming fields, update both backend schema and frontend interface
  - Use clear, descriptive field names

### Pydantic Model Configuration
* **Field Aliasing and Population** - When using field aliases in Pydantic models:
  - Always include `populate_by_name=True` in model_config
  - This ensures proper mapping between database fields and response fields
  - Example:
  ```python
  class ResponseModel(BaseModel):
      field_name: str = Field(..., alias="db_field_name")
      
      model_config = ConfigDict(
          from_attributes=True,
          populate_by_name=True
      )
  ```
  - This pattern is particularly important for timestamps (e.g., mapping `created_at` to `reported_at`)

## Documentation Patterns
* **Archiving Convention** - Outdated documentation is archived rather than deleted:
  - Outdated PRDs and prompt templates moved to `.archive/` subdirectories
  - This maintains a clean, current documentation set while preserving history
  - References to archived documents updated in active documentation

* **Memory Bank Organization** - Memory Bank files follow consistent structure:
  - `activeContext.md` - Records recent changes and current focus areas
  - `decisionLog.md` - Tracks architectural and implementation decisions
  - `productContext.md` - Provides high-level product overview
  - `systemPatterns.md` - Documents recurring patterns and standards
  - `progress.md` - Tracks completed, current, and next tasks

* **Backup Strategy** - Regular backups of memory bank:
  - Timestamp-based backup directories (e.g., `backup-2025-04-06`)
  - Complete snapshot of all memory bank files
  - Backups created before major updates or refactoring

*Last updated: 2025-04-14 15:18:14*
