# Multiple Conversations Per Patient Design

## Overview
Enhance the chat system to support multiple distinct conversations between clinicians and patients, allowing better organization and context separation.

## Database Schema Changes

### 1. New Conversation Table
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL REFERENCES patients(id),
    clinician_id UUID NOT NULL REFERENCES clinicians(id),
    title VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active', -- active, archived, resolved
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_message_at TIMESTAMP,
    metadata JSONB
);

CREATE INDEX idx_conversations_patient_clinician ON conversations(patient_id, clinician_id);
CREATE INDEX idx_conversations_status ON conversations(status);
```

### 2. Modify ChatMessage Table
```sql
ALTER TABLE chat_messages 
ADD COLUMN conversation_id UUID REFERENCES conversations(id);

-- Migrate existing messages to default conversations
-- Create one conversation per patient-clinician pair for existing messages
```

## API Changes

### 1. Update Conversation List Endpoint
```python
# GET /api/v1/chat/conversations
{
    "conversations": [
        {
            "conversation_id": "uuid",
            "patient_id": "uuid",
            "patient_first_name": "John",
            "patient_last_name": "Doe",
            "title": "Follow-up: Medication Side Effects",
            "status": "active",
            "last_message_at": "2024-01-15T10:30:00Z",
            "unread_count": 3,
            "created_at": "2024-01-10T08:00:00Z"
        }
    ],
    "total_count": 15,
    "has_more": true
}
```

### 2. Add Create Conversation Endpoint
```python
# POST /api/v1/chat/conversations
{
    "patient_id": "uuid",
    "title": "New Medication Questions",
    "initial_message": "Patient has questions about..."
}
```

### 3. Update Message Sending
```python
# POST /api/v1/chat/messages
{
    "conversation_id": "uuid",  # Now required
    "message": "Message content"
}
```

### 4. Get Conversation Messages
```python
# GET /api/v1/chat/conversations/{conversation_id}/messages
{
    "messages": [...],
    "conversation": {
        "id": "uuid",
        "title": "Follow-up: Medication Side Effects",
        "status": "active"
    }
}
```

## Frontend Changes

### 1. UI Structure
```
Clinician Chat Page
├── Patient List (left sidebar)
│   ├── John Doe
│   │   ├── [+] Start New Conversation
│   │   ├── Initial Consultation (3 unread)
│   │   ├── Follow-up: Medication
│   │   └── Lab Results Discussion
│   └── Jane Smith
│       ├── [+] Start New Conversation
│       └── General Questions
└── Chat Window (main area)
    ├── Conversation Title
    ├── Messages
    └── Input Area
```

### 2. State Management
```typescript
interface ChatState {
    selectedPatientId: string | null;
    selectedConversationId: string | null;
    conversations: ConversationItem[];
    messages: ChatMessage[];
}

interface ConversationItem {
    conversation_id: string;
    patient_id: string;
    patient_name: string;
    title: string;
    status: 'active' | 'archived' | 'resolved';
    last_message_at: string;
    unread_count: number;
}
```

## Implementation Steps

1. **Database Migration**
   - Create conversations table
   - Add conversation_id to chat_messages
   - Migrate existing data

2. **Backend API**
   - Update CRUD operations
   - Modify endpoints
   - Add conversation management

3. **Frontend Updates**
   - Update UI components
   - Modify state management
   - Add conversation creation flow

4. **Testing**
   - Unit tests for new models
   - API endpoint tests
   - UI integration tests

## Benefits

1. **Better Organization**: Messages grouped by topic/context
2. **Improved Context**: Each conversation maintains its own context
3. **Status Tracking**: Mark conversations as resolved/archived
4. **Better UX**: Easier to find specific discussions

## Migration Strategy

1. Create default conversations for existing messages
2. Group by patient-clinician pairs
3. Use first message date as conversation start
4. Title conversations generically (e.g., "Conversation started Jan 2024")

## Future Enhancements

1. Conversation templates
2. Auto-categorization using AI
3. Conversation search
4. Bulk actions (archive, export)
5. Conversation analytics