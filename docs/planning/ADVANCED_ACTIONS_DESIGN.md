# Advanced Actions System - Technical Design Document

## 1. System Overview

The Advanced Actions System extends PulseTrack's LLM capabilities to handle complex, multi-step workflows through natural language. It enables users to perform multiple related actions with a single request, improving efficiency and user experience.

## 2. Architecture

### 2.1 Component Diagram

```
┌─────────────────┐     ┌──────────────────┐     ┌────────────────────┐
│   Chat UI       │────▶│  LLM Actions API │────▶│  Intent Resolver   │
└─────────────────┘     └──────────────────┘     └────────────────────┘
                                 │                           │
                                 ▼                           ▼
                        ┌──────────────────┐      ┌────────────────────┐
                        │ Chain Executor   │◀─────│  ChainedAction     │
                        │    Service       │      │     Schema         │
                        └──────────────────┘      └────────────────────┘
                                 │
                                 ▼
                        ┌──────────────────┐
                        │ Action Executor  │
                        │    Service       │
                        └──────────────────┘
```

### 2.2 Data Flow

1. User sends natural language request
2. Intent Resolver detects compound action
3. Creates ChainedAction object
4. Chain Executor orchestrates execution
5. Individual actions execute via Action Executor
6. Results aggregated and returned
7. UI displays consolidated response

## 3. Core Components

### 3.1 ChainedAction Schema

```python
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum

class ExecutionMode(str, Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"

class FailureMode(str, Enum):
    STOP_ON_FAILURE = "stop_on_failure"
    CONTINUE_ON_FAILURE = "continue_on_failure"

class ActionDependency(BaseModel):
    """Defines dependencies between actions in a chain"""
    depends_on: str  # ID of action this depends on
    parameter_mapping: Dict[str, str] = {}  # Map output params to input

class ChainedIntent(BaseModel):
    """Extended ResolvedIntent for chains"""
    action_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    intent: ResolvedIntent
    dependencies: List[ActionDependency] = []
    condition: Optional[str] = None  # For conditional execution

class ChainedAction(BaseModel):
    """Model for defining action sequences"""
    chain_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    primary_action: ChainedIntent
    follow_up_actions: List[ChainedIntent] = []
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    failure_mode: FailureMode = FailureMode.STOP_ON_FAILURE
    context_passing: bool = True
    metadata: Dict[str, Any] = {}

class ActionResult(BaseModel):
    """Result of a single action in the chain"""
    action_id: str
    action_type: str
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time_ms: float

class ActionChainResult(BaseModel):
    """Result of executing a chain of actions"""
    chain_id: str
    success: bool
    total_actions: int
    successful_actions: int
    failed_actions: int
    execution_time_ms: float
    results: List[ActionResult] = []
    summary: str
    metadata: Dict[str, Any] = {}
```

### 3.2 Action Chain Executor Service

```python
class ActionChainExecutorService:
    """Service for executing chains of related actions"""
    
    def __init__(
        self, 
        db: Session, 
        action_executor: ActionExecutorService,
        cache_service: Optional[CacheService] = None
    ):
        self.db = db
        self.action_executor = action_executor
        self.cache = cache_service
        self.context_store: Dict[str, Dict[str, Any]] = {}
        
    async def execute_action_chain(
        self,
        user_id: str,
        user_role: str,
        action_chain: ChainedAction,
        chat_context: dict = None
    ) -> ActionChainResult:
        """Main entry point for chain execution"""
        
        start_time = time.time()
        chain_context = ChainContext(
            chain_id=action_chain.chain_id,
            user_id=user_id,
            user_role=user_role,
            chat_context=chat_context or {},
            shared_data={}
        )
        
        # Validate chain before execution
        validation_result = await self._validate_chain(action_chain, chain_context)
        if not validation_result.is_valid:
            return self._create_validation_error_result(
                action_chain, validation_result
            )
        
        # Execute based on mode
        if action_chain.execution_mode == ExecutionMode.SEQUENTIAL:
            results = await self._execute_sequential(
                action_chain, chain_context
            )
        else:
            results = await self._execute_parallel(
                action_chain, chain_context
            )
        
        # Generate summary
        execution_time = (time.time() - start_time) * 1000
        return self._create_chain_result(
            action_chain, results, execution_time
        )
    
    async def _execute_sequential(
        self, 
        chain: ChainedAction, 
        context: ChainContext
    ) -> List[ActionResult]:
        """Execute actions in sequence"""
        results = []
        
        # Execute primary action
        primary_result = await self._execute_single_action(
            chain.primary_action, context
        )
        results.append(primary_result)
        
        if not primary_result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
            return results
        
        # Update context with primary results
        if chain.context_passing and primary_result.data:
            context.shared_data[chain.primary_action.action_id] = primary_result.data
        
        # Execute follow-up actions
        for action in chain.follow_up_actions:
            # Check dependencies
            if not self._check_dependencies(action, results):
                results.append(self._create_skipped_result(action, "Dependencies not met"))
                continue
            
            # Check conditions
            if action.condition and not self._evaluate_condition(action.condition, context):
                results.append(self._create_skipped_result(action, "Condition not met"))
                continue
            
            # Execute action
            result = await self._execute_single_action(action, context)
            results.append(result)
            
            # Update context
            if chain.context_passing and result.data:
                context.shared_data[action.action_id] = result.data
            
            # Check failure mode
            if not result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
                break
        
        return results
    
    async def _execute_parallel(
        self, 
        chain: ChainedAction, 
        context: ChainContext
    ) -> List[ActionResult]:
        """Execute actions in parallel"""
        # Execute primary first (always sequential)
        primary_result = await self._execute_single_action(
            chain.primary_action, context
        )
        
        if not primary_result.success and chain.failure_mode == FailureMode.STOP_ON_FAILURE:
            return [primary_result]
        
        # Update context
        if chain.context_passing and primary_result.data:
            context.shared_data[chain.primary_action.action_id] = primary_result.data
        
        # Execute follow-ups in parallel
        tasks = []
        for action in chain.follow_up_actions:
            if self._check_dependencies(action, [primary_result]):
                task = self._execute_single_action(action, context)
                tasks.append(task)
            else:
                tasks.append(self._create_skipped_result_async(action, "Dependencies not met"))
        
        parallel_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        results = [primary_result]
        for i, result in enumerate(parallel_results):
            if isinstance(result, Exception):
                error_result = self._create_error_result(
                    chain.follow_up_actions[i], str(result)
                )
                results.append(error_result)
            else:
                results.append(result)
        
        return results
```

### 3.3 Enhanced Intent Resolver

```python
# Additional prompt for compound action detection
COMPOUND_ACTION_DETECTION_PROMPT = """
Analyze the user's request to determine if it contains multiple related actions.

Types of compound actions to detect:
1. Sequential Actions: "Do X then Y"
   Example: "Report side effects and schedule an appointment"
   
2. Conditional Actions: "If X then Y"
   Example: "If my weight is over 200, schedule a nutrition consultation"
   
3. Batch Actions: Multiple instances of the same action
   Example: "Log my weight: Monday 185, Tuesday 184, Wednesday 183"
   
4. Contextual Actions: Actions that share context
   Example: "Schedule an appointment and send me a reminder the day before"

For compound actions, structure them as:
{
  "is_compound": true,
  "chain_type": "sequential|conditional|batch|contextual",
  "primary_action": { ... },
  "follow_up_actions": [
    {
      "action": { ... },
      "depends_on": "primary_action",
      "condition": null,
      "context_mapping": {
        "appointment_id": "primary_action.data.appointment_id"
      }
    }
  ]
}
"""
```

## 4. Common Pattern Implementations

### 4.1 Appointment + Reminder Pattern

```python
class AppointmentReminderPattern:
    """Handler for appointment scheduling with automatic reminder"""
    
    @staticmethod
    def detect_pattern(intent: ResolvedIntent) -> bool:
        """Check if this pattern applies"""
        keywords = ["remind", "reminder", "notification", "alert"]
        return (
            intent.action_type == "appointment_create" and
            any(keyword in intent.raw_input.lower() for keyword in keywords)
        )
    
    @staticmethod
    def create_chain(intent: ResolvedIntent) -> ChainedAction:
        """Create the action chain"""
        # Extract reminder timing from intent
        reminder_offset = extract_reminder_offset(intent.raw_input)
        
        return ChainedAction(
            primary_action=ChainedIntent(
                intent=intent,
                action_id="appointment_create"
            ),
            follow_up_actions=[
                ChainedIntent(
                    action_id="reminder_create",
                    intent=ResolvedIntent(
                        action_type="notification_create",
                        parameters=[
                            IntentParameterValue(
                                name="notification_type",
                                value="appointment_reminder",
                                confidence=1.0
                            ),
                            IntentParameterValue(
                                name="offset_hours",
                                value=reminder_offset,
                                confidence=0.9
                            )
                        ]
                    ),
                    dependencies=[
                        ActionDependency(
                            depends_on="appointment_create",
                            parameter_mapping={
                                "appointment_id": "data.appointment_id",
                                "appointment_time": "data.appointment_time"
                            }
                        )
                    ]
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            metadata={"pattern": "appointment_reminder"}
        )
```

### 4.2 Side Effect + Follow-up Pattern

```python
class SideEffectFollowupPattern:
    """Auto-schedule appointment for severe side effects"""
    
    SEVERITY_THRESHOLD = SeverityLevel.MAJOR
    
    @staticmethod
    def should_trigger_followup(severity: str) -> bool:
        """Determine if follow-up is needed"""
        severity_map = {
            "minor": 1, "mild": 1,
            "moderate": 2,
            "major": 3, "severe": 3, "serious": 3
        }
        return severity_map.get(severity.lower(), 0) >= 3
    
    @staticmethod
    def create_conditional_chain(intent: ResolvedIntent) -> ChainedAction:
        """Create conditional chain based on severity"""
        return ChainedAction(
            primary_action=ChainedIntent(
                intent=intent,
                action_id="side_effect_report"
            ),
            follow_up_actions=[
                ChainedIntent(
                    action_id="appointment_request",
                    intent=ResolvedIntent(
                        action_type="appointment_request_create",
                        parameters=[
                            IntentParameterValue(
                                name="reason",
                                value="Follow-up for severe side effects",
                                confidence=1.0
                            ),
                            IntentParameterValue(
                                name="urgency",
                                value="high",
                                confidence=1.0
                            )
                        ]
                    ),
                    condition="severity >= 'major'",
                    dependencies=[
                        ActionDependency(
                            depends_on="side_effect_report",
                            parameter_mapping={
                                "side_effect_id": "data.report_id",
                                "symptoms": "parameters.symptoms"
                            }
                        )
                    ]
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            metadata={
                "pattern": "side_effect_followup",
                "auto_triggered": True
            }
        )
```

## 5. Error Handling and Recovery

### 5.1 Error Types

```python
class ChainExecutionError(Exception):
    """Base class for chain execution errors"""
    pass

class DependencyError(ChainExecutionError):
    """Raised when action dependencies are not met"""
    pass

class ConditionError(ChainExecutionError):
    """Raised when conditional evaluation fails"""
    pass

class RollbackError(ChainExecutionError):
    """Raised when rollback fails"""
    pass
```

### 5.2 Rollback Strategy

```python
class ActionRollbackHandler:
    """Handles rollback of failed action chains"""
    
    REVERSIBLE_ACTIONS = {
        "appointment_create": "appointment_cancel",
        "notification_create": "notification_delete",
        "weight_log_create": "weight_log_delete"
    }
    
    async def rollback_chain(
        self,
        chain: ChainedAction,
        results: List[ActionResult],
        context: ChainContext
    ) -> List[ActionResult]:
        """Rollback completed actions in reverse order"""
        rollback_results = []
        
        # Get successful actions in reverse order
        successful_actions = [
            (action, result) 
            for action, result in zip(
                [chain.primary_action] + chain.follow_up_actions,
                results
            )
            if result.success
        ]
        successful_actions.reverse()
        
        for action, original_result in successful_actions:
            if action.intent.action_type in self.REVERSIBLE_ACTIONS:
                rollback_action = self._create_rollback_action(
                    action, original_result
                )
                rollback_result = await self._execute_rollback(
                    rollback_action, context
                )
                rollback_results.append(rollback_result)
        
        return rollback_results
```

## 6. Performance Considerations

### 6.1 Caching Strategy

```python
class ChainCacheService:
    """Caching for action chains"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self.ttl = 3600  # 1 hour
    
    async def get_chain_result(self, chain_hash: str) -> Optional[ActionChainResult]:
        """Get cached chain result"""
        key = f"chain_result:{chain_hash}"
        data = await self.redis.get(key)
        return ActionChainResult.parse_raw(data) if data else None
    
    async def cache_chain_result(self, chain: ChainedAction, result: ActionChainResult):
        """Cache chain result for reuse"""
        # Only cache successful, deterministic chains
        if result.success and self._is_deterministic(chain):
            chain_hash = self._compute_chain_hash(chain)
            key = f"chain_result:{chain_hash}"
            await self.redis.setex(key, self.ttl, result.json())
```

### 6.2 Optimization Techniques

1. **Parallel Execution**: Use when actions are independent
2. **Early Termination**: Stop on critical failures
3. **Result Caching**: Cache deterministic chains
4. **Connection Pooling**: Reuse database connections
5. **Async I/O**: Non-blocking execution

## 7. Monitoring and Observability

### 7.1 Metrics

```python
class ChainMetrics:
    """Metrics for action chain execution"""
    
    # Prometheus metrics
    chain_execution_duration = Histogram(
        'chain_execution_duration_seconds',
        'Time spent executing action chains',
        ['chain_type', 'success']
    )
    
    chain_action_count = Counter(
        'chain_action_total',
        'Total number of actions in chains',
        ['action_type']
    )
    
    chain_failure_rate = Counter(
        'chain_failures_total',
        'Number of failed chains',
        ['failure_reason']
    )
```

### 7.2 Logging

```python
# Structured logging for chains
logger.info("Chain execution started", extra={
    "chain_id": chain.chain_id,
    "user_id": user_id,
    "primary_action": chain.primary_action.intent.action_type,
    "total_actions": len(chain.follow_up_actions) + 1,
    "execution_mode": chain.execution_mode
})
```

## 8. Security Considerations

1. **Authorization**: Verify user permissions for all actions in chain
2. **Rate Limiting**: Apply limits to chain execution
3. **Input Validation**: Validate all parameters in chain
4. **Audit Trail**: Log all actions for compliance
5. **Data Isolation**: Ensure tenant separation in multi-tenant setup

## 9. Testing Strategy

### 9.1 Unit Tests

```python
class TestActionChainExecutor:
    """Unit tests for chain executor"""
    
    async def test_sequential_execution(self):
        """Test sequential chain execution"""
        chain = create_test_chain(ExecutionMode.SEQUENTIAL)
        result = await executor.execute_action_chain(
            "user123", "patient", chain
        )
        assert result.success
        assert len(result.results) == 2
    
    async def test_failure_handling(self):
        """Test failure mode behavior"""
        chain = create_failing_chain(FailureMode.STOP_ON_FAILURE)
        result = await executor.execute_action_chain(
            "user123", "patient", chain
        )
        assert not result.success
        assert result.failed_actions == 1
```

### 9.2 Integration Tests

```python
@pytest.mark.integration
async def test_appointment_reminder_pattern():
    """Test real appointment + reminder chain"""
    # Create chain from natural language
    intent = await resolver.extract_intent(
        template, 
        "Schedule appointment tomorrow at 2pm and remind me in the morning"
    )
    
    # Execute chain
    result = await chain_executor.execute_action_chain(
        user_id="test_patient",
        user_role="patient",
        action_chain=intent.to_chain()
    )
    
    # Verify both actions completed
    assert result.successful_actions == 2
    assert any(r.action_type == "appointment_create" for r in result.results)
    assert any(r.action_type == "notification_create" for r in result.results)
```

## 10. Migration Plan

1. **Phase 1**: Deploy with feature flag (disabled)
2. **Phase 2**: Enable for internal testing
3. **Phase 3**: Beta release to 10% of users
4. **Phase 4**: Monitor and optimize
5. **Phase 5**: Full rollout

## 11. Future Enhancements

1. **Machine Learning**: Learn common patterns from usage
2. **Custom Workflows**: User-defined action templates
3. **External Integrations**: Chain actions across systems
4. **Visual Builder**: GUI for creating custom chains
5. **Analytics Dashboard**: Insights into chain usage patterns