# Advanced Actions Implementation TODO List

## Overview
This document tracks the implementation of the Advanced Actions System for PulseTrack, enabling complex multi-step workflows through natural language.

**Total Estimated Effort**: 2-3 weeks  
**Priority**: High  
**Business Value**: Reduces user friction by combining multiple actions into single requests

---

## Phase 1: Core Infrastructure (Week 1)

### 1. Schema Design and Models
**File**: `backend/app/schemas/action_chain.py`  
**Effort**: 4 hours  
**Priority**: High  
**Dependencies**: None

- [x] Create `ChainedAction` Pydantic model
  - [x] Define `primary_action` field (ResolvedIntent)
  - [x] Define `follow_up_actions` list field
  - [x] Add `execution_mode` enum (sequential/parallel)
  - [x] Add `failure_mode` enum (stop_on_failure/continue_on_failure)
  - [x] Add `context_passing` boolean flag

- [x] Create `ActionChainResult` model
  - [x] Define `success` boolean
  - [x] Define `executed_actions` list
  - [x] Define `failed_actions` list
  - [x] Add `chain_metadata` dict for summary info

- [x] Create `ChainContext` model for passing data between actions
  - [x] Define standard context fields
  - [x] Add type validation

### 2. Action Chain Executor Service
**File**: `backend/app/services/action_chain_executor_service.py`  
**Effort**: 1 day  
**Priority**: High  
**Dependencies**: Schema models

- [x] Implement `ActionChainExecutorService` class
  - [x] Create `__init__` with db session and action executor
  - [x] Implement `execute_action_chain` method
    - [x] Handle sequential execution
    - [x] Handle parallel execution with asyncio
    - [x] Implement context passing between actions
    - [x] Add proper error handling and rollback

- [x] Implement helper methods
  - [x] `_inject_context`: Smart parameter injection (via _apply_context_substitutions)
  - [x] `_generate_chain_summary`: User-friendly summaries
  - [x] `_handle_action_failure`: Failure recovery logic
  - [x] `_validate_chain`: Pre-execution validation

- [x] Add logging and monitoring
  - [x] Log chain execution start/end
  - [x] Track individual action timings
  - [x] Record failure reasons

### 3. Enhanced Intent Resolver
**File**: `backend/app/services/intent_resolver_service.py`  
**Effort**: 1 day  
**Priority**: High  
**Dependencies**: Schema models

- [x] Update `INTENT_EXTRACTION_PROMPT`
  - [x] Add instructions for detecting compound actions
  - [x] Include examples of chained actions
  - [x] Define output format for action chains

- [x] Modify `extract_intent` method
  - [x] Detect multiple action requests
  - [x] Parse action dependencies
  - [x] Return `ChainedAction` when appropriate

- [x] Add chain validation
  - [x] Validate action compatibility
  - [x] Check parameter dependencies
  - [x] Ensure proper sequencing

### 4. API Integration
**File**: `backend/app/api/v1/endpoints/llm_actions.py`  
**Effort**: 4 hours  
**Priority**: High  
**Dependencies**: Executor service, Intent resolver

- [x] Update `process_text_action` endpoint
  - [x] Check if resolved intent is a chain
  - [x] Route to chain executor when needed
  - [x] Format chain results appropriately

- [x] Add chain-specific response handling
  - [x] Include all action results
  - [x] Provide clear success/failure summary
  - [x] Return actionable error messages

---

## Phase 2: Common Patterns (Week 2)

### 5. Appointment + Reminder Pattern
**Effort**: 4 hours  
**Priority**: Medium  
**Dependencies**: Core infrastructure

- [ ] Create pattern handler in action executor
  - [ ] Detect appointment creation success
  - [ ] Auto-create reminder notification
  - [ ] Pass appointment details to reminder

- [ ] Update templates to recognize pattern
- [ ] Add pattern-specific prompts

### 6. Side Effect + Follow-up Pattern
**Effort**: 4 hours  
**Priority**: Medium  
**Dependencies**: Core infrastructure

- [ ] Implement severity-based auto-scheduling
  - [ ] Check severity in side effect report
  - [ ] Trigger appointment request for severe cases
  - [ ] Include side effect details in appointment reason

- [ ] Add configuration for severity thresholds
- [ ] Create appropriate user messages

### 7. Batch Operations
**Effort**: 6 hours  
**Priority**: Medium  
**Dependencies**: Core infrastructure

- [ ] Implement batch weight logging
  - [ ] Parse multiple weight entries
  - [ ] Execute as parallel actions
  - [ ] Aggregate results

- [ ] Add batch support for other actions
  - [ ] Medication requests
  - [ ] Side effect reports
  - [ ] Appointment scheduling

### 8. Frontend UI Updates
**File**: `frontend-patient/src/components/chat/ChainedActionResponse.tsx`  
**Effort**: 6 hours  
**Priority**: Medium  
**Dependencies**: Backend implementation

- [x] Create `ChainedActionResponse` component
  - [x] Display summary card
  - [x] Show individual action results
  - [x] Add success/failure indicators
  - [ ] Include retry options for failures

- [x] Update chat message handler
  - [x] Detect chained action responses
  - [x] Route to appropriate component
  - [x] Maintain conversation flow

- [ ] Add loading states
  - [ ] Show progress for long chains
  - [ ] Indicate which action is executing

---

## Phase 3: Advanced Features (Week 3)

### 9. Conditional Actions
**Effort**: 1 day  
**Priority**: Low  
**Dependencies**: Core infrastructure

- [ ] Implement condition evaluation
  - [ ] Define condition syntax
  - [ ] Add data fetching for conditions
  - [ ] Execute actions based on results

- [ ] Common conditions
  - [ ] Weight thresholds
  - [ ] Time-based conditions
  - [ ] Side effect severity checks

### 10. Action Rollback
**Effort**: 6 hours  
**Priority**: Low  
**Dependencies**: All patterns

- [ ] Implement rollback mechanism
  - [ ] Track reversible actions
  - [ ] Define rollback procedures
  - [ ] Handle partial rollbacks

- [ ] Add user confirmation for rollbacks
- [ ] Log rollback operations

### 11. Testing Suite
**Effort**: 1 day  
**Priority**: High  
**Dependencies**: All features

- [ ] Unit tests
  - [ ] Schema validation tests
  - [ ] Executor service tests
  - [ ] Intent resolver tests
  - [ ] Individual pattern tests

- [ ] Integration tests
  - [ ] End-to-end chain execution
  - [ ] Error scenario testing
  - [ ] Performance tests
  - [ ] Concurrent execution tests

- [ ] UI tests
  - [ ] Component rendering
  - [ ] User interaction flows
  - [ ] Error display

### 12. Documentation
**Effort**: 4 hours  
**Priority**: Medium  
**Dependencies**: All features

- [ ] API documentation
  - [ ] Update OpenAPI specs
  - [ ] Document chain request format
  - [ ] Add example requests/responses

- [ ] Developer guide
  - [ ] How to add new patterns
  - [ ] Chain configuration options
  - [ ] Debugging chains

- [ ] User documentation
  - [ ] Supported chain patterns
  - [ ] Example commands
  - [ ] Troubleshooting guide

---

## Testing Checklist

### Manual Testing Scenarios
- [ ] Simple chain: "Report headache and schedule appointment"
- [ ] Parallel execution: "Log weight for Mon, Tue, Wed"
- [ ] Conditional: "If weight > 200, schedule nutrition consult"
- [ ] Error handling: Invalid appointment date in chain
- [ ] Partial success: One action fails in chain
- [ ] Context passing: Appointment ID used in reminder

### Performance Targets
- [ ] Chain with 2 actions: < 2 seconds
- [ ] Chain with 5 actions: < 4 seconds
- [ ] Parallel batch of 10: < 3 seconds

### Edge Cases
- [ ] Empty follow-up actions
- [ ] Circular dependencies
- [ ] Missing required parameters
- [ ] Database rollback scenarios
- [ ] Concurrent chain execution

---

## Success Metrics

### Technical Metrics
- [ ] Test coverage > 90%
- [ ] Response time < 2s for typical chains
- [ ] Error rate < 1%
- [ ] Rollback success rate > 99%

### Business Metrics
- [ ] User adoption rate
- [ ] Actions per conversation increase
- [ ] Support ticket reduction
- [ ] User satisfaction scores

---

## Rollout Plan

### Week 1: Internal Testing
- Deploy to staging environment
- Internal team testing
- Performance optimization

### Week 2: Beta Release
- Limited user rollout (10%)
- Monitor error rates
- Gather feedback

### Week 3: Full Release
- Gradual rollout to all users
- Marketing announcement
- Support team training

---

## Notes and Considerations

1. **Security**: Ensure proper authorization checks for chained actions
2. **Rate Limiting**: Consider impact of multiple actions on API limits
3. **Audit Trail**: Maintain clear logs for compliance
4. **Backward Compatibility**: Ensure single actions still work
5. **Feature Flags**: Use flags for gradual rollout

---

## Quick Reference

### File Locations
- Schemas: `backend/app/schemas/action_chain.py`
- Executor: `backend/app/services/action_chain_executor_service.py`
- Intent Resolver: `backend/app/services/intent_resolver_service.py`
- API Endpoint: `backend/app/api/v1/endpoints/llm_actions.py`
- UI Component: `frontend-patient/src/components/chat/ChainedActionResponse.tsx`

### Key Classes
- `ChainedAction`: Defines action sequence
- `ActionChainResult`: Execution result
- `ActionChainExecutorService`: Executes chains
- `ChainedActionResponse`: UI component

### Common Patterns
1. Appointment + Reminder
2. Side Effect + Follow-up
3. Batch Weight Logging
4. Conditional Scheduling