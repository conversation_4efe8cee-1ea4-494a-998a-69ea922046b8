# Chat System Testing Guide & Known Issues
*Generated: 2025-05-26*

## Quick Test Scenarios

### 1. Intent Resolution Testing

#### ✅ Should Work:
```
Patient: "I need to log my weight as 185 pounds"
Patient: "Can I schedule an appointment for next Tuesday?"
Patient: "I'm having nausea from my medication"
Clinician: "Schedule <PERSON> Smith for tomorrow at 2pm"
```

#### ⚠️ Edge Cases to Test:
```
"I weighed 185 this morning" (missing unit)
"Book me next week" (ambiguous time)
"I feel sick" (vague symptoms)
"Schedule Emma for 2" (missing AM/PM)
```

#### ❌ Known Limitations:
```
"Log 185 and schedule for Tuesday" (multi-intent)
"My weight is the same as yesterday" (relative reference)
"Cancel my appointment and reschedule" (complex workflow)
```

### 2. RAG Retrieval Testing

#### Test Queries by Category:

**Clinic Information (Should use RAG):**
- "What are your clinic hours?"
- "Do you accept Blue Cross insurance?"
- "Where is the clinic located?"
- "What services do you offer?"

**Clinical Guidance (Should NOT use RAG):**
- "What's the typical dosage for Ozempic?"
- "Are there interactions with metformin?"
- "What are common side effects?"

**Edge Cases:**
- "Can <PERSON><PERSON> prescribe Ozempic?" (mixed: clinic + clinical)
- "What insurance covers semaglutide?" (mixed: insurance + medication)

### 3. Action Execution Testing

#### Complete Flows:
```bash
# Test weight logging
"Log weight 175 pounds"
→ Should create weight entry for today

# Test appointment request
"I need an appointment next Monday at 10am"
→ Should create appointment request
```

#### Parameter Collection:
```bash
# Missing parameters
User: "Schedule an appointment"
Bot: "What date would you like?"
User: "Tomorrow"
Bot: "What time?"
User: "2pm"
→ Should complete after collecting all params
```

### 4. Fallback Testing

#### LLM Failures:
```bash
# Simulate by:
1. Invalid API key
2. Rate limiting
3. Network timeout
```

#### RAG Failures:
```bash
# Test with:
1. Queries about non-existent services
2. Misspelled clinic names
3. Questions in other languages
```

## Known Issues & Workarounds

### 1. **Timezone Handling**
**Issue**: Appointments may be scheduled in wrong timezone
**Test**: 
```
"Schedule for 3pm tomorrow" (from different timezone)
```
**Workaround**: System tries to detect timezone from context

### 2. **Parameter Ambiguity**
**Issue**: System may misinterpret ambiguous inputs
**Examples**:
- "Next Friday" (which Friday?)
- "Dr. Smith" (multiple Dr. Smiths?)
- "My usual medication" (undefined reference)

### 3. **Multi-Turn State Loss**
**Issue**: Context may be lost between messages
**Test**:
```
User: "I need to schedule an appointment"
User: "Actually, make it for my wife instead"
→ System may not remember the appointment context
```

### 4. **RAG Similarity Threshold**
**Issue**: Low threshold (0.3) may return irrelevant results
**Test**:
```
"What's your fax number?" 
→ May return general contact info even if no fax mentioned
```

## Performance Testing

### Load Testing Scenarios:

1. **Concurrent Intent Resolution**
```python
# Test 50 concurrent intent resolutions
for i in range(50):
    asyncio.create_task(
        resolve_intent("Schedule appointment tomorrow 2pm")
    )
```

2. **RAG Cache Effectiveness**
```python
# Same query multiple times
queries = ["clinic hours"] * 100
# First should be slow, rest should be <50ms
```

3. **Action Execution Under Load**
```python
# Multiple users creating appointments simultaneously
# Check for:
- Double bookings
- Race conditions
- Database locks
```

## Testing Checklist

### Pre-Demo Testing:

- [ ] **Intent Resolution**
  - [ ] All 5 intent types work correctly
  - [ ] Parameter validation catches bad inputs
  - [ ] Timezone conversion is accurate
  
- [ ] **RAG Retrieval**
  - [ ] Clinic-specific queries return relevant content
  - [ ] No leakage between clinics
  - [ ] Fallback messages are appropriate
  
- [ ] **Action Execution**
  - [ ] All actions complete successfully
  - [ ] Error states show helpful messages
  - [ ] Permissions are enforced correctly
  
- [ ] **Fallbacks**
  - [ ] LLM failures handled gracefully
  - [ ] Network issues don't crash system
  - [ ] Users can recover from errors

### Stress Points to Monitor:

1. **Memory Usage**: Check for leaks in long conversations
2. **Response Times**: Should stay <2s under normal load
3. **Cache Hit Rates**: RAG cache should be >80% for common queries
4. **Error Rates**: <5% for intent resolution, <10% for actions

## Debug Commands

### Check Intent Resolution:
```bash
# Test intent detection directly
curl -X POST http://localhost:8000/api/v1/chat/detect-intent \
  -H "Content-Type: application/json" \
  -d '{"message": "I need to log my weight as 180 pounds"}'
```

### Test RAG Retrieval:
```bash
# Query content chunks
curl -X GET "http://localhost:8000/api/v1/content/search?query=clinic+hours&clinic_id=<clinic_id>"
```

### Monitor Chat Flow:
```bash
# Watch chat logs
docker-compose logs -f backend | grep -E "(intent|action|RAG)"
```

## Common Fixes

### Intent Not Recognized:
1. Check if template exists for user role
2. Verify action is in INTENT_SCHEMAS
3. Look for LLM parsing errors in logs

### RAG Returns Wrong Content:
1. Verify clinic_id is correct
2. Check if content was properly chunked
3. Test with higher similarity threshold

### Action Fails to Execute:
1. Check user permissions (RBAC)
2. Verify all required parameters present
3. Look for database constraints violations

### Conversation Falls Back Unexpectedly:
1. Check confidence scores in intent detection
2. Verify LLM provider is responding
3. Look for timeout issues (>10s)

## Testing Scripts

### 1. Full Chat Flow Test:
```python
# backend/scripts/test_chat_integration.py
async def test_full_chat_flow():
    # Test intent detection
    # Test parameter collection  
    # Test action execution
    # Test response generation
    # Verify database state
```

### 2. RAG Accuracy Test:
```python
# backend/scripts/test_rag_accuracy.py
def test_rag_accuracy():
    test_queries = [
        ("clinic hours", "should_find_hours"),
        ("parking information", "should_find_parking"),
        ("staff directory", "should_find_staff")
    ]
    # Calculate precision/recall
```

### 3. Fallback Simulation:
```python
# backend/scripts/test_fallbacks.py
async def test_all_fallbacks():
    # Simulate LLM timeout
    # Simulate invalid API key
    # Simulate empty RAG results
    # Verify graceful degradation
```

## Monitoring Dashboard Queries

### Key Metrics to Track:
```sql
-- Intent resolution success rate
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_intents,
    SUM(CASE WHEN confidence > 0.7 THEN 1 ELSE 0 END) as high_confidence,
    AVG(confidence) as avg_confidence
FROM intent_resolutions
GROUP BY DATE(created_at);

-- Action execution success rate  
SELECT
    action_type,
    COUNT(*) as attempts,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successes,
    AVG(execution_time_ms) as avg_time_ms
FROM action_executions
GROUP BY action_type;

-- RAG retrieval effectiveness
SELECT
    DATE(created_at) as date,
    AVG(chunks_returned) as avg_chunks,
    AVG(max_similarity_score) as avg_similarity,
    COUNT(DISTINCT query_hash) as unique_queries
FROM rag_retrievals
GROUP BY DATE(created_at);
```

## Pre-Demo Validation

Run this checklist 24 hours before demo:

1. **Clear all caches**: Redis FLUSHALL
2. **Seed fresh demo data**: Run all seeding scripts
3. **Test each demo scenario**: Use exact demo script
4. **Monitor error logs**: Zero errors in last hour
5. **Check response times**: All <2s
6. **Verify fallbacks**: Disconnect services and test
7. **Load test**: 10 concurrent users for 10 minutes
8. **Backup database**: In case of demo day issues