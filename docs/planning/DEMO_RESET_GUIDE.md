# Demo Reset Guide

## Overview
The demo reset capability provides a one-click solution to reset the PulseTrack demo environment to a pristine state for investor demonstrations.

## Quick Start

### Command Line Reset
```bash
# From backend directory (inside Docker container)
docker-compose exec backend python scripts/demo_reset.py

# With auto-confirmation (no prompt)
echo "RESET" | docker-compose exec -T backend python scripts/demo_reset.py
```

### Admin UI Reset (Coming Soon)
1. Navigate to Admin Panel: http://localhost:5175
2. Click "Demo Reset" in the navigation
3. Review current data counts
4. Click "Reset Demo Environment"
5. Confirm the action

## What It Does

### Preserves
- Dr. <PERSON> (Clinician) - `user_2waSREJSlduBPyK6Vbv9TU3VhI7`
- <PERSON> (Patient) - `user_2waTCuGL3kQC9k2rY47INdcJXk5`
- Edinburgh Weight Loss Clinic - `385af354-bfe1-4ead-8651-92110e698e30`
- All medications and system configuration

### Clears
- All other patients
- All appointments, chat messages, clinical notes
- All weight logs, side effect reports
- All medication requests
- All patient alerts and event logs

### Reseeds
- Fresh demo data with recent timestamps
- <PERSON>'s critical scenario (37 lbs in 30 days)
- AI-detected urgent alerts
- Routine clinician-patient conversations
- Normal patient data for contrast

## Demo Scenario Highlights

After reset, the demo shows:

1. **Critical Patient Detection**
   - Michael lost 37 lbs in 30 days (dangerous rate)
   - Mentioned serious symptoms to AI but not to clinician
   - AI detected pattern and created CRITICAL alert

2. **Rich Conversation History**
   - 32 realistic chat messages
   - Mix of AI and clinician interactions
   - Clinical guidance conversations

3. **Comprehensive Data**
   - 36 weight log entries showing concerning patterns
   - 4 escalating side effect reports
   - 6 appointments with various statuses

## Safety Features

- **Environment Check**: Won't run in production
- **Confirmation Required**: Must type "RESET" to proceed
- **Transaction Safety**: All operations in database transactions
- **Detailed Logging**: Shows exactly what was deleted/created

## Troubleshooting

### "Cannot connect to database"
- Ensure you're running inside the Docker container
- Check that the database container is running: `docker-compose ps`

### "Demo accounts not found"
- The reset requires the three core accounts to exist
- Run `seed_demo_data.py` first if accounts are missing

### "Foreign key constraint error"
- The script handles dependencies properly
- If error persists, check for custom data relationships

## Best Practices

1. **Run between demos** for consistency
2. **Test after reset** to ensure everything works
3. **Check timestamps** - all data uses recent dates
4. **Review alerts** - ensure AI-generated alert appears

## API Endpoints

- `GET /api/v1/demo/status` - Check demo environment status
- `POST /api/v1/demo/reset` - Trigger reset (requires admin auth)

## Implementation Notes

- Uses `DemoDataSeeder` class for consistent data
- Runs `seed_investor_demo_scenario.py` for critical patient
- All enums use uppercase values (PATIENT, AGENT, CLINICIAN)
- Preserves specific demo user IDs hardcoded in script