# RAG Enhancement Plan: Intelligent Document Q&A
*Generated: 2025-05-26*

## Executive Summary

The current RAG implementation in PulseTrack retrieves relevant content but doesn't effectively enable users to have intelligent conversations with documents. This plan outlines how to transform the system from simple retrieval to true document Q&A capabilities.

## Current State Analysis

### What Works ✅
- Vector similarity search with pgvector
- Clinic-specific content filtering
- Basic chunk retrieval (top 3)
- Caching for performance

### What's Missing ❌
- Full document context not passed to LLM
- Chunks are truncated to 150 chars
- No document-aware conversation flow
- Limited metadata utilization
- No citation/source tracking

## Proposed Enhancements

### 1. Pass Full Retrieved Content to LLM

**Current Issue**: Chunks are truncated to 150 characters, losing critical context.

**Solution**: Pass complete chunks with intelligent context windowing.

```python
# Enhanced context_enricher.py
def format_context_for_prompt(context: dict[str, Any], max_tokens: int = 2000) -> str:
    """Format RAG context with full chunks and metadata."""
    rag_chunks = context.get("rag_chunks") or context.get("rag_context_chunks")
    
    if not rag_chunks:
        return ""
    
    # Build comprehensive context
    formatted_context = "## Relevant Information from Knowledge Base\n\n"
    
    for i, chunk in enumerate(rag_chunks[:5], 1):  # Increase to top 5 chunks
        # Include full chunk text
        formatted_context += f"### Source {i}\n"
        formatted_context += f"**Content**: {chunk['content']}\n"
        formatted_context += f"**Source**: {chunk['source_title']}\n"
        formatted_context += f"**Page**: {chunk['page_number']}\n"
        formatted_context += f"**Relevance**: {chunk['similarity_score']:.2f}\n\n"
    
    return formatted_context
```

### 2. Implement Document-Aware Chat Module

Create a dedicated module for document Q&A:

```python
# New file: app/services/chat_modules/document_qa_module.py
class DocumentQAModule(BaseChatModule):
    """Specialized module for intelligent document Q&A."""
    
    async def process(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        # Get enhanced RAG context
        rag_metadata = context.get("rag_chunks_metadata", [])
        
        if not rag_metadata:
            return {
                "response": "I couldn't find relevant information. Try rephrasing your question.",
                "metadata": {"module": "document_qa", "chunks_found": 0}
            }
        
        # Build comprehensive prompt
        system_prompt = """You are an AI assistant helping users understand medical documents and clinic information.
        
        When answering questions:
        1. Use ONLY information from the provided sources
        2. Cite specific sources when making claims
        3. If information isn't in the sources, say so
        4. Synthesize information from multiple sources when relevant
        5. Maintain medical accuracy and avoid speculation
        """
        
        # Include full RAG context
        user_prompt = f"""
        Question: {message}
        
        Available Information:
        {self._format_full_rag_context(rag_metadata)}
        
        Please provide a comprehensive answer based on these sources.
        Include citations in the format [Source X] where X is the source number.
        """
        
        response = await self.llm_provider.get_completion(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            temperature=0.3  # Lower temperature for factual accuracy
        )
        
        return {
            "response": response,
            "metadata": {
                "module": "document_qa",
                "chunks_used": len(rag_metadata),
                "sources": [chunk["source_title"] for chunk in rag_metadata]
            }
        }
```

### 3. Enhanced RAG Retrieval Strategy

**Implement multi-stage retrieval for better context:**

```python
# Enhanced crud_content_chunk.py
class CRUDContentChunk:
    def get_enhanced_similar_chunks(
        self,
        db: Session,
        query_embedding: List[float],
        clinic_id: Optional[UUID] = None,
        similarity_threshold: float = 0.3,
        initial_limit: int = 10,  # Retrieve more initially
        rerank_limit: int = 5,    # Return top 5 after reranking
        include_context_window: bool = True
    ) -> List[ContentChunkWithContext]:
        
        # Stage 1: Initial retrieval (broader)
        initial_chunks = self.get_similar_chunks(
            db, query_embedding, clinic_id, 
            similarity_threshold, initial_limit
        )
        
        # Stage 2: Rerank using cross-encoder or more sophisticated scoring
        reranked_chunks = self._rerank_chunks(initial_chunks, query_embedding)
        
        # Stage 3: Expand context window for top chunks
        if include_context_window:
            enhanced_chunks = []
            for chunk in reranked_chunks[:rerank_limit]:
                # Get neighboring chunks from same document
                context_chunks = self._get_context_window(
                    db, chunk, window_size=1  # 1 chunk before/after
                )
                enhanced_chunks.append({
                    "main_chunk": chunk,
                    "context": context_chunks,
                    "full_text": self._merge_chunks(context_chunks)
                })
            return enhanced_chunks
        
        return reranked_chunks[:rerank_limit]
```

### 4. Implement Conversation Memory for Documents

**Enable multi-turn document Q&A:**

```python
# Enhanced chat_agent.py
class ChatAgent:
    async def process_chat_message(self, message: str, context: Dict) -> Dict:
        # Check if this is a follow-up question
        conversation_history = context.get("conversation_history", [])
        
        # Determine if we should use previous RAG context
        if self._is_followup_question(message, conversation_history):
            # Reuse previous RAG context for efficiency
            previous_rag_context = self._get_previous_rag_context(conversation_history)
            if previous_rag_context:
                context["rag_chunks_metadata"] = previous_rag_context
                context["using_previous_context"] = True
        else:
            # Perform new RAG retrieval
            enriched_context = await enrich_with_rag(
                db, context["user_id"], message, context["user_role"]
            )
            context.update(enriched_context)
        
        # Route to appropriate module
        if self._is_document_question(message, context):
            return await self.document_qa_module.process(message, context)
```

### 5. Smart Document Detection

**Intelligently route document-related queries:**

```python
# Enhanced chatbot_manager.py
class ChatbotManager:
    DOCUMENT_INDICATORS = [
        "document", "pdf", "file", "page", "section", "chapter",
        "material", "handout", "guide", "according to", "says",
        "mentions", "states", "explains", "describes"
    ]
    
    def _should_use_document_qa(self, message: str, context: Dict) -> bool:
        """Determine if query is about document content."""
        message_lower = message.lower()
        
        # Check for document indicators
        has_doc_indicator = any(ind in message_lower for ind in self.DOCUMENT_INDICATORS)
        
        # Check if education materials are involved
        has_education_context = bool(context.get("education_material_id"))
        
        # Check if previous RAG retrieval found documents
        has_rag_results = len(context.get("rag_chunks_metadata", [])) > 0
        
        return has_doc_indicator or has_education_context or has_rag_results
```

### 6. Citation and Source Tracking

**Provide clear attribution:**

```python
# New schema: schemas/document_qa.py
class DocumentQAResponse(BaseModel):
    answer: str
    citations: List[Citation]
    confidence: float
    sources_used: List[SourceInfo]
    
class Citation(BaseModel):
    text: str
    source_id: UUID
    page_number: Optional[int]
    chunk_id: UUID
    
class SourceInfo(BaseModel):
    title: str
    source_type: str  # "education_material", "scraped_page", "clinic_document"
    url: Optional[str]
    last_updated: datetime
```

### 7. Enhanced Prompt Templates

**Optimize prompts for different document types:**

```python
DOCUMENT_QA_PROMPTS = {
    "education_material": """
    You are answering questions about educational health materials.
    Focus on:
    - Medical accuracy
    - Patient-friendly explanations
    - Actionable advice
    - Safety considerations
    """,
    
    "clinic_info": """
    You are answering questions about clinic services and policies.
    Focus on:
    - Specific clinic details
    - Accurate hours, locations, costs
    - Insurance and payment information
    - Contact details
    """,
    
    "clinical_guidelines": """
    You are answering questions about clinical guidelines.
    Focus on:
    - Evidence-based recommendations
    - Dosing and administration
    - Contraindications and warnings
    - Monitoring requirements
    """
}
```

### 8. Frontend Integration

**Enhance UI for document Q&A:**

```typescript
// Enhanced ChatPage.tsx
interface DocumentQAMessage extends ChatMessage {
  citations?: Citation[];
  sourcesUsed?: SourceInfo[];
  documentContext?: boolean;
}

// Render citations in chat
const renderMessageWithCitations = (message: DocumentQAMessage) => {
  return (
    <div className="message-container">
      <div className="message-content">
        {message.content}
      </div>
      {message.citations && (
        <div className="citations">
          <h4>Sources:</h4>
          {message.citations.map((citation, idx) => (
            <div key={idx} className="citation">
              <span className="citation-number">[{idx + 1}]</span>
              <span className="citation-text">{citation.text}</span>
              <span className="citation-source">
                - {citation.source_title}, p.{citation.page_number}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

## Implementation Roadmap

### Phase 1: Core Enhancements (3-5 days)
1. ✅ Modify `context_enricher.py` to pass full chunks
2. ✅ Update `format_context_for_prompt()` to include metadata
3. ✅ Create `DocumentQAModule` for specialized handling
4. ✅ Update prompt templates for document Q&A

### Phase 2: Advanced Retrieval (5-7 days)
1. ✅ Implement context window expansion
2. ✅ Add reranking capability
3. ✅ Build conversation memory for documents
4. ✅ Enhance similarity search with hybrid approach

### Phase 3: UI/UX Improvements (3-5 days)
1. ✅ Add citation rendering in chat UI
2. ✅ Create document viewer integration
3. ✅ Show source reliability indicators
4. ✅ Add "View Source" functionality

### Phase 4: Optimization (2-3 days)
1. ✅ Tune retrieval parameters
2. ✅ A/B test different prompt templates
3. ✅ Implement feedback collection
4. ✅ Add performance monitoring

## Expected Outcomes

### Before Enhancement
- Users get vague responses about documents
- Limited context from 150-char snippets
- No source attribution
- Single-turn conversations only

### After Enhancement
- Comprehensive answers with full document context
- Clear citations and source tracking
- Multi-turn document conversations
- Ability to ask follow-up questions
- Integration with document viewer

## Example Interactions

### Current System:
```
User: "What does the weight loss guide say about side effects?"
Bot: "Relevant information from clinic materials:
- Common side effects include nausea, vomiting..."
```

### Enhanced System:
```
User: "What does the weight loss guide say about side effects?"
Bot: "According to the GLP-1 Medication Guide, the most common side effects include:

1. **Gastrointestinal effects** [Source 1, Page 3]:
   - Nausea (affecting 20-30% of patients)
   - Vomiting (10-15% of patients)
   - Diarrhea or constipation (15-20% of patients)

2. **Management strategies** [Source 1, Page 5]:
   - Start with a low dose and gradually increase
   - Take medication with food to reduce nausea
   - Stay hydrated and eat smaller meals

The guide emphasizes that most side effects are mild and temporary, typically improving within 4-8 weeks [Source 1, Page 6]. However, you should contact your clinician if you experience severe or persistent symptoms.

Would you like to know more about managing specific side effects?"
```

## Success Metrics

1. **Retrieval Accuracy**: Increase F1 score from 0.79 to 0.90+
2. **Answer Completeness**: 90%+ of answers include citations
3. **User Satisfaction**: 85%+ positive feedback on document Q&A
4. **Context Utilization**: Use 80%+ of retrieved chunk content
5. **Follow-up Success**: 75%+ successful multi-turn conversations

## Risk Mitigation

1. **Context Length**: Monitor token usage to avoid LLM limits
2. **Hallucination**: Strict instruction to use only provided sources
3. **Performance**: Implement aggressive caching for common queries
4. **Privacy**: Ensure clinic-specific content isolation

## Conclusion

These enhancements will transform PulseTrack's RAG system from basic retrieval to intelligent document Q&A, enabling users to have meaningful conversations with their medical documents and clinic information. The phased approach ensures quick wins while building toward a comprehensive solution.