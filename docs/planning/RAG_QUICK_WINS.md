# RAG Quick Wins: Immediate Improvements
*Implementation time: 2-3 hours*

## Quick Win #1: Pass Full Chunks to LLM (30 mins)

### File: `backend/app/utils/context_enricher.py`

Replace the current `format_context_for_prompt` function:

```python
def format_context_for_prompt(context: dict[str, Any], max_tokens: int = 2000) -> str:
    """
    Format context information for inclusion in LLM prompts.
    Now includes FULL chunk content instead of 150-char truncation.
    """
    formatted_parts = []
    
    # Format RAG chunks with full content
    rag_chunks = context.get("rag_chunks") or context.get("rag_context_chunks", [])
    rag_metadata = context.get("rag_chunks_metadata", [])
    
    if rag_chunks:
        formatted_parts.append("=== Relevant Information from Knowledge Base ===\n")
        
        # Use metadata if available for richer context
        if rag_metadata and len(rag_metadata) == len(rag_chunks):
            for i, (chunk, metadata) in enumerate(zip(rag_chunks, rag_metadata), 1):
                formatted_parts.append(f"\n--- Source {i} ---")
                formatted_parts.append(f"Content: {chunk}")  # FULL content, not truncated
                
                # Add source information if available
                if metadata.get("source_title"):
                    formatted_parts.append(f"Source: {metadata['source_title']}")
                if metadata.get("page_number"):
                    formatted_parts.append(f"Page: {metadata['page_number']}")
                if metadata.get("similarity_score"):
                    formatted_parts.append(f"Relevance: {metadata['similarity_score']:.2%}")
                formatted_parts.append("")  # Empty line between chunks
        else:
            # Fallback to simple formatting
            for i, chunk in enumerate(rag_chunks, 1):
                formatted_parts.append(f"\n--- Information Chunk {i} ---")
                formatted_parts.append(chunk)  # FULL content
                formatted_parts.append("")
    
    # Join all parts and manage token limit
    full_context = "\n".join(formatted_parts)
    
    # Implement smart truncation if needed (but try to avoid it)
    if len(full_context) > max_tokens * 4:  # Rough estimate: 1 token ≈ 4 chars
        # Truncate intelligently by removing less relevant chunks
        truncated_parts = formatted_parts[:int(len(formatted_parts) * 0.7)]
        truncated_parts.append("\n[Additional context truncated for length...]")
        full_context = "\n".join(truncated_parts)
    
    return full_context.strip()
```

## Quick Win #2: Increase Chunk Retrieval Limit (15 mins)

### File: `backend/app/crud/crud_content_chunk.py`

Update the retrieval method to get more chunks:

```python
def get_similar_chunks(
    self,
    db: Session,
    query_embedding: List[float],
    clinic_id: Optional[UUID] = None,
    similarity_threshold: float = 0.3,
    limit: int = 5  # Increased from 3 to 5
) -> List[ContentChunk]:
    """Get similar content chunks based on embedding similarity."""
    # Calculate similarity using pgvector
    similarity = func.cosine_distance(ContentChunk.embedding, query_embedding)
    
    query = db.query(
        ContentChunk,
        (1 - similarity).label("similarity_score")  # Add similarity score
    ).filter(
        (1 - similarity) > similarity_threshold
    )
    
    # Filter by clinic if provided
    if clinic_id:
        query = query.join(ScrapedPage).filter(
            or_(
                ScrapedPage.clinic_id == clinic_id,
                ScrapedPage.is_public == True
            )
        )
    
    # Order by similarity and limit
    results = query.order_by(desc("similarity_score")).limit(limit).all()
    
    # Attach similarity score to each chunk for later use
    for chunk, score in results:
        chunk.similarity_score = score
    
    return [chunk for chunk, _ in results]
```

### File: `backend/app/utils/context_enricher.py`

Update the enrichment function to include similarity scores:

```python
async def enrich_with_rag(
    db: Session,
    user_id: str,
    message: str,
    user_role: str = "patient"
) -> Dict[str, Any]:
    """Enrich context with RAG retrieval including similarity scores."""
    try:
        # ... existing code ...
        
        # Get chunks with similarity scores
        content_chunks = crud_content_chunk.get_similar_chunks(
            db=db,
            query_embedding=query_embedding,
            clinic_id=clinic_id,
            similarity_threshold=settings.RAG_SIMILARITY_THRESHOLD,
            limit=5  # Increased from 3
        )
        
        if content_chunks:
            # Format chunks with metadata
            rag_chunks_metadata = []
            for chunk in content_chunks:
                metadata = {
                    "chunk_id": str(chunk.id),
                    "content": chunk.content,
                    "source_id": str(chunk.scraped_page_id),
                    "source_title": chunk.scraped_page.title if chunk.scraped_page else "Unknown",
                    "page_number": chunk.metadata.get("page", 1) if chunk.metadata else None,
                    "similarity_score": getattr(chunk, 'similarity_score', 0.0),
                    "url": chunk.scraped_page.url if chunk.scraped_page else None
                }
                rag_chunks_metadata.append(metadata)
            
            # Sort by similarity score (highest first)
            rag_chunks_metadata.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            return {
                "rag_context_chunks": [m["content"] for m in rag_chunks_metadata],
                "rag_chunks_metadata": rag_chunks_metadata,
                "rag_scraped_pages": []  # Can be populated if needed
            }
```

## Quick Win #3: Create Document Q&A Prompt (45 mins)

### File: `backend/app/services/openai_chat_handler.py`

Add a document-aware prompt when RAG context is available:

```python
async def generate_response(
    self,
    conversation_history: List[Dict[str, str]],
    system_prompt: str,
    context: Optional[Dict[str, Any]] = None,
    streaming_handler: Optional[Any] = None
) -> str:
    """Generate a response using OpenAI API with enhanced document Q&A."""
    
    # Check if we have substantial RAG context
    rag_metadata = context.get("rag_chunks_metadata", []) if context else []
    has_document_context = len(rag_metadata) > 0 and any(
        chunk.get("similarity_score", 0) > 0.5 for chunk in rag_metadata
    )
    
    # Enhance system prompt for document Q&A
    if has_document_context:
        document_qa_prompt = """
You are an AI assistant with access to specific documents and materials. When answering questions:

1. Base your answers primarily on the provided source materials
2. Cite sources when making specific claims using [Source N] notation
3. If the information isn't in the provided sources, clearly state that
4. Synthesize information from multiple sources when relevant
5. Be accurate and avoid speculation beyond what's in the sources

Remember: You have access to clinic-specific information and educational materials that should be prioritized in your responses.
"""
        # Prepend to existing system prompt
        system_prompt = f"{document_qa_prompt}\n\n{system_prompt}"
    
    # Format context with full RAG information
    formatted_context = ""
    if context:
        rag_context = context.get("rag_context", "")
        if rag_context and has_document_context:
            # Add instruction about using the sources
            formatted_context = f"""
Available Information Sources:
{rag_context}

Please use the above sources to answer the user's question accurately and comprehensively.
"""
        else:
            formatted_context = rag_context
```

## Quick Win #4: Better Similarity Threshold (30 mins)

### File: `backend/app/core/rag_config.py`

Implement dynamic thresholds:

```python
from typing import Dict, Any

class RAGConfig:
    """Enhanced RAG configuration with dynamic thresholds."""
    
    # Base configuration
    EMBEDDING_MODEL = "all-mpnet-base-v2"
    EMBEDDING_DIM = 768
    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 50
    
    # Dynamic similarity thresholds
    SIMILARITY_THRESHOLDS = {
        "default": 0.35,      # Slightly higher than current 0.3
        "specific": 0.45,     # For specific queries (names, numbers)
        "general": 0.35,      # For general queries  
        "exploratory": 0.25   # For broad searches
    }
    
    @staticmethod
    def get_similarity_threshold(query: str, context: Dict[str, Any] = None) -> float:
        """Get dynamic similarity threshold based on query characteristics."""
        query_lower = query.lower()
        
        # Specific queries (looking for exact information)
        specific_indicators = ["what is", "how much", "when does", "where is", 
                             "phone", "address", "hours", "cost", "price"]
        if any(indicator in query_lower for indicator in specific_indicators):
            return RAGConfig.SIMILARITY_THRESHOLDS["specific"]
        
        # Exploratory queries (browsing for information)
        exploratory_indicators = ["tell me about", "information about", 
                                 "what do you know", "anything about"]
        if any(indicator in query_lower for indicator in exploratory_indicators):
            return RAGConfig.SIMILARITY_THRESHOLDS["exploratory"]
        
        # Default threshold
        return RAGConfig.SIMILARITY_THRESHOLDS["default"]

# Update settings
rag_config = RAGConfig()
```

### Update `context_enricher.py` to use dynamic thresholds:

```python
# In enrich_with_rag function
from app.core.rag_config import rag_config

# Get dynamic threshold
similarity_threshold = rag_config.get_similarity_threshold(message, {"user_role": user_role})

content_chunks = crud_content_chunk.get_similar_chunks(
    db=db,
    query_embedding=query_embedding,
    clinic_id=clinic_id,
    similarity_threshold=similarity_threshold,  # Dynamic threshold
    limit=5
)
```

## Quick Win #5: Add Source Citations to Responses (30 mins)

### File: `backend/app/services/chat_modules/daily_health_coach.py`

Update to use RAG context with citations:

```python
async def process(
    self, message: str, context: Dict[str, Any]
) -> Dict[str, Any]:
    """Process message with document-aware responses."""
    
    # Check for RAG context
    rag_metadata = context.get("rag_chunks_metadata", [])
    has_sources = len(rag_metadata) > 0
    
    # Build prompt with source awareness
    if has_sources:
        # Create source reference for prompt
        source_context = "\n\nAvailable Sources:\n"
        for i, chunk_meta in enumerate(rag_metadata, 1):
            source_context += f"[Source {i}] {chunk_meta.get('source_title', 'Document')}\n"
        
        system_prompt = self.system_prompt + """

When using information from the provided sources, cite them using [Source N] notation.
This helps users verify the information and builds trust.""" + source_context
    else:
        system_prompt = self.system_prompt
    
    # Rest of the implementation...
```

## Testing Your Changes

### 1. Test Full Chunk Retrieval:
```bash
# Query with a specific question
curl -X POST http://localhost:8000/api/v1/chat/messages \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the side effects mentioned in the GLP-1 guide?",
    "context": {"userRole": "patient"}
  }'
```

### 2. Verify Enhanced Context:
Look for these improvements in the response:
- Longer, more detailed answers
- Source citations [Source 1], [Source 2]
- More comprehensive information
- Better relevance to the question

### 3. Monitor Performance:
```bash
# Check response times
docker-compose logs -f backend | grep "Chat response time"

# Monitor chunk retrieval
docker-compose logs -f backend | grep "Retrieved.*chunks"
```

## Expected Results

### Before (Current System):
```
User: "What does the guide say about nausea?"
Bot: "Relevant information from clinic materials:
- Nausea is a common side effect that us..."
```

### After (With Quick Wins):
```
User: "What does the guide say about nausea?"
Bot: "According to the GLP-1 Medication Guide [Source 1], nausea is one of the most 
common side effects, affecting 20-30% of patients during the first 4-8 weeks of 
treatment. The guide recommends several strategies to manage nausea:

1. Start with a lower dose and gradually increase [Source 1]
2. Take the medication with food to reduce stomach upset [Source 1]  
3. Eat smaller, more frequent meals throughout the day [Source 2]
4. Avoid fatty or spicy foods that may worsen symptoms [Source 2]

The guide emphasizes that nausea typically improves as your body adjusts to the 
medication. However, if nausea persists beyond 8 weeks or becomes severe, you 
should contact your healthcare provider [Source 1]."
```

## Next Steps

After implementing these quick wins:

1. **Monitor Usage**: Track how users interact with enhanced responses
2. **Collect Feedback**: Add feedback buttons for document Q&A
3. **Fine-tune Thresholds**: Adjust based on retrieval accuracy
4. **Expand Context Window**: Implement neighboring chunk retrieval
5. **Add Document Viewer**: Link citations to source documents

These quick wins can be implemented in 2-3 hours and will immediately improve the document Q&A experience without requiring major architectural changes.