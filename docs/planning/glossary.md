# Glossary

**Project:** Codename Pulsetrack
**Last Updated:** [YYYY-MM-DD]

---

## 🎯 Purpose

This document defines key terms, acronyms, and concepts used within the Codename Pulsetrack project to ensure clear and consistent communication among team members and stakeholders.

---

## 📖 Terms & Definitions

*   **Admin Portal:** The web application used by system administrators to manage clinics, users, and potentially system-wide settings.
*   **API-First:** An architectural approach where the backend API is designed and built first, serving as the foundation for frontend applications and potentially third-party integrations.
*   **Atomic PRD:** A Product Requirements Document focused on a single, specific feature or component, derived from a larger PRD.
*   **Azure Blob Storage:** Microsoft Azure's object storage solution, used in this project for storing patient profile pictures and potentially other static assets.
*   **BMI (Body Mass Index):** A measure calculated from a person's weight and height, used in the patient app's weight tracking feature.
*   **Chatbot / Chat Agent:** The AI-powered conversational interface available to patients for support and guidance, utilizing RAG.
*   **CI/CD (Continuous Integration / Continuous Deployment):** Automation practices for building, testing, and deploying code changes frequently and reliably. Implemented using GitHub Actions.
*   **Clerk:** The third-party authentication service used for managing Admin and potentially Clinician user authentication and invitations.
*   **Clinic:** A healthcare practice or organization using the Pulsetrack platform. Managed via the Admin Portal.
*   **Clinician:** A healthcare professional (doctor, nurse, etc.) associated with a Clinic, using the Clinician Portal to manage patients.
*   **Clinician Portal:** The web application used by Clinicians to manage their assigned patients, review data (weight, side effects, medication requests), and generate access codes.
*   **Constraint File:** A document outlining specific rules, standards, or limitations for a particular aspect of the project (e.g., architecture, naming, security). Located in `.xgen/constraints/`.
*   **CRUD:** Acronym for Create, Read, Update, Delete – standard database operations.
*   **Docker:** A platform for developing, shipping, and running applications in containers. Used for local development and deployment.
*   **Docker Compose:** A tool for defining and running multi-container Docker applications. Used for orchestrating the local development environment.
*   **Embedding:** A numerical representation (vector) of text or other data, used in machine learning and RAG for semantic similarity searches.
*   **FastAPI:** A modern, fast (high-performance) web framework for building APIs with Python 3.7+ based on standard Python type hints. Used for the backend.
*   **Firebase Authentication:** Google's authentication service, used in this project for Clinician login.
*   **GDPR (General Data Protection Regulation):** A regulation in EU law on data protection and privacy.
*   **Git:** A distributed version control system used for tracking changes in source code during software development.
*   **GitHub Actions:** A CI/CD platform integrated with GitHub repositories.
*   **HIPAA (Health Insurance Portability and Accountability Act):** US legislation providing data privacy and security provisions for safeguarding medical information.
*   **Hi-Tech (Health Information Technology for Economic and Clinical Health Act):** US legislation promoting the adoption and meaningful use of health information technology, includes updates to HIPAA.
*   **JWT (JSON Web Token):** A compact, URL-safe means of representing claims to be transferred between two parties. Used for session management.
*   **LLM (Large Language Model):** A type of artificial intelligence model trained on vast amounts of text data to understand and generate human-like language. Used in the chatbot.
*   **Magic Code:** A unique, time-limited code generated by Clinicians for Patients to use for initial login/signup to the Patient App.
*   **Memory Bank:** A set of markdown files (`.memory-bank/`) used to maintain project context, decisions, and progress across sessions.
*   **MVP (Minimum Viable Product):** The version of the product with just enough features to be usable by early customers who can then provide feedback for future product development.
*   **Multi-Tenancy:** An architecture where a single instance of the software serves multiple distinct user groups (tenants, in this case, Clinics).
*   **Patient:** An individual receiving care, using the Patient App to track progress, communicate, and access resources.
*   **Patient App:** The mobile-responsive web application used by Patients.
*   **PMF (Product-Market Fit):** The degree to which a product satisfies strong market demand.
*   **PostgreSQL:** A powerful, open-source object-relational database system. Used as the primary database via Supabase.
*   **PRD (Product Requirements Document):** A document outlining the features, requirements, and goals of a product or feature.
*   **Prompt Template:** A pre-defined structure for instructions given to an LLM to generate specific outputs (e.g., code, documentation). Located in `.xgen/prompt_templates/`.
*   **Pydantic:** A Python library for data validation and settings management using Python type annotations. Used extensively in FastAPI.
*   **Python:** The primary programming language used for the backend.
*   **RAG (Retrieval-Augmented Generation):** An AI technique that combines information retrieval (from a knowledge base, like a Vector DB) with generative capabilities of an LLM to produce more accurate and context-aware responses. Used for the chatbot.
*   **RBAC (Role-Based Access Control):** A security mechanism that restricts system access based on user roles (e.g., Patient, Clinician, Admin).
*   **React:** A JavaScript library for building user interfaces. Used for all frontend applications.
*   **RESTful API:** An architectural style for designing networked applications, based on stateless communication using standard HTTP methods.
*   **Rule:** A specific guideline or constraint defined for the project, often indexed in `rules_index.md`.
*   **Ruff:** An extremely fast Python linter and code formatter.
*   **Scaffold:** The foundational directory structure, configuration files, and placeholder documentation generated at the start of a project or feature implementation. Managed by the `.xgen` system.
*   **Shadcn/ui:** A collection of reusable UI components built with Radix UI and Tailwind CSS for React applications.
*   **Side Effect Reporting:** Feature allowing patients to report medication side effects, categorized by severity.
*   **SQLAlchemy:** A SQL toolkit and Object-Relational Mapper (ORM) for Python. Used for database interactions in the backend.
*   **Supabase:** An open-source Firebase alternative, providing a PostgreSQL database, authentication, storage, and other backend services. Used as the BaaS platform.
*   **Tailwind CSS:** A utility-first CSS framework for rapidly building custom user interfaces.
*   **TypeScript:** A typed superset of JavaScript that compiles to plain JavaScript. Used in the frontend projects.
*   **Vector DB (Vector Database):** A database optimized for storing and querying high-dimensional vectors, such as text embeddings. Used in the RAG architecture.
*   **Weight Tracking:** Feature allowing patients to log their weight, view history, and see calculated BMI.
*   **.xgen:** The internal system/directory used for generating project scaffolds, managing PRDs, constraints, prompt templates, and other development artifacts.

---

*This file is a placeholder generated by the .xgen Scaffolding Agent. Add or refine terms as the project evolves.*