### 👟 `project_steps.md`
**Project:** Codename Pulsetrack
**Last Updated:** 2025-03-31

---

## 🎯 Purpose

To provide a high-level sequence of steps for implementing the Codename Pulsetrack MVP, based on the defined PRDs and constraints. This serves as a guide for development prioritization, emphasizing an API-first approach.

---

## 🗺️ Implementation Phases (MVP)

**Phase 1: Project Setup & Foundation**

1.  **Initialize Project Repositories:** Set up Git repository(ies). Consider monorepo (e.g., using Turborepo/Nx) or separate backend/frontend repos based on team preference.
2.  **Choose Core Technologies:** Confirm chosen stack: Backend - Python/FastAPI, Frontend - React (with Shadcn/ui, Tailwind), Database/Platform - Supabase (PostgreSQL).
3.  **Setup Docker Environment:** Create optimized `Dockerfile`s (e.g., using official Python base image for FastAPI, multi-stage build with Node base image for React app). Develop `docker-compose.yml` for local development, orchestrating the FastAPI service, potentially a Caddy/Nginx reverse proxy, and integrating with Supabase local development via its Docker image/CLI (`supabase start`).
    *   *Verification:* Local environment spins up successfully via `docker-compose up`.
4.  **Initialize Core Projects:** 
    *   **Backend (FastAPI):** Set up Python virtual environment (`python -m venv .venv`), activate it, `pip install fastapi uvicorn[standard] python-dotenv supabase-py`, create initial `main.py` with a health check endpoint.
    *   **Frontend (React):** Initialize project using `npx create-react-app <app-name>` or Vite (`npm create vite@latest <app-name> --template react-ts`). Install necessary dependencies: `npm install tailwindcss postcss autoprefixer`, `npm install @supabase/supabase-js`. Initialize Tailwind CSS (`npx tailwindcss init -p`). Initialize Shadcn/ui (`npx shadcn-ui@latest init`).
5.  **Setup CI Pipeline:** Configure basic CI (e.g., GitHub Actions) with workflows for Python (FastAPI) and Node.js (React). Include steps to install dependencies, run linters (e.g., `ruff check . && ruff format . --check` for Python, `npx eslint .` for React), and execute initial tests.
6.  **Configure Supabase:** Initialize Supabase locally (`supabase init`) and link to a remote Supabase project (`supabase link --project-ref <your-project-ref>`). Manage database migrations using Supabase CLI (`supabase migration new <name>`, `supabase db push`). Configure Supabase Auth (enable Email provider, add allowed clinician email domains if needed, set up JWT secret). Securely manage Supabase API URL, anon key, and service role key using environment variables (`.env` file locally, secrets management in deployment).
7.  **Setup Basic Logging:** Implement structured logging in FastAPI using Python's standard `logging` module or a library like `loguru`, capturing request details and timings.

**Phase 2: Backend API Development (API-First)**

*Develop endpoints based on atomic PRDs in `.xgen/prds/api/` and adhere to constraints.*

1.  **Implement Authentication API:**
    *   Magic code generation (clinician endpoint) and validation (patient endpoint).
    *   Clinician Firebase token validation.
    *   Session token (JWT) generation and middleware setup.
    *   *Verification:* API endpoints work as per `authentication.md`; basic integration tests pass.
2.  **Implement Patient Data API:** Core CRUD endpoints (`GET /me`, `PUT /me`, `GET /clinicians/patients/{id}`). Implement authorization logic.
    *   *Verification:* API endpoints work; integration tests pass; data persists correctly.
3.  **Implement Weight Tracking API:** Log entry, history retrieval (including BMI calculation logic). Enforce weekly interval.
    *   *Verification:* API endpoints work; integration tests pass; interval logic correct.
4.  **Implement Medication Management API:** Request submission (patient), request retrieval (clinician).
    *   *Verification:* API endpoints work; integration tests pass.
5.  **Implement Side Effect Reporting API:** Report submission (patient), report retrieval (clinician with filtering/sorting).
    *   *Verification:* API endpoints work; integration tests pass.
6.  **Implement Educational Content API:** Endpoint to serve static content.
    *   *Verification:* API endpoint returns configured content.
7.  **Implement Chat Agent API:** Basic endpoint to proxy messages to/from the chosen chat service. Implement history storage.
    *   *Verification:* API endpoint interacts with chat service; history is saved.
8.  **Implement Clinician Portal API:** Patient list retrieval, access code generation.
    *   *Verification:* API endpoints work; integration tests pass.
9.  **API Documentation:** Generate/update API documentation (e.g., Swagger/OpenAPI).
10. **Integration Testing:** Ensure robust integration tests cover all API endpoints' core functionality and error conditions.

**Phase 3: Frontend Development**

*Develop components based on atomic PRDs in `.xgen/prds/frontend/` and adhere to constraints.*

1.  **Setup Frontend Projects:** Initialize frontend projects (Patient App, Clinician Portal) with chosen framework, state management, and routing.
2.  **Implement Shared Components/UI Kit:** Develop reusable components based on the design system (buttons, inputs, cards, layout elements).
3.  **Patient App - Core Views:**
    *   Implement Authentication view (magic code input).
    *   Implement core navigation and layout.
    *   Implement Profile Management view (view/edit).
    *   Implement Weight Tracking view (log, graph).
    *   Implement Medication Request view.
    *   Implement Side Effect Reporting view.
    *   Implement Educational Content view.
    *   Implement Chat Agent view.
    *   Ensure responsiveness across all views.
    *   *Verification:* Views render correctly, interact with (mocked/real) APIs, responsive design works.
4.  **Clinician Portal - Core Views:**
    *   Implement Authentication flow (Firebase login).
    *   Implement core navigation and layout.
    *   Implement Dashboard view.
    *   Implement Patient List view (with search/sort).
    *   Implement Patient Detail view (tabbed interface for profile, weight, meds, side effects).
    *   Implement Medication Request Triage view.
    *   Implement Side Effect Triage view (with severity highlighting).
    *   Implement Access Code Generation view.
    *   *Verification:* Views render correctly, interact with APIs, workflows are functional on desktop.

**Phase 4: Integration, Testing & Refinement**

1.  **End-to-End Testing:** Connect frontend apps to the deployed backend APIs in a test/staging environment. Perform manual E2E testing of critical user flows defined in `testing_protocols.md`. Implement basic automated E2E tests.
2.  **Compliance/Security Review:** Review implementation against `security.md` constraints. Ensure data handling, encryption, and authorization are correctly implemented. Check disclaimers.
3.  **Performance Testing (Basic):** Use browser dev tools to check frontend load times. Monitor basic API response times under light load.
4.  **Bug Fixing & UI Polish:** Address bugs found during testing. Refine UI/UX based on feedback.

**Phase 5: Deployment Preparation (MVP)**

1.  **Container Image Builds:** Finalize production-ready Docker images for backend and frontend(s).
2.  **Deployment Configuration:** Prepare configuration for the chosen deployment target (e.g., cloud instance setup scripts, simple orchestration config). Include environment variable setup for secrets.
3.  **Database Migration/Setup:** Prepare initial database schema and any necessary migration scripts.
4.  **Deployment Dry Run:** Perform a test deployment to a staging environment.
5.  **Final Manual Testing:** Final validation in the staging environment.



**Phase 6: Chatbot & Multi-Tenancy Implementation**

*Implement features based on `master_chat_prd.md` and associated atomic PRDs/constraints.*

1.  **Backend - Multi-Tenancy Foundation:**
    *   Define/Update Database Models: Implement SQLAlchemy models for `Clinic`, `ClinicianClinicAssociation`, `PatientClinicianAssociation` (or adapt existing models). Ref: `api/chat_multi_tenancy.md`.
    *   Implement RBAC Logic: Update API dependencies (`deps.py`) or middleware to enforce access control based on user roles and clinic associations defined in `api/chat_multi_tenancy.md` and `chat_security.md`.
    *   *Verification:* Unit tests for models; Integration tests verify RBAC on existing relevant endpoints.

2.  **Backend - Data Ingestion & Processing:**
    *   Implement Admin Data Ingestion API: Create endpoint (`/api/v1/admin/clinics/ingest`) per `api/chat_data_ingestion.md`.
    *   Implement Web Scraping Service: Develop logic to scrape content from a given URL. Consider asynchronous execution (e.g., Celery, BackgroundTasks). Ref: `api/chat_knowledge_base.md`.
    *   Implement Embedding/Storage Logic: Integrate embedding model, preprocess text (scraped + supplemental), generate embeddings, and store in Vector DB associated with Clinic ID. Ref: `api/chat_knowledge_base.md`.
    *   *Verification:* Integration tests for ingestion API; Unit/Integration tests for scraping and embedding logic (may require mocking external services/DBs).

3.  **Backend - Chatbot Service:**
    *   Implement Chat API Endpoint: Create endpoint (`/api/v1/chat/{clinicId}` or similar) per `api/chat_service.md`.
    *   Integrate RAG Logic: Implement retrieval from Vector DB based on clinic ID and user query, integrate with LLM for response generation. Ensure disclaimer inclusion.
    *   *Verification:* Integration tests for chat API endpoint (mocking LLM/VectorDB interactions); Test disclaimer presence.

4.  **Frontend - Admin Portal:**
    *   Implement Data Input View: Create the UI form specified in `frontend/admin/data_input.md`. Integrate with the backend ingestion API.
    *   *Verification:* UI renders correctly; Form submission calls the correct API; Success/error feedback works.

5.  **Frontend - Clinician Portal:**
    *   Implement Multi-Clinic Context Switching: Add UI elements (e.g., dropdown) to select active clinic context per `frontend/clinician/multi_clinic_view.md`.
    *   Update Data Views: Modify existing views (Patient List, Detail) to filter/display data based on the selected clinic context.
    *   *Verification:* UI allows context switching; Data views update correctly based on selection.

6.  **Frontend - Patient App:**
    *   Implement Chat Interface: Create the chat UI components per `frontend/patient/chat_integration.md`.
    *   Integrate Chat API: Connect the UI to the backend chat service API, ensuring the correct clinic context is used implicitly.
    *   *Verification:* Chat UI renders; Messages can be sent/received; Disclaimer is visible; Loading/error states work.

7.  **Deployment Updates:**
    *   Update/Create Dockerfiles: Ensure Dockerfiles exist for any new services (Scraping, Embedding, Chat) and the Admin Frontend.
    *   Update Docker Compose: Add new services to `docker-compose.yml`, configure environment variables, networks, and volumes. Ref: `deployment/containerization.md`.
    *   *Verification:* `docker-compose up` starts all new and existing services successfully.

8.  **Testing & Refinement:**
    *   Add Unit/Integration tests for all new backend components/APIs.
    *   Add component/integration tests for new frontend views.
    *   Perform E2E testing for the full flow: Admin inputs data -> Patient interacts with clinic-specific chatbot.
    *   Review against `chat_security.md` and `chat_ui_ux.md`.

**Phase 7: Custom Authentication Implementation (Clerk)**

*Implement custom authentication flows using Clerk based on atomic PRDs in `.xgen/prds/` and adhere to `auth_constraints.md`.*

1.  **Set Up Clerk SDK & Environment:**
    *   Ensure Clerk SDK (`@clerk/clerk-react`, etc.) is installed in all frontend projects.
    *   Verify `CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY` (or Backend API Key/JWT Key) are correctly set in respective `.env` files.
    *   Ref: `project_setup.md`, `auth/auth_sdk_integration.md`
    *   Rule: `[P-AUTH-SEC-003]`, `[P-AUTH-INT-001]`

2.  **Create Shared Auth Provider:**
    *   Implement/Refactor `AuthProvider` React context to wrap all apps.
    *   Ensure it initializes Clerk SDK and exposes `signIn`, `signOut`, `acceptInvitation`, `getToken`, and user state.
    *   Utilize shared UI components for consistency.
    *   Ref: `auth/auth_centralize.md`
    *   Rule: `[P-AUTH-UI-001]`, `[P-AUTH-UI-002]`

3.  **Build Custom Auth Pages:**
    *   Implement custom Login page(s) using `AuthProvider`'s `signIn`.
    *   Implement custom Invitation Acceptance page(s) using Clerk SDK verification logic via `AuthProvider`.
    *   Implement Logout mechanism using `AuthProvider`'s `signOut`.
    *   Ensure pages are responsive and tailored per application (Patient vs. Clinician/Admin).
    *   Ref: `auth/auth_custom_pages.md`, `auth/auth_ui_ux.md`
    *   Rule: `[P-AUTH-UI-003]`, `[P-AUTH-UI-004]`, `[P-AUTH-UI-005]`, `[P-AUTH-INT-003]`, `[P-AUTH-INT-004]`

4.  **Implement Secure Token Handling & Routing:**
    *   Ensure frontend sends Clerk token (requesting `CodenamePulsetrack` template) in `Authorization` header via `AuthProvider`'s `getToken`.
    *   Implement/Verify backend middleware/dependency to validate the Clerk token signature, expiration, and template.
    *   Implement protected routes that check auth state via `AuthProvider` and redirect unauthenticated users.
    *   Ref: `auth/auth_sdk_integration.md`, `auth/auth_routing.md`
    *   Rule: `[P-AUTH-SEC-001]`, `[P-AUTH-SEC-002]`, `[P-AUTH-INT-002]`

5.  **Testing:**
    *   Perform E2E testing for login, logout, and invitation acceptance flows for each user type.
    *   Test edge cases (invalid credentials, expired tokens/invitations, network errors).
    *   Verify protected routes enforce authentication.
    *   Ref: `testing_protocols.md`

6.  **Documentation:**
    *   Update relevant READMEs or create internal documentation detailing the custom authentication flow, setup, and token handling.
    *   Ref: `README.md` (project root), `.xgen/README.md`

---

## ✅ Verification Checkpoints

-   Phase 1: Local Docker environment (`docker-compose up`) runs successfully. Supabase local dev environment integrated. Core FastAPI & React projects initialized. Basic CI pipeline passes linting checks.
-   Phase 2: All core API endpoints implemented and pass integration tests. API documentation generated.
-   Phase 3: Frontend views implemented, connect to APIs (mocked or real), responsive/functional as per PRDs.
-   Phase 4: Critical E2E flows pass manually and/or via automation. Security/Compliance review passed. Basic performance goals met. Major bugs fixed.
-   Phase 5: Successful deployment to staging environment. Final validation complete. Ready for initial production deployment to target clinics.