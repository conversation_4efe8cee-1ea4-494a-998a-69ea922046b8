# Product Context

This file provides a high-level overview of the project and the expected product. It is updated as the project evolves.

## Project Goal

Create an MVP to help gain investment funding and show Product-Market Fit (PMF) for using AI to vastly improve patient communications, care coordination, and advocacy in weight management and aesthetics.

## Key Features

### Patient App
- Secure access through Clerk authentication system (replacing original magic code plan)
- AI chat for support and guidance (trained on clinic-specific data)
- Weight tracking with history, BMI calculation, and visualization
- Medication request management for refills
- Side effect reporting (minor/major with urgency flagging)
- Profile management (including photo uploads to Azure storage)
- Educational resources from clinic-provided content
- Biometric authentication (Face ID) for security
- Appointment scheduling and management
- Dashboard with modular cards for weight progress, medication tracker, side effect log, and educational content

### Clinician Portal
- Web-based patient management dashboard
- Patient invitation system via Clerk
- Side effect triage with severity indicators
- Patient data visualization
- Multi-clinic context switching support
- Medication refill request management
- Appointment management with audit logging
- Enhanced RBAC for patient/clinician/admin roles

### Admin Portal
- Clinic management (CRUD operations)
- Website scraping for clinic data ingestion
- Data input for clinic-specific chatbot training
- Clinician management (invitations, listing, editing)
- Dashboard with summary cards for key metrics

## Overall Architecture

- API-First architecture with strict frontend/backend decoupling via RESTful APIs
- Backend: Python/FastAPI, PostgreSQL, SQLAlchemy, Alembic
- Frontend: React with TypeScript, Shadcn/ui components, TailwindCSS
- Authentication: Clerk (used across patient, clinician, and admin frontends)
- Cloud Storage: Azure Blob Storage for file uploads (profile photos)
- CI/CD: GitHub Actions workflow for Python and Node.js
- Containerization: Docker and docker-compose for development and deployment
- Chat: RAG (Retrieval Augmented Generation) implementation using Vector DB and LLMs
- Database Access: Class-based CRUD pattern with selectinload for N+1 query prevention
- Audit Logging: Comprehensive logging for critical operations

## Implementation Timeline

### Phase 1: Initial Setup (2025-03-31)
- PRD creation and project scaffolding
- Core backend structure with FastAPI
- Docker environment configuration
- Database migrations with Alembic
- Authentication API foundations
- CI/CD pipeline setup

### Phase 2: Core APIs (2025-04-01 to 2025-04-02)
- Authentication endpoints for patients and clinicians
- Patient profile management
- Weight tracking endpoints
- Medication requests
- Side effect reporting
- Educational content
- Chat functionality

### Phase 3: Frontend Development (2025-04-03 to 2025-04-05)
- Patient application views and components
- Clinician portal views and components
- Admin portal for clinic management
- Profile picture management with Azure storage
- Responsive design for mobile use

### Phase 4: Advanced Features (2025-04-05 to 2025-04-10)
- Multi-tenancy model with Role-Based Access Control (RBAC)
- Website scraping for clinic information
- Chat bot implementation with RAG capabilities
- Clinic-specific knowledge base embeddings
- Appointment management (backend and frontend)
- Medication request workflow enhancements
- Comprehensive audit logging implementation

### Phase 5: Refinement and Optimization (2025-04-10 to Present)
- ID type standardization (string for Clerk IDs)
- Patient dashboard modular card system
- UI/UX improvements and accessibility enhancements
- Database query optimization with eager loading
- Documentation reorganization and PRD archiving
- Bug fixes and code quality improvements
- Finalization of logging and notification systems

## Constraints & Compliance

- Must adhere to HIPAA, Hi-Tech, GDPR, and UK healthcare privacy rules
- Web-only initially, but fully responsive for mobile devices
- Containerized (Docker stack) for scalability
- Designed to eventually support hundreds of thousands of concurrent users
- High-quality, simple, and intuitive UI/UX ("drop-down simple")
- No clinical diagnosis or guidance (clear disclaimers required)
- Comprehensive audit logging for compliance requirements

## Current Status

The project is currently in Phase 5. Recent work includes:

- Overhauling the ClinicMedicationsModal with improved UI/UX and accessibility
- Completing 65 atomic prompt templates for logging and notification system
- Refactoring patient dashboard UI with modular components
- Fixing critical issues with appointment and medication request displays
- Enhancing audit logging for compliance requirements
- Resolving database access patterns and query optimization
- Standardizing field naming conventions between frontend and backend
- Implementing comprehensive enum handling in SQLAlchemy models

Immediate next steps include:
- Implementing frontend-clinician patient details page functionality
- Continuing improvements to authentication and user management
- Maintaining consistent error handling and logging patterns
- Finalizing modular dashboard UI components in patient frontend

---

*Last updated: 2025-04-14 15:18:14*
