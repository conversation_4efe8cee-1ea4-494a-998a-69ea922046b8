# Refactoring Tools Configuration Guide

## Ruff Usage
- Install: `pip install ruff`
- Run: `ruff check ./app`
- Auto-fix: `ruff check --fix ./app`

## PyCharm Refactoring
1. Search and Replace in Path (Ctrl+Shift+R / Cmd+Shift+R):
   - Search pattern: `from app import crud\s*\nfrom app\.crud import`
   - Replace with: `from app.crud import`
   - Use "Regular expressions" option
   - Scope: Project Files

2. Find Usages (Alt+F7 / Option+F7):
   - Use on `crud` module to find all references
   - Review and refactor using "Rename" refactoring

## Bowler Script
1. Install: `pip install bowler`
2. Run: `python scripts/refactor_crud_imports.py`
3. Review changes: Add `--print` flag
4. Apply changes: Add `--write` flag

## Validation Steps
1. Run Ruff to verify import patterns
2. Run test suite
3. Check git diff for unexpected changes
4. Review console output for errors

## Rollback Procedure
1. Use git to revert changes: `git reset --hard HEAD`
2. For specific files: `git checkout HEAD -- path/to/file`