# Use an official Node runtime as a parent image with specific version and Debian Bullseye
FROM node:slim

# Set the working directory in the container
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Set environment variables to properly handle platform-specific modules
ENV ROLLUP_SKIP_NODEJS=true
ENV TAILWIND_SKIP_PLATFORM_CHECK=true
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Clean out any potentially cached modules and install with rebuilding
RUN rm -rf node_modules && \
    yarn cache clean && \
    yarn install --network-timeout 600000

# Copy the rest of the application code
COPY . .

# Make port 5175 available
EXPOSE 5175

# Define command to run app
CMD ["yarn", "run", "dev", "--host", "0.0.0.0", "--port", "5175"]
