import { Routes, Route, Navigate } from "react-router-dom";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import ProtectedRoute from "@/components/auth/ProtectedRoute"; // Import the new ProtectedRoute
import { ThemeProvider } from "@/components/theme/ThemeProvider";

// Layout
import MainLayout from "@/components/layout/MainLayout"; // Assuming alias is set up

// Pages
import LoginPage from "@/pages/LoginPage";
import AdminDashboardPage from "@/pages/AdminDashboardPage";
import ClinicManagementPage from "@/pages/ClinicManagementPage";
import ClinicianInvitePage from "@/pages/ClinicianInvitePage";
import ClinicianListPage from "@/pages/ClinicianListPage";
import AdminProfilePage from "@/pages/AdminProfilePage";
import AcceptInvitationPage from "@/pages/AcceptInvitationPage";
import UnauthorizedPage from "@/pages/UnauthorizedPage"; // Import the new page

function App() {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <Routes>
      {/* Public routes */}
      <Route
        path="/login"
        element={
          <>
            <SignedIn>
              {/* If user is signed in, redirect away from login to the admin dashboard */}
              <Navigate to="/" replace />
            </SignedIn>
            <SignedOut>
              {/* If user is signed out, show the custom login page */}
              <LoginPage />
            </SignedOut>
          </>
        }
      />
      <Route
        path="/accept-invitation"
        element={
          <>
            <SignedIn>
              {/* If user is signed in, redirect away from invitation acceptance */}
              {/* Maybe redirect to profile or dashboard? Dashboard seems safer. */}
              <Navigate to="/" replace />
            </SignedIn>
            <SignedOut>
              {/* If user is signed out, show the invitation acceptance page */}
              <AcceptInvitationPage />
            </SignedOut>
          </>
        }
      />
      {/* Unauthorized page route (outside protected routes) */}
      <Route path="/unauthorized" element={<UnauthorizedPage />} />

      {/* Protected Routes: Wrap with ProtectedRoute */}
      <Route element={<ProtectedRoute />}>
        {/* Routes rendered inside ProtectedRoute require authentication */}
        {/* Wrap protected pages with the main layout */}
        <Route element={<MainLayout />}>
          <Route path="/" element={<AdminDashboardPage />} />
          <Route path="/clinics" element={<ClinicManagementPage />} />
          <Route path="/invites" element={<ClinicianInvitePage />} />
          <Route path="/profile" element={<AdminProfilePage />} />
          <Route path="/clinicians" element={<ClinicianListPage />} />
          {/* Add other admin routes here */}
          <Route path="*" element={<div>Admin Page Not Found</div>} />{" "}
          {/* Catch-all for admin section */}
        </Route>
      </Route>
    </Routes>
    </ThemeProvider>
  );
}

export default App;
