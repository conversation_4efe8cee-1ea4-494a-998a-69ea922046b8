import React from "react";
import { Outlet, Navigate } from "react-router-dom"; // Import Navigate
import { useAuth } from "@pulsetrack/shared-frontend";
import { AuthLoadingIndicator } from "@pulsetrack/shared-frontend";

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, isAuthLoading, user } = useAuth();

  // 1. Show loading indicator while authentication status is being determined
  if (isAuthLoading) {
    console.log("Auth loading...");
    return <AuthLoadingIndicator />;
  }

  // 2. If not loading and not authenticated, redirect to login
  if (!isAuthenticated) {
    console.log("User not authenticated. Redirecting to /login.");
    return <Navigate to="/login" replace />;
  }

  // 3. If not loading and authenticated, check for admin role
  const roles = user?.publicMetadata?.role as string[] | undefined;
  const isAdmin = Array.isArray(roles) && roles.includes("admin");

  if (isAdmin) {
    // 3a. If authenticated and is admin, render the protected content
    console.log("User is authenticated admin. Rendering Outlet.");
    return <Outlet />;
  } else {
    // 3b. If authenticated but not admin, redirect to login (or an unauthorized page)
    console.warn(
      "User is authenticated but lacks 'admin' role. Redirecting to /unauthorized.",
    );
    return <Navigate to="/unauthorized" replace />; // Redirect non-admins to the unauthorized page
  }
};

export default ProtectedRoute;
