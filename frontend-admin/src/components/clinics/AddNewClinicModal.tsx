import React, { useState, useMemo, useRef, useEffect } from "react"; // Import useRef, useEffect
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react"; // Import spinner icon
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import { formatDate } from "@pulsetrack/shared-frontend";
import { ClinicRead } from "@/pages/ClinicManagementPage"; // Import ClinicRead type
import { AxiosError } from "axios"; // Import AxiosError

// useEffect is already imported

// Define expected response types for clarity
interface ScrapeInitiateResponse {
  task_id: string;
  message: string;
}

interface ScrapeStatusResponse {
  task_id: string;
  status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED";
  // Potentially add other fields like result or error message if the API provides them
}

interface AddNewClinicModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void; // Function to call on successful creation/update
  clinicToEdit?: ClinicRead | null; // Optional prop for editing
}

export function AddNewClinicModal({
  isOpen,
  onClose,
  onSuccess,
  clinicToEdit = null,
}: AddNewClinicModalProps) {
  const [name, setName] = useState("");
  const [address, setAddress] = useState("");
  const [websiteUrl, setWebsiteUrl] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isScraping, setIsScraping] = useState(false); // Restore isScraping state
  const isEditMode = !!clinicToEdit; // Determine if in edit mode

  // Populate form when clinicToEdit changes (for editing)
  useEffect(() => {
    if (isEditMode && clinicToEdit) {
      setName(clinicToEdit.name);
      setAddress(clinicToEdit.address || "");
      setWebsiteUrl(clinicToEdit.website_url || "");
    } else {
      // Reset form if opening in add mode or clinicToEdit becomes null
      setName("");
      setAddress("");
      setWebsiteUrl("");
    }
    // Reset scraping state when modal opens/closes or mode changes
    setIsScraping(false);
  }, [clinicToEdit, isEditMode, isOpen]); // Re-run effect if clinicToEdit, isEditMode or isOpen changes

  // Format the last scraped date for display
  const lastScrapedDisplay = useMemo(() => {
    if (isEditMode && clinicToEdit?.last_scraped_at) {
      try {
        // Format to show local short date (e.g., 04/06/2025)
        return formatDate(clinicToEdit.last_scraped_at);
      } catch (e) {
        console.error("Error formatting date:", e);
        return "Invalid date";
      }
    }
    return null;
  }, [clinicToEdit, isEditMode]);

  // Add a ref to store the interval ID for cleanup
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = useRef<number>(0); // Ref for poll count

  // Cleanup interval on unmount or modal close
  useEffect(() => {
    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }
    };
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  const handleScrape = async () => {
    if (!isEditMode || !clinicToEdit?.id) {
      toast.error("Cannot Scrape Yet", {
        description: "Please save the clinic before scraping its website.",
      });
      return;
    }
    if (!websiteUrl) {
      toast.error("Missing URL", {
        description: "Please enter a website URL to scrape.",
      });
      return;
    }

    // Clear any previous interval just in case
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
    pollCountRef.current = 0; // Reset poll count

    setIsScraping(true); // Start spinner

    try {
      // 1. Initiate scrape and get task_id
      const initiateResponse = await apiClient.post<ScrapeInitiateResponse>(
        "/admin/clinics/scrape",
        {
          url: websiteUrl,
          clinic_id: clinicToEdit.id,
        },
      );

      const taskId = initiateResponse.data.task_id;

      if (!taskId) {
        throw new Error("Task ID not received from the server.");
      }

      toast.info("Scraping Started", {
        description: `Task initiated for ${clinicToEdit.name}. Checking status...`,
      });

      // 2. Start polling
      const pollInterval = 3000; // 3 seconds
      const maxPollAttempts = 40; // 40 attempts * 3 seconds = 120 seconds (2 minutes) timeout

      intervalIdRef.current = setInterval(async () => {
        pollCountRef.current += 1;

        if (pollCountRef.current > maxPollAttempts) {
          clearInterval(intervalIdRef.current!);
          intervalIdRef.current = null;
          setIsScraping(false);
          toast.error("Scraping Timeout", {
            description: `Status check timed out after ${(maxPollAttempts * pollInterval) / 1000} seconds.`,
          });
          return;
        }

        try {
          const statusResponse = await apiClient.get<ScrapeStatusResponse>(
            `/admin/clinics/scrape/status/${taskId}`,
          );
          const status = statusResponse.data.status;

          switch (status) {
            case "COMPLETED":
              clearInterval(intervalIdRef.current!);
              intervalIdRef.current = null;
              setIsScraping(false);
              toast.success("Scraping Completed", {
                description: `Website scraping for ${clinicToEdit.name} finished successfully.`,
              });
              onSuccess(); // Refresh clinic data/list
              break;
            case "FAILED":
              clearInterval(intervalIdRef.current!);
              intervalIdRef.current = null;
              setIsScraping(false);
              toast.error("Scraping Failed", {
                // Consider adding specific error details from API if available
                description: `Scraping task for ${clinicToEdit.name} failed.`,
              });
              break;
            case "PENDING":
            case "RUNNING":
              // Continue polling, maybe log status?
              // console.log(`Scraping status for task ${taskId}: ${status}`);
              break;
            default:
              // Handle unexpected status
              console.warn(`Unexpected scraping status received: ${status}`);
              // Optionally stop polling on unexpected status
              // clearInterval(intervalIdRef.current!);
              // intervalIdRef.current = null;
              // setIsScraping(false);
              // toast.warning('Unknown Scraping Status', { description: `Received status: ${status}` });
              break;
          }
        } catch (pollError: unknown) {
          // Handle errors during polling (e.g., network error, 404 if task ID expires)
          clearInterval(intervalIdRef.current!);
          intervalIdRef.current = null;
          setIsScraping(false);
          console.error("Polling error:", pollError);
          // Type guard for AxiosError
          const errorMessage =
            pollError instanceof AxiosError && pollError.response?.data?.detail
              ? pollError.response.data.detail
              : "Could not check scraping status.";
          toast.error("Status Check Failed", { description: errorMessage });
        }
      }, pollInterval);
    } catch (initiateError: unknown) {
      // Handle errors during the initial POST request
      setIsScraping(false); // Stop spinner on initial failure
      console.error("Scraping initiation error:", initiateError);
      // Type guard for AxiosError
      const errorMessage =
        initiateError instanceof AxiosError &&
        initiateError.response?.data?.detail
          ? initiateError.response.data.detail
          : "Could not start scraping task.";
      toast.error("Scraping Initiation Failed", { description: errorMessage });
    }
    // NOTE: We no longer set isScraping(false) in a finally block here,
    // as it's handled by the polling logic or initial error handling.
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      const payload = {
        name,
        address,
        website_url: websiteUrl || null, // Send null if empty
      };

      if (isEditMode && clinicToEdit) {
        // Update existing clinic
        await apiClient.put<ClinicRead>(
          `/admin/clinics/${clinicToEdit.id}`,
          payload,
        );
      } else {
        // Create new clinic
        await apiClient.post<ClinicRead>("/admin/clinics", payload);
      }

      toast.success(isEditMode ? "Clinic Updated" : "Clinic Created", {
        description: `${name} has been ${isEditMode ? "updated" : "added"} successfully.`,
      });
      onSuccess(); // Call the success handler (closes modal, refreshes list)
      // Reset form fields after successful submission
      setName("");
      setAddress("");
      setWebsiteUrl("");
    } catch (error: unknown) {
      console.error("Clinic creation/update error:", error);
      // Type guard for AxiosError
      const errorMessage =
        error instanceof AxiosError && error.response?.data?.detail
          ? error.response.data.detail
          : `Could not ${isEditMode ? "update" : "create"} clinic.`;
      toast.error(isEditMode ? "Update Failed" : "Creation Failed", {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Clear interval if closing mid-poll
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
    // Reset form fields when closing
    setName("");
    setAddress("");
    setWebsiteUrl("");
    setIsScraping(false); // Ensure spinner stops if modal is closed mid-scrape
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Clinic" : "Add New Clinic"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update the clinic details below. You can scrape the website for details."
              : "Enter the clinic details below. Save the clinic first to enable website scraping."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="address" className="text-right">
                Address
              </Label>
              <Input
                id="address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="websiteUrl" className="text-right">
                Website URL
              </Label>
              <Input
                id="websiteUrl"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="col-span-3"
                type="url"
              />
              {/* Display Last Scraped Date */}
              {isEditMode && (
                <div className="col-span-4 text-sm text-muted-foreground text-right">
                  {lastScrapedDisplay
                    ? `Last scraped: ${lastScrapedDisplay}`
                    : "Never scraped"}
                </div>
              )}
            </div>
            {/* Restore Scrape Button Container */}
            <div className="flex justify-end items-center space-x-2">
              {" "}
              {/* Added items-center and space-x */}
              <Button
                type="button"
                variant="outline"
                onClick={handleScrape}
                // Disable if not in edit mode, or if scraping, or no URL
                disabled={
                  !isEditMode || isScraping || !websiteUrl || isSubmitting
                }
                size="sm"
                title={
                  isEditMode
                    ? "Scrape website for details"
                    : "Save clinic first to enable scraping"
                }
              >
                {isScraping ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scraping...
                  </>
                ) : (
                  "Scrape Website"
                )}
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting || isScraping}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || isScraping}>
              {isSubmitting
                ? isEditMode
                  ? "Updating..."
                  : "Creating..."
                : isEditMode
                  ? "Update Clinic"
                  : "Create Clinic"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
