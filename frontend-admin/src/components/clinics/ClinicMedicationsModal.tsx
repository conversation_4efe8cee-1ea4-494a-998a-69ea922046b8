import { useState, useEffect, useCallback, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { Plus, X, CheckCircle, Loader2, AlertTriangle } from "lucide-react";
import apiClient from "@/lib/apiClient";
import { ClinicRead } from "@/pages/ClinicManagementPage";
import { AxiosError } from "axios";

interface Medication {
  id?: string;
  name: string | undefined;
  description: string | undefined;
  dosage_guidelines: string | undefined;
  common_side_effects: string | undefined;
  category: string | undefined;
  clinic_specific_notes?: string;
  associatedClinicIds: string[]; // Array of clinic IDs this medication is associated with
}

interface ClinicMedicationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  clinic: ClinicRead;
}

/**
 * Redesigned modal for associating medications with a clinic.
 * Features:
 * - Search, list, and select medications
 * - Add new medication
 * - Associate/disassociate medications
 * - Beautiful, accessible, and responsive UI
 */
export function ClinicMedicationsModal({
  isOpen,
  onClose,
  clinic,
}: ClinicMedicationsModalProps) {
  // State
  const [allMedications, setAllMedications] = useState<Medication[]>([]);
  const [associatedMedicationIds, setAssociatedMedicationIds] = useState<
    Set<string>
  >(new Set());
  const [selectedMedicationId, setSelectedMedicationId] = useState<
    string | null
  >(null);
  const [editingMedication, setEditingMedication] = useState<Medication | null>(
    null,
  );
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [showAdd, setShowAdd] = useState(false);

  // Fetch all medications
  const fetchAllMedications = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.get("/medications/");
      setAllMedications(
        (response.data.items || []).map((med: Medication) => ({
          ...med,
          associatedClinicIds: Array.isArray(med.associatedClinicIds)
            ? med.associatedClinicIds
            : [],
        })),
      );
    } catch (err: unknown) {
      setError(
        err instanceof AxiosError && err.response?.data?.detail
          ? err.response.data.detail
          : "Failed to fetch medications",
      );
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch associated medications
  const fetchAssociatedMedications = useCallback(async () => {
    if (!clinic?.id) return;
    try {
      const response = await apiClient.get(
        `/admin/clinics/${clinic.id}/medications`,
      );
      const associatedMeds: Medication[] = Array.isArray(response.data)
        ? response.data
        : [];
      const ids = new Set(
        associatedMeds.map((med) => med.id).filter((id): id is string => !!id),
      );
      setAssociatedMedicationIds(ids);
    } catch {
      setAssociatedMedicationIds(new Set());
    }
  }, [clinic?.id]);

  useEffect(() => {
    if (isOpen) {
      fetchAllMedications();
      fetchAssociatedMedications();
      setSelectedMedicationId(null);
      setEditingMedication(null);
      setShowAdd(false);
      setSearch("");
    }
  }, [isOpen, fetchAllMedications, fetchAssociatedMedications]);

  // Filtered medications
  const filteredMedications = useMemo(() => {
    if (!search.trim()) return allMedications;
    return allMedications.filter((med) =>
      med.name?.toLowerCase().includes(search.trim().toLowerCase()),
    );
  }, [allMedications, search]);

  // Handlers
  const handleSelectMedication = (id: string) => {
    setSelectedMedicationId(id);
    setEditingMedication(null);
    setShowAdd(false);
  };

  // Delete medication handler
  const handleDeleteMedication = async (id: string) => {
    setSaving(true);
    try {
      await apiClient.delete(`/medications/${id}`);
      toast.success("Medication deleted!");
      setAllMedications((prev) => prev.filter((med) => med.id !== id));
      // If deleted medication was selected, clear selection
      if (selectedMedicationId === id) setSelectedMedicationId(null);
    } catch (err: unknown) {
      // Backend returns 400 if associated with any clinics
      const detail =
        err instanceof AxiosError && err.response?.data?.detail
          ? err.response.data.detail
          : "Failed to delete medication.";
      toast.error("Delete failed", {
        description: detail,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAddNew = () => {
    setEditingMedication({
      name: "",
      description: "",
      dosage_guidelines: "",
      common_side_effects: "",
      category: "",
      clinic_specific_notes: "",
      associatedClinicIds: [],
    });
    setSelectedMedicationId(null);
    setShowAdd(true);
  };

  const handleSaveMedication = async () => {
    if (!editingMedication?.name) {
      toast.error("Medication name is required");
      return;
    }
    setSaving(true);
    try {
      await apiClient.post("/medications/", editingMedication);
      toast.success("Medication added!");
      setShowAdd(false);
      fetchAllMedications();
    } catch {
      toast.error("Failed to add medication");
    } finally {
      setSaving(false);
    }
  };

  const handleAssociate = async (id: string) => {
    setSaving(true);
    try {
      await apiClient.post(
        `/admin/clinics/${clinic.id}/medications/associate`,
        { medication_id: id },
      );
      toast.success("Medication associated!");
      fetchAssociatedMedications();
    } catch {
      toast.error("Failed to associate");
    } finally {
      setSaving(false);
    }
  };

  /**
   * Remove association between a medication and this clinic.
   */
  const handleRemoveAssociation = async (medicationId: string) => {
    setSaving(true);
    try {
      await apiClient.delete(
        `/admin/clinics/${clinic.id}/medications/${medicationId}`,
      );
      toast.success("Association removed!");
      // Refetch associated medications and all medications to update UI
      await fetchAssociatedMedications();
      await fetchAllMedications();
    } catch (err: unknown) {
      const detail =
        err instanceof AxiosError && err.response?.data?.detail
          ? err.response.data.detail
          : "Failed to remove association.";
      toast.error("Remove association failed", {
        description: detail,
      });
    } finally {
      setSaving(false);
    }
  };

  // Modal Content
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90vw] max-w-[1200px] min-w-[320px] h-[80vh] max-h-[90vh] min-h-[320px] p-0 overflow-hidden !max-w-none !sm:max-w-none">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between px-6 py-4 border-b bg-white sticky top-0 z-10">
            <div className="flex items-center gap-3">
              <DialogTitle className="text-2xl font-bold">
                Clinic Medications
              </DialogTitle>
              <DialogDescription className="hidden md:block text-gray-500">
                Manage medications for{" "}
                <span className="font-semibold">{clinic.name}</span>
              </DialogDescription>
            </div>
            <DialogClose asChild>
              <Button variant="ghost" size="icon" aria-label="Close">
                <X className="w-5 h-5" />
              </Button>
            </DialogClose>
          </div>

          {/* Main Content */}
          <div className="flex flex-1 min-h-0 bg-gray-50">
            {/* Left: Medications List */}
            <div className="w-full md:w-1/3 border-r bg-white flex flex-col">
              <div className="p-4 border-b flex gap-2 items-center">
                <Input
                  placeholder="Search medications..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="flex-1"
                  aria-label="Search medications"
                />
                <Button
                  onClick={handleAddNew}
                  variant="outline"
                  className="ml-2"
                  aria-label="Add Medication"
                >
                  <Plus className="w-4 h-4 mr-1" /> Add
                </Button>
              </div>
              <ScrollArea className="flex-1">
                {loading ? (
                  <div className="flex justify-center items-center h-40">
                    <Loader2 className="animate-spin w-6 h-6 text-blue-600" />
                  </div>
                ) : error ? (
                  <div className="flex flex-col items-center justify-center h-40 text-red-600">
                    <AlertTriangle className="w-6 h-6 mb-2" />
                    <span>{error}</span>
                  </div>
                ) : filteredMedications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-40 text-gray-400">
                    <span>No medications found.</span>
                  </div>
                ) : (
                  <ul>
                    {filteredMedications.map((med) => (
                      <li
                        key={med.id}
                        className={`px-4 py-3 flex items-center gap-2 group hover:bg-blue-50 transition rounded ${selectedMedicationId === med.id ? "bg-blue-100" : ""}`}
                      >
                        <span
                          className="font-medium flex-1 truncate cursor-pointer"
                          onClick={() => handleSelectMedication(med.id!)}
                          tabIndex={0}
                        >
                          {med.name}
                        </span>
                        {associatedMedicationIds.has(med.id!) && (
                          <>
                            <span className="sr-only">Associated</span>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </>
                        )}
                        {/* Show delete button if medication is unassociated with any clinic */}
                        {med.associatedClinicIds &&
                          med.associatedClinicIds.length === 0 && (
                            <Button
                              variant="ghost"
                              size="icon"
                              aria-label="Delete medication"
                              className="opacity-60 hover:opacity-100 transition"
                              disabled={saving}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteMedication(med.id!);
                              }}
                            >
                              <X className="w-4 h-4 text-red-500" />
                            </Button>
                          )}
                        {/* Show remove association button if medication is associated with this clinic */}
                        {med.associatedClinicIds &&
                          med.associatedClinicIds.includes(clinic.id) && (
                            <Button
                              variant="ghost"
                              size="icon"
                              aria-label="Remove association"
                              className="opacity-60 hover:opacity-100 transition"
                              disabled={saving}
                              title="Remove this medication's association with this clinic"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveAssociation(med.id!);
                              }}
                            >
                              {saving ? (
                                <Loader2 className="animate-spin w-4 h-4 text-yellow-500" />
                              ) : (
                                <X className="w-4 h-4 text-yellow-500" />
                              )}
                            </Button>
                          )}
                      </li>
                    ))}
                  </ul>
                )}
              </ScrollArea>
            </div>

            {/* Right: Details/Edit/Add */}
            <div className="flex-1 p-6 overflow-auto flex flex-col">
              {/* Add Medication Form */}
              {showAdd && editingMedication ? (
                <div className="max-w-xl mx-auto w-full">
                  <h2 className="text-xl font-semibold mb-4">
                    Add New Medication
                  </h2>
                  <div className="space-y-4">
                    <div>
                      <Label>Name</Label>
                      <Input
                        value={editingMedication.name}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            name: e.target.value,
                          })
                        }
                        required
                        autoFocus
                      />
                    </div>
                    <div>
                      <Label>Description</Label>
                      <Textarea
                        value={editingMedication.description}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            description: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label>Dosage Guidelines</Label>
                      <Textarea
                        value={editingMedication.dosage_guidelines}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            dosage_guidelines: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label>Common Side Effects</Label>
                      <Textarea
                        value={editingMedication.common_side_effects}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            common_side_effects: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label>Category</Label>
                      <Input
                        value={editingMedication.category}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            category: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label>Clinic Specific Notes</Label>
                      <Textarea
                        value={editingMedication.clinic_specific_notes}
                        onChange={(e) =>
                          setEditingMedication({
                            ...editingMedication,
                            clinic_specific_notes: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="flex gap-2 mt-6 justify-end">
                    <Button
                      variant="ghost"
                      onClick={() => setShowAdd(false)}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleSaveMedication} disabled={saving}>
                      {saving ? (
                        <Loader2 className="animate-spin w-4 h-4 mr-2" />
                      ) : null}
                      Save
                    </Button>
                  </div>
                </div>
              ) : selectedMedicationId ? (
                // Medication Details Panel
                (() => {
                  const med = allMedications.find(
                    (m) => m.id === selectedMedicationId,
                  );
                  if (!med) return null;
                  const isAssociated = associatedMedicationIds.has(med.id!);
                  return (
                    <div className="max-w-xl mx-auto w-full flex flex-col gap-4">
                      <div className="flex items-center gap-3 mb-2">
                        <h2 className="text-2xl font-bold flex-1">
                          {med.name}
                        </h2>
                        {isAssociated && (
                          <>
                            <span className="sr-only">Associated</span>
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          </>
                        )}
                      </div>
                      <div className="space-y-3">
                        {med.description && (
                          <div>
                            <h3 className="font-medium text-gray-700 mb-1">
                              Description
                            </h3>
                            <p className="text-gray-800 whitespace-pre-line">
                              {med.description}
                            </p>
                          </div>
                        )}
                        {med.dosage_guidelines && (
                          <div>
                            <h3 className="font-medium text-gray-700 mb-1">
                              Dosage Guidelines
                            </h3>
                            <p className="text-gray-800 whitespace-pre-line">
                              {med.dosage_guidelines}
                            </p>
                          </div>
                        )}
                        {med.common_side_effects && (
                          <div>
                            <h3 className="font-medium text-gray-700 mb-1">
                              Common Side Effects
                            </h3>
                            <p className="text-gray-800 whitespace-pre-line">
                              {med.common_side_effects}
                            </p>
                          </div>
                        )}
                        {med.category && (
                          <div>
                            <h3 className="font-medium text-gray-700 mb-1">
                              Category
                            </h3>
                            <p className="text-gray-800 whitespace-pre-line">
                              {med.category}
                            </p>
                          </div>
                        )}
                        {med.clinic_specific_notes && (
                          <div>
                            <h3 className="font-medium text-gray-700 mb-1">
                              Clinic Notes
                            </h3>
                            <p className="text-gray-800 whitespace-pre-line">
                              {med.clinic_specific_notes}
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2 mt-6">
                        {!isAssociated ? (
                          <Button
                            onClick={() => handleAssociate(med.id!)}
                            disabled={saving}
                            className="flex-1"
                          >
                            {saving ? (
                              <Loader2 className="animate-spin w-4 h-4 mr-2" />
                            ) : null}
                            Associate
                          </Button>
                        ) : (
                          // Show Remove Association button if medication is associated with this clinic
                          med.associatedClinicIds &&
                          med.associatedClinicIds.includes(clinic.id) && (
                            <Button
                              variant="destructive"
                              disabled={saving}
                              className="flex-1"
                              title="Remove this medication's association with this clinic"
                              onClick={() => handleRemoveAssociation(med.id!)}
                            >
                              {saving ? (
                                <Loader2 className="animate-spin w-4 h-4 mr-2" />
                              ) : null}
                              Remove Association
                            </Button>
                          )
                        )}
                      </div>
                    </div>
                  );
                })()
              ) : (
                // Empty State Panel
                <div className="flex flex-1 flex-col justify-center items-center text-gray-400">
                  <span className="text-lg">
                    Select a medication or add a new one.
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
