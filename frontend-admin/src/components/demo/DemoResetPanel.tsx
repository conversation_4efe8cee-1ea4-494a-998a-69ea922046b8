import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle2, 
  Database,
  Users,
  MessageSquare,
  Calendar,
  Activity,
  FileText,
  Loader2
} from 'lucide-react';
import apiClient from '@/lib/apiClient';
import { toast } from 'sonner';

interface DemoStatus {
  is_production: boolean;
  demo_accounts_exist: boolean;
  data_counts: {
    patients: number;
    appointments: number;
    chat_messages: number;
    weight_logs: number;
    side_effects: number;
    clinical_notes: number;
  };
  demo_accounts: {
    clinician: {
      exists: boolean;
      name: string | null;
    };
    patient: {
      exists: boolean;
      name: string | null;
    };
  };
}

export default function DemoResetPanel() {
  const [status, setStatus] = useState<DemoStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResetting, setIsResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const fetchStatus = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching demo status...');
      
      // Log the request details for debugging
      console.log('API Client base URL:', apiClient.defaults.baseURL);
      console.log('Request URL will be:', apiClient.defaults.baseURL + '/demo/status');
      
      const response = await apiClient.get<DemoStatus>('/demo/status');
      console.log('Demo status response:', response.data);
      setStatus(response.data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching demo status:', err);
      console.error('Error response:', err.response);
      if (err.response?.data?.detail) {
        console.error('Error detail:', err.response.data.detail);
        setError(err.response.data.detail);
      } else {
        setError('Failed to fetch demo status');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  const handleReset = async () => {
    try {
      setIsResetting(true);
      setShowConfirmDialog(false);
      
      const response = await apiClient.post('/demo/reset', {
        confirm: true
      });
      
      if (response.data.success) {
        toast.success('Demo environment reset successfully!', {
          description: 'All demo data has been cleared and reseeded.',
          duration: 5000,
        });
        
        // Refresh status after reset
        await fetchStatus();
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to reset demo environment';
      toast.error('Reset failed', {
        description: errorMessage,
        duration: 5000,
      });
      console.error('Error resetting demo:', err);
    } finally {
      setIsResetting(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </CardContent>
      </Card>
    );
  }

  if (error || !status) {
    return (
      <Card>
        <CardContent className="py-12">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || 'Failed to load demo status'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const isProduction = status?.is_production || false;
  const totalDataPoints = status?.data_counts 
    ? Object.values(status.data_counts).reduce((a, b) => a + b, 0)
    : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Demo Environment Management
            </CardTitle>
            <CardDescription>
              Reset and manage demo data for investor presentations
            </CardDescription>
          </div>
          <Badge variant={isProduction ? "destructive" : "secondary"}>
            {isProduction ? "PRODUCTION" : "DEVELOPMENT"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Warning for production */}
        {isProduction && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Production Environment</AlertTitle>
            <AlertDescription>
              Demo reset is disabled in production environments for safety.
            </AlertDescription>
          </Alert>
        )}

        {/* Demo Accounts Status */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-700">Demo Accounts</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
              <Users className="h-4 w-4 text-blue-600" />
              <div className="flex-1">
                <p className="text-sm font-medium">Clinician</p>
                <p className="text-xs text-gray-600">
                  {status?.demo_accounts?.clinician?.exists 
                    ? status.demo_accounts.clinician.name 
                    : 'Not found'}
                </p>
              </div>
              {status?.demo_accounts?.clinician?.exists ? (
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
            </div>
            
            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
              <Users className="h-4 w-4 text-purple-600" />
              <div className="flex-1">
                <p className="text-sm font-medium">Patient</p>
                <p className="text-xs text-gray-600">
                  {status?.demo_accounts?.patient?.exists 
                    ? status.demo_accounts.patient.name 
                    : 'Not found'}
                </p>
              </div>
              {status?.demo_accounts?.patient?.exists ? (
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
            </div>
          </div>
        </div>

        {/* Data Summary */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-700">Current Data</h3>
          <div className="grid grid-cols-3 gap-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Users className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Patients</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.patients || 0}</p>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Appointments</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.appointments || 0}</p>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <MessageSquare className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Messages</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.chat_messages || 0}</p>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Activity className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Weight Logs</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.weight_logs || 0}</p>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <AlertTriangle className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Side Effects</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.side_effects || 0}</p>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <FileText className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">Clinical Notes</span>
              </div>
              <p className="text-lg font-semibold">{status?.data_counts?.clinical_notes || 0}</p>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <span className="text-sm font-medium text-blue-900">Total Data Points</span>
            <span className="text-lg font-semibold text-blue-900">{totalDataPoints}</span>
          </div>
        </div>

        {/* Reset Button */}
        <div className="pt-4 border-t">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                variant="destructive" 
                className="w-full"
                disabled={isProduction || isResetting}
              >
                {isResetting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resetting Demo...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset Demo Environment
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Reset Demo Environment?</AlertDialogTitle>
                <AlertDialogDescription className="space-y-3">
                  <p>This action will:</p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Delete all patient data except demo accounts</li>
                    <li>Clear all appointments, messages, and clinical data</li>
                    <li>Reseed with fresh demo data</li>
                    <li>Reset all timestamps to recent dates</li>
                  </ul>
                  <p className="font-semibold text-red-600 pt-2">
                    This action cannot be undone!
                  </p>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleReset}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Reset Demo
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          
          <p className="text-xs text-gray-500 mt-2 text-center">
            Use this between investor demos to ensure consistent presentation
          </p>
        </div>

        {/* Refresh Status Button */}
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchStatus}
          className="w-full"
        >
          <RefreshCw className="mr-2 h-3 w-3" />
          Refresh Status
        </Button>
      </CardContent>
    </Card>
  );
}