import React from "react";
import { useAuth } from "@pulsetrack/shared-frontend"; // Import useAuth
import { But<PERSON> } from "@/components/ui/button"; // Import Button
import { ThemeToggle } from "@/components/theme/ThemeToggle";

// Removed unused imports: useState, Sun, Moon, Bell, Search, Input, DropdownMenu*, Avatar*, useClerk
/**
 * Simplified HeaderBar Component for Admin Portal
 *
 * Renders a simple header bar with just the application title,
 * similar to the patient app style.
 */
const HeaderBar: React.FC = () => {
  // Removed state and handlers related to icons/dropdown
  const { signOut } = useAuth(); // Get signOut function

  return (
    // Dark mode aware header with theme-based colors
    <header className="sticky top-0 z-50 w-full bg-background border-b border-border shadow-sm">
      {/* Changed to justify-between to add button on the right */}
      <div className="flex justify-between items-center h-14 px-4">
        {" "}
        {/* Removed container class, added padding */}
        {/* Application Name */}
        {/* Consider adding a logo component here later if needed */}
        <span className="text-lg font-semibold">PulseTrack Admin</span>
        {/* Right side controls */}
        <div className="flex items-center gap-2">
          <ThemeToggle />
          <Button variant="ghost" onClick={signOut}>
            Logout
          </Button>
        </div>
      </div>
    </header>
  );
};

export default HeaderBar;
