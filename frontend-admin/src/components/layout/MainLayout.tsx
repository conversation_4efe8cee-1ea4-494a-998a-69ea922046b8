import React from "react";
import { Outlet } from "react-router-dom"; // Import Outlet
import HeaderBar from "./HeaderBar"; // Import the HeaderBar component
import AdminBottomNavigation from "../navigation/AdminBottomNavigation"; // Import the Bottom Navigation component

// Remove children from props as Outlet will handle nested routes
// interface MainLayoutProps {
//   children: React.ReactNode;
// }

const MainLayout: React.FC = () => {
  // Remove props destructuring
  return (
    <div className="flex flex-col min-h-screen">
      {/* Use the HeaderBar component */}
      <HeaderBar />

      <div className="flex flex-1">
        {" "}
        {/* Container for potential sidebar + main content */}
        {/* Example Sidebar (Optional) - Placeholder */}
        {/* <aside className="fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block border-r">
          <div className="relative overflow-hidden h-full py-6 pr-6 lg:py-8">
            Sidebar Content
          </div>
        </aside> */}
        {/* Main Content Area - Added padding and background consistent with clinician layout */}
        {/* Added pb-20 to account for bottom navigation height (h-16 + padding) */}
        <main className="flex-1 p-6 bg-gray-50 dark:bg-gray-900 pb-20">
          <Outlet /> {/* Render child routes here */}
        </main>
      </div>

      {/* Add the Bottom Navigation component */}
      <AdminBottomNavigation />

      {/* Footer removed for consistency */}
    </div>
  );
};

export default MainLayout;
