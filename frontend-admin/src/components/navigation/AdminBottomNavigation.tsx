import React from "react";
import { NavLink } from "react-router-dom";
import {
  LayoutDashboard,
  Building2,
  UserPlus,
  UserCircle,
  Users,
} from "lucide-react"; // Added UserCircle, Users

interface NavItem {
  path: string;
  label: string;
  icon: React.ElementType;
}

// Define navigation items for the Admin portal
const navItems: NavItem[] = [
  { path: "/", label: "Dashboard", icon: LayoutDashboard },
  { path: "/clinics", label: "Clinics", icon: Building2 },
  { path: "/invites", label: "Invites", icon: UserPlus },
  { path: "/clinicians", label: "Clinicians", icon: Users }, // Added Clinicians item
  { path: "/profile", label: "Profile", icon: UserCircle }, // Added Profile item
];

const AdminBottomNavigation: React.FC = () => {
  const activeClassName = "text-primary"; // Tailwind class for active link
  const inactiveClassName = "text-muted-foreground";

  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-background border-t border-border shadow-md z-50">
      <div className="flex justify-around items-center h-full max-w-md mx-auto px-2">
        {navItems.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            end={item.path === "/"} // Use 'end' prop only for the dashboard link
            className={({ isActive }) =>
              `flex flex-col items-center justify-center p-2 rounded-md transition-colors duration-150 ease-in-out ${
                isActive ? activeClassName : inactiveClassName
              } hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50`
            }
          >
            {(
              { isActive }, // Use function as children to access isActive
            ) => (
              <>
                <item.icon
                  className="h-6 w-6 mb-1"
                  strokeWidth={isActive ? 2.5 : 2}
                />
                <span className="text-xs font-medium">{item.label}</span>
              </>
            )}
          </NavLink>
        ))}
      </div>
    </nav>
  );
};

export default AdminBottomNavigation;
