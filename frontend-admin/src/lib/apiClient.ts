import axios from "axios";
// Remove useAuth import, as it cannot be used here
// Determine the base URL based on the environment
// In development (using V<PERSON>'s proxy), relative paths work.
// In production, you might need an absolute URL.
const baseURL = import.meta.env.VITE_API_BASE_URL || "/api/v1"; // Default to /api/v1 for API endpoints

const apiClient = axios.create({
  baseURL: baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add authentication interceptor using Clerk
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // Access the token via the global Clerk instance
      // Ensure Clerk is loaded and there's an active session
      // @ts-expect-error - Accessing global Clerk instance
      if (window.Clerk && window.Clerk.session) {
        // Fetch the token using the specific template if required by the backend
        // @ts-expect-error - Accessing global Clerk instance method
        const token = await window.Clerk.session.getToken({
          template: "CodenamePulsetrack",
        });
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } else {
        console.warn("Clerk session not available for adding auth token.");
      }
    } catch (error) {
      console.error("Error fetching Clerk token via global instance:", error);
    }
    return config;
  },
  (error) => {
    // Do something with request error
    return Promise.reject(error);
  },
);

// TODO: Add response interceptors for error handling (e.g., 401 redirects) if needed

export default apiClient;
