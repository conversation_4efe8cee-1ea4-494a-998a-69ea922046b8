import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter } from "react-router-dom"; // Import BrowserRouter
import "./index.css";
import App from "./App.tsx";
import { AuthProvider } from "@pulsetrack/shared-frontend"; // Import shared AuthProvider

// Import your publishable key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key");
}

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      {" "}
      {/* Wrap with BrowserRouter */}
      <AuthProvider publishableKey={PUBLISHABLE_KEY}>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </StrictMode>,
);
