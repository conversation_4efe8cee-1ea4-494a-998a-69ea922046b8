import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUser, useSignUp } from "@clerk/clerk-react"; // Import Clerk hooks
import {
  AuthInput,
  AuthButton,
  AuthErrorMessage,
  AuthLoadingIndicator,
} from "@pulsetrack/shared-frontend";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"; // Use local shadcn components
import { AxiosError } from "axios"; // Import AxiosError

const AcceptInvitationPage: React.FC = () => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  // Use Clerk hooks directly for state and actions
  const { isLoaded, isSignedIn } = useUser();
  const { signUp, setActive } = useSignUp();
  const navigate = useNavigate();

  // Redirect if user is already signed in
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      navigate("/dashboard"); // Redirect to ADMIN dashboard
    }
  }, [isLoaded, isSignedIn, navigate]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (!signUp || !setActive) {
      setError("Sign up context is not available. Cannot accept invitation.");
      console.error("Clerk signUp or setActive is not available.");
      return;
    }

    setLoading(true);
    try {
      // Clerk handles token verification via URL. We just need to set the password.
      const result = await signUp.update({ password });

      if (result.status === "complete") {
        // Set the session explicitly after successful sign-up/invitation acceptance
        await setActive({ session: result.createdSessionId });
        // The useEffect hook watching isSignedIn should handle the redirect.
        console.log(
          "Admin invitation accepted and password set successfully. Redirecting...",
        );
      } else {
        // Handle potential issues like password complexity requirements not met
        console.error("Clerk sign up update status:", result.status, result);
        // Provide a generic message, specific errors are caught below
        const specificError =
          "An issue occurred during account activation. Please check password requirements or try again.";
        setError(specificError);
      }
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        console.error(
          "Admin invitation acceptance/password set failed:",
          err.response?.data,
        );
        // Use a generic error message for security
        const genericError =
          err.response?.data?.detail ||
          "Failed to activate account. Please check password requirements or try again.";
        setError(genericError);
      } else {
        console.error("Admin invitation acceptance/password set failed:", err);
        // Use a generic error message for security
        const genericError =
          "Failed to activate account. Please check password requirements or try again.";
        setError(genericError);
      }
    } finally {
      setLoading(false);
    }
  };

  // Render loading indicator until Clerk's user state is loaded
  if (!isLoaded) {
    return (
      <div
        className="flex justify-center items-center min-h-screen"
        role="status"
      >
        <AuthLoadingIndicator />
        <p className="ml-2">Loading...</p>
      </div>
    );
  }

  // If user becomes signed in after load (e.g., via redirect), let useEffect handle redirection
  if (isSignedIn) {
    return (
      <div
        className="flex justify-center items-center min-h-screen"
        role="status"
      >
        <AuthLoadingIndicator />
        <p className="ml-2">Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Accept Admin Invitation
          </CardTitle>
          <CardDescription className="text-center">
            Welcome! Set your password to activate your admin account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <AuthInput
              label="Password"
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your new password"
              disabled={loading}
            />
            <AuthInput
              label="Confirm Password"
              id="confirm-password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              placeholder="Confirm your new password"
              disabled={loading}
            />
            {error && (
              <div role="alert">
                <AuthErrorMessage message={error} />
              </div>
            )}
            <AuthButton type="submit" disabled={loading} className="w-full">
              {loading ? (
                <AuthLoadingIndicator />
              ) : (
                "Set Password & Activate Account"
              )}
            </AuthButton>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm text-gray-600">
          Need help? Contact support.
        </CardFooter>
      </Card>
    </div>
  );
};

export default AcceptInvitationPage;
