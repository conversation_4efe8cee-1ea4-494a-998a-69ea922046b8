import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom"; // Import Link for navigation
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button"; // Import Button for links
import { Skeleton } from "@/components/ui/skeleton"; // Import Skeleton for loading state
// import { Users } from 'lucide-react'; // Optional: Import icon if needed for card header - Removed as unused
// Assuming an API client or fetch function exists, e.g., in '@/lib/api'
import apiClient from "@/lib/apiClient"; // Use default import
import DemoResetPanel from "@/components/demo/DemoResetPanel";

// Define an interface for the expected data structure
interface DashboardCounts {
  clinics_count: number;
  active_invitations_count: number;
  total_clinicians_count: number;
}

const AdminDashboardPage: React.FC = () => {
  const [counts, setCounts] = useState<DashboardCounts | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCounts = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Use the relative path assuming apiClient handles the base URL
        // CORRECTED: Add /api/v1 prefix for Vite proxy
        const response = await apiClient.get<DashboardCounts>(
          "/stats/dashboard-counts",
        );
        setCounts(response.data); // Extract the data from the response
      } catch (err) {
        console.error("Failed to fetch dashboard counts:", err);
        setError("Failed to load dashboard data. Please try refreshing.");
        setCounts(null); // Clear counts on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchCounts();
  }, []);

  // Helper to render count or skeleton
  const renderCount = (count: number | undefined) => {
    if (isLoading) {
      return <Skeleton className="h-6 w-10" />;
    }
    if (typeof count === "number") {
      return <span className="text-2xl font-bold">{count}</span>;
    }
    return <span className="text-2xl font-bold">-</span>; // Placeholder if data is missing but not loading
  };

  return (
    <div className="space-y-6">
      {" "}
      {/* Added spacing */}
      <h1 className="text-2xl font-semibold tracking-tight">Admin Dashboard</h1>
      {error && (
        <Card className="border-destructive bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Clinic Management Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle className="text-base font-medium">Clinics</CardTitle>
              <CardDescription>Manage clinic information.</CardDescription>
            </div>
            {renderCount(counts?.clinics_count)}
          </CardHeader>
          <CardContent className="flex flex-col space-y-2 pt-4">
            {" "}
            {/* Added padding top */}
            <p className="text-xs text-muted-foreground">
              View, edit, or add new clinics.
            </p>
            <Link to="/clinics" className="mt-auto pt-2">
              {" "}
              {/* Added padding top */}
              <Button variant="outline" size="sm">
                Manage Clinics
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Clinician Invites Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle className="text-base font-medium">
                Active Invites
              </CardTitle>
              <CardDescription>Invite new clinicians.</CardDescription>
            </div>
            {renderCount(counts?.active_invitations_count)}
          </CardHeader>
          <CardContent className="flex flex-col space-y-2 pt-4">
            <p className="text-xs text-muted-foreground">
              Manage active invitation codes.
            </p>
            <Link to="/invites" className="mt-auto pt-2">
              <Button variant="outline" size="sm">
                Manage Invites
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Clinicians Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle className="text-base font-medium">
                Clinicians
              </CardTitle>
              <CardDescription>View clinician status.</CardDescription>
            </div>
            {renderCount(counts?.total_clinicians_count)}
          </CardHeader>
          <CardContent className="flex flex-col space-y-2 pt-4">
            <p className="text-xs text-muted-foreground">
              See all active clinicians.
            </p>
            <Link to="/clinicians" className="mt-auto pt-2">
              <Button variant="outline" size="sm">
                View Clinicians
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Add more dashboard cards as needed */}
      </div>
      
      {/* Demo Reset Panel - Only show in development */}
      {process.env.NODE_ENV === "development" && (
        <div className="mt-8">
          <DemoResetPanel />
        </div>
      )}
    </div>
  );
};

export default AdminDashboardPage;
