import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { useUser, useClerk } from "@clerk/clerk-react"; // Import useClerk for signout

const AdminProfilePage: React.FC = () => {
  const { user } = useUser();
  const { signOut } = useClerk(); // Get the signOut function

  // Placeholder - replace with actual state and update logic
  const handleProfileUpdate = (event: React.FormEvent) => {
    event.preventDefault();
    // TODO: Add logic to update admin profile via API (if applicable)
    console.log("Profile update submitted");
    // TODO: Add feedback to user
  };

  if (!user) {
    return <div>Loading profile...</div>; // Or redirect, handle loading state
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold tracking-tight">Admin Profile</h1>

      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Your Profile</CardTitle>
            <CardDescription>Manage your account details.</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleProfileUpdate} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                {/* Display user's name from Clerk, potentially make editable */}
                <Input
                  id="fullName"
                  defaultValue={user.fullName ?? ""}
                  placeholder="Your Name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                {/* Display primary email from Clerk, usually not editable */}
                <Input
                  id="email"
                  type="email"
                  value={user.primaryEmailAddress?.emailAddress ?? ""}
                  readOnly
                  disabled
                />
              </div>
              {/* Add other relevant admin profile fields if needed */}
              {/* Example: Role display */}
              {/* <div className="space-y-2">
                <Label>Role</Label>
                <Input value={user.publicMetadata?.role as string ?? 'Admin'} readOnly disabled />
              </div> */}

              <Button type="submit" className="w-full">
                Update Profile
              </Button>
            </form>
            {/* Add Logout Button */}
            <Button
              variant="outline"
              className="w-full mt-4" // Add margin top
              onClick={() => signOut()}
            >
              Logout
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminProfilePage;
