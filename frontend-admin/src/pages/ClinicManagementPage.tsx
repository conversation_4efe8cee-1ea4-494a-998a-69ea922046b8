import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { <PERSON><PERSON>ooter } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components
import apiClient from "@/lib/apiClient";
import { AddNewClinicModal } from "@/components/clinics/AddNewClinicModal";
import { Toaster } from "@/components/ui/sonner"; // Import Toaster
import { toast } from "sonner"; // Import toast from the library itself
import { Pill } from "lucide-react"; // For medication icons
import { ClinicMedicationsModal } from "@/components/clinics/ClinicMedicationsModal";
import { AxiosError } from "axios"; // Import AxiosError

// Define Clinic interface based on backend schema (ClinicRead)
export interface ClinicRead {
  // Added export keyword
  id: string;
  name: string;
  address: string | null;
  website_url: string | null;
  last_scraped_at?: string | null; // Add optional last scraped date (string from JSON)
}

const ClinicManagementPage: React.FC = () => {
  const [clinics, setClinics] = useState<ClinicRead[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [clinicToEdit, setClinicToEdit] = useState<ClinicRead | null>(null);
  const [clinicToDelete, setClinicToDelete] = useState<ClinicRead | null>(null); // State for clinic targeted for deletion
  const [isDeleting, setIsDeleting] = useState<boolean>(false); // State for delete operation loading
  const [isMedicationsModalOpen, setIsMedicationsModalOpen] = useState(false);
  const [selectedClinic, setSelectedClinic] = useState<ClinicRead | null>(null);

  const fetchClinics = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch data using apiClient
      const apiResponse = await apiClient.get<{
        items: ClinicRead[];
        total: number;
      }>("/admin/clinics"); // Fixed path - apiClient already handles /api/v1 prefix
      // Ensure apiResponse.data.items is an array before setting state
      if (Array.isArray(apiResponse.data.items)) {
        setClinics(apiResponse.data.items);
      } else {
        console.error("Invalid data format received:", apiResponse.data);
        setError("Received invalid data format from server.");
        setClinics([]); // Set to empty array on invalid format
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error("Error fetching clinics:", error);
        // Provide more specific error message if possible
        const message =
          error.response?.data?.detail ||
          "Failed to fetch clinics. Please try again later.";
        setError(message);
        setClinics([]); // Clear clinics on error
      } else {
        console.error("Unknown error fetching clinics:", error);
        setError("An unknown error occurred while fetching clinics.");
        setClinics([]); // Clear clinics on error
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClinics();
  }, []); // Empty dependency array ensures fetchClinics runs only once on mount

  const handleModalClose = () => {
    setIsModalOpen(false);
    setClinicToEdit(null); // Clear clinicToEdit when closing
  };

  const handleModalSuccess = () => {
    setIsModalOpen(false);
    setClinicToEdit(null); // Clear clinicToEdit on success
    fetchClinics();
  };

  const handleDeleteClinic = async () => {
    if (!clinicToDelete) return;
    setIsDeleting(true);
    try {
      await apiClient.delete(`/api/v1/admin/clinics/${clinicToDelete.id}`);
      toast.success("Clinic Deleted", {
        description: `${clinicToDelete.name} has been deleted successfully.`,
      });
      setClinicToDelete(null); // Close dialog implicitly by clearing the target
      fetchClinics(); // Refresh the list
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error("Error deleting clinic:", error);
        toast.error("Deletion Failed", {
          description:
            error.response?.data?.detail ||
            `Could not delete ${clinicToDelete.name}.`,
        });
      } else {
        console.error("Unknown error deleting clinic:", error);
        toast.error("Deletion Failed", {
          description: "An unknown error occurred while deleting the clinic.",
        });
      }
    } finally {
      setIsDeleting(false);
    }
  };

  const handleManageMedications = (clinic: ClinicRead) => {
    setSelectedClinic(clinic);
    setIsMedicationsModalOpen(true);
  };

  return (
    <div className="space-y-6">
      {" "}
      {/* Added spacing */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Clinic Management
        </h1>
        <Button
          onClick={() => {
            setClinicToEdit(null);
            setIsModalOpen(true);
          }}
        >
          Add New Clinic
        </Button>{" "}
        {/* Ensure edit state is cleared */}
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Clinics</CardTitle>
          <CardDescription>
            List of registered clinics on the platform.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center p-4">Loading clinics...</div>
          ) : error ? (
            <div className="text-center p-4 text-red-600">{error}</div>
          ) : clinics.length === 0 ? (
            <div className="text-center p-4 text-muted-foreground">
              No clinics found.
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {" "}
              {/* Responsive grid */}
              {clinics.map((clinic) => (
                <Card key={clinic.id}>
                  <CardHeader>
                    <CardTitle>{clinic.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Address
                      </p>
                      <p>{clinic.address || "N/A"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Website
                      </p>
                      {clinic.website_url ? (
                        <a
                          href={clinic.website_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline break-all" // Added break-all for long URLs
                        >
                          {clinic.website_url}
                        </a>
                      ) : (
                        "N/A"
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end space-x-2">
                    {" "}
                    {/* Added CardFooter for actions */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setClinicToEdit(clinic);
                        setIsModalOpen(true);
                      }}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleManageMedications(clinic)}
                    >
                      <Pill className="h-4 w-4 mr-2" />
                      Medications
                    </Button>
                    {/* Wrap Delete button and Dialog logic */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        {/* Keep the onClick here to set the clinic *before* the dialog opens */}
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => setClinicToDelete(clinic)}
                        >
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      {/* Dialog Content specific to this clinic instance */}
                      {clinicToDelete &&
                        clinicToDelete.id === clinic.id && ( // Only render content if this is the clinic to delete
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Are you absolutely sure?
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will
                                permanently delete the clinic "
                                {clinicToDelete?.name}".
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel
                                onClick={() => setClinicToDelete(null)}
                                disabled={isDeleting}
                              >
                                Cancel
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={handleDeleteClinic}
                                disabled={isDeleting}
                              >
                                {isDeleting ? "Deleting..." : "Continue"}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        )}
                    </AlertDialog>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      <AddNewClinicModal
        isOpen={isModalOpen}
        clinicToEdit={clinicToEdit} // Pass the clinic to edit
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
      />
      <Toaster richColors />
      {selectedClinic && (
        <ClinicMedicationsModal
          isOpen={isMedicationsModalOpen}
          onClose={() => {
            setIsMedicationsModalOpen(false);
            setSelectedClinic(null);
          }}
          clinic={selectedClinic}
        />
      )}
      {/* Removed the single AlertDialog from here */}
    </div>
  );
};

export default ClinicManagementPage;
