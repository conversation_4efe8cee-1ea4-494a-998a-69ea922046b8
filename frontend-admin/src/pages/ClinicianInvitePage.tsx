import React, { useState, useEffect, useCallback } from "react"; // Added useCallback
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card"; // Added CardFooter
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
// Removed Table imports as they are no longer used
import { formatDateTime } from "@pulsetrack/shared-frontend";
import { Loader2, Trash2 } from "lucide-react"; // Added icons
import { AxiosError } from "axios"; // Import AxiosError

// Define interface for Clinic data
interface Clinic {
  id: string;
  name: string;
}

// Define interface for Clerk Invitation data (adjust based on actual API response)
interface ClerkInvitation {
  id: string;
  email_address: string;
  status: "pending" | "accepted" | "revoked"; // Example statuses
  created_at: string; // Backend sends ISO 8601 string
  public_metadata?: {
    // Added optional public_metadata
    initial_clinic_id?: string;
  };
}

const ClinicianInvitePage: React.FC = () => {
  // State variables for clinics and invite form
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [selectedClinicId, setSelectedClinicId] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [isSendingInvite, setIsSendingInvite] = useState<boolean>(false); // Renamed for clarity
  const [fetchClinicsError, setFetchClinicsError] = useState<string | null>(
    null,
  );

  // State variables for invitation list
  const [invitations, setInvitations] = useState<ClerkInvitation[]>([]);
  const [isLoadingInvitations, setIsLoadingInvitations] =
    useState<boolean>(true); // Start loading true
  const [fetchInvitationsError, setFetchInvitationsError] = useState<
    string | null
  >(null);
  const [deletingInvitationId, setDeletingInvitationId] = useState<
    string | null
  >(null); // Track deleting state

  // Fetch clinics and invitations
  const fetchData = useCallback(async () => {
    setIsLoadingInvitations(true);
    setFetchClinicsError(null);
    setFetchInvitationsError(null);

    try {
      // Fetch clinics (existing logic) - run concurrently
      const clinicsPromise = apiClient.get("/admin/clinics?limit=200");
      // Fetch invitations - run concurrently
      const invitationsPromise = apiClient.get(
        "/admin/invitations?status=pending",
      ); // Filter for pending

      const [clinicsResponse, invitationsResponse] = await Promise.all([
        clinicsPromise,
        invitationsPromise,
      ]);

      // Process clinics response
      if (clinicsResponse.data && Array.isArray(clinicsResponse.data.items)) {
        setClinics(clinicsResponse.data.items);
      } else {
        console.error(
          "Unexpected response format for clinics:",
          clinicsResponse.data,
        );
        setClinics([]);
        setFetchClinicsError(
          "Failed to fetch clinics due to unexpected response format.",
        );
      }

      // Process invitations response
      // Backend returns { invitations: ClerkInvitation[], total_count: number | null }
      // Access the 'invitations' array within the response data
      if (
        invitationsResponse.data &&
        Array.isArray(invitationsResponse.data.invitations)
      ) {
        // Sort invitations by creation date, newest first
        const sortedInvitations = invitationsResponse.data.invitations.sort(
          (a: ClerkInvitation, b: ClerkInvitation) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );
        setInvitations(sortedInvitations);
      } else {
        console.error(
          "Unexpected response format for invitations:",
          invitationsResponse.data,
        );
        setInvitations([]);
        setFetchInvitationsError(
          "Failed to fetch invitations due to unexpected response format.",
        );
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error("Error fetching data:", error);
        const errorMsg =
          error.response?.data?.detail ||
          "Failed to fetch data. Please try again.";
        // Distinguish between errors if needed, or show a general error
        setFetchClinicsError(errorMsg); // Or set a general fetch error
        setFetchInvitationsError(errorMsg);
        toast.error(errorMsg);
        setClinics([]);
        setInvitations([]);
      } else {
        console.error("Unknown error fetching data:", error);
        setFetchClinicsError("An unknown error occurred while fetching data.");
        setFetchInvitationsError(
          "An unknown error occurred while fetching data.",
        );
        toast.error("An unknown error occurred while fetching data.");
        setClinics([]);
        setInvitations([]);
      }
    } finally {
      setIsLoadingInvitations(false); // Combined loading state might be simpler if fetches are tied
    }
  }, []); // No dependencies, relies on manual refresh via fetchData call

  useEffect(() => {
    fetchData();
  }, [fetchData]); // Run fetchData on mount and when fetchData changes (it won't due to useCallback)

  // Handle invitation submission
  const handleInvite = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!email || !selectedClinicId) {
      toast.error("Please enter an email and select a clinic.");
      return;
    }

    setIsSendingInvite(true);

    try {
      const response = await apiClient.post("/admin/invitations", {
        email: email,
        clinic_id: selectedClinicId,
      });

      toast.success(response.data?.message || "Invitation sent successfully!");
      setEmail("");
      setSelectedClinicId("");
      fetchData(); // Refresh invitation list after sending
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error("Error sending invitation:", error);
        const errorMsg =
          error.response?.data?.detail ||
          "Failed to send invitation. Please try again.";
        toast.error(errorMsg);
      } else {
        console.error("Unknown error sending invitation:", error);
        toast.error("An unknown error occurred while sending the invitation.");
      }
    } finally {
      setIsSendingInvite(false);
    }
  };

  // Handle invitation deletion
  const handleDelete = async (invitationId: string) => {
    // Optional: Add confirmation dialog here later
    setDeletingInvitationId(invitationId); // Set loading state for this specific button
    try {
      await apiClient.delete(`/admin/invitations/${invitationId}`);
      toast.success("Invitation revoked successfully!");
      // Refresh list by filtering locally (faster UI update)
      setInvitations((prev) => prev.filter((inv) => inv.id !== invitationId));
      // Or uncomment below to refetch from server (ensures consistency)
      // fetchData();
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error("Error revoking invitation:", error);
        const errorMsg =
          error.response?.data?.detail ||
          "Failed to revoke invitation. Please try again.";
        toast.error(errorMsg);
      } else {
        console.error("Unknown error revoking invitation:", error);
        toast.error("An unknown error occurred while revoking the invitation.");
      }
    } finally {
      setDeletingInvitationId(null); // Clear loading state
    }
  };

  return (
    <div className="space-y-8">
      {" "}
      {/* Increased spacing */}
      <h1 className="text-2xl font-semibold tracking-tight">
        Invite Clinician
      </h1>
      {fetchClinicsError && ( // Keep specific error for clinics if needed
        <Alert variant="destructive">
          <AlertTitle>Error Loading Clinics</AlertTitle>
          <AlertDescription>
            {typeof fetchClinicsError === "string"
              ? fetchClinicsError
              : JSON.stringify(fetchClinicsError)}
          </AlertDescription>
        </Alert>
      )}
      {/* Send Invitation Card */}
      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Send Invitation</CardTitle>
            <CardDescription>
              Enter the clinician's details and assign them to a clinic.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleInvite} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Clinician Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={
                    isSendingInvite ||
                    clinics.length === 0 ||
                    !!fetchClinicsError
                  } // Disable based on sending state
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clinic">Assign to Clinic</Label>
                <Select
                  required
                  value={selectedClinicId}
                  onValueChange={setSelectedClinicId}
                  disabled={
                    isSendingInvite ||
                    clinics.length === 0 ||
                    !!fetchClinicsError
                  }
                >
                  <SelectTrigger id="clinic">
                    <SelectValue
                      placeholder={
                        clinics.length === 0 && !fetchClinicsError
                          ? "No clinics available"
                          : "Select a clinic"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {clinics.length > 0 ? (
                      clinics.map((clinic) => (
                        <SelectItem key={clinic.id} value={clinic.id}>
                          {clinic.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-clinics" disabled>
                        {fetchClinicsError
                          ? "Error loading clinics"
                          : "No clinics found"}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {clinics.length === 0 &&
                  !fetchClinicsError &&
                  !isSendingInvite && (
                    <p className="text-sm text-muted-foreground">
                      No clinics available. Please create a clinic first in
                      Clinic Management.
                    </p>
                  )}
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={
                  isSendingInvite || clinics.length === 0 || !!fetchClinicsError
                }
              >
                {isSendingInvite ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sending...
                  </>
                ) : (
                  "Send Invite"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
      {/* Invitations List Table */}
      <Card>
        <CardHeader>
          <CardTitle>Pending Invitations</CardTitle>
          <CardDescription>
            List of invitations sent to clinicians.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingInvitations && (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2">Loading invitations...</span>
            </div>
          )}
          {fetchInvitationsError && !isLoadingInvitations && (
            <Alert variant="destructive">
              <AlertTitle>Error Loading Invitations</AlertTitle>
              <AlertDescription>
                {typeof fetchInvitationsError === "string"
                  ? fetchInvitationsError
                  : JSON.stringify(fetchInvitationsError)}
              </AlertDescription>
            </Alert>
          )}
          {!isLoadingInvitations &&
            !fetchInvitationsError &&
            invitations.length === 0 && (
              <p className="text-center text-muted-foreground py-4">
                No pending invitations found.
              </p>
            )}
          {!isLoadingInvitations &&
            !fetchInvitationsError &&
            invitations.length > 0 && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {" "}
                {/* Responsive grid */}
                {invitations.map((invitation) => {
                  // Find clinic name using the ID from metadata
                  const clinicId =
                    invitation.public_metadata?.initial_clinic_id;
                  const clinicName =
                    clinics.find((c) => c.id === clinicId)?.name || "N/A";

                  return (
                    <Card key={invitation.id}>
                      <CardHeader>
                        {/* Use email as title or add another relevant field */}
                        <CardTitle className="text-lg break-all">
                          {invitation.email_address}
                        </CardTitle>
                        <CardDescription>
                          Sent:{" "}
                          {formatDateTime(invitation.created_at)}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Status
                          </p>
                          <p className="capitalize">{invitation.status}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Assigned Clinic
                          </p>
                          <p>{clinicName}</p>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-end">
                        {" "}
                        {/* Added CardFooter */}
                        <Button
                          variant="destructive" // Changed variant for delete
                          size="sm"
                          onClick={() => handleDelete(invitation.id)}
                          disabled={deletingInvitationId === invitation.id}
                          aria-label={`Revoke invitation for ${invitation.email_address}`}
                        >
                          {deletingInvitationId === invitation.id ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> // Added margin
                          ) : (
                            <Trash2 className="mr-2 h-4 w-4" /> // Added margin
                          )}
                          Revoke
                        </Button>
                      </CardFooter>
                    </Card>
                  );
                })}
              </div>
            )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClinicianInvitePage;
