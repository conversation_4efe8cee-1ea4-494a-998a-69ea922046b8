// frontend-admin/src/pages/ClinicianListPage.tsx
import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@clerk/clerk-react";
import { Pencil, Trash2 } from "lucide-react"; // Icons for buttons
import { AxiosResponse, AxiosError } from "axios"; // Import AxiosResponse and AxiosError
import apiClient from "@/lib/apiClient"; // Use default import

// Shadcn/ui components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Added Select component

// Define interfaces for the data we expect
interface Clinic {
  id: string;
  name: string;
}

interface Clinician {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  specialty?: string | null;
  is_active: boolean;
  clerk_id?: string;
  assigned_clinic?: Clinic;
  status?: "Active" | "Invited" | "Revoked";
}

interface Invitation {
  id: string;
  email_address: string;
  status: string;
  public_metadata?: {
    initial_clinic_id?: string;
    role?: string[];
  };
  created_at: string;
}

// Type for the data being edited in the dialog
type ClinicianEditData = {
  first_name: string;
  last_name: string;
  specialty: string;
  is_active: boolean;
  assigned_clinic_id: string | null; // Added clinic ID
};

const ClinicianListPage: React.FC = () => {
  const [clinicians, setClinicians] = useState<Clinician[]>([]);
  const [allClinics, setAllClinics] = useState<Clinic[]>([]); // State for clinics dropdown
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();

  // State for Edit Dialog
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingClinician, setEditingClinician] = useState<Clinician | null>(
    null,
  );
  const [editFormData, setEditFormData] = useState<ClinicianEditData>({
    first_name: "",
    last_name: "",
    specialty: "",
    is_active: true,
    assigned_clinic_id: null, // Initialize clinic ID
  });

  const [deletingClinicianId, setDeletingClinicianId] = useState<string | null>(
    null,
  );

  const fetchInitialData = useCallback(async () => {
    setLoading(true);
    setError(null);
    const token = await getToken({ template: "CodenamePulsetrack" });

    if (!token) {
      setError("Authentication token not available.");
      setLoading(false);
      return;
    }

    try {
      // Fetch active clinicians, pending invites, and all clinics concurrently
      const [
        activeCliniciansResponse,
        pendingInvitesResponse,
        allClinicsResponse,
      ]: [
        AxiosResponse<{ items: Clinician[] }>,
        AxiosResponse<{ invitations: Invitation[] }>,
        AxiosResponse<{ items: Clinic[] }>,
      ] = await Promise.all([
        apiClient.get<{ items: Clinician[] }>("/admin/clinicians", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        apiClient.get<{ invitations: Invitation[] }>(
          "/admin/invitations?status=pending",
          { headers: { Authorization: `Bearer ${token}` } },
        ),
        apiClient.get<{ items: Clinic[] }>("/admin/clinics?limit=200", {
          headers: { Authorization: `Bearer ${token}` },
        }), // Fetch all clinics
      ]);

      // Axios throws for non-2xx, so no need for .ok checks here

      const activeCliniciansData = activeCliniciansResponse.data.items || [];
      const pendingInvitesData = pendingInvitesResponse.data.invitations || [];
      const allClinicsData = allClinicsResponse.data.items || [];

      // Store all clinics for the dropdown
      setAllClinics(allClinicsData);
      const clinicMap: Record<string, string> = allClinicsData.reduce(
        (acc: Record<string, string>, clinic: Clinic) => {
          acc[clinic.id] = clinic.name;
          return acc;
        },
        {},
      );

      // --- Data Merging Logic ---
      const combinedList: Clinician[] = [];

      activeCliniciansData.forEach((clinician: Clinician) => {
        // Explicitly type clinician
        combinedList.push({
          ...clinician,
          status: "Active",
          assigned_clinic:
            clinician.assigned_clinic &&
            typeof clinician.assigned_clinic === "object" &&
            "name" in clinician.assigned_clinic
              ? {
                  id: clinician.assigned_clinic.id,
                  name: clinician.assigned_clinic.name,
                }
              : undefined,
        });
      });

      pendingInvitesData.forEach((invite: Invitation) => {
        if (!combinedList.some((c) => c.email === invite.email_address)) {
          combinedList.push({
            id: invite.id,
            email: invite.email_address,
            first_name: "Invited",
            last_name: "User",
            is_active: false,
            status: "Invited",
            assigned_clinic: invite.public_metadata?.initial_clinic_id
              ? {
                  id: invite.public_metadata.initial_clinic_id,
                  name:
                    clinicMap[invite.public_metadata.initial_clinic_id] ||
                    "Unknown Clinic",
                }
              : undefined,
          });
        }
      });

      setClinicians(combinedList);
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        console.error("Error fetching initial data:", err);
        setError(
          err.response?.data?.detail ||
            err.message ||
            "An unknown error occurred.",
        );
      } else if (err instanceof Error) {
        console.error("Error fetching initial data:", err);
        setError(err.message || "An unknown error occurred.");
      } else {
        console.error("Error fetching initial data:", err);
        setError("An unknown error occurred.");
      }
      // Clear data on error
      setClinicians([]);
      setAllClinics([]);
    } finally {
      setLoading(false);
    }
  }, [getToken]); // Added getToken dependency

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]); // Correct dependency

  // --- Edit Dialog Functions ---
  const handleEditClick = (clinician: Clinician) => {
    setEditingClinician(clinician);
    setEditFormData({
      first_name: clinician.first_name,
      last_name: clinician.last_name,
      specialty: clinician.specialty || "",
      is_active: clinician.is_active,
      assigned_clinic_id: clinician.assigned_clinic?.id || null, // Set initial clinic ID
    });
    setIsEditDialogOpen(true);
  };

  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleActiveSwitchChange = (checked: boolean) => {
    setEditFormData((prev) => ({ ...prev, is_active: checked }));
  };

  const handleClinicSelectChange = (value: string) => {
    // Allow unselecting clinic by selecting a placeholder or handling null
    setEditFormData((prev) => ({
      ...prev,
      assigned_clinic_id: value === "none" ? null : value,
    }));
  };

  const handleUpdateClinician = async () => {
    if (!editingClinician) return;
    setError(null);

    const token = await getToken({ template: "CodenamePulsetrack" });
    if (!token) {
      setError("Authentication token not available.");
      return;
    }

    // Prepare payload, ensuring assigned_clinic_id is included
    const payload = {
      first_name: editFormData.first_name,
      last_name: editFormData.last_name,
      specialty: editFormData.specialty || null, // Send null if empty
      is_active: editFormData.is_active,
      assigned_clinic_id: editFormData.assigned_clinic_id, // Can be string UUID or null
    };

    try {
      const response = await apiClient.put<{ item: Clinician }>(
        "/admin/clinicians/" + editingClinician.id,
        payload,
        { headers: { Authorization: `Bearer ${token}` } },
      );

      // Axios handles non-2xx, no need for .ok check

      const updatedClinicianData = response.data.item; // Backend now returns full clinician with clinic

      // Update local state, including the assigned_clinic object
      setClinicians((prev) =>
        prev.map((c) =>
          c.id === editingClinician.id
            ? { ...updatedClinicianData, status: "Active" }
            : c,
        ),
      );
      setIsEditDialogOpen(false);
      setEditingClinician(null);
    } catch (err: unknown) {
      let errorMsg = "An unknown error occurred during update.";
      if (err instanceof AxiosError) {
        console.error("Error updating clinician:", err);
        errorMsg = err.response?.data?.detail || err.message || errorMsg;
      } else if (err instanceof Error) {
        console.error("Error updating clinician:", err);
        errorMsg = err.message || errorMsg;
      } else {
        console.error("Error updating clinician:", err);
      }
      setError(errorMsg);
    }
  };

  // --- Delete Dialog Functions ---
  const handleDeleteClick = (clinicianId: string) => {
    setDeletingClinicianId(clinicianId);
  };

  const handleDeleteClinician = async () => {
    if (!deletingClinicianId) return;
    setError(null);

    const token = await getToken({ template: "CodenamePulsetrack" });
    if (!token) {
      setError("Authentication token not available.");
      return;
    }

    try {
      await apiClient.delete(
        "/admin/clinicians/" + deletingClinicianId,
        { headers: { Authorization: `Bearer ${token}` } },
      );

      // Axios handles non-2xx, no need for .ok check

      setClinicians((prev) => prev.filter((c) => c.id !== deletingClinicianId));
      setDeletingClinicianId(null);
    } catch (err: unknown) {
      let errorMsg = "An unknown error occurred during deletion.";
      if (err instanceof AxiosError) {
        console.error("Error deleting clinician:", err);
        errorMsg = err.response?.data?.detail || err.message || errorMsg;
      } else if (err instanceof Error) {
        console.error("Error deleting clinician:", err);
        errorMsg = err.message || errorMsg;
      } else {
        console.error("Error deleting clinician:", err);
      }
      setError(errorMsg);
      setDeletingClinicianId(null);
    }
  };

  // --- Render Logic ---
  const renderLoading = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {[...Array(8)].map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-8 w-16 mr-2" />
            <Skeleton className="h-8 w-16" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  const renderError = () => (
    <Alert variant="destructive">
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  );

  const renderClinicianCards = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {clinicians.map((clinician) => (
        <Card key={clinician.id}>
          <CardHeader>
            <CardTitle>
              {clinician.first_name} {clinician.last_name}
            </CardTitle>
            <CardDescription>{clinician.email}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground mb-2">
              Clinic:{" "}
              {clinician.assigned_clinic && clinician.assigned_clinic.name
                ? clinician.assigned_clinic.name
                : "N/A"}
            </div>
            <div className="text-sm text-muted-foreground mb-2">
              Specialty: {clinician.specialty || "N/A"}
            </div>
            <Badge
              variant={
                clinician.status === "Active"
                  ? clinician.is_active
                    ? "default"
                    : "outline"
                  : "secondary"
              }
            >
              {clinician.status === "Active"
                ? clinician.is_active
                  ? "Active"
                  : "Inactive"
                : clinician.status}
            </Badge>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            {/* Edit Button & Dialog */}
            <Dialog
              open={isEditDialogOpen && editingClinician?.id === clinician.id}
              onOpenChange={(open) => {
                if (!open) {
                  setIsEditDialogOpen(false);
                  setEditingClinician(null);
                  setError(null);
                }
              }}
            >
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditClick(clinician)}
                  disabled={clinician.status === "Invited"}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Edit Clinician</DialogTitle>
                  <DialogDescription>
                    Update the details for {editingClinician?.first_name}{" "}
                    {editingClinician?.last_name}.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  {/* Form Fields */}
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="first_name" className="text-right">
                      First Name
                    </Label>
                    <Input
                      id="first_name"
                      name="first_name"
                      value={editFormData.first_name}
                      onChange={handleEditFormChange}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="last_name" className="text-right">
                      Last Name
                    </Label>
                    <Input
                      id="last_name"
                      name="last_name"
                      value={editFormData.last_name}
                      onChange={handleEditFormChange}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="specialty" className="text-right">
                      Specialty
                    </Label>
                    <Input
                      id="specialty"
                      name="specialty"
                      value={editFormData.specialty}
                      onChange={handleEditFormChange}
                      className="col-span-3"
                    />
                  </div>
                  {/* Clinic Selector */}
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="clinic" className="text-right">
                      Clinic
                    </Label>
                    <Select
                      value={editFormData.assigned_clinic_id || "none"}
                      onValueChange={handleClinicSelectChange}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select Clinic" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {allClinics.map((clinic) => (
                          <SelectItem key={clinic.id} value={clinic.id}>
                            {clinic.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Active Switch */}
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="is_active" className="text-right">
                      Active
                    </Label>
                    <Switch
                      id="is_active"
                      checked={editFormData.is_active}
                      onCheckedChange={handleActiveSwitchChange}
                      className="col-span-3"
                    />
                  </div>
                  {/* Error Display */}
                  {error && editingClinician?.id === clinician.id && (
                    <Alert variant="destructive" className="col-span-4">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsEditDialogOpen(false);
                        setEditingClinician(null);
                        setError(null);
                      }}
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button onClick={handleUpdateClinician}>Save Changes</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Delete Button & Dialog */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteClick(clinician.id)}
                  disabled={clinician.status === "Invited"}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    the clinician record for {clinician.first_name}{" "}
                    {clinician.last_name} from the database. It will NOT delete
                    the user from Clerk Authentication.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel
                    onClick={() => setDeletingClinicianId(null)}
                  >
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteClinician}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Clinicians</CardTitle>
        <CardDescription>
          A list of active clinicians and pending invitations.
        </CardDescription>
        {error && !editingClinician && !deletingClinicianId && renderError()}
      </CardHeader>
      <CardContent>
        {loading && renderLoading()}
        {!loading && !error && clinicians.length === 0 && (
          <p className="text-center text-muted-foreground">
            No clinicians found.
          </p>
        )}
        {!loading && !error && clinicians.length > 0 && renderClinicianCards()}
      </CardContent>
    </Card>
  );
};

export default ClinicianListPage;
