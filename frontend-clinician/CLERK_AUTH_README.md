# Clerk Authentication Implementation

This document outlines the Clerk authentication implementation in the PulseTrack Clinician Portal.

## Setup Overview

The authentication system uses <PERSON> for user management, sign-in, and sign-up. <PERSON> provides a secure and easy-to-use authentication service with customizable UI components.

## Key Components

### 1. Clerk<PERSON><PERSON><PERSON> (main.tsx)

The `ClerkProvider` wraps the entire application in `main.tsx` and provides the authentication context:

```tsx
<ClerkProvider
  publishableKey={PUBLISHABLE_KEY}
  appearance={
    {
      // Appearance configuration
    }
  }
>
  <AuthProvider>
    <App />
  </AuthProvider>
</ClerkProvider>
```

### 2. Custom AuthProvider (src/lib/auth/AuthProvider.tsx)

A custom AuthProvider that extends <PERSON>'s functionality and provides a simplified interface for the rest of the application:

```tsx
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isSignedIn, isLoaded, userId, getToken } = useAuth();

  // Store the token in localStorage
  useEffect(() => { ... });

  const value = {
    isAuthenticated: isSignedIn || false,
    isLoading: !isLoaded,
    userId: userId || null,
    getToken: async () => { ... },
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

### 3. Protected Routes (App.tsx)

Routes that require authentication are wrapped with a `ProtectedRoute` component:

```tsx
const ProtectedRoute: React.FC<{ children: React.ReactElement }> = ({
  children,
}) => {
  const { isAuthenticated, isLoading } = useAuthContext();

  if (isLoading) {
    // Show loading indicator
  }

  if (!isAuthenticated) {
    return <Navigate to="/sign-in" replace />;
  }

  return children;
};
```

### 4. User Interface Components

- **UserButton**: A component that displays the user's profile and provides sign-out functionality (`src/components/auth/user-button.tsx`)

### 5. Authentication Utilities

- **useAuthContext**: A hook to access authentication state (`src/lib/auth/AuthProvider.tsx`)
- **useAuthToken**: A hook to get the authentication token (`src/lib/auth/utils.ts`)
- **createAuthorizedRequest**: A function to create authenticated fetch requests (`src/lib/auth/utils.ts`)

### 6. API Integration

The `useApi` hook provides methods for making authenticated API requests:

```tsx
const api = useApi();

// Examples
const data = await api.get("/endpoint");
const response = await api.post("/endpoint", { data });
```

## Environment Variables

The following environment variables are required:

```
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
```

You can obtain this key from your Clerk Dashboard after setting up your application.

## Middleware (src/middleware.ts)

The middleware file provides functionality for protecting API routes:

```tsx
export const withAuth = (handler: Function) => {
  return async (req: Request, ...rest: any[]) => {
    // Authentication logic
    // ...

    return handler(req, ...rest);
  };
};
```

## Authentication Flow

1. User visits the application
2. If not authenticated, they are redirected to the sign-in page
3. After successful sign-in, Clerk provides an authentication token
4. The token is stored and used for all API requests
5. Protected routes check authentication status before rendering

## Sign-In and Sign-Up Pages

The application uses Clerk's built-in components for sign-in and sign-up:

```tsx
<Route
  path="/sign-in/*"
  element={
    <SignedOut>
      <SignIn
        routing="path"
        path="/sign-in"
        signUpUrl="/sign-up"
        afterSignInUrl="/clinician/dashboard"
        redirectUrl="/clinician/dashboard"
      />
    </SignedOut>
  }
/>
```

## Integration with MainLayout

The UserButton component is integrated into the MainLayout header:

```tsx
<header className="bg-gray-800 text-white p-4 shadow-md sticky top-0 z-10">
  <div className="container mx-auto flex justify-between items-center">
    <h1 className="text-xl font-semibold">Clinician Portal</h1>
    <div className="flex items-center space-x-4">
      {/* User profile button from Clerk */}
      <UserButton />
    </div>
  </div>
</header>
```

## Using Authentication in Components

To access authentication information in any component:

```tsx
import { useAuthContext } from "../lib/auth/AuthProvider";

const MyComponent = () => {
  const { isAuthenticated, userId } = useAuthContext();

  if (!isAuthenticated) {
    return <div>Please sign in</div>;
  }

  return <div>Welcome, User {userId}</div>;
};
```

## Making Authenticated API Calls

```tsx
import { useApi } from "../lib/api";

const DataComponent = () => {
  const api = useApi();
  const [data, setData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await api.get("/user/profile");
        setData(result);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    };

    fetchData();
  }, []);

  return <div>{/* Render data */}</div>;
};
```

## Setup Instructions

1. Sign up for a Clerk account at https://clerk.com/
2. Create a new application in the Clerk Dashboard
3. Configure authentication methods (email, social logins, etc.)
4. Get your publishable key from the Clerk Dashboard
5. Add the key to your .env file: `VITE_CLERK_PUBLISHABLE_KEY=your_key`
6. Install the Clerk package: `npm install @clerk/clerk-react`
7. Set up the ClerkProvider in your application
8. Implement protected routes and authentication logic

## Recommended Practices

1. Always use the `useAuthContext` hook for authentication checks
2. Use the `useApi` hook for API calls to ensure authentication headers are included
3. Implement loading states for authentication checks
4. Use the `<ProtectedRoute>` component for routes that require authentication
5. Keep sensitive operations server-side and use authentication to protect them

## Troubleshooting

Common issues:

1. **Missing Publishable Key**: Make sure your .env file includes the VITE_CLERK_PUBLISHABLE_KEY
2. **Authentication Redirects**: Check that your routes and redirects are configured correctly
3. **Token Issues**: Verify that the token is being stored and retrieved correctly
4. **CORS Issues**: Ensure your backend API allows requests from your frontend domain
