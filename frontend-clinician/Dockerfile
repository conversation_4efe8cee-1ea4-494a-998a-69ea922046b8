# Use an official Node runtime as a parent image
FROM node:slim

# Set the working directory in the container
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json ./

# Set environment variables to properly handle platform-specific modules
ENV ROLLUP_SKIP_NODEJS=true
ENV TAILWIND_SKIP_PLATFORM_CHECK=true
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Clean out any potentially cached modules and install with rebuilding
RUN rm -rf node_modules && yarn cache clean && yarn install

# Copy the rest of the application code
COPY . .

# Make port 3000 available
EXPOSE 3000

# Define command to run app
CMD ["yarn", "run", "dev", "--", "--host", "--port", "3000"]