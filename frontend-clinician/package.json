{"name": "frontend-clinician", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@clerk/clerk-react": "^5.25.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.0.17", "@tanstack/react-table": "^8.21.2", "@types/react-calendar": "^3.9.0", "@types/recharts": "^1.8.29", "add": "^2.0.6", "axios": "^1.8.4", "badge": "^1.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.486.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-calendar": "^5.1.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-router-dom": "^7.4.1", "recharts": "^2.15.1", "shadcn": "^2.4.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.0.17", "ts-jest": "^29.3.4", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}