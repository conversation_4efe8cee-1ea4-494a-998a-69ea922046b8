#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Weight History List Styles for PatientDetailPage */
/* DEBUG: Force visibility for weight entry list */
.weight-history-list {
  margin-bottom: 1rem;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  list-style: disc !important;
  border: 2px solid red !important;
}
.weight-history-item {
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  color: #111 !important;
  background: #ffd !important;
}
.weight-history-item:last-child {
  border-bottom: none;
}
