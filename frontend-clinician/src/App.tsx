import { Routes, Route, Navigate } from "react-router-dom";
import { SignUp, SignedIn, SignedOut } from "@clerk/clerk-react";
import { ThemeProvider } from "./components/theme/ThemeProvider";

// Page Imports
import ClinicianDashboardPageAI from "./pages/ClinicianDashboardPageAI";
import InvitePatientPage from "./pages/InvitePatientPage";
import PatientListPage from "./pages/PatientListPage";
import PatientDetailPage from "./pages/PatientDetailPage";
import MedicationTriagePage from "./pages/MedicationTriagePage";
import SideEffectsPage from "./pages/SideEffectsPage";
import AppointmentsPage from "./pages/AppointmentsPage";
import AppointmentDetailPage from "./pages/AppointmentDetailPage";
import MedicationRequestsPage from "./pages/MedicationRequestsPage";
import MedicationRequestDetailPage from "./pages/MedicationRequestDetailPage";
import ProfilePage from "./pages/ProfilePage";
import ChatPage from "./pages/ChatPage";
import EducationMaterialsPage from "./pages/EducationMaterialsPage";
import AlertsPage from "./pages/AlertsPage";
import ClinicalNotesPage from "./pages/ClinicalNotesPage";
import PopulationHealthDashboard from "./pages/PopulationHealthDashboard";
import LoginPage from "./pages/LoginPage";
// Layout and Auth Components
import MainLayout from "./components/layout/MainLayout";
import ProtectedRoute from "./components/auth/ProtectedRoute";

function App() {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <Routes>
      {/* Public Routes - Using Clerk's built-in components */}
      <Route
        path="/login/*"
        element={
          <>
            <SignedIn>
              <Navigate to="/clinician/dashboard" replace />
            </SignedIn>
            <SignedOut>
              <LoginPage />
            </SignedOut>
          </>
        }
      />
      <Route
        path="/accept-invitation/*"
        element={
          <SignUp
            routing="path"
            path="/accept-invitation"
            redirectUrl="/clinician/dashboard"
          />
        }
      />

      {/* Protected Routes */}
      <Route path="/clinician" element={<ProtectedRoute />}>
        <Route element={<MainLayout />}>
          <Route index element={<Navigate to="dashboard" />} />
          <Route path="dashboard" element={<ClinicianDashboardPageAI />} />
          <Route path="patients" element={<PatientListPage />} />
          <Route path="patients/:patientId" element={<PatientDetailPage />} />
          <Route path="invite-patient" element={<InvitePatientPage />} />
          <Route
            path="medication-requests"
            element={<MedicationRequestsPage />}
          />
          <Route
            path="medication-requests/:requestId"
            element={<MedicationRequestDetailPage />}
          />
          <Route path="medication-triage" element={<MedicationTriagePage />} />
          <Route path="side-effects" element={<SideEffectsPage />} />
          <Route path="education" element={<EducationMaterialsPage />} />
          <Route path="appointments" element={<AppointmentsPage />} />
          <Route path="alerts" element={<AlertsPage />} />
          <Route
            path="appointments/:appointmentId"
            element={<AppointmentDetailPage />}
          />
          <Route path="profile" element={<ProfilePage />} />
          <Route path="chat" element={<ChatPage />} />
          <Route path="clinical-notes" element={<ClinicalNotesPage />} />
          <Route path="population-health" element={<PopulationHealthDashboard />} />
        </Route>
      </Route>

      {/* Public route catch-all */}
      <Route
        path="*"
        element={
          <>
            <SignedIn>
              <Navigate to="/clinician/dashboard" replace />
            </SignedIn>
            <SignedOut>
              <Navigate to="/login" replace />
            </SignedOut>
          </>
        }
      />
    </Routes>
    </ThemeProvider>
  );
}

export default App;
