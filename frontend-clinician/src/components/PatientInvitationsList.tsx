import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { format } from "date-fns";
import { Loader2, Trash2 } from "lucide-react";
import { AxiosError } from "axios";

// Define interface for Clerk Invitation data (Patient specific context, but structure is generic)
// Based on backend schema PaginatedClerkInvitationListResponse and clerk_models.Invitation
interface ClerkInvitation {
  id: string;
  email_address: string;
  status: "pending" | "accepted" | "revoked";
  created_at: number; // Clerk SDK returns timestamp in milliseconds
  public_metadata?: {
    inviter_user_id?: string;
    invitee_role?: string;
    // Add other potential patient-specific metadata if needed
  };
  // Add other relevant fields from clerk_models.Invitation if needed
}

interface PatientInvitationsListProps {
  onRefresh?: () => (fetchFunc: () => Promise<void>) => void;
}

const PatientInvitationsList: React.FC<PatientInvitationsListProps> = ({
  onRefresh,
}) => {
  const [invitations, setInvitations] = useState<ClerkInvitation[]>([]);
  const [isLoadingInvitations, setIsLoadingInvitations] =
    useState<boolean>(true);
  const [fetchInvitationsError, setFetchInvitationsError] = useState<
    string | null
  >(null);
  const [deletingInvitationId, setDeletingInvitationId] = useState<
    string | null
  >(null);

  // Fetch patient invitations sent by this clinician
  const fetchInvitations = useCallback(async () => {
    setIsLoadingInvitations(true);
    setFetchInvitationsError(null);

    try {
      // Use the new clinician endpoint
      const response = await apiClient.get(
        "/clinicians/invitations?status=pending",
      ); // Fetch pending invites by default

      // Backend returns { invitations: ClerkInvitation[], total_count: number }
      if (response.data && Array.isArray(response.data.invitations)) {
        // Sort invitations by creation date, newest first
        const sortedInvitations = response.data.invitations.sort(
          (a: ClerkInvitation, b: ClerkInvitation) =>
            b.created_at - a.created_at,
        ); // Sort by timestamp
        setInvitations(sortedInvitations);
        // Call the onRefresh callback if provided
        onRefresh?.();
      } else {
        console.error(
          "Unexpected response format for invitations:",
          response.data,
        );
        setInvitations([]);
        setFetchInvitationsError(
          "Failed to fetch invitations due to unexpected response format.",
        );
      }
    } catch (error: unknown) {
      console.error("Error fetching patient invitations:", error);
      let errorMsg = "Failed to fetch patient invitations.";
      if (error instanceof AxiosError) {
        errorMsg = error.response?.data?.detail || error.message || errorMsg;
      } else if (error instanceof Error) {
        errorMsg = error.message;
      }
      setFetchInvitationsError(errorMsg);
      toast.error(errorMsg);
      setInvitations([]);
    } finally {
      setIsLoadingInvitations(false);
    }
  }, [onRefresh]);

  useEffect(() => {
    fetchInvitations();
  }, [fetchInvitations]);

  // Pass the fetchInvitations function to parent on mount
  useEffect(() => {
    if (onRefresh) {
      const setRefreshFunc = onRefresh();
      setRefreshFunc(fetchInvitations);
    }
  }, [onRefresh, fetchInvitations]);

  // Handle invitation deletion (revocation)
  const handleDelete = async (invitationId: string) => {
    setDeletingInvitationId(invitationId);
    try {
      // Use the new clinician endpoint for deletion
      await apiClient.delete(`/clinicians/invitations/${invitationId}`);
      toast.success("Patient invitation revoked successfully!");
      // Refresh list by filtering locally
      setInvitations((prev) => prev.filter((inv) => inv.id !== invitationId));
      // Or refetch from server:
      // fetchInvitations();
    } catch (error: unknown) {
      console.error("Error revoking patient invitation:", error);
      let errorMsg = "Failed to revoke patient invitation.";
      if (error instanceof AxiosError) {
        errorMsg = error.response?.data?.detail || error.message || errorMsg;
      } else if (error instanceof Error) {
        errorMsg = error.message;
      }
      toast.error(errorMsg);
    } finally {
      setDeletingInvitationId(null);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Patient Invitations</CardTitle>
        <CardDescription>List of invitations sent to patients.</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingInvitations && (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2">Loading invitations...</span>
          </div>
        )}
        {fetchInvitationsError && !isLoadingInvitations && (
          <Alert variant="destructive">
            <AlertTitle>Error Loading Invitations</AlertTitle>
            <AlertDescription>
              {typeof fetchInvitationsError === "string"
                ? fetchInvitationsError
                : JSON.stringify(fetchInvitationsError)}
            </AlertDescription>
          </Alert>
        )}
        {!isLoadingInvitations &&
          !fetchInvitationsError &&
          invitations.length === 0 && (
            <p className="text-center text-muted-foreground py-4">
              No pending patient invitations found.
            </p>
          )}
        {!isLoadingInvitations &&
          !fetchInvitationsError &&
          invitations.length > 0 && (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {" "}
              {/* Responsive grid */}
              {invitations.map((invitation) => (
                <Card key={invitation.id}>
                  <CardHeader>
                    <CardTitle className="text-lg break-all">
                      {invitation.email_address}
                    </CardTitle>
                    <CardDescription>
                      Sent: {format(new Date(invitation.created_at), "PPpp")}{" "}
                      {/* Format timestamp */}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Status
                      </p>
                      <p className="capitalize">{invitation.status}</p>
                    </div>
                    {/* Add any other relevant patient invite details here if available in metadata */}
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(invitation.id)}
                      disabled={deletingInvitationId === invitation.id}
                      aria-label={`Revoke invitation for ${invitation.email_address}`}
                    >
                      {deletingInvitationId === invitation.id ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="mr-2 h-4 w-4" />
                      )}
                      Revoke
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
      </CardContent>
    </Card>
  );
};

export default PatientInvitationsList;
