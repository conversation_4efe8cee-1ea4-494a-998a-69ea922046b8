import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { toast } from 'sonner';
import apiClient from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { formatDistanceToNow } from 'date-fns';

interface PatientAlert {
  id: string;
  patient_id: string;
  alert_type: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  status: "new" | "acknowledged" | "resolved" | "dismissed";
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  resolved_by_clinician_id?: string;
  metadata?: any;
}

interface AlertDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  alert: PatientAlert | null;
  onUpdate: (updatedAlert: PatientAlert) => void;
}

export const AlertDetailModal: React.FC<AlertDetailModalProps> = ({ 
  isOpen, 
  onClose, 
  alert,
  onUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedDescription, setEditedDescription] = useState('');
  const [editedSeverity, setEditedSeverity] = useState<PatientAlert['severity']>('medium');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (alert) {
      setEditedDescription(alert.description);
      setEditedSeverity(alert.severity);
    }
  }, [alert]);

  if (!isOpen || !alert) return null;

  const handleStatusUpdate = async (newStatus: PatientAlert['status']) => {
    setIsLoading(true);
    try {
      const response = await apiClient.patch(`/patient-alerts/${alert.id}`, {
        status: newStatus
      });
      onUpdate(response.data);
      toast.success(`Alert ${newStatus}`);
      
      // Dispatch event to update header bar alert count
      window.dispatchEvent(new CustomEvent('patientAlertUpdated'));
      
      if (newStatus === 'resolved' || newStatus === 'dismissed') {
        onClose();
      }
    } catch (error: any) {
      toast.error(`Failed to update alert status: ${error.response?.data?.detail || error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveEdit = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.patch(`/patient-alerts/${alert.id}`, {
        description: editedDescription,
        severity: editedSeverity
      });
      onUpdate(response.data);
      setIsEditing(false);
      toast.success('Alert updated successfully');
    } catch (error: any) {
      toast.error(`Failed to update alert: ${error.response?.data?.detail || error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      default: return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'destructive';
      case 'acknowledged': return 'secondary';
      case 'resolved': return 'outline';
      case 'dismissed': return 'outline';
      default: return 'outline';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Alert Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Alert Type and Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="text-sm">
                {alert.alert_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Badge>
              <Badge variant={getSeverityColor(alert.severity)}>
                {alert.severity.toUpperCase()}
              </Badge>
              <Badge variant={getStatusColor(alert.status)}>
                {alert.status.toUpperCase()}
              </Badge>
            </div>
            {!isEditing && alert.status !== 'resolved' && alert.status !== 'dismissed' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                Edit
              </Button>
            )}
          </div>

          {/* Timestamps */}
          <div className="text-sm text-gray-600 space-y-1">
            <p>Created: {new Date(alert.created_at).toLocaleString()} ({formatDistanceToNow(new Date(alert.created_at), { addSuffix: true })})</p>
            <p>Last Updated: {new Date(alert.updated_at).toLocaleString()}</p>
            {alert.resolved_at && (
              <p>Resolved: {new Date(alert.resolved_at).toLocaleString()}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label>Description</Label>
            {isEditing ? (
              <Textarea
                value={editedDescription}
                onChange={(e) => setEditedDescription(e.target.value)}
                rows={4}
                className="w-full"
              />
            ) : (
              <div className="p-3 bg-gray-50 rounded-md">
                <p className="whitespace-pre-wrap">{alert.description}</p>
              </div>
            )}
          </div>

          {/* Severity (editable) */}
          {isEditing && (
            <div className="space-y-2">
              <Label>Severity</Label>
              <Select
                value={editedSeverity}
                onValueChange={(value: PatientAlert['severity']) => setEditedSeverity(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Additional Metadata if available */}
          {alert.metadata && Object.keys(alert.metadata).length > 0 && (
            <div className="space-y-2">
              <Label>Additional Information</Label>
              <div className="p-3 bg-gray-50 rounded-md">
                <pre className="text-sm">{JSON.stringify(alert.metadata, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          {isEditing ? (
            <div className="flex space-x-2 ml-auto">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setEditedDescription(alert.description);
                  setEditedSeverity(alert.severity);
                }}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={isLoading}
              >
                Save Changes
              </Button>
            </div>
          ) : (
            <>
              <div className="flex space-x-2">
                {alert.status === 'new' && (
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate('acknowledged')}
                    disabled={isLoading}
                  >
                    Acknowledge
                  </Button>
                )}
                {(alert.status === 'new' || alert.status === 'acknowledged') && (
                  <>
                    <Button
                      variant="default"
                      onClick={() => handleStatusUpdate('resolved')}
                      disabled={isLoading}
                    >
                      Resolve
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleStatusUpdate('dismissed')}
                      disabled={isLoading}
                    >
                      Dismiss
                    </Button>
                  </>
                )}
              </div>
              <Button variant="ghost" onClick={onClose}>
                Close
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};