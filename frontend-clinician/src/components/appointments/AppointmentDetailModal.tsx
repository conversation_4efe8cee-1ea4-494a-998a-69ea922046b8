import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, <PERSON>cil, Trash2, X } from "lucide-react";
import { format } from "date-fns";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface AppointmentDetailModalProps {
  appointment: {
    id: string;
    patient: {
      id: string;
      first_name: string;
      last_name: string;
    };
    clinician?: {
      id: string;
      first_name: string;
      last_name: string;
    };
    appointment_datetime: string;
    duration_minutes: number;
    appointment_type: string;
    status: "scheduled" | "completed" | "cancelled";
    reason?: string;
    patient_notes?: string;
    clinician_notes?: string;
  } | null;
  isOpen: boolean;
  isLoading?: boolean;
  error?: string | null;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isSubmitting?: boolean;
}

const getStatusBadgeVariant = (status: string | undefined) => {
  switch (status) {
    case "scheduled":
      return "default";
    case "completed":
      return "secondary";
    case "cancelled":
      return "destructive";
    default:
      return "secondary";
  }
};

export const AppointmentDetailModal: React.FC<AppointmentDetailModalProps> = ({
  appointment,
  isOpen,
  isLoading = false,
  error = null,
  onClose,
  onEdit,
  onDelete,
  isSubmitting = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full relative">
        <button
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
        <Card className="shadow-none border-none">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle>Appointment Details</CardTitle>
              <CardDescription>
                {appointment ? `Appointment ID: ${appointment.id}` : ""}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {!isLoading && !error && appointment && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onEdit}
                    disabled={isSubmitting}
                  >
                    <Pencil className="h-4 w-4 mr-2" /> Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={onDelete}
                    disabled={isSubmitting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" /> Delete
                  </Button>
                </>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : appointment ? (
              <div className="space-y-4">
                {/* Basic Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-semibold">Patient:</span>{" "}
                    {appointment.patient.first_name}{" "}
                    {appointment.patient.last_name}
                  </div>
                  <div>
                    <span className="font-semibold">Date & Time:</span>{" "}
                    {format(new Date(appointment.appointment_datetime), "PPp")}
                  </div>
                  <div>
                    <span className="font-semibold">Duration:</span>{" "}
                    {appointment.duration_minutes} min
                  </div>
                  <div>
                    <span className="font-semibold">Type:</span>{" "}
                    {appointment.appointment_type}
                  </div>
                  <div>
                    <span className="font-semibold">Status:</span>{" "}
                    <Badge variant={getStatusBadgeVariant(appointment.status)}>
                      {appointment.status}
                    </Badge>
                  </div>
                  {/* Uncomment if you want to show clinician */}
                  {/* {appointment.clinician && (
                    <div>
                      <span className="font-semibold">Clinician:</span>{" "}
                      {appointment.clinician.first_name} {appointment.clinician.last_name}
                    </div>
                  )} */}
                </div>
                {/* Notes/Reason Sections */}
                {appointment.reason && (
                  <div>
                    <span className="font-semibold">Reason:</span>{" "}
                    {appointment.reason}
                  </div>
                )}
                {appointment.patient_notes && (
                  <div>
                    <span className="font-semibold">Patient Notes:</span>{" "}
                    {appointment.patient_notes}
                  </div>
                )}
                {appointment.clinician_notes && (
                  <div>
                    <span className="font-semibold">Clinician Notes:</span>{" "}
                    {appointment.clinician_notes}
                  </div>
                )}
              </div>
            ) : (
              <p>No appointment details found.</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
