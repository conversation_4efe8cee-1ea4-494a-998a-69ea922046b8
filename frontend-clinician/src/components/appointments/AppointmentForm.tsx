import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { Label } from "../../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { toast } from "sonner";
import apiClient from "../../lib/apiClient";
import { formatForDateTimeInput, convertToUTC } from "@pulsetrack/shared-frontend";

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
}

export interface AppointmentFormData {
  patient_id: string;
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  reason?: string;
  patient_notes?: string;
}

interface AppointmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AppointmentFormData) => void;
  isSubmitting?: boolean;
  appointment?: {
    id: string;
    patient_id: string;
    appointment_datetime: string;
    duration_minutes: number;
    appointment_type: string;
    reason?: string;
    patient_notes?: string;
  };
}

// Constants from backend
const APPOINTMENT_DURATIONS = [15, 30, 45, 60];
const APPOINTMENT_TYPES = ["Initial", "Follow-up", "Procedure", "Consultation"];

export const AppointmentForm: React.FC<AppointmentFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false,
  appointment,
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(false);


  const [formData, setFormData] = useState<AppointmentFormData>({
    patient_id: "", // Initialize empty, will be set in useEffect
    appointment_datetime: "", // Initialize empty
    duration_minutes: 30, // Default duration
    appointment_type: "Follow-up", // Default type
    reason: "",
    patient_notes: "",
  });

  useEffect(() => {
    // Pre-fill form data if appointment data is provided (for editing)
    if (appointment) {
      setFormData({
        patient_id: appointment.patient_id,
        appointment_datetime: formatForDateTimeInput(
          appointment.appointment_datetime,
        ),
        duration_minutes: appointment.duration_minutes,
        appointment_type: appointment.appointment_type,
        reason: appointment.reason || "",
        patient_notes: appointment.patient_notes || "",
      });
    } else {
      // Reset form for new appointment
      setFormData({
        patient_id: "",
        appointment_datetime: "",
        duration_minutes: 30,
        appointment_type: "Follow-up",
        reason: "",
        patient_notes: "",
      });
    }
  }, [appointment]); // Rerun when appointment prop changes

  useEffect(() => {
    // Fetch patients only when the dialog is open and it's for a new appointment
    // or if patients haven't been loaded yet (relevant if modal stays mounted)
    if (isOpen && (!appointment || patients.length === 0)) {
      const fetchPatients = async () => {
        setIsLoading(true);
        try {
          // This endpoint already filters by the current clinician
          const response = await apiClient.get("/clinicians/patients", {
            params: {
              limit: 100, // Adjust limit as needed
            },
          });
          setPatients(response.data.items);
        } catch (error) {
          console.error("Failed to fetch patients:", error);
          toast.error("Failed to load patient list", {
            description:
              "Please try again or contact support if the problem persists.",
          });
        } finally {
          setIsLoading(false);
        }
      };
      fetchPatients();
    }
    // Clear form when closing if it wasn't an edit form that just submitted
    if (!isOpen && !isSubmitting && !appointment) {
      // Optional: Reset form state on close for 'new' mode
      // Consider if parent component handles state clearing better
    }
  }, [isOpen, appointment, isSubmitting, patients.length]); // Add patients.length dependency here

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {appointment ? "Edit Appointment" : "New Appointment"}
          </DialogTitle>
          <DialogDescription>
            {appointment
              ? "Update the appointment details below."
              : "Create a new appointment by filling out the form below."}
          </DialogDescription>
        </DialogHeader>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="space-y-2">
            <Label htmlFor="patient">Patient</Label>
            <Select
              value={formData.patient_id}
              onValueChange={(value) =>
                setFormData({ ...formData, patient_id: value })
              }
              disabled={isLoading || isSubmitting || !!appointment}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id}>
                    {`${patient.first_name} ${patient.last_name}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="datetime">Date and Time</Label>
            <Input
              id="datetime"
              type="datetime-local"
              value={formData.appointment_datetime}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  appointment_datetime: e.target.value,
                })
              }
              required
              disabled={isSubmitting}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="duration">Duration (minutes)</Label>
            <Select
              value={formData.duration_minutes.toString()}
              onValueChange={(value) =>
                setFormData({ ...formData, duration_minutes: parseInt(value) })
              }
              disabled={isSubmitting}
            >
              <SelectTrigger id="duration">
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                {APPOINTMENT_DURATIONS.map((duration) => (
                  <SelectItem key={duration} value={duration.toString()}>
                    {duration} minutes
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Appointment Type</Label>
            <Select
              value={formData.appointment_type}
              onValueChange={(value) =>
                setFormData({ ...formData, appointment_type: value })
              }
              disabled={isSubmitting}
            >
              <SelectTrigger id="type">
                <SelectValue placeholder="Select appointment type" />
              </SelectTrigger>
              <SelectContent>
                {APPOINTMENT_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Textarea
              id="reason"
              placeholder="Reason for the appointment"
              value={formData.reason || ""}
              onChange={(e) =>
                setFormData({ ...formData, reason: e.target.value })
              }
              disabled={isSubmitting}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="patient_notes">Patient Notes</Label>
            <Textarea
              id="patient_notes"
              placeholder="Add any relevant notes about the patient"
              value={formData.patient_notes || ""}
              onChange={(e) =>
                setFormData({ ...formData, patient_notes: e.target.value })
              }
              disabled={isSubmitting}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || isSubmitting}>
              {isSubmitting ? "Saving..." : appointment ? "Update" : "Create"}{" "}
              Appointment
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
