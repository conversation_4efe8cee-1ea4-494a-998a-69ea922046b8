import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>ertCircle, Loader2, X, Check, XCircle } from "lucide-react";
import { format } from "date-fns";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface AppointmentRequest {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  preferred_datetime: string;
  reason: string;
  status: "pending" | "approved" | "rejected" | "cancelled";
  created_at: string;
  updated_at: string;
  patient_id: string;
  reviewed_by_id?: string;
  reviewed_by?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  review_notes?: string;
  clinician_preference?: string;
}

interface AppointmentRequestModalProps {
  appointmentRequest: AppointmentRequest | null;
  isOpen: boolean;
  isLoading?: boolean;
  error?: string | null;
  onClose: () => void;
  onApprove: (requestId: string, notes?: string) => Promise<void>;
  onReject: (requestId: string, notes: string) => Promise<void>;
  isSubmitting?: boolean;
}

const getStatusBadgeVariant = (status: string | undefined) => {
  switch (status) {
    case "pending":
      return "outline";
    case "approved":
      return "default";
    case "rejected":
      return "destructive";
    case "cancelled":
      return "secondary";
    default:
      return "secondary";
  }
};

export const AppointmentRequestModal: React.FC<AppointmentRequestModalProps> = ({
  appointmentRequest,
  isOpen,
  isLoading = false,
  error = null,
  onClose,
  onApprove,
  onReject,
  isSubmitting = false,
}) => {
  const [reviewNotes, setReviewNotes] = useState<string>("");
  const [action, setAction] = useState<"approve" | "reject" | null>(null);

  const handleClose = () => {
    setReviewNotes("");
    setAction(null);
    onClose();
  };

  const handleApprove = async () => {
    if (!appointmentRequest) return;
    setAction("approve");
    try {
      await onApprove(appointmentRequest.id, reviewNotes);
      handleClose();
    } catch (error) {
      setAction(null);
    }
  };

  const handleReject = async () => {
    if (!appointmentRequest) return;
    
    // Set action first to show validation UI
    setAction("reject");
    
    // Check if notes are provided
    if (!reviewNotes.trim()) {
      return; // Don't proceed without notes, but keep action state for UI feedback
    }
    
    try {
      await onReject(appointmentRequest.id, reviewNotes);
      handleClose();
    } catch (error) {
      setAction(null);
    }
  };

  const isActionInProgress = (actionType: "approve" | "reject") => {
    return action === actionType && isSubmitting;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto relative mx-4">
        <button
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 z-10"
          onClick={handleClose}
          aria-label="Close"
          disabled={isSubmitting}
        >
          <X className="h-5 w-5" />
        </button>
        <Card className="shadow-none border-none">
          <CardHeader className="pb-4">
            <CardTitle>Appointment Request Review</CardTitle>
            <CardDescription>
              {appointmentRequest ? `Request ID: ${appointmentRequest.id}` : ""}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : appointmentRequest ? (
              <div className="space-y-6">
                {/* Basic Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-semibold">Patient:</span>{" "}
                    {appointmentRequest.patient.first_name}{" "}
                    {appointmentRequest.patient.last_name}
                  </div>
                  <div>
                    <span className="font-semibold">Preferred Date & Time:</span>{" "}
                    {format(new Date(appointmentRequest.preferred_datetime), "PPp")}
                  </div>
                  <div>
                    <span className="font-semibold">Status:</span>{" "}
                    <Badge variant={getStatusBadgeVariant(appointmentRequest.status)}>
                      {appointmentRequest.status}
                    </Badge>
                  </div>
                  <div>
                    <span className="font-semibold">Requested On:</span>{" "}
                    {format(new Date(appointmentRequest.created_at), "PPp")}
                  </div>
                </div>

                {/* Reason Section */}
                <div>
                  <span className="font-semibold">Reason for Appointment:</span>
                  <p className="mt-1 text-sm text-gray-700 bg-gray-50 p-3 rounded">
                    {appointmentRequest.reason || "No reason provided"}
                  </p>
                </div>

                {/* Clinician Preference */}
                {appointmentRequest.clinician_preference && (
                  <div>
                    <span className="font-semibold">Preferred Clinician:</span>{" "}
                    {appointmentRequest.clinician_preference}
                  </div>
                )}

                {/* Previous Review Info */}
                {appointmentRequest.reviewed_by && (
                  <div className="space-y-2">
                    <div>
                      <span className="font-semibold">Reviewed By:</span>{" "}
                      {appointmentRequest.reviewed_by.first_name} {appointmentRequest.reviewed_by.last_name}
                    </div>
                    {appointmentRequest.reviewed_at && (
                      <div>
                        <span className="font-semibold">Reviewed At:</span>{" "}
                        {format(new Date(appointmentRequest.reviewed_at), "PPp")}
                      </div>
                    )}
                    {appointmentRequest.review_notes && (
                      <div>
                        <span className="font-semibold">Previous Review Notes:</span>
                        <p className="mt-1 text-sm text-gray-700 bg-gray-50 p-3 rounded">
                          {appointmentRequest.review_notes}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Review Actions (only show for pending requests) */}
                {appointmentRequest.status === "pending" && (
                  <div className="space-y-4 border-t pt-4">
                    <div>
                      <label htmlFor="reviewNotes" className="block text-sm font-medium text-gray-700 mb-2">
                        Review Notes {action === "reject" && <span className="text-red-500">*</span>}
                      </label>
                      <Textarea
                        id="reviewNotes"
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        placeholder={
                          action === "reject" 
                            ? "Please provide a reason for rejection..." 
                            : "Optional notes for this review..."
                        }
                        disabled={isSubmitting}
                        className="min-h-[80px]"
                      />
                      {action === "reject" && !reviewNotes.trim() && (
                        <p className="text-sm text-red-500 mt-1">
                          Review notes are required when rejecting a request
                        </p>
                      )}
                    </div>

                    <div className="flex gap-3 justify-end">
                      <Button
                        variant="destructive"
                        onClick={handleReject}
                        disabled={isSubmitting}
                      >
                        {isActionInProgress("reject") ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <XCircle className="h-4 w-4 mr-2" />
                        )}
                        Reject
                      </Button>
                      <Button
                        onClick={handleApprove}
                        disabled={isSubmitting}
                      >
                        {isActionInProgress("approve") ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Check className="h-4 w-4 mr-2" />
                        )}
                        Approve
                      </Button>
                    </div>
                  </div>
                )}

                {/* Status message for non-pending requests */}
                {appointmentRequest.status !== "pending" && (
                  <div className="border-t pt-4">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Request Already Reviewed</AlertTitle>
                      <AlertDescription>
                        This appointment request has already been {appointmentRequest.status}.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>
            ) : (
              <p>No appointment request details found.</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};