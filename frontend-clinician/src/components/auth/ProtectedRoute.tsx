import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@pulsetrack/shared-frontend"; // Assuming useAuth is exported from the shared package
import { AuthLoadingIndicator } from "@pulsetrack/shared-frontend"; // Assuming a shared loading indicator

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, isAuthLoading } = useAuth();

  if (isAuthLoading) {
    // Render loading indicator while checking auth status
    return <AuthLoadingIndicator />;
  }

  if (!isAuthenticated) {
    // Redirect to the clinician login page if not authenticated
    return <Navigate to="/login" replace />;
  }

  // Render the child route component if authenticated
  return <Outlet />;
};

export default ProtectedRoute;
