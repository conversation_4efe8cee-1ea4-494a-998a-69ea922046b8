import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, Activity } from 'lucide-react';

interface ActionResult {
  action_type: string;
  success: boolean;
  message?: string;
}

interface ChainedActionData {
  chain_id: string;
  execution_time_ms?: number;
  results: ActionResult[];
}

interface ChainedActionResponseProps {
  data: ChainedActionData;
  message: string;
  success: boolean;
}

export const ChainedActionResponse: React.FC<ChainedActionResponseProps> = ({
  data,
  message,
  success
}) => {
  const successCount = data.results.filter(r => r.success).length;
  const totalCount = data.results.length;

  const getActionIcon = (action: ActionResult) => {
    if (action.success) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-600" />;
    }
  };

  const getActionLabel = (actionType: string) => {
    const labels: Record<string, string> = {
      appointment_create: 'Appointment Scheduled',
      appointment_request: 'Appointment Requested',
      side_effect_report: 'Side Effect Reported',
      weight_log_create: 'Weight Logged',
      notification_create: 'Reminder Set',
      medication_request: 'Medication Requested'
    };
    return labels[actionType] || actionType.replace(/_/g, ' ');
  };

  return (
    <Card className="p-4 bg-blue-50 border-blue-200">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h4 className="font-medium text-gray-900">Multiple Actions Completed</h4>
        </div>
        <Badge variant={success ? "default" : "destructive"}>
          {successCount}/{totalCount} Successful
        </Badge>
      </div>

      <div className="space-y-2">
        {data.results.map((result, index) => (
          <div
            key={index}
            className="flex items-start gap-2 p-2 rounded-lg bg-white border border-gray-100"
          >
            {getActionIcon(result)}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                {getActionLabel(result.action_type)}
              </p>
              {result.message && (
                <p className="text-xs text-gray-600 mt-0.5">{result.message}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      {data.execution_time_ms && (
        <div className="flex items-center gap-1 mt-3 text-xs text-gray-500">
          <Clock className="w-3 h-3" />
          <span>Completed in {data.execution_time_ms}ms</span>
        </div>
      )}
    </Card>
  );
};