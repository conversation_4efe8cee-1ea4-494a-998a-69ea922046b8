import React from "react";
import { Bo<PERSON>, MessageSquare, User } from "lucide-react";
import { ConversationType } from "./NewConversationDialog";

interface ConversationHeaderProps {
  isClinicianView: boolean;
  conversationType?: ConversationType;
  patientName?: string;
  messagesExist: boolean;
}

const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  isClinicianView,
  conversationType,
  patientName,
  messagesExist,
}) => {
  // For patient app (not clinician view)
  if (!isClinicianView) {
    return (
      <div className="flex items-center">
        <MessageSquare className="mr-2 h-5 w-5" />
        <span>Chat with AI Assistant</span>
      </div>
    );
  }

  // For clinician view
  if (conversationType === "patient" && patientName) {
    return (
      <div className="flex items-center">
        <User className="mr-2 h-5 w-5 text-green-600" />
        <span>Chat with AI about {patientName}</span>
      </div>
    );
  }

  if (
    conversationType === "general" ||
    (conversationType === undefined && messagesExist)
  ) {
    return (
      <div className="flex items-center">
        <Bot className="mr-2 h-5 w-5 text-blue-600" />
        <span>General Medical AI Chat</span>
      </div>
    );
  }

  // Default state - no conversation selected
  return (
    <div className="flex items-center">
      <MessageSquare className="mr-2 h-5 w-5" />
      <span>Select a Conversation</span>
    </div>
  );
};

export default ConversationHeader;
