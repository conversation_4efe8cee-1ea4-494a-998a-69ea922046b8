import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { DatePicker } from '@/components/ui/date-picker';
import { TimePicker } from '@/components/ui/time-picker';
import { AlertCircle, Send } from 'lucide-react';

interface Parameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  nullable?: boolean;
  enum?: string[];
  format?: string;
  placeholder?: string;
  is_missing?: boolean;
  current_value?: any;
}

interface MissingParameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  enum?: string[];
  format?: string;
  placeholder?: string;
}

interface ParameterHarvestingFormProps {
  actionType: string;
  allParameters?: Parameter[];  // New prop for all parameters
  missingParameters: MissingParameter[];
  existingParameters?: Record<string, any>;
  onSubmit: (parameters: Record<string, any>) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  userRole?: 'patient' | 'clinician';
  patientName?: string;
}

export const ParameterHarvestingForm: React.FC<ParameterHarvestingFormProps> = ({
  actionType,
  allParameters,
  missingParameters,
  existingParameters = {},
  onSubmit,
  onCancel,
  isLoading = false,
  userRole = 'patient',
  patientName
}) => {
  // Initialize form data with existing parameters and current values from allParameters
  const initialFormData = { ...existingParameters };
  if (allParameters) {
    allParameters.forEach(param => {
      if (param.current_value !== undefined && param.current_value !== null) {
        initialFormData[param.name] = param.current_value;
      }
    });
  }
  
  const [formData, setFormData] = useState<Record<string, any>>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const getActionTitle = (type: string) => {
    const titles: Record<string, string> = {
      appointment_create: 'Schedule Appointment',
      appointment_request: 'Request Appointment',
      side_effect_report_create: 'Report Side Effect',
      weight_log_create: 'Log Weight',
      medication_request_create: 'Request Medication',
      notification_create: 'Set Reminder'
    };
    return titles[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleInputChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Use allParameters if available, otherwise fall back to missingParameters
    const parametersToValidate = allParameters || missingParameters;
    
    parametersToValidate.forEach(param => {
      if (param.required && !param.nullable && !formData[param.name]) {
        newErrors[param.name] = `${param.name} is required`;
      }
      
      // Add specific validations based on format
      if (param.format === 'date-time' && formData[param.name]) {
        const date = new Date(formData[param.name]);
        if (isNaN(date.getTime())) {
          newErrors[param.name] = 'Invalid date format';
        }
      }
      
      if (param.type === 'integer' && formData[param.name]) {
        const num = parseInt(formData[param.name]);
        if (isNaN(num)) {
          newErrors[param.name] = 'Must be a number';
        }
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Merge existing parameters with new form data
      const allParameters = { ...existingParameters, ...formData };
      onSubmit(allParameters);
    }
  };

  const formatFieldValue = (name: string, value: any): string => {
    if (!value) return '';
    
    // Handle patient_id display
    if (name === 'patient_id') {
      // For patients, show "You"
      if (userRole === 'patient' && value.startsWith('user_')) {
        return 'You';
      }
      // For clinicians, show the patient name if available, otherwise show the ID
      if (userRole === 'clinician') {
        return patientName || value;
      }
    }
    
    return String(value);
  };

  const renderField = (param: Parameter | MissingParameter) => {
    const { name, type, description, enum: enumValues, format, placeholder } = param;
    const value = formData[name] || '';
    // Check if this parameter is missing (only available in Parameter type)
    const isMissing = 'is_missing' in param ? param.is_missing : false;
    
    // Use description as the label if available, otherwise format the name
    const fieldLabel = description || name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Handle enum fields as select dropdowns
    if (enumValues && enumValues.length > 0) {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium">
            {fieldLabel}
            {param.required && <span className="text-red-500 ml-1">*</span>}
            {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
          </Label>
          <Select value={value} onValueChange={(val) => handleInputChange(name, val)}>
            <SelectTrigger id={name} className={errors[name] ? 'border-red-500' : ''}>
              <SelectValue placeholder={placeholder || `Select ${name}`} />
            </SelectTrigger>
            <SelectContent>
              {enumValues.map(option => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
          {errors[name] && (
            <p className="text-xs text-red-500">{errors[name]}</p>
          )}
        </div>
      );
    }

    // Handle date-time fields
    if (format === 'date-time') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium">
            {fieldLabel}
            {param.required && <span className="text-red-500 ml-1">*</span>}
            {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
          </Label>
          <DateTimePicker
            value={value}
            onChange={(dateTime) => handleInputChange(name, dateTime)}
            placeholder={placeholder || `Select ${name.replace(/_/g, ' ')}`}
            className={errors[name] ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
          {errors[name] && (
            <p className="text-xs text-red-500">{errors[name]}</p>
          )}
        </div>
      );
    }
    
    // Handle date-only fields
    if (format === 'date' || type === 'date') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium">
            {fieldLabel}
            {param.required && <span className="text-red-500 ml-1">*</span>}
            {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
          </Label>
          <DatePicker
            value={value}
            onChange={(date) => handleInputChange(name, date)}
            placeholder={placeholder || `Select ${name.replace(/_/g, ' ')}`}
            className={errors[name] ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
          {errors[name] && (
            <p className="text-xs text-red-500">{errors[name]}</p>
          )}
        </div>
      );
    }
    
    // Handle time fields
    if (format === 'time' || type === 'time') {
      // Check if this is a datetime field (like onset_time) rather than just time of day
      const isDateTimeField = name.includes('onset') || name.includes('occurred') || 
                             name.includes('started') || name.includes('ended') ||
                             (description && description.toLowerCase().includes('when'));
      
      if (isDateTimeField) {
        // Use DateTimePicker for fields that represent a point in time
        return (
          <div key={name} className="space-y-2">
            <Label htmlFor={name} className="text-sm font-medium">
              {fieldLabel}
              {param.required && <span className="text-red-500 ml-1">*</span>}
              {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
            </Label>
            <DateTimePicker
              value={value}
              onChange={(dateTime) => handleInputChange(name, dateTime)}
              placeholder={placeholder || `Select ${name.replace(/_/g, ' ')}`}
              className={errors[name] ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {description && (
              <p className="text-xs text-gray-500">{description}</p>
            )}
            {errors[name] && (
              <p className="text-xs text-red-500">{errors[name]}</p>
            )}
          </div>
        );
      } else {
        // Use TimePicker for time-of-day fields (like preferred_time)
        return (
          <div key={name} className="space-y-2">
            <Label htmlFor={name} className="text-sm font-medium">
              {fieldLabel}
              {param.required && <span className="text-red-500 ml-1">*</span>}
              {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
            </Label>
            <TimePicker
              value={value}
              onChange={(time) => handleInputChange(name, time)}
              placeholder={placeholder || `Select ${name.replace(/_/g, ' ')}`}
              className={errors[name] ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {description && (
              <p className="text-xs text-gray-500">{description}</p>
            )}
            {errors[name] && (
              <p className="text-xs text-red-500">{errors[name]}</p>
            )}
          </div>
        );
      }
    }

    // Handle long text fields
    if (name.includes('notes') || name.includes('description') || name.includes('reason')) {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium">
            {fieldLabel}
            {param.required && <span className="text-red-500 ml-1">*</span>}
            {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
          </Label>
          <Textarea
            id={name}
            value={value}
            onChange={(e) => handleInputChange(name, e.target.value)}
            placeholder={placeholder || `Enter ${name}`}
            className={errors[name] ? 'border-red-500' : ''}
            disabled={isLoading}
            rows={3}
          />
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
          {errors[name] && (
            <p className="text-xs text-red-500">{errors[name]}</p>
          )}
        </div>
      );
    }

    // Handle number fields
    if (type === 'integer' || type === 'number') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium">
            {fieldLabel}
            {param.required && <span className="text-red-500 ml-1">*</span>}
            {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
          </Label>
          <Input
            id={name}
            type="number"
            value={value}
            onChange={(e) => handleInputChange(name, e.target.value)}
            placeholder={placeholder || `Enter ${name}`}
            className={errors[name] ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
          {errors[name] && (
            <p className="text-xs text-red-500">{errors[name]}</p>
          )}
        </div>
      );
    }

    // Special handling for patient_id field
    if (name === 'patient_id') {
      return (
        <div key={name} className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            {name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <div className="p-2 bg-gray-100 rounded-md text-sm text-gray-600">
            {formatFieldValue(name, value)}
          </div>
        </div>
      );
    }

    // Default to text input
    return (
      <div key={name} className="space-y-2">
        <Label htmlFor={name} className="text-sm font-medium">
          {name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          {param.required && <span className="text-red-500 ml-1">*</span>}
          {isMissing && <span className="text-orange-600 ml-2 text-xs">(Missing)</span>}
        </Label>
        <Input
          id={name}
          type="text"
          value={value}
          onChange={(e) => handleInputChange(name, e.target.value)}
          placeholder={placeholder || `Enter ${name}`}
          className={errors[name] ? 'border-red-500' : ''}
          disabled={isLoading}
        />
        {description && (
          <p className="text-xs text-gray-500">{description}</p>
        )}
        {errors[name] && (
          <p className="text-xs text-red-500">{errors[name]}</p>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full max-w-2xl border-orange-200 bg-orange-50">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-lg">{getActionTitle(actionType)}</CardTitle>
        </div>
        <CardDescription>
          Please provide the following information to complete your request:
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Use allParameters if available, otherwise fall back to missingParameters */}
          {allParameters 
            ? allParameters.map(param => renderField(param))
            : missingParameters.map(param => renderField(param))
          }
          
          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                'Processing...'
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Complete Action
                </>
              )}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ParameterHarvestingForm;