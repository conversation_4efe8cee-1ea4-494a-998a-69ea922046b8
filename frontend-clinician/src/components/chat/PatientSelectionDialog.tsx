import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/ui/spinner";
import { UserPlus, Search, X } from "lucide-react";
import apiClient from "@/lib/apiClient";

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
}

interface PatientSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPatient: (patientId: string, patientName: string) => void;
}

const PatientSelectionDialog: React.FC<PatientSelectionDialogProps> = ({
  isOpen,
  onClose,
  onSelectPatient,
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);

  // Fetch patients when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchPatients();
    }
  }, [isOpen]);

  // Filter patients when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPatients(patients);
      return;
    }

    const lowerCaseSearch = searchTerm.toLowerCase();
    const filtered = patients.filter(
      (patient) =>
        `${patient.first_name} ${patient.last_name}`
          .toLowerCase()
          .includes(lowerCaseSearch) ||
        patient.email?.toLowerCase().includes(lowerCaseSearch),
    );
    setFilteredPatients(filtered);
  }, [searchTerm, patients]);

  const fetchPatients = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get("/clinicians/patients?limit=100");
      if (response.data && response.data.items) {
        setPatients(response.data.items);
        setFilteredPatients(response.data.items);
      } else {
        setError("Failed to load patients: Invalid response format");
      }
    } catch (err) {
      // Error fetching patients
      setError("Failed to load patients. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectPatient = (patient: Patient) => {
    onSelectPatient(patient.id, `${patient.first_name} ${patient.last_name}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <UserPlus className="mr-2 h-5 w-5" />
            Start New Conversation
          </DialogTitle>
          <DialogDescription>
            Select a patient to start a new conversation.
          </DialogDescription>
        </DialogHeader>

        {/* Search Box */}
        <div className="relative my-2">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search patients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="absolute right-2.5 top-2.5 h-4 w-4 text-gray-500 hover:text-gray-900"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Patient List */}
        <div className="max-h-[300px] overflow-y-auto border rounded-md">
          {isLoading ? (
            <div className="flex justify-center items-center p-10">
              <Spinner className="h-8 w-8" />
            </div>
          ) : error ? (
            <div className="p-4 text-red-500 text-center">{error}</div>
          ) : filteredPatients.length === 0 ? (
            <div className="p-4 text-gray-500 text-center">
              {searchTerm
                ? "No patients found matching your search."
                : "No patients available."}
            </div>
          ) : (
            <ul className="divide-y">
              {filteredPatients.map((patient) => (
                <li
                  key={patient.id}
                  onClick={() => handleSelectPatient(patient)}
                  className="p-3 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
                >
                  <div className="font-medium">
                    {patient.first_name} {patient.last_name}
                  </div>
                  {patient.email && (
                    <div className="text-sm text-gray-500">{patient.email}</div>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PatientSelectionDialog;
