# Message Routing Feature Testing Checklist

> **Update**: Icons have been changed to use available ones in lucide-react v0.486.0:
>
> - Using `CircleUser` instead of `UserRound` for patient routing
> - Using `BrainCircuit` instead of `Robot` for AI routing

This document provides a manual testing checklist for the AI/Patient Message Routing Dropdown feature.

## Prerequisites

- Ensure you're logged in as a clinician
- Have at least one patient conversation available

## Functional Tests

### Dropdown UI and Selection

- [ ] Dropdown is visible when a patient conversation is selected
- [ ] Dropdown shows "AI Assistant" (default) and "Patient" options
- [ ] Dropdown shows the currently selected option in the button text
- [ ] Selecting an option from the dropdown changes the displayed option
- [ ] Dropdown is properly styled with icons for each option
- [ ] Button color changes according to selected option (blue for AI, green for Patient)

### Message Sending

- [ ] When "AI Assistant" is selected:

  - [ ] Placeholder text indicates message will go to AI
  - [ ] Sending a message includes `message_route: "ai"` in the context
  - [ ] Message appears in the chat with a blue background and "AI Only" badge
  - [ ] Information banner below the input shows AI-only information

- [ ] When "Patient" is selected:
  - [ ] Placeholder text indicates message will go to the patient
  - [ ] Sending a message includes `message_route: "patient"` in the context
  - [ ] Message appears in the chat with a green background and "Sent to Patient" badge
  - [ ] Information banner below the input shows patient message information

### State Persistence

- [ ] If "Patient" option is selected, refreshing the page keeps "Patient" selected
- [ ] If "AI Assistant" option is selected, refreshing the page keeps "AI Assistant" selected
- [ ] The saved preference should persist across browser sessions (close and reopen browser)

### Edge Cases

- [ ] The dropdown should be disabled when a message is being sent
- [ ] The dropdown should not appear in general conversations (no patient selected)
- [ ] The dropdown should not appear in the patient interface
- [ ] When switching between patient conversations, the selected option should remain consistent with the last choice

## Visual Testing

- [ ] Dark mode compatibility: The component should look good in both light and dark modes
- [ ] Mobile view: The component should be usable on smaller screens
- [ ] The dropdown and message input should be properly aligned
- [ ] The information banner should be clearly visible and match the selected option's color theme

## Performance and Usability

- [ ] The dropdown should open and close smoothly
- [ ] Selecting an option should be immediate with no noticeable delay
- [ ] Hover states on the dropdown options should provide clear feedback
- [ ] The dropdown should not obscure any important UI elements when open

## Accessibility Testing

- [ ] The dropdown should be navigable and usable with keyboard only
- [ ] The selected option should be clearly distinguishable both visually and for screen readers
- [ ] Color contrast should meet WCAG AA standards for all text elements

## Known Issues/Limitations

- The routing feature is only available for patient-specific conversations
- When switching between patients, the same routing option is used for all patients (global preference)
