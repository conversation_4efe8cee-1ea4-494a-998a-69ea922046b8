import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Calendar, Clock, Pill, AlertTriangle, FileText, User } from 'lucide-react';
import { formatWeightDisplay } from '@/lib/weightUtils';

interface SingleActionResponseProps {
  actionType: string;
  success: boolean;
  message: string;
  data?: any;
}

export const SingleActionResponse: React.FC<SingleActionResponseProps> = ({
  actionType,
  success,
  message,
  data
}) => {
  const getActionIcon = () => {
    const iconMap: Record<string, JSX.Element> = {
      appointment_create: <Calendar className="w-5 h-5" />,
      appointment_request_create: <Calendar className="w-5 h-5" />,
      medication_request_create: <Pill className="w-5 h-5" />,
      side_effect_report_create: <AlertTriangle className="w-5 h-5" />,
      note_create: <FileText className="w-5 h-5" />,
      weight_log_create: <User className="w-5 h-5" />
    };
    return iconMap[actionType] || <CheckCircle className="w-5 h-5" />;
  };

  const getActionLabel = (type: string) => {
    const labels: Record<string, string> = {
      appointment_create: 'Appointment Scheduled',
      appointment_request_create: 'Appointment Requested',
      medication_request_create: 'Medication Requested',
      side_effect_report_create: 'Side Effect Reported',
      note_create: 'Clinical Note Added',
      weight_log_create: 'Weight Logged'
    };
    return labels[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const renderActionDetails = () => {
    if (!data) return null;

    switch (actionType) {
      case 'appointment_create':
        return (
          <div className="space-y-2 mt-3">
            {data.appointment_time && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Calendar className="w-4 h-4" />
                <span>Scheduled for: {new Date(data.appointment_time).toLocaleString()}</span>
              </div>
            )}
            {data.duration_minutes && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>Duration: {data.duration_minutes} minutes</span>
              </div>
            )}
            {data.appointment_type && (
              <div className="text-sm text-gray-600">
                Type: {data.appointment_type}
              </div>
            )}
          </div>
        );

      case 'medication_request_create':
        return (
          <div className="space-y-2 mt-3">
            {data.medication_name && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Pill className="w-4 h-4" />
                <span>Medication: {data.medication_name}</span>
              </div>
            )}
            {data.dosage && (
              <div className="text-sm text-gray-600">
                Dosage: {data.dosage}
              </div>
            )}
            {data.frequency && (
              <div className="text-sm text-gray-600">
                Frequency: {data.frequency}
              </div>
            )}
            {data.duration && (
              <div className="text-sm text-gray-600">
                Duration: {data.duration}
              </div>
            )}
          </div>
        );

      case 'side_effect_report_create':
        return (
          <div className="space-y-2 mt-3">
            {data.medication_name && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Pill className="w-4 h-4" />
                <span>Medication: {data.medication_name}</span>
              </div>
            )}
            {data.symptoms && (
              <div className="text-sm text-gray-600">
                Symptoms: {data.symptoms}
              </div>
            )}
            {data.severity && (
              <div className="mt-2">
                <Badge variant={
                  data.severity.toLowerCase() === 'severe' ? 'destructive' :
                  data.severity.toLowerCase() === 'moderate' ? 'outline' : 'secondary'
                }>
                  {data.severity} Severity
                </Badge>
              </div>
            )}
          </div>
        );

      case 'note_create':
        return (
          <div className="space-y-2 mt-3">
            {data.note_content && (
              <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                {data.note_content}
              </div>
            )}
            {data.note_type && (
              <div className="text-sm text-gray-600">
                Type: {data.note_type}
              </div>
            )}
          </div>
        );

      case 'weight_log_create':
        return (
          <div className="space-y-2 mt-3">
            {data.weight_kg && (
              <div className="text-sm text-gray-600">
                Weight: <span className="font-semibold">{formatWeightDisplay(data.weight_kg)}</span>
              </div>
            )}
            {data.log_date && (
              <div className="text-sm text-gray-600">
                Date: {new Date(data.log_date).toLocaleDateString()}
              </div>
            )}
          </div>
        );

      default:
        // For any other action type, show all data fields
        return (
          <div className="space-y-2 mt-3">
            {Object.entries(data).map(([key, value]) => {
              // Skip internal fields
              if (key.endsWith('_id') || key === 'id') return null;
              
              return (
                <div key={key} className="text-sm text-gray-600">
                  <span className="font-medium">
                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                  </span>{' '}
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </div>
              );
            })}
          </div>
        );
    }
  };

  return (
    <Card className={`p-4 ${success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-start gap-3">
        <div className={`mt-0.5 ${success ? 'text-green-600' : 'text-red-600'}`}>
          {success ? <CheckCircle className="w-5 h-5" /> : <XCircle className="w-5 h-5" />}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <div className={success ? 'text-green-600' : 'text-red-600'}>
              {getActionIcon()}
            </div>
            <h4 className="font-medium text-gray-900">
              {getActionLabel(actionType)}
            </h4>
          </div>
          
          <p className="text-sm text-gray-600">{message}</p>
          
          {success && renderActionDetails()}
        </div>
      </div>
    </Card>
  );
};