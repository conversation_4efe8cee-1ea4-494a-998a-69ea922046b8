import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import MessageRoutingDropdown, {
  MessageRoutingOption,
} from "../MessageRoutingDropdown";
// Mock the Lucide icons since they might not be available in the test environment
jest.mock("lucide-react", () => ({
  ChevronDown: () => <div data-testid="chevron-icon">ChevronDown</div>,
  BrainCircuit: () => <div data-testid="brain-icon">BrainCircuit</div>,
  CircleUser: () => <div data-testid="user-icon">CircleUser</div>,
  Send: () => <div data-testid="send-icon">Send</div>,
}));

describe("MessageRoutingDropdown", () => {
  // Setup default props
  const defaultOptions: MessageRoutingOption[] = [
    {
      id: "patient",
      label: "Patient",
      description: "Message will be sent to the patient directly",
    },
    {
      id: "ai",
      label: "AI Assistant",
      description: "Message will be processed by the AI only",
    },
  ];

  const defaultProps = {
    selectedRoute: defaultOptions[1], // Default to AI
    options: defaultOptions,
    onRouteChange: jest.fn(),
    disabled: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders with the selected route visible", () => {
    render(<MessageRoutingDropdown {...defaultProps} />);

    // The selected route should be visible in the button
    expect(screen.getByText(/send to: ai assistant/i)).toBeInTheDocument();
  });

  it("shows all options when clicked", async () => {
    render(<MessageRoutingDropdown {...defaultProps} />);

    // Open the dropdown
    fireEvent.click(screen.getByRole("button"));

    // Both options should be in the dropdown
    expect(screen.getByText("Patient")).toBeInTheDocument();
    expect(screen.getByText("AI Assistant")).toBeInTheDocument();
  });

  it("calls onRouteChange when an option is selected", () => {
    render(<MessageRoutingDropdown {...defaultProps} />);

    // Open the dropdown
    fireEvent.click(screen.getByRole("button"));

    // Select the patient option
    fireEvent.click(screen.getByText("Patient"));

    // Check if onRouteChange was called with the correct option
    expect(defaultProps.onRouteChange).toHaveBeenCalledWith(defaultOptions[0]);
  });

  it("displays different styling based on the selected route", () => {
    // First render with AI selected
    const { rerender } = render(<MessageRoutingDropdown {...defaultProps} />);

    // Get the button element
    const button = screen.getByRole("button");

    // Check AI styling
    expect(button.className).toContain("text-blue-700");

    // Re-render with Patient selected
    rerender(
      <MessageRoutingDropdown
        {...defaultProps}
        selectedRoute={defaultOptions[0]}
      />,
    );

    // Check patient styling
    expect(button.className).toContain("text-green-700");
  });

  it("is disabled when the disabled prop is true", () => {
    render(<MessageRoutingDropdown {...defaultProps} disabled={true} />);

    // The button should be disabled
    expect(screen.getByRole("button")).toBeDisabled();
  });
});
