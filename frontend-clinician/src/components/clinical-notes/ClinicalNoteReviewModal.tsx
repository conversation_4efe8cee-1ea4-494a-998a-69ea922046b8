import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
// Removed ScrollArea import - using div with overflow instead
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  CheckCircle,
  Edit3,
  FileText,
  Save,
  X,
  Info,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface BillingCode {
  code: string;
  description: string;
  confidence: number;
}

interface ClinicalNoteSections {
  subjective?: string;
  objective?: string;
  assessment?: string;
  plan?: string;
  additional_notes?: string;
  _metadata?: {
    key_findings?: string[];
    missing_information?: string[];
  };
}

interface ClinicalNoteData {
  id: string;
  patient_id: string;
  note_type: string;
  sections: ClinicalNoteSections;
  raw_text: string;
  status: string;
  ai_confidence_score: number;
  suggested_icd10_codes?: BillingCode[];
  suggested_cpt_codes?: BillingCode[];
  created_at: string;
  updated_at: string;
}

interface GenerateResponse {
  note: ClinicalNoteData;
  confidence_score: number;
  messages_processed: number;
  generation_time_ms: number;
  suggested_edits?: string[];
}

interface ClinicalNoteReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientId: string;
  patientName?: string;
  generatedNote?: GenerateResponse;
  onSave: (noteId: string, updatedSections: ClinicalNoteSections) => Promise<void>;
  onApprove: (noteId: string) => Promise<void>;
}

export default function ClinicalNoteReviewModal({
  isOpen,
  onClose,
  patientId,
  patientName = "Patient",
  generatedNote,
  onSave,
  onApprove,
}: ClinicalNoteReviewModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isApproving, setIsApproving] = useState(false);
  const [editedSections, setEditedSections] = useState<ClinicalNoteSections>({});
  const [activeTab, setActiveTab] = useState("structured");

  useEffect(() => {
    if (generatedNote?.note.sections) {
      setEditedSections(generatedNote.note.sections);
    }
  }, [generatedNote]);

  const handleSectionChange = (section: keyof ClinicalNoteSections, value: string) => {
    setEditedSections((prev) => ({
      ...prev,
      [section]: value,
    }));
  };

  const handleSave = async () => {
    if (!generatedNote?.note.id) return;
    
    setIsSaving(true);
    try {
      await onSave(generatedNote.note.id, editedSections);
      setIsEditing(false);
    } finally {
      setIsSaving(false);
    }
  };

  const handleApprove = async () => {
    if (!generatedNote?.note.id) return;
    
    setIsApproving(true);
    try {
      await onApprove(generatedNote.note.id);
      onClose();
    } finally {
      setIsApproving(false);
    }
  };

  const getConfidenceBadgeColor = (score: number) => {
    if (score >= 0.8) return "bg-green-500";
    if (score >= 0.6) return "bg-yellow-500";
    return "bg-red-500";
  };

  if (!generatedNote) return null;

  const { note, confidence_score, messages_processed, generation_time_ms, suggested_edits } = generatedNote;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Clinical Note Review - {patientName}</span>
            <div className="flex items-center gap-2">
              <Badge className={cn("text-white", getConfidenceBadgeColor(confidence_score))}>
                Confidence: {(confidence_score * 100).toFixed(0)}%
              </Badge>
              <Badge variant="outline">
                {messages_processed} messages • {generation_time_ms}ms
              </Badge>
            </div>
          </DialogTitle>
          <DialogDescription>
            Review and edit the AI-generated clinical note before approval
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[calc(90vh-12rem)] overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="structured">Structured View</TabsTrigger>
              <TabsTrigger value="raw">Raw Text</TabsTrigger>
              <TabsTrigger value="billing">Billing Codes</TabsTrigger>
            </TabsList>

            <div className="max-h-[calc(90vh-16rem)] overflow-y-auto">
              <TabsContent value="structured" className="space-y-4 p-1">
              {/* Suggested Edits Alert */}
              {suggested_edits && suggested_edits.length > 0 && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription>
                    <strong>Suggested improvements:</strong>
                    <ul className="list-disc list-inside mt-1">
                      {suggested_edits.map((edit, index) => (
                        <li key={index}>{edit}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Key Findings */}
              {note.sections._metadata?.key_findings && note.sections._metadata.key_findings.length > 0 && (
                <div>
                  <Label className="text-sm font-semibold mb-2 flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    Key Findings
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {note.sections._metadata.key_findings.map((finding, index) => (
                      <Badge key={index} variant="secondary">{finding}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* SOAP Sections */}
              <div className="space-y-4">
                {["subjective", "objective", "assessment", "plan"].map((section) => (
                  <div key={section}>
                    <Label className="text-sm font-semibold mb-2 capitalize">
                      {section}
                    </Label>
                    {isEditing ? (
                      <Textarea
                        value={editedSections[section as keyof ClinicalNoteSections] || ""}
                        onChange={(e) => handleSectionChange(section as keyof ClinicalNoteSections, e.target.value)}
                        rows={4}
                        className="w-full"
                      />
                    ) : (
                      <div className="p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                        {note.sections[section as keyof ClinicalNoteSections] || "Not documented"}
                      </div>
                    )}
                  </div>
                ))}

                {/* Additional Notes */}
                {(isEditing || note.sections.additional_notes) && (
                  <div>
                    <Label className="text-sm font-semibold mb-2">Additional Notes</Label>
                    {isEditing ? (
                      <Textarea
                        value={editedSections.additional_notes || ""}
                        onChange={(e) => handleSectionChange("additional_notes", e.target.value)}
                        rows={3}
                        className="w-full"
                      />
                    ) : (
                      <div className="p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
                        {note.sections.additional_notes}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="raw" className="space-y-4 p-1">
              <div className="p-4 bg-gray-50 rounded-md">
                <pre className="whitespace-pre-wrap font-mono text-sm">
                  {note.raw_text}
                </pre>
              </div>
            </TabsContent>

            <TabsContent value="billing" className="space-y-6 p-1">
              {/* ICD-10 Codes */}
              <div>
                <Label className="text-sm font-semibold mb-3 block">ICD-10 Diagnosis Codes</Label>
                {note.suggested_icd10_codes && note.suggested_icd10_codes.length > 0 ? (
                  <div className="space-y-2">
                    {note.suggested_icd10_codes.map((code, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div>
                          <span className="font-mono font-semibold">{code.code}</span>
                          <span className="ml-3 text-sm text-gray-600">{code.description}</span>
                        </div>
                        <Badge variant={code.confidence >= 0.8 ? "default" : "secondary"}>
                          {(code.confidence * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No diagnosis codes suggested</p>
                )}
              </div>

              {/* CPT Codes */}
              <div>
                <Label className="text-sm font-semibold mb-3 block">CPT Procedure Codes</Label>
                {note.suggested_cpt_codes && note.suggested_cpt_codes.length > 0 ? (
                  <div className="space-y-2">
                    {note.suggested_cpt_codes.map((code, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div>
                          <span className="font-mono font-semibold">{code.code}</span>
                          <span className="ml-3 text-sm text-gray-600">{code.description}</span>
                        </div>
                        <Badge variant={code.confidence >= 0.8 ? "default" : "secondary"}>
                          {(code.confidence * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No procedure codes suggested</p>
                )}
              </div>
            </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between mt-4">
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} disabled={isSaving || isApproving}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            {!isEditing && note.status === "draft" && (
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                <Edit3 className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            {isEditing && (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setEditedSections(note.sections);
                  }}
                  disabled={isSaving}
                >
                  Cancel Edit
                </Button>
                <Button onClick={handleSave} disabled={isSaving}>
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </>
            )}
            {!isEditing && note.status === "draft" && (
              <Button onClick={handleApprove} disabled={isApproving} className="bg-green-600 hover:bg-green-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                {isApproving ? "Approving..." : "Approve Note"}
              </Button>
            )}
            {note.status === "approved" && (
              <Badge className="bg-green-100 text-green-800">
                <CheckCircle className="h-4 w-4 mr-1" />
                Approved
              </Badge>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}