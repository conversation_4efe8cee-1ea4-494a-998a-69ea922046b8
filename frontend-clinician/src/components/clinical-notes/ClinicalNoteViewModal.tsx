import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  CheckCircle,
  Edit3,
  FileText,
  Save,
  X,
  Info,
  Trash2,
} from "lucide-react";
import { formatDateTime } from "@pulsetrack/shared-frontend";
import apiClient from "@/lib/apiClient";

interface BillingCode {
  code: string;
  description: string;
  confidence: number;
}

interface ClinicalNote {
  id: string;
  patient_id: string;
  patient_name?: string;
  clinician_id: string;
  clinician_name?: string;
  note_type: string;
  status: 'draft' | 'reviewed' | 'approved' | 'amended';
  ai_confidence_score?: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
  approved_by?: string;
  approver_name?: string;
  sections: {
    subjective?: string;
    objective?: string;
    assessment?: string;
    plan?: string;
    additional_notes?: string;
    _metadata?: {
      key_findings?: string[];
      missing_information?: string[];
      extraction_timestamp?: string;
    };
  };
  suggested_icd10_codes?: BillingCode[];
  suggested_cpt_codes?: BillingCode[];}

interface ClinicalNoteViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  note: ClinicalNote;
  onUpdate?: (updatedNote: ClinicalNote) => void;
  onDelete?: (noteId: string) => void;
}

export default function ClinicalNoteViewModal({
  isOpen,
  onClose,
  note,
  onUpdate,
  onDelete,
}: ClinicalNoteViewModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedSections, setEditedSections] = useState(note.sections);
  const [isSaving, setIsSaving] = useState(false);
  const [isApproving, setIsApproving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSectionChange = (section: keyof typeof editedSections, value: string) => {
    // Don't allow editing _metadata
    if (section === '_metadata') return;
    
    setEditedSections(prev => ({
      ...prev,
      [section]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      const response = await apiClient.put(`/clinical-notes/${note.id}`, {
        sections: editedSections,
        status: 'reviewed',
      });
      
      if (onUpdate) {
        onUpdate(response.data);
      }
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving clinical note:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleApprove = async () => {
    try {
      setIsApproving(true);
      const response = await apiClient.post(`/clinical-notes/${note.id}/approve`);
      
      if (onUpdate) {
        onUpdate(response.data);
      }
    } catch (error) {
      console.error('Error approving clinical note:', error);
    } finally {
      setIsApproving(false);
    }
  };

  const handleDelete = async () => {
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    try {
      setIsDeleting(true);
      await apiClient.delete(`/clinical-notes/${note.id}`);
      
      if (onDelete) {
        onDelete(note.id);
      }
      
      onClose();
    } catch (error) {
      console.error('Error deleting clinical note:', error);
      const errorMessage = (error as any).response?.data?.detail || 'Failed to delete clinical note';
      alert(errorMessage);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'reviewed':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'amended':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderSection = (title: string, content?: string, sectionKey?: keyof typeof editedSections) => {
    // Skip rendering _metadata section
    if (sectionKey === '_metadata') return null;
    if (!content && !isEditing) return null;

    return (
      <div className="space-y-2">
        <Label className="text-base font-semibold">{title}</Label>
        {isEditing && sectionKey ? (
          <Textarea
            value={editedSections[sectionKey] || ''}
            onChange={(e) => handleSectionChange(sectionKey, e.target.value)}
            className="min-h-[100px]"
            placeholder={`Enter ${title.toLowerCase()}...`}
          />
        ) : (
          <div className="p-3 bg-gray-50 rounded-md whitespace-pre-wrap">
            {content || <span className="text-gray-400 italic">No content</span>}
          </div>
        )}
      </div>
    );
  };

  const renderBillingCodes = (codes: BillingCode[] | undefined, title: string) => {
    if (!codes || codes.length === 0) return null;

    return (
      <div className="space-y-2">
        <Label className="text-base font-semibold">{title}</Label>
        <div className="space-y-1">
          {codes.map((code, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div>
                <span className="font-mono font-semibold">{code.code}</span>
                <span className="ml-2 text-sm text-gray-600">{code.description}</span>
              </div>
              <Badge variant="secondary" className="ml-2">
                {Math.round(code.confidence * 100)}%
              </Badge>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl flex items-center gap-2">
            <FileText className="w-6 h-6" />
            Clinical Note Review
          </DialogTitle>
          <DialogDescription asChild>
            <div className="space-y-2">
              <div className="flex items-center gap-4 mt-2">
                <span>
                  <strong>Patient:</strong> {note.patient_name || 'Unknown'}
                </span>
                <span>
                  <strong>Type:</strong> {note.note_type}
                </span>
                <Badge className={getStatusColor(note.status)}>
                  {note.status.charAt(0).toUpperCase() + note.status.slice(1)}
                </Badge>
                {note.ai_confidence_score && (
                  <Badge variant="outline">
                    AI Confidence: {Math.round(note.ai_confidence_score * 100)}%
                  </Badge>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Created {formatDateTime(note.created_at)} by {note.clinician_name || 'Unknown'}
                {note.approved_at && (
                  <span className="ml-2">
                    • Approved {formatDateTime(note.approved_at)} by {note.approver_name || 'Unknown'}
                  </span>
                )}
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="content" className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="content">Note Content</TabsTrigger>
            <TabsTrigger value="billing">Billing Codes</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="flex-1 overflow-y-auto space-y-4 mt-4">
            {renderSection("Subjective", note.sections?.subjective, "subjective")}
            {renderSection("Objective", note.sections?.objective, "objective")}
            {renderSection("Assessment", note.sections?.assessment, "assessment")}
            {renderSection("Plan", note.sections?.plan, "plan")}
            {renderSection("Additional Notes", note.sections?.additional_notes, "additional_notes")}
            
            {/* Show key findings if available */}
            {note.sections?._metadata?.key_findings && note.sections._metadata.key_findings.length > 0 && (
              <div className="space-y-2 mt-4">
                <Label className="text-base font-semibold">Key Findings</Label>
                <div className="p-3 bg-blue-50 rounded-md">
                  <ul className="list-disc list-inside space-y-1">
                    {note.sections._metadata.key_findings.map((finding: string, index: number) => (
                      <li key={index} className="text-sm">{finding}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
            
            {/* Show missing information if available */}
            {note.sections?._metadata?.missing_information && note.sections._metadata.missing_information.length > 0 && (
              <div className="space-y-2 mt-4">
                <Label className="text-base font-semibold">Missing Information</Label>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      {note.sections._metadata.missing_information.map((info: string, index: number) => (
                        <li key={index} className="text-sm">{info}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </TabsContent>

          <TabsContent value="billing" className="flex-1 overflow-y-auto space-y-4 mt-4">
            {renderBillingCodes(note.suggested_icd10_codes, "ICD-10 Diagnosis Codes")}
            {renderBillingCodes(note.suggested_cpt_codes, "CPT Procedure Codes")}
            
            {(!note.suggested_icd10_codes?.length && !note.suggested_cpt_codes?.length) && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  No billing codes have been suggested for this note.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="border-t pt-4">
          <div className="flex items-center gap-2 w-full">
            {!isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  disabled={note.status === 'approved'}
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  Edit Note
                </Button>
                {note.status !== 'approved' && (
                  <Button
                    onClick={handleApprove}
                    disabled={isApproving}
                    variant="default"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    {isApproving ? 'Approving...' : 'Approve Note'}
                  </Button>
                )}
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setEditedSections(note.sections);
                  }}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            )}
            
            <div className="flex-1" />
            
            {/* Delete button - shown for all notes by the author */}
            {!isEditing && (
              <Button
                variant={showDeleteConfirm ? "destructive" : "ghost"}
                onClick={handleDelete}
                disabled={isDeleting}
                className={showDeleteConfirm ? "" : "text-red-600 hover:text-red-800 hover:bg-red-50"}
              >
                {showDeleteConfirm ? (
                  <>
                    {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </Button>
            )}
            
            {showDeleteConfirm && (
              <Button 
                variant="ghost" 
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
            )}
            
            {!showDeleteConfirm && (
              <Button variant="ghost" onClick={onClose}>
                Close
              </Button>
            )}
            
            {note.status === 'approved' && (
              <Badge variant="default" className="ml-2 bg-green-100 text-green-800 border-green-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Approved
              </Badge>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}