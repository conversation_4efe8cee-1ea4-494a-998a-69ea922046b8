import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Loader2 } from "lucide-react";

interface GenerateClinicalNoteButtonProps {
  patientId: string;
  disabled?: boolean;
  onGenerate: () => void;
}

export default function GenerateClinicalNoteButton({
  patientId,
  disabled = false,
  onGenerate,
}: GenerateClinicalNoteButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleClick = async () => {
    setIsGenerating(true);
    try {
      await onGenerate();
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleClick}
      disabled={disabled || isGenerating || !patientId}
      className="gap-2"
      title="Generate a clinical note from this conversation"
    >
      {isGenerating ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          Generating...
        </>
      ) : (
        <>
          <FileText className="h-4 w-4" />
          Generate Clinical Note
        </>
      )}
    </Button>
  );
}