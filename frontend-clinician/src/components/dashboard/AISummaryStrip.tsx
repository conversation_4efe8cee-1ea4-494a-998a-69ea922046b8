import React from "react";
import { BrainCircuit } from "lucide-react";

interface AISummaryStripProps {
  summary?: string;
}

const defaultSummary =
  "🧠 AI Insights: Loading personalized insights...";

export const AISummaryStrip: React.FC<AISummaryStripProps> = ({
  summary = defaultSummary,
}) => {
  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 rounded-lg px-4 py-3 flex items-center space-x-3 shadow-sm border border-blue-200">
      <BrainCircuit className="h-5 w-5 flex-shrink-0 animate-pulse" />
      <span className="text-sm font-medium">{summary}</span>
    </div>
  );
};

export default AISummaryStrip;
