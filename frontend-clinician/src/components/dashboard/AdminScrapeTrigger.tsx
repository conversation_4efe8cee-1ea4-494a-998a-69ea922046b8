import React, { useState } from "react";
import apiClient from "../../lib/apiClient";
import { AxiosError } from "axios";

const AdminScrapeTrigger: React.FC = () => {
  const [clinicUrl, setClinicUrl] = useState("");
  const [clinicId, setClinicId] = useState("");
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");
  const [message, setMessage] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus("loading");
    setMessage(null);

    try {
      const payload = {
        clinic_url: clinicUrl,
        supplemental_context: {
          clinic_id: clinicId || undefined,
        },
      };

      await apiClient.post("/admin/clinics/ingest", payload);
      setStatus("success");
      setMessage("Scraping initiated successfully.");
    } catch (error: unknown) {
      console.error("Error triggering scraping:", error);
      setStatus("error");
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as AxiosError<{ detail?: string }>;
        setMessage(
          axiosError.response?.data?.detail || "Failed to initiate scraping.",
        );
      } else {
        setMessage("Failed to initiate scraping.");
      }
    }
  };

  return (
    <div className="p-4 border rounded shadow max-w-md">
      <h2 className="text-lg font-semibold mb-2">
        Trigger Clinic Website Scraping
      </h2>
      <form onSubmit={handleSubmit} className="flex flex-col gap-2">
        <label>
          Clinic URL:
          <input
            type="url"
            value={clinicUrl}
            onChange={(e) => setClinicUrl(e.target.value)}
            required
            className="border p-1 rounded w-full"
            placeholder="https://exampleclinic.com"
          />
        </label>
        <label>
          Clinic ID (optional):
          <input
            type="text"
            value={clinicId}
            onChange={(e) => setClinicId(e.target.value)}
            className="border p-1 rounded w-full"
            placeholder="Clinic ID"
          />
        </label>
        <button
          type="submit"
          disabled={status === "loading"}
          className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded"
        >
          {status === "loading" ? "Submitting..." : "Start Scraping"}
        </button>
      </form>
      {message && (
        <div
          className={`mt-2 ${status === "error" ? "text-red-600" : "text-green-600"}`}
        >
          {message}
        </div>
      )}
    </div>
  );
};

export default AdminScrapeTrigger;
