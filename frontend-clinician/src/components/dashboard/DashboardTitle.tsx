import React from "react";

interface DashboardTitleProps {
  title: string;
  subtitle?: string;
}

export const DashboardTitle: React.FC<DashboardTitleProps> = ({
  title,
  subtitle,
}) => {
  return (
    <div className="mb-6">
      {" "}
      {/* Add margin-bottom for spacing */}
      <h1 className="text-3xl font-semibold">
        <span role="img" aria-label="female doctor emoji" className="mr-2">
          👩‍⚕️
        </span>
        {title}
      </h1>
      {subtitle && <p className="text-muted-foreground mt-1">{subtitle}</p>}
    </div>
  );
};
