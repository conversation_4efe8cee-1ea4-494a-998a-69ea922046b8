import React, { useState, useEffect } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  <PERSON>r, 
  <PERSON><PERSON>, 
  Loader2, 
  AlertCircle, 
  MessageSquare, 
  Calendar,
  Weight,
  AlertTriangle,
  Pill,
  FileText,
  Activity
} from "lucide-react";
import apiClient from "@/lib/apiClient";
import { formatRelativeTime } from "@pulsetrack/shared-frontend";
import { Badge } from "@/components/ui/badge";
import { formatWeightDisplay } from "@/lib/weightUtils";

interface EventLog {
  id: string;
  actor_user_id: string;
  actor_role: string;
  action: string;
  target_resource_type?: string;
  target_resource_id?: string;
  details?: Record<string, any>;
  extracted_intent?: string;
  is_llm_driven?: boolean;
  created_at: string;
  outcome: string;
}

interface PatientActivityFeedProps {
  patientId?: string;
  limit?: number;
  showHeader?: boolean;
}

export const PatientActivityFeed: React.FC<PatientActivityFeedProps> = ({ 
  patientId, 
  limit = 20,
  showHeader = true 
}) => {
  const [activities, setActivities] = useState<EventLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      setError(null);
      try {
        const params: any = {
          limit,
          skip: 0,
        };
        
        if (patientId) {
          params.actor_id = patientId;
        }

        const response = await apiClient.get<EventLog[]>("/event-logs/", { params });
        setActivities(response.data);
      } catch (err: any) {
        console.error("Error fetching patient activities:", err);
        setError(err.response?.data?.detail || "Failed to fetch activities");
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();

    // Refresh every 30 seconds to make it "live"
    const interval = setInterval(fetchActivities, 30000);
    return () => clearInterval(interval);
  }, [patientId, limit]);

  const getActivityIcon = (event: EventLog) => {
    // Icon based on resource type and action
    if (event.is_llm_driven || event.extracted_intent) {
      return <Bot className="h-4 w-4" />;
    }
    
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    const action = event.action.toLowerCase();
    
    if (resourceType.includes("message") || action.includes("chat")) {
      return <MessageSquare className="h-4 w-4" />;
    }
    if (resourceType.includes("appointment")) {
      return <Calendar className="h-4 w-4" />;
    }
    if (resourceType.includes("weight")) {
      return <Weight className="h-4 w-4" />;
    }
    if (resourceType.includes("side_effect")) {
      return <AlertTriangle className="h-4 w-4" />;
    }
    if (resourceType.includes("medication")) {
      return <Pill className="h-4 w-4" />;
    }
    if (resourceType.includes("note") || resourceType.includes("clinical")) {
      return <FileText className="h-4 w-4" />;
    }
    
    return <Activity className="h-4 w-4" />;
  };

  const getActivityColor = (event: EventLog) => {
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    
    if (event.is_llm_driven || event.extracted_intent) {
      return "bg-blue-100 text-blue-600";
    }
    if (resourceType.includes("side_effect")) {
      return "bg-red-100 text-red-600";
    }
    if (resourceType.includes("appointment")) {
      return "bg-green-100 text-green-600";
    }
    if (resourceType.includes("weight")) {
      return "bg-purple-100 text-purple-600";
    }
    if (resourceType.includes("medication")) {
      return "bg-orange-100 text-orange-600";
    }
    
    return "bg-gray-100 text-gray-600";
  };

  const formatActivityDescription = (event: EventLog): string => {
    const actor = event.details?.actor_name || "Patient";
    const resource = event.target_resource_type?.replace(/_/g, " ") || "resource";
    
    // Handle LLM-driven actions with intent
    if (event.extracted_intent) {
      return `AI: ${event.extracted_intent}`;
    }
    
    // Custom descriptions for specific actions
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    const action = event.action.toUpperCase();
    
    if (resourceType === "chat_message") {
      if (action === "CREATE") {
        return `${actor} sent a message`;
      }
    }
    
    if (resourceType === "weight_log") {
      if (action === "CREATE") {
        const weight = event.details?.weight_kg;
        return weight ? `${actor} logged weight: ${formatWeightDisplay(weight)}` : `${actor} logged weight`;
      }
    }
    
    if (resourceType === "side_effect_report") {
      if (action === "CREATE") {
        const severity = event.details?.severity;
        return severity ? `${actor} reported ${severity} side effects` : `${actor} reported side effects`;
      }
    }
    
    if (resourceType === "appointment") {
      if (action === "CREATE") {
        return `${actor} scheduled an appointment`;
      }
      if (action === "UPDATE") {
        return `${actor} updated appointment`;
      }
      if (action === "COMPLETE") {
        return `Appointment completed with ${actor}`;
      }
    }
    
    if (resourceType === "medication_request") {
      if (action === "CREATE") {
        return `${actor} requested medication`;
      }
      if (action === "APPROVE") {
        return `Medication request approved`;
      }
      if (action === "REJECT") {
        return `Medication request rejected`;
      }
    }
    
    // Default formatting
    switch (action) {
      case "CREATE":
        return `${actor} created ${resource}`;
      case "UPDATE":
        return `${actor} updated ${resource}`;
      case "DELETE":
        return `${actor} deleted ${resource}`;
      case "VIEW":
        return `${actor} viewed ${resource}`;
      case "APPROVE":
        return `${actor} approved ${resource}`;
      case "REJECT":
        return `${actor} rejected ${resource}`;
      case "ASSIGN":
        return `${actor} assigned ${resource}`;
      case "COMPLETE":
        return `${actor} completed ${resource}`;
      case "FLAG":
        return `${actor} flagged ${resource}`;
      default:
        return `${actor} performed ${event.action.toLowerCase()} on ${resource}`;
    }
  };

  const getOutcomeBadge = (outcome: string) => {
    switch (outcome.toUpperCase()) {
      case "SUCCESS":
        return <Badge variant="outline" className="text-green-600">Success</Badge>;
      case "FAILURE":
        return <Badge variant="outline" className="text-red-600">Failed</Badge>;
      case "PARTIAL":
        return <Badge variant="outline" className="text-yellow-600">Partial</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2 text-muted-foreground">Loading activities...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-48 text-destructive">
        <AlertCircle className="h-5 w-5 mr-2" />
        <span>{error}</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {showHeader && (
        <h3 className="text-lg font-semibold mb-4">Patient Activity</h3>
      )}
      <div className="space-y-4">
        {activities.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">
            No activity to display
          </p>
        ) : (
          activities.map((activity, index) => (
            <div
              key={activity.id}
              className={`flex items-start space-x-3 ${
                index < activities.length - 1 ? "pb-4 border-b border-border" : ""
              }`}
            >
              <Avatar className={`h-8 w-8 mt-1 ${getActivityColor(activity)}`}>
                <AvatarFallback>
                  {getActivityIcon(activity)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <p className="text-sm text-muted-foreground">
                    {activity.created_at && !isNaN(new Date(activity.created_at).getTime())
                      ? formatRelativeTime(activity.created_at)
                      : "Recently"}
                  </p>
                  {getOutcomeBadge(activity.outcome)}
                </div>
                <p className="text-sm font-medium">
                  {formatActivityDescription(activity)}
                </p>
                {(activity.is_llm_driven || activity.extracted_intent) && (
                  <span className="text-xs text-blue-600 font-medium">
                    AI-Assisted
                  </span>
                )}
                {activity.details?.notes && (
                  <p className="text-xs text-muted-foreground mt-1 truncate">
                    {activity.details.notes}
                  </p>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};