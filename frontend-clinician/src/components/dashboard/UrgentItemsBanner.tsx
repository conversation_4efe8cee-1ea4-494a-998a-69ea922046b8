import React from "react";
import { AlertTriangle, AlertCircle, Calendar, Activity } from "lucide-react";

interface UrgentItemsData {
  totalUrgentItems: number;
  criticalAlerts: number;
  highAlerts?: number;
  overdueAppointments: number;
  criticalSideEffects: number;
}

interface UrgentItemsBannerProps {
  items: UrgentItemsData;
  onViewAll?: () => void;
}

export const UrgentItemsBanner: React.FC<UrgentItemsBannerProps> = ({
  items,
  onViewAll,
}) => {
  if (items.totalUrgentItems === 0) return null;

  const urgentItemsList = [
    {
      count: items.criticalAlerts,
      label: "critical patient alerts",
      icon: AlertCircle,
      color: "text-red-600 bg-red-100",
    },
    {
      count: items.highAlerts || 0,
      label: "high priority alerts",
      icon: AlertCircle,
      color: "text-orange-600 bg-orange-100",
    },
    {
      count: items.overdueAppointments,
      label: "overdue appointments",
      icon: Calendar,
      color: "text-orange-600 bg-orange-100",
    },
    {
      count: items.criticalSideEffects,
      label: "critical side effects",
      icon: Activity,
      color: "text-amber-600 bg-amber-100",
    },
  ].filter(item => item.count > 0);

  return (
    <div className="bg-gradient-to-r from-red-50 to-orange-50 border-2 border-red-200 rounded-lg p-4 shadow-sm hover:shadow-lg hover:from-red-100 hover:to-orange-100 transition-all duration-200 cursor-pointer group">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="p-2 bg-red-100 rounded-full group-hover:bg-red-200 transition-colors">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-red-900">
              {items.totalUrgentItems} urgent {items.totalUrgentItems === 1 ? 'item' : 'items'} need your attention
            </h3>
            <div className="mt-3 flex flex-wrap gap-3">
              {urgentItemsList.map((item, index) => (
                <div key={index} className={`flex items-center space-x-2 px-3 py-1.5 rounded-full ${item.color} group-hover:shadow-sm transition-shadow`}>
                  <item.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {item.count} {item.label}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
        {onViewAll && (
          <button
            onClick={onViewAll}
            className="text-red-600 hover:text-red-800 text-sm font-medium whitespace-nowrap group-hover:translate-x-1 transition-transform"
          >
            View All →
          </button>
        )}
      </div>
    </div>
  );
};