import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { PatientActivityFeed } from "../PatientActivityFeed";
import { Activity } from "lucide-react";

export const PatientActivityCard: React.FC = () => {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Recent Patient Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] overflow-y-auto">
          <PatientActivityFeed limit={15} showHeader={false} />
        </div>
      </CardContent>
    </Card>
  );
};