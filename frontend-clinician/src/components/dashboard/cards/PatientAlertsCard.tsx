import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { BellRing, AlertCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import apiClient from "@/lib/apiClient";
import { formatChatTimestamp } from "@pulsetrack/shared-frontend";
import { Link } from "react-router-dom";

interface PatientAlert {
  id: string;
  patient_id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  alert_type: string;
  severity: "info" | "warning" | "critical";
  status: "New" | "Acknowledged" | "Resolved";
  message: string;
  created_at: string;
}

type PatientAlertsCardProps = Record<string, never>;

export const PatientAlertsCard: React.FC<PatientAlertsCardProps> = () => {
  const [alerts, setAlerts] = useState<PatientAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAlerts = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get<PatientAlert[]>("/dashboard/patient-alerts", {
          params: {
            status: "new",
            limit: 5,
          },
        });
        setAlerts(response.data);
      } catch (err: any) {
        console.error("Error fetching patient alerts:", err);
        setError(err.response?.data?.detail || "Failed to fetch alerts");
      } finally {
        setLoading(false);
      }
    };

    fetchAlerts();
  }, []);

  const getSeverityColor = (severity: PatientAlert["severity"]) => {
    switch (severity) {
      case "critical":
        return "destructive";
      case "warning":
        return "secondary";
      case "info":
      default:
        return "outline";
    }
  };

  const getSeverityIcon = (severity: PatientAlert["severity"]) => {
    switch (severity) {
      case "critical":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case "info":
      default:
        return <BellRing className="h-4 w-4 text-blue-600" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Patient Alerts</span>
            <BellRing className="w-5 h-5 text-muted-foreground" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Patient Alerts</span>
            <BellRing className="w-5 h-5 text-muted-foreground" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Patient Alerts</span>
          <BellRing className="w-5 h-5 text-muted-foreground" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <Alert variant="default" className="border-dashed">
            <AlertTitle className="text-muted-foreground">
              No Active Alerts
            </AlertTitle>
            <AlertDescription className="text-muted-foreground">
              The Patient Alerts feature is currently unavailable as the required
              backend service is not yet implemented.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-muted/50 transition-colors"
                >
                  <div className="mt-1">{getSeverityIcon(alert.severity)}</div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">
                        {alert.patient.first_name} {alert.patient.last_name}
                      </span>
                      <Badge variant={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{alert.message}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatChatTimestamp(alert.created_at)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-right">
              <Link
                to="/clinician/alerts"
                className="text-sm text-blue-600 hover:underline"
              >
                View All Alerts
              </Link>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};