import React, { useEffect, useState } from "react";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON>lipboardList, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import apiClient from "@/lib/apiClient";

interface DashboardTaskSummary {
  pending_medication_requests: number;
  pending_lab_results: number;
  unread_patient_messages: number;
  pending_appointment_requests: number; // Added field
}

interface Task {
  name: string;
  count: number;
  priority: boolean;
  isLoading?: boolean;
  path?: string; // For navigation
  clickable: boolean;
}

const PendingTasksCard: React.FC = () => {
  const navigate = useNavigate();
  const [taskSummary, setTaskSummary] = useState<DashboardTaskSummary | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTaskSummary = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiClient.get<DashboardTaskSummary>(
          "/dashboard/task-summary",
        );
        setTaskSummary(response.data);
      } catch (err) {
        console.error("Failed to fetch task summary:", err);
        setError("Failed to load task summary.");
        setTaskSummary(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTaskSummary();
  }, []);

  const tasks: Task[] = [
    {
      name: "Medication Requests",
      count: taskSummary?.pending_medication_requests ?? 0,
      priority: (taskSummary?.pending_medication_requests ?? 0) > 0,
      isLoading: isLoading,
      path: "/clinician/medication-requests", // Corrected path
      clickable: true,
    },
    // Lab Results Review removed as labs are not implemented in the app
    {
      name: "Patient Messages",
      count: taskSummary?.unread_patient_messages ?? 0,
      priority: (taskSummary?.unread_patient_messages ?? 0) > 0,
      isLoading: isLoading,
      path: "/clinician/chat",
      clickable: true,
    },
    {
      name: "Appointment Requests",
      count: taskSummary?.pending_appointment_requests ?? 0,
      priority: (taskSummary?.pending_appointment_requests ?? 0) > 0,
      isLoading: isLoading,
      path: "/clinician/appointments",
      clickable: true,
    },
  ];

  const handleItemClick = (path?: string) => {
    console.log('PendingTasksCard: Attempting to navigate to:', path);
    if (path) {
      try {
        navigate(path);
        console.log('PendingTasksCard: Navigation successful to:', path);
      } catch (error) {
        console.error('PendingTasksCard: Navigation failed:', error);
      }
    } else {
      console.warn('PendingTasksCard: No path provided for navigation');
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-medium">Pending Tasks</CardTitle>
        <ClipboardList className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {error && !isLoading && (
          <p className="text-sm text-red-500 mb-2">{error}</p>
        )}
        <ul className="space-y-2">
          {tasks.map((task, index) => {
            const TaskContent = () => (
              <div className="flex justify-between items-center text-sm w-full">
                <span className="flex items-center">
                  {task.priority && !task.isLoading && task.count > 0 && (
                    <span
                      className="mr-2 h-2 w-2 rounded-full bg-red-500"
                      aria-label="High priority"
                    ></span>
                  )}
                  {task.name}
                </span>
                <span className="flex items-center gap-2">
                  {task.isLoading ? (
                    <Badge variant="outline">Loading...</Badge>
                  ) : (
                    <Badge variant={task.count > 0 ? "default" : "secondary"}>
                      {task.count}
                    </Badge>
                  )}
                  {task.clickable && !task.isLoading && (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </span>
              </div>
            );

            return (
              <li key={index} className="block">
                {task.clickable && !task.isLoading && task.path ? (
                  <Link
                    to={task.path}
                    className={`flex justify-between items-center text-sm p-2 -m-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors no-underline text-inherit ${task.count === 0 ? "opacity-60" : ""}`}
                    aria-label={`Go to ${task.name} (${task.count} pending)`}
                    title={`Click to view ${task.name.toLowerCase()}`}
                  >
                    <TaskContent />
                  </Link>
                ) : (
                  <div className={`flex justify-between items-center text-sm p-2 -m-2 ${task.count === 0 && !task.isLoading ? "opacity-60" : ""}`}>
                    <TaskContent />
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      </CardContent>
    </Card>
  );
};

export default PendingTasksCard;
