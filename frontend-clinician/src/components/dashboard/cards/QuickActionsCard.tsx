import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Zap, UserPlus, KeyRound, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const QuickActionsCard: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Zap className="w-5 h-5" />
          <span>Quick Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-2">
          <Button variant="default" size="sm" className="w-full justify-start">
            {" "}
            {/* Use default variant for primary action */}
            <UserPlus className="mr-2 h-4 w-4" /> + Add Patient
          </Button>
          <Button
            variant="secondary"
            size="sm"
            className="w-full justify-start"
          >
            <KeyRound className="mr-2 h-4 w-4" /> Generate Access Code
          </Button>
          <Button
            variant="secondary"
            size="sm"
            className="w-full justify-start"
          >
            <Upload className="mr-2 h-4 w-4" /> Upload Note
          </Button>
          {/* Add more actions as needed */}
        </div>
      </CardContent>
    </Card>
  );
};
