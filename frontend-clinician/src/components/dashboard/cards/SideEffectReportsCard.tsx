import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Loader2 } from "lucide-react"; // Added Loader2
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import apiClient from "@/lib/apiClient"; // Import the API client
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // Import Alert components
import { Link } from "react-router-dom";
import { AxiosError } from "axios";

// Using Record<string, never> to indicate an object with no properties
type SideEffectReportsCardProps = Record<string, never>;

// Update interface to reflect the structure from SideEffectReportResponseWithPatientDetails
interface SideEffectReportData {
  id: string; // UUID is a string
  severity: "minor" | "moderate" | "major"; // Use the actual enum values
  patient: {
    id: string; // Clerk User ID is string
    first_name: string;
    last_name: string;
  };
  description: string;
  status?: string; // Status field to help with sorting
  reported_at?: string; // Date field for secondary sorting
}

// Sort function to prioritize urgent/high severity and unresolved items
const sortReports = (reports: SideEffectReportData[]): SideEffectReportData[] => {
  // Define severity priorities (higher number = higher priority)
  const severityPriority: Record<string, number> = {
    major: 3,
    moderate: 2,
    minor: 1,
  };

  // Define status priorities (unresolved/new items get higher priority)
  const statusPriority: Record<string, number> = {
    Submitted: 3,
    Reviewing: 2,
    Reviewed: 1,
    Resolved: 0,
  };

  return [...reports].sort((a, b) => {
    // First, sort by severity (major -> moderate -> minor)
    const severityDiff = 
      (severityPriority[b.severity] || 0) - (severityPriority[a.severity] || 0);
    
    if (severityDiff !== 0) {
      return severityDiff;
    }

    // If severity is the same, sort by status (unresolved first)
    const statusDiff = 
      (statusPriority[b.status || "Submitted"] || 0) - 
      (statusPriority[a.status || "Submitted"] || 0);
    
    if (statusDiff !== 0) {
      return statusDiff;
    }

    // Finally, sort by date (most recent first)
    if (a.reported_at && b.reported_at) {
      return new Date(b.reported_at).getTime() - new Date(a.reported_at).getTime();
    }

    return 0;
  });
};

export const SideEffectReportsCard: React.FC<
  SideEffectReportsCardProps
> = () => {
  const [reports, setReports] = useState<SideEffectReportData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReports = async () => {
      setLoading(true);
      setError(null);
      try {
        // Remove the redundant /api/v1/ prefix. apiClient likely handles it.
        // Get unresolved reports sorted by severity (major first)
        const response =
          await apiClient.get<SideEffectReportData[]>("/side-effects/", {
            params: {
              report_status: "Submitted", // Only show open/unresolved reports
              limit: 10, // Fetch more to ensure we get the most urgent after sorting
            },
          });
        // Sort the reports to prioritize urgent items and take top 5
        const sortedReports = sortReports(response.data);
        setReports(sortedReports.slice(0, 5));
      } catch (err: unknown) {
        console.error("Error fetching side effect reports:", err);
        let errorMessage = "Failed to fetch reports.";
        if (err instanceof AxiosError) {
          const responseData = err.response?.data as Record<string, unknown>;
          if (responseData && typeof responseData.detail === "string") {
            errorMessage = responseData.detail;
          } else {
            errorMessage = err.message || errorMessage;
          }
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchReports();
  }, []);

  const getSeverityColor = (severity: SideEffectReportData["severity"]) => {
    switch (severity) {
      case "major": // Use actual enum values
        return "bg-red-500";
      case "moderate": // Use actual enum values
        return "bg-yellow-500";
      case "minor": // Use actual enum values
        return "bg-green-500";
      default:
        return "bg-gray-400";
    }
  };

  // Helper to get initials
  const getInitials = (firstName: string, lastName: string): string => {
    const firstInitial = firstName?.charAt(0) || "";
    const lastInitial = lastName?.charAt(0) || "";
    return `${firstInitial}${lastInitial}`.toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5" />
          <span>Side Effect Reports</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading && (
          <div className="flex justify-center items-center h-32">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              Loading reports...
            </span>
          </div>
        )}
        {error && (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {!loading && !error && (
          <>
            <div className="space-y-2 overflow-y-auto max-h-64 pr-2">
              {reports.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No side effect reports found.
                </p>
              ) : (
                reports.map((report) => (
                  <div key={report.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                    <span
                      className={`inline-block h-2.5 w-2.5 rounded-full flex-shrink-0 ${getSeverityColor(report.severity)}`}
                    ></span>
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      {/* Use helper to get initials from the nested patient object */}
                      <AvatarFallback className="text-xs">
                        {getInitials(
                          report.patient.first_name,
                          report.patient.last_name,
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                          report.severity === 'major' ? 'bg-red-100 text-red-700' :
                          report.severity === 'moderate' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {report.severity.toUpperCase()}
                        </span>
                        {report.status && report.status !== 'Submitted' && (
                          <span className="text-xs text-gray-500">
                            {report.status}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground truncate mt-1">
                        {report.description}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
            <div className="mt-4 text-right">
              <Link
                to="/clinician/side-effects"
                className="h-auto p-0 text-sm text-blue-600 hover:underline"
              >
                View All Side Effects
              </Link>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
