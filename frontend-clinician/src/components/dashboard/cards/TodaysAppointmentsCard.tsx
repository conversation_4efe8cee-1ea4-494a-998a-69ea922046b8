import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { CalendarDays, AlertCircle, Loader2 } from "lucide-react"; // Added AlertCircle and Loader2
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import apiClient from "@/lib/apiClient"; // Use default import
import { format, isToday, isTomorrow } from "date-fns"; // For formatting appointment time
import { Link } from "react-router-dom";
import { AxiosError } from "axios";
import { convertUTCToLocal } from "@/lib/dateUtils"; // Shared timezone conversion utility

// Define an interface for the appointment data structure
interface Appointment {
  id: string; // Assuming ID is a string from the backend
  appointment_datetime: string; // Changed from appointment_time to match backend
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  // Add other relevant fields if needed, e.g., appointment_type
}

// Using Record<string, never> to indicate an object with no properties
// clinicianId is no longer needed as the API uses the authenticated user
type TodaysAppointmentsCardProps = Record<string, never>;

export const TodaysAppointmentsCard: React.FC<
  TodaysAppointmentsCardProps
> = () => {
  // Removed clinicianId prop
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAppointments = async () => {
      // Removed clinicianId check as it's no longer needed

      setLoading(true);
      setError(null); // Clear previous errors

      try {
        // Fetch upcoming appointments for the authenticated clinician
        const response = await apiClient.get<Appointment[]>(
          `/appointments/upcoming`,
        );
        console.log("API Response:", response.data);
        setAppointments(response.data || []); // The API returns the array directly
      } catch (err: unknown) {
        console.error("Error fetching appointments:", err);
        let errorMessage = "Failed to fetch appointments.";
        if (err instanceof AxiosError) {
          const responseData = err.response?.data as Record<string, unknown>;
          if (responseData && typeof responseData.detail === "string") {
            errorMessage = responseData.detail;
          } else {
            errorMessage = err.message || errorMessage;
          }
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchAppointments();
  }, []); // Dependency array is now empty as clinicianId is removed

  const getInitials = (firstName: string, lastName: string): string => {
    const firstInitial = firstName ? firstName[0].toUpperCase() : "";
    const lastInitial = lastName ? lastName[0].toUpperCase() : "";
    return `${firstInitial}${lastInitial}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CalendarDays className="w-5 h-5" />
          <span>Upcoming Appointments</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 overflow-y-auto max-h-48 pr-2 min-h-[5rem] flex flex-col">
          {" "}
          {/* Added min-h and flex */}
          {loading ? (
            <div className="flex items-center justify-center flex-grow">
              <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center flex-grow text-destructive">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>{error}</span>
            </div>
          ) : appointments.length > 0 ? (
            appointments.map((appt) => {
              // Convert UTC datetime to local timezone
              const appointmentDate = convertUTCToLocal(appt.appointment_datetime);
              
              // Debug logging
              console.log("Appointment time conversion:", {
                original: appt.appointment_datetime,
                converted: appointmentDate.toString(),
                localTime: format(appointmentDate, "h:mm a"),
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
              });
              
              const dateDisplay = isToday(appointmentDate) 
                ? "Today" 
                : isTomorrow(appointmentDate) 
                ? "Tomorrow" 
                : format(appointmentDate, "MMM d");
              
              return (
                <div key={appt.id} className="flex items-center space-x-3">
                  <div className="text-sm text-right min-w-[80px]">
                    <div className="font-medium">{format(appointmentDate, "h:mm a")}</div>
                    <div className="text-xs text-muted-foreground">{dateDisplay}</div>
                  </div>
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-xs">
                      {getInitials(
                        appt.patient.first_name,
                        appt.patient.last_name,
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <p className="text-sm text-muted-foreground truncate">
                    {`${appt.patient.first_name} ${appt.patient.last_name}`}
                  </p>
                </div>
              );
            })
          ) : (
            <div className="flex items-center justify-center flex-grow text-muted-foreground">
              <span>No upcoming appointments scheduled.</span>
            </div>
          )}
        </div>
        <div className="mt-4 text-right">
          <Link
            to="/clinician/appointments"
            className="h-auto p-0 text-sm text-blue-600 hover:underline"
          >
            View All Appointments
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};
