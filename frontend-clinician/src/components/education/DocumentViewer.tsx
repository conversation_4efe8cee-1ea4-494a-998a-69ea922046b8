import React, { useState } from 'react';
import { X, Download, Maximize2, Minimize2, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import apiClient from '@/lib/apiClient';

interface DocumentViewerProps {
  materialId: string;
  title: string;
  onClose?: () => void;
  className?: string;
}

export default function DocumentViewer({ 
  materialId, 
  title, 
  onClose,
  className 
}: DocumentViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documentBlobUrl, setDocumentBlobUrl] = useState<string | null>(null);

  // Construct the secure document URL that goes through our backend
  const documentUrl = `/education-materials/${materialId}/content`;

  // Load document with authentication
  React.useEffect(() => {
    const loadDocument = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch document with authentication
        const response = await apiClient.get(documentUrl, {
          responseType: 'blob'
        });
        
        // Create blob URL for iframe
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        setDocumentBlobUrl(url);
        
      } catch (err) {
        console.error('Failed to load document:', err);
        setError('Failed to load document. Please try again.');
        setIsLoading(false);
      }
    };
    
    loadDocument();
    
    // Cleanup blob URL on unmount
    return () => {
      if (documentBlobUrl) {
        window.URL.revokeObjectURL(documentBlobUrl);
      }
    };
  }, [materialId]);

  const handleDownload = async () => {
    try {
      if (documentBlobUrl) {
        // Use existing blob URL for download
        const link = document.createElement('a');
        link.href = documentBlobUrl;
        link.download = title;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Fetch document if not already loaded
        const response = await apiClient.get(documentUrl, {
          responseType: 'blob'
        });
        
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = title;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        setTimeout(() => window.URL.revokeObjectURL(url), 100);
      }
    } catch (err) {
      console.error('Download error:', err);
      setError('Failed to download document');
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleError = () => {
    setIsLoading(false);
    setError('Failed to load document. Please try again.');
  };

  return (
    <div className={cn(
      "flex flex-col bg-white dark:bg-gray-900 rounded-lg shadow-lg",
      isFullscreen ? "fixed inset-0 z-50" : "relative",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
        <h3 className="text-lg font-semibold truncate">{title}</h3>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDownload}
            title="Download"
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
            title={isFullscreen ? "Exit fullscreen" : "Fullscreen"}
          >
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              title="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Document viewer */}
      <div className="flex-1 relative bg-gray-50 dark:bg-gray-800">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Retry
              </Button>
            </div>
          </div>
        )}

        {/* PDF viewer iframe */}
        {documentBlobUrl && (
          <iframe
            src={`${documentBlobUrl}#toolbar=0`}
            className={cn(
              "w-full h-full border-0",
              isLoading && "invisible"
            )}
            onLoad={handleLoad}
            onError={handleError}
            title={title}
          />
        )}
      </div>
    </div>
  );
}