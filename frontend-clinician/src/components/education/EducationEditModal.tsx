import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import apiClient from '@/lib/apiClient';

interface EducationEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  materialId: string;
  initialData: {
    title: string;
    description?: string;
    category?: string;
    is_public: boolean;
  };
}

const PRESET_CATEGORIES = [
  'Medication Information',
  'Disease Management',
  'Lifestyle & Diet',
  'Exercise & Physical Therapy',
  'Mental Health',
  'Preventive Care',
  'Treatment Guidelines',
  'Post-Procedure Care',
  'Other'
];

const EducationEditModal: React.FC<EducationEditModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  materialId,
  initialData
}) => {
  const [title, setTitle] = useState(initialData.title);
  const [description, setDescription] = useState(initialData.description || '');
  const [category, setCategory] = useState(initialData.category || '');
  const [isPublic, setIsPublic] = useState(initialData.is_public);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when initial data changes
  useEffect(() => {
    setTitle(initialData.title);
    setDescription(initialData.description || '');
    setCategory(initialData.category || '');
    setIsPublic(initialData.is_public);
    setError(null);
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Please enter a title');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      const updateData: any = {
        title: title.trim(),
        is_public: isPublic
      };

      // Only include optional fields if they have values
      if (description.trim()) {
        updateData.description = description.trim();
      }
      if (category && category !== '') {
        updateData.category = category;
      }

      const response = await apiClient.put(`/education-materials/${materialId}`, updateData);

      if (response.data) {
        onSuccess();
        onClose();
      }
    } catch (err: any) {
      console.error('Update error:', err.response?.data);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update material';
      setError(`Update failed: ${errorMessage}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Educational Material</DialogTitle>
          <DialogDescription>
            Update the details of this educational material.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a descriptive title"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of the content (optional)"
              rows={3}
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category || "uncategorized"} onValueChange={(value) => setCategory(value === "uncategorized" ? "" : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="uncategorized">No category</SelectItem>
                {PRESET_CATEGORIES.map((cat) => (
                  <SelectItem key={cat} value={cat}>
                    {cat}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Public/Private Toggle */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is-public"
              checked={isPublic}
              onCheckedChange={(checked) => setIsPublic(checked as boolean)}
            />
            <Label 
              htmlFor="is-public" 
              className="text-sm font-normal cursor-pointer"
            >
              Make this material publicly available to all clinics
            </Label>
          </div>

          {/* Info Alert */}
          {!isPublic && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This material will only be available to clinicians in your clinic.
              </AlertDescription>
            </Alert>
          )}

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EducationEditModal;