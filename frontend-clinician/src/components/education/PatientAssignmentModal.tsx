import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Users, Calendar, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import apiClient from '@/lib/apiClient';

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
}

interface ExistingAssignment {
  patient_id: string;
  material_id: string;
  status: string;
  assigned_at: string;
}

interface PatientAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  materialId: string;
  materialTitle: string;
  onSuccess?: () => void;
}

export default function PatientAssignmentModal({
  isOpen,
  onClose,
  materialId,
  materialTitle,
  onSuccess
}: PatientAssignmentModalProps) {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [existingAssignments, setExistingAssignments] = useState<ExistingAssignment[]>([]);
  const [selectedPatientIds, setSelectedPatientIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [priority, setPriority] = useState('medium');
  const [dueDate, setDueDate] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch patients when modal opens
  useEffect(() => {
    if (isOpen && materialId) {
      fetchPatientsAndAssignments();
    }
  }, [isOpen, materialId]);

  const fetchPatientsAndAssignments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch patients and existing assignments in parallel
      const [patientsResponse, assignmentsResponse] = await Promise.all([
        apiClient.get('/clinicians/patients'),
        apiClient.get(`/patient-education/assignments?material_id=${materialId}`)
      ]);
      
      setPatients(patientsResponse.data.items || []);
      setExistingAssignments(assignmentsResponse.data || []);
      
    } catch (err: any) {
      console.error('Failed to fetch data:', err);
      setError('Failed to load patients. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const isPatientAssigned = (patientId: string) => {
    return existingAssignments.some(assignment => assignment.patient_id === patientId);
  };

  const handlePatientSelection = (patientId: string, checked: boolean) => {
    // Don't allow selection of already assigned patients
    if (isPatientAssigned(patientId)) {
      return;
    }
    
    if (checked) {
      setSelectedPatientIds([...selectedPatientIds, patientId]);
    } else {
      setSelectedPatientIds(selectedPatientIds.filter(id => id !== patientId));
    }
  };

  const handleSelectAll = () => {
    const filteredPatients = getFilteredPatients();
    // Only select patients that aren't already assigned
    const selectablePatients = filteredPatients.filter(p => !isPatientAssigned(p.id));
    
    if (selectedPatientIds.length === selectablePatients.length) {
      // Deselect all
      setSelectedPatientIds([]);
    } else {
      // Select all filtered patients that aren't already assigned
      setSelectedPatientIds(selectablePatients.map(p => p.id));
    }
  };

  const getFilteredPatients = () => {
    if (!searchTerm) return patients;
    
    const term = searchTerm.toLowerCase();
    return patients.filter(patient => 
      patient.first_name.toLowerCase().includes(term) ||
      patient.last_name.toLowerCase().includes(term) ||
      patient.email?.toLowerCase().includes(term)
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedPatientIds.length === 0) {
      setError('Please select at least one patient.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const response = await apiClient.post(
        `/education-materials/${materialId}/assign`,
        {
          patient_ids: selectedPatientIds,
          priority,
          due_date: dueDate || null,
          notes: notes || null
        }
      );

      const { successful_assignments, failed_assignments, summary } = response.data;

      if (summary.successful > 0) {
        setSuccess(
          `Successfully assigned "${materialTitle}" to ${summary.successful} patient(s).` +
          (summary.failed > 0 ? ` ${summary.failed} assignment(s) failed.` : '')
        );
        
        // Reset form
        setSelectedPatientIds([]);
        setDueDate('');
        setNotes('');
        
        if (onSuccess) {
          onSuccess();
        }
        
        // Close modal after showing success message briefly
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError(`Failed to assign material: ${failed_assignments[0]?.reason || 'Unknown error'}`);
      }

    } catch (err: any) {
      console.error('Assignment failed:', err);
      setError(err.response?.data?.detail || 'Failed to assign material. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state
    setSelectedPatientIds([]);
    setSearchTerm('');
    setPriority('medium');
    setDueDate('');
    setNotes('');
    setError(null);
    setSuccess(null);
    onClose();
  };

  const filteredPatients = getFilteredPatients();

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Assign Material to Patients
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Assign "{materialTitle}" to selected patients
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1 overflow-hidden">
          {/* Patient Selection */}
          <div className="space-y-4 flex-1 overflow-hidden">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search patients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Select All Button */}
            {filteredPatients.length > 0 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className="w-full"
              >
                {selectedPatientIds.length === filteredPatients.length 
                  ? 'Deselect All' 
                  : `Select All (${filteredPatients.length})`
                }
              </Button>
            )}

            {/* Patient List */}
            <div className="border rounded-lg max-h-64 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : filteredPatients.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  {patients.length === 0 ? 'No patients found' : 'No patients match your search'}
                </div>
              ) : (
                <div className="p-2 space-y-2">
                  {filteredPatients.map((patient) => {
                    const isAssigned = isPatientAssigned(patient.id);
                    return (
                      <div
                        key={patient.id}
                        className={`flex items-center space-x-3 p-2 rounded ${
                          isAssigned 
                            ? 'bg-gray-100 dark:bg-gray-800 opacity-60' 
                            : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                      >
                        <Checkbox
                          checked={isAssigned || selectedPatientIds.includes(patient.id)}
                          disabled={isAssigned}
                          onCheckedChange={(checked) => 
                            handlePatientSelection(patient.id, checked as boolean)
                          }
                        />
                        <div className="flex-1">
                          <div className="font-medium flex items-center gap-2">
                            {patient.first_name} {patient.last_name}
                            {isAssigned && (
                              <span className="text-xs text-muted-foreground">(Already assigned)</span>
                            )}
                          </div>
                          {patient.email && (
                            <div className="text-sm text-muted-foreground">
                              {patient.email}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Assignment Options */}
            <div className="grid grid-cols-2 gap-4">
              {/* Priority */}
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={priority} onValueChange={setPriority}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Due Date */}
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date (Optional)</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                />
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any additional notes for the assignment..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          {/* Status Messages */}
          {error && (
            <Alert className="mt-4">
              <AlertDescription className="text-red-600">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mt-4">
              <AlertDescription className="text-green-600">
                {success}
              </AlertDescription>
            </Alert>
          )}

          {/* Footer */}
          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || selectedPatientIds.length === 0}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Assigning...
                </>
              ) : (
                `Assign to ${selectedPatientIds.length} Patient${selectedPatientIds.length !== 1 ? 's' : ''}`
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}