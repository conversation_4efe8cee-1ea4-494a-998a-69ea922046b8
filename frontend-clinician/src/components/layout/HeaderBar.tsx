import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@pulsetrack/shared-frontend";
import { Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import apiClient from "@/lib/apiClient";
import { ThemeToggle } from "@/components/theme/ThemeToggle";

/**
 * HeaderBar Component
 *
 * Renders the main header bar for the Clinician Dashboard.
 * Provides a container for navigation and action elements, structured
 * to hold left, center, and right aligned content.
 */
const HeaderBar: React.FC = () => {
  const { signOut } = useAuth(); // Get signOut function from AuthProvider context
  const [urgentAlertCount, setUrgentAlertCount] = useState(0);

  const fetchUrgentAlertCount = useCallback(async () => {
    try {
      const response = await apiClient.get("/patient-alerts", {
        params: {
          status: "new",
          severity: "critical",
          limit: 1,
        },
      });
      setUrgentAlertCount(response.data.total || 0);
    } catch (error) {
      console.error("Error fetching urgent alert count:", error);
    }
  }, []);

  useEffect(() => {
    fetchUrgentAlertCount();
    
    // Set up an interval to refresh the count every 30 seconds
    const interval = setInterval(fetchUrgentAlertCount, 30000);
    
    // Listen for custom event when alerts are updated
    const handleAlertUpdate = () => {
      fetchUrgentAlertCount();
    };
    window.addEventListener('patientAlertUpdated', handleAlertUpdate);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('patientAlertUpdated', handleAlertUpdate);
    };
  }, [fetchUrgentAlertCount]);

  return (
    <header className="w-full bg-primary dark:bg-primary/90 text-primary-foreground px-6 py-4 shadow-md">
      <div className="flex justify-between items-center max-w-screen-xl mx-auto">
        {/* Left Section */}
        <span className="text-xl font-semibold">PulseTrack Clinician</span>

        {/* Right Section */}
        <div className="flex items-center gap-4">
          <Link
            to="/clinician/alerts"
            className="relative text-primary-foreground hover:text-primary-foreground/80 transition-colors"
          >
            <Bell className="h-6 w-6" />
            {urgentAlertCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
              >
                {urgentAlertCount}
              </Badge>
            )}
          </Link>
          <ThemeToggle />
          <button
            onClick={() => signOut()}
            className="text-primary-foreground/90 hover:text-primary-foreground transition-colors text-sm font-medium px-4 py-2 rounded-md hover:bg-primary-foreground/10"
          >
            Logout
          </button>
        </div>
      </div>
    </header>
  );
};

export default HeaderBar;
