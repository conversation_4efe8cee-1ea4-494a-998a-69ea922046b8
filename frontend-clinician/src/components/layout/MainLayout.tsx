import React from "react";
import { Outlet } from "react-router-dom"; // Import Outlet
import HeaderBar from "./HeaderBar"; // Import the new HeaderBar component
import ClinicianQuickAccessNavigation from "../navigation/ClinicianQuickAccessNavigation"; // Import the bottom navigation

// Remove MainLayoutProps interface and children prop
const MainLayout: React.FC = () => {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Use the new HeaderBar component */}
      <HeaderBar />

      <div className="flex flex-1">
        {/* Main Content Area */}
        {/* Main Content Area - Added pb-16 for bottom nav clearance */}
        <main className="flex-1 p-6 bg-gray-50 pb-16">
          <div className="max-w-screen-xl mx-auto">
            <Outlet /> {/* Render nested child routes here */}
          </div>
        </main>
      </div>

      {/* Persistent Bottom Navigation */}
      <ClinicianQuickAccessNavigation />
    </div>
  );
};

export default MainLayout;
