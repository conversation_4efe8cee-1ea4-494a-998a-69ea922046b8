import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
}

interface Medication {
  id: string;
  name: string;
  description?: string;
  dosage_guidelines?: string;
  common_side_effects?: string;
}

export interface MedicationRequestFormData {
  patient_id: string;
  medication_name: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  notes?: string;
}

interface MedicationRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: MedicationRequestFormData) => void;
  isSubmitting?: boolean;
  medicationRequest?: {
    id: string;
    patient_id: string;
    medication_name: string;
    dosage?: string;
    frequency?: string;
    duration?: string;
    notes?: string;
  };
}

// Constants for frequency options
const FREQUENCY_OPTIONS = [
  "Once daily",
  "Twice daily",
  "Three times daily",
  "Four times daily",
  "Every 4 hours",
  "Every 6 hours",
  "Every 8 hours",
  "Every 12 hours",
  "As needed",
  "Other",
];

export const MedicationRequestForm: React.FC<MedicationRequestFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false,
  medicationRequest,
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [medications, setMedications] = useState<Medication[]>([]);
  const [isLoadingPatients, setIsLoadingPatients] = useState(false);
  const [isLoadingMedications, setIsLoadingMedications] = useState(false);
  const [selectedMedication, setSelectedMedication] =
    useState<Medication | null>(null);

  const [formData, setFormData] = useState<MedicationRequestFormData>({
    patient_id: "",
    medication_name: "",
    dosage: "",
    frequency: "Once daily",
    duration: "",
    notes: "",
  });

  useEffect(() => {
    // Pre-fill form data if medication request data is provided (for editing)
    if (medicationRequest) {
      setFormData({
        patient_id: medicationRequest.patient_id,
        medication_name: medicationRequest.medication_name,
        dosage: medicationRequest.dosage || "",
        frequency: medicationRequest.frequency || "Once daily",
        duration: medicationRequest.duration || "",
        notes: medicationRequest.notes || "",
      });
    } else {
      // Reset form for new medication request
      setFormData({
        patient_id: "",
        medication_name: "",
        dosage: "",
        frequency: "Once daily",
        duration: "",
        notes: "",
      });
    }
  }, [medicationRequest]);

  useEffect(() => {
    // Fetch patients only when the dialog is open and it's for a new request
    // or if patients haven't been loaded yet
    if (isOpen && (!medicationRequest || patients.length === 0)) {
      const fetchPatients = async () => {
        setIsLoadingPatients(true);
        try {
          const response = await apiClient.get("/clinicians/patients", {
            params: {
              limit: 100,
            },
          });
          setPatients(response.data.items);
        } catch (error) {
          console.error("Failed to fetch patients:", error);
          toast.error("Failed to load patient list", {
            description:
              "Please try again or contact support if the problem persists.",
          });
        } finally {
          setIsLoadingPatients(false);
        }
      };
      fetchPatients();
    }
  }, [isOpen, medicationRequest, patients.length]);

  useEffect(() => {
    // Fetch medications when the dialog is open
    if (isOpen && medications.length === 0) {
      const fetchMedications = async () => {
        setIsLoadingMedications(true);
        try {
          const response = await apiClient.get("/medications", {
            params: {
              limit: 100,
            },
          });
          setMedications(response.data.items);
        } catch (error) {
          console.error("Failed to fetch medications:", error);
          toast.error("Failed to load medication list", {
            description:
              "Please try again or contact support if the problem persists.",
          });
        } finally {
          setIsLoadingMedications(false);
        }
      };
      fetchMedications();
    }
  }, [isOpen, medications.length]);

  // Update selected medication when medication name changes
  useEffect(() => {
    if (formData.medication_name) {
      const selected = medications.find(
        (med) => med.name === formData.medication_name,
      );
      if (selected) {
        setSelectedMedication(selected);

        // If we're creating a new request and dosage is empty,
        // auto-populate with medication guidelines
        if (
          !medicationRequest &&
          !formData.dosage &&
          selected.dosage_guidelines
        ) {
          setFormData((prev) => ({
            ...prev,
            dosage: selected.dosage_guidelines,
          }));
        }
      } else {
        setSelectedMedication(null);
      }
    } else {
      setSelectedMedication(null);
    }
  }, [
    formData.medication_name,
    medications,
    medicationRequest,
    formData.dosage,
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {medicationRequest
              ? "Edit Medication Request"
              : "New Medication Request"}
          </DialogTitle>
          <DialogDescription>
            {medicationRequest
              ? "Update the medication request details below."
              : "Fill in the details for the new medication request."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="patient">Patient</Label>
            <Select
              value={formData.patient_id}
              onValueChange={(value) =>
                setFormData({ ...formData, patient_id: value })
              }
              disabled={
                isLoadingPatients || isSubmitting || !!medicationRequest
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id}>
                    {`${patient.first_name} ${patient.last_name}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="medication">Medication</Label>
            <Select
              value={formData.medication_name}
              onValueChange={(value) =>
                setFormData({ ...formData, medication_name: value })
              }
              disabled={isLoadingMedications || isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a medication" />
              </SelectTrigger>
              <SelectContent>
                {medications
                  .filter((medication) => medication.name)
                  .map((medication) => (
                    <SelectItem key={medication.id} value={medication.name}>
                      {medication.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            {selectedMedication?.description && (
              <p className="text-sm text-gray-500 mt-1">
                {selectedMedication.description}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="dosage">Dosage</Label>
            <Input
              id="dosage"
              placeholder="e.g., 1 tablet, 10mg, etc."
              value={formData.dosage || ""}
              onChange={(e) =>
                setFormData({ ...formData, dosage: e.target.value })
              }
              disabled={isSubmitting}
            />
            {selectedMedication?.dosage_guidelines && (
              <p className="text-xs text-gray-500">
                Suggested: {selectedMedication.dosage_guidelines}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="frequency">Frequency</Label>
            <Select
              value={formData.frequency || ""}
              onValueChange={(value) =>
                setFormData({ ...formData, frequency: value })
              }
              disabled={isSubmitting}
            >
              <SelectTrigger id="frequency">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                {FREQUENCY_OPTIONS.map((frequency) => (
                  <SelectItem key={frequency} value={frequency}>
                    {frequency}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="duration">Duration</Label>
            <Input
              id="duration"
              placeholder="e.g., 7 days, 2 weeks, etc."
              value={formData.duration || ""}
              onChange={(e) =>
                setFormData({ ...formData, duration: e.target.value })
              }
              disabled={isSubmitting}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Additional instructions or notes"
              value={formData.notes || ""}
              onChange={(e) =>
                setFormData({ ...formData, notes: e.target.value })
              }
              disabled={isSubmitting}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Saving..."
                : medicationRequest
                  ? "Update"
                  : "Create"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
