import React from "react";
import { NavLink } from "react-router-dom";
import {
  LayoutDashboard,
  Users,
  CalendarDays,
  ClipboardList,
  AlertTriangle,
  MessageCircle,
  BookOpen,
  FileText,
  BarChart3,
} from "lucide-react";

interface NavItem {
  path: string;
  label: string;
  icon: React.ElementType;
}

// Chat is placed first to emphasize it as the primary interaction point
const navItems: NavItem[] = [
  { path: "/clinician/chat", label: "Chat", icon: MessageCircle },
  { path: "/clinician/dashboard", label: "Dashboard", icon: LayoutDashboard },
  { path: "/clinician/population-health", label: "Analytics", icon: BarChart3 },
  { path: "/clinician/patients", label: "Patients", icon: Users },
  { path: "/clinician/appointments", label: "Appts", icon: CalendarDays },
  {
    path: "/clinician/medication-requests",
    label: "Meds",
    icon: ClipboardList,
  },
  { path: "/clinician/side-effects", label: "Effects", icon: <PERSON><PERSON><PERSON>rian<PERSON> },
  { path: "/clinician/clinical-notes", label: "Notes", icon: FileText },
  { path: "/clinician/education", label: "Education", icon: BookOpen },
];

const ClinicianQuickAccessNavigation: React.FC = () => {
  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-background border-t border-border shadow-md z-50">
      <div className="flex justify-around items-center h-full w-full max-w-3xl mx-auto px-4">
        {navItems.map((item) => {
          const isChat = item.path === "/clinician/chat";
          
          return (
            <NavLink
              key={item.path}
              to={item.path}
              end={item.path === "/clinician/dashboard"}
              className={({ isActive }) =>
                `flex flex-col items-center justify-center p-2 rounded-md transition-all duration-150 ease-in-out ${
                  isActive
                    ? isChat
                      ? "text-white bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg scale-110"
                      : "text-indigo-600 bg-indigo-50"
                    : isChat
                      ? "text-purple-700 bg-gradient-to-br from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100"
                      : "text-muted-foreground hover:text-primary hover:bg-primary/5"
                } focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 ${
                  isChat ? 'relative' : ''
                }`
              }
            >
              {({ isActive }) => (
                <>
                  {isChat && !isActive && (
                    <div className="absolute -top-1 -right-1 h-2.5 w-2.5 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full animate-pulse shadow-sm" />
                  )}
                  <item.icon 
                    className={`${isChat ? 'w-6 h-6' : 'w-5 h-5'} mb-0.5`} 
                    strokeWidth={isChat ? 2 : 1.5} 
                  />
                  <span className={`text-[10px] leading-tight font-medium text-center w-12 ${
                    isChat && isActive ? 'font-bold' : ''
                  }`}>
                    {item.label}
                  </span>
                </>
              )}
            </NavLink>
          );
        })}
      </div>
    </nav>
  );
};

export default ClinicianQuickAccessNavigation;
