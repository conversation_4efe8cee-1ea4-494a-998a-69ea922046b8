import React, { useState } from 'react';
import { format } from 'date-fns';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/simple-alert-dialog';
import { Edit, Trash2, Save, X, FileText, User, Calendar } from 'lucide-react';
import apiClient from '@/lib/apiClient';

interface GeneralNote {
  id: string;
  patient_id: string;
  clinician_id: string;
  title?: string;
  content: string;
  note_type?: string;
  created_at: string;
  updated_at: string;
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  clinician?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

interface GeneralNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  note: GeneralNote;
  onUpdate?: (updatedNote: GeneralNote) => void;
  onDelete?: (noteId: string) => void;
}

const GeneralNoteModal: React.FC<GeneralNoteModalProps> = ({
  isOpen,
  onClose,
  note,
  onUpdate,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editForm, setEditForm] = useState({
    title: note.title || '',
    content: note.content || '',
    note_type: note.note_type || 'general',
  });

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm({
      title: note.title || '',
      content: note.content || '',
      note_type: note.note_type || 'general',
    });
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm({
      title: note.title || '',
      content: note.content || '',
      note_type: note.note_type || 'general',
    });
  };

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      
      const updateData = {
        title: editForm.title.trim() || null,
        content: editForm.content.trim(),
        note_type: editForm.note_type,
      };

      const response = await apiClient.put(`/notes/${note.id}`, updateData);
      
      if (onUpdate) {
        onUpdate(response.data);
      }
      
      setIsEditing(false);
      // Note: We don't close the modal here so user can see the updated note
    } catch (error) {
      console.error('Error updating note:', error);
      const errorMessage = (error as any).response?.data?.detail || 'Failed to update note';
      alert(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!showDeleteDialog) {
      setShowDeleteDialog(true);
      return;
    }

    try {
      setIsDeleting(true);
      
      await apiClient.delete(`/notes/${note.id}`);
      
      if (onDelete) {
        onDelete(note.id);
      }
      
      // Modal will be closed by parent component
    } catch (error) {
      console.error('Error deleting note:', error);
      const errorMessage = (error as any).response?.data?.detail || 'Failed to delete note';
      alert(errorMessage);
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const getFormattedPatientName = () => {
    if (note.patient) {
      return `${note.patient.first_name} ${note.patient.last_name}`;
    }
    return 'Unknown Patient';
  };

  const getFormattedClinicianName = () => {
    if (note.clinician) {
      return `${note.clinician.first_name} ${note.clinician.last_name}`;
    }
    return 'Unknown Clinician';
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              {isEditing ? 'Edit General Note' : 'General Note Details'}
            </DialogTitle>
            <DialogDescription>
              {isEditing ? 'Make changes to the note details below.' : 'View note details and make edits if needed.'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Note Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Patient</p>
                  <p className="text-sm text-gray-600">{getFormattedPatientName()}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Created</p>
                  <p className="text-sm text-gray-600">
                    {format(new Date(note.created_at), 'MMM d, yyyy h:mm a')}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Author</p>
                  <p className="text-sm text-gray-600">{getFormattedClinicianName()}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div>
                  <p className="text-sm font-medium text-gray-900">Type</p>
                  <Badge variant="outline" className="mt-1">
                    {note.note_type || 'General'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Note Content */}
            <div className="space-y-4">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                {isEditing ? (
                  <Input
                    value={editForm.title}
                    onChange={(e) => setEditForm({ ...editForm, title: e.target.value })}
                    placeholder="Enter note title (optional)"
                    className="w-full"
                  />
                ) : (
                  <p className="text-gray-900 font-medium">
                    {note.title || 'Untitled Note'}
                  </p>
                )}
              </div>

              {/* Note Type */}
              {isEditing && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <Select
                    value={editForm.note_type}
                    onValueChange={(value) => setEditForm({ ...editForm, note_type: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select note type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="consultation">Consultation</SelectItem>
                      <SelectItem value="follow_up">Follow Up</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Content
                </label>
                {isEditing ? (
                  <Textarea
                    value={editForm.content}
                    onChange={(e) => setEditForm({ ...editForm, content: e.target.value })}
                    placeholder="Enter note content"
                    rows={8}
                    className="w-full"
                    required
                  />
                ) : (
                  <div className="p-3 bg-white border border-gray-200 rounded-md min-h-[200px] whitespace-pre-wrap">
                    {note.content}
                  </div>
                )}
              </div>
            </div>

            {/* Last Updated */}
            {note.updated_at !== note.created_at && (
              <div className="text-xs text-gray-500 border-t pt-3">
                Last updated: {format(new Date(note.updated_at), 'MMM d, yyyy h:mm a')}
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              {/* Delete button - shown for all notes */}
              {!isEditing && (
                <Button
                  variant={showDeleteDialog ? "destructive" : "ghost"}
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className={showDeleteDialog ? "" : "text-red-600 hover:text-red-800 hover:bg-red-50"}
                >
                  {showDeleteDialog ? (
                    <>
                      {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </>
                  )}
                </Button>
              )}
              
              {showDeleteDialog && (
                <Button 
                  variant="ghost" 
                  onClick={() => setShowDeleteDialog(false)}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
              )}
            </div>
            
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isUpdating}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isUpdating || !editForm.content.trim()}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isUpdating ? 'Saving...' : 'Save Changes'}
                  </Button>
                </>
              ) : (
                <>
                  {!showDeleteDialog && (
                    <Button variant="outline" onClick={onClose}>
                      Close
                    </Button>
                  )}
                  <Button onClick={handleEdit}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                </>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </>
  );
};

export default GeneralNoteModal;