import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { AxiosError } from "axios";

interface Patient {
  id: string; // This is the Clerk user ID that we'll use in API requests
  first_name: string;
  last_name: string;
}

interface AddSideEffectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  fixedPatient?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export const AddSideEffectModal: React.FC<AddSideEffectModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  fixedPatient,
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<string>(
    fixedPatient ? fixedPatient.id : "",
  );
  const [description, setDescription] = useState<string>("");
  const [severity, setSeverity] = useState<string>("");

  // Fetch patients list
  useEffect(() => {
    if (fixedPatient) {
      setSelectedPatient(fixedPatient.id);
      setPatients([fixedPatient]);
      return;
    }
    const fetchPatients = async () => {
      if (!isOpen) return;

      setIsLoading(true);
      try {
        const response = await apiClient.get("/clinicians/patients");
        const patientsList = response.data?.items || [];
        setPatients(patientsList);
      } catch (err) {
        console.error("Error fetching patients:", err);
        toast.error("Failed to load patients list");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPatients();
  }, [isOpen, fixedPatient]);

  const resetForm = () => {
    setSelectedPatient("");
    setDescription("");
    setSeverity("");
    setIsSubmitting(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handlePatientSelect = (patientId: string) => {
    setSelectedPatient(patientId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPatient || !description || !severity) {
      toast.error("Please fill all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      // Log what we're sending
      console.log("Sending side effect report:", {
        patient_id: selectedPatient,
        description,
        severity,
      });

      // Make the API call with the patient Clerk ID in the URL
      const result = await apiClient.post(`/side-effects/${selectedPatient}`, {
        description,
        severity,
      });

      console.log("Success response:", result);
      toast.success("Side effect report added successfully");
      handleClose();
      onSuccess(); // Refresh the side effects list
    } catch (err: unknown) {
      console.error("Error creating side effect report:", err);

      // Show the detailed validation error
      if (err && typeof err === "object" && "response" in err) {
        const axiosError = err as AxiosError<{ detail?: string }>;
        console.error("Error response status:", axiosError.response?.status);
        console.error("Error response data:", axiosError.response?.data);

        // Extract the actual validation error message
        const errorDetail = axiosError.response?.data?.detail;
        if (errorDetail) {
          console.error("Validation error details:", errorDetail);
          toast.error(`Validation error: ${errorDetail}`);
        } else {
          toast.error("Failed to create side effect report");
        }
      } else {
        toast.error("Failed to create side effect report");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Side Effect Report</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-4">
          <div className="space-y-2">
            <Label htmlFor="patient">Patient</Label>
            <Select
              value={selectedPatient}
              onValueChange={handlePatientSelect}
              disabled={!!fixedPatient || isLoading || isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id}>
                    {patient.first_name} {patient.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="severity">Severity</Label>
            <Select
              value={severity}
              onValueChange={setSeverity}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select severity level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minor">Minor</SelectItem>
                <SelectItem value="moderate">Moderate</SelectItem>
                <SelectItem value="major">Major</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the side effect in detail..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={5}
              disabled={isSubmitting}
              required
            />
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              {isSubmitting ? "Adding..." : "Add Side Effect"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
