import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format, parseISO, isValid } from "date-fns";
import { AlertCircle, CheckCircle2, Clock, Pencil, Trash2 } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
}

interface SideEffectReport {
  id: string;
  patient: Patient;
  description: string;
  severity: "minor" | "moderate" | "major";
  status: string;
  reported_at: string;
}

interface SideEffectDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  sideEffect: SideEffectReport;
  onStatusUpdate: (reportId: string, newStatus: string) => Promise<void>;
  onDelete: (reportId: string) => void;
  onEdit: (updated: SideEffectReport) => void;
}

export const SideEffectDetailModal: React.FC<SideEffectDetailModalProps> = ({
  isOpen,
  onClose,
  sideEffect,
  onStatusUpdate,
  onDelete,
  onEdit,
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const getSeverityBadgeVariant = (severity: string) => {
    switch (severity) {
      case "minor":
        return "outline";
      case "moderate":
        return "default";
      case "major":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const handleMarkAsReviewed = async () => {
    setIsUpdating(true);
    try {
      await onStatusUpdate(sideEffect.id, "Reviewed");
      toast.success("Side effect report marked as reviewed");
    } catch (error: unknown) {
      console.error("Error updating status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleMarkAsResolved = async () => {
    setIsUpdating(true);
    try {
      await onStatusUpdate(sideEffect.id, "Resolved");
      toast.success("Side effect report marked as resolved");
    } finally {
      setIsUpdating(false);
    }
  };

  // DELETE logic
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await apiClient.delete(`/side-effects/${sideEffect.id}`);
      toast.success("Side effect report deleted");
      setShowDeleteConfirm(false);
      onDelete(sideEffect.id);
      onClose();
    } catch {
      toast.error("Failed to delete side effect report");
      setShowDeleteConfirm(false);
    } finally {
      setIsDeleting(false);
    }
  };

  // Function removed: handleEditSubmit was defined but never used
  // It was replaced by the EditSideEffectModal component's handleSubmit function

  // Safe date formatting function (similar to SideEffectsPage)
  const safeFormatDate = (dateString: string | undefined | null): string => {
    if (!dateString) {
      return "Date not available";
    }
    try {
      const date = parseISO(dateString);
      if (isValid(date)) {
        return format(date, "PPp"); // e.g., "Dec 25, 2023, 4:30:00 PM"
      } else {
        console.warn("Received invalid date string:", dateString);
        return "Invalid Date";
      }
    } catch (error: unknown) {
      console.error("Error parsing date string:", dateString, error);
      return "Error formatting date";
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Side Effect Report Details</DialogTitle>
            <DialogDescription>
              Reported on {safeFormatDate(sideEffect.reported_at)}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Patient Info */}
            <div>
              <span className="font-semibold">Patient:</span>{" "}
              {sideEffect.patient.first_name} {sideEffect.patient.last_name}
            </div>

            {/* Severity */}
            <div>
              <span className="font-semibold">Severity:</span>{" "}
              <Badge variant={getSeverityBadgeVariant(sideEffect.severity)}>
                {sideEffect.severity}
              </Badge>
            </div>

            {/* Status */}
            <div>
              <span className="font-semibold">Status:</span>{" "}
              <Badge
                variant={
                  sideEffect.status === "Submitted"
                    ? "outline"
                    : sideEffect.status === "Reviewed"
                      ? "secondary"
                      : "default"
                }
              >
                {sideEffect.status}
              </Badge>
            </div>

            {/* Description */}
            <div>
              <span className="font-semibold">Description:</span>
              <p className="mt-1 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                {sideEffect.description}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap justify-end gap-2 pt-4">
              {sideEffect.status === "Submitted" && (
                <Button
                  variant="outline"
                  onClick={handleMarkAsReviewed}
                  disabled={isUpdating}
                  className="flex items-center"
                >
                  {isUpdating ? (
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <AlertCircle className="h-4 w-4 mr-2" />
                  )}
                  Mark as Reviewed
                </Button>
              )}

              {(sideEffect.status === "Submitted" ||
                sideEffect.status === "Reviewed") && (
                <Button
                  variant="default"
                  onClick={handleMarkAsResolved}
                  disabled={isUpdating}
                  className="flex items-center"
                >
                  {isUpdating ? (
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                  )}
                  Mark as Resolved
                </Button>
              )}

              {/* Edit Button */}
              <Button
                variant="secondary"
                onClick={() => setShowEditModal(true)}
                disabled={isUpdating}
                className="flex items-center"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Edit
              </Button>

              {/* Delete Button */}
              <Button
                variant="destructive"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={isUpdating || isDeleting}
                className="flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>

            {/* Delete Confirmation Dialog */}
            {showDeleteConfirm && (
              <div className="w-full flex flex-col items-end space-y-2 mt-2">
                <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-md p-3 text-sm text-red-700 dark:text-red-200">
                  Are you sure you want to delete this side effect report? This
                  action cannot be undone.
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteConfirm(false)}
                    disabled={isDeleting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    {isDeleting ? "Deleting..." : "Confirm Delete"}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Modal (for editing side effect report) */}
      {showEditModal && (
        <EditSideEffectModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSuccess={(updatedReport) => {
            onEdit(updatedReport);
            setShowEditModal(false);
            onClose();
          }}
          sideEffect={sideEffect}
        />
      )}
    </>
  );
};

// Inline EditSideEffectModal component
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";

interface EditSideEffectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedReport: SideEffectReport) => void;
  sideEffect: SideEffectReport;
}

const EditSideEffectModal: React.FC<EditSideEffectModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  sideEffect,
}) => {
  const [description, setDescription] = useState<string>(
    sideEffect.description,
  );
  const [severity, setSeverity] = useState<string>(sideEffect.severity);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description || !severity) {
      toast.error("Please fill all required fields");
      return;
    }
    setIsSubmitting(true);
    try {
      const response = await apiClient.put(`/side-effects/${sideEffect.id}`, {
        description,
        severity,
      });
      toast.success("Side effect report updated");
      onSuccess(response.data);
    } catch {
      toast.error("Failed to update side effect report");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Side Effect Report</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-4">
          <div className="space-y-2">
            <Label htmlFor="patient">Patient</Label>
            <div className="border rounded px-3 py-2 bg-gray-50 dark:bg-gray-800">
              {sideEffect.patient.first_name} {sideEffect.patient.last_name}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="severity">Severity</Label>
            <Select
              value={severity}
              onValueChange={setSeverity}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select severity level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minor">Minor</SelectItem>
                <SelectItem value="moderate">Moderate</SelectItem>
                <SelectItem value="major">Major</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the side effect in detail..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={5}
              disabled={isSubmitting}
              required
            />
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
