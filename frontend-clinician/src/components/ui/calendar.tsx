import * as React from "react";
import ReactCalendar from "react-calendar";
import "react-calendar/dist/Calendar.css";

import { cn } from "@/lib/utils";

type CalendarProps = React.ComponentProps<typeof ReactCalendar>;

function Calendar({ className, ...props }: CalendarProps) {
  return (
    <ReactCalendar
      className={cn("react-calendar rounded-md border", className)}
      {...props}
    />
  );
}

export { Calendar };
