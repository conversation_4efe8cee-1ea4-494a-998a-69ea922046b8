import * as React from "react";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";

interface DateTimePickerProps {
  value?: string | Date;
  onChange: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
}

export function DateTimePicker({
  value,
  onChange,
  placeholder = "Pick a date and time",
  className,
  disabled = false,
  minDate,
  maxDate,
}: DateTimePickerProps) {
  const [open, setOpen] = React.useState(false);
  
  // Parse the value into a Date object
  const currentValue = React.useMemo(() => {
    if (!value) return null;
    const parsed = value instanceof Date ? value : new Date(value);
    return isNaN(parsed.getTime()) ? null : parsed;
  }, [value]);

  // Use a simple datetime-local input instead of the complex picker
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (newValue) {
      const date = new Date(newValue);
      onChange(date.toISOString());
    } else {
      onChange('');
    }
  };

  // Format value for datetime-local input
  const inputValue = currentValue 
    ? format(currentValue, "yyyy-MM-dd'T'HH:mm")
    : '';

  return (
    <Input
      type="datetime-local"
      value={inputValue}
      onChange={handleChange}
      className={className}
      disabled={disabled}
      min={minDate ? format(minDate, "yyyy-MM-dd'T'HH:mm") : undefined}
      max={maxDate ? format(maxDate, "yyyy-MM-dd'T'HH:mm") : undefined}
    />
  );
}