import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResizablePaneProps {
  children: React.ReactNode;
  defaultWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  storageKey?: string;
  className?: string;
  onWidthChange?: (width: number) => void;
}

export const ResizablePane: React.FC<ResizablePaneProps> = ({
  children,
  defaultWidth = 300,
  minWidth = 250,
  maxWidth = 500,
  storageKey,
  className,
  onWidthChange,
}) => {
  // Load width from localStorage if storageKey is provided
  const getInitialWidth = useCallback(() => {
    if (storageKey && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(storageKey);
        if (saved) {
          const parsed = parseInt(saved, 10);
          if (!isNaN(parsed) && parsed >= minWidth && parsed <= maxWidth) {
            return parsed;
          }
        }
      } catch (error) {
        console.error('Error loading width from localStorage:', error);
      }
    }
    return defaultWidth;
  }, [storageKey, defaultWidth, minWidth, maxWidth]);

  const [width, setWidth] = useState(getInitialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const paneRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);

  // Save width to localStorage
  const saveWidth = useCallback((newWidth: number) => {
    if (storageKey && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, newWidth.toString());
      } catch (error) {
        console.error('Error saving width to localStorage:', error);
      }
    }
  }, [storageKey]);

  // Handle mouse down on resize handle
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = width;
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [width]);

  // Handle mouse move during resize
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - startXRef.current;
    const newWidth = Math.max(
      minWidth,
      Math.min(maxWidth, startWidthRef.current + deltaX)
    );

    setWidth(newWidth);
    onWidthChange?.(newWidth);
  }, [isResizing, minWidth, maxWidth, onWidthChange]);

  // Handle mouse up to stop resizing
  const handleMouseUp = useCallback(() => {
    if (!isResizing) return;

    setIsResizing(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    saveWidth(width);
  }, [isResizing, width, saveWidth]);

  // Add/remove event listeners for mouse events
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Handle touch events for mobile support
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.touches[0].clientX;
    startWidthRef.current = width;
  }, [width]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isResizing) return;

    const deltaX = e.touches[0].clientX - startXRef.current;
    const newWidth = Math.max(
      minWidth,
      Math.min(maxWidth, startWidthRef.current + deltaX)
    );

    setWidth(newWidth);
    onWidthChange?.(newWidth);
  }, [isResizing, minWidth, maxWidth, onWidthChange]);

  const handleTouchEnd = useCallback(() => {
    if (!isResizing) return;

    setIsResizing(false);
    saveWidth(width);
  }, [isResizing, width, saveWidth]);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);
      return () => {
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isResizing, handleTouchMove, handleTouchEnd]);

  // Handle window resize for mobile detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on mount
    checkIsMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div className="flex">
      <div
        ref={paneRef}
        className={cn('flex-shrink-0 relative', className)}
        style={{ 
          width: isMobile ? '100%' : `${width}px`,
          minWidth: isMobile ? '100%' : `${minWidth}px`,
          maxWidth: isMobile ? '100%' : `${maxWidth}px`
        }}
      >
        {children}
        
        {/* Resize handle - hidden on mobile */}
        <div
          className={cn(
            'absolute top-0 right-0 w-1 h-full cursor-col-resize bg-transparent hover:bg-blue-400 transition-colors duration-200 group',
            'before:absolute before:content-[""] before:w-3 before:h-full before:-translate-x-1 before:top-0',
            'hidden md:block', // Hide on mobile (screens smaller than 768px)
            isResizing && 'bg-blue-400'
          )}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          role="separator"
          aria-label="Resize conversation pane"
        >
          {/* Visual indicator for the resize handle */}
          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-12 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </div>
      </div>
    </div>
  );
};

export default ResizablePane;