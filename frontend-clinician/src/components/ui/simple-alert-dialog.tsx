import * as React from "react";
import { cn } from "@/lib/utils";

interface AlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

export function AlertDialog({ open, onOpenChange, children }: AlertDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={() => onOpenChange(false)}
      />
      
      {/* Content */}
      <div className="relative z-50">
        {children}
      </div>
    </div>
  );
}

export function AlertDialogContent({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string 
}) {
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-lg p-6 max-w-md w-full mx-4",
      "dark:bg-gray-800",
      className
    )}>
      {children}
    </div>
  );
}

export function AlertDialogHeader({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string 
}) {
  return (
    <div className={cn("mb-4", className)}>
      {children}
    </div>
  );
}

export function AlertDialogTitle({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string 
}) {
  return (
    <h2 className={cn(
      "text-lg font-semibold text-gray-900 dark:text-gray-100",
      className
    )}>
      {children}
    </h2>
  );
}

export function AlertDialogDescription({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string 
}) {
  return (
    <p className={cn(
      "text-sm text-gray-600 dark:text-gray-400 mt-2",
      className
    )}>
      {children}
    </p>
  );
}

export function AlertDialogFooter({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string 
}) {
  return (
    <div className={cn(
      "flex gap-2 justify-end mt-6",
      className
    )}>
      {children}
    </div>
  );
}

export function AlertDialogAction({ 
  children, 
  onClick,
  disabled,
  className 
}: { 
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md",
        "hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        className
      )}
    >
      {children}
    </button>
  );
}

export function AlertDialogCancel({ 
  children, 
  onClick,
  disabled,
  className 
}: { 
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md",
        "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500",
        "dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        className
      )}
    >
      {children}
    </button>
  );
}