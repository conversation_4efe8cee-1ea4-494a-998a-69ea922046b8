import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { AxiosError } from "axios";
import { convertToUTC, formatForDateInput } from "@pulsetrack/shared-frontend";

interface AddWeightLogModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  fixedPatient?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export const AddWeightLogModal: React.FC<AddWeightLogModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  fixedPatient,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<string>(
    fixedPatient ? fixedPatient.id : "",
  );
  const [weightKg, setWeightKg] = useState<string>("");
  const [logDate, setLogDate] = useState<string>(
    formatForDateInput(new Date()),
  ); // Default to today

  // Fetch patients list if needed
  useEffect(() => {
    if (fixedPatient) {
      setSelectedPatient(fixedPatient.id);
      return;
    }

    // This component is designed to be used with a fixed patient, but including this for completeness
    const fetchPatients = async () => {
      if (!isOpen) return;

      setIsLoading(true);
      try {
        const response = await apiClient.get("/clinicians/patients");
        console.log("Patients loaded:", response.data?.items?.length || 0);
      } catch (err) {
        console.error("Error fetching patients:", err);
        toast.error("Failed to load patients list");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPatients();
  }, [isOpen, fixedPatient]);

  const resetForm = () => {
    setSelectedPatient(fixedPatient ? fixedPatient.id : "");
    setWeightKg("");
    setLogDate(formatForDateInput(new Date()));
    setIsSubmitting(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPatient || !weightKg || !logDate) {
      toast.error("Please fill all required fields");
      return;
    }

    // Validate weight is a positive number
    const weight = parseFloat(weightKg);
    if (isNaN(weight) || weight <= 0) {
      toast.error("Weight must be a positive number");
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Adding weight log entry:", {
        patient_id: selectedPatient,
        weight_kg: weight,
        log_date: logDate,
      });

      // Make the API call to add weight log
      // Use the patient-specific endpoint pattern (apiClient handles /api/v1 base)
      const result = await apiClient.post(
        `/patients/${selectedPatient}/weight-logs`,
        {
          weight_kg: weight,
          log_date: convertToUTC(logDate + "T00:00:00"),
        },
      );

      console.log("Success response:", result);
      toast.success("Weight log entry added successfully");
      handleClose();
      onSuccess(); // Refresh the weight log list
    } catch (err: unknown) {
      console.error("Error creating weight log entry:", err);

      // Show detailed validation error if available
      if (err && typeof err === "object" && "response" in err) {
        const axiosError = err as AxiosError<{ detail?: string }>;
        console.error("Error response status:", axiosError.response?.status);
        console.error("Error response data:", axiosError.response?.data);

        const errorDetail = axiosError.response?.data?.detail;
        if (errorDetail) {
          console.error("Validation error details:", errorDetail);
          toast.error(`Validation error: ${errorDetail}`);
        } else {
          toast.error("Failed to create weight log entry");
        }
      } else {
        toast.error("Failed to create weight log entry");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Weight Log Entry</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-4">
          {fixedPatient && (
            <div className="space-y-2">
              <Label>Patient</Label>
              <div className="p-2 border rounded-md bg-gray-50">
                {fixedPatient.first_name} {fixedPatient.last_name}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="weight">Weight (kg)</Label>
            <Input
              id="weight"
              type="number"
              step="0.1"
              min="0"
              placeholder="Enter weight in kg"
              value={weightKg}
              onChange={(e) => setWeightKg(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="logDate">Log Date</Label>
            <Input
              id="logDate"
              type="date"
              value={logDate}
              onChange={(e) => setLogDate(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              {isSubmitting ? "Adding..." : "Add Weight Log"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
