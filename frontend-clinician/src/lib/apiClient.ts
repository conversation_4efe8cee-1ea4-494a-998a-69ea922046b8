import axios from "axios";
import { getSessionToken } from "./clerk";

// Ensure the base URL includes /api/v1
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8000/api/v1";

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    // Remove default Content-Type. Axios/Browser will set it correctly.
    // 'Content-Type': 'application/json',
  },
  withCredentials: true, // Important for CORS with credentials
});

// Lock mechanism for handling 401 errors
let isHandling401 = false;
let lastAuthFailureTime = 0; // Timestamp of the last 401 error
const AUTH_FAILURE_COOLDOWN = 2000; // 2 seconds cooldown period

// Helper function to reset the 401 handling flag after a delay
const resetAuthHandling = () => {
  setTimeout(() => {
    isHandling401 = false;
    console.log("Auth handling lock reset");
  }, 1000); // Reset after 1 second
};

// Add a request interceptor to attach the Clerk session token
apiClient.interceptors.request.use(
  async (config) => {
    // --- Cooldown Check ---
    const now = Date.now();
    if (now - lastAuthFailureTime < AUTH_FAILURE_COOLDOWN) {
      console.log(
        "Request blocked: still in auth failure cooldown period.",
        config.url,
      );
      return Promise.reject(
        new Error("Request cancelled - in auth failure cooldown"),
      );
    }

    // --- 401 Handling / Unauthenticated State Check ---
    // Block all requests if handling 401
    if (isHandling401) {
      console.log(`Request blocked (handling 401):`, config.url);
      return Promise.reject(
        new Error("Request cancelled - handling authentication failure"),
      );
    }

    // --- Token Attachment ---
    try {
      // Get the Clerk session token using our utility function
      const token = await getSessionToken();

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log("Clerk token attached to request:", config.url); // For debugging
        
        // Decode token to check role (for debugging)
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            console.log("Token payload role:", payload.public_metadata?.role || payload.role || 'NO ROLE FOUND');
            console.log("Full token payload:", payload);
          }
        } catch (e) {
          console.error("Failed to decode token for debugging:", e);
        }
      } else {
        // If no Clerk token was found, log it.
        // The request will proceed without the Authorization header.
        // Public routes are allowed. Protected routes will likely get a 401 response,
        // which is handled by the response interceptor.
        console.log(
          "No Clerk authentication token found, proceeding without Authorization header:",
          config.url,
        );

        // Optional: Check if it's a public endpoint and allow explicitly
        // if (config.url?.includes('/auth/') || config.url?.includes('/content/')) {
        //   // Allow public endpoints even without token
        // } else {
        //   // Potentially reject or handle non-public endpoints without token differently if needed
        // }
      }
    } catch (error) {
      console.error("Error getting Clerk token:", error);
      // Continue with the request even if token retrieval fails,
      // allowing backend to handle auth if necessary.
    }
    // Ensure config is always returned from the success handler
    return config;
  },
  (error: unknown) => {
    // Do something with request error
    console.error("Error in request interceptor:", error);
    return Promise.reject(error);
  },
);

// Optional: Add a response interceptor for handling common errors (e.g., 401 Unauthorized)
apiClient.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    return response;
  },
  (error: unknown) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Safely log error details
    if (error && typeof error === "object") {
      if ("response" in error) {
        console.error("Error in response interceptor:", error.response);
      } else if ("message" in error && typeof error.message === "string") {
        console.error("Error in response interceptor:", error.message);
      }
    } else {
      console.error("Error in response interceptor:", error);
    }

    // Type guard to check if error is an AxiosError with response property
    if (
      error &&
      typeof error === "object" &&
      "response" in error &&
      error.response &&
      typeof error.response === "object" &&
      "status" in error.response &&
      error.response.status === 401
    ) {
      // Prevent multiple simultaneous 401 handlers
      if (isHandling401) {
        console.warn("Already handling a 401 error, ignoring subsequent ones.");
        return Promise.reject(error);
      }

      isHandling401 = true;
      lastAuthFailureTime = Date.now(); // Update timestamp for cooldown
      console.warn("Unauthorized request (401). Redirecting to login.");

      // Redirect with visual indicator and slight delay
      if (window.location.pathname !== "/login") {
        // Add visual indicator that a redirect is happening
        document.body.style.opacity = "0.5"; // Dim the screen slightly

        // Force a small delay to let React finish current rendering cycle before navigation
        setTimeout(() => {
          window.location.href = "/login";
        }, 50); // 50ms delay

        // Reset the flag after a longer delay (allows redirect to fully initiate)
        resetAuthHandling();
      } else {
        // If already on login page, just reset the flag after delay
        resetAuthHandling();
      }
    }

    return Promise.reject(error); // Ensure error is still rejected outside the 401 block
  },
);

// Analytics API methods
export const analyticsApi = {
  getPopulationDashboard: () => apiClient.get('/analytics/population-dashboard'),
  getPopulationOverview: () => apiClient.get('/analytics/population-overview'),
  getRiskStratification: () => apiClient.get('/analytics/risk-stratification'),
  getTreatmentAnalytics: () => apiClient.get('/analytics/treatment-analytics'),
  getPredictiveAnalytics: () => apiClient.get('/analytics/predictive-analytics'),
  getOptimizationInsights: () => apiClient.get('/analytics/optimization-insights'),
};

export default apiClient;
