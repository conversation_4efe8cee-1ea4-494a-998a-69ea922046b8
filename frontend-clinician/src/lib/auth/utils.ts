import { useAuth } from "@clerk/clerk-react";

// Name of the JWT template to use for API requests
const JWT_TEMPLATE_NAME = "CodenamePulsetrack";

/**
 * Hook to get the current user's authentication token with the correct template
 * This can be used when making API calls to authenticate the user
 */
export const useAuthToken = () => {
  const { getToken } = useAuth();

  const getAuthToken = async () => {
    try {
      // Always use the template to ensure the token has the correct claims
      return await getToken({ template: JWT_TEMPLATE_NAME });
    } catch (error) {
      console.error(
        `Failed to get auth token with template ${JWT_TEMPLATE_NAME}:`,
        error,
      );
      return null;
    }
  };

  return { getAuthToken };
};

/**
 * Function to create an authorized fetch request with the templated auth token in the header
 */
export const createAuthorizedRequest = async (
  url: string,
  options: RequestInit = {},
  getAuthTokenFn?: () => Promise<string | null>, // Optional function to get auth token
): Promise<Response> => {
  // Use the provided function or a default implementation
  const getTokenFn =
    getAuthTokenFn ||
    (async () => {
      console.warn(
        "No getAuthToken function provided to createAuthorizedRequest",
      );
      return null;
    });

  const token = await getTokenFn();

  if (!token) {
    throw new Error("No auth token available. Are you logged in?");
  }

  const headers = {
    ...options.headers,
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

/**
 * Helper to extract user information from Clerk auth
 */
export const useUserInfo = () => {
  const { userId, isSignedIn } = useAuth();
  // Clerk typically separates auth state and user details into different hooks
  // If you have access to useUser, import and use it instead
  // For now, we'll return just the basic info from useAuth

  return {
    userId,
    isSignedIn,
    email: null, // These would come from useUser() if available
    firstName: null,
    lastName: null,
    fullName: "",
    imageUrl: null,
  };
};
