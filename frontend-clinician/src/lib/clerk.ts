/**
 * Clerk authentication configuration
 * This file provides a central place for Clerk-related configuration and utilities
 */
import { useAuth, useSession } from "@clerk/clerk-react";

// The name of the JWT template that includes the required claims for our backend
const JWT_TEMPLATE_NAME = "CodenamePulsetrack";

/**
 * Get the current user's ID from Clerk
 * This is a utility function that can be used throughout the application
 */
export const getCurrentUserId = (): string | null => {
  try {
    // Try to access <PERSON>'s global instance
    if (typeof window !== "undefined" && window.Clerk?.user?.id) {
      return window.Clerk.user.id;
    }
    return null;
  } catch (error) {
    console.error("Error getting user ID:", error);
    return null;
  }
};

/**
 * Get the current session token from <PERSON>
 * This is useful for making authenticated API requests
 */
export const getSessionToken = async (): Promise<string | null> => {
  try {
    // Try to access <PERSON>'s global instance
    if (typeof window !== "undefined" && window.Clerk?.session) {
      console.log(`Requesting token with template: ${JWT_TEMPLATE_NAME}`); // Added log
      // Use the template name to get a token with the required claims
      return await window.Clerk.session.getToken({
        template: JWT_TEMPLATE_NAME,
      });
    }
    return null;
  } catch (error) {
    console.error(
      `Error getting Clerk session token with template ${JWT_TEMPLATE_NAME}:`,
      error,
    ); // Updated log
    return null;
  }
};

/**
 * Check if the user has a specific role
 * This is a placeholder for role-based access control
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasRole = (_: string): boolean => {
  // This would typically check against Clerk's user metadata
  // For now, this is just a placeholder
  return true;
};

/**
 * Hook to get user data in a consistent format
 * This provides a unified interface for user data across the application
 */
export const useClerkUser = () => {
  const { userId, isSignedIn, isLoaded } = useAuth();

  return {
    id: userId,
    isAuthenticated: isSignedIn,
    isLoading: !isLoaded,
  };
};

/**
 * Hook to get the session token
 * This is useful for components that need to make authenticated API requests
 */
export const useClerkToken = () => {
  const session = useSession();
  const isSignedIn = session.isSignedIn;

  const getSessionToken = async (): Promise<string | null> => {
    if (!isSignedIn || !session.session) return null;
    try {
      // Use the template name to get a token with the required claims
      return await session.session.getToken({ template: JWT_TEMPLATE_NAME });
    } catch (error) {
      console.error(
        `Error getting session token with template ${JWT_TEMPLATE_NAME}:`,
        error,
      ); // Updated log
      return null;
    }
  };

  return { getSessionToken };
};

// Add TypeScript declaration for Clerk's global instance
declare global {
  interface Window {
    Clerk?: {
      user?: {
        id: string;
      };
      session?: {
        // Updated to include optional options parameter for template
        getToken: (options?: { template?: string }) => Promise<string>;
      };
    };
  }
}
