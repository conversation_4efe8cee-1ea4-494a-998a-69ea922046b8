import { parseISO } from "date-fns";

/**
 * Convert a UTC datetime string to the user's local timezone
 * @param utcDateString - ISO datetime string in UTC
 * @returns Date object in user's local timezone
 */
export function convertUTCToLocal(utcDateString: string): Date {
  // parseISO correctly parses ISO strings with timezone information
  // The resulting Date object will be in UTC internally but will
  // display in the user's local timezone when formatted
  const date = parseISO(utcDateString);
  
  // Verify the date is valid
  if (isNaN(date.getTime())) {
    console.error(`Invalid date string: ${utcDateString}`);
    return new Date(); // Return current date as fallback
  }
  
  return date;
}

/**
 * Get the user's current timezone
 * @returns Timezone string (e.g., "America/New_York")
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}