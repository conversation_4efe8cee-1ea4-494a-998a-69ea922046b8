const KG_TO_LBS_FACTOR = 2.20462;

export const kgToLbs = (kg: number): number => {
  return kg * KG_TO_LBS_FACTOR;
};

export const lbsToKg = (lbs: number): number => {
  return lbs / KG_TO_LBS_FACTOR;
};

export const formatWeight = (kg: number): { kg: string; lbs: string } => {
  return {
    kg: kg.toFixed(2),
    lbs: kgToLbs(kg).toFixed(2)
  };
};

export const formatWeightDisplay = (kg: number): string => {
  const formatted = formatWeight(kg);
  return `${formatted.kg} kg / ${formatted.lbs} lbs`;
};