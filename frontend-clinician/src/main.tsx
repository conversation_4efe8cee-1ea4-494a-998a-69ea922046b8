import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import "./index.css";
import App from "./App.tsx";
import { AuthProvider } from "@pulsetrack/shared-frontend";

// Import your Publishable Key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key");
}

// Log the base URL for debugging
console.log("Base URL:", window.location.origin);
console.log("Current Path:", window.location.pathname);

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      <AuthProvider publishableKey={PUBLISHABLE_KEY}>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </StrictMode>,
);
