// Middleware for authentication protection
// This will work with the ClerkProvider in main.tsx

/**
 * Create a middleware function that can be used to protect API routes or other features
 * based on authentication status. When used with an API, this should be applied to routes
 * that need authentication.
 */
export const withAuth = (
  handler: (req: Request, ...args: unknown[]) => Response | Promise<Response>,
) => {
  return async (req: Request, ...rest: unknown[]) => {
    // Check if in test environment
    if (process.env.NODE_ENV === "test") {
      return handler(req, ...rest);
    }

    // Get the session token from the request headers
    const authHeader = req.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Extract the token (would verify with <PERSON> in a real implementation)
    const token = authHeader.split(" ")[1];
    if (!token) {
      return new Response(JSON.stringify({ error: "Invalid token format" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // In a real implementation, you would verify the token with Clerk
    // For now, we just pass it to the handler
    return handler(req, ...rest);
  };
};

// This is a placeholder function that would be replaced with actual Clerk verification
// In a real implementation with clerk-backend
export const getAuth = async (req: Request) => {
  const authHeader = req.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.split(" ")[1];
  if (!token) {
    return null;
  }

  // In a real implementation, you would verify the token with Clerk
  // For example, using the Clerk SDK
  // return await clerkClient.sessions.verifySession(token);

  // For now, we just return a placeholder
  return { userId: "test-user-id" };
};
