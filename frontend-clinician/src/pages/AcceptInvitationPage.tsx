import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUser, useSignUp } from "@clerk/clerk-react";
import {
  AuthInput,
  AuthButton,
  AuthErrorMessage,
  AuthLoadingIndicator,
} from "@pulsetrack/shared-frontend";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AxiosError } from "axios";

const AcceptInvitationPage: React.FC = () => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  // Use Clerk hooks directly for state and actions
  const { isLoaded, isSignedIn, user } = useUser();
  const { signUp, setActive } = useSignUp();
  const navigate = useNavigate();

  // Redirect if user is already signed in
  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      // Check user role from metadata to determine the correct dashboard
      const userRole = user.publicMetadata?.role;

      // Determine redirect path based on role
      const redirectPath = "/patient/dashboard"; // Default to patient dashboard for patient invitations

      // Log for debugging
      console.log("User role from metadata:", userRole);

      // Redirect to the appropriate dashboard
      console.log(`Redirecting to ${redirectPath}`);
      navigate(redirectPath);
    }
  }, [isLoaded, isSignedIn, navigate, user]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (!signUp || !setActive) {
      setError("Sign up context is not available. Cannot accept invitation.");
      console.error("Clerk signUp or setActive is not available.");
      return;
    }

    setLoading(true);
    try {
      // Clerk handles token verification via URL. We just need to set the password.
      const result = await signUp.update({ password });

      if (result.status === "complete") {
        // Set the session explicitly after successful sign-up/invitation acceptance
        await setActive({ session: result.createdSessionId });
        // The useEffect hook watching isSignedIn should handle the redirect to patient dashboard
        console.log(
          "Patient invitation accepted and password set successfully. Redirecting to patient dashboard...",
        );
      } else {
        // Handle potential issues like password complexity requirements not met
        console.error("Clerk sign up update status:", result.status, result);
        // Provide a generic message, specific errors are caught below
        const specificError =
          "An issue occurred during account activation. Please check password requirements or try again.";
        setError(specificError);
      }
    } catch (err: unknown) {
      console.error("Invitation acceptance/password set failed:", err);
      let errorMsg =
        "Failed to activate account. Please check password requirements or try again.";
      if (err instanceof AxiosError) {
        errorMsg = err.response?.data?.detail || err.message || errorMsg;
      } else if (err instanceof Error) {
        errorMsg = err.message;
      }
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // Render loading indicator until Clerk's user state is loaded
  if (!isLoaded) {
    return (
      <div
        className="flex justify-center items-center min-h-screen"
        role="status"
      >
        <AuthLoadingIndicator />
        <p className="ml-2">Loading...</p>
      </div>
    );
  }

  // If user becomes signed in after load (e.g., via redirect), let useEffect handle redirection
  if (isSignedIn) {
    return (
      <div
        className="flex justify-center items-center min-h-screen"
        role="status"
      >
        <AuthLoadingIndicator />
        <p className="ml-2">Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Accept Patient Invitation
          </CardTitle>
          <CardDescription className="text-center">
            Welcome to PulseTrack! Set your password to activate your patient
            account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <AuthInput
              label="Password"
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your new password"
              disabled={loading}
            />
            <AuthInput
              label="Confirm Password"
              id="confirm-password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              placeholder="Confirm your new password"
              disabled={loading}
            />
            {error && (
              <div role="alert">
                <AuthErrorMessage message={error} />
              </div>
            )}
            <AuthButton type="submit" disabled={loading} className="w-full">
              {loading ? (
                <AuthLoadingIndicator />
              ) : (
                "Set Password & Activate Account"
              )}
            </AuthButton>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm text-gray-600">
          Need help? Contact support.
        </CardFooter>
      </Card>
    </div>
  );
};

export default AcceptInvitationPage;
