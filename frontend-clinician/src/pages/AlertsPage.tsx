import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import apiClient from "@/lib/apiClient";
import { format } from "date-fns";
import {
  AlertCircle,
  AlertTriangle,
  Info,
  CheckCircle,
  Search,
  Filter,
  ChevronRight,
  User,
  Calendar,
  Activity,
} from "lucide-react";

interface PatientAlert {
  id: string;
  patient_id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  alert_type: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  status: "new" | "acknowledged" | "resolved" | "dismissed";
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  resolved_by_clinician_id?: string;
}

export default function AlertsPage() {
  const navigate = useNavigate();
  const [alerts, setAlerts] = useState<PatientAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("new");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalAlerts, setTotalAlerts] = useState(0);
  const alertsPerPage = 10;

  useEffect(() => {
    fetchAlerts();
  }, [severityFilter, statusFilter, typeFilter, currentPage]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {
        skip: (currentPage - 1) * alertsPerPage,
        limit: alertsPerPage,
      };
      
      if (severityFilter !== "all") params.severity = severityFilter;
      if (statusFilter !== "all") params.status = statusFilter;
      if (typeFilter !== "all") params.alert_type = typeFilter;
      
      const response = await apiClient.get("/patient-alerts", { params });
      setAlerts(response.data.items || []);
      setTotalAlerts(response.data.total || 0);
    } catch (err) {
      console.error("Error fetching alerts:", err);
      setError("Failed to fetch patient alerts");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (alertId: string, newStatus: string) => {
    try {
      await apiClient.patch(`/patient-alerts/${alertId}`, {
        status: newStatus,
      });
      
      // Dispatch event to update header bar alert count
      window.dispatchEvent(new CustomEvent('patientAlertUpdated'));
      
      // Refresh the alerts list
      fetchAlerts();
    } catch (err) {
      console.error("Error updating alert status:", err);
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "critical":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case "high":
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case "medium":
        return <Info className="h-5 w-5 text-yellow-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getSeverityBadgeVariant = (severity: string) => {
    switch (severity) {
      case "critical":
        return "destructive";
      case "high":
        return "warning";
      case "medium":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "new":
        return "destructive";
      case "acknowledged":
        return "warning";
      case "resolved":
        return "success";
      case "dismissed":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getAlertTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      side_effect_reported: "Side Effect",
      medication_request: "Medication Request",
      weight_threshold: "Weight Threshold",
      missed_log: "Missed Log",
      ai_detected_pattern: "AI Pattern",
    };
    return labels[type] || type;
  };

  const filteredAlerts = alerts.filter((alert) => {
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      const patientName = `${alert.patient.first_name} ${alert.patient.last_name}`.toLowerCase();
      return (
        patientName.includes(searchLower) ||
        alert.description.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  const totalPages = Math.ceil(totalAlerts / alertsPerPage);

  return (
    <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Patient Alerts</h1>
            <p className="text-muted-foreground mt-1">
              Monitor and manage patient alerts across your practice
            </p>
          </div>
          <Badge variant="outline" className="text-lg px-4 py-2">
            {totalAlerts} Total Alerts
          </Badge>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search patient or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="severity">Severity</Label>
                <Select value={severityFilter} onValueChange={setSeverityFilter}>
                  <SelectTrigger id="severity">
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="acknowledged">Acknowledged</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="dismissed">Dismissed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="type">Alert Type</Label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="side_effect_reported">Side Effect</SelectItem>
                    <SelectItem value="medication_request">Medication Request</SelectItem>
                    <SelectItem value="weight_threshold">Weight Threshold</SelectItem>
                    <SelectItem value="missed_log">Missed Log</SelectItem>
                    <SelectItem value="ai_detected_pattern">AI Pattern</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Alerts List */}
        <div className="space-y-4">
          {loading ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">Loading alerts...</p>
                </div>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center text-destructive">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>{error}</p>
                </div>
              </CardContent>
            </Card>
          ) : filteredAlerts.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center text-muted-foreground">
                  <Info className="h-8 w-8 mx-auto mb-2" />
                  <p>No alerts found matching your criteria</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredAlerts.map((alert) => (
              <Card key={alert.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center gap-3">
                        {getSeverityIcon(alert.severity)}
                        <div className="flex items-center gap-2">
                          <Badge variant={getSeverityBadgeVariant(alert.severity)}>
                            {alert.severity.toUpperCase()}
                          </Badge>
                          <Badge variant={getStatusBadgeVariant(alert.status)}>
                            {alert.status.toUpperCase()}
                          </Badge>
                          <Badge variant="outline">
                            {getAlertTypeLabel(alert.alert_type)}
                          </Badge>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-lg">
                          {alert.patient.first_name} {alert.patient.last_name}
                        </h3>
                        <p className="text-muted-foreground mt-1">
                          {alert.description}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-6 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          <span>{alert.patient.email}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{format(new Date(alert.created_at), "MMM d, yyyy h:mm a")}</span>
                        </div>
                        {alert.resolved_at && (
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Resolved {format(new Date(alert.resolved_at), "MMM d, yyyy")}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      {alert.status === "new" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(alert.id, "acknowledged")}
                        >
                          Acknowledge
                        </Button>
                      )}
                      {alert.status === "acknowledged" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(alert.id, "resolved")}
                        >
                          Resolve
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => navigate(`/clinician/patients/${alert.patient_id}`)}
                      >
                        View Patient
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter((page) => {
                  const distance = Math.abs(page - currentPage);
                  return distance === 0 || distance === 1 || page === 1 || page === totalPages;
                })
                .map((page, index, array) => (
                  <React.Fragment key={page}>
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}
                    <Button
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  </React.Fragment>
                ))}
            </div>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
  );
}