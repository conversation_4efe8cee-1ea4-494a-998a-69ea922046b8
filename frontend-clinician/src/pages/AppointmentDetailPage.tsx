import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import apiClient from "@/lib/apiClient";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { AppointmentForm } from "@/components/appointments/AppointmentForm";
import { toast } from "sonner";
import { AxiosError } from "axios";

// TODO: Move this interface to a shared types file
interface Appointment {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  clinician: {
    // Assuming clinician info is needed/available
    id: string;
    first_name: string;
    last_name: string;
  };
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  status: "scheduled" | "completed" | "cancelled";
  reason?: string;
  patient_notes?: string;
  clinician_notes?: string; // Assuming clinician notes exist
}

// Add FormData type matching AppointmentForm
interface AppointmentFormData {
  patient_id: string;
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  reason?: string;
  patient_notes?: string;
}

const AppointmentDetailPage: React.FC = () => {
  const { appointmentId } = useParams<{ appointmentId: string }>();
  const navigate = useNavigate();
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // State for edit modal
  const [isSubmitting, setIsSubmitting] = useState(false); // State for submission status

  useEffect(() => {
    const fetchAppointmentDetails = async () => {
      if (!appointmentId) {
        setError("Appointment ID is missing.");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Assuming the endpoint is /appointments/{id}/
        const response = await apiClient.get<Appointment>(
          `/appointments/${appointmentId}/`,
        );
        setAppointment(response.data);
      } catch (fetchError: unknown) {
        console.error("Error fetching appointment details:", fetchError);
        let errorMsg = "Failed to fetch appointment details.";
        if (fetchError instanceof AxiosError) {
          errorMsg =
            fetchError.response?.data?.detail || fetchError.message || errorMsg;
        } else if (fetchError instanceof Error) {
          errorMsg = fetchError.message;
        }
        setError(errorMsg);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointmentDetails();
  }, [appointmentId]); // Dependency array includes appointmentId

  const getStatusBadgeVariant = (status: string | undefined) => {
    switch (status) {
      case "scheduled":
        return "default";
      case "completed":
        return "secondary";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const handleEdit = () => {
    setIsEditModalOpen(true); // Open the modal
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleUpdateAppointment = async (data: AppointmentFormData) => {
    if (!appointmentId) return;

    setIsSubmitting(true);
    try {
      // IMPORTANT: The database stores times in UTC
      // When the user selects a time in the form, it's in local time
      // We need to convert it to UTC for storage

      // Get the local date and time components from the form
      const [datePart, timePart] = data.appointment_datetime.split("T");

      // Create a Date object in local time
      const localDate = new Date(`${datePart}T${timePart}:00`);
      console.log("Local date from form:", localDate.toString());

      // Convert to UTC ISO string for the backend
      const isoDateWithTZ = localDate.toISOString();
      console.log("UTC ISO string for backend:", isoDateWithTZ);
      console.log(
        "Local time selected by user:",
        localDate.toLocaleTimeString(),
      );
      console.log(
        "UTC time to be stored:",
        new Date(isoDateWithTZ).toUTCString(),
      );

      console.log("Original datetime from form:", data.appointment_datetime);
      console.log("Converted ISO datetime with TZ:", isoDateWithTZ);

      // Construct payload - patient_id should not be updated
      const updatePayload = {
        appointment_datetime: isoDateWithTZ,
        duration_minutes: data.duration_minutes,
        appointment_type: data.appointment_type,
        status: appointment?.status, // Keep original status unless specifically changed
        reason: data.reason || undefined,
        patient_notes: data.patient_notes || undefined,
        // Add clinician_notes if the form supports it
      };

      // Assuming PUT or PATCH endpoint is /appointments/{id}/
      const response = await apiClient.put<Appointment>(
        `/appointments/${appointmentId}/`,
        updatePayload,
      );

      setAppointment(response.data); // Update local state with the response
      toast.success("Appointment updated successfully");
      setIsEditModalOpen(false); // Close modal on success
    } catch (updateError: unknown) {
      console.error("Error updating appointment:", updateError);
      let errorMsg = "Failed to update appointment.";
      if (updateError instanceof AxiosError) {
        errorMsg =
          updateError.response?.data?.detail || updateError.message || errorMsg;
      } else if (updateError instanceof Error) {
        errorMsg = updateError.message;
      }
      toast.error(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!appointmentId) return;
    if (window.confirm("Are you sure you want to cancel this appointment?")) {
      try {
        // Using POST to cancel the appointment with our API endpoint
        await apiClient.post(`/appointments/${appointmentId}/cancel/`, {
          reason: "Cancelled by clinician",
        });
        toast.success("Appointment cancelled successfully"); // Update toast message
        navigate("/clinician/appointments");
      } catch (deleteError: unknown) {
        console.error("Error cancelling appointment:", deleteError);
        let errorMsg = "Failed to cancel appointment.";
        if (deleteError instanceof AxiosError) {
          errorMsg =
            deleteError.response?.data?.detail ||
            deleteError.message ||
            errorMsg;
        } else if (deleteError instanceof Error) {
          errorMsg = deleteError.message;
        }
        toast.error(errorMsg);
        setError(
          typeof errorMsg === "string"
            ? errorMsg
            : "Failed to cancel appointment",
        );
      }
    }
  };

  // Transform full Appointment data to the shape expected by AppointmentForm
  const appointmentFormDataForEdit = appointment
    ? {
        id: appointment.id,
        patient_id: appointment.patient.id, // Need patient ID
        appointment_datetime: appointment.appointment_datetime,
        duration_minutes: appointment.duration_minutes,
        appointment_type: appointment.appointment_type,
        reason: appointment.reason,
        patient_notes: appointment.patient_notes,
      }
    : undefined;

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Appointment Details</CardTitle>
            <CardDescription>
              Viewing details for appointment ID: {appointmentId}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {/* TODO: Add Edit/Delete buttons - only show if not loading and no error */}
            {!isLoading && !error && appointment && (
              <>
                <Button variant="outline" size="sm" onClick={handleEdit}>
                  <Pencil className="h-4 w-4 mr-2" /> Edit
                </Button>
                <Button variant="destructive" size="sm" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" /> Cancel
                </Button>
              </>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : appointment ? (
            // Display appointment details
            <div className="space-y-4">
              {/* Basic Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-semibold">Patient:</span>{" "}
                  {appointment.patient.first_name}{" "}
                  {appointment.patient.last_name}
                </div>
                <div>
                  <span className="font-semibold">Date & Time:</span>{" "}
                  {(() => {
                    // The appointment_datetime comes from the API in UTC
                    // JavaScript's Date object automatically converts to local time when displaying
                    const utcDate = new Date(appointment.appointment_datetime);

                    // Log both UTC and local time for debugging
                    console.log(
                      "UTC time from API:",
                      appointment.appointment_datetime,
                    );
                    console.log(
                      "UTC time as Date object:",
                      utcDate.toUTCString(),
                    );
                    console.log("Local time for display:", utcDate.toString());

                    // Format the date in local time using date-fns
                    const formattedDate = format(utcDate, "PPp");
                    console.log("Formatted local time:", formattedDate);

                    return formattedDate;
                  })()}
                </div>
                <div>
                  <span className="font-semibold">Duration:</span>{" "}
                  {appointment.duration_minutes} min
                </div>
                <div>
                  <span className="font-semibold">Type:</span>{" "}
                  {appointment.appointment_type}
                </div>
                <div>
                  <span className="font-semibold">Status:</span>{" "}
                  <Badge variant={getStatusBadgeVariant(appointment.status)}>
                    {appointment.status}
                  </Badge>
                </div>
                {/* Add Clinician if available */}
                {/* <div><span className="font-semibold">Clinician:</span> {appointment.clinician.first_name} {appointment.clinician.last_name}</div> */}
              </div>

              {/* Notes/Reason Sections */}
              {appointment.reason && (
                <div>
                  <span className="font-semibold">Reason:</span>{" "}
                  {appointment.reason}
                </div>
              )}
              {appointment.patient_notes && (
                <div>
                  <span className="font-semibold">Patient Notes:</span>{" "}
                  {appointment.patient_notes}
                </div>
              )}
              {appointment.clinician_notes && (
                <div>
                  <span className="font-semibold">Clinician Notes:</span>{" "}
                  {appointment.clinician_notes}
                </div>
              )}
            </div>
          ) : (
            <p>No appointment details found.</p> // Should ideally not happen if ID is valid
          )}
        </CardContent>
      </Card>

      {/* Edit Appointment Modal */}
      {appointmentFormDataForEdit && (
        <AppointmentForm
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          onSubmit={handleUpdateAppointment}
          isSubmitting={isSubmitting}
          appointment={appointmentFormDataForEdit} // Pass existing data
        />
      )}
    </div>
  );
};

export default AppointmentDetailPage;
