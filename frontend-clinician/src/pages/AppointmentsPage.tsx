import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>T<PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, CalendarPlus, Loader2, RefreshCw } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import apiClient from "@/lib/apiClient";
import { format, isSameDay, startOfDay, parseISO, isAfter, isBefore, addDays } from "date-fns";
import { AppointmentForm } from "@/components/appointments/AppointmentForm";
import { AppointmentRequestModal } from "@/components/appointments/AppointmentRequestModal";
import { toast } from "sonner";
import { useAuth } from "@clerk/clerk-react";
import { useNavigate } from "react-router-dom";
import { Calendar } from "@/components/ui/calendar";
import { formatDateTime, formatDate, formatTime, formatAppointmentTime, toZonedTime, getUserTimezone } from "@pulsetrack/shared-frontend";
// Define the Value type based on what react-calendar accepts
type Value = Date | Date[] | null;
import "./calendar-styles.css";

interface Appointment {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  status: "scheduled" | "completed" | "cancelled";
  reason?: string;
  patient_notes?: string;
}

interface AppointmentRequest {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  preferred_datetime: string;
  reason: string;
  status: "pending" | "approved" | "rejected" | "cancelled";
  created_at: string;
  updated_at: string;
  patient_id: string;
  reviewed_by_id?: string;
  reviewed_by?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  review_notes?: string;
  clinician_preference?: string;
}

// Restored AppointmentFormData interface
interface AppointmentFormData {
  patient_id: string;
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  reason?: string;
  patient_notes?: string;
}

const AppointmentsPage: React.FC = () => {
  const { userId } = useAuth();
  const navigate = useNavigate();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [appointmentRequests, setAppointmentRequests] = useState<AppointmentRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingRequests, setIsLoadingRequests] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [requestsError, setRequestsError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Appointment request modal state
  const [selectedAppointmentRequest, setSelectedAppointmentRequest] = useState<AppointmentRequest | null>(null);
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
  const [isRequestModalLoading, setIsRequestModalLoading] = useState(false);
  const [requestModalError, setRequestModalError] = useState<string | null>(null);
  const [isRequestSubmitting, setIsRequestSubmitting] = useState(false);

  // Calendar state
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    undefined,
  );

  const fetchAppointments = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get<Appointment[]>("/appointments/");
      setAppointments(response.data || []);
    } catch (error: unknown) {
      console.error("Error fetching appointments:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch appointments";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchAppointmentRequests = useCallback(async () => {
    setIsLoadingRequests(true);
    setRequestsError(null);

    try {
      const response = await apiClient.get<AppointmentRequest[]>("/appointments/requests");
      setAppointmentRequests(response.data || []);
    } catch (error: unknown) {
      console.error("Error fetching appointment requests:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch appointment requests";
      setRequestsError(errorMessage);
    } finally {
      setIsLoadingRequests(false);
    }
  }, []);

  useEffect(() => {
    fetchAppointments();
    fetchAppointmentRequests();
  }, [fetchAppointments, fetchAppointmentRequests]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "scheduled":
        return "default";
      case "completed":
        return "secondary";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getRequestStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "pending":
        return "outline";
      case "approved":
        return "default";
      case "rejected":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const handleNewAppointment = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitAppointment = async (data: AppointmentFormData) => {
    if (!userId) {
      toast.error("Clinician ID not available");
      return;
    }

    setIsSubmitting(true);
    try {
      const appointmentDateObj = new Date(data.appointment_datetime);
      const isoDateWithTZ = appointmentDateObj.toISOString();

      const appointmentData = {
        patient_id: data.patient_id,
        clinician_id: userId,
        appointment_datetime: isoDateWithTZ,
        duration_minutes: data.duration_minutes,
        appointment_type: data.appointment_type,
        status: "scheduled",
        reason: data.reason || undefined,
        patient_notes: data.patient_notes || undefined,
      };

      console.log(
        "Sending appointment data:",
        JSON.stringify(appointmentData, null, 2),
      );

      const response = await apiClient.post("/appointments/", appointmentData);
      console.log("Appointment created successfully:", response.data);

      toast.success("Appointment created successfully");
      setIsModalOpen(false);
      fetchAppointments();
    } catch (error: unknown) {
      console.error("Error creating appointment:", error);

      if (error instanceof Error) {
        console.error(error.message);
      }

      const errorDetail =
        error instanceof Error ? error.message : "An error occurred";
      toast.error("Failed to create appointment", {
        description: errorDetail,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Appointment request modal handlers
  const handleOpenRequestModal = async (request: AppointmentRequest) => {
    setSelectedAppointmentRequest(request);
    setIsRequestModalOpen(true);
    setRequestModalError(null);
  };

  const handleCloseRequestModal = () => {
    setIsRequestModalOpen(false);
    setSelectedAppointmentRequest(null);
    setRequestModalError(null);
  };

  const handleApproveRequest = async (requestId: string, notes?: string) => {
    if (!userId) {
      toast.error("Clinician ID not available");
      return;
    }

    setIsRequestSubmitting(true);
    try {
      const updateData = {
        status: "approved",
        reviewed_by_id: userId,
        review_notes: notes || undefined,
      };

      await apiClient.patch(`/appointments/requests/${requestId}/status`, updateData);
      
      toast.success("Appointment request approved successfully");
      
      // Refresh the requests list
      await fetchAppointmentRequests();
    } catch (error: unknown) {
      console.error("Error approving appointment request:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to approve request";
      toast.error("Failed to approve request", { description: errorMessage });
      throw error; // Re-throw to handle in modal
    } finally {
      setIsRequestSubmitting(false);
    }
  };

  const handleRejectRequest = async (requestId: string, notes: string) => {
    if (!userId) {
      toast.error("Clinician ID not available");
      return;
    }

    setIsRequestSubmitting(true);
    try {
      const updateData = {
        status: "rejected",
        reviewed_by_id: userId,
        review_notes: notes,
      };

      await apiClient.patch(`/appointments/requests/${requestId}/status`, updateData);
      
      toast.success("Appointment request rejected");
      
      // Refresh the requests list
      await fetchAppointmentRequests();
    } catch (error: unknown) {
      console.error("Error rejecting appointment request:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to reject request";
      toast.error("Failed to reject request", { description: errorMessage });
      throw error; // Re-throw to handle in modal
    } finally {
      setIsRequestSubmitting(false);
    }
  };

  const appointmentDates = appointments.map((appt) =>
    startOfDay(new Date(appt.appointment_datetime)),
  );

  const appointmentRequestDates = appointmentRequests.map((req) =>
    startOfDay(new Date(req.preferred_datetime)),
  );

  const handleDateSelect = (value: Value) => {
    if (value instanceof Date) {
      setSelectedDate(value);
    } else if (Array.isArray(value)) {
      setSelectedDate(undefined);
    } else {
      setSelectedDate(undefined);
    }
  };

  const today = startOfDay(new Date());
  const tomorrow = addDays(today, 1);

  const todaysAppointments = appointments.filter((app) =>
    isSameDay(parseISO(app.appointment_datetime), today),
  );

  const upcomingAppointments = appointments.filter((app) => {
    const appointmentDate = parseISO(app.appointment_datetime);
    return isAfter(appointmentDate, tomorrow);
  });

  const filteredAppointments = selectedDate
    ? appointments.filter((app) =>
        isSameDay(parseISO(app.appointment_datetime), selectedDate),
      )
    : appointments;

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Appointments</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {
              fetchAppointments();
              fetchAppointmentRequests();
            }}
            disabled={isLoading || isLoadingRequests}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleNewAppointment} disabled={isLoading}>
            <CalendarPlus className="h-4 w-4 mr-2" />
            New Appointment
          </Button>
        </div>
      </div>

      <div className="bg-card p-4 rounded-lg shadow flex justify-center mb-8">
        <Calendar
          value={selectedDate}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange={handleDateSelect as any} // Type assertion needed due to incompatible library types
          tileClassName={({ date }) => {
            const hasAppointment = appointmentDates.some((appDate) => isSameDay(appDate, date));
            const hasRequest = appointmentRequestDates.some((reqDate) => isSameDay(reqDate, date));
            
            if (hasAppointment && hasRequest) {
              return "has-appointment has-request";
            } else if (hasAppointment) {
              return "has-appointment";
            } else if (hasRequest) {
              return "has-request";
            }
            return null;
          }}
        />
      </div>

      {selectedDate ? (
        // Selected date view
        <Card>
          <CardHeader>
            <CardTitle>
              Appointments for {formatDate(selectedDate)}
            </CardTitle>
            <CardDescription>
              View and manage your appointments for the selected date
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Patient</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAppointments.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        className="text-center text-muted-foreground"
                      >
                        No appointments found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAppointments.map((appointment) => (
                      <TableRow key={appointment.id}>
                        <TableCell>
                          {formatDateTime(appointment.appointment_datetime)}
                        </TableCell>
                        <TableCell>
                          {`${appointment.patient.first_name} ${appointment.patient.last_name}`}
                        </TableCell>
                        <TableCell>{appointment.appointment_type}</TableCell>
                        <TableCell>{appointment.duration_minutes} min</TableCell>
                        <TableCell>
                          <Badge
                            variant={getStatusBadgeVariant(appointment.status)}
                          >
                            {appointment.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              navigate(
                                `/clinician/appointments/${appointment.id}`,
                              )
                            }
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      ) : (
        // Default view: Today's + Upcoming appointments
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Today's Appointments</CardTitle>
              <CardDescription>
                {format(today, "EEEE, MMMM d, yyyy")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : error ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Time</TableHead>
                      <TableHead>Patient</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {todaysAppointments.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          className="text-center text-muted-foreground"
                        >
                          No appointments scheduled for today
                        </TableCell>
                      </TableRow>
                    ) : (
                      todaysAppointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>
                            {formatTime(appointment.appointment_datetime)}
                          </TableCell>
                          <TableCell>
                            {`${appointment.patient.first_name} ${appointment.patient.last_name}`}
                          </TableCell>
                          <TableCell>{appointment.appointment_type}</TableCell>
                          <TableCell>{appointment.duration_minutes} min</TableCell>
                          <TableCell>
                            <Badge
                              variant={getStatusBadgeVariant(appointment.status)}
                            >
                              {appointment.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                navigate(
                                  `/clinician/appointments/${appointment.id}`,
                                )
                              }
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming Appointments</CardTitle>
              <CardDescription>
                All appointments after today
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Patient</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {upcomingAppointments.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        className="text-center text-muted-foreground"
                      >
                        No upcoming appointments scheduled
                      </TableCell>
                    </TableRow>
                  ) : (
                    upcomingAppointments
                      .sort((a, b) => 
                        new Date(a.appointment_datetime).getTime() - 
                        new Date(b.appointment_datetime).getTime()
                      )
                      .map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>
                            {formatDateTime(appointment.appointment_datetime)}
                          </TableCell>
                          <TableCell>
                            {`${appointment.patient.first_name} ${appointment.patient.last_name}`}
                          </TableCell>
                          <TableCell>{appointment.appointment_type}</TableCell>
                          <TableCell>{appointment.duration_minutes} min</TableCell>
                          <TableCell>
                            <Badge
                              variant={getStatusBadgeVariant(appointment.status)}
                            >
                              {appointment.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                navigate(
                                  `/clinician/appointments/${appointment.id}`,
                                )
                              }
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Appointment Requests</CardTitle>
              <CardDescription>
                Patient requests for appointments awaiting your approval
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingRequests ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : requestsError ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{requestsError}</AlertDescription>
                </Alert>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Requested Date</TableHead>
                      <TableHead>Patient</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Requested</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {appointmentRequests.filter(req => req.status === "pending").length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          className="text-center text-muted-foreground"
                        >
                          No pending appointment requests found
                        </TableCell>
                      </TableRow>
                    ) : (
                      appointmentRequests
                        .filter((request) => request.status === "pending")
                        .sort((a, b) => 
                          new Date(b.created_at).getTime() - 
                          new Date(a.created_at).getTime()
                        )
                        .map((request) => (
                          <TableRow 
                            key={request.id}
                            className="cursor-pointer hover:bg-gray-50"
                            onClick={() => handleOpenRequestModal(request)}
                          >
                            <TableCell>
                              {formatDateTime(request.preferred_datetime)}
                            </TableCell>
                            <TableCell>
                              {`${request.patient.first_name} ${request.patient.last_name}`}
                            </TableCell>
                            <TableCell className="max-w-[200px]">
                              <div className="truncate" title={request.reason || "No reason provided"}>
                                {request.reason || "No reason provided"}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={getRequestStatusBadgeVariant(request.status)}>
                                {request.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {formatDate(request.created_at)}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent row click when button is clicked
                                  handleOpenRequestModal(request);
                                }}
                              >
                                Review
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      )}

      <AppointmentForm
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitAppointment}
        isSubmitting={isSubmitting}
      />

      <AppointmentRequestModal
        appointmentRequest={selectedAppointmentRequest}
        isOpen={isRequestModalOpen}
        isLoading={isRequestModalLoading}
        error={requestModalError}
        onClose={handleCloseRequestModal}
        onApprove={handleApproveRequest}
        onReject={handleRejectRequest}
        isSubmitting={isRequestSubmitting}
      />
    </div>
  );
};

export default AppointmentsPage;
