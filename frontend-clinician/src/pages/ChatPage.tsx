/**
 * ChatPage: Handles patient and clinician chat interfaces
 *
 * The chat interface allows clinicians to:
 * 1. Chat directly with the AI about general medical topics
 * 2. Select a specific patient and have patient-contextualized conversations
 * 3. Route messages to either the AI or directly to patients
 */

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge"; // Added Badge import
import {
  AlertCircle,
  Send,
  Bot,
  BrainCircuit,
  Users,
  CircleUser,
  SmilePlus,
  Search,
  X,
  ChevronDown,
  Stethoscope,
  FileText,
} from "lucide-react"; // Using available icons
import { cn } from "@/lib/utils";
import apiClient from "@/lib/apiClient";
import { useUser } from "@clerk/clerk-react"; // To check user role
import NewConversationDialog, {
  ConversationTarget,
} from "@/components/chat/NewConversationDialog";
import ConversationHeader from "@/components/chat/ConversationHeader";
import MessageRoutingDropdown, {
  MessageRoutingOption,
} from "@/components/chat/MessageRoutingDropdown";
import ResizablePane from "@/components/ui/resizable-pane";
import GenerateClinicalNoteButton from "@/components/clinical-notes/GenerateClinicalNoteButton";
import ClinicalNoteReviewModal from "@/components/clinical-notes/ClinicalNoteReviewModal";
import { ActionResponseHandler } from "@/components/chat/ActionResponseHandler";
import { toast } from "sonner";
import { formatChatTimestamp, formatDateTime, getUserTimezone } from "@pulsetrack/shared-frontend";

// Define the structure for a chat message (for displaying individual messages)
interface ChatMessageDisplayItem {
  id?: string;
  sender: "user" | "agent" | "patient" | "clinician" | "system" | "clinical_note"; // Added clinical_note
  message: string;
  timestamp?: string;
  routedTo?: "patient" | "ai"; // Add routing information
  metadata?: any; // For clinical note details
}

// Helper function to format date separators in chat
const formatMessageDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Check if the date is today
  if (date.toDateString() === today.toDateString()) {
    return "Today";
  }

  // Check if the date is yesterday
  if (date.toDateString() === yesterday.toDateString()) {
    return "Yesterday";
  }

  // Otherwise, return full date
  return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' });
};

const shouldGroupMessages = (
  currentMsg: ChatMessageDisplayItem,
  prevMsg?: ChatMessageDisplayItem,
): boolean => {
  if (!prevMsg) return false;
  if (currentMsg.sender !== prevMsg.sender) return false;

  // Check if messages are within 5 minutes of each other
  if (currentMsg.timestamp && prevMsg.timestamp) {
    const currentTime = new Date(currentMsg.timestamp).getTime();
    const prevTime = new Date(prevMsg.timestamp).getTime();
    const fiveMinutesMs = 5 * 60 * 1000;

    return currentTime - prevTime < fiveMinutesMs;
  }

  return false;
};

// Structure for individual messages in history from API (patient/agent focused)
interface ChatHistoryMessageAPI {
  message_id: string;
  sender_type: "USER" | "AGENT" | "PATIENT" | "CLINICIAN" | "CLINICAL_NOTE"; // Uppercase to match DB
  message: string;
  timestamp: string;
  is_read: boolean;
  metadata?: Record<string, unknown>;
  message_route?: "patient" | "ai"; // Add message routing information
}

interface ChatHistoryResponseAPI {
  messages: ChatHistoryMessageAPI[];
  total_count: number;
  has_more: boolean;
}

// Structure for clinician's list of conversations
interface ClinicianConversationItemAPI {
  patient_id: string;
  patient_first_name: string;
  patient_last_name: string;
  last_message_at?: string; // Assuming ISO string from backend
  unread_count: number;
}

interface ClinicianConversationsResponseAPI {
  conversations: ClinicianConversationItemAPI[];
  total_count: number;
  has_more: boolean;
}

const ChatPage: React.FC = () => {
  const { user } = useUser(); // Get user to check role
  const navigate = useNavigate();
  const [isClinicianView, setIsClinicianView] = useState(false);

  // State for clinician view
  const [clinicianConversations, setClinicianConversations] = useState<
    ClinicianConversationItemAPI[]
  >([]);
  const [filteredConversations, setFilteredConversations] = useState<
    ClinicianConversationItemAPI[]
  >([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null,
  );
  const [isLoadingConversations, setIsLoadingConversations] =
    useState<boolean>(false);
  const [lastMessageSenders, setLastMessageSenders] = useState<{
    [patientId: string]: string;
  }>({});
  const [lastMessageRoutes, setLastMessageRoutes] = useState<{
    [patientId: string]: string | undefined;
  }>({});
  const [hasRestoredFromLocalStorage, setHasRestoredFromLocalStorage] = useState<boolean>(false);
  const [lastFetchedPatientId, setLastFetchedPatientId] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState<boolean>(true);

  // State for displaying messages (either patient's own or selected patient for clinician)
  const [displayedMessages, setDisplayedMessages] = useState<
    ChatMessageDisplayItem[]
  >([]);
  const [inputMessage, setInputMessage] = useState<string>("");
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false);
  const [isLoadingResponse, setIsLoadingResponse] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const chatEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  // Removed lastRefreshTime ref - no longer needed without focus refresh
  const [showScrollButton, setShowScrollButton] = useState<boolean>(false);
  const [unreadMessagesCount, setUnreadMessagesCount] = useState<number>(0);

  // Conversation state
  const [isConversationDialogOpen, setIsConversationDialogOpen] =
    useState<boolean>(false);
  const [conversationType, setConversationType] = useState<
    ConversationTarget["type"] | undefined
  >(undefined);
  const [selectedPatientName, setSelectedPatientName] = useState<
    string | undefined
  >(undefined);

  // Clinical Note state
  const [showClinicalNoteModal, setShowClinicalNoteModal] = useState(false);
  const [generatedClinicalNote, setGeneratedClinicalNote] = useState<any>(null);
  const [isGeneratingNote, setIsGeneratingNote] = useState(false);
  const [justSentMessage, setJustSentMessage] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Message routing options
  const messageRoutingOptions: MessageRoutingOption[] = [
    {
      id: "patient",
      label: "Patient",
      description: "Message will be sent to the patient directly",
    },
    {
      id: "ai",
      label: "AI Assistant",
      description:
        "Message will be processed by the AI only, not shared with patient",
    },
  ];

  // Load saved preference from localStorage or default to AI
  const getSavedRoutePreference = (): MessageRoutingOption => {
    if (typeof window === "undefined") return messageRoutingOptions[1]; // Default to AI

    try {
      const savedRouteId = localStorage.getItem("clinicianMessageRoute");
      if (savedRouteId) {
        const savedOption = messageRoutingOptions.find(
          (option) => option.id === savedRouteId,
        );
        if (savedOption) return savedOption;
      }
    } catch (error) {
      // Error accessing localStorage
    }

    return messageRoutingOptions[1]; // Default to AI if no saved preference or error
  };

  const [selectedRoute, setSelectedRoute] = useState<MessageRoutingOption>(
    getSavedRoutePreference(),
  );

  // Save preference to localStorage when it changes
  const handleRouteChange = (newRoute: MessageRoutingOption) => {
    setSelectedRoute(newRoute);

    try {
      localStorage.setItem("clinicianMessageRoute", newRoute.id);
    } catch (error) {
      // Error saving to localStorage
    }
  };

  useEffect(() => {
    // Determine if the current user is a clinician
    // This logic might need adjustment based on how roles are stored in Clerk's user object
    
    let isClinicianUser = false;
    if (
      user &&
      user.publicMetadata &&
      Array.isArray(user.publicMetadata.role) &&
      user.publicMetadata.role.includes("clinician")
    ) {
      isClinicianUser = true;
    } else if (
      user &&
      user.publicMetadata &&
      typeof user.publicMetadata.role === "string" &&
      user.publicMetadata.role === "clinician"
    ) {
      isClinicianUser = true;
    }
    
    setIsClinicianView(isClinicianUser);
  }, [user]);

  // --- Scroll to Bottom ---
  const scrollToBottom = (behavior: ScrollBehavior = "smooth") => {
    if (behavior === "instant" && messagesContainerRef.current) {
      // For instant scroll, directly set scrollTop to ensure no animation
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    } else {
      chatEndRef.current?.scrollIntoView({ behavior });
    }
  };

  // Check if the user has scrolled up and should show the button
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } =
      messagesContainerRef.current;
    // Show button if user has scrolled up more than 100px from bottom
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    const shouldShowButton = !isNearBottom && scrollHeight > clientHeight;

    setShowScrollButton(shouldShowButton);

    // Calculate approximate number of messages not in view
    if (shouldShowButton) {
      // Estimate how many messages are below the viewport based on average message height
      // Assuming average message height is around 100px (very rough estimate)
      const avgMessageHeight = 100;
      const pixelsBelowViewport = scrollHeight - scrollTop - clientHeight;
      const estimatedMessagesBelow = Math.min(
        Math.ceil(pixelsBelowViewport / avgMessageHeight),
        99, // Cap at 99 to avoid UI issues
      );
      setUnreadMessagesCount(estimatedMessagesBelow);
    } else {
      setUnreadMessagesCount(0);
    }
  }, []);

  // Add scroll event listener to the messages container
  useEffect(() => {
    const messagesContainer = messagesContainerRef.current;
    if (messagesContainer) {
      messagesContainer.addEventListener("scroll", handleScroll);
      return () =>
        messagesContainer.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  // Scroll to bottom when messages change, but only if appropriate
  useEffect(() => {
    // Skip if no messages
    if (displayedMessages.length === 0) {
      return;
    }
    
    // When messages first load (initial load or switching patients), instantly jump to bottom
    if (isInitialLoad) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom("instant");
        setIsInitialLoad(false);
        handleScroll();
      }, 0);
      return;
    }
    
    // Skip any scrolling if we're still loading
    if (isLoadingHistory) {
      return;
    }
    
    // Only auto-scroll if we're not currently showing parameter forms or if user just sent a message
    const hasParameterForms = displayedMessages.some(msg => 
      msg.sender === "agent" && 
      msg.metadata &&
      typeof msg.metadata === 'object' &&
      (('missing_parameters' in msg.metadata) || 
       ('compound_missing_parameters' in msg.metadata) ||
       ('all_parameters' in msg.metadata))
    );
    
    // Auto-scroll only if:
    // 1. User just sent a message OR
    // 2. User is near the bottom already (and no parameter forms)
    const shouldAutoScroll = justSentMessage || (!hasParameterForms && !showScrollButton);
    
    if (shouldAutoScroll) {
      scrollToBottom("smooth");
    }
    
    // Check scroll position after a short delay
    setTimeout(handleScroll, 100);
  }, [displayedMessages, handleScroll, justSentMessage, showScrollButton, isInitialLoad, isLoadingHistory]);

  // Update conversation type and clear message history when patient selection changes
  useEffect(() => {
    if (!selectedPatientId) {
      setDisplayedMessages([]);
      if (conversationType !== "general") {
        setConversationType("general");
      }
    } else {
      setConversationType("patient");
    }
  }, [selectedPatientId, conversationType]);

  // --- API Interaction Handlers ---

  const fetchClinicianConversations = useCallback(async () => {
    if (!isClinicianView) return;
    setIsLoadingConversations(true);
    setError(null);
    try {
      const response =
        await apiClient.get<ClinicianConversationsResponseAPI>("/chat/history"); // No patient_id_filter
      const conversations = response.data.conversations || [];
      setClinicianConversations(conversations);
      setFilteredConversations(conversations); // Initialize filtered list with all conversations
    } catch (err) {
      // Error fetching clinician conversations
      setError(
        err instanceof Error ? err.message : "Could not load conversations.",
      );
      setClinicianConversations([]);
      setFilteredConversations([]);
    } finally {
      setIsLoadingConversations(false);
    }
  }, [isClinicianView]);

  // Add a ref to track if we're currently fetching
  const isFetchingRef = useRef(false);
  
  const fetchPatientChatHistory = useCallback(
    async (patientIdToFetch: string | null) => {
      if (!patientIdToFetch) {
        // No patientId provided, clearing messages
        setDisplayedMessages([]);
        return;
      }
      
      // Prevent duplicate calls for the same patient
      if (isLoadingHistory) {
        // Already loading history, skipping duplicate call
        return;
      }
      
      setIsLoadingHistory(true);
      isFetchingRef.current = true;
      setError(null);
      try {
        // Ensure patientIdToFetch is passed as a string to avoid type issues
        const safePatientId = String(patientIdToFetch);
        const endpoint = isClinicianView
          ? `/chat/history?patient_id_filter=${safePatientId}&limit=100`
          : "/chat/history?limit=100";

        // Clear cache headers to ensure fresh data
        const response = await apiClient.get<
          ChatHistoryResponseAPI | ClinicianConversationsResponseAPI
        >(endpoint, {
          headers: {
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        console.log("Full API response structure:", {
          hasData: !!response?.data,
          dataType: typeof response?.data,
          dataKeys: response?.data ? Object.keys(response.data) : [],
          messagesLength: response?.data && 'messages' in response.data ? (response.data as ChatHistoryResponseAPI).messages?.length : undefined,
          firstMessage: response?.data && 'messages' in response.data ? (response.data as ChatHistoryResponseAPI).messages?.[0] : undefined
        });

        if (response && response.data) {
          if (
            "messages" in response.data &&
            Array.isArray(response.data.messages)
          ) {
            // Retrieved messages from history endpoint

            const formattedMessages: ChatMessageDisplayItem[] =
              response.data.messages.map((msg) => {
                // Debug log to check sender types
                console.log("Message from API:", {
                  sender_type: msg.sender_type,
                  message_route: msg.message_route,
                  message: msg.message.substring(0, 50) + "..."
                });
                
                // Map uppercase sender types from DB to lowercase for display
                const senderMap: Record<string, "user" | "agent" | "patient" | "clinician" | "clinical_note"> = {
                  "USER": "user",
                  "AGENT": "agent",
                  "PATIENT": "patient",
                  "CLINICIAN": "clinician",
                  "CLINICAL_NOTE": "clinical_note"
                };
                
                return {
                  id: msg.message_id,
                  sender: senderMap[msg.sender_type] || msg.sender_type.toLowerCase() as any,
                  message: msg.message,
                  timestamp: msg.timestamp,
                  routedTo: msg.message_route as "patient" | "ai" | undefined,
                  metadata: msg.metadata, // Include metadata for clinical notes
                };
              });

            // Updating displayed messages
            console.log("Fetched messages:", formattedMessages.length, "messages");
            // Log any clinical notes to check their metadata
            formattedMessages.forEach((msg, index) => {
              if (msg.sender === "clinical_note") {
                console.log(`Clinical note message ${index}:`, {
                  id: msg.id,
                  deleted: msg.metadata?.deleted,
                  metadata: msg.metadata
                });
              }
            });
            setDisplayedMessages(formattedMessages);

            if (formattedMessages.length > 0) {
              const lastMessage =
                formattedMessages[formattedMessages.length - 1];
              setLastMessageSenders((prev) => ({
                ...prev,
                [patientIdToFetch]: lastMessage.sender,
              }));
              setLastMessageRoutes((prev) => ({
                ...prev,
                [patientIdToFetch]: lastMessage.routedTo,
              }));
            }
          } else if (
            "conversations" in response.data &&
            Array.isArray(response.data.conversations)
          ) {
            // Received unexpected conversation list instead of messages
            setDisplayedMessages([]);
          } else {
            // Received unexpected data structure from chat history API
            setError("Failed to parse chat history.");
            setDisplayedMessages([]);
          }
        } else {
          // Invalid response from chat history API
          setError("Failed to parse chat history.");
          setDisplayedMessages([]);
        }
      } catch (err) {
        // Error fetching chat history
        setError(
          err instanceof Error ? err.message : "Could not load chat history.",
        );
        setDisplayedMessages([]);
      } finally {
        setIsLoadingHistory(false);
        isFetchingRef.current = false;
      }
    },
    [isClinicianView],
  );

  // Function to fetch message history for all patients in conversation list
  const fetchAllPatientsLastMessage = useCallback(async () => {
    if (!isClinicianView || clinicianConversations.length === 0) return;

    // Limit concurrent requests to avoid overwhelming the server
    const batchSize = 5;
    const newSenderMap: { [patientId: string]: string } = {};

    // Process patients in batches
    for (let i = 0; i < clinicianConversations.length; i += batchSize) {
      const batch = clinicianConversations.slice(i, i + batchSize);

      // Create array of promises for this batch
      const fetchPromises = batch.map(async (convo) => {
        try {
          const endpoint = `/chat/history?patient_id_filter=${convo.patient_id}`;
          const response =
            await apiClient.get<ChatHistoryResponseAPI>(endpoint);

          if (
            response &&
            response.data &&
            "messages" in response.data &&
            Array.isArray(response.data.messages) &&
            response.data.messages.length > 0
          ) {
            // Get the last message
            const lastMessage =
              response.data.messages[response.data.messages.length - 1];
            // Map uppercase sender types from DB to lowercase
            const senderMap: Record<string, string> = {
              "USER": "user",
              "AGENT": "agent",
              "PATIENT": "patient",
              "CLINICIAN": "clinician",
              "CLINICAL_NOTE": "clinical_note"
            };
            
            return {
              patientId: convo.patient_id,
              lastMessageSender: senderMap[lastMessage.sender_type] || lastMessage.sender_type.toLowerCase(),
              lastMessageRoute: lastMessage.message_route,
            };
          }
          return null;
        } catch (err) {
          // Error fetching patient history
          return null;
        }
      });

      // Wait for this batch to complete
      const results = await Promise.all(fetchPromises);

      // Add results to the map
      results.forEach((result) => {
        if (result) {
          newSenderMap[result.patientId] = result.lastMessageSender;
        }
      });
    }

    // Update state with the combined results
    setLastMessageSenders(newSenderMap);
    
    // Also update routes
    const newRouteMap: { [patientId: string]: string | undefined } = {};
    results.forEach((result) => {
      if (result && result.lastMessageRoute !== undefined) {
        newRouteMap[result.patientId] = result.lastMessageRoute;
      }
    });
    setLastMessageRoutes(newRouteMap);
  }, [isClinicianView, clinicianConversations]);

  // Load selected patient from localStorage on component mount
  useEffect(() => {
    if (isClinicianView && !hasRestoredFromLocalStorage) {
      try {
        const savedPatientId = localStorage.getItem("selectedPatientId");
        const savedPatientName = localStorage.getItem("selectedPatientName");
        setHasRestoredFromLocalStorage(true); // Mark as restored regardless of whether we found data
        
        if (savedPatientId) {
          // Restoring selected patient ID from localStorage
          setSelectedPatientId(savedPatientId);
          if (savedPatientName) {
            setSelectedPatientName(savedPatientName);
          }
          setConversationType("patient");
          // Don't fetch here - let the chat history effect handle it after state is set
          // This avoids the closure issue where selectedPatientId is null
        }
      } catch (error) {
        // Error accessing localStorage for patient selection
        setHasRestoredFromLocalStorage(true); // Mark as restored even on error
      }
    }
  }, [isClinicianView, hasRestoredFromLocalStorage]);

  // Save selected patient to localStorage whenever it changes
  useEffect(() => {
    if (isClinicianView && selectedPatientId) {
      try {
        // Saving selected patient ID to localStorage
        localStorage.setItem("selectedPatientId", selectedPatientId);
        if (selectedPatientName) {
          localStorage.setItem("selectedPatientName", selectedPatientName);
        }
      } catch (error) {
        // Error saving to localStorage
      }
    }
  }, [isClinicianView, selectedPatientId, selectedPatientName]);

  // Removed focus/visibility event listeners to prevent unwanted refreshes
  // The chat will now only refresh when explicitly needed (e.g., when switching patients)

  // Consolidated data fetch effect - handles both clinician and patient scenarios
  useEffect(() => {
    // Main data fetch effect triggered
    
    // Only proceed if we have a user and have determined their role
    if (!user) {
      // No user yet, waiting...
      return;
    }

    // Add a small delay on initial load to prevent rapid-fire calls from React Strict Mode
    if (isInitializing) {
      // Initial load - adding delay to prevent rapid calls
      const timer = setTimeout(() => {
        setIsInitializing(false);
      }, 100);
      return () => clearTimeout(timer);
    }

    if (isClinicianView) {
      // Clinician flow
      
      // For clinicians, we need to wait for localStorage restoration
      if (!hasRestoredFromLocalStorage) {
        // Waiting for localStorage restoration...
        return;
      }
      
      // Only fetch conversations - handle patient chat in separate effect
      // Fetching clinician conversations only
      fetchClinicianConversations();
      
    } else {
      // Patient flow - fetching own history
      fetchPatientChatHistory(user.id);
    }
  }, [
    user,
    isClinicianView,
    hasRestoredFromLocalStorage,
    isInitializing,
    fetchClinicianConversations,
    fetchPatientChatHistory,
  ]);

  // Separate useEffect ONLY for patient chat history in clinician view
  useEffect(() => {
    if (
      isClinicianView && 
      hasRestoredFromLocalStorage && 
      !isInitializing &&
      selectedPatientId && 
      selectedPatientId !== lastFetchedPatientId
    ) {
      // Fetching chat history for selected patient
      fetchPatientChatHistory(selectedPatientId);
      setLastFetchedPatientId(selectedPatientId);
    } else if (
      isClinicianView && 
      hasRestoredFromLocalStorage && 
      !isInitializing &&
      !selectedPatientId
    ) {
      // No patient selected, clearing messages
      setDisplayedMessages([]);
      setLastFetchedPatientId(null);
    }
  }, [
    isClinicianView,
    hasRestoredFromLocalStorage, 
    isInitializing,
    selectedPatientId, 
    lastFetchedPatientId,
    fetchPatientChatHistory
  ]);

  // DISABLED: After fetching conversations, fetch the last message for each conversation
  // This was causing excessive API calls - TODO: implement more efficient last message fetching
  // useEffect(() => {
  //   if (clinicianConversations.length > 0) {
  //     fetchAllPatientsLastMessage();
  //   }
  // }, [clinicianConversations, fetchAllPatientsLastMessage]);

  // Filter and sort conversations based on search term and last message sender
  useEffect(() => {
    if (!clinicianConversations.length) {
      setFilteredConversations([]);
      return;
    }

    // First, filter based on search term if it exists
    let filtered = clinicianConversations;
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = clinicianConversations.filter(
        (convo) =>
          // Search by first name, last name, or both
          `${convo.patient_first_name} ${convo.patient_last_name}`
            .toLowerCase()
            .includes(lowerSearchTerm) ||
          convo.patient_first_name.toLowerCase().includes(lowerSearchTerm) ||
          convo.patient_last_name.toLowerCase().includes(lowerSearchTerm),
      );
    }

    // Then, sort the filtered list:
    // 1. First by red dot (messages routed to clinician at the top)
    // 2. Then by oldest last_message_at (oldest conversations at the top)
    const sorted = [...filtered].sort((a, b) => {
      // First, sort by messages needing clinician attention
      const aHasRedDot = 
        (lastMessageSenders[a.patient_id] === "patient" && 
         lastMessageRoutes[a.patient_id] !== "ai") ||
        (lastMessageSenders[a.patient_id] === "agent" && 
         lastMessageRoutes[a.patient_id] !== "patient");
      const bHasRedDot = 
        (lastMessageSenders[b.patient_id] === "patient" && 
         lastMessageRoutes[b.patient_id] !== "ai") ||
        (lastMessageSenders[b.patient_id] === "agent" && 
         lastMessageRoutes[b.patient_id] !== "patient");

      if (aHasRedDot && !bHasRedDot) return -1;
      if (!aHasRedDot && bHasRedDot) return 1;

      // If both have same last message sender status, sort by oldest message
      // Note: last_message_at could be undefined, handle that case
      if (!a.last_message_at) return -1; // No timestamp comes first
      if (!b.last_message_at) return 1; // No timestamp comes last

      // Convert to Date objects and compare (oldest first)
      const aDate = new Date(a.last_message_at);
      const bDate = new Date(b.last_message_at);
      return aDate.getTime() - bDate.getTime(); // Ascending time (oldest first)
    });

    setFilteredConversations(sorted);
  }, [searchTerm, clinicianConversations, lastMessageSenders, lastMessageRoutes]);

  const sendMessage = useCallback(
    async (messageToSend: string, additionalContext?: Record<string, any>) => {
      if (!messageToSend.trim()) return;

      const currentSender: "patient" | "clinician" = isClinicianView
        ? "clinician"
        : "patient";

      const userMessage: ChatMessageDisplayItem = {
        id: `temp-user-${Date.now()}`, // Temporary ID for optimistic update
        sender: currentSender,
        message: messageToSend,
        timestamp: new Date().toISOString(),
        routedTo:
          isClinicianView && selectedPatientId
            ? (selectedRoute.id as "patient" | "ai")
            : undefined,
      };
      setDisplayedMessages((prev) => [...prev, userMessage]);
      setInputMessage("");
      setIsLoadingResponse(true);
      setError(null);
      setJustSentMessage(true);

      // Move payload declaration outside try block so it's accessible in catch block
      
      // Check if the last message from AI has parameter collection metadata
      const lastAIMessage = displayedMessages
        .filter(msg => msg.sender === "agent" && msg.metadata)
        .slice(-1)[0];
      
      const parameterCollectionMetadata = {};
      if (lastAIMessage?.metadata) {
        // Extract parameter collection state from last AI message
        if (lastAIMessage.metadata.previous_parameters) {
          parameterCollectionMetadata['previous_parameters'] = lastAIMessage.metadata.previous_parameters;
        }
        if (lastAIMessage.metadata.current_intent_action_type) {
          parameterCollectionMetadata['current_intent_action_type'] = lastAIMessage.metadata.current_intent_action_type;
        }
        if (lastAIMessage.metadata.pending_compound_action) {
          parameterCollectionMetadata['pending_compound_action'] = lastAIMessage.metadata.pending_compound_action;
        }
      }
      
      const payload = {
        message: messageToSend,
        // Use direct context field in the payload instead of nested structure
        context: {
          userRole: currentSender,
          conversationType:
            conversationType ||
            (isClinicianView && !selectedPatientId ? "general" : "patient"),
          conversationHistory: displayedMessages.map((msg) => ({
            sender: msg.sender,
            message: msg.message,
            timestamp: msg.timestamp,
            metadata: msg.metadata, // Include metadata in conversation history
          })),
          timezone_offset: -new Date().getTimezoneOffset() / 60,
          ...(isClinicianView && selectedPatientId
            ? {
                currentPatientId: selectedPatientId,
                patient_id: selectedPatientId,
              }
            : {}),
          // Include parameter collection metadata directly in context
          ...parameterCollectionMetadata,
          // Include any additional context passed to sendMessage
          ...(additionalContext || {}),
        },
        message_route:
          isClinicianView && selectedPatientId
            ? selectedRoute.id.toLowerCase()
            : null,
      };

      // Sending message with payload

      try {
        // Sending chat message

        // Endpoint might need to change based on who is sending
        const response = await apiClient.post<{
          message: string;
          message_id: string;
          timestamp: string;
          metadata?: unknown;
        }>("/chat/messages", payload);

        // Response received from API

        // Check if this was a patient-routed message
        let isPatientRouted = false;
        if (
          response.data.metadata &&
          typeof response.data.metadata === "object" &&
          response.data.metadata !== null &&
          "patient_routed" in response.data.metadata
        ) {
          isPatientRouted =
            (response.data.metadata as { patient_routed?: boolean })
              .patient_routed === true;
        }

        // Check if message was patient-routed

        // Only add agent response if this wasn't a patient-routed message
        if (!isPatientRouted) {
          const agentMessage: ChatMessageDisplayItem = {
            id: response.data.message_id,
            sender: "agent",
            message: response.data.message,
            timestamp: response.data.timestamp,
            metadata: response.data.metadata, // Include metadata for compound actions
          };
          setDisplayedMessages((prev) => [...prev, agentMessage]);
        } else {
          // For patient-routed messages, add a small confirmation message
          const confirmationMessage: ChatMessageDisplayItem = {
            sender: "system",
            message: "Your message was sent to the patient.",
            timestamp: new Date().toISOString(),
          };
          setDisplayedMessages((prev) => [...prev, confirmationMessage]);
        }

        // Refetch after a short delay to get the real message IDs from the database
        // This ensures consistency between frontend and backend state
        setTimeout(() => {
          setJustSentMessage(false);
          
          // Check if the latest agent response has parameter forms before refreshing
          // Parameter forms are ephemeral and shouldn't be overwritten by database refresh
          const hasParameterFormsInLatestResponse = response.data.metadata &&
            typeof response.data.metadata === 'object' &&
            (('missing_parameters' in response.data.metadata) || 
             ('compound_missing_parameters' in response.data.metadata) ||
             ('all_parameters' in response.data.metadata));
          
          // Only refetch if the latest response doesn't have parameter forms (to preserve ephemeral UI state)
          if (!hasParameterFormsInLatestResponse && isClinicianView && selectedPatientId) {
            fetchPatientChatHistory(selectedPatientId);
          }
        }, 500);

        // Update last message sender and route state if this is a patient conversation
        if (selectedPatientId) {
          // If it was a patient-routed message, the last message is from clinician
          if (isPatientRouted) {
            setLastMessageSenders((prev) => ({
              ...prev,
              [selectedPatientId]: "clinician",
            }));
            setLastMessageRoutes((prev) => ({
              ...prev,
              [selectedPatientId]: "patient",
            }));
          } else {
            // Otherwise, the last message is from agent
            setLastMessageSenders((prev) => ({
              ...prev,
              [selectedPatientId]: "agent",
            }));
            // Agent messages without explicit route are for clinician
            setLastMessageRoutes((prev) => ({
              ...prev,
              [selectedPatientId]: undefined, // undefined means it's for clinician
            }));
          }
        }
      } catch (err: unknown) {
        // Error sending message

        // Enhanced error logging for debugging the 500 error
        if (err && typeof err === "object" && "response" in err) {
          const axiosErr = err as unknown;
          if (
            typeof axiosErr === "object" &&
            axiosErr !== null &&
            "response" in axiosErr &&
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            typeof (axiosErr as any).response === "object"
          ) {
            // API error occurred - check response details
          }
        }

        let errorMessage = "An error occurred when sending the message.";
        if (err && typeof err === "object") {
          if (
            "response" in err &&
            err.response &&
            typeof err.response === "object" &&
            "data" in err.response &&
            err.response.data &&
            typeof err.response.data === "object" &&
            "detail" in err.response.data
          ) {
            errorMessage = String(err.response.data.detail);
          } else if ("message" in err && typeof err.message === "string") {
            errorMessage = err.message;
          }
        }
        setError(errorMessage);

        // Add a clear error message to the chat for better UX
        setDisplayedMessages((prev) => [
          ...prev,
          {
            sender: "agent",
            message: `I'm sorry, there was an error processing your message. The server returned: ${errorMessage}`,
            timestamp: new Date().toISOString(),
          },
        ]);
      } finally {
        setIsLoadingResponse(false);
      }
    },
    [
      displayedMessages,
      isClinicianView,
      selectedPatientId,
      setLastMessageSenders,
      selectedRoute,
      conversationType,
      fetchPatientChatHistory,
    ],
  ); // Updated dependencies

  // Clinical Note handlers
  const handleGenerateClinicalNote = async () => {
    if (!selectedPatientId) {
      toast.error("Please select a patient first");
      return;
    }

    setIsGeneratingNote(true);
    try {
      const response = await apiClient.post("/clinical-notes/generate", {
        patient_id: selectedPatientId,
        note_type: "SOAP",
        // Look back 30 days for messages (adjust as needed)
        start_time: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      });

      setGeneratedClinicalNote(response.data);
      setShowClinicalNoteModal(true);
      
      // Show success toast with generation stats
      toast.success(
        `Clinical note generated in ${response.data.generation_time_ms}ms with ${Math.round(
          response.data.confidence_score * 100
        )}% confidence`
      );
      
      // Refresh chat messages to show the new clinical note
      if (selectedPatientId) {
        await fetchPatientChatHistory(selectedPatientId);
      }
    } catch (error: any) {
      console.error("Error generating clinical note:", error);
      toast.error(
        error.response?.data?.detail || "Failed to generate clinical note"
      );
    } finally {
      setIsGeneratingNote(false);
    }
  };

  const handleSaveClinicalNote = async (noteId: string, updatedSections: any) => {
    try {
      await apiClient.put(`/clinical-notes/${noteId}`, {
        sections: updatedSections,
      });
      
      // Update the local state
      if (generatedClinicalNote) {
        setGeneratedClinicalNote({
          ...generatedClinicalNote,
          note: {
            ...generatedClinicalNote.note,
            sections: updatedSections,
          },
        });
      }
      
      toast.success("Clinical note saved successfully");
    } catch (error: any) {
      console.error("Error saving clinical note:", error);
      toast.error(error.response?.data?.detail || "Failed to save clinical note");
      throw error; // Re-throw to handle in modal
    }
  };

  const handleApproveClinicalNote = async (noteId: string) => {
    try {
      await apiClient.post(`/clinical-notes/${noteId}/approve`);
      toast.success("Clinical note approved successfully");
      setShowClinicalNoteModal(false);
      
      // Refresh chat messages to ensure we have the latest
      if (selectedPatientId) {
        await fetchPatientChatHistory(selectedPatientId);
      }
    } catch (error: any) {
      console.error("Error approving clinical note:", error);
      toast.error(
        error.response?.data?.detail || "Failed to approve clinical note"
      );
      throw error; // Re-throw to handle in modal
    }
  };

  const handleSendClick = () => {
    sendMessage(inputMessage);
  };

  const formatSingleParameters = (parameters: Record<string, any>) => {
    const lines: string[] = ["I've provided the following information:"];
    
    Object.entries(parameters).forEach(([key, value]) => {
      if (key === 'patient_id') return; // Skip patient_id in display
      
      let displayValue = value;
      let displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      // Format dates
      if (key.includes('date') && value) {
        try {
          const date = new Date(value);
          displayValue = date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
          });
        } catch {
          displayValue = value;
        }
      }
      
      // Format times (be more specific to avoid affecting other fields)
      if ((key === 'preferred_time' || key === 'appointment_time') && value && typeof value === 'string') {
        try {
          const [hours, minutes] = value.split(':');
          const hour = parseInt(hours);
          const ampm = hour >= 12 ? 'PM' : 'AM';
          const displayHour = hour % 12 || 12;
          displayValue = `${displayHour}:${minutes} ${ampm}`;
        } catch {
          displayValue = value;
        }
      }
      
      lines.push(`• ${displayKey}: ${displayValue}`);
    });
    
    return lines.join('\n');
  };

  const formatCompoundParameters = (allParameters: Record<string, Record<string, any>>) => {
    const lines: string[] = ["I've provided the following information:"];
    
    Object.entries(allParameters).forEach(([actionKey, params]) => {
      const parts = actionKey.split('_');
      const actionIndex = parts[parts.length - 1]; // Get last part as index
      const actionType = parts.slice(0, -1).join('_'); // Join all parts except last as action type
      const actionName = actionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      lines.push("");
      lines.push(`${parseInt(actionIndex) + 1}. ${actionName}:`);
      
      Object.entries(params).forEach(([key, value]) => {
        if (key === 'patient_id') return; // Skip patient_id in display
        
        let displayValue = value;
        let displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        // Format dates
        if (key.includes('date') && value) {
          try {
            const date = new Date(value);
            displayValue = date.toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric', 
              year: 'numeric' 
            });
          } catch {
            displayValue = value;
          }
        }
        
        // Format times (be more specific to avoid affecting other fields)
        if ((key === 'preferred_time' || key === 'appointment_time') && value && typeof value === 'string') {
          try {
            const [hours, minutes] = value.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour % 12 || 12;
            displayValue = `${displayHour}:${minutes} ${ampm}`;
          } catch {
            displayValue = value;
          }
        }
        
        lines.push(`  • ${displayKey}: ${displayValue}`);
      });
    });
    
    return lines.join('\n');
  };

  const handleParameterSubmit = async (parameters: Record<string, any>, originalMetadata: any) => {
    // Create a user-friendly message
    const parametersMessage = formatSingleParameters(parameters);
    
    // Set a flag to indicate this is a parameter completion
    const contextWithParams = {
      completed_parameters: parameters,
      original_action_type: originalMetadata.action_type,
      is_parameter_completion: true
    };
    
    // Send the message with completed parameters
    await sendMessage(parametersMessage, contextWithParams);
    
    // Force refresh after parameter completion to clear ephemeral forms and show results
    setTimeout(() => {
      if (isClinicianView && selectedPatientId) {
        fetchPatientChatHistory(selectedPatientId);
      }
    }, 1000); // Slightly longer delay to ensure the action completes
  };

  const handleCompoundParameterSubmit = async (allParameters: Record<string, Record<string, any>>, originalMetadata: any) => {
    // Create a user-friendly message
    const parametersMessage = formatCompoundParameters(allParameters);
    
    // Set a flag to indicate this is a compound parameter completion
    const contextWithParams = {
      completed_compound_parameters: allParameters,
      chain_id: originalMetadata.chain_id,
      is_compound_parameter_completion: true
    };
    
    // Send the message with completed parameters
    await sendMessage(parametersMessage, contextWithParams);
    
    // Force refresh after compound parameter completion to clear ephemeral forms and show results
    setTimeout(() => {
      if (isClinicianView && selectedPatientId) {
        fetchPatientChatHistory(selectedPatientId);
      }
    }, 1000); // Slightly longer delay to ensure the action completes
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      sendMessage(inputMessage);
    }
  };

  const handleConversationSelect = (patientId: string) => {
    // Ensure patientId is a string
    const patientIdStr = String(patientId);
    // Selected patient for conversation

    // Clear messages first to prevent scrolling through old messages
    setDisplayedMessages([]);
    
    // Reset fetch tracking to force reload when switching patients
    setLastFetchedPatientId(null);
    
    // Reset initial load flag so the new conversation opens at bottom
    setIsInitialLoad(true);
    setSelectedPatientId(patientIdStr);

    // Get patient name from conversations list for localStorage
    const selectedPatient = clinicianConversations.find(
      (convo) => convo.patient_id === patientIdStr,
    );
    if (selectedPatient) {
      const fullName =
        `${selectedPatient.patient_first_name} ${selectedPatient.patient_last_name}`.trim();
      setSelectedPatientName(fullName);
      // Patient name set for display
    }
    // Fetch history for this patient, this will be triggered by useEffect on selectedPatientId change
  };

  return (
    <div className="-mx-6 -mt-6 flex flex-col overflow-hidden border border-gray-200 border-b-0 h-[calc(100vh-8.25rem)]">
      {/* Adjusted to precisely meet the navigation */}
      <div className="flex-grow flex overflow-hidden">
        {/* Ensure content doesn't exceed container */}
        {/* Left Panel: Conversations List (Clinician View Only) */}
        {isClinicianView && (
          <ResizablePane
            defaultWidth={320}
            minWidth={250}
            maxWidth={500}
            storageKey="clinician-conversation-pane-width"
            className="border-r border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden"
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold flex items-center">
                <Users className="mr-2 h-5 w-5" /> Patient Conversations
              </h2>
            </div>

            {/* Search Box for Patient Conversations */}
            <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search patients..."
                  className="pl-8 h-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm.length > 0 && (
                  <button
                    onClick={() => setSearchTerm("")}
                    className="absolute right-2.5 top-2.5 h-4 w-4 text-gray-500 hover:text-gray-900"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Scrollable Conversation List */}
            <div className="flex-grow overflow-y-auto">
              {isLoadingConversations ? (
                <p className="p-4 text-gray-500">Loading conversations...</p>
              ) : clinicianConversations.length === 0 ? (
                <p className="p-4 text-gray-500">
                  No patient conversations found.
                </p>
              ) : filteredConversations.length === 0 ? (
                <p className="p-4 text-gray-500">
                  No patients match your search.
                </p>
              ) : (
                <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredConversations.map((convo) => (
                    <li
                      key={convo.patient_id}
                      className={cn(
                        "p-3 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer",
                        selectedPatientId === convo.patient_id
                          ? "bg-gray-100 dark:bg-gray-800 font-semibold"
                          : "",
                      )}
                      onClick={() => handleConversationSelect(convo.patient_id)}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <span
                            className={`truncate ${selectedPatientId === convo.patient_id ? "font-medium" : ""}`}
                          >
                            {convo.patient_first_name} {convo.patient_last_name}
                          </span>
                          {/* Show red dot if the last message is routed to clinician and hasn't been responded to */}
                          {((lastMessageSenders[convo.patient_id] === "patient" && 
                            lastMessageRoutes[convo.patient_id] !== "ai") ||
                           (lastMessageSenders[convo.patient_id] === "agent" && 
                            lastMessageRoutes[convo.patient_id] !== "patient")) && (
                            <div
                              className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"
                              title="Message awaiting your response"
                            ></div>
                          )}
                        </div>
                      </div>
                      {convo.last_message_at && (
                        <p className="text-xs text-gray-500 truncate">
                          Last: {formatDateTime(convo.last_message_at)}
                        </p>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </ResizablePane>
        )}
        {/* Right Panel: Chat Interface */}
        <div className="flex flex-col flex-grow overflow-hidden">
          {/* Fixed Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <ConversationHeader
              isClinicianView={isClinicianView}
              conversationType={conversationType}
              patientName={
                selectedPatientId
                  ? clinicianConversations.find(
                      (c) => c.patient_id === selectedPatientId,
                    )?.patient_first_name || selectedPatientName
                  : undefined
              }
              messagesExist={displayedMessages.length > 0}
            />
            {isClinicianView && (
              <div className="flex items-center gap-2">
                {selectedPatientId && (
                  <>
                    <GenerateClinicalNoteButton
                      patientId={selectedPatientId}
                      disabled={isGeneratingNote || displayedMessages.length === 0}
                      onGenerate={handleGenerateClinicalNote}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log("Manual refresh triggered");
                        fetchPatientChatHistory(selectedPatientId);
                      }}
                      disabled={isLoadingHistory}
                      className="flex items-center gap-1"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={cn(
                          "lucide lucide-refresh-cw",
                          isLoadingHistory && "animate-spin"
                        )}
                      >
                        <path d="M21 2v6h-6"></path>
                        <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
                        <path d="M3 22v-6h6"></path>
                        <path d="M21 12a9 9 0 0 1-15 6.7L3 16"></path>
                      </svg>
                      Refresh
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsConversationDialogOpen(true)}
                  className="flex items-center gap-1"
                >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-plus-circle"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                New Conversation
              </Button>
              </div>
            )}
          </div>

          {/* Patient Disclaimer Banner (Fixed) */}
          {!isClinicianView && (
            <Alert
              variant="default"
              className="mx-4 mt-4 bg-blue-50 border-blue-300 text-blue-800"
            >
              <Bot className="h-4 w-4" />
              <AlertTitle>Informational Use Only</AlertTitle>
              <AlertDescription>
                This chat is for informational purposes only and cannot provide
                medical advice. Consult your healthcare provider for any medical
                concerns.
              </AlertDescription>
            </Alert>
          )}

          {/* Middle Scrollable Content Area - Flex-grow to take available space */}
          <div className="flex-grow flex flex-col overflow-hidden relative">
            {/* Empty State Message (No conversation selected) */}
            {isClinicianView &&
              !selectedPatientId &&
              conversationType !== "general" &&
              !isLoadingConversations && (
                <div className="flex-grow flex flex-col items-center justify-center p-8 space-y-4">
                  <div className="text-center max-w-lg">
                    <h2 className="text-xl font-semibold mb-2">
                      Choose a Conversation
                    </h2>
                    <p className="text-gray-500 mb-4">
                      Select an existing patient conversation from the left
                      panel or use the "New Conversation" button to start a
                      general chat with the AI assistant.
                    </p>
                    <p className="text-gray-500 text-sm">
                      The AI assistant can help with clinical guidelines,
                      medical information, and general healthcare questions.
                    </p>
                  </div>
                </div>
              )}

            {/* Chat Messages Area (Scrollable) */}
            {(!isClinicianView ||
              (isClinicianView && selectedPatientId) ||
              (isClinicianView && conversationType === "general")) && (
              <div
                className="flex-grow overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900 space-y-4 max-h-full relative"
                ref={messagesContainerRef}
              >
                {isLoadingHistory ? (
                  <p className="text-center text-gray-500">
                    Loading chat history...
                  </p>
                ) : displayedMessages.length === 0 && !error ? (
                  <p className="text-center text-gray-500">
                    {isClinicianView && selectedPatientId
                      ? "No messages in this conversation yet."
                      : isClinicianView && conversationType === "general"
                        ? "This is a new conversation. You can ask general medical questions or about clinical guidelines."
                        : "Start the conversation by typing a message below."}
                  </p>
                ) : (
                  displayedMessages.map((msg, index) => {
                    const prevMsg =
                      index > 0 ? displayedMessages[index - 1] : undefined;
                    const isGrouped = shouldGroupMessages(msg, prevMsg);
                    const showDate =
                      index === 0 ||
                      (msg.timestamp &&
                        prevMsg?.timestamp &&
                        formatMessageDate(msg.timestamp) !==
                          formatMessageDate(prevMsg.timestamp));
                    // Only clinician messages are from the current user
                    const isClinicianMessage = msg.sender === "clinician";

                    return (
                      <React.Fragment key={msg.id || index}>
                        {showDate && msg.timestamp && (
                          <div className="text-center my-4 relative">
                            <div className="absolute inset-0 flex items-center">
                              <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                            </div>
                            <div className="relative flex justify-center">
                              <span className="text-xs bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full">
                                {formatMessageDate(msg.timestamp)}
                              </span>
                            </div>
                          </div>
                        )}
                        <div
                          className={cn(
                            "flex flex-col",
                            isClinicianMessage ? "items-end" : "items-start",
                            isGrouped ? "mt-1" : "mt-4",
                          )}
                        >
                          <div
                            className={cn(
                              "p-3 rounded-lg max-w-[75%] transition-all",
                              isClinicianMessage
                                ? msg.routedTo === "patient"
                                  ? "bg-gradient-to-r from-green-600 to-green-700 text-white shadow-md"
                                  : "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md"
                                : msg.sender === "patient"
                                  ? "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100 border border-green-200 dark:border-green-700"
                                  : msg.sender === "clinical_note"
                                    ? "bg-purple-50 text-purple-900 dark:bg-purple-900/20 dark:text-purple-100 border border-purple-200 dark:border-purple-700"
                                  : msg.sender === "system"
                                    ? "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600"
                                    : "bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100",
                              isGrouped && isClinicianMessage
                                ? "rounded-tr-sm"
                                : "",
                              isGrouped && !isClinicianMessage
                                ? "rounded-tl-sm"
                                : "",
                              // Add distinctive styling for routing
                              isClinicianMessage &&
                                msg.routedTo === "patient" &&
                                "border-l-4 border-green-300 dark:border-green-400",
                              isClinicianMessage &&
                                msg.routedTo === "ai" &&
                                "border-l-4 border-blue-300 dark:border-blue-400",
                            )}
                          >
                            {/* Show sender label for patient messages */}
                            {msg.sender === "patient" && (
                              <div className="flex items-center gap-1 mb-1">
                                <SmilePlus className="h-4 w-4 text-green-600 dark:text-green-400" />
                                <p className="text-xs font-medium opacity-80">
                                  Patient → {
                                    msg.routedTo === "ai" ? "AI" : "Clinician"  // Patient messages default to clinician
                                  }
                                </p>
                              </div>
                            )}
                            {/* Show sender label for agent messages */}
                            {msg.sender === "agent" && (
                              <div className="flex items-center gap-1 mb-1">
                                <Bot className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                <p className="text-xs font-medium opacity-80">
                                  AI → {
                                    msg.routedTo === "patient" ? "Patient" : "Clinician"  // AI messages default to clinician
                                  }
                                </p>
                              </div>
                            )}
                            {/* Show sender label for system messages */}
                            {msg.sender === "system" && (
                              <div className="flex items-center gap-1 mb-1">
                                <AlertCircle className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                                <p className="text-xs font-medium opacity-80">
                                  System
                                </p>
                              </div>
                            )}
                            {/* Show sender label for clinician messages */}
                            {msg.sender === "clinician" && (
                              <div className="flex items-center gap-1 mb-1">
                                <Stethoscope className={cn(
                                  "h-4 w-4",
                                  msg.routedTo === "patient" 
                                    ? "text-green-200 dark:text-green-300" 
                                    : msg.routedTo === "ai"
                                    ? "text-blue-200 dark:text-blue-300"
                                    : "text-green-200 dark:text-green-300"  // Default to patient color
                                )} />
                                <p className="text-xs font-semibold">
                                  Clinician → {
                                    msg.routedTo === "patient" ? "Patient" : 
                                    msg.routedTo === "ai" ? "AI" : 
                                    msg.routedTo === "clinician" ? "Patient" :  // Fix: clinician->clinician means patient
                                    "Patient"  // Default for clinician messages
                                  }
                                </p>
                              </div>
                            )}
                            {/* Show clinical note */}
                            {msg.sender === "clinical_note" && msg.metadata && (
                              <div className="space-y-3">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <Badge 
                                      variant="outline" 
                                      className={msg.metadata.deleted 
                                        ? "bg-red-100 text-red-800 border-red-300" 
                                        : "bg-purple-100 text-purple-800 border-purple-300"}
                                    >
                                      Clinical Note → Clinician Only {msg.metadata.deleted && "(Deleted)"}
                                    </Badge>
                                    <span className="text-xs text-gray-600">
                                      {msg.metadata.note_type || "SOAP"}
                                    </span>
                                    {!msg.metadata.deleted && msg.metadata.confidence_score && (
                                      <span className="text-xs text-gray-500">
                                        {Math.round(msg.metadata.confidence_score * 100)}% confidence
                                      </span>
                                    )}
                                  </div>
                                  {msg.metadata.clinical_note_id && !msg.metadata.deleted && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="text-xs text-purple-600 hover:text-purple-800 hover:bg-purple-100"
                                      onClick={() => {
                                        // Navigate to clinical notes page with the specific note
                                        navigate(`/clinician/clinical-notes?noteId=${msg.metadata.clinical_note_id}`);
                                      }}
                                    >
                                      <FileText className="h-3 w-3 mr-1" />
                                      View Full Note
                                    </Button>
                                  )}
                                </div>
                                
                                {/* If deleted, show deletion info instead of content */}
                                {msg.metadata.deleted ? (
                                  <div className="text-sm text-gray-500 italic">
                                    <p>This clinical note has been deleted.</p>
                                    {msg.metadata.deleted_at && (
                                      <p className="text-xs mt-1">
                                        Deleted on {new Date(msg.metadata.deleted_at).toLocaleString()}
                                      </p>
                                    )}
                                  </div>
                                ) : (
                                  <>
                                    {/* Display SOAP sections */}
                                    {msg.metadata.sections && (
                                      <div className="space-y-2 text-sm">
                                        {msg.metadata.sections.subjective && (
                                          <div>
                                            <strong className="text-xs uppercase text-gray-600">Subjective:</strong>
                                            <p className="mt-1">{msg.metadata.sections.subjective}</p>
                                          </div>
                                        )}
                                        {msg.metadata.sections.objective && (
                                          <div>
                                            <strong className="text-xs uppercase text-gray-600">Objective:</strong>
                                            <p className="mt-1">{msg.metadata.sections.objective}</p>
                                          </div>
                                        )}
                                        {msg.metadata.sections.assessment && (
                                          <div>
                                            <strong className="text-xs uppercase text-gray-600">Assessment:</strong>
                                            <p className="mt-1">{msg.metadata.sections.assessment}</p>
                                          </div>
                                        )}
                                        {msg.metadata.sections.plan && (
                                          <div>
                                            <strong className="text-xs uppercase text-gray-600">Plan:</strong>
                                            <p className="mt-1">{msg.metadata.sections.plan}</p>
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </>
                                )}
                                
                                {/* Show clinician info */}
                                {msg.metadata.clinician_name && (
                                  <p className="text-xs text-gray-500 mt-2">
                                    Generated by {msg.metadata.clinician_name}
                                  </p>
                                )}
                              </div>
                            )}
                            {/* Regular message content (not for clinical notes) */}
                            {msg.sender !== "clinical_note" && (
                              <>
                                <p className="text-sm">
                                  {msg.message.split('\n').map((line, lineIndex) => {
                                  // Check if this line contains a URL
                                  const urlRegex = /(https?:\/\/[^\s]+)/g;
                                  const parts = line.split(urlRegex);
                                  
                                  return (
                                    <React.Fragment key={lineIndex}>
                                      {lineIndex > 0 && <br />}
                                      {parts.map((part, partIndex) => {
                                        if (part.match(urlRegex)) {
                                          return (
                                            <a
                                              key={partIndex}
                                              href={part}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="text-blue-500 hover:text-blue-700 underline"
                                            >
                                              {part}
                                            </a>
                                          );
                                        }
                                        return <span key={partIndex}>{part}</span>;
                                      })}
                                    </React.Fragment>
                                  );
                                })}
                                </p>
                                {/* Add action response handler for compound actions and parameter harvesting */}
                                {msg.metadata && msg.sender === "agent" && 
                                 typeof msg.metadata === 'object' &&
                                 (('action_type' in msg.metadata &&
                                  ('success' in msg.metadata || 'module' in msg.metadata)) ||
                                  'missing_parameters' in msg.metadata ||
                                  'compound_missing_parameters' in msg.metadata) && (
                                  <div className="mt-2">
                                    <ActionResponseHandler 
                                      metadata={msg.metadata}
                                      onParameterSubmit={(parameters) => {
                                        // Handle parameter submission
                                        handleParameterSubmit(parameters, msg.metadata);
                                      }}
                                      onCompoundParameterSubmit={(allParameters) => {
                                        // Handle compound parameter submission
                                        handleCompoundParameterSubmit(allParameters, msg.metadata);
                                      }}
                                      userRole="clinician"
                                      patientName={selectedPatientName || undefined}
                                    />
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                          {msg.timestamp &&
                            !shouldGroupMessages(
                              displayedMessages[index + 1] || {},
                              msg,
                            ) && (
                              <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 px-2">
                                {formatChatTimestamp(msg.timestamp)}
                              </span>
                            )}
                        </div>
                      </React.Fragment>
                    );
                  })
                )}
                {error && (
                  <Alert variant="destructive" className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div ref={chatEndRef} />
              </div>
            )}
            
            {/* Scroll to bottom button - positioned fixed at bottom of messages area */}
            {showScrollButton && (
              <button
                onClick={() => scrollToBottom("instant")}
                className="absolute bottom-20 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center z-10"
                aria-label="Scroll to bottom"
              >
                <ChevronDown className="h-5 w-5" />
                {unreadMessagesCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
                    {unreadMessagesCount}
                  </span>
                )}
              </button>
            )}
          </div>

          {/* Fixed Input Area (always at bottom) */}
          {(!isClinicianView ||
            (isClinicianView && selectedPatientId) ||
            (isClinicianView && conversationType === "general")) && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex flex-col space-y-3 bg-white dark:bg-gray-800">
              {/* Input row with integrated routing dropdown for patient conversations */}
              <div
                className={`flex ${isClinicianView && selectedPatientId ? "space-x-3" : ""}`}
              >
                {/* Show routing dropdown inline for clinician-patient conversations */}
                {isClinicianView && selectedPatientId && (
                  <div className="flex-shrink-0">
                    <MessageRoutingDropdown
                      selectedRoute={selectedRoute}
                      options={messageRoutingOptions}
                      onRouteChange={handleRouteChange}
                      disabled={isLoadingResponse}
                    />
                  </div>
                )}

                <div className="flex-grow flex items-center space-x-2">
                  <Textarea
                    placeholder={
                      isClinicianView && selectedPatientId
                        ? `Type your message to ${selectedRoute.id === "patient" ? "the patient" : "the AI assistant"}...`
                        : "Type your message here..."
                    }
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    rows={1}
                    className="flex-grow resize-none"
                    disabled={isLoadingResponse}
                  />
                  <Button
                    onClick={handleSendClick}
                    disabled={!inputMessage.trim() || isLoadingResponse}
                    className={cn(
                      isClinicianView &&
                        selectedPatientId &&
                        selectedRoute.id === "patient" &&
                        "bg-green-600 hover:bg-green-700",
                      isClinicianView &&
                        selectedPatientId &&
                        selectedRoute.id === "ai" &&
                        "bg-blue-600 hover:bg-blue-700",
                    )}
                  >
                    {isLoadingResponse ? (
                      "Sending..."
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Show a hint message about the current routing mode */}
              {isClinicianView && selectedPatientId && (
                <div
                  className={cn(
                    "text-xs px-3 py-2 rounded-md",
                    selectedRoute.id === "patient"
                      ? "bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                      : "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                  )}
                >
                  <span className="font-medium">
                    {selectedRoute.id === "patient"
                      ? "Direct Patient Message Mode: "
                      : "AI-Only Message Mode: "}
                  </span>
                  {selectedRoute.id === "patient"
                    ? "Your message will be sent directly to the patient."
                    : "Your message will only be processed by the AI and won't be visible to the patient."}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {/* Conversation Selection Dialog */}
      <NewConversationDialog
        isOpen={isConversationDialogOpen}
        onClose={() => setIsConversationDialogOpen(false)}
        onSelectConversation={(target: ConversationTarget) => {
          setConversationType(target.type);

          if (target.type === "patient" && target.patientId) {
            setSelectedPatientId(target.patientId);
            setSelectedPatientName(target.name);

            // Save to localStorage
            try {
              localStorage.setItem("selectedPatientId", target.patientId);
              localStorage.setItem("selectedPatientName", target.name || "");
            } catch (error) {
              // Error saving patient selection to localStorage
            }
          } else {
            // Clear patient ID for general conversations
            setSelectedPatientId(null);
            setSelectedPatientName(undefined);
            // Initialize empty conversation
            setDisplayedMessages([]);

            // Clear from localStorage
            try {
              localStorage.removeItem("selectedPatientId");
              localStorage.removeItem("selectedPatientName");
            } catch (error) {
              // Error removing patient selection from localStorage
            }
          }

          setIsConversationDialogOpen(false);
        }}
      />

      {/* Clinical Note Review Modal */}
      <ClinicalNoteReviewModal
        isOpen={showClinicalNoteModal}
        onClose={() => {
          setShowClinicalNoteModal(false);
          setGeneratedClinicalNote(null);
        }}
        patientId={selectedPatientId || ""}
        patientName={
          selectedPatientId
            ? clinicianConversations.find(
                (c) => c.patient_id === selectedPatientId
              )?.patient_first_name || selectedPatientName
            : undefined
        }
        generatedNote={generatedClinicalNote}
        onSave={handleSaveClinicalNote}
        onApprove={handleApproveClinicalNote}
      />
    </div>
  );
};

export default ChatPage;
