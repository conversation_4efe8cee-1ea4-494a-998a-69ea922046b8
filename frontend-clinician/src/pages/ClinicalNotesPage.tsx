import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { formatDateTime } from '@pulsetrack/shared-frontend';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/simple-alert-dialog';
import ClinicalNoteViewModal from '@/components/clinical-notes/ClinicalNoteViewModal';
import GeneralNoteModal from '@/components/notes/GeneralNoteModal';
import apiClient from '@/lib/apiClient';
import { Search, FileText, CheckCircle, Clock, Edit, Download, Plus, Trash2, StickyNote, MessageCircle, Info } from 'lucide-react';

interface ClinicalNote {
  id: string;
  patient_id: string;
  patient_name?: string;
  clinician_id: string;
  clinician_name?: string;
  note_type: string;
  status: 'draft' | 'reviewed' | 'approved' | 'amended';
  ai_confidence_score?: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
  approved_by?: string;
  sections: {
    subjective?: string;
    objective?: string;
    assessment?: string;
    plan?: string;
    additional_notes?: string;
    _metadata?: {
      key_findings?: string[];
      missing_information?: string[];
      extraction_timestamp?: string;
    };
  };
  suggested_icd10_codes?: Array<{
    code: string;
    description: string;
    confidence: number;
  }>;
  suggested_cpt_codes?: Array<{
    code: string;
    description: string;
    confidence: number;
  }>;
}

interface GeneralNote {
  id: string;
  patient_id: string;
  clinician_id: string;
  title?: string;
  content: string;
  note_type?: string;
  created_at: string;
  updated_at: string;
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  clinician?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

const ClinicalNotesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [notes, setNotes] = useState<ClinicalNote[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<ClinicalNote[]>([]);
  const [generalNotes, setGeneralNotes] = useState<GeneralNote[]>([]);
  const [filteredGeneralNotes, setFilteredGeneralNotes] = useState<GeneralNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [generalNotesLoading, setGeneralNotesLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [generalSearchQuery, setGeneralSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedNote, setSelectedNote] = useState<ClinicalNote | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedGeneralNote, setSelectedGeneralNote] = useState<GeneralNote | null>(null);
  const [isGeneralNoteModalOpen, setIsGeneralNoteModalOpen] = useState(false);
  const [deleteNoteId, setDeleteNoteId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('clinical');

  useEffect(() => {
    fetchClinicalNotes();
    fetchGeneralNotes();
  }, []);

  useEffect(() => {
    filterNotes();
  }, [notes, searchQuery, statusFilter]);

  useEffect(() => {
    filterGeneralNotes();
  }, [generalNotes, generalSearchQuery]);

  // Handle noteId query parameter
  useEffect(() => {
    const noteId = searchParams.get('noteId');
    if (noteId && notes.length > 0) {
      const note = notes.find(n => n.id === noteId);
      if (note) {
        handleViewNote(note);
        // Clear the query parameter after handling
        navigate('/clinician/clinical-notes', { replace: true });
      }
    }
  }, [searchParams, notes, navigate]);

  const fetchClinicalNotes = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/clinical-notes/', {
        params: {
          include_relations: true,
          limit: 100
        }
      });
      console.log('Clinical notes response:', response.data);
      setNotes(response.data.items || []);
    } catch (error) {
      console.error('Error fetching clinical notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGeneralNotes = async () => {
    try {
      setGeneralNotesLoading(true);
      const response = await apiClient.get('/notes/my-notes', {
        params: {
          limit: 100
        }
      });
      console.log('General notes response:', response.data);
      setGeneralNotes(response.data || []);
    } catch (error) {
      console.error('Error fetching general notes:', error);
    } finally {
      setGeneralNotesLoading(false);
    }
  };

  const filterNotes = () => {
    let filtered = [...notes];

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(note => note.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(note => 
        note.patient_name?.toLowerCase().includes(query) ||
        note.clinician_name?.toLowerCase().includes(query) ||
        note.note_type.toLowerCase().includes(query) ||
        JSON.stringify(note.sections).toLowerCase().includes(query)
      );
    }

    // Sort by created date (newest first)
    filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    setFilteredNotes(filtered);
  };

  const filterGeneralNotes = () => {
    let filtered = [...generalNotes];

    // Filter by search query
    if (generalSearchQuery) {
      const query = generalSearchQuery.toLowerCase();
      filtered = filtered.filter(note => 
        note.patient?.first_name?.toLowerCase().includes(query) ||
        note.patient?.last_name?.toLowerCase().includes(query) ||
        note.clinician?.first_name?.toLowerCase().includes(query) ||
        note.clinician?.last_name?.toLowerCase().includes(query) ||
        note.title?.toLowerCase().includes(query) ||
        note.content.toLowerCase().includes(query) ||
        note.note_type?.toLowerCase().includes(query)
      );
    }

    // Sort by created date (newest first)
    filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    setFilteredGeneralNotes(filtered);
  };

  const handleViewNote = (note: ClinicalNote) => {
    setSelectedNote(note);
    setIsReviewModalOpen(true);
  };

  const handleNoteUpdate = async (updatedNote: ClinicalNote) => {
    // Update the note in our local state
    setNotes(prevNotes => 
      prevNotes.map(note => 
        note.id === updatedNote.id ? updatedNote : note
      )
    );
    
    // If it's the selected note, update it too
    if (selectedNote?.id === updatedNote.id) {
      setSelectedNote(updatedNote);
    }
  };

  const handleViewGeneralNote = (note: GeneralNote) => {
    setSelectedGeneralNote(note);
    setIsGeneralNoteModalOpen(true);
  };

  const handleGeneralNoteUpdate = async (updatedNote: GeneralNote) => {
    // Update the note in our local state
    setGeneralNotes(prevNotes => 
      prevNotes.map(note => 
        note.id === updatedNote.id ? updatedNote : note
      )
    );
    
    // If it's the selected note, update it too
    if (selectedGeneralNote?.id === updatedNote.id) {
      setSelectedGeneralNote(updatedNote);
    }
  };

  const handleGeneralNoteDelete = async (noteId: string) => {
    // Remove the note from our local state
    setGeneralNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
    
    // Close the modal
    setIsGeneralNoteModalOpen(false);
    setSelectedGeneralNote(null);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, icon: <Edit className="w-3 h-3" />, className: '' },
      reviewed: { variant: 'default' as const, icon: <Clock className="w-3 h-3" />, className: 'bg-blue-100 text-blue-800 border-blue-200' },
      approved: { variant: 'default' as const, icon: <CheckCircle className="w-3 h-3" />, className: 'bg-green-100 text-green-800 border-green-200' },
      amended: { variant: 'secondary' as const, icon: <Edit className="w-3 h-3" />, className: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <Badge variant={config.variant} className={`flex items-center gap-1 ${config.className}`}>
        {config.icon}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getConfidenceBadge = (score?: number) => {
    if (!score) return null;
    
    const percentage = Math.round(score * 100);
    const variant = percentage >= 80 ? 'default' : percentage >= 60 ? 'secondary' : 'outline';
    const className = percentage >= 80 ? 'bg-green-100 text-green-800 border-green-200' : 
                     percentage >= 60 ? '' : '';
    
    return (
      <Badge variant={variant as 'default' | 'secondary' | 'outline'} className={`ml-2 ${className}`}>
        {percentage}% confidence
      </Badge>
    );
  };

  const handleExportPDF = async (noteId: string) => {
    // TODO: Implement PDF export endpoint in backend
    console.log('PDF export not yet implemented for note:', noteId);
    alert('PDF export functionality coming soon!');
  };

  const handleDeleteNote = (noteId: string) => {
    setDeleteNoteId(noteId);
  };

  const confirmDeleteNote = async () => {
    if (!deleteNoteId) return;

    setIsDeleting(true);
    try {
      await apiClient.delete(`/clinical-notes/${deleteNoteId}`);
      
      // Remove the note from local state
      setNotes(prevNotes => prevNotes.filter(note => note.id !== deleteNoteId));
      
      // Close the review modal if the deleted note was being viewed
      if (selectedNote?.id === deleteNoteId) {
        setIsReviewModalOpen(false);
        setSelectedNote(null);
      }
      
      // Show success message
      alert('Clinical note deleted successfully');
    } catch (error) {
      console.error('Error deleting clinical note:', error);
      const errorMessage = (error as any).response?.data?.detail || 'Failed to delete clinical note';
      alert(errorMessage);
    } finally {
      setIsDeleting(false);
      setDeleteNoteId(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Notes</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Review and manage all clinical documentation and general notes
          </p>
        </div>

        {/* Info Alert about Note Creation */}
        <div className="mb-6">
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-900 mb-1">
                  Note Creation
                </h3>
                <p className="text-sm text-blue-700 mb-3">
                  Both Clinical Notes and General Notes are generated through the Chat interface using templates. 
                  Use chat to create structured documentation that will appear here automatically.
                </p>
                <Button 
                  onClick={() => navigate('/clinician/chat')}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Go to Chat
                </Button>
              </div>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="clinical" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Clinical Notes ({filteredNotes.length})
            </TabsTrigger>
            <TabsTrigger value="general" className="flex items-center gap-2">
              <StickyNote className="w-4 h-4" />
              General Notes ({filteredGeneralNotes.length})
            </TabsTrigger>
          </TabsList>

          {/* Clinical Notes Tab */}
          <TabsContent value="clinical" className="space-y-6">
            {/* Filters and Search for Clinical Notes */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Search clinical notes by patient, clinician, or content..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="reviewed">Reviewed</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="amended">Amended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Clinical Notes Table */}
            <Card>
              <CardHeader>
                <CardTitle>Clinical Notes ({filteredNotes.length})</CardTitle>
              </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading clinical notes...</div>
            ) : filteredNotes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchQuery || statusFilter !== 'all' 
                  ? 'No notes found matching your filters' 
                  : 'No clinical notes yet'}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Patient</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Clinician</TableHead>
                      <TableHead>AI Score</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredNotes.map((note) => (
                      <TableRow key={note.id}>
                        <TableCell className="font-medium">
                          {note.patient_name || 'Unknown Patient'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{note.note_type}</Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(note.status)}</TableCell>
                        <TableCell>
                          {formatDateTime(note.created_at)}
                        </TableCell>
                        <TableCell>{note.clinician_name || 'Unknown'}</TableCell>
                        <TableCell>
                          {getConfidenceBadge(note.ai_confidence_score)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewNote(note)}
                              className="flex items-center gap-1"
                            >
                              <FileText className="w-4 h-4" />
                              View
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleExportPDF(note.id)}
                              className="flex items-center gap-1"
                            >
                              <Download className="w-4 h-4" />
                              PDF
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteNote(note.id)}
                              className="flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50"
                              title={note.status === 'approved' ? 'Cannot delete approved notes (backend will block this)' : 'Delete clinical note'}
                            >
                              <Trash2 className="w-4 h-4" />
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      {/* General Notes Tab */}
      <TabsContent value="general" className="space-y-6">
        {/* Search for General Notes */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search general notes by patient, content, or title..."
                    value={generalSearchQuery}
                    onChange={(e) => setGeneralSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* General Notes Table */}
        <Card>
          <CardHeader>
            <CardTitle>General Notes ({filteredGeneralNotes.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {generalNotesLoading ? (
              <div className="text-center py-8">Loading general notes...</div>
            ) : filteredGeneralNotes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {generalSearchQuery 
                  ? 'No notes found matching your search' 
                  : 'No general notes yet. Create notes from chat using templates!'}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Patient</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Content Preview</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredGeneralNotes.map((note) => (
                      <TableRow key={note.id}>
                        <TableCell className="font-medium">
                          {note.title || 'Untitled Note'}
                        </TableCell>
                        <TableCell>
                          {note.patient ? `${note.patient.first_name} ${note.patient.last_name}` : 'Unknown Patient'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{note.note_type || 'General'}</Badge>
                        </TableCell>
                        <TableCell>
                          {formatDateTime(note.created_at)}
                        </TableCell>
                        <TableCell className="max-w-[300px]">
                          <div className="truncate text-sm text-gray-600" title={note.content}>
                            {note.content}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewGeneralNote(note)}
                              className="flex items-center gap-1"
                            >
                              <FileText className="w-4 h-4" />
                              View
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

      {/* Review Modal */}
      {selectedNote && (
        <ClinicalNoteViewModal
          isOpen={isReviewModalOpen}
          onClose={() => {
            setIsReviewModalOpen(false);
            setSelectedNote(null);
          }}
          note={selectedNote}
          onUpdate={handleNoteUpdate}
          onDelete={(noteId) => {
            // Remove the note from local state
            setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
            // Close the modal
            setIsReviewModalOpen(false);
            setSelectedNote(null);
            alert('Clinical note deleted successfully');
          }}
        />
      )}

      {/* General Note Modal */}
      {selectedGeneralNote && (
        <GeneralNoteModal
          isOpen={isGeneralNoteModalOpen}
          onClose={() => {
            setIsGeneralNoteModalOpen(false);
            setSelectedGeneralNote(null);
          }}
          note={selectedGeneralNote}
          onUpdate={handleGeneralNoteUpdate}
          onDelete={handleGeneralNoteDelete}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteNoteId} onOpenChange={(open) => !open && setDeleteNoteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Clinical Note</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this clinical note? This action cannot be undone.
              The clinical note and all associated data will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              onClick={() => setDeleteNoteId(null)}
              disabled={isDeleting}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteNote}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ClinicalNotesPage;