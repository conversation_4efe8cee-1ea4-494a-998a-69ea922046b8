import React, { useState, useEffect } from "react";
import { useAuth, useUser } from "@clerk/clerk-react";
import apiClient from "@/lib/apiClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PatientAlertsCard } from "@/components/dashboard/cards/PatientAlertsCard";
import { SideEffectReportsCard } from "@/components/dashboard/cards/SideEffectReportsCard";
import { TodaysAppointmentsCard } from "@/components/dashboard/cards/TodaysAppointmentsCard";
// Removed QuickActionsCard import as requested
import PendingTasksCard from "@/components/dashboard/cards/PendingTasksCard"; // Corrected import
import { PatientActivityCard } from "@/components/dashboard/cards/PatientActivityCard";
import { DashboardTitle } from "@/components/dashboard/DashboardTitle";
import { UrgentItemsBanner } from "@/components/dashboard/UrgentItemsBanner";

interface UrgentItemsData {
  totalUrgentItems: number;
  criticalAlerts: number;
  highAlerts: number;
  overdueAppointments: number;
  criticalSideEffects: number;
}

const ClinicianDashboardPage: React.FC = () => {
  const { isLoaded: authIsLoaded, isSignedIn } = useAuth();
  const { isLoaded: userIsLoaded, user } = useUser();
  const [error] = useState<string | null>(null); // In a real app, setError would be used
  const [urgentItems, setUrgentItems] = useState<UrgentItemsData>({
    totalUrgentItems: 0,
    criticalAlerts: 0,
    highAlerts: 0,
    overdueAppointments: 0,
    criticalSideEffects: 0,
  });

  const fetchUrgentItems = async () => {
    if (!isSignedIn) return;
    
    try {
      // Fetch AI dashboard data to get urgent items
      const response = await apiClient.get("/dashboard/ai-prioritized");
      const data = response.data;
      
      // Extract urgent items data from the AI dashboard response
      const patientAlertsCard = data.prioritized_cards.find(
        (card: any) => card.card_type === "patient_alerts"
      );
      const appointmentsCard = data.prioritized_cards.find(
        (card: any) => card.card_type === "todays_appointments"
      );
      const sideEffectsCard = data.prioritized_cards.find(
        (card: any) => card.card_type === "side_effect_reports"
      );
      
      const urgentItemsData = {
        criticalAlerts: patientAlertsCard?.highlighted_data?.critical_patient_alerts || 0,
        highAlerts: patientAlertsCard?.highlighted_data?.high_patient_alerts || 0,
        overdueAppointments: appointmentsCard?.highlighted_data?.overdue_count || 0,
        criticalSideEffects: sideEffectsCard?.highlighted_data?.critical_count || 0,
        totalUrgentItems: 0
      };
      
      urgentItemsData.totalUrgentItems = 
        urgentItemsData.criticalAlerts + 
        urgentItemsData.highAlerts + 
        urgentItemsData.overdueAppointments + 
        urgentItemsData.criticalSideEffects;
      
      setUrgentItems(urgentItemsData);
    } catch (err) {
      console.error("Failed to fetch urgent items:", err);
    }
  };

  useEffect(() => {
    fetchUrgentItems();
    
    // Listen for alert updates
    const handleAlertUpdate = () => {
      fetchUrgentItems();
    };
    window.addEventListener('patientAlertUpdated', handleAlertUpdate);
    
    return () => {
      window.removeEventListener('patientAlertUpdated', handleAlertUpdate);
    };
  }, [isSignedIn]);

  if (!authIsLoaded || !userIsLoaded) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Please sign in to access the dashboard.</p>
      </div>
    );
  }

  const userName =
    user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : "Clinician";

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8 space-y-6">
      <DashboardTitle
        title={`Welcome back, ${userName}!`}
        subtitle="Your AI-optimized dashboard is ready."
      />

      {/* Urgent Items Banner */}
      <UrgentItemsBanner
        items={urgentItems}
        onViewAll={() => {
          // Navigate to appropriate page based on urgent item type
          console.log("View all urgent items");
        }}
      />

      {/* Main Dashboard Grid */}
      {userIsLoaded && user && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Column 1 */}
          <div className="lg:col-span-2 space-y-6">
            <TodaysAppointmentsCard />
            <PatientAlertsCard />
          </div>

          {/* Column 2 (Sidebar-like) */}
          <div className="space-y-6">
            <PendingTasksCard />
            <SideEffectReportsCard />
          </div>

          {/* Full-width section below the grid */}
          <div className="md:col-span-2 lg:col-span-3">
            <PatientActivityCard />
          </div>
        </div>
      )}

      {error && (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-6"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}
    </div>
  );
};

export default ClinicianDashboardPage;
