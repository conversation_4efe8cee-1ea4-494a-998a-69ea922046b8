import React, { useState, useEffect } from "react";
import { useAuth, useUser } from "@clerk/clerk-react";
import { Link } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PatientAlertsCard } from "@/components/dashboard/cards/PatientAlertsCard";
import { SideEffectReportsCard } from "@/components/dashboard/cards/SideEffectReportsCard";
import { TodaysAppointmentsCard } from "@/components/dashboard/cards/TodaysAppointmentsCard";
import PendingTasksCard from "@/components/dashboard/cards/PendingTasksCard";
import { RecentActivityFeed } from "@/components/dashboard/RecentActivityFeed";
import { UrgentItemsBanner } from "@/components/dashboard/UrgentItemsBanner";
import apiClient from "@/lib/apiClient";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";

interface PrioritizedCard {
  card_type: string;
  priority: number;
  reason?: string;
  highlighted_data?: Record<string, any>;
}

interface InsightItem {
  type: "recommendation" | "alert" | "trend" | "summary";
  message: string;
  severity: "info" | "warning" | "critical";
  related_card?: string;
}

interface AIPrioritizedDashboard {
  summary: string;
  prioritized_cards: PrioritizedCard[];
  insights: InsightItem[];
  task_counts: {
    pending_medication_requests: number;
    pending_lab_results: number;
    unread_patient_messages: number;
    pending_appointment_requests: number;
  };
  generated_at: string;
}

interface UrgentItemsData {
  totalUrgentItems: number;
  criticalAlerts: number;
  highAlerts: number;
  overdueAppointments: number;
  criticalSideEffects: number;
}

const ClinicianDashboardPageAI: React.FC = () => {
  const { isLoaded: authIsLoaded, isSignedIn } = useAuth();
  const { isLoaded: userIsLoaded, user } = useUser();
  const [dashboardData, setDashboardData] = useState<AIPrioritizedDashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chatStats, setChatStats] = useState<{ active: number; pending: number }>({ active: 0, pending: 0 });

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get<AIPrioritizedDashboard>("/dashboard/ai-prioritized");
      setDashboardData(response.data);
      
      // Fetch chat statistics - fallback to dashboard data if endpoint doesn't exist
      try {
        const chatResponse = await apiClient.get("/chat/stats");
        if (chatResponse.data) {
          setChatStats({
            active: chatResponse.data.active_conversations || 0,
            pending: chatResponse.data.pending_responses || 0
          });
        }
      } catch (chatErr: any) {
        // Fallback: use dashboard task counts as approximation
        if (response.data.task_counts) {
          setChatStats({
            active: response.data.task_counts.unread_patient_messages || 0,
            pending: Math.max(0, (response.data.task_counts.unread_patient_messages || 0) - 1)
          });
        }
      }
    } catch (err: any) {
      console.error("Failed to fetch AI-prioritized dashboard:", err);
      setError(err.response?.data?.detail || "Failed to load dashboard");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isSignedIn) {
      fetchDashboardData();
    }
    
    // Listen for alert updates
    const handleAlertUpdate = () => {
      if (isSignedIn) {
        fetchDashboardData();
      }
    };
    window.addEventListener('patientAlertUpdated', handleAlertUpdate);
    
    return () => {
      window.removeEventListener('patientAlertUpdated', handleAlertUpdate);
    };
  }, [isSignedIn]);

  if (!authIsLoaded || !userIsLoaded) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="flex justify-center items-center h-96">
        <p className="text-lg text-gray-600">Please sign in to access the dashboard.</p>
      </div>
    );
  }

  const userFullName = user?.firstName && user?.lastName
    ? `${user.firstName} ${user.lastName}${user.lastName?.includes('MD') ? '' : ', MD'}`
    : "Doctor";

  // Calculate urgent items from dashboard data
  const patientAlertsCard = dashboardData?.prioritized_cards.find((c) => c.card_type === "patient_alerts");
  const criticalAlerts = patientAlertsCard?.highlighted_data?.critical_patient_alerts || 0;
  const highAlerts = patientAlertsCard?.highlighted_data?.high_patient_alerts || 0;
  
  const urgentItemsData: UrgentItemsData = {
    criticalAlerts: criticalAlerts,
    highAlerts: highAlerts,
    overdueAppointments: dashboardData?.prioritized_cards.find((c) => c.card_type === "todays_appointments")?.highlighted_data?.overdue_count || 0,
    criticalSideEffects: dashboardData?.prioritized_cards.find((c) => c.card_type === "side_effect_reports")?.highlighted_data?.critical_count || 0,
    totalUrgentItems: 0
  };
  urgentItemsData.totalUrgentItems = urgentItemsData.criticalAlerts + urgentItemsData.highAlerts + urgentItemsData.overdueAppointments + urgentItemsData.criticalSideEffects;

  const renderCard = (cardType: string) => {
    const card = dashboardData?.prioritized_cards.find((c) => c.card_type === cardType);
    const isUrgent = card && card.priority <= 2;

    // Map card types to their components and routes
    const cardConfig = {
      patient_alerts: {
        component: <PatientAlertsCard />,
        route: "/clinician/alerts"
      },
      todays_appointments: {
        component: <TodaysAppointmentsCard />,
        route: "/clinician/appointments"
      },
      side_effect_reports: {
        component: <SideEffectReportsCard />,
        route: "/clinician/side-effects"
      },
      pending_tasks: {
        component: <PendingTasksCard />,
        route: "/clinician/dashboard" // or appropriate route for tasks
      },
    }[cardType];

    if (!cardConfig) return null;

    return (
      <Link to={cardConfig.route} className="block group">
        <div className={`relative transform transition-all duration-200 hover:scale-[1.02] hover:shadow-lg ${isUrgent ? 'ring-2 ring-red-500 ring-offset-2' : ''}`}>
          {isUrgent && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 z-10"
            >
              Urgent
            </Badge>
          )}
          <div className="cursor-pointer">
            {cardConfig.component}
          </div>
        </div>
      </Link>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Welcome Section */}
          <div className="mb-6">
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
              👋 Welcome back, {userFullName}!
            </h1>
            <p className="text-gray-600 mt-1">
              Your AI-optimized dashboard is ready.
            </p>
          </div>

          {/* Urgent Items Banner - Only show if there are urgent items */}
          {urgentItemsData.totalUrgentItems > 0 && (
            <Link to="/clinician/alerts" className="block mb-6">
              <UrgentItemsBanner 
                items={urgentItemsData}
                onViewAll={() => {
                  // Navigation is handled by Link, but we can keep this for the button
                }}
              />
            </Link>
          )}

          {/* Featured Chat Section - Primary Call to Action */}
          <Link to="/clinician/chat" className="block mb-6">
            <Card className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 border-purple-200 hover:from-indigo-100 hover:via-purple-100 hover:to-pink-100 transition-all duration-200 shadow-md hover:shadow-xl overflow-hidden relative">
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-indigo-200/20 rounded-full -mr-20 -mt-20" />
              <CardHeader className="pb-3 relative">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full shadow-md">
                      <MessageSquare className="h-6 w-6 text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-purple-700 to-indigo-700 bg-clip-text text-transparent font-semibold">
                      Patient Communication Hub
                    </span>
                  </CardTitle>
                  <Badge variant="outline" className="bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 border-purple-300">
                    AI-Enhanced
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0 relative">
                <p className="text-base mb-3 text-gray-700">Engage with patients through our intelligent chat system with AI assistance.</p>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2 text-sm bg-white/80 backdrop-blur px-3 py-2 rounded-lg border border-emerald-200">
                    <div className="h-2.5 w-2.5 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full shadow-sm"></div>
                    <span className="text-emerald-700 font-medium">{chatStats.active} active conversation{chatStats.active !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm bg-white/80 backdrop-blur px-3 py-2 rounded-lg border border-orange-200">
                    <div className="h-2.5 w-2.5 bg-gradient-to-br from-orange-400 to-red-500 rounded-full animate-pulse shadow-sm"></div>
                    <span className="text-orange-700 font-semibold">{chatStats.pending} awaiting response</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Main Dashboard Content */}
          {loading ? (
            // Loading State
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardContent className="p-6">
                    <Skeleton className="h-4 w-3/4 mb-4" />
                    <Skeleton className="h-40" />
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <Skeleton className="h-4 w-3/4 mb-4" />
                    <Skeleton className="h-40" />
                  </CardContent>
                </Card>
              </div>
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-6">
                    <Skeleton className="h-4 w-3/4 mb-4" />
                    <Skeleton className="h-32" />
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : error ? (
            // Error State
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6 text-center">
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={fetchDashboardData} variant="outline">
                  Retry
                </Button>
              </CardContent>
            </Card>
          ) : (
            // Main Dashboard Grid
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Primary Column - Critical Items */}
              <div className="lg:col-span-2 space-y-6">
                {/* Patient Alerts - Always First */}
                {renderCard("patient_alerts")}
                
                {/* Today's Appointments */}
                {renderCard("todays_appointments")}
                
                {/* Side Effect Reports */}
                {renderCard("side_effect_reports")}
              </div>

              {/* Secondary Column - Tasks and Actions */}
              <div className="space-y-6">
                {/* Pending Tasks */}
                {renderCard("pending_tasks")}

                {/* Quick Actions Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="/clinician/invite-patient">➕ Invite New Patient</a>
                    </Button>
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="/clinician/appointments">📅 Schedule Appointment</a>
                    </Button>
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="/clinician/medication-requests">💊 Review Medications</a>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Recent Activity - Full Width Below */}
          <div className="mt-8">
            <Link to="/clinician/patients" className="block group">
              <Card className="transform transition-all duration-200 hover:scale-[1.01] hover:shadow-lg cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Recent Activity</span>
                    <span className="text-sm font-normal text-muted-foreground group-hover:text-primary transition-colors">
                      View all patients →
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <RecentActivityFeed />
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </div>
  );
};

export default ClinicianDashboardPageAI;