import React from 'react';
import {
  Book<PERSON><PERSON>,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  Eye,
  FileText,
  Video,
  Link as LinkIcon,
  Plus,
  Calendar,
  Clock
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import apiClient from '@/lib/apiClient';
import EducationUploadModal from '@/components/education/EducationUploadModal';
import EducationEditModal from '@/components/education/EducationEditModal';
import DocumentViewerModal from '@/components/education/DocumentViewerModal';
import PatientAssignmentModal from '@/components/education/PatientAssignmentModal';

// Types for education materials
interface EducationMaterial {
  id: string;
  title: string;
  description?: string;
  type: 'PDF' | 'VIDEO' | 'LINK' | 'DOCUMENT';
  category?: string;
  content_url?: string;
  file_path?: string;
  duration_minutes?: number;
  is_public: boolean;
  clinic_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  views_count?: number;
  assignments_count?: number;
}

interface MaterialsStats {
  total_materials: number;
  public_materials: number;
  clinic_materials: number;
  total_assignments: number;
  most_assigned_material?: EducationMaterial;
}

// API base URL is handled by apiClient

const EducationMaterialsPage: React.FC = () => {
  const [materials, setMaterials] = React.useState<EducationMaterial[]>([]);
  const [stats, setStats] = React.useState<MaterialsStats | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [categoryFilter, setCategoryFilter] = React.useState<string>('all');
  const [typeFilter, setTypeFilter] = React.useState<string>('all');
  const [publicFilter, setPublicFilter] = React.useState<string>('all');
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showEditModal, setShowEditModal] = React.useState(false);
  const [editingMaterial, setEditingMaterial] = React.useState<EducationMaterial | null>(null);
  const [viewingMaterial, setViewingMaterial] = React.useState<{id: string; title: string} | null>(null);
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = React.useState(false);
  const [materialIdToAssign, setMaterialIdToAssign] = React.useState<string | null>(null);
  const [materialTitleToAssign, setMaterialTitleToAssign] = React.useState<string | null>(null);

  // Fetch materials
  const fetchMaterials = React.useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (categoryFilter !== 'all') params.append('category', categoryFilter);
      if (typeFilter !== 'all') params.append('type', typeFilter);
      if (publicFilter !== 'all') params.append('is_public', publicFilter);

      const response = await apiClient.get(`/education-materials/?${params}`);
      console.log('Education materials response:', response.data);
      console.log('Number of materials:', response.data.length);
      setMaterials(response.data);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to load materials';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, categoryFilter, typeFilter, publicFilter]);

  // Fetch stats
  const fetchStats = React.useCallback(async () => {
    try {
      const response = await apiClient.get('/education-materials/analytics/summary');
      setStats(response.data);
    } catch (err: unknown) {
      console.error('Failed to fetch stats:', err instanceof Error ? err.message : err);
    }
  }, []);

  React.useEffect(() => {
    fetchMaterials();
    // TODO: Fix analytics endpoint authentication
    // fetchStats();
  }, [fetchMaterials]);

  // Edit material
  const handleEditMaterial = (material: EducationMaterial) => {
    setEditingMaterial(material);
    setShowEditModal(true);
  };

  // Delete material
  const deleteMaterial = async (materialId: string) => {
    if (!confirm('Are you sure you want to delete this material? This action cannot be undone.')) {
      return;
    }

    try {
      await apiClient.delete(`/education-materials/${materialId}`);
      setMaterials((prev: EducationMaterial[]) => prev.filter((m: EducationMaterial) => m.id !== materialId));
      await fetchStats(); // Refresh stats
    } catch (err: unknown) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to delete material';
      alert(errorMessage);
    }
  };

  // Get icon for material type
  const getIconForType = (type: string) => {
    switch (type) {
      case 'PDF':
      case 'DOCUMENT':
        return FileText;
      case 'VIDEO':
        return Video;
      case 'LINK':
        return LinkIcon;
      default:
        return FileText;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get unique categories for filter
  const categories = Array.from(new Set(
    materials.map((m: EducationMaterial) => m.category).filter(Boolean)
  )) as string[];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BookOpen className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Education Materials</h1>
              <p className="text-sm text-gray-600">Manage and assign educational content to patients</p>
            </div>
          </div>
          <Button onClick={() => setShowUploadModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Material
          </Button>
        </div>
      </div>

      <div className="flex-1 px-6 py-6 space-y-6">
        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Materials</p>
                    <p className="text-2xl font-bold">{stats.total_materials}</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Public</p>
                    <p className="text-2xl font-bold">{stats.public_materials}</p>
                  </div>
                  <Eye className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Clinic Only</p>
                    <p className="text-2xl font-bold">{stats.clinic_materials}</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Assignments</p>
                    <p className="text-2xl font-bold">{stats.total_assignments}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search materials..."
                    value={searchQuery}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="PDF">PDF</SelectItem>
                  <SelectItem value="VIDEO">Video</SelectItem>
                  <SelectItem value="LINK">Link</SelectItem>
                  <SelectItem value="DOCUMENT">Document</SelectItem>
                </SelectContent>
              </Select>

              <Select value={publicFilter} onValueChange={setPublicFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Access" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Access</SelectItem>
                  <SelectItem value="true">Public</SelectItem>
                  <SelectItem value="false">Clinic Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Materials Grid */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading materials...</p>
            </div>
          </div>
        ) : materials.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No materials found</h3>
              <p className="text-muted-foreground text-center mb-4">
                {searchQuery || categoryFilter !== 'all' || typeFilter !== 'all' 
                  ? 'Try adjusting your filters or search terms.' 
                  : 'Get started by uploading your first educational material.'}
              </p>
              <Button onClick={() => setShowUploadModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Material
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {materials.map((material: EducationMaterial) => {
              const IconComponent = getIconForType(material.type);
              
              return (
                <Card key={material.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <IconComponent className="h-5 w-5 text-blue-600" />
                        <Badge variant={material.is_public ? "default" : "secondary"}>
                          {material.is_public ? 'Public' : 'Clinic Only'}
                        </Badge>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditMaterial(material)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setMaterialIdToAssign(material.id);
                            setMaterialTitleToAssign(material.title);
                            setIsAssignmentModalOpen(true);
                          }}>
                            <Users className="h-4 w-4 mr-2" />
                            Assign to Patients
                          </DropdownMenuItem>
                          {material.content_url && (
                            <DropdownMenuItem onClick={() => setViewingMaterial({id: material.id, title: material.title})}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Material
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => deleteMaterial(material.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <CardTitle className="text-lg">{material.title}</CardTitle>
                    {material.description && (
                      <CardDescription className="line-clamp-2">
                        {material.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-3">
                        {material.duration_minutes && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {material.duration_minutes}min
                          </div>
                        )}
                        {material.category && (
                          <Badge variant="outline" className="text-xs">
                            {material.category}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Created {formatDate(material.created_at)}</span>
                      <div className="flex items-center gap-2">
                        {material.views_count !== undefined && (
                          <span>{material.views_count} views</span>
                        )}
                        {material.assignments_count !== undefined && (
                          <span>{material.assignments_count} assigned</span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  
                  <CardFooter className="gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setMaterialIdToAssign(material.id);
                        setMaterialTitleToAssign(material.title);
                        setIsAssignmentModalOpen(true);
                      }}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Assign
                    </Button>
                    {material.content_url && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setViewingMaterial({id: material.id, title: material.title})}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      <EducationUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onSuccess={() => {
          fetchMaterials();
          // TODO: Fix analytics endpoint authentication
          // fetchStats();
        }}
      />

      {/* Edit Modal */}
      {editingMaterial && (
        <EducationEditModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingMaterial(null);
          }}
          onSuccess={() => {
            fetchMaterials();
            // TODO: Fix analytics endpoint authentication
            // fetchStats();
          }}
          materialId={editingMaterial.id}
          initialData={{
            title: editingMaterial.title,
            description: editingMaterial.description,
            category: editingMaterial.category,
            is_public: editingMaterial.is_public
          }}
        />
      )}

      {/* Document Viewer Modal */}
      {viewingMaterial && (
        <DocumentViewerModal
          isOpen={!!viewingMaterial}
          onClose={() => setViewingMaterial(null)}
          materialId={viewingMaterial.id}
          title={viewingMaterial.title}
        />
      )}

      {/* Patient Assignment Modal */}
      <PatientAssignmentModal
        isOpen={isAssignmentModalOpen}
        onClose={() => setIsAssignmentModalOpen(false)}
        materialId={materialIdToAssign || ''}
        materialTitle={materialTitleToAssign || ''}
        onSuccess={() => {
          console.log('Material assigned successfully! Refreshing materials list.');
          fetchMaterials();
          // TODO: Fix analytics endpoint authentication
          // fetchStats();
        }}
      />
    </div>
  );
};

export default EducationMaterialsPage;