import React, { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import PatientInvitationsList from "@/components/PatientInvitationsList"; // Import the new component
import apiClient from "@/lib/apiClient"; // Assuming apiClient handles auth
import axios from "axios";

const InvitePatientPage: React.FC = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshInvitations, setRefreshInvitations] = useState<
    (() => Promise<void>) | undefined
  >(undefined);

  // Memoize the onRefresh callback to prevent infinite loops
  const onRefresh = useCallback(() => {
    return (fetchFunc: () => Promise<void>) => {
      setRefreshInvitations(() => fetchFunc);
    };
  }, []); // Empty dependency array since this function never needs to change

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setError(null);
      setIsLoading(true);

      try {
        await apiClient.post("/patients/invitations", { email });

        toast.success("Invitation Sent", {
          description: `An invitation has been sent to ${email}.`,
        });
        setEmail("");
        // Refresh the invitations list
        if (refreshInvitations) {
          await refreshInvitations();
        }
      } catch (err) {
        console.error("Error sending patient invitation:", err);
        let errorDetail = "Failed to send invitation.";
        if (axios.isAxiosError(err) && err.response) {
          const backendErrorData = err.response.data;
          // Try to extract the detail message sent by FastAPI
          let specificDetail = "An unknown error occurred."; // Default detail
          if (
            typeof backendErrorData === "object" &&
            backendErrorData !== null &&
            backendErrorData.detail
          ) {
            specificDetail = backendErrorData.detail;
          } else if (typeof backendErrorData === "string") {
            // Handle cases where data might be a plain string
            specificDetail = backendErrorData;
          }

          // Handle specific HTTP status codes
          if (err.response.status === 400) {
            // Use the specific detail extracted from the backend response
            errorDetail =
              specificDetail ||
              "Invalid email or patient already exists/invited.";
          } else if (
            err.response.status === 401 ||
            err.response.status === 403
          ) {
            errorDetail = "You are not authorized to perform this action.";
          } else {
            // For other server errors, use the extracted detail or a generic message
            errorDetail = `An error occurred: ${specificDetail}`;
          }
        } else if (err instanceof Error) {
          errorDetail = err.message; // Handle non-HTTP errors
        }
        setError(errorDetail);
        toast.error("Invitation Failed", {
          description: errorDetail,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [email, refreshInvitations],
  );

  return (
    <div className="container mx-auto p-4 space-y-4">
      {/* Invite form */}
      <Card>
        <CardHeader>
          <CardTitle>Invite New Patient</CardTitle>
          <CardDescription>
            Enter the email address of the patient you wish to invite. They will
            receive an email with instructions to set up their account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="patient-email">Patient Email</Label>
              <Input
                id="patient-email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !email}
            className="w-full"
          >
            {isLoading ? "Sending Invitation..." : "Send Invitation"}
          </Button>
        </CardFooter>
      </Card>

      <PatientInvitationsList onRefresh={onRefresh} />
    </div>
  );
};

export default InvitePatientPage;
