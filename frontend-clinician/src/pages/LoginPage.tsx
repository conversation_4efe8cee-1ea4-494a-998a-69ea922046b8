import React from "react";
import { SignIn } from "@clerk/clerk-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"; // Assuming shadcn card is used for layout

const LoginPage: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Clinician Portal Access
          </CardTitle>
          <CardDescription className="text-center">
            Please sign in using your registered email and password.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SignIn routing="path" path="/login" />
        </CardContent>
        <CardFooter className="text-center text-sm text-gray-600">
          {/* Optional: Add links like "Forgot Password?" if needed later */}
        </CardFooter>
      </Card>
    </div>
  );
};

export default LoginPage;
