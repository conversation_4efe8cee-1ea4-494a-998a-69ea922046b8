import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>ert<PERSON>ircle, <PERSON>ader2, <PERSON><PERSON><PERSON>, Trash2, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import apiClient from "@/lib/apiClient";
import { format, parseISO, isValid } from "date-fns";
import { toast } from "sonner";
import { MedicationRequestForm } from "@/components/medication-requests/MedicationRequestForm";
import { AxiosError } from "axios";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface MedicationRequest {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  medication_name: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  notes?: string;
}

// This interface should match the one in MedicationRequestForm component
interface MedicationRequestFormData {
  patient_id: string;
  medication_name: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  notes?: string;
}

// Interface for the edit form data that includes the id field
interface MedicationRequestEditData extends MedicationRequestFormData {
  id: string;
}

interface ApiErrorResponse {
  detail?: string | Array<{ loc?: string[]; msg: string }>;
}

const MedicationRequestDetailPage: React.FC = () => {
  const { requestId } = useParams<{ requestId: string }>();
  const navigate = useNavigate();
  const [request, setRequest] = useState<MedicationRequest | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  useEffect(() => {
    const fetchRequestDetails = async () => {
      if (!requestId) {
        setError("Medication request ID is missing.");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await apiClient.get<MedicationRequest>(
          `/medication-requests/${requestId}/`,
        );
        setRequest(response.data);
      } catch (err: unknown) {
        console.error("Error fetching medication request details:", err);
        const axiosError = err as AxiosError<ApiErrorResponse>;
        setError(
          (axiosError.response?.data?.detail as string) ||
            "Failed to fetch medication request details",
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchRequestDetails();
  }, [requestId]);

  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleUpdateRequest = async (data: MedicationRequestFormData) => {
    if (!requestId) return;

    setIsSubmitting(true);
    try {
      const response = await apiClient.put<MedicationRequest>(
        `/medication-requests/${requestId}/`,
        data,
      );

      setRequest(response.data);
      toast.success("Medication request updated successfully");
      setIsEditModalOpen(false);
    } catch (error: unknown) {
      console.error("Error updating medication request:", error);

      const axiosError = error as AxiosError<ApiErrorResponse>;
      const errorDetail = axiosError.response?.data?.detail;
      let errorMessage = "An error occurred";

      if (typeof errorDetail === "string") {
        errorMessage = errorDetail;
      } else if (Array.isArray(errorDetail)) {
        errorMessage = errorDetail
          .map((err) => `${err.loc?.join(".") || "field"}: ${err.msg}`)
          .join(", ");
      }

      toast.error("Failed to update medication request", {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleApprove = async () => {
    if (!requestId) return;

    try {
      setIsSubmitting(true);
      // Use the new status update endpoint
      const response = await apiClient.put<MedicationRequest>(
        `/medication-requests/${requestId}/status`,
        {
          status: "Approved",
          notes: "Approved by clinician",
        },
      );

      setRequest(response.data);
      toast.success("Medication request approved successfully");
    } catch (error: unknown) {
      console.error("Error approving medication request:", error);
      const axiosError = error as AxiosError<ApiErrorResponse>;
      toast.error("Failed to approve request", {
        description:
          (axiosError.response?.data?.detail as string) || "An error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!requestId) return;

    try {
      setIsSubmitting(true);
      // Use the new status update endpoint
      const response = await apiClient.put<MedicationRequest>(
        `/medication-requests/${requestId}/status`,
        {
          status: "Rejected",
          notes: "Rejected by clinician",
        },
      );

      setRequest(response.data);
      toast.success("Medication request rejected successfully");
    } catch (error: unknown) {
      console.error("Error rejecting medication request:", error);
      const axiosError = error as AxiosError<ApiErrorResponse>;
      toast.error("Failed to reject request", {
        description:
          (axiosError.response?.data?.detail as string) || "An error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!requestId) return;
    setIsSubmitting(true);
    try {
      await apiClient.delete(`/medication-requests/${requestId}/`);
      toast.success("Medication request deleted successfully");
      navigate("/clinician/medication-requests");
    } catch (error: unknown) {
      console.error("Error deleting medication request:", error);
      const axiosError = error as AxiosError<ApiErrorResponse>;
      toast.error("Failed to delete request", {
        description:
          (axiosError.response?.data?.detail as string) || "An error occurred",
      });
    } finally {
      setIsSubmitting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Prepare data for edit form - use the MedicationRequestEditData interface
  const requestFormDataForEdit = request
    ? ({
        patient_id: request.patient.id,
        medication_name: request.medication_name,
        dosage: request.dosage,
        frequency: request.frequency,
        duration: request.duration,
        notes: request.notes,
        id: request.id, // This is needed for the form component's internal use
      } as MedicationRequestEditData)
    : undefined;

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>Medication Request Details</CardTitle>
          <CardDescription>
            View and manage medication request details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : request ? (
            <div className="space-y-6">
              {/* Basic Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-semibold">Patient:</span>{" "}
                  {request.patient.first_name} {request.patient.last_name}
                </div>
                <div>
                  <span className="font-semibold">Medication:</span>{" "}
                  {request.medication_name}
                </div>
                <div>
                  <span className="font-semibold">Requested:</span>
                  {(() => {
                    if (!request.created_at) return "Date not available";
                    try {
                      const date = parseISO(request.created_at);
                      return isValid(date)
                        ? format(date, "PPp")
                        : "Invalid Date";
                    } catch (error) {
                      console.error("Error parsing date:", error);
                      return "Invalid Date Format";
                    }
                  })()}
                </div>
                <div>
                  <span className="font-semibold">Status:</span>{" "}
                  <Badge
                    variant={
                      request.status?.toLowerCase() === "rejected"
                        ? "destructive"
                        : "outline"
                    }
                    className={
                      request.status?.toLowerCase() === "pending"
                        ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                        : request.status?.toLowerCase() === "approved"
                          ? "bg-green-100 text-green-800 border-green-200"
                          : "" // Use destructive variant styles for rejected
                    }
                  >
                    {request.status}
                  </Badge>
                </div>
              </div>

              {/* Medication Details */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Medication Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {request.dosage && (
                    <div>
                      <span className="font-medium">Dosage:</span>{" "}
                      {request.dosage}
                    </div>
                  )}
                  {request.frequency && (
                    <div>
                      <span className="font-medium">Frequency:</span>{" "}
                      {request.frequency}
                    </div>
                  )}
                  {request.duration && (
                    <div>
                      <span className="font-medium">Duration:</span>{" "}
                      {request.duration}
                    </div>
                  )}
                </div>
              </div>

              {/* Notes */}
              {request.notes && (
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Notes</h3>
                  <p>{request.notes}</p>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-wrap gap-2 mt-6">
                {request.status?.toLowerCase() === "pending" && (
                  <>
                    <Button
                      onClick={handleApprove}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        "Approve Request"
                      )}
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleReject}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        "Reject Request"
                      )}
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={handleEdit}
                  disabled={isSubmitting}
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  disabled={isSubmitting}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          ) : (
            <p>No medication request details found.</p>
          )}
        </CardContent>
      </Card>

      {/* Edit Request Modal */}
      {requestFormDataForEdit && (
        <MedicationRequestForm
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          onSubmit={handleUpdateRequest}
          isSubmitting={isSubmitting}
          medicationRequest={requestFormDataForEdit}
        />
      )}
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Medication Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this medication request? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MedicationRequestDetailPage;
