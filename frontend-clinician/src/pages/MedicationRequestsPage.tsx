import React, { useState, useEffect, use<PERSON>emo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Loader2, PlusCircle, ChevronsRight } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import apiClient from "@/lib/apiClient";
import { format, parseISO, isValid } from "date-fns";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { MedicationRequestForm } from "@/components/medication-requests/MedicationRequestForm";
import { AxiosError } from "axios";

interface MedicationRequest {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  medication_name: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  status: string;
  created_at: string;
}

interface PatientOption {
  id: string;
  name: string;
}

interface MedicationRequestFormData {
  patient_id: string;
  medication_name: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  notes?: string;
}

const MedicationRequestsPage: React.FC = () => {
  const [requests, setRequests] = useState<MedicationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const [selectedPatient, setSelectedPatient] = useState<string>("all");
  const [selectedMedication, setSelectedMedication] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("Pending");

  const [filteredRequests, setFilteredRequests] = useState<MedicationRequest[]>(
    [],
  );

  const fetchRequests = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get<MedicationRequest[]>(
        "/clinicians/medication-requests",
      );
      console.log("API Response Data:", response.data);
      setRequests(response.data);
    } catch (err: unknown) {
      console.error("Error fetching medication requests:", err);
      if (err && typeof err === "object" && "response" in err) {
        const axiosError = err as AxiosError<{ detail?: string }>;
        setError(
          axiosError.response?.data?.detail ||
            "Failed to fetch medication requests",
        );
      } else {
        setError("Failed to fetch medication requests");
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const patientOptions = useMemo(() => {
    const uniquePatients: PatientOption[] = [];
    const patientIds = new Set<string>();
    requests.forEach((req) => {
      if (!patientIds.has(req.patient.id)) {
        patientIds.add(req.patient.id);
        uniquePatients.push({
          id: req.patient.id,
          name: `${req.patient.first_name} ${req.patient.last_name}`,
        });
      }
    });
    uniquePatients.sort((a, b) => a.name.localeCompare(b.name));
    return [{ id: "all", name: "All Patients" }, ...uniquePatients];
  }, [requests]);

  const medicationOptions = useMemo(() => {
    const uniqueMedications = new Set<string>();
    requests.forEach((req) => {
      if (req.medication_name) {
        uniqueMedications.add(req.medication_name);
      }
    });
    const sortedMedications = Array.from(uniqueMedications).sort();
    return ["all", ...sortedMedications];
  }, [requests]);

  const statusOptions = [
    { value: "all", label: "All Statuses" },
    { value: "Pending", label: "Pending" },
    { value: "Approved", label: "Approved" },
    { value: "Rejected", label: "Rejected" },
  ];

  useEffect(() => {
    let results = requests;

    if (selectedPatient !== "all") {
      results = results.filter((req) => req.patient.id === selectedPatient);
    }

    if (selectedMedication !== "all") {
      results = results.filter(
        (req) => req.medication_name === selectedMedication,
      );
    }

    if (selectedStatus !== "all") {
      results = results.filter((req) => req.status === selectedStatus);
    }

    setFilteredRequests(results);
  }, [requests, selectedPatient, selectedMedication, selectedStatus]);

  const handleCreateRequest = async (data: MedicationRequestFormData) => {
    setIsSubmitting(true);
    try {
      await apiClient.post("/medication-requests", data);
      toast.success("Medication request created successfully");
      setIsCreateModalOpen(false);
      fetchRequests();
    } catch (error: unknown) {
      console.error("Error creating medication request:", error);
      const axiosError = error as AxiosError<{ detail?: string }>;
      toast.error("Failed to create request", {
        description: axiosError.response?.data?.detail || "An error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to safely format date
  const safeFormatDate = (dateString: string | undefined | null): string => {
    if (!dateString) {
      return "Date not available";
    }
    try {
      const date = parseISO(dateString);
      if (isValid(date)) {
        return format(date, "PPp"); // e.g., Sep 21, 2023, 4:30:00 PM
      } else {
        console.warn("Received invalid date string:", dateString);
        return "Invalid Date";
      }
    } catch (error) {
      console.error("Error parsing date string:", dateString, error);
      return "Invalid Date Format";
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Medication Requests</h1>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <PlusCircle className="h-4 w-4 mr-2" />
          New Request
        </Button>
      </div>

      {/* Filter Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 border rounded-lg">
        <div>
          <Label htmlFor="patient-filter">Patient</Label>
          <Select value={selectedPatient} onValueChange={setSelectedPatient}>
            <SelectTrigger id="patient-filter">
              <SelectValue placeholder="Filter by patient" />
            </SelectTrigger>
            <SelectContent>
              {patientOptions.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="medication-filter">Medication</Label>
          <Select
            value={selectedMedication}
            onValueChange={setSelectedMedication}
          >
            <SelectTrigger id="medication-filter">
              <SelectValue placeholder="Filter by medication" />
            </SelectTrigger>
            <SelectContent>
              {medicationOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option === "all" ? "All Medications" : option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="status-filter">Status</Label>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger id="status-filter">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Medication Requests</CardTitle>
          <CardDescription>
            View and manage medication requests from your patients
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : requests.length === 0 ? (
            <p className="text-center py-4 text-gray-500">
              No medication requests found.
            </p>
          ) : filteredRequests.length === 0 ? (
            <p className="text-center py-4 text-gray-500">
              No medication requests match the current filters.
            </p>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredRequests.map((request) => (
                <Card key={request.id}>
                  <CardHeader>
                    <CardTitle>
                      {request.patient.first_name} {request.patient.last_name}
                    </CardTitle>
                    <CardDescription>{request.medication_name}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {request.dosage && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Dosage
                        </p>
                        <p>{request.dosage}</p>
                      </div>
                    )}
                    {request.frequency && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Frequency
                        </p>
                        <p>{request.frequency}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Status
                      </p>
                      <Badge
                        variant={
                          request.status?.toLowerCase() === "rejected"
                            ? "destructive"
                            : "outline"
                        }
                        className={
                          request.status?.toLowerCase() === "pending"
                            ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                            : request.status?.toLowerCase() === "approved"
                              ? "bg-green-100 text-green-800 border-green-200"
                              : "" // Use destructive variant styles for rejected
                        }
                      >
                        {request.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Requested
                      </p>
                      <p>{safeFormatDate(request.created_at)}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        navigate(`/clinician/medication-requests/${request.id}`)
                      }
                    >
                      View Details
                      <ChevronsRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Request Modal */}
      <MedicationRequestForm
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateRequest}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default MedicationRequestsPage;
