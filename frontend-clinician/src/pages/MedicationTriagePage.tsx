import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import apiClient from "@/lib/apiClient"; // Import the centralized API client
import axios from "axios"; // Import axios for error type checking
// Assuming a MainLayout component exists
// import MainLayout from '@/components/layout/MainLayout';

// Define the structure for a medication request list item based on API response
// GET /api/clinicians/medication-requests
interface MedicationRequestItem {
  id: string; // Request ID
  patient_id: string; // Patient ID
  medication_name: string; // Or medication_details
  status: string; // Should be 'Pending' for this view
  requested_at: string; // ISO 8601
  // Add other relevant fields if returned by the API
}

// Define the structure for the paginated API response
interface PaginatedMedRequestResponse {
  items: MedicationRequestItem[];
  total: number;
  page: number; // Current page number (1-based)
  size: number; // Items per page
  pages: number; // Total number of pages
}

const ITEMS_PER_PAGE = 15; // Adjust items per page as needed

const MedicationTriagePage: React.FC = () => {
  const [requests, setRequests] = useState<MedicationRequestItem[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Removed API_BASE_URL and getAuthToken

  const fetchMedicationRequests = useCallback(async (page: number) => {
    setIsLoading(true);
    setError(null);
    // Token is handled by apiClient interceptor

    const limit = ITEMS_PER_PAGE;
    const skip = (page - 1) * limit;

    // Construct query parameters for Axios
    const params = {
      skip: skip,
      limit: limit,
      status: "Pending", // Crucial filter for this view
    };

    try {
      // Use apiClient for the GET request
      const response = await apiClient.get<PaginatedMedRequestResponse>(
        "/clinicians/medication-requests",
        { params },
      );
      // Auth header is added by interceptor

      const data = response.data; // Axios response data

      setRequests(data.items);
      setTotalItems(data.total);
      setTotalPages(data.pages || Math.ceil(data.total / limit));
      setCurrentPage(data.page || page);
    } catch (err) {
      console.error("Error fetching medication requests:", err);
      let errorDetail = "Failed to fetch medication requests.";
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.status === 401 || err.response.status === 403) {
          errorDetail = "Authentication failed. Please log in again.";
        } else {
          errorDetail =
            err.response.data?.detail || `Server error: ${err.response.status}`;
        }
      } else if (err instanceof Error) {
        errorDetail = err.message;
      }
      setError(errorDetail);
      setRequests([]); // Clear requests on error
      setTotalPages(0);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
    }
  }, []); // Dependency array is empty

  // Fetch data on initial mount and when page changes
  useEffect(() => {
    fetchMedicationRequests(currentPage);
  }, [fetchMedicationRequests, currentPage]);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    try {
      // More user-friendly format
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString; // Fallback
    }
  };

  return (
    // <MainLayout>
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <h1 className="text-2xl font-bold mb-6">Pending Medication Requests</h1>

      {isLoading && <p>Loading requests...</p>}
      {error && <p className="text-red-600 mb-4">Error: {error}</p>}

      {!isLoading && !error && (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Patient ID</TableHead>
                <TableHead>Medication</TableHead>
                <TableHead>Date Requested</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {requests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    No pending medication requests found.
                  </TableCell>
                </TableRow>
              ) : (
                requests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      {/* TODO: Fetch/Display Patient Name instead of ID. Requires API change or extra fetches. */}
                      {request.patient_id}
                    </TableCell>
                    <TableCell>{request.medication_name}</TableCell>
                    <TableCell>{formatDate(request.requested_at)}</TableCell>
                    <TableCell>
                      <Link
                        to={`/clinician/patients/${request.patient_id}`} // Link to patient detail page
                        className="text-blue-600 hover:underline"
                      >
                        View Patient
                      </Link>
                      {/* Add Approve/Deny buttons later if functionality is added */}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePreviousPage}
                disabled={currentPage === 1 || isLoading}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {totalPages} (Total Pending: {totalItems})
              </span>
              <Button
                variant="outline"
                onClick={handleNextPage}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
    // </MainLayout>
  );
};

export default MedicationTriagePage;
