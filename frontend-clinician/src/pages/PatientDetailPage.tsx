import React, { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";
import apiClient from "@/lib/apiClient"; // Import the centralized API client
import { AxiosError } from "axios"; // Import only the AxiosError type
import { toast } from "sonner"; // Import toast for notifications
import { SideEffectDetailModal } from "@/components/side-effects/SideEffectDetailModal";
import { AddSideEffectModal } from "@/components/side-effects/AddSideEffectModal";
import { AddWeightLogModal } from "@/components/weight/AddWeightLogModal";
import { formatWeight, formatWeightDisplay } from "@/lib/weightUtils";
import {
  MedicationRequestForm,
  MedicationRequestFormData,
} from "@/components/medication-requests/MedicationRequestForm";
import { AppointmentDetailModal } from "@/components/appointments/AppointmentDetailModal";
import {
  AppointmentForm,
  AppointmentFormData,
} from "@/components/appointments/AppointmentForm";
import { AlertDetailModal } from "@/components/alerts/AlertDetailModal";
import { PatientActivityFeed } from "@/components/dashboard/PatientActivityFeed";
// MainLayout is applied via routing in App.tsx

// --- Data Type Definitions (Align with actual API responses) ---

interface PatientProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  date_of_birth?: string | null;
  phone_number?: string | null;
  profile_photo_url?: string | null;
  height_cm?: number | null;
  goal_weight_kg?: number | null;
  goal_weight_date?: string | null;
}

interface WeightLogEntry {
  id: string;
  weight_kg: number;
  bmi?: number | null;
  log_date: string; // ISO 8601
}

interface WeightLogEntryResponse {
  id: string;
  weight_kg: number;
  bmi?: number | null;
  log_date: string; // ISO 8601
}

interface MedicationRequest {
  id: string;
  medication_name: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  status: string;
  requested_at: string;
}

// Assuming API returns paginated medication request data
interface PaginatedMedRequestResponse {
  items: MedicationRequest[];
  // Add other pagination fields if needed (total, page, etc.)
}

// --- Appointments Types ---
/**
 * Assumption: The appointments API returns either:
 *   - An array of Appointment objects, or
 *   - A paginated object: { items: Appointment[], ...pagination }
 * Example Appointment shape:
 * {
 *   id: string;
 *   date: string; // ISO 8601
 *   type: string;
 *   status: string;
 *   notes?: string;
 * }
 */
interface Appointment {
  id: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  status: "scheduled" | "completed" | "cancelled";
  reason?: string;
  patient_notes?: string;
}

interface SideEffectReport {
  id: string;
  description: string;
  severity: "minor" | "moderate" | "major";
  status: string;
  reported_at: string;
}

interface SideEffectReportWithPatient extends SideEffectReport {
  patient: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

// Assuming API returns paginated side effect data
interface PaginatedSideEffectResponse {
  items: SideEffectReport[];
  // Add other pagination fields if needed (total, page, etc.)
}

// Patient Alert interface
interface PatientAlert {
  id: string;
  patient_id: string;
  alert_type: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  status: "new" | "acknowledged" | "resolved" | "dismissed";
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  resolved_by_clinician_id?: string;
}

// --- Component ---

const PatientDetailPage: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const [activeTab, setActiveTab] = useState<string>("profile");

  // State for fetched data
  const [profile, setProfile] = useState<PatientProfile | null>(null);
  const [weightHistory, setWeightHistory] = useState<WeightLogEntry[]>([]);
  const [medRequests, setMedRequests] = useState<MedicationRequest[]>([]);
  const [sideEffects, setSideEffects] = useState<SideEffectReport[]>([]);
  const [patientAlerts, setPatientAlerts] = useState<PatientAlert[]>([]);
  // --- Appointments State ---
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isAppointmentsLoading, setIsAppointmentsLoading] =
    useState<boolean>(false);
  const [appointmentsError, setAppointmentsError] = useState<string | null>(
    null,
  );

  // Appointment Detail Modal State
  const [selectedAppointment, setSelectedAppointment] =
    useState<Appointment | null>(null);
  const [isAppointmentDetailModalOpen, setIsAppointmentDetailModalOpen] =
    useState(false);

  // Edit Appointment Modal State
  const [isEditAppointmentModalOpen, setIsEditAppointmentModalOpen] =
    useState(false);
  const [editAppointmentData, setEditAppointmentData] = useState<{
    id: string;
    patient_id: string;
    appointment_datetime: string;
    duration_minutes: number;
    appointment_type: string;
    reason?: string;
    patient_notes?: string;
  } | null>(null);

  const [isSubmittingEditAppointment, setIsSubmittingEditAppointment] =
    useState(false);

  const [selectedSideEffect, setSelectedSideEffect] =
    useState<SideEffectReportWithPatient | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isAddWeightModalOpen, setIsAddWeightModalOpen] = useState(false);
  const [isAddMedRequestModalOpen, setIsAddMedRequestModalOpen] =
    useState(false);
  const [isSubmittingMedRequest, setIsSubmittingMedRequest] = useState(false);

  // Alert Detail Modal State
  const [selectedAlert, setSelectedAlert] = useState<PatientAlert | null>(null);
  const [isAlertDetailModalOpen, setIsAlertDetailModalOpen] = useState(false);

  // State for loading and errors
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Removed API_BASE_URL and getAuthToken

  // --- API Fetching Functions ---

  const fetchPatientData = useCallback(async () => {
    if (!patientId) {
      setError("Patient ID not found in URL.");
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    // Token is handled by apiClient interceptor

    // Define fetch promises using apiClient
    const fetchProfile = apiClient.get<PatientProfile>(
      `/clinicians/patients/${patientId}`,
    );
    const fetchWeight = apiClient.get<WeightLogEntryResponse[]>(
      `/clinicians/patients/${patientId}/weight-log`,
    );
    const fetchMeds = apiClient.get<PaginatedMedRequestResponse>(
      "/clinicians/medication-requests",
      { params: { patient_id: patientId } },
    );
    const fetchSideEffects = apiClient.get<PaginatedSideEffectResponse>(
      `/clinicians/patients/${patientId}/side-effects`,
    );
    const fetchPatientAlerts = apiClient.get<{ items: PatientAlert[]; total: number }>(
      "/patient-alerts",
      { params: { patient_id: patientId, limit: 50 } },
    );
    // Appointments are fetched separately below using /appointments/?patient_id=...

    try {
      // Use Promise.allSettled to handle individual fetch failures
      const results = await Promise.allSettled([
        fetchProfile,
        fetchWeight,
        fetchMeds,
        fetchSideEffects,
        fetchPatientAlerts,
      ]);

      let fetchError = false;
      const errorMessages: string[] = [];

      // Process Profile Result
      if (results[0].status === "fulfilled") {
        const data = results[0].value.data;
        setProfile(data);
      } else {
        const reason = results[0].reason;
        const errorMessage =
          reason instanceof AxiosError
            ? `HTTP ${reason.response?.status}: ${reason.response?.data?.detail || reason.message}`
            : String(reason);
        console.error("Error fetching patient profile:", errorMessage);
        fetchError = true;
        errorMessages.push(`Profile: ${errorMessage}`);
      }

      // Process Weight History Result
      if (results[1].status === "fulfilled") {
        // Backend returns an array, not an object with items
        const data = results[1].value.data;
        const sortedData = (data || []).sort(
          (a: WeightLogEntry, b: WeightLogEntry) =>
            new Date(a.log_date).getTime() - new Date(b.log_date).getTime(),
        );
        setWeightHistory(sortedData);
      } else {
        const reason = results[1].reason;
        const errorMessage =
          reason instanceof AxiosError
            ? `HTTP ${reason.response?.status}: ${reason.response?.data?.detail || reason.message}`
            : String(reason);
        errorMessages.push(`Error fetching weight history: ${errorMessage}`);
        fetchError = true;
      }

      // Process Medication Requests Result
      if (results[2].status === "fulfilled") {
        const data = results[2].value.data;
        setMedRequests(Array.isArray(data) ? data : data.items || []);
      } else {
        const reason = results[2].reason;
        const errorMessage =
          reason instanceof AxiosError
            ? `HTTP ${reason.response?.status}: ${reason.response?.data?.detail || reason.message}`
            : String(reason);
        console.error("Error fetching medication requests:", errorMessage);
      }

      // Process Side Effects Result
      if (results[3].status === "fulfilled") {
        const data = results[3].value.data;
        setSideEffects(Array.isArray(data) ? data : data.items || []);
      } else {
        const reason = results[3].reason;
        const errorMessage =
          reason instanceof AxiosError
            ? `HTTP ${reason.response?.status}: ${reason.response?.data?.detail || reason.message}`
            : String(reason);
        console.error("Error fetching side effects:", errorMessage);
      }

      // Process Patient Alerts Result
      if (results[4].status === "fulfilled") {
        const data = results[4].value.data;
        setPatientAlerts(data.items || []);
      } else {
        const reason = results[4].reason;
        const errorMessage =
          reason instanceof AxiosError
            ? `HTTP ${reason.response?.status}: ${reason.response?.data?.detail || reason.message}`
            : String(reason);
        console.error("Error fetching patient alerts:", errorMessage);
      }

      if (fetchError) {
        // Set combined error only if critical data (like profile) failed
        setError(errorMessages.join("; "));
      }
    } catch (err: unknown) {
      // Catch potential errors in Promise.allSettled itself (unlikely)
      console.error("Unexpected error fetching patient data:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred.",
      );
    } finally {
      setIsLoading(false);
      setIsAppointmentsLoading(false);
    }
  }, [patientId]); // Dependency only on patientId

  // Fetch data when patientId changes
  useEffect(() => {
    fetchPatientData();
    // Fetch appointments separately for loading state
    setIsAppointmentsLoading(true);
    setAppointmentsError(null);
    // Fetch appointments for this patient using the same logic as AppointmentsPage
    apiClient
      .get<Appointment[]>("/appointments/", {
        params: { patient_id: patientId },
      })
      .then((resp) => {
        setAppointments(resp.data || []);
        setIsAppointmentsLoading(false);
      })
      .catch((err: unknown) => {
        if (err instanceof AxiosError) {
          const errorMessage = `HTTP ${err.response?.status}: ${err.response?.data?.detail || err.message}`;
          setAppointmentsError(errorMessage);
        } else if (err instanceof Error) {
          setAppointmentsError(err.message);
        } else {
          setAppointmentsError(
            "An unknown error occurred fetching appointments.",
          );
        }
        setIsAppointmentsLoading(false);
      });
  }, [fetchPatientData, patientId]);

  // --- Helper Functions ---
  // Used for date-only display (e.g., DOB, medication requests, side effects)
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString; // Fallback
    }
  };
  // Used for date & time display (e.g., appointments)
  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString; // Fallback
    }
  };

  // Format date for chart tooltip/axis
  const formatChartDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString; // Fallback
    }
  };

  // --- Render Logic ---

  if (isLoading) {
    return <div className="p-6">Loading patient details...</div>; // Or use LoadingSpinner
  }

  if (error && !profile) {
    // Show main error only if profile failed to load
    return (
      <div className="p-6 text-red-600">
        Error loading patient data: {error}
      </div>
    );
  }

  if (!profile) {
    // This case might be hit if profile fetch failed but wasn't the only error
    return (
      <div className="p-6">
        Patient profile could not be loaded. Check console for details.
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <h1 className="text-3xl font-bold mb-4">
        {profile.first_name} {profile.last_name}
      </h1>
      <p className="text-gray-600 mb-6">Patient ID: {profile.id}</p>
      {error && (
        <p className="text-orange-600 mb-4">
          Note: Some data sections might have failed to load ({error})
        </p>
      )}

      <Tabs
        defaultValue="profile"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="weight">Weight History</TabsTrigger>
          <TabsTrigger value="medications">Medication Requests</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
          <TabsTrigger value="side-effects">Side Effects</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          {/* <TabsTrigger value="chat">Chat</TabsTrigger> */}
        </TabsList>

        {/* Profile Tab Content */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Patient Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p>
                <strong>Email:</strong> {profile.email}
              </p>
              <p>
                <strong>Date of Birth:</strong>{" "}
                {formatDate(profile.date_of_birth)}
              </p>
              <p>
                <strong>Phone:</strong> {profile.phone_number || "N/A"}
              </p>
              <p>
                <strong>Height:</strong> {profile.height_cm ? `${profile.height_cm} cm` : "N/A"}
              </p>
              <p>
                <strong>Goal Weight:</strong> {profile.goal_weight_kg ? formatWeightDisplay(profile.goal_weight_kg) : "Not set"}
              </p>
              <p>
                <strong>Goal Date:</strong> {formatDate(profile.goal_weight_date) || "Not set"}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Weight History Tab Content */}
        <TabsContent value="weight">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Weight History</CardTitle>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setIsAddWeightModalOpen(true)}
                >
                  Add Weight Log
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div>Loading weight history...</div>
              ) : error ? (
                <div className="error">{error}</div>
              ) : (
                <>
                  {/* Goal Weight Progress Info */}
                  {profile?.goal_weight_kg && (
                    <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold mb-2">Goal Weight Progress</h4>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Current:</span>{" "}
                          <span className="font-medium">
                            {weightHistory.length > 0 
                              ? formatWeightDisplay(weightHistory[weightHistory.length - 1].weight_kg)
                              : "N/A"}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Goal:</span>{" "}
                          <span className="font-medium">{formatWeightDisplay(profile.goal_weight_kg)}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Target Date:</span>{" "}
                          <span className="font-medium">
                            {profile.goal_weight_date 
                              ? new Date(profile.goal_weight_date).toLocaleDateString()
                              : "Not set"}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={weightHistory}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="log_date"
                          tickFormatter={formatChartDate}
                        />
                        <YAxis
                          domain={[
                            (dataMin: number) => {
                              const min = Math.min(dataMin, profile?.goal_weight_kg || dataMin);
                              return Math.floor(min - 5);
                            },
                            (dataMax: number) => {
                              const max = Math.max(dataMax, profile?.goal_weight_kg || dataMax);
                              return Math.ceil(max + 5);
                            }
                          ]}
                          tickFormatter={(value) => value.toFixed(0)}
                          label={{
                            value: "Weight (kg)",
                            angle: -90,
                            position: "insideLeft",
                          }}
                        />
                        <Tooltip
                          labelFormatter={formatChartDate}
                          formatter={(value: number) => {
                            const formatted = formatWeight(value);
                            return [`${formatted.kg} kg / ${formatted.lbs} lbs`, "Weight"];
                          }}
                        />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="weight_kg"
                          stroke="#8884d8"
                          name="Weight (kg)"
                          connectNulls
                        />
                        {profile?.goal_weight_kg && (
                          <ReferenceLine
                            y={profile.goal_weight_kg}
                            stroke="#22c55e"
                            strokeDasharray="5 5"
                            strokeWidth={2}
                            label={{ 
                              value: `Goal: ${formatWeightDisplay(profile.goal_weight_kg)}`, 
                              position: "right",
                              fill: "#22c55e",
                              fontSize: 12
                            }}
                          />
                        )}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Weight Entry List Card */}
          {weightHistory.length > 0 && (
            <Card style={{ marginTop: "1.5rem" }}>
              <CardHeader>
                <CardTitle>Weight Entry List</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="weight-history-list">
                  {weightHistory.map((entry) => (
                    <li key={entry.id} className="weight-history-item">
                      <strong>
                        {new Date(entry.log_date).toLocaleDateString()}
                      </strong>
                      : {formatWeightDisplay(entry.weight_kg)}
                      {entry.bmi !== undefined && entry.bmi !== null && (
                        <span> (BMI: {entry.bmi.toFixed(2)})</span>
                      )}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Medication Requests Tab Content */}
        <TabsContent value="medications">
          {/*
                            Medication requests are fetched for the current patient via:
                            apiClient.get<PaginatedMedRequestResponse>('/clinicians/medication-requests', { params: { patient_id: patientId } });
                            This matches the logic on the main MedicationRequestsPage, but scoped to the patient.
                        */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Medication Requests</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-blue-500 text-blue-700 hover:bg-blue-50"
                  onClick={() => setIsAddMedRequestModalOpen(true)}
                >
                  Add Medication Request
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {medRequests.length === 0 ? (
                <p>No medication requests found for this patient.</p>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {medRequests.map((req) => (
                    <Card key={req.id}>
                      <CardHeader>
                        <CardTitle>{req.medication_name}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {req.dosage && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Dosage
                            </p>
                            <p>{req.dosage}</p>
                          </div>
                        )}
                        {req.frequency && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Frequency
                            </p>
                            <p>{req.frequency}</p>
                          </div>
                        )}
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Status
                          </p>
                          <Badge
                            variant={
                              req.status?.toLowerCase() === "rejected"
                                ? "destructive"
                                : req.status?.toLowerCase() === "approved"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {req.status}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Requested
                          </p>
                          <p>{formatDate(req.requested_at)}</p>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-end">
                        <Button
                          asChild
                          variant="outline"
                          size="sm"
                          className="border-gray-300 hover:bg-gray-100"
                        >
                          <a
                            href={`/clinician/medication-requests/${req.id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            View Details
                          </a>
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          {/* Add Medication Request Modal */}
          {isAddMedRequestModalOpen && profile && (
            <MedicationRequestForm
              isOpen={isAddMedRequestModalOpen}
              onClose={() => setIsAddMedRequestModalOpen(false)}
              isSubmitting={isSubmittingMedRequest}
              onSubmit={async (formData: MedicationRequestFormData) => {
                setIsSubmittingMedRequest(true);
                try {
                  await apiClient.post("/medication-requests", formData);
                  setIsAddMedRequestModalOpen(false);
                  // Refresh medication requests after adding
                  fetchPatientData();
                } catch (err: unknown) {
                  if (err instanceof AxiosError) {
                    toast.error(
                      `Failed to create medication request: ${err.response?.data?.detail || err.message}`,
                    );
                  } else if (err instanceof Error) {
                    toast.error(
                      `Failed to create medication request: ${err.message}`,
                    );
                  } else {
                    toast.error(
                      "Failed to create medication request due to an unknown error.",
                    );
                  }
                } finally {
                  setIsSubmittingMedRequest(false);
                }
              }}
              // Pre-fill and lock patient selection
              medicationRequest={{
                id: "",
                patient_id: profile.id,
                medication_name: "",
                dosage: "",
                frequency: "Once daily",
                duration: "",
                notes: "",
              }}
            />
          )}
        </TabsContent>
        {/* Appointments Tab Content */}
        <TabsContent value="appointments">
          <Card>
            <CardHeader>
              <CardTitle>Appointments</CardTitle>
            </CardHeader>
            <CardContent>
              {isAppointmentsLoading ? (
                <div>Loading appointments...</div>
              ) : appointmentsError ? (
                <div className="text-red-600">
                  Error loading appointments: {appointmentsError}
                </div>
              ) : appointments.length === 0 ? (
                <div>No appointments found for this patient.</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date &amp; Time</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {appointments.map((appt) => (
                      <TableRow key={appt.id}>
                        <TableCell>
                          {formatDateTime(appt.appointment_datetime)}
                        </TableCell>
                        <TableCell>{appt.appointment_type}</TableCell>
                        <TableCell>{appt.duration_minutes} min</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              appt.status === "cancelled"
                                ? "destructive"
                                : appt.status === "completed"
                                  ? "secondary"
                                  : "default"
                            }
                          >
                            {appt.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{appt.reason || "-"}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-gray-300 hover:bg-gray-100"
                            onClick={() => {
                              setSelectedAppointment(appt);
                              setIsAppointmentDetailModalOpen(true);
                            }}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        {/* Side Effects Tab Content */}
        {/* Side Effects Tab Content */}
        <TabsContent value="side-effects">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Side Effect Reports</CardTitle>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setIsAddModalOpen(true)}
                >
                  Add Side Effect
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {sideEffects.length === 0 ? (
                <p>No side effect reports found or failed to load.</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reported At</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sideEffects.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>{formatDate(report.reported_at)}</TableCell>
                        <TableCell>{report.severity}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              report.status?.toLowerCase() === "resolved"
                                ? "secondary"
                                : report.status?.toLowerCase() === "reviewed"
                                  ? "default"
                                  : "outline"
                            }
                          >
                            {report.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {report.description}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mr-2"
                            disabled={
                              report.status === "Reviewed" ||
                              report.status === "Resolved"
                            }
                            onClick={async () => {
                              try {
                                await apiClient.put(
                                  `/side-effects/${report.id}`,
                                  { status: "Reviewed" },
                                );
                                setSideEffects((prev) =>
                                  prev.map((r) =>
                                    r.id === report.id
                                      ? { ...r, status: "Reviewed" }
                                      : r,
                                  ),
                                );
                              } catch (err: unknown) {
                                if (err instanceof AxiosError) {
                                  toast.error(
                                    `Failed to mark as Reviewed: ${err.response?.data?.detail || err.message}`,
                                  );
                                } else if (err instanceof Error) {
                                  toast.error(
                                    `Failed to mark as Reviewed: ${err.message}`,
                                  );
                                } else {
                                  toast.error(
                                    "Failed to mark as Reviewed due to an unknown error.",
                                  );
                                }
                              }
                            }}
                          >
                            Mark as Reviewed
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mr-2"
                            disabled={report.status === "Resolved"}
                            onClick={async () => {
                              try {
                                await apiClient.put(
                                  `/side-effects/${report.id}`,
                                  { status: "Resolved" },
                                );
                                setSideEffects((prev) =>
                                  prev.map((r) =>
                                    r.id === report.id
                                      ? { ...r, status: "Resolved" }
                                      : r,
                                  ),
                                );
                              } catch (err: unknown) {
                                if (err instanceof AxiosError) {
                                  toast.error(
                                    `Failed to mark as Resolved: ${err.response?.data?.detail || err.message}`,
                                  );
                                } else if (err instanceof Error) {
                                  toast.error(
                                    `Failed to mark as Resolved: ${err.message}`,
                                  );
                                } else {
                                  toast.error(
                                    "Failed to mark as Resolved due to an unknown error.",
                                  );
                                }
                              }
                            }}
                          >
                            Mark as Resolved
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => {
                              setSelectedSideEffect({
                                ...report,
                                patient: {
                                  id: profile.id,
                                  first_name: profile.first_name,
                                  last_name: profile.last_name,
                                },
                                severity: report.severity as
                                  | "minor"
                                  | "moderate"
                                  | "major",
                              });
                              setIsDetailModalOpen(true);
                            }}
                          >
                            Edit
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab Content */}
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Patient Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              {patientAlerts.length === 0 ? (
                <p>No alerts found for this patient.</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {patientAlerts.map((alert) => (
                      <TableRow 
                        key={alert.id}
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => {
                          setSelectedAlert(alert);
                          setIsAlertDetailModalOpen(true);
                        }}
                      >
                        <TableCell>{formatDate(alert.created_at)}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {alert.alert_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              alert.severity === "critical"
                                ? "destructive"
                                : alert.severity === "high"
                                  ? "default"
                                  : alert.severity === "medium"
                                    ? "secondary"
                                    : "outline"
                            }
                          >
                            {alert.severity.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              alert.status === "new"
                                ? "destructive"
                                : alert.status === "acknowledged"
                                  ? "secondary"
                                  : alert.status === "resolved"
                                    ? "outline"
                                    : "outline"
                            }
                          >
                            {alert.status.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <p className="truncate">{alert.description}</p>
                        </TableCell>
                        <TableCell>
                          {alert.status === "new" && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async (e) => {
                                e.stopPropagation();
                                try {
                                  await apiClient.patch(
                                    `/patient-alerts/${alert.id}`,
                                    { status: "acknowledged" },
                                  );
                                  setPatientAlerts((prev) =>
                                    prev.map((a) =>
                                      a.id === alert.id
                                        ? { ...a, status: "acknowledged" }
                                        : a,
                                    ),
                                  );
                                  toast.success("Alert acknowledged");
                                } catch (err: unknown) {
                                  if (err instanceof AxiosError) {
                                    toast.error(
                                      `Failed to acknowledge alert: ${err.response?.data?.detail || err.message}`,
                                    );
                                  } else {
                                    toast.error("Failed to acknowledge alert");
                                  }
                                }
                              }}
                            >
                              Acknowledge
                            </Button>
                          )}
                          {alert.status === "acknowledged" && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async (e) => {
                                e.stopPropagation();
                                try {
                                  await apiClient.patch(
                                    `/patient-alerts/${alert.id}`,
                                    { status: "resolved" },
                                  );
                                  setPatientAlerts((prev) =>
                                    prev.map((a) =>
                                      a.id === alert.id
                                        ? { ...a, status: "resolved" }
                                        : a,
                                    ),
                                  );
                                  toast.success("Alert resolved");
                                } catch (err: unknown) {
                                  if (err instanceof AxiosError) {
                                    toast.error(
                                      `Failed to resolve alert: ${err.response?.data?.detail || err.message}`,
                                    );
                                  } else {
                                    toast.error("Failed to resolve alert");
                                  }
                                }
                              }}
                            >
                              Resolve
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab Content */}
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Patient Activity History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[600px] overflow-y-auto">
                <PatientActivityFeed patientId={patientId} limit={50} showHeader={false} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optional Chat Tab Content Placeholder */}
        {/* <TabsContent value="chat">
                        <Card>
                            <CardHeader><CardTitle>Chat History</CardTitle></CardHeader>
                            <CardContent>
                                <p>Chat history component placeholder.</p>
                            </CardContent>
                        </Card>
                    </TabsContent> */}
      </Tabs>
      {selectedSideEffect && (
        <SideEffectDetailModal
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          sideEffect={selectedSideEffect}
          onDelete={(reportId: string) => {
            // Remove the deleted side effect from the list
            setSideEffects((prev) => prev.filter((r) => r.id !== reportId));
            // Close the modal
            setIsDetailModalOpen(false);
            // Show success toast
            toast.success("Side effect report deleted successfully");
          }}
          onEdit={(updated: SideEffectReportWithPatient) => {
            // Update the side effect in the list
            setSideEffects((prev) =>
              prev.map((r) => (r.id === updated.id ? updated : r)),
            );
            // Close the modal
            setIsDetailModalOpen(false);
            // Show success toast
            toast.success("Side effect report updated successfully");
          }}
          onStatusUpdate={async (reportId: string, newStatus: string) => {
            try {
              await apiClient.put(`/side-effects/${reportId}`, {
                status: newStatus,
              });
              setSideEffects((prev) =>
                prev.map((r) =>
                  r.id === reportId ? { ...r, status: newStatus } : r,
                ),
              );
              setIsDetailModalOpen(false);
            } catch (err: unknown) {
              if (err instanceof AxiosError) {
                toast.error(
                  `Failed to update status: ${err.response?.data?.detail || err.message}`,
                );
              } else if (err instanceof Error) {
                toast.error(`Failed to update status: ${err.message}`);
              } else {
                toast.error("Failed to update status due to an unknown error.");
              }
            }
          }}
        />
      )}
      {isAddModalOpen && profile && (
        <AddSideEffectModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          fixedPatient={{
            id: profile.id,
            first_name: profile.first_name,
            last_name: profile.last_name,
          }}
          onSuccess={() => {
            setIsAddModalOpen(false);
            // Refresh side effects after adding
            fetchPatientData();
          }}
        />
      )}
      {isAddWeightModalOpen && profile && (
        <AddWeightLogModal
          isOpen={isAddWeightModalOpen}
          onClose={() => setIsAddWeightModalOpen(false)}
          fixedPatient={{
            id: profile.id,
            first_name: profile.first_name,
            last_name: profile.last_name,
          }}
          onSuccess={() => {
            setIsAddWeightModalOpen(false);
            // Refresh weight logs after adding
            fetchPatientData();
          }}
        />
      )}
      {/* Appointment Detail Modal */}
      <AppointmentDetailModal
        appointment={selectedAppointment}
        isOpen={isAppointmentDetailModalOpen}
        onClose={() => setIsAppointmentDetailModalOpen(false)}
        onEdit={() => {
          if (!selectedAppointment) return;
          // Prepare data for AppointmentForm (matches its 'appointment' prop)
          setEditAppointmentData({
            id: selectedAppointment.id,
            patient_id: selectedAppointment.patient.id,
            appointment_datetime: selectedAppointment.appointment_datetime,
            duration_minutes: selectedAppointment.duration_minutes,
            appointment_type: selectedAppointment.appointment_type,
            reason: selectedAppointment.reason,
            patient_notes: selectedAppointment.patient_notes,
          });
          setIsAppointmentDetailModalOpen(false);
          setIsEditAppointmentModalOpen(true);
        }}
        onDelete={async () => {
          if (!selectedAppointment) return;
          if (
            window.confirm("Are you sure you want to delete this appointment?")
          ) {
            try {
              await apiClient.delete(
                `/appointments/${selectedAppointment.id}/`,
              );
              setIsAppointmentDetailModalOpen(false);
              // Refresh appointments after delete
              setAppointments((prev) =>
                prev.filter((appt) => appt.id !== selectedAppointment.id),
              );
            } catch (err: unknown) {
              if (err instanceof AxiosError) {
                toast.error(
                  `Failed to delete appointment: ${err.response?.data?.detail || err.message}`,
                );
              } else if (err instanceof Error) {
                toast.error(`Failed to delete appointment: ${err.message}`);
              } else {
                toast.error(
                  "Failed to delete appointment due to an unknown error.",
                );
              }
            }
          }
        }}
      />

      {/* Edit Appointment Modal */}
      <AppointmentForm
        isOpen={isEditAppointmentModalOpen}
        onClose={() => setIsEditAppointmentModalOpen(false)}
        isSubmitting={isSubmittingEditAppointment}
        appointment={editAppointmentData || undefined}
        onSubmit={async (formData: AppointmentFormData) => {
          if (!editAppointmentData) return;
          setIsSubmittingEditAppointment(true);
          try {
            // Format datetime to ISO string if needed
            const isoDateTime =
              formData.appointment_datetime.length === 16 // "YYYY-MM-DDTHH:mm"
                ? new Date(formData.appointment_datetime).toISOString()
                : formData.appointment_datetime;
            const updatePayload = {
              appointment_datetime: isoDateTime,
              duration_minutes: formData.duration_minutes,
              appointment_type: formData.appointment_type,
              reason: formData.reason,
              patient_notes: formData.patient_notes,
            };
            const response = await apiClient.put(
              `/appointments/${editAppointmentData.id}/`,
              updatePayload,
            );
            // Update appointment in state
            setAppointments((prev) =>
              prev.map((appt) =>
                appt.id === editAppointmentData.id
                  ? { ...appt, ...response.data }
                  : appt,
              ),
            );
            setIsEditAppointmentModalOpen(false);
            setEditAppointmentData(null);
          } catch (err: unknown) {
            if (err instanceof AxiosError) {
              toast.error(
                `Failed to update appointment: ${err.response?.data?.detail || err.message}`,
              );
            } else if (err instanceof Error) {
              toast.error(`Failed to update appointment: ${err.message}`);
            } else {
              toast.error(
                "Failed to update appointment due to an unknown error.",
              );
            }
          } finally {
            setIsSubmittingEditAppointment(false);
          }
        }}
      />
      {/* Alert Detail Modal */}
      <AlertDetailModal
        isOpen={isAlertDetailModalOpen}
        onClose={() => {
          setIsAlertDetailModalOpen(false);
          setSelectedAlert(null);
        }}
        alert={selectedAlert}
        onUpdate={(updatedAlert) => {
          setPatientAlerts((prev) =>
            prev.map((a) => (a.id === updatedAlert.id ? updatedAlert : a))
          );
        }}
      />
    </div>
  );
};

export default PatientDetailPage;
