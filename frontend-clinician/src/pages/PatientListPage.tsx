import React, { useState, useEffect, useCallback } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import apiClient from "@/lib/apiClient"; // Import the centralized API client
import axios from "axios"; // Import axios for error type checking
import { RefreshCw, AlertCircle } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { buttonVariants } from "@/components/ui/button-variants";
// MainLayout is applied via routing in App.tsx

// Define the structure for a patient list item based on API response
// GET /api/clinicians/patients
interface PatientListItem {
  id: string; // Assuming UUID
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  // Add other relevant fields returned by the API if needed
}

// Define the structure for the paginated API response
interface PaginatedPatientResponse {
  items: PatientListItem[];
  total: number;
  page: number; // Current page number (1-based)
  size: number; // Items per page
  pages: number; // Total number of pages
}

const ITEMS_PER_PAGE = 10; // Define how many items per page

const PatientListPage: React.FC = () => {
  const [patients, setPatients] = useState<PatientListItem[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // TODO: Add state for search/sort parameters if implementing
  // const [searchTerm, setSearchTerm] = useState<string>('');
  // const [sortBy, setSortBy] = useState<string>('last_name');
  // const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Removed API_BASE_URL and getAuthToken - apiClient handles these

  const fetchPatientList = useCallback(async (page: number) => {
    setIsLoading(true);
    setError(null);

    const limit = ITEMS_PER_PAGE;
    const skip = (page - 1) * limit;

    try {
      const response = await apiClient.get<PaginatedPatientResponse>(
        "/clinicians/patients",
        {
          params: { skip, limit },
        },
      );

      // Add this detailed logging
      console.log("Patient API Response:", {
        data: response.data,
        firstPatient: response.data.items[0],
        totalItems: response.data.total,
        currentPage: response.data.page,
      });

      const data = response.data;
      setPatients(data.items);
      setTotalItems(data.total);
      setTotalPages(data.pages || Math.ceil(data.total / limit));
      setCurrentPage(data.page || page);
    } catch (err) {
      console.error("Error fetching patient list:", err);
      let errorDetail = "Failed to fetch patient list.";
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.status === 401 || err.response.status === 403) {
          errorDetail = "Authentication failed. Please log in again.";
          // The response interceptor might handle redirection already
        } else {
          errorDetail =
            err.response.data?.detail || `Server error: ${err.response.status}`;
        }
      } else if (err instanceof Error) {
        errorDetail = err.message;
      }
      setError(errorDetail);
      setPatients([]); // Clear patients on error
      setTotalPages(0);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
    }
  }, []); // Add dependencies if search/sort implemented

  // Fetch data on initial mount and when page changes
  useEffect(() => {
    fetchPatientList(currentPage);
  }, [fetchPatientList, currentPage]);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Patient List</h1>
        <div className="flex gap-2">
          {/* Add search when implemented */}
          <Button
            variant="outline"
            onClick={() => fetchPatientList(currentPage)}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="w-full flex justify-center p-8">
          <Spinner className="h-8 w-8" /> {/* Add a Spinner component */}
        </div>
      )}

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!isLoading && !error && (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px]">Name</TableHead>
                  <TableHead className="w-[300px]">Email</TableHead>
                  <TableHead className="w-[200px]">Status</TableHead>
                  <TableHead className="w-[150px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {patients.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      className="text-center h-24 text-muted-foreground"
                    >
                      No patients found
                    </TableCell>
                  </TableRow>
                ) : (
                  patients.map((patient) => (
                    <TableRow key={patient.id}>
                      <TableCell className="font-medium">
                        {patient.first_name} {patient.last_name}
                      </TableCell>
                      <TableCell>{patient.email}</TableCell>
                      <TableCell>
                        <Badge
                          variant={patient.is_active ? "default" : "secondary"}
                        >
                          {patient.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Link
                            to={`/clinician/patients/${patient.id}`}
                            className={buttonVariants({
                              variant: "default",
                              size: "sm",
                            })}
                          >
                            View Details
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {!isLoading && !error && patients.length > 0 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="flex-1 text-sm text-muted-foreground">
                Showing {(currentPage - 1) * ITEMS_PER_PAGE + 1} to{" "}
                {Math.min(currentPage * ITEMS_PER_PAGE, totalItems)} of{" "}
                {totalItems} patients
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousPage}
                  disabled={currentPage <= 1}
                >
                  Previous
                </Button>
                <div className="text-sm font-medium">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default PatientListPage;
