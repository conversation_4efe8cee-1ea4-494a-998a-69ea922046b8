import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  AlertTriangle, 
  Activity,
  BarChart3,
  Brain,
  Lightbulb,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from "lucide-react";
import { analyticsApi } from "@/lib/apiClient";
import { toast } from "sonner";

interface PopulationHealthDashboardProps {}

const PopulationHealthDashboard: React.FC<PopulationHealthDashboardProps> = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [activeSection, setActiveSection] = useState<string>('overview');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getPopulationDashboard();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load analytics dashboard');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpRight className="h-4 w-4 text-green-600" />;
      case 'down':
        return <ArrowDownRight className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-400" />;
    }
  };

  const getRiskBadgeColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'bg-red-600 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'moderate':
        return 'bg-yellow-500 text-white';
      default:
        return 'bg-green-500 text-white';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Population Health Analytics</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-[150px]" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-[100px] mb-2" />
                <Skeleton className="h-4 w-[120px]" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return <div>No data available</div>;
  }

  const { overview, risk_stratification, treatment_analytics, predictive_analytics, optimization_insights } = dashboardData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Population Health Analytics</h1>
        <Button onClick={fetchDashboardData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-4 border-b">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'risk', label: 'Risk Stratification', icon: AlertTriangle },
          { id: 'treatment', label: 'Treatment Analytics', icon: Activity },
          { id: 'predictive', label: 'Predictive Insights', icon: Brain },
          { id: 'optimization', label: 'Optimization', icon: Lightbulb },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveSection(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
              activeSection === tab.id
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-600 hover:text-gray-900'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Overview Section */}
      {activeSection === 'overview' && overview && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{overview.total_patients}</div>
                <p className="text-xs text-muted-foreground">
                  {overview.active_patients} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Weight Loss</CardTitle>
                <TrendingDown className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <div className="text-2xl font-bold">
                    {overview.average_weight_loss.current.toFixed(1)} lbs
                  </div>
                  {getTrendIcon(overview.average_weight_loss.trend)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {overview.average_weight_loss.change_percentage > 0 ? '+' : ''}
                  {overview.average_weight_loss.change_percentage.toFixed(1)}% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Adherence Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <div className="text-2xl font-bold">
                    {overview.adherence_rate.current.toFixed(1)}%
                  </div>
                  {getTrendIcon(overview.adherence_rate.trend)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {overview.adherence_rate.change_percentage > 0 ? '+' : ''}
                  {overview.adherence_rate.change_percentage.toFixed(1)}% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Side Effect Rate</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <div className="text-2xl font-bold">
                    {overview.side_effect_rate.current.toFixed(1)}%
                  </div>
                  {getTrendIcon(overview.side_effect_rate.trend)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {overview.side_effect_rate.change_percentage > 0 ? '+' : ''}
                  {overview.side_effect_rate.change_percentage.toFixed(1)}% from last month
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>New Patients This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-blue-600">
                {overview.new_patients_this_month}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Risk Stratification Section */}
      {activeSection === 'risk' && risk_stratification && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Risk Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(risk_stratification.risk_distribution).map(([level, count]) => (
                    <div key={level} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge className={getRiskBadgeColor(level)}>
                          {level.charAt(0).toUpperCase() + level.slice(1)}
                        </Badge>
                        <span className="text-sm font-medium">{count as number} patients</span>
                      </div>
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            level === 'critical' ? 'bg-red-600' :
                            level === 'high' ? 'bg-orange-500' :
                            level === 'moderate' ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{
                            width: `${((count as number) / overview.total_patients) * 100}%`
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Risk Factors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {risk_stratification.top_risk_factors.map((factor: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{factor.factor}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{factor.count}</span>
                        <span className="text-xs text-muted-foreground">
                          ({factor.percentage.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>High-Risk Patients Requiring Attention</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {risk_stratification.high_risk_patients.map((patient: any) => (
                  <div key={patient.patient_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium">{patient.patient_name}</h4>
                        <Badge className={getRiskBadgeColor(patient.risk_level)}>
                          {patient.risk_level.toUpperCase()}
                        </Badge>
                        {patient.urgent_action_required && (
                          <Badge variant="destructive">Urgent</Badge>
                        )}
                      </div>
                      <Button size="sm">View Details</Button>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Last Contact:</span>
                        <p className="font-medium">{patient.days_since_last_interaction} days ago</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Weight Trend:</span>
                        <p className="font-medium capitalize">{patient.weight_trend}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Adherence:</span>
                        <p className="font-medium">{patient.adherence_percentage}%</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Risk Factors:</span>
                        <p className="font-medium">{patient.risk_factors.length}</p>
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-2">
                        {patient.risk_factors.map((factor: string, idx: number) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {factor}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Treatment Analytics Section */}
      {activeSection === 'treatment' && treatment_analytics && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Medication Usage & Effectiveness</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {treatment_analytics.medications_by_usage.map((med: any) => (
                  <div key={med.medication_name} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-lg">{med.medication_name}</h4>
                      <Badge variant="secondary">{med.patient_count} patients</Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Efficacy Rate</span>
                        <p className="font-medium text-lg">{med.average_efficacy.toFixed(1)}%</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Success Rate</span>
                        <p className="font-medium text-lg">{med.success_rate.toFixed(1)}%</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Discontinuation</span>
                        <p className="font-medium text-lg">{med.discontinuation_rate.toFixed(1)}%</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Avg Side Effects</span>
                        <p className="font-medium text-lg">{med.average_side_effects_per_patient.toFixed(1)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Treatment Phase Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(treatment_analytics.treatment_phase_distribution).map(([phase, count]) => (
                    <div key={phase} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{phase.replace('_', ' ')}</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{count as number}</span>
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="h-2 rounded-full bg-blue-600"
                            style={{
                              width: `${((count as number) / overview.total_patients) * 100}%`
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Average Time to Goal</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-blue-600">
                  {treatment_analytics.average_time_to_goal} days
                </div>
                <p className="text-muted-foreground mt-2">
                  Average duration from treatment start to goal achievement
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Predictive Analytics Section */}
      {activeSection === 'predictive' && predictive_analytics && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Overall Success Rate Prediction</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-green-600">
                {predictive_analytics.overall_success_rate_prediction}%
              </div>
              <p className="text-muted-foreground mt-2">
                Predicted success rate for next 30 days
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>At-Risk Predictions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {predictive_analytics.at_risk_predictions.map((prediction: any) => (
                    <div key={prediction.patient_id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium">{prediction.patient_name}</h5>
                        <Badge variant="destructive">
                          {prediction.risk_of_discontinuation.toFixed(0)}% risk
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                        <div>
                          <span className="text-muted-foreground">30d Weight Loss:</span>
                          <p className="font-medium">{prediction.predicted_weight_loss_30d.toFixed(1)} lbs</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Success Probability:</span>
                          <p className="font-medium">{prediction.success_probability.toFixed(0)}%</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">Recommended interventions:</p>
                        <div className="flex flex-wrap gap-1">
                          {prediction.recommended_interventions.map((intervention: string, idx: number) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {intervention}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Success Predictions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {predictive_analytics.success_predictions.map((prediction: any) => (
                    <div key={prediction.patient_id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium">{prediction.patient_name}</h5>
                        <Badge className="bg-green-600 text-white">
                          {prediction.success_probability.toFixed(0)}% success
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                        <div>
                          <span className="text-muted-foreground">30d Weight Loss:</span>
                          <p className="font-medium">{prediction.predicted_weight_loss_30d.toFixed(1)} lbs</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Discontinuation Risk:</span>
                          <p className="font-medium">{prediction.risk_of_discontinuation.toFixed(0)}%</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">Optimization opportunities:</p>
                        <div className="flex flex-wrap gap-1">
                          {prediction.recommended_interventions.map((intervention: string, idx: number) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {intervention}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Intervention Impact Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(predictive_analytics.intervention_impact_analysis).map(([intervention, impact]) => (
                  <div key={intervention} className="flex items-center justify-between">
                    <span className="text-sm">{intervention}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full bg-blue-600"
                          style={{ width: `${impact}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-12 text-right">+{impact}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Optimization Insights Section */}
      {activeSection === 'optimization' && optimization_insights && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Weekly Time Savings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">
                  {optimization_insights.total_time_savings_per_week.toFixed(1)} hours
                </div>
                <p className="text-muted-foreground mt-2">
                  Potential time saved with optimizations
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Efficiency Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {optimization_insights.efficiency_score.toFixed(1)}%
                </div>
                <p className="text-muted-foreground mt-2">
                  Current workflow efficiency
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Active Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {optimization_insights.insights.length}
                </div>
                <p className="text-muted-foreground mt-2">
                  Actionable recommendations
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Optimization Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {optimization_insights.insights.map((insight: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium">{insight.title}</h4>
                          <Badge variant={
                            insight.priority === 'high' ? 'destructive' :
                            insight.priority === 'medium' ? 'default' : 'secondary'
                          }>
                            {insight.priority} priority
                          </Badge>
                          <Badge variant="outline">{insight.category}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{insight.description}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Impact:</span>
                        <p className="font-medium">{insight.potential_impact}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Affected Patients:</span>
                        <p className="font-medium">{insight.affected_patients}</p>
                      </div>
                      {insight.estimated_time_saving && (
                        <div>
                          <span className="text-muted-foreground">Time Saving:</span>
                          <p className="font-medium">{insight.estimated_time_saving}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Workflow Bottlenecks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {optimization_insights.top_bottlenecks.map((bottleneck: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{bottleneck.area}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full bg-red-500"
                          style={{ width: `${(bottleneck.impact_hours / 5) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-20 text-right">
                        {bottleneck.impact_hours} hrs/week
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PopulationHealthDashboard;