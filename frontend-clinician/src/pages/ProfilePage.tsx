import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "../components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "../components/ui/avatar";
import { Badge } from "../components/ui/badge";
import { But<PERSON> } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Checkbox } from "../components/ui/checkbox";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "../components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import apiClient from "../lib/apiClient";
import { useUser } from "@clerk/clerk-react";
import { 
  Camera, 
  Edit3, 
  Mail, 
  Phone, 
  MapPin, 
  Award, 
  Calendar,
  Bell,
  Shield,
  Settings,
  User,
  Stethoscope,
  GraduationCap,
  Clock,
  Save,
  X
} from "lucide-react";

interface Clinician {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  specialty?: string;
  bio?: string;
  credentials?: string[];
  years_experience?: number;
  clinic?: string;
  clinics?: string[];
  photo_url?: string;
  created_at: string;
  updated_at: string;
}

interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  appointment_reminders: boolean;
  new_patient_alerts: boolean;
  medication_request_alerts: boolean;
  side_effect_alerts: boolean;
}

const MEDICAL_SPECIALTIES = [
  "General Practice",
  "Internal Medicine", 
  "Family Medicine",
  "Cardiology",
  "Dermatology",
  "Endocrinology",
  "Gastroenterology",
  "Neurology",
  "Oncology",
  "Orthopedics",
  "Pediatrics",
  "Psychiatry",
  "Radiology",
  "Surgery",
  "Urology",
  "Other"
];

const CREDENTIALS = [
  "MD", "DO", "PA", "NP", "RN", "PharmD", "PhD", "MPH", "MSN", "BSN"
];

const getInitials = (firstName: string, lastName: string) => {
  if (!firstName && !lastName) return "??";
  return ((firstName?.[0] || "") + (lastName?.[0] || "")).toUpperCase();
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export default function ProfilePage() {
  const { user, isLoaded } = useUser();
  const [backendClinician, setBackendClinician] = useState<Clinician | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    specialty: "",
    bio: "",
    credentials: [] as string[],
    yearsExperience: "",
    clinic: "",
    profileImage: "",
  });
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_notifications: true,
    push_notifications: true,
    appointment_reminders: true,
    new_patient_alerts: true,
    medication_request_alerts: true,
    side_effect_alerts: true,
  });
  const [saveLoading, setSaveLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch backend clinician data
  useEffect(() => {
    let mounted = true;
    async function fetchProfile() {
      setLoading(true);
      setError(null);
      try {
        const res = await apiClient.get("/clinicians/me");
        if (mounted) {
          setBackendClinician(res.data);
        }
      } catch (error: unknown) {
        console.error("Error fetching profile:", error);
        setError("Failed to load profile data.");
      } finally {
        setLoading(false);
      }
    }
    fetchProfile();
    return () => {
      mounted = false;
    };
  }, []);

  // Merge Clerk and backend data for display/form
  useEffect(() => {
    if (!isLoaded || !user || !backendClinician) return;
    setForm({
      firstName: user.firstName || backendClinician.first_name || "",
      lastName: user.lastName || backendClinician.last_name || "",
      email: user.emailAddresses?.[0]?.emailAddress || backendClinician.email || "",
      phone: user.phoneNumbers?.[0]?.phoneNumber || "",
      specialty: backendClinician.specialty || "",
      bio: backendClinician.bio || "",
      credentials: backendClinician.credentials || [],
      yearsExperience: backendClinician.years_experience?.toString() || "",
      clinic: backendClinician.clinics && backendClinician.clinics.length > 0
        ? (typeof backendClinician.clinics[0] === 'string' ? backendClinician.clinics[0] : backendClinician.clinics[0]?.name || "")
        : "",
      profileImage: user.imageUrl || backendClinician.photo_url || "",
    });
  }, [isLoaded, user, backendClinician]);

  const handleEditSection = (section: string) => {
    setEditingSection(editingSection === section ? null : section);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm({ ...form, [name]: value });
  };

  const handleCredentialToggle = (credential: string) => {
    const updatedCredentials = form.credentials.includes(credential)
      ? form.credentials.filter(c => c !== credential)
      : [...form.credentials, credential];
    setForm({ ...form, credentials: updatedCredentials });
  };

  const handleNotificationChange = (setting: keyof NotificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError('Image file must be less than 5MB');
      return;
    }

    setUploadingPhoto(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('photo', file);

      // This would need to be implemented in the backend
      const response = await apiClient.post('/clinicians/me/photo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Update the form with the new photo URL
      setForm({ ...form, profileImage: response.data.photo_url });
      setPhotoDialogOpen(false);
      
      // Optionally refetch the profile
      const res = await apiClient.get("/clinicians/me");
      setBackendClinician(res.data);
      
    } catch (error: unknown) {
      console.error("Error uploading photo:", error);
      setError("Failed to upload photo. Please try again.");
    } finally {
      setUploadingPhoto(false);
    }
  };

  const handleSave = async (section: string) => {
    setSaveLoading(true);
    setError(null);

    try {
      // Prepare the update payload based on the section
      let updatePayload: any = {};
      
      if (section === 'basic') {
        updatePayload = {
          first_name: form.firstName,
          last_name: form.lastName,
          specialty: form.specialty,
        };
      } else if (section === 'professional') {
        updatePayload = {
          bio: form.bio,
          credentials: form.credentials,
          years_experience: form.yearsExperience ? parseInt(form.yearsExperience) : null,
        };
      } else if (section === 'notifications') {
        // This would need a separate endpoint for user settings
        // await apiClient.put('/clinicians/me/settings', notificationSettings);
        console.log('Notification settings would be saved:', notificationSettings);
      }

      if (Object.keys(updatePayload).length > 0) {
        // Update Clerk profile for name changes
        if (section === 'basic' && user) {
          const clerkUpdates: any = {};
          if (user.firstName !== form.firstName) clerkUpdates.firstName = form.firstName;
          if (user.lastName !== form.lastName) clerkUpdates.lastName = form.lastName;
          
          if (Object.keys(clerkUpdates).length > 0) {
            await user.update(clerkUpdates);
          }
        }

        // Update backend profile
        await apiClient.put<Clinician>(`/clinicians/me`, updatePayload);

        // Refetch backend data to refresh
        const res = await apiClient.get<Clinician>("/clinicians/me");
        setBackendClinician(res.data);
      }
      
      setEditingSection(null);
    } catch (error: unknown) {
      console.error("Error saving profile:", error);
      setError("Failed to save profile changes.");
    } finally {
      setSaveLoading(false);
    }
  };

  if (loading || !isLoaded) {
    return (
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>
          <div className="bg-gray-200 rounded-lg h-64 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-200 rounded-lg h-48"></div>
            <div className="bg-gray-200 rounded-lg h-48"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !backendClinician) {
    return (
      <div className="max-w-4xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-6">My Profile</h1>
        <Card className="p-6 border-red-200 bg-red-50">
          <div className="text-red-600 flex items-center gap-2">
            <X className="h-5 w-5" />
            {error}
          </div>
        </Card>
      </div>
    );
  }

  const memberSince = backendClinician?.created_at ? formatDate(backendClinician.created_at) : 'Unknown';

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <div className="p-2 bg-blue-100 rounded-lg">
          <User className="h-6 w-6 text-blue-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="text-red-600 flex items-center gap-2">
              <X className="h-5 w-5" />
              {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Profile Header Card */}
      <Card className="overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-24"></div>
        <CardContent className="relative p-6">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between">
            <div className="flex flex-col md:flex-row md:items-end gap-4 -mt-12">
              {/* Avatar Section */}
              <div className="relative flex-shrink-0">
                <Avatar className="w-24 h-24 border-4 border-white shadow-lg">
                  <AvatarImage src={form.profileImage} alt={`${form.firstName} ${form.lastName}`} />
                  <AvatarFallback className="text-2xl font-semibold bg-gray-100">
                    {getInitials(form.firstName, form.lastName)}
                  </AvatarFallback>
                </Avatar>
                
                <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      className="absolute -bottom-2 -right-2 rounded-full p-2 h-8 w-8 shadow-lg"
                    >
                      <Camera className="h-3 w-3" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Update Profile Photo</DialogTitle>
                      <DialogDescription>
                        Choose a new profile photo. Image should be less than 5MB.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoUpload}
                        className="hidden"
                      />
                      <Button 
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingPhoto}
                        className="w-full"
                      >
                        {uploadingPhoto ? "Uploading..." : "Choose Photo"}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              
              {/* Basic Info */}
              <div className="space-y-2 mt-4 md:mt-0">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 whitespace-nowrap">
                    {form.firstName} {form.lastName}
                  </h2>
                  <p className="text-gray-600">{form.specialty || 'Healthcare Professional'}</p>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    {form.email}
                  </Badge>
                  {form.phone && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {form.phone}
                    </Badge>
                  )}
                  {form.clinic && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {typeof form.clinic === 'string' ? form.clinic : form.clinic.name || 'Clinic'}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  Member since {memberSince}
                </div>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-4 mt-6 md:mt-0 md:self-end">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {form.yearsExperience || '0'}
                </div>
                <div className="text-xs text-gray-500">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {form.credentials.length}
                </div>
                <div className="text-xs text-gray-500">Credentials</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="professional" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Professional
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-semibold">Basic Information</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditSection('basic')}
                  className="flex items-center gap-2"
                >
                  <Edit3 className="h-4 w-4" />
                  {editingSection === 'basic' ? 'Cancel' : 'Edit'}
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {editingSection === 'basic' ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          value={form.firstName}
                          onChange={handleChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          value={form.lastName}
                          onChange={handleChange}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="specialty">Specialty</Label>
                      <Select value={form.specialty} onValueChange={(value) => handleSelectChange('specialty', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your specialty" />
                        </SelectTrigger>
                        <SelectContent>
                          {MEDICAL_SPECIALTIES.map((specialty) => (
                            <SelectItem key={specialty} value={specialty}>
                              {specialty}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={() => handleSave('basic')} disabled={saveLoading} className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        {saveLoading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm text-gray-500">Full Name</Label>
                      <div className="font-medium">{form.firstName} {form.lastName}</div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500">Email</Label>
                      <div className="font-medium">{form.email}</div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500">Phone</Label>
                      <div className="font-medium">{form.phone || 'Not provided'}</div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500">Specialty</Label>
                      <div className="font-medium">{form.specialty || 'Not specified'}</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Professional Summary */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-semibold">Professional Summary</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditSection('professional')}
                  className="flex items-center gap-2"
                >
                  <Edit3 className="h-4 w-4" />
                  {editingSection === 'professional' ? 'Cancel' : 'Edit'}
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {editingSection === 'professional' ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="bio">Professional Bio</Label>
                      <Textarea
                        id="bio"
                        name="bio"
                        value={form.bio}
                        onChange={handleChange}
                        placeholder="Tell us about your professional background, areas of expertise, and approach to patient care..."
                        rows={4}
                      />
                    </div>
                    <div>
                      <Label htmlFor="yearsExperience">Years of Experience</Label>
                      <Input
                        id="yearsExperience"
                        name="yearsExperience"
                        type="number"
                        value={form.yearsExperience}
                        onChange={handleChange}
                        placeholder="0"
                        min="0"
                        max="50"
                      />
                    </div>
                    <div>
                      <Label>Credentials & Certifications</Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {CREDENTIALS.map((credential) => (
                          <div key={credential} className="flex items-center space-x-2">
                            <Checkbox
                              id={credential}
                              checked={form.credentials.includes(credential)}
                              onCheckedChange={() => handleCredentialToggle(credential)}
                            />
                            <Label htmlFor={credential} className="text-sm">
                              {credential}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={() => handleSave('professional')} disabled={saveLoading} className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        {saveLoading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm text-gray-500">Bio</Label>
                      <div className="font-medium text-sm leading-relaxed">
                        {form.bio || 'No professional bio provided.'}
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500 flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        Experience
                      </Label>
                      <div className="font-medium">{form.yearsExperience || '0'} years</div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500 flex items-center gap-1">
                        <GraduationCap className="h-4 w-4" />
                        Credentials
                      </Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {form.credentials.length > 0 ? (
                          form.credentials.map((credential) => (
                            <Badge key={credential} variant="outline" className="text-xs">
                              {credential}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-gray-500 text-sm">No credentials listed</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Professional Tab */}
        <TabsContent value="professional" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Professional Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Stethoscope className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">Professional Features Coming Soon</h3>
                <p>Advanced professional features including certifications, publications, and achievements will be available soon.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleEditSection('notifications')}
                className="flex items-center gap-2"
              >
                <Edit3 className="h-4 w-4" />
                {editingSection === 'notifications' ? 'Cancel' : 'Edit'}
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {editingSection === 'notifications' ? (
                <div className="space-y-4">
                  {Object.entries(notificationSettings).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label htmlFor={key} className="text-sm font-medium capitalize">
                        {key.replace(/_/g, ' ')}
                      </Label>
                      <Checkbox
                        id={key}
                        checked={value}
                        onCheckedChange={() => handleNotificationChange(key as keyof NotificationSettings)}
                      />
                    </div>
                  ))}
                  <div className="flex gap-2 pt-4">
                    <Button onClick={() => handleSave('notifications')} disabled={saveLoading} className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      {saveLoading ? 'Saving...' : 'Save Preferences'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {Object.entries(notificationSettings).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between py-2">
                      <span className="text-sm capitalize">{key.replace(/_/g, ' ')}</span>
                      <Badge variant={value ? "default" : "secondary"}>
                        {value ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & Account
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Password Management</Label>
                <div className="mt-2">
                  <Button variant="outline" disabled>
                    Change Password (Managed by Clerk)
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Password changes are handled through our secure authentication provider.
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Two-Factor Authentication</Label>
                <div className="mt-2">
                  <Button variant="outline" disabled>
                    Configure 2FA (Coming Soon)
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Enhanced security options will be available in a future update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}