import React, { useState, useEffect, useCallback } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // For filters/sort
import { Label } from "@/components/ui/label"; // For filter labels
import apiClient from "@/lib/apiClient"; // Import the centralized API client
import axios from "axios"; // Import axios for error type checking
// Assuming a MainLayout component exists
// import MainLayout from '@/components/layout/MainLayout';
import { cn } from "@/lib/utils"; // For conditional classes

// Define the structure for a side effect report list item based on API response
// GET /api/clinicians/side-effects
interface SideEffectReportItem {
  id: string; // Report ID
  patient_id: string; // Patient ID
  severity: "Minor" | "Moderate" | "Severe"; // Assuming these specific values from API/Enum
  status: string; // e.g., 'Submitted', 'Reviewed'
  description: string;
  reported_at: string; // ISO 8601
  // Add other relevant fields if returned by the API
}

// Define the structure for the paginated API response
interface PaginatedSideEffectResponse {
  items: SideEffectReportItem[];
  total: number;
  page: number; // Current page number (1-based)
  size: number; // Items per page
  pages: number; // Total number of pages
}

const ITEMS_PER_PAGE = 15; // Adjust items per page as needed

const SideEffectTriagePage: React.FC = () => {
  const [reports, setReports] = useState<SideEffectReportItem[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // State for filters and sorting
  const [severityFilter, setSeverityFilter] = useState<string>(""); // '', 'Minor', 'Moderate', 'Severe'
  const [statusFilter, setStatusFilter] = useState<string>("Submitted"); // Default to 'Submitted' or '' for all
  const [sortBy, setSortBy] = useState<string>("reported_at"); // Default sort
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc"); // Default order

  // Removed API_BASE_URL and getAuthToken

  const fetchSideEffectReports = useCallback(
    async (page: number) => {
      setIsLoading(true);
      setError(null);
      // Token is handled by apiClient interceptor

      const limit = ITEMS_PER_PAGE;
      const skip = (page - 1) * limit;

      // Construct query parameters for Axios, omitting empty filters
      const params: Record<string, string | number> = {
        skip: skip,
        limit: limit,
        sort_by: sortBy,
        sort_order: sortOrder,
      };
      if (severityFilter) {
        params.severity = severityFilter;
      }
      if (statusFilter) {
        params.status = statusFilter;
      }

      try {
        // Use apiClient for the GET request
        const response = await apiClient.get<PaginatedSideEffectResponse>(
          "/clinicians/side-effects",
          { params },
        );
        // Auth header is added by interceptor

        const data = response.data; // Axios response data

        setReports(data.items);
        setTotalItems(data.total);
        setTotalPages(data.pages || Math.ceil(data.total / limit));
        setCurrentPage(data.page || page);
      } catch (err) {
        console.error("Error fetching side effect reports:", err);
        let errorDetail = "Failed to fetch side effect reports.";
        if (axios.isAxiosError(err) && err.response) {
          if (err.response.status === 401 || err.response.status === 403) {
            errorDetail = "Authentication failed. Please log in again.";
          } else {
            errorDetail =
              err.response.data?.detail ||
              `Server error: ${err.response.status}`;
          }
        } else if (err instanceof Error) {
          errorDetail = err.message;
        }
        setError(errorDetail);
        setReports([]); // Clear reports on error
        setTotalPages(0);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [severityFilter, statusFilter, sortBy, sortOrder],
  ); // Include filters/sort in dependency array

  // Fetch data on initial mount and when page/filters/sort change
  useEffect(() => {
    // Reset to page 1 when filters/sort change
    // This effect depends on fetchSideEffectReports, which depends on filters/sort
    // When filters/sort change, fetchSideEffectReports changes, triggering this effect
    setCurrentPage(1);
    fetchSideEffectReports(1); // Fetch page 1 with new filters/sort
  }, [fetchSideEffectReports]); // Only trigger when the fetch function itself changes due to dependencies

  useEffect(() => {
    // Fetch data only when page changes, using the current filters/sort baked into fetchSideEffectReports
    // This prevents double-fetching when filters change AND page is 1
    if (currentPage !== 1) {
      // Avoid refetching page 1 if filters changed
      fetchSideEffectReports(currentPage);
    }
  }, [currentPage, fetchSideEffectReports]); // Trigger on page change

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleString(); // User-friendly format
    } catch {
      return dateString; // Fallback
    }
  };

  // Helper function for severity styling
  const getSeverityClass = (
    severity: SideEffectReportItem["severity"],
  ): string => {
    switch (severity) {
      case "Severe":
        return "bg-red-100 text-red-800";
      case "Moderate":
        return "bg-yellow-100 text-yellow-800";
      case "Minor":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    // <MainLayout>
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <h1 className="text-2xl font-bold mb-6">Side Effect Report Triage</h1>

      {/* Filter and Sort Controls */}
      <div className="flex flex-wrap gap-4 mb-6 p-4 border rounded-md bg-gray-50">
        {/* Severity Filter */}
        <div className="flex flex-col space-y-1.5">
          <Label htmlFor="severity-filter">Severity</Label>
          <Select value={severityFilter} onValueChange={setSeverityFilter}>
            <SelectTrigger id="severity-filter" className="w-[180px]">
              <SelectValue placeholder="All Severities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Severities</SelectItem>
              <SelectItem value="Minor">Minor</SelectItem>
              <SelectItem value="Moderate">Moderate</SelectItem>
              <SelectItem value="Severe">Severe</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="flex flex-col space-y-1.5">
          <Label htmlFor="status-filter">Status</Label>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger id="status-filter" className="w-[180px]">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="Submitted">Submitted</SelectItem>
              <SelectItem value="Reviewed">Reviewed</SelectItem>
              {/* Add other statuses if applicable */}
            </SelectContent>
          </Select>
        </div>

        {/* Sort By */}
        <div className="flex flex-col space-y-1.5">
          <Label htmlFor="sort-by">Sort By</Label>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger id="sort-by" className="w-[180px]">
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="reported_at">Date Reported</SelectItem>
              <SelectItem value="severity">Severity</SelectItem>
              {/* Add other sortable fields */}
            </SelectContent>
          </Select>
        </div>

        {/* Sort Order */}
        <div className="flex flex-col space-y-1.5">
          <Label htmlFor="sort-order">Order</Label>
          <Select
            value={sortOrder}
            onValueChange={(value) => setSortOrder(value as "asc" | "desc")}
          >
            <SelectTrigger id="sort-order" className="w-[120px]">
              <SelectValue placeholder="Order" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="desc">Descending</SelectItem>
              <SelectItem value="asc">Ascending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading && <p>Loading reports...</p>}
      {error && <p className="text-red-600 mb-4">Error: {error}</p>}

      {!isLoading && !error && (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Patient ID</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Date Reported</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">
                    No matching side effect reports found.
                  </TableCell>
                </TableRow>
              ) : (
                reports.map((report) => (
                  <TableRow
                    key={report.id}
                    className={cn("hover:brightness-95")}
                  >
                    <TableCell>
                      {/* TODO: Fetch/Display Patient Name instead of ID. */}
                      {report.patient_id}
                    </TableCell>
                    <TableCell>
                      <span
                        className={cn(
                          "px-2 py-0.5 rounded-full text-xs font-medium",
                          getSeverityClass(report.severity),
                        )}
                      >
                        {report.severity}
                      </span>
                    </TableCell>
                    <TableCell className="max-w-sm truncate">
                      {report.description}
                    </TableCell>
                    <TableCell>{formatDate(report.reported_at)}</TableCell>
                    <TableCell>{report.status}</TableCell>
                    <TableCell>
                      <Link
                        to={`/clinician/patients/${report.patient_id}`} // Link to patient detail page
                        className="text-blue-600 hover:underline"
                      >
                        View Patient
                      </Link>
                      {/* Add Review/Action buttons later */}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePreviousPage}
                disabled={currentPage === 1 || isLoading}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {totalPages} (Total Found: {totalItems})
              </span>
              <Button
                variant="outline"
                onClick={handleNextPage}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
    // </MainLayout>
  );
};

export default SideEffectTriagePage;
