import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardHeader,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertCircle,
  Loader2,
  RefreshCw,
  PlusCircle,
  ChevronsRight,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import apiClient from "@/lib/apiClient";
import { format, parseISO, isValid } from "date-fns";
import { toast } from "sonner";
import { SideEffectDetailModal } from "@/components/side-effects/SideEffectDetailModal";
import { AddSideEffectModal } from "@/components/side-effects/AddSideEffectModal";

interface Patient {
  id: string;
  first_name: string;
  last_name: string;
}

interface SideEffectReport {
  id: string;
  patient: Patient;
  description: string;
  severity: "minor" | "moderate" | "major";
  status: string;
  reported_at: string;
}

interface PatientOption {
  id: string;
  name: string;
}

const SideEffectsPage: React.FC = () => {
  const [sideEffects, setSideEffects] = useState<SideEffectReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSideEffect, setSelectedSideEffect] =
    useState<SideEffectReport | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const [selectedPatient, setSelectedPatient] = useState<string>("all");
  const [selectedSeverity, setSelectedSeverity] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("Submitted");

  const [filteredSideEffects, setFilteredSideEffects] = useState<
    SideEffectReport[]
  >([]);

  const fetchSideEffects = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get("/side-effects/");
      setSideEffects(response.data || []);
    } catch (err: unknown) {
      console.error("Error fetching side effect reports:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to fetch side effect reports";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSideEffects();
  }, []);

  const patientOptions = useMemo(() => {
    const uniquePatients: PatientOption[] = [];
    const patientIds = new Set<string>();
    sideEffects.forEach((req) => {
      if (!patientIds.has(req.patient.id)) {
        patientIds.add(req.patient.id);
        uniquePatients.push({
          id: req.patient.id,
          name: `${req.patient.first_name} ${req.patient.last_name}`,
        });
      }
    });
    uniquePatients.sort((a, b) => a.name.localeCompare(b.name));
    return [{ id: "all", name: "All Patients" }, ...uniquePatients];
  }, [sideEffects]);

  const severityOptions = [
    { value: "all", label: "All Severities" },
    { value: "minor", label: "Minor" },
    { value: "moderate", label: "Moderate" },
    { value: "major", label: "Major" },
  ];

  const statusOptions = [
    { value: "all", label: "All Statuses" },
    { value: "Submitted", label: "Submitted" },
    { value: "Reviewed", label: "Reviewed" },
    { value: "Resolved", label: "Resolved" },
  ];

  useEffect(() => {
    let results = sideEffects;

    if (selectedPatient !== "all") {
      results = results.filter((se) => se.patient.id === selectedPatient);
    }

    if (selectedSeverity !== "all") {
      results = results.filter((se) => se.severity === selectedSeverity);
    }

    if (selectedStatus !== "all") {
      results = results.filter((se) => se.status === selectedStatus);
    }

    setFilteredSideEffects(results);
  }, [sideEffects, selectedPatient, selectedSeverity, selectedStatus]);

  const handleViewDetails = (sideEffect: SideEffectReport) => {
    setSelectedSideEffect(sideEffect);
    setIsDetailModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsDetailModalOpen(false);
    setSelectedSideEffect(null);
  };

  const handleOpenAddModal = () => {
    setIsAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    setIsAddModalOpen(false);
  };

  // Refresh list and close modal after delete
  const handleDelete = (reportId: string) => {
    // We're not using the reportId directly here since we're just refreshing the entire list
    console.log(`Deleted report with ID: ${reportId}`);
    fetchSideEffects();
    setIsDetailModalOpen(false);
  };

  // Refresh list and close modal after edit
  const handleEdit = (updatedReport: SideEffectReport) => {
    // We're not using the updatedReport directly here since we're just refreshing the entire list
    console.log(
      `Updated report for patient: ${updatedReport.patient.first_name} ${updatedReport.patient.last_name}`,
    );
    fetchSideEffects();
    setIsDetailModalOpen(false);
  };

  const handleStatusUpdate = async (reportId: string, newStatus: string) => {
    try {
      await apiClient.put(`/side-effects/${reportId}`, { status: newStatus });
      toast.success(`Status updated to ${newStatus}`);
      await fetchSideEffects();
      handleCloseModal();
    } catch (error: unknown) {
      console.error("Error updating side effect status:", error);
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error("Failed to update status", {
        description: errorMessage,
      });
    }
  };

  const safeFormatDate = (dateString: string | undefined | null): string => {
    if (!dateString) {
      return "Date not available";
    }
    try {
      const date = parseISO(dateString);
      if (isValid(date)) {
        return format(date, "PPp");
      } else {
        console.warn("Received invalid date string:", dateString);
        return "Invalid Date";
      }
    } catch (error) {
      console.error("Error parsing date string:", dateString, error);
      return "Invalid Date Format";
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Side Effect Reports</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchSideEffects}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleOpenAddModal}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Side Effect
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 border rounded-lg">
        <div>
          <Label htmlFor="patient-filter">Patient</Label>
          <Select value={selectedPatient} onValueChange={setSelectedPatient}>
            <SelectTrigger id="patient-filter">
              <SelectValue placeholder="Filter by patient" />
            </SelectTrigger>
            <SelectContent>
              {patientOptions.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="severity-filter">Severity</Label>
          <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
            <SelectTrigger id="severity-filter">
              <SelectValue placeholder="Filter by severity" />
            </SelectTrigger>
            <SelectContent>
              {severityOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="status-filter">Status</Label>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger id="status-filter">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Side Effect Reports</CardTitle>
          <CardDescription>
            View and manage side effect reports from your patients
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : sideEffects.length === 0 ? (
            <p className="text-center py-4 text-gray-500">
              No side effect reports found.
            </p>
          ) : filteredSideEffects.length === 0 ? (
            <p className="text-center py-4 text-gray-500">
              No side effect reports match the current filters.
            </p>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredSideEffects.map((sideEffect) => (
                <Card key={sideEffect.id}>
                  <CardHeader>
                    <CardTitle>{`${sideEffect.patient.first_name} ${sideEffect.patient.last_name}`}</CardTitle>
                    <CardDescription>
                      Reported: {safeFormatDate(sideEffect.reported_at)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Severity
                      </p>
                      <Badge
                        variant={
                          sideEffect.severity === "major"
                            ? "destructive"
                            : sideEffect.severity === "moderate"
                              ? "default"
                              : "outline"
                        }
                      >
                        {sideEffect.severity}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Status
                      </p>
                      <Badge
                        variant={"outline"}
                        className={
                          sideEffect.status?.toLowerCase() === "submitted"
                            ? "bg-gray-100 text-gray-800 border-gray-200"
                            : sideEffect.status?.toLowerCase() === "reviewed"
                              ? "bg-blue-100 text-blue-800 border-blue-200"
                              : sideEffect.status?.toLowerCase() === "resolved"
                                ? "bg-green-100 text-green-800 border-green-200"
                                : ""
                        }
                      >
                        {sideEffect.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Description
                      </p>
                      <p className="line-clamp-2">{sideEffect.description}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(sideEffect)}
                    >
                      View Details
                      <ChevronsRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedSideEffect && (
        <SideEffectDetailModal
          isOpen={isDetailModalOpen}
          onClose={handleCloseModal}
          sideEffect={selectedSideEffect}
          onStatusUpdate={handleStatusUpdate}
          onDelete={handleDelete}
          onEdit={handleEdit}
        />
      )}

      <AddSideEffectModal
        isOpen={isAddModalOpen}
        onClose={handleCloseAddModal}
        onSuccess={fetchSideEffects}
      />
    </div>
  );
};

export default SideEffectsPage;
