import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ChatPage from "../ChatPage";
import apiClient from "@/lib/apiClient";

// Mock the Lucide icons
jest.mock("lucide-react", () => ({
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  Send: () => <div data-testid="send-icon" />,
  Bot: () => <div data-testid="bot-icon" />,
  BrainCircuit: () => <div data-testid="brain-icon" />,
  MessageSquare: () => <div data-testid="message-square-icon" />,
  Users: () => <div data-testid="users-icon" />,
  User: () => <div data-testid="user-icon" />,
  CircleUser: () => <div data-testid="circle-user-icon" />,
  SmilePlus: () => <div data-testid="smile-plus-icon" />,
  Search: () => <div data-testid="search-icon" />,
  X: () => <div data-testid="x-icon" />,
  ChevronDown: () => <div data-testid="chevron-down-icon" />,
}));

// Mock apiClient
jest.mock("@/lib/apiClient", () => ({
  get: jest.fn().mockResolvedValue({
    data: { conversations: [], total_count: 0, has_more: false },
  }),
  post: jest.fn().mockResolvedValue({
    data: {
      message: "Response message",
      message_id: "123",
      timestamp: new Date().toISOString(),
    },
  }),
}));

// Mock Clerk's useUser hook
jest.mock("@clerk/clerk-react", () => ({
  useUser: jest.fn().mockReturnValue({
    user: {
      id: "test-clinician-id",
      publicMetadata: {
        role: "clinician",
      },
    },
  }),
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    clear: () => {
      store = {};
    },
  };
})();
Object.defineProperty(window, "localStorage", { value: localStorageMock });

describe("ChatPage Routing Feature", () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();
  });

  // Setup a helper function to select a patient and enable the routing feature
  const setupWithPatient = async () => {
    const { container } = render(<ChatPage />);

    // Wait for the component to initialize
    await waitFor(() => {
      expect(apiClient.get).toHaveBeenCalled();
    });

    // Mock getting a patient
    (apiClient.get as jest.Mock).mockResolvedValueOnce({
      data: {
        conversations: [
          {
            patient_id: "test-patient-id",
            patient_first_name: "John",
            patient_last_name: "Doe",
            last_message_at: new Date().toISOString(),
            unread_count: 0,
          },
        ],
        total_count: 1,
        has_more: false,
      },
    });

    // Trigger fetchClinicianConversations
    fireEvent.click(screen.getByText(/New Conversation/i));

    // Wait for the modal to appear
    await waitFor(() => {
      expect(screen.getByText(/Start New Conversation/i)).toBeInTheDocument();
    });

    // Select a patient (if the patient list is rendered)
    (apiClient.get as jest.Mock).mockResolvedValueOnce({
      data: {
        items: [
          {
            id: "test-patient-id",
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
          },
        ],
      },
    });

    // Wait for the patient list
    await waitFor(() => {
      const patientElement = screen.queryByText(/John Doe/i);
      if (patientElement) {
        fireEvent.click(patientElement);
      }
    });

    return { container };
  };

  it("renders the routing dropdown when a patient is selected", async () => {
    await setupWithPatient();

    // The dropdown should be visible
    expect(screen.getByText(/Send to:/i)).toBeInTheDocument();
  });

  it("sends message with correct routing information", async () => {
    await setupWithPatient();

    // By default, AI mode should be selected
    expect(screen.getByText(/Send to: AI Assistant/i)).toBeInTheDocument();

    // Type a message
    const textareaElement = screen.getByPlaceholderText(
      /Type your message to the AI assistant/i,
    );
    fireEvent.change(textareaElement, { target: { value: "Test message" } });

    // Send the message
    fireEvent.click(screen.getByRole("button", { name: "" })); // Send button has no text

    // Check that the API was called with AI routing
    await waitFor(() => {
      expect(apiClient.post).toHaveBeenCalledWith(
        "/chat/messages",
        expect.objectContaining({
          message: "Test message",
          context: expect.objectContaining({
            message_route: "ai",
          }),
        }),
      );
    });

    // Now change to patient mode
    fireEvent.click(screen.getByText(/Send to: AI Assistant/i));
    await waitFor(() => {
      const patientOption = screen.getByText(/Patient/i);
      fireEvent.click(patientOption);
    });

    // Type another message
    fireEvent.change(textareaElement, {
      target: { value: "Direct patient message" },
    });

    // Send the message
    fireEvent.click(screen.getByRole("button", { name: "" }));

    // Check that the API was called with patient routing
    await waitFor(() => {
      expect(apiClient.post).toHaveBeenCalledWith(
        "/chat/messages",
        expect.objectContaining({
          message: "Direct patient message",
          context: expect.objectContaining({
            message_route: "patient",
          }),
        }),
      );
    });
  });

  it("saves the selected route to localStorage", async () => {
    await setupWithPatient();

    // Initially AI is selected
    expect(localStorageMock.getItem("clinicianMessageRoute")).toBeNull();

    // Change to patient mode
    fireEvent.click(screen.getByText(/Send to: AI Assistant/i));
    await waitFor(() => {
      const patientOption = screen.getByText(/Patient/i);
      fireEvent.click(patientOption);
    });

    // Check localStorage was updated
    expect(localStorageMock.getItem("clinicianMessageRoute")).toBe("patient");
  });

  it("loads the selected route from localStorage on mount", async () => {
    // Set localStorage value
    localStorageMock.setItem("clinicianMessageRoute", "patient");

    await setupWithPatient();

    // Check patient mode is selected
    expect(screen.getByText(/Send to: Patient/i)).toBeInTheDocument();
  });
});
