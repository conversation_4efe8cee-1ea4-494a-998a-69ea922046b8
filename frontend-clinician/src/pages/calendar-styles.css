/* Custom styling for react-calendar */
.react-calendar {
  width: 100%;
  max-width: 600px;
  background-color: white;
  font-family: inherit;
  border: none !important;
}

/* Make the navigation buttons more visible */
.react-calendar__navigation button {
  min-width: 44px;
  background: none;
  font-size: 16px;
}

/* Style the day headers (<PERSON>, <PERSON><PERSON>, etc.) - centered properly */
.react-calendar__month-view__weekdays {
  text-align: center;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
  font-weight: bold;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.8em;
  text-align: center;
}

/* Style the days */
.react-calendar__tile {
  padding: 0.75em 0.5em;
  text-align: center;
  line-height: 16px;
}

/* Style for dates with appointments */
.has-appointment {
  font-weight: bold;
  background-color: rgba(22, 163, 74, 0.1) !important;
  position: relative;
}

/* Add a small indicator dot for days with appointments */
.has-appointment::after {
  content: "";
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: #16a34a;
  border-radius: 50%;
}

/* Style for dates with appointment requests */
.has-request {
  font-weight: bold;
  background-color: rgba(251, 146, 60, 0.1) !important;
  position: relative;
}

/* Add a small orange indicator dot for days with appointment requests */
.has-request::before {
  content: "";
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: #f97316;
  border-radius: 50%;
}

/* When a date has both appointments and requests, show both dots */
.has-appointment.has-request {
  background-color: rgba(22, 163, 74, 0.1) !important;
}

.has-appointment.has-request::before {
  content: "";
  position: absolute;
  bottom: 4px;
  left: 40%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: #f97316;
  border-radius: 50%;
}

.has-appointment.has-request::after {
  content: "";
  position: absolute;
  bottom: 4px;
  left: 60%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: #16a34a;
  border-radius: 50%;
}

/* Style for the selected date */
.react-calendar__tile--active {
  background: #0f172a !important;
  color: white;
}

/* Style for today's date */
.react-calendar__tile--now {
  background: #e2e8f0;
}

/* Hover effect for tiles */
.react-calendar__tile:enabled:hover {
  background-color: #dbeafe;
}
