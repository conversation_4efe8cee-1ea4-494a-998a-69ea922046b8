import { create } from "zustand";
import { useAuth as useClerkAuth } from "@clerk/clerk-react";

type AuthState = "loading" | "authenticated" | "unauthenticated";

interface User {
  id?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  [key: string]: unknown;
}

interface AuthStoreState {
  authState: AuthState;
  user: User | null;
  error: Error | null;
  setAuthState: (state: AuthState) => void;
  setUser: (user: User | null) => void;
  setError: (error: Error | null) => void;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthStoreState>((set) => ({
  authState: "loading", // Initial state
  user: null,
  error: null,
  setAuthState: (state) => set({ authState: state, error: null }), // Clear error on state change
  setUser: (user) => set({ user }),
  setError: (error) => set({ error }),
  logout: async () => {
    try {
      // Clerk logout is handled by the Clerk SDK directly
      // This function is kept for compatibility with existing code
      console.log("Logout requested through store");

      // State updates for Clerk auth are handled by <PERSON> hooks
    } catch (error) {
      console.error("Error signing out:", error);
      // Update the store with the error
      set({ error: error as Error });
    }
  },
}));

// Selector hook for convenience with optimized memoization
export const useAuth = () => {
  const { isLoaded, isSignedIn, userId } = useClerkAuth();
  const authState = useAuthStore((state) => state.authState);
  const error = useAuthStore((state) => state.error);
  const logout = useAuthStore((state) => state.logout);

  // Map Clerk auth state to our app's auth state
  const isLoading = !isLoaded;
  const isAuthenticated = isSignedIn || false;

  return {
    authState,
    user: { id: userId }, // Provide minimal user object with ID
    isLoading,
    isAuthenticated,
    logout,
    error,
  };
};
