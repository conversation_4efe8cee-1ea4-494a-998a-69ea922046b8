// Remove React module declaration to use built-in types

// React Testing Library module declaration
declare module '@testing-library/react' {
  export const render: any;
  export const screen: any;
  export const fireEvent: any;
  export const waitFor: any;
  export const within: any;
  export const act: any;
  export const cleanup: any;
}

// Lucide React module declaration
declare module 'lucide-react' {
  import { FC, SVGProps } from 'react';
  
  type LucideIcon = FC<SVGProps<SVGSVGElement>>;
  
  export const AlertCircle: LucideIcon;
  export const AlertTriangle: LucideIcon;
  export const Calendar: LucideIcon;
  export const CalendarDays: LucideIcon;
  export const Activity: LucideIcon;
  export const Send: LucideIcon;
  export const Bot: LucideIcon;
  export const BrainCircuit: LucideIcon;
  export const MessageSquare: LucideIcon;
  export const MessageCircle: LucideIcon;
  export const Users: LucideIcon;
  export const User: LucideIcon;
  export const CircleUser: LucideIcon;
  export const SmilePlus: LucideIcon;
  export const Search: LucideIcon;
  export const X: LucideIcon;
  export const ChevronDown: LucideIcon;
  export const LayoutDashboard: LucideIcon;
  export const ClipboardList: LucideIcon;
  export const MailPlus: LucideIcon;
  export const BookOpen: LucideIcon;
  export const FileText: LucideIcon;
  export const BarChart3: LucideIcon;
  export const UserCircle: LucideIcon;
  export const Scale: LucideIcon;
  export const Pill: LucideIcon;
  export const CheckCircle: LucideIcon;
  export const XCircle: LucideIcon;
  export const Clock: LucideIcon;
  export const Save: LucideIcon;
  export const Download: LucideIcon;
  export const Maximize2: LucideIcon;
  export const Minimize2: LucideIcon;
  export const Loader2: LucideIcon;
  export const Upload: LucideIcon;
  export const Edit: LucideIcon;
  export const Plus: LucideIcon;
  export const Trash2: LucideIcon;
  export const Home: LucideIcon;
  export const Siren: LucideIcon;
  export const Link: LucideIcon;
  export const PlayCircle: LucideIcon;
  export const Play: LucideIcon;
  export const Eye: LucideIcon;
  export const Stethoscope: LucideIcon;
}

// Clerk React module declaration
declare module '@clerk/clerk-react' {
  import { ReactNode } from 'react';
  
  export const useAuth: () => {
    isLoaded: boolean;
    isSignedIn: boolean;
    userId: string | null;
  };
  
  export const useUser: () => {
    isLoaded: boolean;
    isSignedIn: boolean;
    user: {
      id: string;
      firstName?: string;
      lastName?: string;
      publicMetadata: Record<string, any>;
    } | null;
  };
  
  export const useSession: () => {
    isLoaded: boolean;
    isSignedIn: boolean;
    session: any;
  };
  
  export const useSignUp: () => any;
  
  export const UserButton: React.FC<any>;
  
  export const SignIn: React.FC<{
    routing?: string;
    path?: string;
    redirectUrl?: string;
  }>;
  
  export const SignUp: React.FC<{
    routing?: string;
    path?: string;
    redirectUrl?: string;
  }>;
  
  export const SignedIn: React.FC<{
    children: ReactNode;
  }>;
  
  export const SignedOut: React.FC<{
    children: ReactNode;
  }>;
}