// Type declarations for testing libraries
import '@testing-library/jest-dom';

// Declare global Jest variables
declare global {
  const jest: {
    fn: <T extends (...args: any[]) => any>(implementation?: T) => jest.Mock<ReturnType<T>, Parameters<T>>;
    mock: (moduleName: string, factory?: any) => jest.Mock;
    spyOn: (object: any, methodName: any) => jest.Mock;
    clearAllMocks: () => void;
    resetAllMocks: () => void;
    restoreAllMocks: () => void;
  };
  
  namespace jest {
    interface Mock<T = any, Y extends any[] = any[]> {
      (...args: Y): T;
      mockImplementation: (fn: (...args: Y) => T) => Mock<T, Y>;
      mockReturnValue: (value: T) => Mock<T, Y>;
      mockReturnValueOnce: (value: T) => Mock<T, Y>;
      mockResolvedValue: <U>(value: U) => Mock<Promise<U>, Y>;
      mockResolvedValueOnce: <U>(value: U) => Mock<Promise<U>, Y>;
      mockRejectedValue: (reason: any) => Mock<Promise<never>, Y>;
      mockRejectedValueOnce: (reason: any) => Mock<Promise<never>, Y>;
      mockClear: () => Mock<T, Y>;
      mockReset: () => Mock<T, Y>;
      mockRestore: () => Mock<T, Y>;
      getMockName: () => string;
      getMockImplementation: () => ((...args: Y) => T) | undefined;
      mock: {
        calls: Y[];
        instances: T[];
        contexts: any[];
        results: Array<{ type: string; value: T }>;
        lastCall: Y;
      };
    }
  }
  
  const describe: (name: string, fn: () => void) => void;
  const it: (name: string, fn: () => void | Promise<void>) => void;
  const test: (name: string, fn: () => void | Promise<void>) => void;
  const expect: any;
  const beforeEach: (fn: () => void | Promise<void>) => void;
  const afterEach: (fn: () => void | Promise<void>) => void;
  const beforeAll: (fn: () => void | Promise<void>) => void;
  const afterAll: (fn: () => void | Promise<void>) => void;
}

// This is necessary to make the file a module
export {};