import React, { useState } from "react";
// Mock types for authentication until Firebase is properly integrated
interface AuthError {
  code: string;
  message: string;
}

// Mock authentication function until Firebase is properly integrated
const signInWithEmailAndPassword = async (
  _auth: unknown,
  email: string,
  password: string,
) => {
  console.log("Mock authentication with:", email);
  // This would be replaced with actual Firebase authentication
  if (email === "<EMAIL>" && password === "password") {
    return { user: { uid: "123", email } };
  }
  throw {
    code: "auth/invalid-credential",
    message: "Invalid credentials",
  } as AuthError;
};

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // For displaying errors
import { LucideAlertTriangle } from "lucide-react"; // Icon for alert

const LoginView: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault(); // Prevent default form submission
    setLoading(true);
    setError(null);

    try {
      const userCredential = await signInWithEmailAndPassword(
        null,
        email,
        password,
      );
      // Login successful
      console.log("Login successful:", userCredential.user);

      // TODO: Store token (next prompt)
      // const idToken = await userCredential.user.getIdToken();
      // localStorage.setItem('firebaseIdToken', idToken);

      // TODO: Redirect to dashboard or appropriate page
      // This will be implemented in a future update
    } catch (err) {
      const authError = err as AuthError;
      console.error("Login failed:", authError);
      // Provide user-friendly error messages
      switch (authError.code) {
        case "auth/invalid-email":
          setError("Invalid email address format.");
          break;
        case "auth/user-disabled":
          setError("This user account has been disabled.");
          break;
        case "auth/user-not-found":
        case "auth/wrong-password":
        case "auth/invalid-credential": // Covers wrong password/user not found in newer SDK versions
          setError("Invalid email or password.");
          break;
        default:
          setError("Login failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Clinician Portal Login</CardTitle>
          <CardDescription>
            Enter your credentials to access the portal.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin}>
            <div className="grid w-full items-center gap-4">
              {error && (
                <Alert variant="destructive">
                  <LucideAlertTriangle className="h-4 w-4" />
                  <AlertTitle>Login Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter>
          <Button
            type="submit"
            className="w-full"
            onClick={handleLogin}
            disabled={loading}
          >
            {loading ? "Logging in..." : "Login"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LoginView;
