import path from "path";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Point to the built files instead of source files
      "@pulsetrack/shared-frontend": path.resolve(
        __dirname,
        "../packages/shared-frontend/dist",
      ),
    },
  },
  optimizeDeps: {
    // Force Vite to optimize dependencies even in development mode
    force: true,
    // Explicitly include react-dom to ensure it's properly optimized
    include: ["react-dom"],
  },
  server: {
    host: true,
    port: 5173,
    watch: {
      usePolling: true,
      interval: 100,
    },
  },
});
