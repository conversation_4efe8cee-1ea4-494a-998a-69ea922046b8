import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
// Import SignedIn/SignedOut for the public login route logic
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import ProtectedRoute from './components/auth/ProtectedRoute'; // Import the new ProtectedRoute
import { ThemeProvider } from './components/theme/ThemeProvider';
// Import your page components
import WeightTrackingPage from './pages/WeightTrackingPage';
import SideEffectPage from './pages/SideEffectPage';
import MedicationRequestPage from './pages/MedicationRequestPage';
import ProfilePage from './pages/ProfilePage';
import PatientEducationPage from './pages/PatientEducationPage';
import PatientDashboardPage from './pages/PatientDashboardPage';
import ChatPage from './pages/ChatPage';
import LoginPage from './pages/LoginPage'; // Import the custom LoginPage
import { AcceptInvitationPage } from "./pages/AcceptInvitationPage";
import AppointmentsPage from './pages/AppointmentsPage'; // Import the new AppointmentsPage
import EducationMaterialDetailPage from './pages/EducationMaterialDetailPage';
// import LoginPage from './pages/LoginPage';
// Assuming AuthProvider, ProtectedRoute, PublicOnlyRoute are replaced by Clerk's components/hooks
// import { AuthProvider } from './contexts/AuthContext';
// import { ProtectedRoute, PublicOnlyRoute } from './components/auth/RouteProtection';
import MainLayout from './components/layout/MainLayout';

import './App.css';

// Main App component structure
function App() {
  // Log the API base URL from environment variables (can be kept for debugging)
  console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <BrowserRouter>
        <Routes>
        {/* Public routes */}
        {/* Custom Login Route */}
        <Route
          path="/login"
          element={
            <>
              <SignedIn>
                {/* If user is signed in, redirect away from login */}
                <Navigate to="/" replace />
              </SignedIn>
              <SignedOut>
                {/* If user is signed out, show the custom login page */}
                <LoginPage />
              </SignedOut>
            </>
          }
        />
        {/* Keep SignUp route commented out or implement if needed */}
        {/* <Route path="/sign-up/*" element={<SignUp routing="path" path="/sign-up" />} /> */}
        <Route
          path="/accept-invitation/*"
          element={
            <>
              <SignedIn>
                {/* If user is signed in, redirect to main dashboard */}
                <Navigate to="/" replace />
              </SignedIn>
              <SignedOut>
                {/* If user is signed out, show the invitation acceptance page */}
                <AcceptInvitationPage />
              </SignedOut>
            </>
          }
        />

        {/* Protected Routes: Wrap with ProtectedRoute and MainLayout */}
        <Route element={<ProtectedRoute />}>
          {/* /chat route is protected but NOT wrapped in MainLayout */}
          <Route path="/chat" element={<ChatPage />} />
          <Route element={<MainLayout />}>
            <Route path="/" element={<PatientDashboardPage />} />
            <Route path="/weight-tracking" element={<WeightTrackingPage />} />
            {/* Add route for the new medication request page */}
            <Route path="/medication-requests" element={<MedicationRequestPage />} />
            <Route path="/side-effects" element={<SideEffectPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/education" element={<PatientEducationPage />} />
            <Route path="/appointments" element={<AppointmentsPage />} />
            <Route path="/education-material/:materialId" element={<EducationMaterialDetailPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Route>
        </Route>
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  );
}

export default App;
