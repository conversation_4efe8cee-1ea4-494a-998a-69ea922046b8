import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/components/ui/card';
import { AppointmentResponse } from '@/types'; 
import { parseISO, isPast } from 'date-fns';
import { formatDateTime } from '@pulsetrack/shared-frontend';

interface AppointmentDetailCardProps {
  appointment: AppointmentResponse;
}

export const AppointmentDetailCard: React.FC<AppointmentDetailCardProps> = ({ appointment }) => {
  const appointmentDateTime = parseISO(appointment.appointment_datetime);
  const isAppointmentPast = isPast(appointmentDateTime);

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>{`Appointment with ${appointment.clinician.first_name} ${appointment.clinician.last_name}`}</CardTitle>
        <CardDescription>
          {formatDateTime(appointment.appointment_datetime)} ({appointment.duration_minutes} minutes)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p><strong>Clinician:</strong> {`${appointment.clinician.first_name} ${appointment.clinician.last_name}`}</p>
        <p className="text-sm text-muted-foreground mb-2">{appointment.clinician.email}</p>
        <p><strong>Type:</strong> {appointment.appointment_type}</p>
        {appointment.reason && <p><strong>Reason:</strong> {appointment.reason}</p>}
        {appointment.patient_notes && <p><strong>Your Notes:</strong> {appointment.patient_notes}</p>}
        <p><strong>Status:</strong> <span className={`capitalize font-medium ${appointment.status === 'cancelled' ? 'text-destructive' : appointment.status === 'completed' ? 'text-muted-foreground' : 'text-primary'}`}>{appointment.status}</span></p>
        {appointment.status === 'cancelled' && appointment.cancellation_reason && (
          <p><strong>Cancellation Reason:</strong> {appointment.cancellation_reason}</p>
        )}
        {isAppointmentPast && appointment.status !== 'cancelled' && (
          <p className="text-sm text-destructive mt-2">This appointment has already occurred.</p>
        )}
      </CardContent>
    </Card>
  );
};
