import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '@pulsetrack/shared-frontend'; // Assuming useAuth is exported from the shared package
import { AuthLoadingIndicator } from '@pulsetrack/shared-frontend'; // Assuming a shared loading indicator

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, isAuthLoading } = useAuth();

  if (isAuthLoading) {
    // You might want a more sophisticated loading screen/spinner here
    return <AuthLoadingIndicator />;
  }

  if (!isAuthenticated) {
    // Redirect them to the /login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to="/login" replace />;
  }

  return <Outlet />; // Render the child route component
};

export default ProtectedRoute;