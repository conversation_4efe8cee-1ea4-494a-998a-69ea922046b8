import { useMemo, ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext'; // Using our updated AuthContext that integrates with Clerk

// Simple Loading Component
export const LoadingSpinner = () => (
  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
    Loading...
  </div>
);

// Protected Route Logic Component
export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation(); // To store intended destination

  const routeElement = useMemo(() => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    if (!isAuthenticated) {
      // Redirect to login, saving the current location
      return <Navigate to="/login" state={{ from: location }} replace />;
    }

    return children;
  }, [isAuthenticated, isLoading, children, location]);

  return routeElement;
};

// Route for pages accessible only when logged out (like Login)
export const PublicOnlyRoute = ({ children }: { children: ReactNode }) => {
    const { isAuthenticated, isLoading } = useAuth();

    const routeElement = useMemo(() => {
      if (isLoading) {
          return <LoadingSpinner />;
      }

      if (isAuthenticated) {
          // Redirect to dashboard (or home '/') if already logged in
          return <Navigate to="/" replace />;
      }

      return children;
    }, [isAuthenticated, isLoading, children]);

    return routeElement;
};