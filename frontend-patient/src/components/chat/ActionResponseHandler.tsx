import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Check, AlertCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ChainedActionResponse } from "./ChainedActionResponse";
import { SingleActionResponse } from "./SingleActionResponse";
import ParameterHarvestingForm from "./ParameterHarvestingForm";
import CompoundParameterHarvestingForm from "./CompoundParameterHarvestingForm";

interface ActionResponseHandlerProps {
  metadata: {
    action_type: string;
    success: boolean;
    result: string;
    message: string;
    data?: any;
    missing_parameters?: Array<{
      name: string;
      type: string;
      description?: string;
      required: boolean;
      enum?: string[];
      format?: string;
    }>;
    existing_parameters?: Record<string, any>;
    // For compound actions
    compound_missing_parameters?: Array<{
      actionType: string;
      actionIndex: number;
      missingParameters: Array<{
        name: string;
        type: string;
        description?: string;
        required: boolean;
        enum?: string[];
        format?: string;
      }>;
      existingParameters?: Record<string, any>;
    }>;
  };
  onParameterSubmit?: (parameters: Record<string, any>) => void;
  onCompoundParameterSubmit?: (parameters: Record<string, Record<string, any>>) => void;
}

export const ActionResponseHandler: React.FC<ActionResponseHandlerProps> = ({ metadata, onParameterSubmit, onCompoundParameterSubmit }) => {
  const navigate = useNavigate();
  
  // Return null if no metadata is provided or metadata is invalid
  if (!metadata || typeof metadata !== 'object') return null;
  
  // Check if this is a compound parameter harvesting response
  if (metadata.compound_missing_parameters && metadata.compound_missing_parameters.length > 0) {
    return (
      <CompoundParameterHarvestingForm
        actionGroups={metadata.compound_missing_parameters}
        onSubmit={onCompoundParameterSubmit || (() => {})}
        isLoading={false}
      />
    );
  }
  
  // Check if this is a single parameter harvesting response
  if (metadata.missing_parameters && metadata.missing_parameters.length > 0) {
    return (
      <ParameterHarvestingForm
        actionType={metadata.action_type}
        missingParameters={metadata.missing_parameters}
        existingParameters={metadata.existing_parameters}
        onSubmit={onParameterSubmit || (() => {})}
        isLoading={false}
      />
    );
  }
  
  // Check if metadata has the expected properties for action responses
  // Must have action_type AND either success or module fields
  if (!('action_type' in metadata) || 
      (!('success' in metadata) && !('module' in metadata))) {
    // For non-action metadata, return null to hide the action card
    return null;
  }
  
  const { action_type, success, message, result, data } = metadata;
  
  const handleViewDetails = () => {
    switch (action_type) {
      case "weight_log_create":
        navigate("/weight-tracking");
        break;
      // Add other action types as needed
      default:
        // Default behavior if the action type isn't handled
        break;
    }
  };
  
  // Return different UI based on action type
  switch (action_type) {
    case "compound_action":
      return (
        <ChainedActionResponse
          data={data}
          message={message}
          success={success}
        />
      );
    
    // All single actions use the SingleActionResponse component
    case "appointment_create":
    case "appointment_request_create":
    case "medication_request_create":
    case "side_effect_report_create":
    case "note_create":
    case "weight_log_create":
      return (
        <SingleActionResponse
          actionType={action_type}
          success={success}
          message={message}
          data={data}
        />
      );
      
    default:
      // For unhandled action types, check if it's an actual action response
      // If it doesn't have a module field, it's likely not an LLM action
      if (!metadata.module || metadata.module !== 'LLMActionModule') {
        return null;
      }
      
      // For unhandled action types, also use SingleActionResponse for consistency
      return (
        <SingleActionResponse
          actionType={action_type}
          success={success}
          message={message}
          data={data}
        />
      );
  }
};

export default ActionResponseHandler;