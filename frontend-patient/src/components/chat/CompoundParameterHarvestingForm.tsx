import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { AlertCircle, Send, Activity, Calendar, Clock, User, FileText, AlertTriangle, ChevronDown, ChevronRight, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDate } from '@pulsetrack/shared-frontend';

interface Parameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  enum?: string[];
  format?: string;
  placeholder?: string;
  value?: any;
}

interface ActionParameterGroup {
  actionType: string;
  actionIndex: number;
  missingParameters: Parameter[];
  existingParameters?: Record<string, any>;
}

interface CompoundParameterHarvestingFormProps {
  actionGroups: ActionParameterGroup[];
  onSubmit: (allParameters: Record<string, Record<string, any>>) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

// Helper function to get all parameters for an action
const getAllParameters = (group: ActionParameterGroup): Parameter[] => {
  // Get parameter definitions from action type
  const parameterDefinitions = getParameterDefinitions(group.actionType);
  
  // Merge existing values with parameter definitions
  const allParams: Parameter[] = [];
  
  // Add existing parameters that might not be in missing list
  if (group.existingParameters) {
    Object.entries(group.existingParameters).forEach(([key, value]) => {
      // Find if this parameter is already in missing list
      const missingParam = group.missingParameters.find(p => p.name === key);
      if (!missingParam) {
        // Get definition from our known parameters
        const definition = parameterDefinitions[key] || {
          name: key,
          type: 'string',
          required: false
        };
        allParams.push({
          ...definition,
          value
        });
      }
    });
  }
  
  // Add missing parameters
  group.missingParameters.forEach(param => {
    allParams.push({
      ...param,
      value: group.existingParameters?.[param.name] || ''
    });
  });
  
  // Sort parameters: required fields first, then by name
  return allParams.sort((a, b) => {
    if (a.required && !b.required) return -1;
    if (!a.required && b.required) return 1;
    return a.name.localeCompare(b.name);
  });
};

// Define parameter metadata for better display
const getParameterDefinitions = (actionType: string): Record<string, Parameter> => {
  const commonDefs: Record<string, Record<string, Parameter>> = {
    appointment_request_create: {
      preferred_date: {
        name: 'preferred_date',
        type: 'date',
        description: 'Select your preferred appointment date',
        required: true,
        format: 'date'
      },
      preferred_time: {
        name: 'preferred_time',
        type: 'time',
        description: 'Select your preferred appointment time',
        required: true,
        format: 'time'
      },
      reason: {
        name: 'reason',
        type: 'string',
        description: 'Reason for appointment',
        required: true
      },
      clinician_preference: {
        name: 'clinician_preference',
        type: 'string',
        description: 'Preferred clinician',
        required: false
      },
      patient_id: {
        name: 'patient_id',
        type: 'string',
        description: 'Patient ID',
        required: true
      }
    },
    side_effect_report_create: {
      medication_name: {
        name: 'medication_name',
        type: 'string',
        description: 'Which medication is causing side effects?',
        required: true,
        placeholder: 'Enter medication name'
      },
      symptoms: {
        name: 'symptoms',
        type: 'string',
        description: 'Describe the symptoms you\'re experiencing',
        required: true,
        placeholder: 'Describe your symptoms...'
      },
      severity: {
        name: 'severity',
        type: 'string',
        description: 'How severe are the side effects?',
        required: true,
        enum: ['Mild', 'Moderate', 'Severe']
      },
      onset_time: {
        name: 'onset_time',
        type: 'string',
        description: 'When did the side effects start?',
        required: false,
        placeholder: 'e.g., Yesterday, 2 days ago'
      },
      additional_notes: {
        name: 'additional_notes',
        type: 'string',
        description: 'Any additional information',
        required: false
      },
      patient_id: {
        name: 'patient_id',
        type: 'string',
        description: 'Patient ID',
        required: true
      }
    }
  };
  
  return commonDefs[actionType] || {};
};

export const CompoundParameterHarvestingForm: React.FC<CompoundParameterHarvestingFormProps> = ({
  actionGroups: propActionGroups,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  // Store actionGroups in component scope for access in renderField
  const actionGroups = propActionGroups;
  // Initialize form data with all parameters (both missing and existing)
  const initialFormData: Record<string, Record<string, any>> = {};
  actionGroups.forEach(group => {
    const actionKey = `${group.actionType}_${group.actionIndex}`;
    const allParams = getAllParameters(group);
    initialFormData[actionKey] = {};
    
    // Set initial values
    allParams.forEach(param => {
      initialFormData[actionKey][param.name] = param.value || 
        group.existingParameters?.[param.name] || '';
    });
  });
  
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState<Record<string, Record<string, string>>>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>(() => {
    // Initially expand the first section and any sections with errors
    const initial: Record<string, boolean> = {};
    actionGroups.forEach((group, index) => {
      const actionKey = `${group.actionType}_${group.actionIndex}`;
      initial[actionKey] = index === 0; // Expand first section by default
    });
    return initial;
  });

  const getActionTitle = (type: string) => {
    const titles: Record<string, string> = {
      appointment_create: 'Schedule Appointment',
      appointment_request: 'Request Appointment',
      appointment_request_create: 'Request Appointment',
      side_effect_report_create: 'Report Side Effect',
      side_effect_report: 'Report Side Effect',
      weight_log_create: 'Log Weight',
      medication_request_create: 'Request Medication',
      notification_create: 'Set Reminder'
    };
    return titles[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getActionIcon = (type: string) => {
    if (type.includes('appointment')) return <Calendar className="h-4 w-4" />;
    if (type.includes('side_effect')) return <AlertTriangle className="h-4 w-4" />;
    if (type.includes('weight')) return <Activity className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const formatValue = (name: string, value: any): string => {
    if (!value) return '';
    
    // Handle patient_id display
    if (name === 'patient_id' && value.startsWith('user_')) {
      return 'You';
    }
    
    // Handle dates
    if (name.includes('date') && value) {
      try {
        return formatDate(value);
      } catch {
        return value;
      }
    }
    
    // Handle times
    if (name.includes('time') && value && !name.includes('onset')) {
      // Convert 24-hour to 12-hour format
      try {
        const [hours, minutes] = value.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
      } catch {
        return value;
      }
    }
    
    return String(value);
  };

  const toggleSection = (actionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [actionKey]: !prev[actionKey]
    }));
  };

  const isSectionComplete = (actionKey: string, group: ActionParameterGroup) => {
    const allParams = getAllParameters(group);
    const sectionData = formData[actionKey] || {};
    
    // Check if all required parameters have values
    return allParams.every(param => {
      if (!param.required) return true;
      return sectionData[param.name] && sectionData[param.name] !== '';
    });
  };

  const handleInputChange = (actionKey: string, name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [actionKey]: {
        ...prev[actionKey],
        [name]: value
      }
    }));
    
    // Clear error when user starts typing
    if (errors[actionKey]?.[name]) {
      setErrors(prev => ({
        ...prev,
        [actionKey]: {
          ...prev[actionKey],
          [name]: ''
        }
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, Record<string, string>> = {};
    const sectionsToExpand: string[] = [];
    let isValid = true;
    
    actionGroups.forEach(group => {
      const actionKey = `${group.actionType}_${group.actionIndex}`;
      const actionFormData = formData[actionKey] || {};
      const actionErrors: Record<string, string> = {};
      const allParams = getAllParameters(group);
      
      allParams.forEach(param => {
        if (param.required && !actionFormData[param.name]) {
          actionErrors[param.name] = `${param.name.replace(/_/g, ' ')} is required`;
          isValid = false;
        }
      });
      
      if (Object.keys(actionErrors).length > 0) {
        newErrors[actionKey] = actionErrors;
        sectionsToExpand.push(actionKey);
      }
    });
    
    setErrors(newErrors);
    
    // Expand sections with errors
    if (sectionsToExpand.length > 0) {
      setExpandedSections(prev => {
        const updated = { ...prev };
        sectionsToExpand.forEach(key => {
          updated[key] = true;
        });
        return updated;
      });
    }
    
    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const renderField = (actionKey: string, param: Parameter, isEditable: boolean = true): React.ReactNode => {
    const value = formData[actionKey]?.[param.name] || '';
    const fieldId = `${actionKey}_${param.name}`;
    const fieldError = errors[actionKey]?.[param.name];

    // Special handling for non-editable fields
    if (!isEditable || param.name === 'patient_id') {
      return (
        <div key={fieldId} className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <div className="p-2 bg-gray-100 rounded-md text-sm text-gray-600">
            {formatValue(param.name, value)}
          </div>
        </div>
      );
    }

    // Handle enum fields as select dropdowns
    if (param.enum && param.enum.length > 0) {
      return (
        <div key={fieldId} className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">
            {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            {param.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Select value={value} onValueChange={(val) => handleInputChange(actionKey, param.name, val)}>
            <SelectTrigger id={fieldId} className={fieldError ? 'border-red-500' : ''}>
              <SelectValue placeholder={param.placeholder || `Select ${param.name}`} />
            </SelectTrigger>
            <SelectContent>
              {param.enum.map(option => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {param.description && (
            <p className="text-xs text-gray-500">{param.description}</p>
          )}
          {fieldError && (
            <p className="text-xs text-red-500">{fieldError}</p>
          )}
        </div>
      );
    }

    // Handle date fields - check if there's a corresponding time field for consolidation
    if (param.format === 'date' || param.type === 'date' || param.name.includes('date')) {
      // Check if this action has a corresponding time field
      const group = actionGroups.find(g => `${g.actionType}_${g.actionIndex}` === actionKey);
      if (group) {
        const allParams = getAllParameters(group);
        const hasTimeField = allParams.some(p => 
          (p.name === 'preferred_time' && param.name === 'preferred_date') ||
          (p.name.includes('time') && param.name.replace('date', 'time') === p.name)
        );
        
        if (hasTimeField && param.name === 'preferred_date') {
          // Render combined date-time picker for preferred_date
          const timeValue = formData[actionKey]?.['preferred_time'] || '';
          const dateTimeValue = value && timeValue ? `${value}T${timeValue}` : value || '';
          
          return (
            <div key={fieldId} className="space-y-2">
              <Label htmlFor={fieldId} className="text-sm font-medium">
                Preferred Date & Time
                {param.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
              <Input
                id={fieldId}
                type="datetime-local"
                value={dateTimeValue}
                onChange={(e) => {
                  if (e.target.value) {
                    const [date, time] = e.target.value.split('T');
                    handleInputChange(actionKey, 'preferred_date', date);
                    handleInputChange(actionKey, 'preferred_time', time);
                  } else {
                    handleInputChange(actionKey, 'preferred_date', '');
                    handleInputChange(actionKey, 'preferred_time', '');
                  }
                }}
                className={fieldError ? 'border-red-500' : ''}
                disabled={isLoading}
              />
              {param.description && (
                <p className="text-xs text-gray-500">Select your preferred appointment date and time</p>
              )}
              {(fieldError || errors[actionKey]?.['preferred_time']) && (
                <p className="text-xs text-red-500">{fieldError || errors[actionKey]?.['preferred_time']}</p>
              )}
            </div>
          );
        }
      }
      
      // Regular date field without time consolidation
      return (
        <div key={fieldId} className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">
            {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            {param.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Input
            id={fieldId}
            type="date"
            value={value}
            onChange={(e) => handleInputChange(actionKey, param.name, e.target.value)}
            className={fieldError ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {param.description && (
            <p className="text-xs text-gray-500">{param.description}</p>
          )}
          {fieldError && (
            <p className="text-xs text-red-500">{fieldError}</p>
          )}
        </div>
      );
    }

    // Skip time fields if they're part of a date-time combo
    if (param.format === 'time' || param.type === 'time' || param.name === 'preferred_time') {
      // Check if this is part of a date-time combo
      const group = actionGroups.find(g => `${g.actionType}_${g.actionIndex}` === actionKey);
      if (group) {
        const allParams = getAllParameters(group);
        const hasDateField = allParams.some(p => p.name === 'preferred_date');
        
        if (hasDateField && param.name === 'preferred_time') {
          // Skip rendering - it's handled by the date field
          return null;
        }
      }
      
      // Check if this is a datetime field (like onset_time) rather than just time of day
      const isDateTimeField = param.name.includes('onset') || param.name.includes('occurred') || 
                             param.name.includes('started') || param.name.includes('ended') ||
                             (param.description && param.description.toLowerCase().includes('when'));
      
      if (isDateTimeField) {
        // Use DateTimePicker for fields that represent a point in time
        return (
          <div key={fieldId} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              {param.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <DateTimePicker
              value={value}
              onChange={(dateTime) => handleInputChange(actionKey, param.name, dateTime)}
              placeholder={param.placeholder || `Select ${param.name.replace(/_/g, ' ')}`}
              className={fieldError ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {param.description && (
              <p className="text-xs text-gray-500">{param.description}</p>
            )}
            {fieldError && (
              <p className="text-xs text-red-500">{fieldError}</p>
            )}
          </div>
        );
      } else {
        // Standalone time field for time-of-day
        return (
          <div key={fieldId} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              {param.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={fieldId}
              type="time"
              value={value}
              onChange={(e) => handleInputChange(actionKey, param.name, e.target.value)}
              className={fieldError ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {param.description && (
              <p className="text-xs text-gray-500">{param.description}</p>
            )}
            {fieldError && (
              <p className="text-xs text-red-500">{fieldError}</p>
            )}
          </div>
        );
      }
    }

    // Handle long text fields
    if (param.name.includes('notes') || param.name.includes('description') || 
        param.name.includes('reason') || param.name.includes('symptoms')) {
      return (
        <div key={fieldId} className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">
            {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            {param.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleInputChange(actionKey, param.name, e.target.value)}
            placeholder={param.placeholder || `Enter ${param.name}`}
            className={fieldError ? 'border-red-500' : ''}
            disabled={isLoading}
            rows={3}
          />
          {param.description && (
            <p className="text-xs text-gray-500">{param.description}</p>
          )}
          {fieldError && (
            <p className="text-xs text-red-500">{fieldError}</p>
          )}
        </div>
      );
    }

    // Default to text input
    return (
      <div key={fieldId} className="space-y-2">
        <Label htmlFor={fieldId} className="text-sm font-medium">
          {param.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          {param.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <Input
          id={fieldId}
          type="text"
          value={value}
          onChange={(e) => handleInputChange(actionKey, param.name, e.target.value)}
          placeholder={param.placeholder || `Enter ${param.name}`}
          className={fieldError ? 'border-red-500' : ''}
          disabled={isLoading}
        />
        {param.description && (
          <p className="text-xs text-gray-500">{param.description}</p>
        )}
        {fieldError && (
          <p className="text-xs text-red-500">{fieldError}</p>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full max-w-3xl border-orange-200 bg-orange-50 flex flex-col" style={{ height: '500px' }}>
      <CardHeader className="pb-4 flex-shrink-0 border-b border-orange-200">
        <div className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-lg">Complete Multiple Actions</CardTitle>
        </div>
        <CardDescription>
          Review and provide any missing information for each action below:
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow p-0 flex flex-col overflow-hidden">
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          {/* Scrollable content area */}
          <div className="flex-grow px-6 py-4 space-y-6 overflow-y-auto">
            {actionGroups.map((group, groupIndex) => {
              const actionKey = `${group.actionType}_${group.actionIndex}`;
              const allParams = getAllParameters(group);
              
              const isExpanded = expandedSections[actionKey];
              const isComplete = isSectionComplete(actionKey, group);
              const hasErrors = errors[actionKey] && Object.keys(errors[actionKey]).length > 0;
              
              return (
                <div key={actionKey} className="border border-gray-200 rounded-lg overflow-hidden">
                  {/* Action Group Header - Clickable */}
                  <button
                    type="button"
                    onClick={() => toggleSection(actionKey)}
                    className={cn(
                      "w-full px-4 py-3 flex items-center gap-2 hover:bg-gray-50 transition-colors",
                      isExpanded ? "bg-gray-50" : "bg-white",
                      hasErrors && "bg-red-50 hover:bg-red-100"
                    )}
                  >
                    <div className="flex items-center gap-2 flex-1">
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-500" />
                      )}
                      {getActionIcon(group.actionType)}
                      <h3 className="text-base font-semibold text-gray-900">
                        {groupIndex + 1}. {getActionTitle(group.actionType)}
                      </h3>
                    </div>
                    {/* Status indicator */}
                    <div className="flex items-center gap-2">
                      {isComplete && !hasErrors && (
                        <div className="flex items-center gap-1 text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          <span className="text-sm">Complete</span>
                        </div>
                      )}
                      {hasErrors && (
                        <div className="flex items-center gap-1 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          <span className="text-sm">Required fields missing</span>
                        </div>
                      )}
                    </div>
                  </button>
                  
                  {/* Action Group Fields - Collapsible */}
                  {isExpanded && (
                    <div className="px-4 py-4 border-t border-gray-200 space-y-4 bg-gray-50">
                      {allParams.map(param => {
                        const field = renderField(actionKey, param);
                        return field; // This handles null returns for skipped fields
                      }).filter(Boolean)}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {/* Fixed footer with buttons */}
          <div className="flex-shrink-0 border-t border-orange-200 px-6 py-4 bg-orange-50">
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  'Processing...'
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Complete All Actions
                  </>
                )}
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default CompoundParameterHarvestingForm;