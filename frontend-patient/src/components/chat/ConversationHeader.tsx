import React from 'react';
import { MessageSquare, Bot } from 'lucide-react';

interface ConversationHeaderProps {
  messagesExist?: boolean;
}

const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  messagesExist = false
}) => {
  return (
    <div className="flex items-center">
      <Bot className="mr-2 h-5 w-5 text-blue-600" />
      <span>Chat with AI Assistant</span>
    </div>
  );
};

export default ConversationHeader;