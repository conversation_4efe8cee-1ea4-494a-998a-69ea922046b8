import React from "react";

// Button variant type for type-safe casting
type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "outline-green"
  | "outline-blue"
  | "secondary"
  | "ghost"
  | "link";
import { ChevronDown, BrainCircuit, Stethoscope, Send } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export interface MessageRoutingOption {
  id: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface MessageRoutingDropdownProps {
  selectedRoute: MessageRoutingOption;
  options: MessageRoutingOption[];
  onRouteChange: (option: MessageRoutingOption) => void;
  disabled?: boolean;
}

const MessageRoutingDropdown: React.FC<MessageRoutingDropdownProps> = ({
  selectedRoute,
  options,
  onRouteChange,
  disabled = false,
}) => {
  // Get the default icons for known routing options if not provided
  const getOptionWithIcon = (option: MessageRoutingOption) => {
    if (option.icon) return option;

    const iconMap: Record<string, React.ReactNode> = {
      clinician: <Stethoscope className="h-4 w-4 text-green-600" />,
      ai: <BrainCircuit className="h-4 w-4 text-blue-600" />,
    };

    return {
      ...option,
      icon: iconMap[option.id.toLowerCase()] || <Send className="h-4 w-4" />,
    };
  };

  const optionsWithIcons = options.map(getOptionWithIcon);
  const selectedOptionWithIcon = getOptionWithIcon(selectedRoute);

  // Determine button variant based on the selected option
  const getButtonVariant = () => {
    switch (selectedOptionWithIcon.id) {
      case "clinician":
        return "outline-green";
      case "ai":
        return "outline-blue";
      default:
        return "outline";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button
          variant={getButtonVariant() as ButtonVariant}
          size="sm"
          className={cn(
            "h-9 gap-2 flex items-center whitespace-nowrap font-medium border-2 px-3 transition-all",
            selectedOptionWithIcon.id === "clinician" &&
              "text-green-700 hover:bg-green-50",
            selectedOptionWithIcon.id === "ai" &&
              "text-blue-700 hover:bg-blue-50",
          )}
        >
          {selectedOptionWithIcon.icon}
          <span>Send to: {selectedOptionWithIcon.label}</span>
          <ChevronDown className="h-4 w-4 opacity-60" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
          Message Routing
        </div>
        <DropdownMenuSeparator />
        {optionsWithIcons.map((option) => (
          <DropdownMenuItem
            key={option.id}
            onClick={() => onRouteChange(option)}
            className={cn(
              "flex flex-col items-start cursor-pointer py-2 px-3",
              option.id === selectedOptionWithIcon.id &&
                "bg-gray-100 dark:bg-gray-800",
            )}
          >
            <div className="flex items-center gap-2 w-full">
              {option.icon}
              <span className="font-medium">{option.label}</span>
            </div>
            {option.description && (
              <span className="text-xs text-muted-foreground ml-6 mt-0.5">
                {option.description}
              </span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MessageRoutingDropdown;