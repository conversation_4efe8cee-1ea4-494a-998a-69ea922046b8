import React, { useState, useEffect } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Bo<PERSON>, 
  Loader2, 
  AlertCircle, 
  MessageSquare, 
  Calendar,
  Weight,
  AlertTriangle,
  Pill,
  FileText,
  Activity,
  GraduationCap
} from "lucide-react";
import apiClient from "@/lib/apiClient";
import { formatRelativeTime } from "@pulsetrack/shared-frontend";
import { Badge } from "@/components/ui/badge";

interface EventLog {
  id: string;
  actor_user_id: string;
  actor_role: string;
  action: string;
  target_resource_type?: string;
  target_resource_id?: string;
  details?: Record<string, any>;
  extracted_intent?: string;
  is_llm_driven?: boolean;
  created_at: string;
  outcome: string;
}

export const PatientActivityFeed: React.FC = () => {
  const [activities, setActivities] = useState<EventLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get<EventLog[]>("/event-logs/", {
          params: {
            limit: 10,
            skip: 0,
          },
        });
        setActivities(response.data);
      } catch (err: any) {
        console.error("Error fetching recent activities:", err);
        setError(err.response?.data?.detail || "Failed to fetch activities");
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();

    // Refresh every 60 seconds for patients (less frequent than clinician dashboard)
    const interval = setInterval(fetchActivities, 60000);
    return () => clearInterval(interval);
  }, []);

  const getActivityIcon = (event: EventLog) => {
    // Icon based on resource type and action
    if (event.is_llm_driven || event.extracted_intent) {
      return <Bot className="h-4 w-4" />;
    }
    
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    const action = event.action.toLowerCase();
    
    if (resourceType.includes("message") || action.includes("chat")) {
      return <MessageSquare className="h-4 w-4" />;
    }
    if (resourceType.includes("appointment")) {
      return <Calendar className="h-4 w-4" />;
    }
    if (resourceType.includes("weight")) {
      return <Weight className="h-4 w-4" />;
    }
    if (resourceType.includes("side_effect")) {
      return <AlertTriangle className="h-4 w-4" />;
    }
    if (resourceType.includes("medication")) {
      return <Pill className="h-4 w-4" />;
    }
    if (resourceType.includes("note") || resourceType.includes("clinical")) {
      return <FileText className="h-4 w-4" />;
    }
    if (resourceType.includes("education")) {
      return <GraduationCap className="h-4 w-4" />;
    }
    
    return <Activity className="h-4 w-4" />;
  };

  const getActivityColor = (event: EventLog) => {
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    
    if (event.is_llm_driven || event.extracted_intent) {
      return "bg-blue-100 text-blue-600";
    }
    if (resourceType.includes("side_effect")) {
      return "bg-red-100 text-red-600";
    }
    if (resourceType.includes("appointment")) {
      return "bg-green-100 text-green-600";
    }
    if (resourceType.includes("weight")) {
      return "bg-purple-100 text-purple-600";
    }
    if (resourceType.includes("medication")) {
      return "bg-orange-100 text-orange-600";
    }
    if (resourceType.includes("education")) {
      return "bg-yellow-100 text-yellow-600";
    }
    
    return "bg-gray-100 text-gray-600";
  };

  const formatActivityDescription = (event: EventLog): string => {
    // For patients, we simplify the descriptions and make them more personal
    const resourceType = event.target_resource_type?.toLowerCase() || "";
    const action = event.action.toUpperCase();
    
    // Handle LLM-driven actions with intent
    if (event.extracted_intent) {
      return `AI Assistant: ${event.extracted_intent}`;
    }
    
    // Custom descriptions for specific actions
    if (resourceType === "chat_message") {
      if (action === "CREATE") {
        if (event.actor_role === "patient") {
          return "You sent a message";
        } else if (event.actor_role === "clinician") {
          return "Your clinician responded";
        } else if (event.actor_role === "ai_agent") {
          return "AI Assistant responded";
        }
      }
    }
    
    if (resourceType === "weight_log") {
      if (action === "CREATE") {
        const weight = event.details?.weight_kg;
        return weight ? `You logged weight: ${weight} kg` : "You logged your weight";
      }
    }
    
    if (resourceType === "side_effect_report") {
      if (action === "CREATE") {
        const severity = event.details?.severity;
        return severity ? `You reported ${severity} side effects` : "You reported side effects";
      }
    }
    
    if (resourceType === "appointment") {
      if (action === "CREATE") {
        return "You scheduled an appointment";
      }
      if (action === "UPDATE") {
        return "Your appointment was updated";
      }
      if (action === "COMPLETE") {
        return "You completed an appointment";
      }
    }
    
    if (resourceType === "medication_request") {
      if (action === "CREATE") {
        return "You requested medication";
      }
      if (action === "APPROVE") {
        return "Your medication request was approved";
      }
      if (action === "REJECT") {
        return "Your medication request was declined";
      }
    }
    
    if (resourceType === "patient_education_assignment") {
      if (action === "CREATE") {
        return "New education material assigned to you";
      }
      if (action === "UPDATE" && event.details?.status === "completed") {
        return "You completed education material";
      }
    }
    
    // Default formatting for patient perspective
    const resource = event.target_resource_type?.replace(/_/g, " ") || "activity";
    switch (action) {
      case "CREATE":
        return `You created a ${resource}`;
      case "UPDATE":
        return `You updated your ${resource}`;
      case "DELETE":
        return `You deleted a ${resource}`;
      case "VIEW":
        return `You viewed ${resource}`;
      case "COMPLETE":
        return `You completed ${resource}`;
      default:
        return `${event.action} on ${resource}`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2 text-muted-foreground">Loading activities...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-48 text-destructive">
        <AlertCircle className="h-5 w-5 mr-2" />
        <span>{error}</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activities.length === 0 ? (
        <p className="text-center text-muted-foreground py-4">
          No recent activity to display
        </p>
      ) : (
        activities.map((activity, index) => (
          <div
            key={activity.id}
            className={`flex items-start space-x-3 ${
              index < activities.length - 1 ? "pb-4 border-b border-border" : ""
            }`}
          >
            <Avatar className={`h-8 w-8 mt-1 ${getActivityColor(activity)}`}>
              <AvatarFallback>
                {getActivityIcon(activity)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <p className="text-sm text-muted-foreground">
                  {activity.created_at && !isNaN(new Date(activity.created_at).getTime())
                    ? formatRelativeTime(activity.created_at)
                    : "Recently"}
                </p>
                {activity.outcome === "FAILURE" && (
                  <Badge variant="outline" className="text-red-600 text-xs">Failed</Badge>
                )}
              </div>
              <p className="text-sm font-medium">
                {formatActivityDescription(activity)}
              </p>
              {(activity.is_llm_driven || activity.extracted_intent) && (
                <span className="text-xs text-blue-600 font-medium">
                  AI-Assisted
                </span>
              )}
              {activity.details?.notes && (
                <p className="text-xs text-muted-foreground mt-1 truncate">
                  {activity.details.notes}
                </p>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  );
};