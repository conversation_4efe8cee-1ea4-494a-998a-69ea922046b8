import React, { useState, useEffect } from 'react';
import apiClient from '@/lib/apiClient';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@pulsetrack/shared-frontend';

interface PatientDashboardHeaderProps {
  showMotivationalMessage?: boolean;
  motivationalMessage?: string;
}

const PatientDashboardHeader: React.FC<PatientDashboardHeaderProps> = ({
  showMotivationalMessage = true,
  motivationalMessage = "You're doing great. Let's check in today.",
}) => {
  const [firstName, setFirstName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { signOut } = useAuth();

  useEffect(() => {
    const fetchPatientName = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiClient.get('/patients/me');
        setFirstName(response.data.first_name);
      } catch (err: unknown) {
        console.error("Error fetching patient name:", err);
        setError("Failed to load greeting.");
        setFirstName(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPatientName();
  }, []);

  const renderGreeting = () => {
    if (isLoading) {
      return <Skeleton className="h-8 w-48 bg-gray-200 rounded" />;
    }
    if (error || !firstName) {
      return <span className="text-xl font-semibold">PulseTrack</span>;
    }
    return <span className="text-xl font-semibold">PulseTrack</span>;
  };

  const renderMotivationalMessage = () => {
    if (isLoading || error || !showMotivationalMessage) {
      return null;
    }
    return <p className="mt-1 text-sm text-white/90">{motivationalMessage}</p>;
  };

  return (
    <header className="w-full bg-[#4A90E2] text-white px-6 py-4 shadow-md">
      <div className="flex justify-between items-center max-w-screen-xl mx-auto">
        <div>
          {renderGreeting()}
          {renderMotivationalMessage()}
        </div>
        <button 
          onClick={() => signOut()}
          className="text-white/90 hover:text-white transition-colors text-sm font-medium px-4 py-2 rounded-md hover:bg-[#357ABD]"
        >
          Logout
        </button>
      </div>
    </header>
  );
};

export default PatientDashboardHeader;