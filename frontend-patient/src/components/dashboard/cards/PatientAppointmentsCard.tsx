import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Calendar } from 'lucide-react';
import { parseISO } from 'date-fns';
import { getWithAuth } from '@/lib/utils';
import { AppointmentResponse, PaginatedAppointments } from '@/types';
import { formatDate, formatTime } from '@pulsetrack/shared-frontend';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
const APPOINTMENTS_LIMIT = 3; // How many upcoming appointments to fetch for the card

const PatientAppointmentsCard: React.FC = () => {
  const [appointments, setAppointments] = useState<AppointmentResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUpcomingAppointments = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await getWithAuth(
          `${API_BASE_URL}/patients/me/appointments?limit=${APPOINTMENTS_LIMIT}`
        ) as PaginatedAppointments;
        setAppointments(response.items || []);
      } catch (err) {
        console.error('Failed to fetch upcoming appointments for dashboard:', err);
        setError('Could not load upcoming appointments.');
      } finally {
        setLoading(false);
      }
    };

    fetchUpcomingAppointments();
  }, []);

  const renderContent = () => {
    if (loading) {
      return <Skeleton className="h-10 w-full" />;
    }

    if (error) {
      return (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="mr-1 h-4 w-4 flex-shrink-0" /> {error}
        </div>
      );
    }

    if (appointments.length === 0) {
      return <p className="text-sm text-muted-foreground">No upcoming appointments scheduled.</p>;
    }

    // Display details of the next upcoming appointment
    const nextAppointment = appointments[0];
    const appointmentDateTime = parseISO(nextAppointment.appointment_datetime);

    return (
      <div className="space-y-2">
        <p className="font-semibold">
          Next: {formatDate(nextAppointment.appointment_datetime)} at {formatTime(nextAppointment.appointment_datetime)}
        </p>
        <p className="text-sm text-muted-foreground">
          With {nextAppointment.clinician.first_name} {nextAppointment.clinician.last_name} ({nextAppointment.appointment_type})
        </p>
        {/* Optionally show more appointments if needed */}
      </div>
    );
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-primary" />
          Upcoming Appointments
        </CardTitle>
        <CardDescription>
          Your next scheduled appointments.
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        {renderContent()}
      </CardContent>
      <div className="p-4 pt-0 mt-auto"> {/* Ensure button is at the bottom */}
        <Button variant="outline" size="sm" asChild className="w-full">
          <Link to="/appointments">View All Appointments</Link>
        </Button>
      </div>
    </Card>
  );
};

export default PatientAppointmentsCard;
