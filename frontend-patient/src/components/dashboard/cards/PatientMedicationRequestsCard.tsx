import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Pill, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { getWithAuth } from '@/lib/utils'; 

// Get API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Define the structure of a single Medication Request item from the API
interface MedicationRequest {
  id: string;
  medication_name: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  // Add other fields if needed, but status is key here
}

// Define the expected structure of the API response
interface MedicationRequestListResponse {
  items: MedicationRequest[];
  // Include pagination fields if the API uses them
  total?: number;
  page?: number;
  size?: number;
  pages?: number;
}

// Define the type for the status counts
interface StatusCounts {
  pending: number;
  approved: number;
  rejected: number;
}

const PatientMedicationRequestsCard: React.FC = () => {
  const [counts, setCounts] = useState<StatusCounts>({ pending: 0, approved: 0, rejected: 0 }); 
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMedicationRequests = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Fetch all requests - adjust limit if necessary and API supports pagination properly
      // Remove generic type argument from getWithAuth, use type assertion on result
      const response = (await getWithAuth(
        `${API_BASE_URL}/patients/me/medication-requests?limit=500` // Corrected path: Removed duplicate /api/v1
      )) as MedicationRequestListResponse; // Type assertion

      const requests = response.items || [];
      // Add explicit types to reduce parameters
      const statusCounts = requests.reduce(
        (acc: StatusCounts, request: MedicationRequest) => {
          const status = request.status.trim().toUpperCase(); // Trim and convert to uppercase
          if (status === 'PENDING') acc.pending++;
          else if (status === 'APPROVED') acc.approved++;
          else if (status === 'REJECTED') acc.rejected++;
          return acc;
        },
        { pending: 0, approved: 0, rejected: 0 } // Initial value matches StatusCounts
      );
      setCounts(statusCounts);
    } catch (err) {
      console.error('Failed to fetch medication requests:', err);
      setError('Could not load medication request data.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchMedicationRequests();
  }, [fetchMedicationRequests]);

  return (
    <Link to="/medication-requests" className="block">
      <Card className="hover:bg-muted/50 transition-colors h-full"> {/* Ensure card takes full height */}
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Pill className="h-5 w-5 text-primary" />
            Medication Requests
          </CardTitle>
          <CardDescription>
            Status of your recent requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ) : error ? (
            <p className="text-sm text-destructive flex items-center gap-2">
              <AlertCircle className="h-4 w-4" /> {error}
            </p>
          ) : (
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li className="flex items-center justify-between">
                <span className="flex items-center gap-1.5">
                   <AlertCircle className="h-4 w-4 text-yellow-500" /> Pending
                </span>
                <span>{counts.pending}</span>
              </li>
              <li className="flex items-center justify-between">
                 <span className="flex items-center gap-1.5">
                    <CheckCircle className="h-4 w-4 text-green-500" /> Approved
                 </span>
                <span>{counts.approved}</span>
              </li>
              <li className="flex items-center justify-between">
                <span className="flex items-center gap-1.5">
                  <XCircle className="h-4 w-4 text-red-500" /> Rejected
                </span>
                <span>{counts.rejected}</span>
              </li>
            </ul>
          )}
        </CardContent>
      </Card>
    </Link>
  );
};

export default PatientMedicationRequestsCard;
