import React, { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";
import { getWithAuth } from "@/lib/utils";

interface SeveritySummary {
  minor: number;
  moderate: number;
  major: number;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";

const PatientSideEffectsCard: React.FC = () => {
  const [summary, setSummary] = useState<SeveritySummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSummary = async () => {
      setLoading(true);
      try {
        const data = await getWithAuth(`${API_BASE_URL}/patients/me/side-effects/summary`);
        setSummary(data);
      } catch (error) {
        console.error("Failed to fetch side effect summary:", error);
        setSummary(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSummary();
  }, []);

  return (
    <Link to="/side-effects">
      <Card className="hover:bg-muted/50 transition-colors h-full flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-primary" />
            Side Effects
          </CardTitle>
          <CardDescription>
            Report and view your side effects
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-grow flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <p className="text-sm flex-shrink mr-4">
            Log any side effects you are experiencing and view your history.
          </p>
          <div className="flex-shrink-0 space-y-1 text-sm w-full sm:w-auto">
            {loading ? (
              <div className="text-muted-foreground">Loading counts...</div>
            ) : summary && (summary.minor > 0 || summary.moderate > 0 || summary.major > 0) ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-blue-600 font-medium">Minor:</span>
                  <span className="font-semibold">{summary.minor}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-yellow-600 font-medium">Moderate:</span>
                  <span className="font-semibold">{summary.moderate}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-red-600 font-medium">Major:</span>
                  <span className="font-semibold">{summary.major}</span>
                </div>
              </>
            ) : (
              <div className="text-muted-foreground italic">No reports</div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default PatientSideEffectsCard;
