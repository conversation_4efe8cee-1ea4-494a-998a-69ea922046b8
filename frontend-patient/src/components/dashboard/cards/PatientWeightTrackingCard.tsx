import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Minus, Scale } from "lucide-react";
import { getWithAuth } from "@/lib/utils";
import { formatWeight } from "@/lib/weightUtils";

interface WeightLogEntry {
  id: string;
  weight_kg: number;
  log_date: string;
  bmi?: number;
}

interface GoalWeightSummary {
  has_goal: boolean;
  goal_weight_kg?: number;
  current_weight_kg?: number;
  progress_percentage?: number;
  is_goal_achieved: boolean;
  trend?: string;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";

export const PatientWeightTrackingCard: React.FC = () => {
  const [weightHistory, setWeightHistory] = useState<WeightLogEntry[]>([]);
  const [goalSummary, setGoalSummary] = useState<GoalWeightSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch both weight history and goal summary in parallel
        const [weightRes, goalRes] = await Promise.allSettled([
          getWithAuth(`${API_BASE_URL}/patients/me/weight-log?limit=2`),
          getWithAuth(`${API_BASE_URL}/patients/me/goal-weight/summary`)
        ]);
        
        // Process weight history
        if (weightRes.status === 'fulfilled') {
          const items: WeightLogEntry[] = (weightRes.value?.items || []).sort(
            (a: WeightLogEntry, b: WeightLogEntry) =>
              new Date(b.log_date).getTime() - new Date(a.log_date).getTime()
          );
          setWeightHistory(items);
        } else {
          setWeightHistory([]);
        }
        
        // Process goal summary
        if (goalRes.status === 'fulfilled') {
          setGoalSummary(goalRes.value as GoalWeightSummary);
        }
      } catch {
        setWeightHistory([]);
        setGoalSummary(null);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Calculate weight trend
  let trendIcon = <Minus className="h-4 w-4 text-muted-foreground" />;
  let trendText = "No change";
  if (weightHistory.length >= 2) {
    const diff = weightHistory[0].weight_kg - weightHistory[1].weight_kg;
    const diffLbs = diff * 2.20462;
    if (diff > 0) {
      trendIcon = <TrendingUp className="h-4 w-4 text-red-500" />;
      trendText = `+${diff.toFixed(2)} kg / +${diffLbs.toFixed(2)} lbs`;
    } else if (diff < 0) {
      trendIcon = <TrendingDown className="h-4 w-4 text-green-500" />;
      trendText = `${diff.toFixed(2)} kg / ${diffLbs.toFixed(2)} lbs`;
    }
  }

  return (
      <Link to="/weight-tracking" style={{ textDecoration: "none" }}>
          <Card tabIndex={0} role="button" className="cursor-pointer hover:bg-muted/50 transition-colors focus:ring-2 focus:ring-primary focus:outline-none">
              <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                      <Scale className="h-5 w-5 text-primary" />
                      Weight Tracking
                  </CardTitle>
                  <CardDescription>
                      View your most recent weight and trend
                  </CardDescription>
              </CardHeader>
              <CardContent>
                  {loading ? (
                      <div className="text-muted-foreground">Loading...</div>
                  ) : (
                      <div>
                          <div className="font-semibold">Latest Weight</div>
                          {weightHistory.length > 0 ? (
                              <>
                                  <div className="flex flex-col gap-1">
                                      <div className="flex items-center gap-2">
                                          <span className="text-xl font-bold">
                                              {formatWeight(weightHistory[0].weight_kg).kg} kg / {formatWeight(weightHistory[0].weight_kg).lbs} lbs
                                          </span>
                                      </div>
                                      <div className="flex items-center gap-2 text-sm">
                                          <span className="flex items-center gap-1">
                                              {trendIcon}
                                              {trendText}
                                          </span>
                                          <span className="ml-2 text-xs text-muted-foreground">
                                              {new Date(weightHistory[0].log_date).toLocaleDateString()}
                                          </span>
                                      </div>
                                  </div>
                                  {goalSummary?.has_goal && (
                                      <div className="mt-2 pt-2 border-t">
                                          <div className="flex items-center justify-between text-sm">
                                              <span className="text-muted-foreground">Goal Progress</span>
                                              <span className="font-medium">
                                                  {goalSummary.progress_percentage 
                                                      ? `${goalSummary.progress_percentage.toFixed(0)}%`
                                                      : 'N/A'}
                                              </span>
                                          </div>
                                          {goalSummary.goal_weight_kg && (
                                              <div className="text-xs text-muted-foreground mt-1">
                                                  Goal: {formatWeight(goalSummary.goal_weight_kg).kg} kg / {formatWeight(goalSummary.goal_weight_kg).lbs} lbs
                                                  {goalSummary.is_goal_achieved && (
                                                      <span className="ml-2 text-green-600">✓ Achieved</span>
                                                  )}
                                              </div>
                                          )}
                                      </div>
                                  )}
                              </>
                          ) : (
                              <div className="text-muted-foreground">No weight data available.</div>
                          )}
                      </div>
                  )}
              </CardContent>
          </Card>
      </Link>
  );
};

export default PatientWeightTrackingCard;