import { Dialog, DialogContent } from '@/components/ui/dialog';
import DocumentViewer from './DocumentViewer';

interface DocumentViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  materialId: string;
  title: string;
}

export default function DocumentViewerModal({ 
  isOpen, 
  onClose, 
  materialId, 
  title 
}: DocumentViewerModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] p-0">
        <DocumentViewer
          materialId={materialId}
          title={title}
          onClose={onClose}
          className="h-full"
        />
      </DialogContent>
    </Dialog>
  );
}