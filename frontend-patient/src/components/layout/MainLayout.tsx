import React from 'react';
import { Outlet } from 'react-router-dom';
import PatientHeaderBar from './PatientHeaderBar';
import PatientBottomNavigation from './PatientBottomNavigation';

const MainLayout: React.FC<React.PropsWithChildren> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Sticky Header */}
      <PatientHeaderBar />

      <div className="flex flex-1">
        {/* Main Content Area - padding and background match admin */}
        <main className="flex-1 p-6 bg-gray-50 dark:bg-gray-900 pb-20">
          {children ?? <Outlet />}
        </main>
      </div>

      {/* Fixed Bottom Navigation */}
      <PatientBottomNavigation />
    </div>
  );
};

export default MainLayout;

