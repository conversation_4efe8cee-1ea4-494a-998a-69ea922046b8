import React from 'react';
import { NavLink } from 'react-router-dom';
import { LayoutDashboard, CalendarDays, UserCircle, Scale, Activity, Pill, MessageCircle, BookOpen } from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ElementType;
}

// Define navigation items for the Patient portal
// Chat is placed first to emphasize it as the primary interaction point
const navItems: NavItem[] = [
  { path: '/chat', label: 'Chat', icon: MessageCircle },
  { path: '/', label: 'Dashboard', icon: LayoutDashboard },
  { path: '/appointments', label: 'Appointments', icon: CalendarDays },
  { path: '/weight-tracking', label: 'Weight', icon: Scale },
  { path: '/side-effects', label: 'Effects', icon: Activity },
  { path: '/medication-requests', label: 'Medications', icon: Pill },
  { path: '/education', label: 'Education', icon: BookOpen },
  { path: '/profile', label: 'Profile', icon: UserCircle },
];

const PatientBottomNavigation: React.FC = () => {
  const activeClassName = "text-primary";
  const inactiveClassName = "text-muted-foreground";

  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-background border-t border-border shadow-md z-50">
      <div className="flex justify-around items-center h-full w-full max-w-2xl mx-auto px-4">
        {navItems.map((item) => {
          const isChat = item.path === '/chat';
          
          return (
            <NavLink
              key={item.path}
              to={item.path}
              end={item.path === '/'}
              className={({ isActive }) =>
                `flex flex-col items-center justify-center p-2 rounded-md transition-all duration-150 ease-in-out ${
                  isActive 
                    ? isChat 
                      ? 'text-white bg-gradient-to-br from-blue-500 to-teal-600 shadow-lg scale-110' 
                      : 'text-blue-600 bg-blue-50' 
                    : isChat 
                      ? 'text-teal-700 bg-gradient-to-br from-teal-50 to-blue-50 hover:from-teal-100 hover:to-blue-100' 
                      : inactiveClassName
                } hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 ${
                  isChat ? 'relative' : ''
                }`
              }
            >
              {({ isActive }) => (
                <>
                  {isChat && !isActive && (
                    <div className="absolute -top-1 -right-1 h-2.5 w-2.5 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full animate-pulse shadow-sm" />
                  )}
                  <item.icon 
                    className={`${isChat ? 'h-7 w-7' : 'h-6 w-6'} mb-1`} 
                    strokeWidth={isActive ? 2.5 : 2} 
                  />
                  <span className={`text-xs font-medium ${isChat && isActive ? 'font-bold' : ''}`}>
                    {item.label}
                  </span>
                </>
              )}
            </NavLink>
          );
        })}
      </div>
    </nav>
  );
};

export default PatientBottomNavigation;