import React from 'react';
import { useAuth } from '@pulsetrack/shared-frontend';
import { But<PERSON> } from '@/components/ui/button';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

/**
 * PatientHeaderBar - Sticky header for the patient dashboard.
 */
const PatientHeaderBar: React.FC = () => {
  const { signOut } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full bg-background border-b border-border shadow-sm">
      <div className="flex justify-between items-center h-14 px-4">
        <span className="text-lg font-semibold">
          PulseTrack Patient
        </span>
        <div className="flex items-center gap-2">
          <ThemeToggle />
          <Button variant="ghost" onClick={signOut}>
            Logout
          </Button>
        </div>
      </div>
    </header>
  );
};

export default PatientHeaderBar;