import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, ControllerRenderProps } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  // FormDescription, // Removed as it's no longer used
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';

interface MedicationSelectItem {
  id: string;
  name: string;
}

const formSchema = z.object({
  medication_name: z.string().min(1, {
    message: 'Please select a medication.',
  }),
  notes: z.string().min(1, {
    message: 'Notes cannot be empty.',
  }),
});

export type PatientMedicationRequestFormData = z.infer<typeof formSchema>;

interface PatientMedicationRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: PatientMedicationRequestFormData) => Promise<void>;
  isSubmitting: boolean;
  availableMedications: MedicationSelectItem[];
}

export const PatientMedicationRequestForm: React.FC<PatientMedicationRequestFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting,
  availableMedications,
}) => {
  const form = useForm<PatientMedicationRequestFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      medication_name: '',
      notes: '',
    },
  });

  const handleFormSubmit = async (values: PatientMedicationRequestFormData) => {
    await onSubmit(values);
  };

  React.useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [isOpen, form]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Request New Medication Refill</DialogTitle>
          <DialogDescription>
            Select your medication and add any necessary notes.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6 py-4">
            <FormField
              control={form.control}
              name="medication_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Medication Name <span className="text-red-500">*</span></FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a medication..." />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableMedications.length === 0 && (
                        <SelectItem value="loading" disabled>Loading medications...</SelectItem>
                      )}
                      {availableMedications.map((med) => (
                        <SelectItem key={med.id} value={med.name}>
                          {med.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }: { field: ControllerRenderProps<PatientMedicationRequestFormData, 'notes'> }) => (
                <FormItem>
                  <FormLabel>Additional Notes <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Include any relevant details or questions..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
                ) : (
                  'Submit Request'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
