import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, Scale, Pill, Siren, BookOpen, MessageSquare, User } from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ElementType;
}

const navItems: NavItem[] = [
  { path: '/', label: 'Home', icon: Home },
  { path: '/weight-tracking', label: 'Log Weight', icon: Scale },
  { path: '/medication-request', label: 'Request Med', icon: Pill },
  { path: '/report-side-effect', label: 'Report Symptom', icon: Siren },
  { path: '/education', label: 'Education', icon: BookOpen },
  { path: '/chat', label: 'Chat', icon: MessageSquare },
  { path: '/profile', label: 'Profile', icon: User },
];

const PatientQuickAccessNavigation: React.FC = () => {
  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-background border-t border-border shadow-md z-50">
      <div className="flex justify-around items-center h-full max-w-md mx-auto px-2">
        {navItems.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            end={item.path === '/'}
            className={({ isActive }) =>
              `flex flex-col items-center justify-center p-2 rounded-md transition-colors duration-150 ease-in-out ${
                isActive 
                  ? 'text-primary bg-primary/10' 
                  : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
              } focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50`
            }
          >
            {({ isActive }) => (
              <>
                <item.icon className="h-6 w-6 mb-1" strokeWidth={isActive ? 2.5 : 2} />
                <span className="text-xs font-medium">{item.label}</span>
              </>
            )}
          </NavLink>
        ))}
      </div>
    </nav>
  );
};

export default PatientQuickAccessNavigation;
