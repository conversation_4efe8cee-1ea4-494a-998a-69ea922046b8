import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatDateTime } from "@pulsetrack/shared-frontend";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface DateTimePickerProps {
  value?: string | Date;
  onChange: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
}

export function DateTimePicker({
  value,
  onChange,
  placeholder = "Pick a date and time",
  className,
  disabled = false,
  minDate,
  maxDate,
}: DateTimePickerProps) {
  const [open, setOpen] = React.useState(false);
  
  // Parse the value into a Date object
  const dateValue = React.useMemo(() => {
    if (!value) return undefined;
    if (value instanceof Date) return value;
    // Handle ISO string or datetime-local format
    const parsed = new Date(value);
    return isNaN(parsed.getTime()) ? undefined : parsed;
  }, [value]);

  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(dateValue);
  const [selectedHour, setSelectedHour] = React.useState<string>(
    dateValue ? format(dateValue, "HH") : "09"
  );
  const [selectedMinute, setSelectedMinute] = React.useState<string>(
    dateValue ? format(dateValue, "mm") : "00"
  );

  React.useEffect(() => {
    if (dateValue) {
      setSelectedDate(dateValue);
      setSelectedHour(format(dateValue, "HH"));
      setSelectedMinute(format(dateValue, "mm"));
    }
  }, [dateValue]);

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return;
    
    setSelectedDate(date);
    
    // Combine date with time
    const newDateTime = new Date(date);
    newDateTime.setHours(parseInt(selectedHour));
    newDateTime.setMinutes(parseInt(selectedMinute));
    
    // Format as ISO string with timezone
    onChange(newDateTime.toISOString());
  };

  const handleTimeChange = (type: "hour" | "minute", value: string) => {
    if (type === "hour") {
      setSelectedHour(value);
    } else {
      setSelectedMinute(value);
    }

    if (selectedDate) {
      const newDateTime = new Date(selectedDate);
      newDateTime.setHours(type === "hour" ? parseInt(value) : parseInt(selectedHour));
      newDateTime.setMinutes(type === "minute" ? parseInt(value) : parseInt(selectedMinute));
      
      onChange(newDateTime.toISOString());
    }
  };

  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, "0"));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, "0"));

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !dateValue && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {dateValue ? (
            formatDateTime(dateValue)
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            initialFocus
            disabled={(date) => {
              if (minDate && date < minDate) return true;
              if (maxDate && date > maxDate) return true;
              return false;
            }}
          />
          <div className="border-t mt-3 pt-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="flex gap-1 items-center">
                <Select
                  value={selectedHour}
                  onValueChange={(value) => handleTimeChange("hour", value)}
                >
                  <SelectTrigger className="w-[70px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {hours.map((hour) => (
                      <SelectItem key={hour} value={hour}>
                        {hour}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm">:</span>
                <Select
                  value={selectedMinute}
                  onValueChange={(value) => handleTimeChange("minute", value)}
                >
                  <SelectTrigger className="w-[70px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {minutes.map((minute) => (
                      <SelectItem key={minute} value={minute}>
                        {minute}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}