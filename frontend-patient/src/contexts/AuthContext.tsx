import { createContext, useContext, ReactNode } from 'react';
import { useAuth as useClerkAuth, useSession } from '@clerk/clerk-react';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // Use Clerk's authentication hooks
  const { isLoaded, isSignedIn } = useClerkAuth();
  const session = useSession();

  // Map Clerk's auth state to our app's auth state
  const isAuthenticated = isSignedIn || false;
  const isLoading = !isLoaded;

  // Keep the login function for backward compatibility
  // In a real implementation, this would use Clerk's signIn method
  const login = (token: string) => {
    // Store the token in localStorage for backward compatibility
    localStorage.setItem('patientToken', token);
    
    // Note: With Clerk, we don't need to manually set authentication state
    // as it's handled by Clerk's hooks
  };

  // Use Clerk's signOut method for logout
  const logout = async () => {
    try {
      // Remove the token from localStorage for backward compatibility
      localStorage.removeItem('patientToken');
      
      // Use Clerk's signOut method
      if (isSignedIn && session.session) {
        await session.session.end();
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};