import axios from 'axios';
import { getSessionToken } from './clerk';

// Retrieve API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'; // Default to /api/v1 if not set

// Create an Axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Lock mechanism for handling 401 errors
let isHandling401 = false;
// Removed unused variable: lastAuthFailureTime

// Helper function to reset the 401 handling flag after a delay
const resetAuthHandling = () => {
  setTimeout(() => {
    isHandling401 = false;
    console.log('Auth handling lock reset');
  }, 1000); // Reset after 1 second
};

// Add a request interceptor to attach the authentication token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // First try to get the Clerk session token
      const clerkToken = await getSessionToken();
      
      if (clerkToken) {
        config.headers.Authorization = `Bearer ${clerkToken}`;
        console.log('Clerk token attached to request:', config.url);
        return config;
      }
      
      // Allow public endpoints to proceed without a token
      if (config.url?.includes('/auth/') || config.url?.includes('/content/')) {
        return config;
      }
      
      // If no Clerk token was found, log it.
      // The request will proceed without the Authorization header.
      // Public routes are allowed. Protected routes will likely get a 401 response,
      // which is handled by the response interceptor.
      console.log('No Clerk authentication token found, proceeding without Authorization header:', config.url);
    } catch (error) {
      console.error('Error getting authentication token:', error);
      // Continue with the request even if token retrieval fails
    }
    return config;
  },
  (error) => {
    console.error('Error in request interceptor:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor for handling common errors (e.g., 401 Unauthorized)
apiClient.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    console.error('Error in response interceptor:', error.response || error.message);
    
    if (error.response && error.response.status === 401) {
      // Prevent multiple simultaneous 401 handlers
      if (isHandling401) {
        console.warn('Already handling a 401 error, ignoring subsequent ones.');
        return Promise.reject(error);
      }
      
      isHandling401 = true;
      console.warn('Unauthorized request (401). Redirecting to login.');
      
      // Redirect with visual indicator and slight delay
      if (window.location.pathname !== '/login') {
        // Add visual indicator that a redirect is happening
        document.body.style.opacity = '0.5'; // Dim the screen slightly
        
        // Force a small delay to let React finish current rendering cycle before navigation
        setTimeout(() => {
          window.location.href = '/login';
        }, 50); // 50ms delay
        
        // Reset the flag after a longer delay (allows redirect to fully initiate)
        resetAuthHandling();
      } else {
        // If already on login page, just reset the flag after delay
        resetAuthHandling();
      }
    }
    
    return Promise.reject(error); // Ensure error is still rejected outside the 401 block
  }
);

export default apiClient;