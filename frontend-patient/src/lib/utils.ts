import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { getSessionToken } from "./clerk";

/**
 * Creates an authenticated fetch request using Clerk's templated token
 * This should be used for all API calls that require authentication
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  // Get a fresh token with the template for each request
  const token = await getSessionToken();
  
  if (!token) {
    throw new Error('No auth token available. Are you logged in?');
  }

  const headers = {
    ...options.headers,
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

/**
 * Helper for making GET requests with authentication
 */
/**
 * Helper for making GET requests with authentication
 * @param url The URL to fetch
 * @param options Request options including optional AbortSignal
 * @returns Promise with the parsed JSON response
 */
export const getWithAuth = async (url: string, options: RequestInit = {}) => {
  try {
    // Ensure signal is passed to fetchWithAuth
    const response = await fetchWithAuth(url, { ...options, method: 'GET' });
    
    if (!response.ok) {
      // Attempt to parse error details from JSON response
      const errorData = await response.json().catch(() => ({
        detail: `HTTP error! status: ${response.status} ${response.statusText}`
      }));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status} ${response.statusText}`);
    }
    
    // Check content type to handle non-JSON responses gracefully
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      if (response.status === 204) {
          return null;
      }
      return response.json();
    } else {
      console.warn('Response not JSON:', contentType);
      return [];
    }
  } catch (error) {
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.debug('Request was aborted:', url);
      throw error;
    }
    console.error('Error in getWithAuth:', error);
    throw error;
  }
};

/**
 * Helper for making POST requests with authentication
 */
export const postWithAuth = async <T extends object>(url: string, data: T, options: RequestInit = {}) => {
  // Log request payload for debugging
  console.log('Request payload:', data);
  
  const response = await fetchWithAuth(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}` }));
    // Log full error response
    console.error('Full error response:', errorData);
    throw new Error(JSON.stringify(errorData) || `HTTP error! status: ${response.status}`);
  }
  
  return response.json();
};

/**
 * Helper for making PATCH requests with authentication
 */
export const patchWithAuth = async <T extends object>(url: string, data: T, options: RequestInit = {}) => {
  // Log request payload for debugging
  console.log('PATCH request payload:', data);
  
  const response = await fetchWithAuth(url, {
    ...options,
    method: 'PATCH',
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}` }));
    // Log full error response
    console.error('Full error response:', errorData);
    throw new Error(JSON.stringify(errorData) || `HTTP error! status: ${response.status}`);
  }
  
  return response.json();
};

/**
 * Helper for making PUT requests with authentication
 */
export const putWithAuth = async <T extends object>(url: string, data: T, options: RequestInit = {}) => {
  // Log request payload for debugging
  console.log('PUT request payload:', data);
  
  const response = await fetchWithAuth(url, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}` }));
    // Log full error response
    console.error('Full error response:', errorData);
    throw new Error(JSON.stringify(errorData) || `HTTP error! status: ${response.status}`);
  }
  
  return response.json();
};

/**
 * Helper for making DELETE requests with authentication
 */
export const deleteWithAuth = async (url: string, options: RequestInit = {}) => {
  console.log('DELETE request to:', url);
  
  const response = await fetchWithAuth(url, {
    ...options,
    method: 'DELETE',
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}` }));
    console.error('Full error response:', errorData);
    throw new Error(JSON.stringify(errorData) || `HTTP error! status: ${response.status}`);
  }

  // Explicitly handle 204 No Content
  if (response.status === 204) {
    return null;
  }

  // Defensive: if content-length is zero, return null
  const contentLength = response.headers.get("content-length");
  if (contentLength === "0") {
    return null;
  }

  // Defensive: if no content-type or not JSON, return null
  const contentType = response.headers.get("content-type");
  if (!contentType || !contentType.includes("application/json")) {
    return null;
  }

  // Try to parse JSON, but catch empty body errors
  try {
    return await response.json();
  } catch {
    return null;
  }
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
