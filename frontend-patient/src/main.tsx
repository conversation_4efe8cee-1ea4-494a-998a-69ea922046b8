import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { Toaster } from "@/components/ui/sonner"; // Import Toaster
import { AuthProvider } from '@pulsetrack/shared-frontend'; // Import shared AuthProvider

// Import your Publishable Key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key")
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AuthProvider publishableKey={PUBLISHABLE_KEY}>
      <App />
      <Toaster richColors /> {/* Add Toaster here */}
    </AuthProvider>
  </StrictMode>,
)
