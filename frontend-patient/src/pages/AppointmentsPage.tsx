import React, { useState, useEffect, useCallback } from 'react';
import { getWithAuth, deleteWithAuth, postWithAuth } from '@/lib/utils';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { AxiosError } from 'axios';
import { format, isSameDay, isPast, startOfDay, parseISO } from 'date-fns';
import { AppointmentDetailCard } from '@/components/appointments/AppointmentDetailCard';
import { AppointmentResponse, PaginatedAppointments } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Added Card components
import { Badge } from '@/components/ui/badge'; // Added Badge
import { Button } from '@/components/ui/button'; // Added Button
import { formatDateTime, formatDate, formatAppointmentTime, formatForTimeInput, convertToUTC } from '@pulsetrack/shared-frontend';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import '@/styles/calendar.css'; // Import calendar styles

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// --- Define types for Appointment Requests --- START
// Basic Clinician Info (matching backend schema)
interface ClinicianBasicInfo {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  specialty?: string; // Added specialty field as optional
  // Add other fields if needed, e.g., photo_url
}

interface AppointmentRequestResponse {
  id: string;
  patient_id: string;
  preferred_datetime: string; // ISO string
  reason: string | null;
  clinician_preference: string | null; // The ID
  preferred_clinician: ClinicianBasicInfo | null; // The fetched details (optional)
  status: 'pending' | 'scheduled' | 'cancelled' | 'rejected'; // Assuming possible statuses
  created_at: string; // ISO string
  // Add other relevant fields if needed, e.g., reviewed_at, review_notes
}

interface PaginatedAppointmentRequests {
  items: AppointmentRequestResponse[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
// --- Define types for Appointment Requests --- END

const AppointmentsPage: React.FC = () => {
  const [appointments, setAppointments] = useState<AppointmentResponse[]>([]);
  const [appointmentRequests, setAppointmentRequests] = useState<AppointmentRequestResponse[]>([]); // State for requests
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(startOfDay(new Date()));
  const [displayedAppointment, setDisplayedAppointment] = useState<AppointmentResponse | null>(null);

  // State for cancellation dialogs
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [appointmentToCancel, setAppointmentToCancel] = useState<AppointmentResponse | null>(null);
  const [requestToCancel, setRequestToCancel] = useState<AppointmentRequestResponse | null>(null);
  const [cancelling, setCancelling] = useState(false);
  
  // State for appointment request dialog
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [preferredDateTime, setPreferredDateTime] = useState<Date | undefined>(undefined);
  const [reason, setReason] = useState('');
  const [clinicianPreference, setClinicianPreference] = useState<string | null>(null);
  const [primaryClinician, setPrimaryClinician] = useState<ClinicianBasicInfo | null>(null);
  // State to track loading state of primary clinician fetch
  const [isLoadingClinician, setLoadingClinician] = useState(false);

  // Function to cancel a confirmed appointment
  const cancelAppointment = async (appointmentId: string) => {
    setCancelling(true);
    try {
      // Use the correct endpoint for canceling confirmed appointments
      await postWithAuth(`${API_BASE_URL}/appointments/${appointmentId}/cancel`, {});
      toast.success('Appointment cancelled successfully');
      fetchData();
    } catch (err) {
      console.error('Failed to cancel appointment:', err);
      toast.error('Failed to cancel appointment. Please try again.');
    } finally {
      setCancelling(false);
      setCancelDialogOpen(false);
      setAppointmentToCancel(null);
    }
  };

  // Function to cancel an appointment request
  const cancelAppointmentRequest = async (requestId: string) => {
    setCancelling(true);
    try {
      await deleteWithAuth(`${API_BASE_URL}/patients/me/appointment-requests/${requestId}`);
      toast.success('Appointment request cancelled successfully');
      fetchData();
    } catch (err) {
      console.error('Failed to cancel appointment request:', err);
      toast.error('Failed to cancel appointment request. Please try again.');
    } finally {
      setCancelling(false);
      setCancelDialogOpen(false);
      setRequestToCancel(null);
    }
  };

  // Open cancel dialog for confirmed appointment
  const openCancelAppointmentDialog = (appointment: AppointmentResponse) => {
    setAppointmentToCancel(appointment);
    setRequestToCancel(null);
    setCancelDialogOpen(true);
  };

  // Open cancel dialog for appointment request
  const openCancelRequestDialog = (request: AppointmentRequestResponse) => {
    setRequestToCancel(request);
    setAppointmentToCancel(null);
    setCancelDialogOpen(true);
  };

  // Combined fetch function
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch confirmed appointments
      const apptPromise = getWithAuth(
        `${API_BASE_URL}/patients/me/appointments`
      ) as Promise<PaginatedAppointments>;

      // Fetch appointment requests
      const requestPromise = getWithAuth(
        `${API_BASE_URL}/patients/me/appointment-requests`
      ) as Promise<PaginatedAppointmentRequests>;

      // Wait for both fetches to complete
      const [apptResponse, requestResponse] = await Promise.all([apptPromise, requestPromise]);

      const fetchedAppointments = apptResponse.items || [];
      const fetchedRequests = requestResponse.items || [];

      setAppointments(fetchedAppointments);
      setAppointmentRequests(fetchedRequests);

      // --- Set default displayed appointment to TODAY's confirmed appointment ---
      const today = startOfDay(new Date());
      const appointmentsToday = fetchedAppointments.filter(appt =>
        isSameDay(parseISO(appt.appointment_datetime), today)
      );

      if (appointmentsToday.length > 0) {
        appointmentsToday.sort((a, b) => new Date(a.appointment_datetime).getTime() - new Date(b.appointment_datetime).getTime());
        setDisplayedAppointment(appointmentsToday[0]);
      } else {
        setDisplayedAppointment(null);
      }
      // --- End default display logic ---

    } catch (err: unknown) {
      console.error('Failed to fetch appointment data:', err);
      let errorMessage = 'Failed to load appointment data.';
      if (err instanceof AxiosError) {
        const responseData = err.response?.data as Record<string, unknown>;
        if (responseData && typeof responseData.detail === 'string') {
          errorMessage = responseData.detail;
        } else {
          errorMessage = err.message || errorMessage;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      toast.error('Could not load appointment history or requests.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Function to fetch primary clinician
  const fetchPrimaryClinician = useCallback(async () => {
    setLoadingClinician(true);
    try {
      // Use the new endpoint to fetch the primary clinician
      const response = await getWithAuth(`${API_BASE_URL}/patients/me/primary-clinician`);
      console.log('Primary clinician response:', response);
      setPrimaryClinician(response);
      // Set the clinician preference to the primary clinician's ID
      setClinicianPreference(response.id);
    } catch (err) {
      console.error('Failed to fetch primary clinician:', err);
      // Don't show error toast as this is optional
      setPrimaryClinician(null);
    } finally {
      setLoadingClinician(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Fetch primary clinician when the request dialog is opened
  useEffect(() => {
    if (requestDialogOpen) {
      fetchPrimaryClinician();
    }
  }, [requestDialogOpen, fetchPrimaryClinician]);

  // --- Update Calendar Modifiers --- START
  const confirmedAppointmentDates = appointments.map(appt => startOfDay(parseISO(appt.appointment_datetime)));
  const requestedAppointmentDates = appointmentRequests.map(req => startOfDay(parseISO(req.preferred_datetime)));
  
  // Find days that have both types of appointments
  const confirmedDatesStr = confirmedAppointmentDates.map(date => date.toISOString());
  const requestedDatesStr = requestedAppointmentDates.map(date => date.toISOString());
  const bothTypesStr = confirmedDatesStr.filter(date => requestedDatesStr.includes(date));
  const bothTypesDates = bothTypesStr.map(dateStr => new Date(dateStr));
  
  // Filter out days with both types from the individual arrays
  const onlyConfirmedDates = confirmedAppointmentDates.filter(
    date => !bothTypesDates.some(bothDate => isSameDay(date, bothDate))
  );
  const onlyRequestedDates = requestedAppointmentDates.filter(
    date => !bothTypesDates.some(bothDate => isSameDay(date, bothDate))
  );

  const modifiers = {
    hasConfirmedAppointment: onlyConfirmedDates,
    hasRequestedAppointment: onlyRequestedDates,
    hasBothAppointmentTypes: bothTypesDates,
    past: (date: Date) => isPast(date) && !isSameDay(date, new Date()), // Style past days differently
  };

  const modifiersStyles = {
    hasConfirmedAppointment: {
      fontWeight: 'bold',
      backgroundImage: 'radial-gradient(circle at 50% 85%, hsl(142, 76%, 36%) 0, hsl(142, 76%, 36%) 3px, transparent 3px)',
    },
    hasRequestedAppointment: {
      fontWeight: 'normal',
      backgroundImage: 'radial-gradient(circle at 50% 85%, hsl(38, 92%, 50%) 0, hsl(38, 92%, 50%) 3px, transparent 3px)',
    },
    hasBothAppointmentTypes: {
      fontWeight: 'bold',
      backgroundImage: 'radial-gradient(circle at 40% 85%, hsl(142, 76%, 36%) 0, hsl(142, 76%, 36%) 3px, transparent 3px), radial-gradient(circle at 60% 85%, hsl(38, 92%, 50%) 0, hsl(38, 92%, 50%) 3px, transparent 3px)',
    },
    past: {
      color: 'hsl(var(--muted-foreground) / 0.6)',
      fontStyle: 'italic',
    },
  };
  // --- Update Calendar Modifiers --- END

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      // Prioritize showing confirmed appointment details
      const appointmentsOnDay = appointments.filter(app =>
        isSameDay(parseISO(app.appointment_datetime), date)
      );
      setDisplayedAppointment(appointmentsOnDay.length > 0 ? appointmentsOnDay[0] : null);
    } else {
      setDisplayedAppointment(null);
    }
  };

  // Helper to format status
  const formatStatus = (status: AppointmentRequestResponse['status']) => {
    switch (status) {
      case 'pending': return <Badge variant="outline">Pending</Badge>;
      case 'scheduled': return <Badge variant="default">Scheduled</Badge>;
      case 'cancelled': return <Badge variant="destructive">Cancelled</Badge>;
      case 'rejected': return <Badge variant="secondary">Rejected</Badge>;
      default: return <Badge variant="secondary">{status}</Badge>;
    }
  };


  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6 lg:p-8">
        <h1 className="text-3xl font-bold mb-6">My Appointments</h1>
        <Skeleton className="h-[350px] w-full mb-4" />
        <Skeleton className="h-[100px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 md:p-6 lg:p-8">
        <h1 className="text-3xl font-bold mb-6">My Appointments</h1>
        <div className="flex items-center justify-center text-destructive">
          <AlertCircle className="mr-2 h-5 w-5" /> {error}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8 space-y-8 relative">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">My Appointments</h1>
        <div className="flex gap-2">
          <Button
            onClick={() => setRequestDialogOpen(true)}
            className="bg-primary hover:bg-primary/90"
          >
            Request Appointment
          </Button>
        </div>
      </div>

      <div className="bg-card p-4 rounded-lg shadow flex justify-center">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          modifiers={modifiers}
          modifiersStyles={modifiersStyles}
          className="rounded-md border"
        />
      </div>

      {/* Display Confirmed Appointment Detail or No Appointment Message */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">
          {selectedDate ? `Details for ${formatDate(selectedDate)}` : 'Selected Appointment Details'}
        </h2>
        {displayedAppointment ? (
          <AppointmentDetailCard appointment={displayedAppointment} />
        ) : selectedDate ? (
           <p className="text-muted-foreground">No confirmed appointments scheduled for this date.</p>
        ) : (
           <p className="text-muted-foreground">Select a date to see appointment details.</p>
        )}
      </div>

      {/* --- Appointment Requests List --- START */}
      <Card>
        <CardHeader>
          <CardTitle>Requested Appointments</CardTitle>
        </CardHeader>
        <CardContent>
          {appointmentRequests.length > 0 ? (
            <ul className="space-y-4">
              {appointmentRequests.map((request) => (
                <li key={request.id} className="border p-4 rounded-md flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                  <div>
                    <p className="font-medium">
                      Preferred: {formatDateTime(request.preferred_datetime)}
                    </p>
                    {request.reason && <p className="text-sm text-muted-foreground">Reason: {request.reason}</p>}
                    {/* Display Clinician Name if available, otherwise the ID */}
                    {request.preferred_clinician ? (
                      <p className="text-sm text-muted-foreground">
                        Clinician Pref: {request.preferred_clinician.first_name} {request.preferred_clinician.last_name}
                      </p>
                    ) : request.clinician_preference ? (
                      <p className="text-sm text-muted-foreground">Clinician Pref ID: {request.clinician_preference}</p>
                    ): null}
                     <p className="text-sm text-muted-foreground">Requested: {formatDateTime(request.created_at)}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {formatStatus(request.status)}
                    {request.status === 'pending' && !isPast(parseISO(request.preferred_datetime)) && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive border-destructive hover:bg-destructive/10"
                        onClick={() => openCancelRequestDialog(request)}
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground">You have no pending appointment requests.</p>
          )}
        </CardContent>
      </Card>
      {/* --- Appointment Requests List --- END */}

      {/* --- All Confirmed Appointments List --- START */}
      <Card>
        <CardHeader>
          <CardTitle>All Confirmed Appointments</CardTitle>
        </CardHeader>
        <CardContent>
          {appointments.length > 0 ? (
            <ul className="space-y-4">
              {appointments
                .sort((a, b) => new Date(a.appointment_datetime).getTime() - new Date(b.appointment_datetime).getTime())
                .map((appointment) => {
                  const appointmentDate = parseISO(appointment.appointment_datetime);
                  const isPastAppointment = isPast(appointmentDate) && !isSameDay(appointmentDate, new Date());
                  const isToday = isSameDay(appointmentDate, new Date());
                  
                  return (
                    <li
                      key={appointment.id}
                      className={`border p-4 rounded-md flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 ${
                        isPastAppointment ? 'border-muted' : isToday ? 'border-primary' : 'border-secondary'
                      }`}
                    >
                      <div>
                        <div className="flex items-center gap-2">
                          <p className="font-medium">
                            {formatDateTime(appointment.appointment_datetime)}
                          </p>
                          {isPastAppointment && (
                            <Badge variant="outline">Past</Badge>
                          )}
                          {isToday && (
                            <Badge variant="default">Today</Badge>
                          )}
                          {!isPastAppointment && !isToday && (
                            <Badge variant="secondary">Upcoming</Badge>
                          )}
                        </div>
                        {appointment.clinician && (
                          <p className="text-sm text-muted-foreground">
                            Clinician: {appointment.clinician.first_name} {appointment.clinician.last_name}
                          </p>
                        )}
                        {appointment.reason && (
                          <p className="text-sm text-muted-foreground">Reason: {appointment.reason}</p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {isPastAppointment ? (
                          <Badge variant="outline">Completed</Badge>
                        ) : (
                          <>
                            <Badge variant="default">Scheduled</Badge>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-destructive border-destructive hover:bg-destructive/10"
                              onClick={() => openCancelAppointmentDialog(appointment)}
                            >
                              Cancel
                            </Button>
                          </>
                        )}
                      </div>
                    </li>
                  );
                })}
            </ul>
          ) : (
            <p className="text-muted-foreground">You have no confirmed appointments.</p>
          )}
        </CardContent>
      </Card>
      {/* --- All Confirmed Appointments List --- END */}

      {/* Confirmation Dialog */}
      <AlertDialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {appointmentToCancel ? 'Cancel Appointment' : 'Cancel Appointment Request'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {appointmentToCancel && (
                <div>
                  Are you sure you want to cancel your appointment scheduled for{' '}
                  <strong>{formatDateTime(appointmentToCancel.appointment_datetime)}</strong>?
                  <div className="mt-2">This action cannot be undone.</div>
                </div>
              )}
              {requestToCancel && (
                <div>
                  Are you sure you want to cancel your appointment request for{' '}
                  <strong>{formatDateTime(requestToCancel.preferred_datetime)}</strong>?
                  <div className="mt-2">This action cannot be undone.</div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={cancelling}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={cancelling}
              onClick={(e) => {
                e.preventDefault();
                if (appointmentToCancel) {
                  cancelAppointment(appointmentToCancel.id);
                } else if (requestToCancel) {
                  cancelAppointmentRequest(requestToCancel.id);
                }
              }}
              className="bg-destructive hover:bg-destructive/90"
            >
              {cancelling ? 'Cancelling...' : 'Yes, cancel'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* New Appointment Request Dialog */}
      <AlertDialog open={requestDialogOpen} onOpenChange={setRequestDialogOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Request New Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide your preferred date, time, and reason for the appointment.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="preferred-date" className="text-sm font-medium">
                Preferred Date and Time
              </label>
              <div className="flex justify-center">
                <Calendar
                  mode="single"
                  selected={preferredDateTime}
                  onSelect={setPreferredDateTime}
                  disabled={(date) => date < startOfDay(new Date())}
                  className="rounded-md border"
                />
              </div>
              {preferredDateTime && (
                <div className="flex justify-center mt-2">
                  <input
                    type="time"
                    className="border rounded px-2 py-1"
                    value={preferredDateTime ? formatForTimeInput(preferredDateTime) : ''}
                    onChange={(e) => {
                      if (preferredDateTime && e.target.value) {
                        const [hours, minutes] = e.target.value.split(':').map(Number);
                        const newDate = new Date(preferredDateTime);
                        newDate.setHours(hours, minutes);
                        setPreferredDateTime(newDate);
                      }
                    }}
                  />
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <label htmlFor="reason" className="text-sm font-medium">
                Reason for Appointment
              </label>
              <textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="Please describe the reason for your appointment request"
              />
            </div>
            
            {/* Show loading state or primary clinician selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Primary Clinician
              </label>
              {isLoadingClinician ? (
                <div className="p-2 border rounded-md flex items-center justify-center">
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : primaryClinician ? (
                <>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <div className="flex-1">
                      <p className="font-medium">{primaryClinician.first_name} {primaryClinician.last_name}</p>
                      {primaryClinician.specialty && (
                        <p className="text-sm text-muted-foreground">{primaryClinician.specialty}</p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setClinicianPreference(null)}
                      className={clinicianPreference ? "bg-primary/10" : ""}
                    >
                      {clinicianPreference ? "Selected" : "Not Selected"}
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Your primary clinician will be assigned to this appointment request by default.
                    Click "Not Selected" if you prefer any available clinician.
                  </p>
                </>
              ) : (
                <div className="p-2 border rounded-md">
                  <p className="text-sm text-muted-foreground">No primary clinician assigned.</p>
                </div>
              )}
            </div>
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel disabled={submitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={submitting || !preferredDateTime || !reason}
              onClick={(e) => {
                e.preventDefault();
                submitAppointmentRequest();
              }}
              className="bg-primary hover:bg-primary/90"
            >
              {submitting ? 'Submitting...' : 'Submit Request'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );

  // Function to submit appointment request
  async function submitAppointmentRequest() {
    if (!preferredDateTime || !reason) {
      toast.error('Please provide both preferred date/time and reason');
      return;
    }

    setSubmitting(true);
    try {
      const requestData = {
        preferred_datetime: preferredDateTime.toISOString(),
        reason: reason,
        clinician_preference: clinicianPreference || undefined
      };
      
      console.log('Submitting appointment request:', requestData);
      
      // Submit the request without storing the unused response
      await postWithAuth(
        `${API_BASE_URL}/patients/me/appointment-requests`,
        requestData
      );

      toast.success('Appointment request submitted successfully');
      setRequestDialogOpen(false);
      
      // Reset form
      setPreferredDateTime(undefined);
      setReason('');
      setClinicianPreference(null);
      
      // Refresh data to show the new request
      fetchData();
    } catch (err) {
      console.error('Failed to submit appointment request:', err);
      toast.error('Failed to submit appointment request. Please try again.');
    } finally {
      setSubmitting(false);
    }
  }
};

export default AppointmentsPage;
