import React, { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Send, Bot, ChevronDown, MessageSquare, User } from "lucide-react";
import MainLayout from "@/components/layout/MainLayout";
import { cn } from "@/lib/utils";
import apiClient from "@/lib/apiClient";
import ActionResponseHandler from "@/components/chat/ActionResponseHandler";
import ConversationHeader from "@/components/chat/ConversationHeader";
import MessageRoutingDropdown, { MessageRoutingOption } from "@/components/chat/MessageRoutingDropdown";
import { formatChatTimestamp } from "@pulsetrack/shared-frontend";

interface ChatMessage {
  id?: string;
  sender: "user" | "agent" | "patient" | "clinician";
  message: string;
  timestamp?: string;
  status?: "sent" | "delivered" | "read";
  routedTo?: "patient" | "ai" | "clinician";
  metadata?: any;
}

// API response interface for chat history
interface ChatHistoryResponseAPI {
  messages: {
    message_id: string;
    sender_type: "user" | "assistant" | "patient" | "agent" | "clinician";
    message: string;
    timestamp: string;
    is_read: boolean;
    status?: "sent" | "delivered" | "read";
    message_route?: string;
    metadata?: any;
  }[];
  total_count: number;
  has_more: boolean;
}

// Helper function to format date separators in chat
const formatMessageDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  // Check if the date is today
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  }
  
  // Check if the date is yesterday
  if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  }
  
  // Otherwise, return full date
  return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' });
};

const shouldGroupMessages = (currentMsg: ChatMessage, prevMsg?: ChatMessage): boolean => {
  if (!prevMsg) return false;
  if (currentMsg.sender !== prevMsg.sender) return false;
  
  // Check if messages are within 5 minutes of each other
  if (currentMsg.timestamp && prevMsg.timestamp) {
    const currentTime = new Date(currentMsg.timestamp).getTime();
    const prevTime = new Date(prevMsg.timestamp).getTime();
    const fiveMinutesMs = 5 * 60 * 1000;
    
    return currentTime - prevTime < fiveMinutesMs;
  }
  
  return false;
};

const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState("");
  const [isLoadingResponse, setIsLoadingResponse] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chatEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState<boolean>(false);
  const [unreadMessagesCount, setUnreadMessagesCount] = useState<number>(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [justSentMessage, setJustSentMessage] = useState(false);
  
  // Store pending compound action metadata
  const [pendingCompoundAction, setPendingCompoundAction] = useState<any>(null);
  
  // Message routing options for patient chat
  const messageRoutingOptions: MessageRoutingOption[] = [
    {
      id: "ai",
      label: "AI Health Coach",
      description: "Chat with AI for health guidance and action assistance",
    },
    {
      id: "clinician",
      label: "My Clinician",
      description: "Send message directly to your healthcare provider",
    },
  ];

  // Load saved preference from localStorage or default to AI
  const getSavedRoutePreference = (): MessageRoutingOption => {
    if (typeof window === "undefined") return messageRoutingOptions[0]; // Default to AI

    try {
      const savedRouteId = localStorage.getItem("patientMessageRoute");
      if (savedRouteId) {
        const savedOption = messageRoutingOptions.find(
          (option) => option.id === savedRouteId,
        );
        if (savedOption) return savedOption;
      }
    } catch (error) {
      console.error("Error accessing localStorage:", error);
    }

    return messageRoutingOptions[0]; // Default to AI if no saved preference or error
  };

  const [selectedRoute, setSelectedRoute] = useState<MessageRoutingOption>(
    getSavedRoutePreference(),
  );

  // Save preference to localStorage when it changes
  const handleRouteChange = (newRoute: MessageRoutingOption) => {
    setSelectedRoute(newRoute);

    try {
      localStorage.setItem("patientMessageRoute", newRoute.id);
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  };
  
  // Note: Conversation list functionality removed for patient chat simplification

  // Scroll to bottom function
  const scrollToBottom = (behavior: ScrollBehavior = "smooth") => {
    if (behavior === "instant" && messagesContainerRef.current) {
      // For instant scroll, directly set scrollTop to ensure no animation
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    } else {
      chatEndRef.current?.scrollIntoView({ behavior });
    }
  };
  

  // Check if the user has scrolled up and should show the button
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    // Show button if user has scrolled up more than 100px from bottom
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    const shouldShowButton = !isNearBottom && scrollHeight > clientHeight;
    
    setShowScrollButton(shouldShowButton);
    
    // Calculate approximate number of messages not in view
    if (shouldShowButton) {
      // Estimate how many messages are below the viewport based on average message height
      // Assuming average message height is around 100px (very rough estimate)
      const avgMessageHeight = 100;
      const pixelsBelowViewport = scrollHeight - scrollTop - clientHeight;
      const estimatedMessagesBelow = Math.min(
        Math.ceil(pixelsBelowViewport / avgMessageHeight), 
        99 // Cap at 99 to avoid UI issues
      );
      setUnreadMessagesCount(estimatedMessagesBelow);
    } else {
      setUnreadMessagesCount(0);
    }
  }, []);

  // Add scroll event listener to the messages container
  useEffect(() => {
    const messagesContainer = messagesContainerRef.current;
    if (messagesContainer) {
      messagesContainer.addEventListener('scroll', handleScroll);
      return () => messagesContainer.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Scroll to bottom when messages change, but only if appropriate
  useEffect(() => {
    // Skip if no messages
    if (messages.length === 0) {
      return;
    }
    
    // When messages first load (initial load), instantly jump to bottom
    if (isInitialLoad) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom("instant");
        setIsInitialLoad(false);
        handleScroll();
      }, 0);
      return;
    }
    
    // Skip any scrolling if we're still loading
    if (isLoadingHistory) {
      return;
    }
    
    // Only auto-scroll if we're not currently showing parameter forms or if user just sent a message
    const hasParameterForms = messages.some(msg => 
      msg.sender === "agent" && 
      msg.metadata &&
      typeof msg.metadata === 'object' &&
      (('missing_parameters' in msg.metadata) || 
       ('compound_missing_parameters' in msg.metadata) ||
       ('all_parameters' in msg.metadata))
    );
    
    // Auto-scroll only if:
    // 1. User just sent a message OR
    // 2. User is near the bottom already (and no parameter forms)
    const shouldAutoScroll = justSentMessage || (!hasParameterForms && !showScrollButton);
    
    if (shouldAutoScroll) {
      scrollToBottom("smooth");
    }
    
    // Check scroll position after a short delay
    setTimeout(handleScroll, 100);
  }, [messages, handleScroll, justSentMessage, showScrollButton, isInitialLoad, isLoadingHistory]);

  // Fetch chat history on component mount
  useEffect(() => {
    const fetchChatHistory = async () => {
      setIsLoadingHistory(true);
      setError(null);
      
      try {
        const response = await apiClient.get<ChatHistoryResponseAPI>('/chat/history?limit=100');
        
        if (response.data && response.data.messages && Array.isArray(response.data.messages)) {
          // Map uppercase sender types from DB to lowercase for display
          const senderMap: Record<string, "user" | "agent" | "patient" | "clinician"> = {
            "USER": "user",
            "AGENT": "agent", 
            "PATIENT": "patient",
            "CLINICIAN": "clinician"
          };
          
          const formattedMessages: ChatMessage[] = response.data.messages.map(msg => ({
            id: msg.message_id,
            sender: senderMap[msg.sender_type] || msg.sender_type.toLowerCase() as any,
            message: msg.message,
            timestamp: msg.timestamp,
            status: msg.status || (msg.is_read && msg.sender_type === 'patient' ? 'read' : 'sent'),
            routedTo: msg.message_route as "patient" | "ai" | "clinician" | undefined,
            metadata: msg.metadata,
          }));
          setMessages(formattedMessages);
        }
      } catch (err) {
        console.error("Error fetching chat history:", err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("Failed to load chat history. Please try again later.");
        }
      } finally {
        setIsLoadingHistory(false);
      }
    };
    
    fetchChatHistory();
  }, []);

  const sendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!input.trim()) return;
    setError(null);
    setIsLoadingResponse(true);

    // Optimistically add user message with current timestamp
    const userMessage: ChatMessage = { 
      sender: "patient", 
      message: input,
      timestamp: new Date().toISOString(),
      status: "sent",
      routedTo: selectedRoute.id === 'clinician' ? 'clinician' : 'ai'
    };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setJustSentMessage(true);

    try {
      const timezoneOffsetMinutes = new Date().getTimezoneOffset();
      const timezoneOffsetHours = -timezoneOffsetMinutes / 60;
      
      // Include pending compound action metadata if it exists
      const contextData: any = {
        userRole: 'patient',
        conversationType: 'patient',
        message_route: selectedRoute.id === 'clinician' ? 'clinician' : 'ai',
        timezone_offset: timezoneOffsetHours
      };
      
      // Check if the last message from AI has parameter collection metadata
      const lastAIMessage = messages
        .filter(msg => msg.sender === "agent" && msg.metadata)
        .slice(-1)[0];
      
      if (lastAIMessage?.metadata) {
        // Extract parameter collection state from last AI message
        if (lastAIMessage.metadata.previous_parameters) {
          contextData.previous_parameters = lastAIMessage.metadata.previous_parameters;
          console.log('Including previous_parameters in context:', lastAIMessage.metadata.previous_parameters);
        }
        if (lastAIMessage.metadata.current_intent_action_type) {
          contextData.current_intent_action_type = lastAIMessage.metadata.current_intent_action_type;
          console.log('Including current_intent_action_type in context:', lastAIMessage.metadata.current_intent_action_type);
        }
      }
      
      // Add pending compound action if exists
      if (pendingCompoundAction) {
        contextData.pending_compound_action = pendingCompoundAction;
        console.log('Including pending compound action in context:', pendingCompoundAction);
      }
      
      console.log('Full context being sent:', contextData);
      
      const payload = { 
        message: userMessage.message,
        context: contextData
      };
      
      console.log('Full payload being sent:', payload);
      
      const response = await apiClient.post<{
        message: string;
        message_id: string;
        sender_type: string;
        timestamp: string;
        metadata?: any;
      }>('/chat/messages', payload);
      
      
      // Debug logging for response
      console.log('Full API response:', response.data);
      console.log('Response metadata:', response.data.metadata);
      
      // Check for pending compound action in the response metadata
      if (response.data.metadata?.pending_compound_action) {
        console.log('Received pending compound action:', response.data.metadata.pending_compound_action);
        setPendingCompoundAction(response.data.metadata.pending_compound_action);
      } else {
        // Clear pending compound action if it was used successfully
        if (pendingCompoundAction) {
          console.log('Clearing pending compound action after use');
          setPendingCompoundAction(null);
        }
      }
      
      // Update the message status
      setMessages((prev) => {
        // First, find the latest patient message and mark it as delivered
        const updatedMessages = prev.map(msg => {
          if (msg.sender === "patient" && msg.status === "sent") {
            return { ...msg, status: "delivered" as const };
          }
          return msg;
        });
        
        // Then, add the agent's response
        const newMessage: ChatMessage = { 
          id: response.data.message_id,
          sender: (response.data.sender_type || 'agent').toLowerCase() as "agent",
          message: response.data.message || "",
          timestamp: response.data.timestamp || new Date().toISOString(),
          routedTo: "patient",
          metadata: response.data.metadata 
        };
        
        return [...updatedMessages, newMessage];
      });
      
      // Simulate updating to 'read' status after agent responds
      setTimeout(() => {
        setJustSentMessage(false);
        setMessages(prev => prev.map(msg => {
          if (msg.sender === "patient" && msg.status === "delivered") {
            return { ...msg, status: "read" as const };
          }
          return msg;
        }));
      }, 2000);
    } catch (err: unknown) {
      console.error("Error sending message:", err);
      let errorMessage = 'An error occurred while sending your message.';
      if (err && typeof err === 'object') {
        if ('response' in err && 
            err.response && typeof err.response === 'object' && 
            'data' in err.response && 
            err.response.data && typeof err.response.data === 'object' && 
            'detail' in err.response.data) {
          errorMessage = String(err.response.data.detail);
        } else if ('message' in err && typeof err.message === 'string') {
          errorMessage = err.message;
        }
      }
      setError(errorMessage);
    } finally {
      setIsLoadingResponse(false);
      setJustSentMessage(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  const formatSingleParameters = (parameters: Record<string, any>) => {
    const lines: string[] = ["I've provided the following information:"];
    
    Object.entries(parameters).forEach(([key, value]) => {
      if (key === 'patient_id') return; // Skip patient_id in display
      
      let displayValue = value;
      let displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      // Format dates
      if (key.includes('date') && value) {
        try {
          const date = new Date(value);
          displayValue = date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
          });
        } catch {
          displayValue = value;
        }
      }
      
      // Format times (be more specific to avoid affecting other fields)
      if ((key === 'preferred_time' || key === 'appointment_time') && value && typeof value === 'string') {
        try {
          const [hours, minutes] = value.split(':');
          const hour = parseInt(hours);
          const ampm = hour >= 12 ? 'PM' : 'AM';
          const displayHour = hour % 12 || 12;
          displayValue = `${displayHour}:${minutes} ${ampm}`;
        } catch {
          displayValue = value;
        }
      }
      
      lines.push(`• ${displayKey}: ${displayValue}`);
    });
    
    return lines.join('\n');
  };

  const handleParameterSubmit = async (parameters: Record<string, any>, _originalMetadata: any) => {
    // Create a user-friendly message
    const parametersMessage = formatSingleParameters(parameters);
    
    // Save the current input message and clear it
    const currentInput = input;
    setInput(parametersMessage);
    
    // Send the message with completed parameters context
    await sendMessage();
    
    // Restore the original input if it wasn't sent
    if (input === parametersMessage) {
      setInput(currentInput);
    }
  };

  const formatCompoundParameters = (allParameters: Record<string, Record<string, any>>) => {
    const lines: string[] = ["I've provided the following information:"];
    
    Object.entries(allParameters).forEach(([actionKey, params]) => {
      const parts = actionKey.split('_');
      const actionIndex = parts[parts.length - 1]; // Get last part as index
      const actionType = parts.slice(0, -1).join('_'); // Join all parts except last as action type
      const actionName = actionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      lines.push("");
      lines.push(`${parseInt(actionIndex) + 1}. ${actionName}:`);
      
      Object.entries(params).forEach(([key, value]) => {
        if (key === 'patient_id') return; // Skip patient_id in display
        
        let displayValue = value;
        let displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        // Format dates
        if (key.includes('date') && value) {
          try {
            const date = new Date(value);
            displayValue = date.toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric', 
              year: 'numeric' 
            });
          } catch {
            displayValue = value;
          }
        }
        
        // Format times (be more specific to avoid affecting other fields)
        if ((key === 'preferred_time' || key === 'appointment_time') && value && typeof value === 'string') {
          try {
            const [hours, minutes] = value.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour % 12 || 12;
            displayValue = `${displayHour}:${minutes} ${ampm}`;
          } catch {
            displayValue = value;
          }
        }
        
        lines.push(`  • ${displayKey}: ${displayValue}`);
      });
    });
    
    return lines.join('\n');
  };

  const handleCompoundParameterSubmit = async (allParameters: Record<string, Record<string, any>>, _originalMetadata: any) => {
    // Create a user-friendly message
    const parametersMessage = formatCompoundParameters(allParameters);
    
    // Save the current input message and clear it
    const currentInput = input;
    setInput(parametersMessage);
    
    // Send the message with completed parameters
    await sendMessage();
    
    // Restore the original input if it wasn't sent
    if (input === parametersMessage) {
      setInput(currentInput);
    }
  };

  return (
    <MainLayout>
      <div className="-mx-6 -mt-6 flex flex-col overflow-hidden border border-gray-200 border-b-0 h-[calc(100vh-8.25rem)]">
        <div className="flex-grow flex overflow-hidden">
          <div className="flex flex-col flex-grow w-full overflow-hidden relative">
            {/* Fixed Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <ConversationHeader messagesExist={messages.length > 0} />
            </div>
            
            {/* Patient Disclaimer Banner */}
            <Alert variant="default" className="mx-4 mt-4 bg-blue-50 border-blue-300 text-blue-800">
              <Bot className="h-4 w-4" />
              <AlertTitle>Informational Use Only</AlertTitle>
              <AlertDescription>
                This chat is for informational purposes only and cannot provide medical advice. Consult your healthcare provider for any medical concerns.
              </AlertDescription>
            </Alert>
            
            {/* Chat Messages Area (Scrollable) */}
            <div 
              className="flex-grow overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900 space-y-4 max-h-full relative" 
              ref={messagesContainerRef}
            >
              {isLoadingHistory ? (
                <p className="text-center text-gray-500">Loading chat history...</p>
              ) : messages.length === 0 && !error ? (
                <p className="text-center text-gray-500">
                  Start the conversation by typing a message below.
                </p>
              ) : (
                messages.map((msg, index) => {
                  const prevMsg = index > 0 ? messages[index - 1] : undefined;
                  const isGrouped = shouldGroupMessages(msg, prevMsg);
                  const showDate = index === 0 || 
                    (msg.timestamp && prevMsg?.timestamp && 
                    formatMessageDate(msg.timestamp) !== formatMessageDate(prevMsg.timestamp));
                  const isPatientMessage = msg.sender === 'patient' || msg.sender === 'user';
                  
                  // DEBUG: Log message to console
                  console.log(`Message ${index}: sender="${msg.sender}", message="${msg.message.substring(0, 30)}...", routedTo="${msg.routedTo}"`);
                  
                  return (
                    <React.Fragment key={`${msg.id || index}-${msg.sender}`}>
                      {showDate && msg.timestamp && (
                        <div className="text-center my-4 relative">
                          <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                          </div>
                          <div className="relative flex justify-center">
                            <span className="text-xs bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full">
                              {formatMessageDate(msg.timestamp)}
                            </span>
                          </div>
                        </div>
                      )}
                      <div
                        className={cn(
                          "flex flex-col",
                          isPatientMessage ? "items-end" : "items-start",
                          isGrouped ? "mt-1" : "mt-4"
                        )}
                      >
                        <div
                          className={cn(
                            "p-3 rounded-lg max-w-[75%] transition-all",
                            isPatientMessage
                              ? msg.routedTo === 'clinician'
                                ? "bg-gradient-to-r from-green-600 to-green-700 text-white shadow-md"
                                : "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md"
                              : msg.sender === "clinician"
                                ? "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100 border border-green-200 dark:border-green-700"
                                : "bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100",
                            isGrouped && isPatientMessage ? "rounded-tr-sm" : "",
                            isGrouped && !isPatientMessage ? "rounded-tl-sm" : "",
                            // Add distinctive styling for routing
                            isPatientMessage && msg.routedTo === 'clinician' && 
                              "border-l-4 border-green-300 dark:border-green-400",
                            isPatientMessage && (msg.routedTo === 'ai' || msg.routedTo === 'patient') && 
                              "border-l-4 border-blue-300 dark:border-blue-400"
                          )}
                        >
                          {/* Show routing info for patient messages */}
                          {isPatientMessage && (
                            <div className="flex items-center gap-1 mb-1">
                              <MessageSquare className="h-4 w-4 text-white/80" />
                              <p className="text-xs font-medium opacity-80">
                                You → {msg.routedTo === 'clinician' ? 'Clinician' : 'AI'}
                              </p>
                            </div>
                          )}
                          {/* Show sender label for AI messages */}
                          {msg.sender === "agent" && (
                            <div className="flex items-center gap-1 mb-1">
                              <Bot className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                              <p className="text-xs font-medium opacity-80">
                                AI Assistant → You
                              </p>
                            </div>
                          )}
                          {/* Show sender label for clinician messages */}
                          {msg.sender === "clinician" && (
                            <div className="flex items-center gap-1 mb-1">
                              <User className="h-4 w-4 text-green-600 dark:text-green-400" />
                              <p className="text-xs font-medium opacity-80">Your Clinician → You</p>
                            </div>
                          )}
                          <p className="text-sm">{msg.message}</p>
                        </div>
                        {msg.timestamp && !shouldGroupMessages(messages[index + 1] || {}, msg) && (
                          <div className="flex items-center gap-2 mt-1 px-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatChatTimestamp(msg.timestamp)}
                            </span>
                            {isPatientMessage && msg.status && (
                              <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                {msg.status === "sent" && (
                                  <Badge variant="outline" className="px-1.5 py-0 h-4 text-xs text-gray-500 dark:text-gray-400 border-gray-300 dark:border-gray-600">
                                    Sent
                                  </Badge>
                                )}
                                {msg.status === "delivered" && (
                                  <Badge variant="outline" className="px-1.5 py-0 h-4 text-xs text-gray-500 dark:text-gray-400 border-gray-300 dark:border-gray-600">
                                    Delivered
                                  </Badge>
                                )}
                                {msg.status === "read" && (
                                  <Badge variant="outline" className="px-1.5 py-0 h-4 text-xs text-blue-500 dark:text-blue-400 border-blue-300 dark:border-blue-600">
                                    Read
                                  </Badge>
                                )}
                              </span>
                            )}
                          </div>
                        )}
                        {msg.metadata && typeof msg.metadata === 'object' && 
                         (('action_type' in msg.metadata && ('success' in msg.metadata || 'module' in msg.metadata)) ||
                          'missing_parameters' in msg.metadata ||
                          'compound_missing_parameters' in msg.metadata) && (
                          <div className="w-full max-w-[75%] mt-1">
                            <ActionResponseHandler 
                              metadata={msg.metadata}
                              onParameterSubmit={(parameters) => {
                                handleParameterSubmit(parameters, msg.metadata);
                              }}
                              onCompoundParameterSubmit={(allParameters) => {
                                handleCompoundParameterSubmit(allParameters, msg.metadata);
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </React.Fragment>
                  );
                })
              )}
              {/* Error display within chat area */}
              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    {error}
                  </AlertDescription>
                </Alert>
              )}
              <div ref={chatEndRef} />
            </div>
            
            {/* Scroll to bottom button - positioned fixed at bottom of messages area */}
            {showScrollButton && (
              <button
                onClick={() => scrollToBottom("instant")}
                className="absolute bottom-20 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center z-10"
                aria-label="Scroll to bottom"
              >
                <ChevronDown size={20} />
                {unreadMessagesCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
                    {unreadMessagesCount}
                  </span>
                )}
              </button>
            )}
            
            {/* Fixed Input Area (always at bottom) */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              {/* Input area with routing dropdown on left */}
              <div className="flex items-center space-x-2">
                <MessageRoutingDropdown
                  selectedRoute={selectedRoute}
                  options={messageRoutingOptions}
                  onRouteChange={handleRouteChange}
                  disabled={isLoadingResponse}
                />
                <Textarea
                  placeholder={selectedRoute.id === 'clinician' 
                    ? "Type your message to your clinician..." 
                    : "Type your message here..."
                  }
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  rows={1}
                  className="flex-grow resize-none"
                  disabled={isLoadingResponse}
                />
                <Button type="submit" onClick={() => sendMessage()} disabled={!input.trim() || isLoadingResponse}>
                  {isLoadingResponse ? "Sending..." : <Send className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default ChatPage;