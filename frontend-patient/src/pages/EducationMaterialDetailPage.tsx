import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DocumentViewer from '@/components/education/DocumentViewer';
import apiClient from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { <PERSON>ertCircle, CheckCircle, Eye } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface EducationMaterial {
  id: string;
  title: string;
  description?: string;
  type: 'PDF' | 'VIDEO' | 'LINK' | 'DOCUMENT';
  category?: string;
  content_url?: string;
  file_path?: string;
  duration_minutes?: number;
  is_public: boolean;
  clinic_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  views_count?: number;
  assignments_count?: number;
}

interface Assignment {
  id: string;
  patient_id: string;
  material_id: string;
  assigned_by: string;
  assigned_at: string;
  due_date: string | null;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'assigned' | 'viewed' | 'in_progress' | 'completed' | 'overdue';
  clinician_notes: string | null;
}

const EducationMaterialDetailPage: React.FC = () => {
  const { materialId } = useParams<{ materialId: string }>();
  const navigate = useNavigate();
  const [material, setMaterial] = useState<EducationMaterial | null>(null);
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);
  const [statusUpdateError, setStatusUpdateError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMaterialDetails = async () => {
      if (!materialId) {
        setError("Material ID is missing.");
        setIsLoading(false);
        return;
      }
      try {
        const response = await apiClient.get(`/education-materials/${materialId}`);
        setMaterial(response.data);
        
        // Fetch the assignment for this material
        try {
          const assignmentsResponse = await apiClient.get('/patient-education/assignments', {
            params: { material_id: materialId }
          });
          
          if (assignmentsResponse.data && assignmentsResponse.data.length > 0) {
            setAssignment(assignmentsResponse.data[0]); // Get the first assignment for this material
            
            // If status is 'assigned', automatically mark as 'viewed'
            if (assignmentsResponse.data[0].status === 'assigned') {
              updateAssignmentStatus('viewed');
            }
          }
        } catch (assignmentErr) {
          console.error("Failed to fetch assignment:", assignmentErr);
          // Don't set error here, as the material might not be assigned
        }
      } catch (err) {
        console.error("Failed to fetch material details:", err);
        setError("Failed to load material details. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMaterialDetails();
  }, [materialId]);

  const updateAssignmentStatus = async (newStatus: 'viewed' | 'completed') => {
    if (!assignment) return;
    
    setStatusUpdateLoading(true);
    setStatusUpdateSuccess(false);
    setStatusUpdateError(null);
    
    try {
      const response = await apiClient.put(`/patient-education/assignments/${assignment.id}`, {
        status: newStatus
      });
      
      setAssignment(response.data);
      setStatusUpdateSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setStatusUpdateSuccess(false);
      }, 3000);
    } catch (err) {
      console.error(`Failed to update assignment status to ${newStatus}:`, err);
      setStatusUpdateError(`Failed to update status. Please try again.`);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  const renderStatusControls = () => {
    if (!assignment) return null;
    
    return (
      <div className="bg-gray-50 p-4 rounded-lg mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium">Assignment Status</h3>
          <div className="flex items-center">
            <span className={`px-2 py-1 text-xs rounded-full ${
              assignment.status === 'completed' ? 'bg-green-100 text-green-800' :
              assignment.status === 'viewed' ? 'bg-blue-100 text-blue-800' :
              assignment.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
              assignment.status === 'overdue' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
            </span>
          </div>
        </div>
        
        <div className="flex gap-2 mt-4">
          {assignment.status !== 'viewed' && assignment.status !== 'completed' && (
            <Button 
              onClick={() => updateAssignmentStatus('viewed')}
              disabled={statusUpdateLoading}
              size="sm"
              className="flex items-center gap-1"
            >
              <Eye className="h-4 w-4" />
              Mark as Viewed
            </Button>
          )}
          
          {assignment.status !== 'completed' && (
            <Button 
              onClick={() => updateAssignmentStatus('completed')}
              disabled={statusUpdateLoading}
              size="sm"
              className="flex items-center gap-1"
            >
              <CheckCircle className="h-4 w-4" />
              Mark as Completed
            </Button>
          )}
        </div>
        
        {statusUpdateSuccess && (
          <Alert className="mt-2 bg-green-50 text-green-800 border-green-200">
            <AlertDescription>
              Status updated successfully!
            </AlertDescription>
          </Alert>
        )}
        
        {statusUpdateError && (
          <Alert className="mt-2 bg-red-50 text-red-800 border-red-200">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {statusUpdateError}
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading material details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (!material) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Material not found.</p>
      </div>
    );
  }

  // Render different viewers based on material type
  switch (material.type) {
    case 'PDF':
    case 'DOCUMENT':
      return (
        <div className="flex flex-col h-screen">
          <div className="p-4 bg-white border-b">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate(-1)}
              className="mb-2"
            >
              Back
            </Button>
            <h1 className="text-xl font-bold">{material.title}</h1>
            {assignment && renderStatusControls()}
          </div>
          <DocumentViewer materialId={material.id} title={material.title} className="flex-1" />
        </div>
      );
    case 'VIDEO':
      return (
        <div className="flex flex-col items-center p-4">
          <div className="w-full max-w-3xl">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate(-1)}
              className="mb-4"
            >
              Back
            </Button>
            <h1 className="text-2xl font-bold mb-4">{material.title}</h1>
            {assignment && renderStatusControls()}
            {material.content_url ? (
              <video 
                controls 
                src={material.content_url} 
                className="w-full h-auto rounded-lg shadow-lg"
                onPlay={() => {
                  if (assignment && assignment.status === 'assigned') {
                    updateAssignmentStatus('viewed');
                  }
                }}
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <p className="text-red-500">Video URL not available.</p>
            )}
            {material.description && <p className="mt-4">{material.description}</p>}
          </div>
        </div>
      );
    case 'LINK':
      return (
        <div className="flex flex-col items-center p-4">
          <div className="w-full max-w-3xl">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate(-1)}
              className="mb-4"
            >
              Back
            </Button>
            <h1 className="text-2xl font-bold mb-4">{material.title}</h1>
            {assignment && renderStatusControls()}
            {material.content_url ? (
              <a 
                href={material.content_url} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-blue-600 hover:underline text-lg"
                onClick={() => {
                  if (assignment && assignment.status === 'assigned') {
                    updateAssignmentStatus('viewed');
                  }
                }}
              >
                Go to Material Link
              </a>
            ) : (
              <p className="text-red-500">Link URL not available.</p>
            )}
            {material.description && <p className="mt-4">{material.description}</p>}
          </div>
        </div>
      );
    default:
      return (
        <div className="flex items-center justify-center min-h-screen">
          <p className="text-red-500">Unsupported material type: {material.type}</p>
        </div>
      );
  }
};

export default EducationMaterialDetailPage;