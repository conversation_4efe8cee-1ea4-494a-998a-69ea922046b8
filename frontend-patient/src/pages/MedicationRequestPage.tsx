import React, { useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton'; 
import { getWithAuth } from '@/lib/utils';
import { toast } from "sonner"; 
import { Button } from "@/components/ui/button";
import {
  PatientMedicationRequestForm,
  PatientMedicationRequestFormData
} from '@/components/medication-requests/PatientMedicationRequestForm';
import { postWithAuth } from '@/lib/utils'; 
import { AxiosError } from 'axios';
import { PlusCircle } from 'lucide-react';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

interface MedicationRequest {
  id: string;
  patient_id: string; 
  medication_name: string;
  dosage?: string | null;
  frequency?: string | null;
  duration?: string | null;
  notes?: string | null;
  status: string; 
  created_at: string; 
  resolved_at?: string | null; 
}

interface ApiResponse {
  items: MedicationRequest[];
  total: number;
  skip: number; 
  limit: number;
}

interface MedicationSelectItem {
  id: string;
  name: string;
}

const MedicationRequestPage: React.FC = () => {
  const [items, setItems] = useState<MedicationRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableMedications, setAvailableMedications] = useState<MedicationSelectItem[]>([]);
  const [medicationsLoading, setMedicationsLoading] = useState(true);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    const limit = 100; 

    try {
      const response = await getWithAuth(
        `${API_BASE_URL}/patients/me/medication-requests?limit=${limit}&sortBy=created_at&sortDesc=true`
      ) as ApiResponse;

      const fetchedItems = response.items || [];

      const sortedItems = fetchedItems.sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setItems(sortedItems);

    } catch (err) {
      console.error('Failed to fetch medication requests:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load medication requests.';
      setError(errorMessage);
      setItems([]); 
      toast.error("Could not load medication request history.");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchAvailableMedications = useCallback(async () => {
    setMedicationsLoading(true);
    try {
      const response = await getWithAuth(`${API_BASE_URL}/me/medications`);
      setAvailableMedications(response || []);
    } catch (err) {
      console.error('Failed to fetch available medications:', err);
      toast.error("Could not load medications for the request form.");
      setAvailableMedications([]); 
    } finally {
      setMedicationsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
    fetchAvailableMedications();
  }, [fetchData, fetchAvailableMedications]);

  const formatDateForDisplay = (dateString: string | null | undefined): string => {
    if (!dateString) {
      return 'N/A';
    }
    try {
      return format(new Date(dateString), 'PPpp'); 
    } catch (e) {
      console.error("Error formatting date:", dateString, e);
      return 'Invalid Date';
    }
  };

  const getStatusBadgeVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'default'; 
      case 'denied':
      case 'rejected': 
        return 'destructive'; 
      case 'pending':
      default:
        return 'secondary'; 
    }
  };

  const handleRequestSubmit = async (data: PatientMedicationRequestFormData) => {
    setIsSubmitting(true);
    try {
      await postWithAuth(`${API_BASE_URL}/patients/me/medication-requests`, data);
      toast.success("Medication request submitted successfully!");
      setIsModalOpen(false);
      fetchData(); 
    } catch (err: unknown) { 
      console.error('Failed to submit medication request:', err);
      let errorMessage = 'Failed to submit request.';
      if (err instanceof AxiosError) {
        errorMessage = err.response?.data?.detail || err.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      toast.error(`Submission failed: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Medication Requests</h1>
        <Button onClick={() => setIsModalOpen(true)} disabled={medicationsLoading}>
          <PlusCircle className="mr-2 h-4 w-4" /> {medicationsLoading ? 'Loading...' : 'New Request'}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Request History</CardTitle>
          <CardDescription>
            View the status and details of your past medication refill requests.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto"> 
            {loading ? (
              <div className="space-y-3 p-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-3/4" />
              </div>
            ) : error ? (
              <p className="text-red-600 font-medium p-4">Error: {error}</p>
            ) : items.length === 0 ? (
              <p className="text-muted-foreground p-4">No medication requests found.</p>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {items.map((request) => (
                  <Card key={request.id}>
                    <CardHeader>
                      <CardTitle>{request.medication_name}</CardTitle>
                      <CardDescription>
                        Requested: {formatDateForDisplay(request.created_at)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Status</p>
                        <Badge variant={getStatusBadgeVariant(request.status)} className="mt-1">
                          {request.status}
                        </Badge>
                      </div>
                      {request.dosage && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Dosage</p>
                          <p>{request.dosage}</p>
                        </div>
                      )}
                      {request.frequency && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Frequency</p>
                          <p>{request.frequency}</p>
                        </div>
                      )}
                      {request.duration && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Duration</p>
                          <p>{request.duration}</p>
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Clinician Notes</p>
                        <p className="text-sm text-muted-foreground italic">
                          {request.notes || '-'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Resolved On</p>
                        <p>{formatDateForDisplay(request.resolved_at)}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <PatientMedicationRequestForm
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleRequestSubmit}
        isSubmitting={isSubmitting}
        availableMedications={availableMedications}
      />
    </div>
  );
};

export default MedicationRequestPage;
