import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { MessageSquare, BookOpen } from 'lucide-react';
import { useUser } from '@clerk/clerk-react';
import apiClient from '../lib/apiClient';

interface EducationMaterialSummary {
  id: string;
  title: string;
  type: string;
  file_url: string;
}

interface AssignedMaterial {
  id: string;
  patient_id: string;
  material_id: string;
  assigned_by: string;
  assigned_at: string;
  due_date: string | null;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'assigned' | 'viewed' | 'in_progress' | 'completed' | 'overdue';
  clinician_notes: string | null;
  material: EducationMaterialSummary;
}

import PatientWeightTrackingCard from "@/components/dashboard/cards/PatientWeightTrackingCard";
import PatientSideEffectsCard from "@/components/dashboard/cards/PatientSideEffectsCard";
import PatientMedicationRequestsCard from "@/components/dashboard/cards/PatientMedicationRequestsCard";
import PatientAppointmentsCard from '@/components/dashboard/cards/PatientAppointmentsCard';
import { PatientActivityFeed } from '@/components/dashboard/PatientActivityFeed';

const PatientDashboardPage: React.FC = () => {
  const { isLoaded, user } = useUser();
  const [assignedMaterials, setAssignedMaterials] = useState<AssignedMaterial[]>([]);
  const [loadingMaterials, setLoadingMaterials] = useState(true);
  const [errorMaterials, setErrorMaterials] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssignedMaterials = async () => {
      if (!isLoaded || !user) {
        return;
      }
      setLoadingMaterials(true);
      setErrorMaterials(null);
      try {
        const response = await apiClient.get<AssignedMaterial[]>('/patient-education/assignments');
        setAssignedMaterials(response.data);
      } catch (error) {
        console.error('Failed to fetch assigned materials:', error);
        setErrorMaterials('Failed to load assigned materials.');
      } finally {
        setLoadingMaterials(false);
      }
    };

    fetchAssignedMaterials();
  }, [isLoaded, user]);

  if (!isLoaded) {
    return null; // Or loading state
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <h1 className="text-2xl font-semibold tracking-tight">
          Welcome back{user?.firstName ? `, ${user.firstName}` : ''}
        </h1>
        <p className="text-muted-foreground">
          Manage your health journey and stay connected with your care team.
        </p>
      </div>

      {/* Featured Chat Section - Primary Call to Action */}
      <Link to="/chat" className="block">
        <Card className="bg-gradient-to-br from-blue-50 via-teal-50 to-cyan-50 border-teal-200 hover:from-blue-100 hover:via-teal-100 hover:to-cyan-100 transition-all duration-200 shadow-md hover:shadow-xl overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-200/20 to-blue-200/20 rounded-full -mr-16 -mt-16" />
          <CardHeader className="pb-3 relative">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-3 bg-gradient-to-br from-teal-500 to-blue-600 rounded-full shadow-md">
                  <MessageSquare className="h-6 w-6 text-white" />
                </div>
                <span className="bg-gradient-to-r from-teal-700 to-blue-700 bg-clip-text text-transparent font-semibold">
                  Start a Conversation
                </span>
              </CardTitle>
              <div className="flex items-center gap-2">
                <span className="animate-pulse h-3 w-3 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full shadow-sm"></span>
                <span className="text-sm text-teal-700 font-medium">Available now</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0 relative">
            <p className="text-base mb-3 text-gray-700">Get instant help from your care team through our AI-powered chat system.</p>
            <div className="flex flex-wrap gap-2 text-sm">
              <span className="bg-white/80 backdrop-blur px-3 py-1.5 rounded-full text-teal-700 border border-teal-200">📋 Schedule appointments</span>
              <span className="bg-white/80 backdrop-blur px-3 py-1.5 rounded-full text-blue-700 border border-blue-200">💊 Request medications</span>
              <span className="bg-white/80 backdrop-blur px-3 py-1.5 rounded-full text-cyan-700 border border-cyan-200">📝 Report side effects</span>
              <span className="bg-white/80 backdrop-blur px-3 py-1.5 rounded-full text-teal-700 border border-teal-200">❓ Ask health questions</span>
            </div>
          </CardContent>
        </Card>
      </Link>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <PatientWeightTrackingCard />
        <PatientSideEffectsCard />
        <PatientMedicationRequestsCard />
        <PatientAppointmentsCard />

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks you might want to perform</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col gap-2">
            {/* Updated Quick Actions */}
            <Button variant="outline" asChild>
              <Link to="/side-effects">Report New Side Effect</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/weight-tracking">Record New Weight</Link>
            </Button>
            {/* Moved comment outside to fix React.Children.only error */}
            <Button variant="outline" asChild>
              <Link to="/appointments">Request Appointment</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/medication-requests">Request Medication</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest health updates</CardDescription>
          </CardHeader>
          <CardContent>
            <PatientActivityFeed />
          </CardContent>
        </Card>


        <Link to="/education">
          <Card className="hover:bg-muted/50 transition-colors h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                Education
              </CardTitle>
              <CardDescription>
                Learn about your health
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Access educational resources and materials.</p>
            </CardContent>
          </Card>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Assigned Education Materials</CardTitle>
          <CardDescription>Materials assigned to you by your care team.</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingMaterials && <p>Loading assigned materials...</p>}
          {errorMaterials && <p className="text-red-500">{errorMaterials}</p>}
          {!loadingMaterials && assignedMaterials.length === 0 && (
            <p className="text-sm text-muted-foreground">No education materials assigned yet.</p>
          )}
          {!loadingMaterials && assignedMaterials.length > 0 && (
            <div className="space-y-4">
              {assignedMaterials.map((material) => (
                <div key={material.id} className="flex items-center justify-between p-3 border rounded-md">
                  <div>
                    <h3 className="font-semibold">{material.material.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      Priority: {material.priority} | Status: {material.status}
                      {material.due_date && ` | Due: ${new Date(material.due_date).toLocaleDateString()}`}
                    </p>
                    {material.clinician_notes && (
                      <p className="text-xs text-muted-foreground mt-1">Notes: {material.clinician_notes}</p>
                    )}
                  </div>
                  <Button asChild size="sm">
                    <Link to={`/education-material/${material.material.id}`}>View Material</Link>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PatientDashboardPage;
