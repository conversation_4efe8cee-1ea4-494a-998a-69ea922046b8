import React, { useState, useEffect, useCallback } from 'react';
import { AlertCircle, BookOpen, Clock, FileText, Link, PlayCircle, Search, CheckCircle, Play, Calendar } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
// Removed tabs import - will use simple state-based tabs
import { Skeleton } from '@/components/ui/skeleton';
import { getWithAuth, postWithAuth, putWithAuth } from '@/lib/utils';
import DocumentViewerModal from '@/components/education/DocumentViewerModal';

// Types for education system
interface EducationMaterial {
  id: string;
  title: string;
  description: string | null;
  type: 'PDF' | 'VIDEO' | 'LINK' | 'DOCUMENT';
  content_url: string | null;
  file_path: string | null;
  duration_minutes: number | null;
  category: string | null;
  tags: string[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

interface PatientEducationAssignment {
  id: string;
  patient_id: string;
  material_id: string;
  assigned_by: string;
  assigned_at: string;
  due_date: string | null;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'assigned' | 'viewed' | 'in_progress' | 'completed' | 'overdue';
  clinician_notes: string | null;
  created_at: string;
  updated_at: string;
}

interface EducationProgress {
  id: string;
  patient_id: string;
  material_id: string;
  assignment_id: string;
  progress_percentage: number;
  time_spent_minutes: number;
  last_accessed: string | null;
  completed_at: string | null;
  patient_feedback: string | null;
  patient_rating: number | null;
}

interface DashboardData {
  patient_id: string;
  total_assignments: number;
  completed_assignments: number;
  in_progress_assignments: number;
  overdue_assignments: number;
  completion_rate: number;
  high_priority_count: number;
  urgent_priority_count: number;
  recent_assignments: Array<{
    id: string;
    material_title: string;
    material_type: string;
    status: string;
    priority: string;
    assigned_at: string;
    due_date: string | null;
  }>;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

const PatientEducationPage: React.FC = () => {
  const [assignments, setAssignments] = useState<PatientEducationAssignment[]>([]);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [publicMaterials, setPublicMaterials] = useState<EducationMaterial[]>([]);
  const [progress, setProgress] = useState<Record<string, EducationProgress>>({});
  const [assignmentMaterials, setAssignmentMaterials] = useState<Record<string, EducationMaterial>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('assignments');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Document viewer modal state
  const [viewerModal, setViewerModal] = useState<{
    isOpen: boolean;
    materialId: string;
    title: string;
  }>({
    isOpen: false,
    materialId: '',
    title: ''
  });

  // Removed fetchDashboard - using fetchAssignments instead

  // Combined fetch for assignments with loading state
  const fetchAssignments = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getWithAuth(`${API_BASE_URL}/patient-education/assignments`);
      setAssignments(response);
      
      // Fetch material details for each assignment
      const materialPromises = response.map((assignment: PatientEducationAssignment) =>
        getWithAuth(`${API_BASE_URL}/education-materials/${assignment.material_id}`)
          .then((material: EducationMaterial) => ({ [assignment.material_id]: material }))
          .catch(() => ({ [assignment.material_id]: null })) // Handle missing materials gracefully
      );
      
      const materialResults = await Promise.all(materialPromises);
      const materialsMap = materialResults.reduce((acc, result) => ({ ...acc, ...result }), {});
      setAssignmentMaterials(materialsMap);
      
    } catch (err: any) {
      let errorMessage = 'Failed to load assignments.';
      if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch public materials
  const fetchPublicMaterials = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        is_public: 'true',
        limit: '20',
        ...(searchQuery && { search: searchQuery })
      });
      const response = await getWithAuth(`${API_BASE_URL}/education-materials/?${params}`);
      setPublicMaterials(response);
    } catch (err: any) {
      console.error('Failed to fetch public materials:', err);
    }
  }, [searchQuery]);

  // Fetch progress for an assignment
  const fetchProgress = useCallback(async (assignmentId: string) => {
    try {
      const response = await getWithAuth(`${API_BASE_URL}/patient-education/progress/${assignmentId}`);
      setProgress(prev => ({ ...prev, [assignmentId]: response }));
    } catch (err: any) {
      // Progress might not exist yet, that's okay - only log if it's not a 404
      if (err.response?.status !== 404) {
        console.error(`Error fetching progress for assignment ${assignmentId}:`, err);
      }
      // For 404s, we just don't have progress yet, which is normal
    }
  }, []);

  // Update assignment status
  const updateAssignmentStatus = useCallback(async (assignmentId: string, status: string) => {
    try {
      await putWithAuth(`${API_BASE_URL}/patient-education/assignments/${assignmentId}`, {
        status
      });
      
      // Refresh assignments
      await fetchAssignments();
    } catch (err: any) {
      console.error('Failed to update assignment status:', err);
      // Show error to user
      setError('Failed to update assignment status. Please try again.');
    }
  }, [fetchAssignments]);

  // Create or update progress
  const updateProgress = useCallback(async (assignmentId: string, progressPercentage: number, timeSpent: number = 0) => {
    try {
      const assignment = assignments.find(a => a.id === assignmentId);
      if (!assignment) return;

      await postWithAuth(`${API_BASE_URL}/patient-education/progress`, {
        assignment_id: assignmentId,
        material_id: assignment.material_id,
        progress_percentage: progressPercentage,
        time_spent_minutes: timeSpent
      });

      // Refresh progress
      await fetchProgress(assignmentId);
      
      // Update assignment status if completed
      if (progressPercentage >= 100) {
        await updateAssignmentStatus(assignmentId, 'completed');
      } else if (progressPercentage > 0) {
        await updateAssignmentStatus(assignmentId, 'in_progress');
      }
    } catch (err: any) {
      console.error('Failed to update progress:', err);
    }
  }, [assignments, fetchProgress, updateAssignmentStatus]);

  // Mark material as viewed
  const markAsViewed = useCallback(async (assignmentId: string) => {
    const assignment = assignments.find(a => a.id === assignmentId);
    if (assignment && assignment.status === 'assigned') {
      await updateAssignmentStatus(assignmentId, 'viewed');
      await updateProgress(assignmentId, 10); // 10% for viewing
    }
  }, [assignments, updateAssignmentStatus, updateProgress]);

  // Open document viewer
  const openDocumentViewer = useCallback((materialId: string, title: string, assignmentId?: string) => {
    setViewerModal({
      isOpen: true,
      materialId,
      title
    });
    
    // Mark as viewed if it's an assignment
    if (assignmentId) {
      markAsViewed(assignmentId);
    }
  }, [markAsViewed]);

  useEffect(() => {
    fetchAssignments();
    fetchPublicMaterials();
  }, [fetchAssignments, fetchPublicMaterials]);

  useEffect(() => {
    // Fetch progress for all assignments
    assignments.forEach(assignment => {
      fetchProgress(assignment.id);
    });
  }, [assignments, fetchProgress]);

  // Helper functions
  const getIconForType = (type: string) => {
    switch (type) {
      case 'PDF':
      case 'DOCUMENT':
        return FileText;
      case 'VIDEO':
        return PlayCircle;
      case 'LINK':
        return Link;
      default:
        return BookOpen;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'destructive';
      case 'high':
        return 'secondary';
      case 'medium':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'in_progress':
        return 'text-blue-600';
      case 'overdue':
        return 'text-red-600';
      case 'viewed':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDuration = (minutes: number | null) => {
    if (!minutes) return null;
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Filter materials and assignments based on search
  const filteredAssignments = assignments.filter(assignment => {
    const material = assignmentMaterials[assignment.material_id];
    if (!material) return false;
    return (
      material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.category?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const filteredPublicMaterials = publicMaterials.filter(material =>
    material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    material.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    material.category?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6 pb-20">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Educational Resources</h1>
        <p className="text-muted-foreground">
          Access your assigned materials and explore educational content to support your health journey.
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Overview */}
      {dashboardData && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.total_assignments}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{dashboardData.completed_assignments}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round(dashboardData.completion_rate * 100)}% completion rate
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{dashboardData.in_progress_assignments}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {dashboardData.high_priority_count + dashboardData.urgent_priority_count}
              </div>
              {dashboardData.overdue_assignments > 0 && (
                <p className="text-xs text-red-600">
                  {dashboardData.overdue_assignments} overdue
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search educational materials..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Content Tabs */}
      <div className="space-y-4">
        <div className="flex w-full border-b">
          <Button
            variant={activeTab === 'assignments' ? 'default' : 'ghost'}
            className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary"
            onClick={() => setActiveTab('assignments')}
          >
            My Assignments
          </Button>
          <Button
            variant={activeTab === 'explore' ? 'default' : 'ghost'}
            className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary"
            onClick={() => setActiveTab('explore')}
          >
            Explore Resources
          </Button>
        </div>

        {/* Assignments Tab */}
        {activeTab === 'assignments' && (
          <div className="space-y-4">
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-20 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredAssignments.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No assignments yet</h3>
                <p className="text-muted-foreground text-center">
                  Your healthcare provider will assign educational materials to help you on your health journey.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredAssignments.map((assignment) => {
                const material = assignmentMaterials[assignment.material_id];
                if (!material) return null; // Skip if material not loaded
                
                const IconComponent = getIconForType(material.type);
                const assignmentProgress = progress[assignment.id];
                const progressPercentage = assignmentProgress?.progress_percentage || 0;
                
                return (
                  <Card key={assignment.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-5 w-5 text-primary" />
                          <Badge variant={getPriorityColor(assignment.priority)} className="text-xs">
                            {assignment.priority}
                          </Badge>
                        </div>
                        <div className={`text-sm font-medium ${getStatusColor(assignment.status)}`}>
                          {assignment.status.replace('_', ' ')}
                        </div>
                      </div>
                      <CardTitle className="text-lg">{material.title}</CardTitle>
                      {material.description && (
                        <CardDescription className="line-clamp-2">
                          {material.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      {/* Progress Bar */}
                      {progressPercentage > 0 && (
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{Math.round(progressPercentage)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progressPercentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                      
                      {/* Material Info */}
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-4">
                          {material.duration_minutes && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatDuration(material.duration_minutes)}
                            </div>
                          )}
                          {material.category && (
                            <Badge variant="outline" className="text-xs">
                              {material.category}
                            </Badge>
                          )}
                        </div>
                        {assignment.due_date && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Due {formatDate(assignment.due_date)}
                          </div>
                        )}
                      </div>
                      
                      {/* Clinician Notes */}
                      {assignment.clinician_notes && (
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-blue-900 mb-1">Note from your provider:</p>
                          <p className="text-sm text-blue-800">{assignment.clinician_notes}</p>
                        </div>
                      )}
                    </CardContent>
                    
                    <CardFooter className="gap-2">
                      <Button
                        className="flex-1"
                        onClick={() => openDocumentViewer(material.id, material.title, assignment.id)}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        {assignment.status === 'assigned' ? 'Start' : 'Continue'}
                      </Button>
                      
                      {assignment.status !== 'completed' && (
                        <Button
                          variant="outline"
                          onClick={() => updateAssignmentStatus(assignment.id, 'completed')}
                          title="Mark as complete"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Mark Complete
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
          </div>
        )}

        {/* Explore Tab */}
        {activeTab === 'explore' && (
          <div className="space-y-4">
          {filteredPublicMaterials.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Search className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No resources found</h3>
                <p className="text-muted-foreground text-center">
                  Try adjusting your search terms or check back later for new educational content.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredPublicMaterials.map((material) => {
                const IconComponent = getIconForType(material.type);
                
                return (
                  <Card key={material.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-5 w-5 text-primary" />
                        <Badge variant="outline" className="text-xs">Public</Badge>
                      </div>
                      <CardTitle className="text-lg">{material.title}</CardTitle>
                      {material.description && (
                        <CardDescription className="line-clamp-3">
                          {material.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                    
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-4">
                          {material.duration_minutes && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatDuration(material.duration_minutes)}
                            </div>
                          )}
                          {material.category && (
                            <Badge variant="outline" className="text-xs">
                              {material.category}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                    
                    <CardFooter>
                      <Button
                        className="w-full"
                        variant="outline"
                        onClick={() => openDocumentViewer(material.id, material.title)}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        View Resource
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
          </div>
        )}
      </div>
      
      {/* Document Viewer Modal */}
      <DocumentViewerModal
        isOpen={viewerModal.isOpen}
        onClose={() => setViewerModal({ isOpen: false, materialId: '', title: '' })}
        materialId={viewerModal.materialId}
        title={viewerModal.title}
      />
    </div>
  );
};

export default PatientEducationPage;