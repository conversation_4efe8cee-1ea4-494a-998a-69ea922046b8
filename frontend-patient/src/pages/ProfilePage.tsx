import React, { useState, useEffect, useCallback } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format, parseISO } from 'date-fns';
import { User } from 'lucide-react'; 
import { useClerk } from "@clerk/clerk-react"; 
import { AxiosError } from 'axios'; 

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Toaster } from "@/components/ui/sonner"; 
import { toast } from "sonner"; 
import apiClient from '@/lib/apiClient'; 

// --- Module Level Type Guard ---
const isErrorWithDetail = (data: unknown): data is { detail: string } => {
  return typeof data === 'object' && data !== null && 'detail' in data && typeof (data as Record<string, unknown>).detail === 'string';
};

// Define the structure of patient profile data based on API response
interface PatientProfileData {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  date_of_birth?: string | null; 
  phone_number?: string | null;
  height_cm?: number | null;
  photo_url?: string | null; 
  is_active: boolean;
  goal_weight_kg?: number | null;
  goal_weight_date?: string | null;
}

// Define the Zod schema for form validation
const profileFormSchema = z.object({
  first_name: z.string().min(1, "First name is required").optional().nullable(),
  last_name: z.string().min(1, "Last name is required").optional().nullable(),
  date_of_birth: z.date().optional().nullable(),
  phone_number: z.string().min(1, "Phone number is required").optional().nullable(),
  height_cm: z.number()
    .positive("Height must be positive")
    .int("Height must be a whole number")
    .lt(300, "Height seems too high (must be less than 300 cm)") 
    .nullable(),
  goal_weight_kg: z.number()
    .positive("Goal weight must be positive")
    .max(1000, "Goal weight seems too high (must be less than 1000 kg)")
    .nullable(),
  goal_weight_date: z.date().optional().nullable(),
  // email is read-only, not part of the update payload handled by react-hook-form directly here
  // photo is handled separately
});

type ProfileFormData = z.infer<typeof profileFormSchema>;

const ProfilePage: React.FC = () => {
  const [profileData, setProfileData] = useState<PatientProfileData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true); 
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null); 
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null); 
  
  // Get signOut function from Clerk
  const { signOut } = useClerk();
  
  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut();
      toast.success("Logged out successfully");
      // Clerk will handle redirection to login page
    } catch (error: unknown) {
      console.error("Error signing out:", error);
      const errorMsg = error instanceof Error ? error.message : "Failed to log out";
      toast.error(errorMsg);
    }
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty }, 
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      date_of_birth: null,
      phone_number: '',
      height_cm: null,
      goal_weight_kg: null,
      goal_weight_date: null,
    },
  });

  // --- Fetch Profile Data ---
  const fetchProfile = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get<PatientProfileData>('/patients/me');
      console.log("Fetched patient profile:", response.data);
      setProfileData(response.data);
      // Reset form with fetched data
      reset({
        first_name: response.data.first_name,
        last_name: response.data.last_name,
        date_of_birth: response.data.date_of_birth ? parseISO(response.data.date_of_birth) : null,
        phone_number: response.data.phone_number,
        height_cm: response.data.height_cm ? Math.round(response.data.height_cm) : null,
        goal_weight_kg: response.data.goal_weight_kg,
        goal_weight_date: response.data.goal_weight_date ? parseISO(response.data.goal_weight_date) : null,
      });

      // Fetch photo if URL exists
      if (response.data.photo_url) {
        const fetchImageBlob = async () => {
          if (response.data.id && response.data.photo_url) {
            // Construct the proxy URL (relative path is fine for apiClient)
            const filename = response.data.photo_url.split('/').pop();
            if (!filename) return; 
            // Construct path RELATIVE to apiClient's baseURL (which already includes /api/v1)
            const proxyUrl = `patients/profile-photos/${response.data.id}/${filename}`;
            
            try {
              console.log(`Fetching image blob from proxy: ${proxyUrl}`);
              const response = await apiClient.get(proxyUrl, { responseType: 'blob' });
              const objectUrl = URL.createObjectURL(response.data);
              setProfileImageUrl(objectUrl);
              console.log(`Created object URL: ${objectUrl}`);
            } catch (error) {
              console.error("Error fetching profile image blob:", error);
              setProfileImageUrl(null); 
            }
          } else {
             setProfileImageUrl(null); 
          }
        };

        fetchImageBlob();
      }

    } catch (err: unknown) {
      console.error("Error fetching profile:", err);
      let errorMsg = 'An unknown error occurred';
      if (err instanceof AxiosError) {
        // Safely access detail property
        const responseData = (err as AxiosError<Record<string, unknown>>).response?.data;
        if (isErrorWithDetail(responseData)) {
          errorMsg = responseData.detail;
        } else {
          errorMsg = err.message || errorMsg;
        }
      } else if (err instanceof Error) {
        errorMsg = err.message;
      }
      toast.error(`Failed to load profile: ${errorMsg}`);
      setProfileData(null); 
    } finally {
      setIsLoading(false);
    }
  }, [reset]);

  // Fetch profile on component mount
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  // Effect to fetch image blob and create object URL when profileData.photo_url changes
  useEffect(() => {
    let objectUrl: string | null = null;

    const fetchImageBlob = async () => {
      if (profileData?.id && profileData.photo_url) {
        // Construct the proxy URL (relative path is fine for apiClient)
        const filename = profileData.photo_url.split('/').pop();
        if (!filename) return; 
        // Construct path RELATIVE to apiClient's baseURL (which already includes /api/v1)
        const proxyUrl = `patients/profile-photos/${profileData.id}/${filename}`;
        
        try {
          console.log(`Fetching image blob from proxy: ${proxyUrl}`);
          const response = await apiClient.get(proxyUrl, { responseType: 'blob' });
          objectUrl = URL.createObjectURL(response.data);
          setProfileImageUrl(objectUrl);
          console.log(`Created object URL: ${objectUrl}`);
        } catch (error) {
          console.error("Error fetching profile image blob:", error);
          setProfileImageUrl(null); 
        }
      } else {
         setProfileImageUrl(null); 
      }
    };

    fetchImageBlob();

    // Cleanup function to revoke the object URL
    return () => {
      if (objectUrl) {
        console.log(`Revoking object URL: ${objectUrl}`);
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [profileData?.id, profileData?.photo_url]); 

  // --- Update Profile Data ---
  const onSubmit = async (data: ProfileFormData) => {
    // Check if there are actual changes or a photo selected
    if (!isDirty && !selectedPhoto) {
      toast.info("No changes to save.");
      return;
    }

    setIsSubmitting(true);
    const formData = new FormData();

    // Ensure we have the email - it's the only guaranteed field
    if (!profileData || !profileData.email) {
      toast.error("Cannot update profile: email is required");
      setIsSubmitting(false);
      return;
    }

    // Clean phone number by removing all non-digits except leading +
    const cleanPhoneNumber = (phone: string | null | undefined): string => {
      if (!phone) return "";
      // If it starts with +, keep it, otherwise remove all non-digits
      if (phone.startsWith('+')) {
        return '+' + phone.slice(1).replace(/\D/g, '');
      }
      return phone.replace(/\D/g, '');
    };

    // Prepare the JSON data for profile update fields
    const profileUpdateData: Record<string, string | number | null | undefined> = {
      email: profileData.email, 
      first_name: data.first_name || "",
      last_name: data.last_name || "",
      phone_number: cleanPhoneNumber(data.phone_number),
      height_cm: data.height_cm,
      date_of_birth: data.date_of_birth ? format(data.date_of_birth, 'yyyy-MM-dd') : null,
      goal_weight_kg: data.goal_weight_kg,
      goal_weight_date: data.goal_weight_date ? format(data.goal_weight_date, 'yyyy-MM-dd') : null
    };
    
    console.log("Sending profile data:", profileUpdateData);

    // Convert to JSON string and log it
    const profileUpdateJson = JSON.stringify(profileUpdateData);
    console.log("JSON string being sent:", profileUpdateJson);
    
    // Append the profile update data as a JSON string to the FormData
    formData.append('profile_update', profileUpdateJson);

    // Debug: Log FormData entries
    console.log("FormData entries:");
    for (const pair of formData.entries()) {
      console.log(pair[0], pair[1]);
    }

    // Append photo file if selected
    if (selectedPhoto) {
      formData.append('profile_photo', selectedPhoto);
    }

    try {
      // Always send the form data since it contains both profile update and/or photo
      if (isDirty || selectedPhoto) {
        const response = await apiClient.put<PatientProfileData>('/patients/me', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        // Explicitly type before logging
        const updatedData: PatientProfileData = response.data;
        console.log("Profile updated successfully:", updatedData);
        setProfileData(updatedData); 
        setSelectedPhoto(null);
      }

      reset({ 
        first_name: profileData!.first_name,
        last_name: profileData!.last_name,
        date_of_birth: profileData!.date_of_birth ? parseISO(profileData!.date_of_birth) : null,
        phone_number: profileData!.phone_number,
        height_cm: profileData!.height_cm,
        goal_weight_kg: profileData!.goal_weight_kg,
        goal_weight_date: profileData!.goal_weight_date ? parseISO(profileData!.goal_weight_date) : null,
      });

      // Force refresh profile from backend to ensure latest saved data
      await fetchProfile();

      toast.success("Profile updated successfully!");

    } catch (err: unknown) {
      console.error("Error updating profile:", err);
      
      // Handle Bad Request errors like duplicate phone number (status 400)
      if (err instanceof AxiosError) {
        // Safely access detail property
        if (isErrorWithDetail(err.response?.data)) {
          const errorMessage = err.response.data.detail;
          toast.error("Update failed.", {
            description: errorMessage,
            // Adding a custom icon for duplicate phone number error
            ...(errorMessage.includes('phone number') && {
              icon: ''
            })
          });
        }
        // Handle validation errors from backend (status 422)
        else if (err.response?.status === 422 && err.response?.data?.detail) {
          // Basic handling: show the first validation error detail
          const errorDetail = err.response.data.detail[0]?.msg || 'Validation failed.';
          toast.error("Update failed.", { description: errorDetail });
        }
        // Handle all other errors
        else {
          toast.error("Failed to update profile.", {
            description: err.response?.data?.detail || err.message || 'An unknown server error occurred.',
          });
        }
      } else {
        toast.error("Failed to update profile.", {
          description: err instanceof Error ? err.message : 'An unknown error occurred.',
        });
      }
      // Refresh profile data from backend after showing the error
      fetchProfile();
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle photo selection
  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      // Basic validation (optional)
      if (file.size > 5 * 1024 * 1024) { 
          toast.error("File size exceeds 5MB limit.");
          return;
      }
      if (!file.type.startsWith('image/')) {
          toast.error("Please select an image file.");
          return;
      }

      setSelectedPhoto(file);
      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setSelectedPhoto(null);
      // Revert preview to the fetched image URL (or null if none) if file selection is cleared
      setPhotoPreview(profileData?.photo_url || null);
    }
  };

  // Helper to get initials for Avatar fallback
  const getInitials = (firstName?: string | null, lastName?: string | null): string | React.ReactElement => {
    const firstInitial = firstName?.[0] || '';
    const lastInitial = lastName?.[0] || '';
    const initials = `${firstInitial}${lastInitial}`.toUpperCase();
    return initials || <User className="h-full w-full" />;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-200px)]">
        <p>Loading profile...</p> 
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-200px)]">
        <p className="text-destructive">Failed to load profile data. Please try again later.</p>
      </div>
    );
  }

  // photoPreview is for local selections, profileImageUrl is for fetched blobs
  // avatarSrc calculation and log removed

  return (
    <>
      <Toaster richColors /> 
      <div className="container mx-auto p-4 md:p-6 lg:p-8 max-w-2xl">
        <h1 className="text-2xl font-bold mb-6">Your Profile</h1>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Card>
            <CardHeader className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex flex-col items-center space-y-2">
                <Avatar className="h-24 w-24">
                  {/* Construct proxy URL if original photo_url exists, otherwise use preview */}
                  <AvatarImage
                    src={photoPreview || profileImageUrl || undefined} 
                    alt="Profile photo" />
                  <AvatarFallback>{getInitials(profileData!.first_name, profileData!.last_name)}</AvatarFallback>
                </Avatar>
                <Label htmlFor="photo" className="cursor-pointer text-sm text-blue-600 hover:underline">Change Photo</Label>
                <Input id="photo" name="photo" type="file" accept="image/*" onChange={handlePhotoChange} className="hidden" disabled={isSubmitting}/>
              </div>
              <div className="text-center sm:text-left">
                <CardTitle className="text-xl">{profileData!.first_name || 'Patient'} {profileData!.last_name || ''}</CardTitle>
                <p className="text-sm text-muted-foreground">{profileData!.email}</p> 
              </div>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              {/* Email (Read-Only) */}
              <div className="space-y-1">
                <Label htmlFor="email_display">Email</Label>
                <Input id="email_display" value={profileData!.email} readOnly disabled className="bg-muted/50" />
              </div>

              {/* First Name */}
              <div className="space-y-1">
                <Label htmlFor="first_name">First Name</Label>
                <Controller
                  name="first_name"
                  control={control}
                  render={({ field }) => <Input id="first_name" {...field} value={field.value ?? ''} disabled={isSubmitting} />}
                />
                {errors.first_name && <p className="text-sm text-destructive">{errors.first_name?.message}</p>}
              </div>

              {/* Last Name */}
              <div className="space-y-1">
                <Label htmlFor="last_name">Last Name</Label>
                <Controller
                  name="last_name"
                  control={control}
                  render={({ field }) => <Input id="last_name" {...field} value={field.value ?? ''} disabled={isSubmitting} />}
                />
                {errors.last_name && <p className="text-sm text-destructive">{errors.last_name?.message}</p>}
              </div>

              {/* Date of Birth - Typable Input */}
              <div className="space-y-1">
                <Label htmlFor="date_of_birth">Date of Birth</Label>
                <Controller
                  name="date_of_birth"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={field.value ? format(field.value, 'yyyy-MM-dd') : ''}
                      onChange={(e) => {
                        const val = e.target.value;
                        field.onChange(val ? parseISO(val) : null);
                      }}
                      disabled={isSubmitting}
                      max={format(new Date(), 'yyyy-MM-dd')} 
                      className="w-full"
                    />
                  )}
                />
                {errors.date_of_birth && <p className="text-sm text-destructive">{errors.date_of_birth?.message}</p>}
              </div>

              {/* Phone Number */}
              <div className="space-y-1">
                <Label htmlFor="phone_number">Phone Number</Label>
                <Controller
                  name="phone_number"
                  control={control}
                  render={({ field }) => <Input id="phone_number" type="tel" {...field} value={field.value ?? ''} disabled={isSubmitting} />}
                />
                {errors.phone_number && <p className="text-sm text-destructive">{errors.phone_number?.message}</p>}
              </div>

              {/* Height */}
              <div className="space-y-1">
                <Label htmlFor="height_cm">Height (cm)</Label>
                <Controller
                  name="height_cm"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="height_cm"
                      type="number"
                      // Spread field props BUT override value and onChange
                      {...field}
                      value={field.value ?? ''} 
                      onChange={(e) => {
                        // Convert input value to number or null before passing to react-hook-form state
                        const value = e.target.value;
                        field.onChange(value === '' ? null : parseInt(value, 10));
                      }}
                      disabled={isSubmitting}
                      min="0"
                    />
                  )}
                />
                {errors.height_cm && <p className="text-sm text-destructive">{errors.height_cm?.message}</p>}
              </div>

              {/* Goal Weight */}
              <div className="space-y-1">
                <Label htmlFor="goal_weight_kg">Goal Weight (kg)</Label>
                <Controller
                  name="goal_weight_kg"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="goal_weight_kg"
                      type="number"
                      {...field}
                      value={field.value ?? ''} 
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === '' ? null : parseFloat(value));
                      }}
                      disabled={isSubmitting}
                      min="0"
                      step="0.1"
                      placeholder="Enter your goal weight in kg"
                    />
                  )}
                />
                {errors.goal_weight_kg && <p className="text-sm text-destructive">{errors.goal_weight_kg?.message}</p>}
              </div>

              {/* Goal Weight Date */}
              <div className="space-y-1">
                <Label htmlFor="goal_weight_date">Goal Date</Label>
                <Controller
                  name="goal_weight_date"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="goal_weight_date"
                      type="date"
                      {...field}
                      value={field.value ? format(field.value, 'yyyy-MM-dd') : ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value ? new Date(value) : null);
                      }}
                      disabled={isSubmitting}
                      min={format(new Date(), 'yyyy-MM-dd')} // Don't allow past dates
                    />
                  )}
                />
                {errors.goal_weight_date && <p className="text-sm text-destructive">{errors.goal_weight_date?.message}</p>}
              </div>

            </CardContent>
            <CardFooter className="flex flex-col items-end space-y-2">
              <Button type="submit" disabled={isSubmitting || (!isDirty && !selectedPhoto)}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
              {/* Logout Button Added Here */}
              <Button variant="outline" onClick={handleLogout} className="mt-4">
                Logout
              </Button>
            </CardFooter>
          </Card>
        </form>
      </div>
    </>
  );
};

export default ProfilePage;