import React, { useState, useEffect, useCallback } from 'react';

import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter, CardDescription } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { getWithAuth, postWithAuth } from '@/lib/utils';
import { format } from 'date-fns';
import { Info, AlertTriangle } from "lucide-react";
import { AxiosError } from 'axios'; // Import AxiosError

// Define Severity Levels matching the backend Enum
enum SeverityLevel {
  MINOR = "minor",
  MODERATE = "moderate",
  MAJOR = "major",
}

// Define the structure of a side effect report entry based on API response
interface SideEffectReportEntry {
  id: string;
  description: string;
  severity: SeverityLevel;
  status: string;
  reported_at: string; // ISO string format
}

// Define the structure for the API response (potentially paginated)
interface PaginatedSideEffectResponse {
  items: SideEffectReportEntry[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Define the structure for the POST request payload
interface LogSideEffectPayload {
  description: string;
  severity: SeverityLevel;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// --- Helper Functions ---
const formatDateForDisplay = (dateString: string | null | undefined): string => {
  if (!dateString) {
    return "N/A"; // Handle null or undefined input
  }
  try {
    // Ensure the date string is treated as UTC by appending 'Z' if it's missing
    // and it looks like a standard ISO format without timezone.
    const potentialUtcString = 
      /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateString) && !dateString.endsWith('Z')
      ? dateString + 'Z' 
      : dateString;

    // Creating a new Date object from a full ISO string (with Z or offset)
    // correctly interprets it and adjusts to the browser's local timezone.
    const date = new Date(potentialUtcString);
    
    // Check if the date is valid after parsing
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date produced after parsing');
    }
    
    // format() then displays this Date object using the browser's local timezone settings
    return format(date, 'PPpp'); // Example format: Apr 11, 2025, 11:06:46 AM
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return "Invalid date"; // Return fallback text on error
  }
};

const getSeverityIcon = (severity: SeverityLevel) => {
  switch (severity) {
    case SeverityLevel.MINOR:
      return <Info className="h-4 w-4 text-blue-500" />;
    case SeverityLevel.MODERATE:
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case SeverityLevel.MAJOR:
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    default:
      return null;
  } 
};

const SideEffectPage: React.FC = () => {
  const [descriptionInput, setDescriptionInput] = useState<string>('');
  const [severityInput, setSeverityInput] = useState<SeverityLevel | ''>('');
  const [sideEffectHistory, setSideEffectHistory] = useState<SideEffectReportEntry[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false);
  const [isLoadingLog, setIsLoadingLog] = useState<boolean>(false);
  const [errorHistory, setErrorHistory] = useState<string | null>(null);
  const [errorLog, setErrorLog] = useState<string | null>(null);

  // --- API Interaction Handlers ---

  const fetchSideEffectHistory = useCallback(async () => {
    setIsLoadingHistory(true);
    setErrorHistory(null);
    try {
      // Use the correct full endpoint path
      const response: PaginatedSideEffectResponse = await getWithAuth(`${API_BASE_URL}/patients/me/side-effects?sortBy=created_at&sortDesc=true&limit=100`); // Fetch latest 100
      // Backend should sort, but sort locally as fallback/verification
      const sortedItems = (response.items || []).sort(
        (a, b) => new Date(b.reported_at).getTime() - new Date(a.reported_at).getTime()
      );
      setSideEffectHistory(sortedItems);
    } catch (err: unknown) {
      console.error('Error fetching side effect history:', err);
      let errorMessage = 'Failed to load history.';
      if (err instanceof AxiosError) {
        const responseData = err.response?.data as Record<string, unknown>; 
        if (responseData && typeof responseData.detail === 'string') {
          errorMessage = responseData.detail;
        } else {
          errorMessage = err.message || errorMessage;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setErrorHistory(errorMessage);
      setSideEffectHistory([]); // Clear history on error
    } finally {
      setIsLoadingHistory(false);
    }
  }, []);

  useEffect(() => {
    fetchSideEffectHistory();
  }, [fetchSideEffectHistory]);

  const handleLogSideEffect = async () => {
    if (!descriptionInput.trim() || !severityInput) {
      setErrorLog('Please provide both a description and severity level.');
      toast.warning('Description and severity are required.');
      return;
    }

    setIsLoadingLog(true);
    setErrorLog(null);

    const reportData: LogSideEffectPayload = { description: descriptionInput.trim(), severity: severityInput };
    // Use the correct full endpoint path
    try {
      await postWithAuth(`${API_BASE_URL}/patients/me/side-effects`, reportData);
      toast.success("Side effect reported successfully!");
      // Clear form
      setDescriptionInput('');
      setSeverityInput('');
      // Refresh history
      fetchSideEffectHistory();
    } catch (err: unknown) {
      console.error('Error reporting side effect:', err);
      let errorMessage = 'Failed to report side effect.';
      if (err instanceof AxiosError) {
        const responseData = err.response?.data as Record<string, unknown>; 
        if (responseData && typeof responseData.detail === 'string') {
          errorMessage = responseData.detail;
        } else {
          errorMessage = err.message || errorMessage;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setErrorLog(errorMessage);
    } finally {
      setIsLoadingLog(false);
    }
  };

  // --- Render Logic ---
  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Report Side Effects</h1>
      <p className="text-muted-foreground">
        Log any side effects you are experiencing. Your care team will review this information.
      </p>

      {/* Log New Side Effect Card */}
      <Card>
        <CardHeader>
          <CardTitle>Report a New Side Effect</CardTitle>
          <CardDescription>Describe the side effect and its severity.</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-6">
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the side effect(s)..."
              value={descriptionInput}
              onChange={(e) => setDescriptionInput(e.target.value)}
              disabled={isLoadingLog}
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="severity">Severity</Label>
            <Select
              value={severityInput}
              onValueChange={(value) => setSeverityInput(value as SeverityLevel)}
              disabled={isLoadingLog}
            >
              <SelectTrigger id="severity">
                <SelectValue placeholder="Select severity level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={SeverityLevel.MINOR}>Minor</SelectItem>
                <SelectItem value={SeverityLevel.MODERATE}>Moderate</SelectItem>
                <SelectItem value={SeverityLevel.MAJOR}>Major</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {errorLog && <p className="text-sm text-red-600">{errorLog}</p>}
        </CardContent>
        <CardFooter>
          <Button onClick={handleLogSideEffect} disabled={isLoadingLog} className="w-full md:w-auto">
            {isLoadingLog ? 'Reporting...' : 'Report Side Effect'}
          </Button>
        </CardFooter>
      </Card>

      {/* Side Effect History Card */}
      <Card>
        <CardHeader>
          <CardTitle>Report History</CardTitle>
          <CardDescription>Your previously reported side effects.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            {isLoadingHistory ? (
              <p className="text-muted-foreground">Loading history...</p>
            ) : errorHistory ? (
              <p className="text-red-600">{errorHistory}</p>
            ) : sideEffectHistory.length === 0 ? (
              <p className="text-muted-foreground">No side effects reported yet.</p>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"> {/* Grid layout */}
                {sideEffectHistory.map((entry) => (
                  <Card key={entry.id} className="flex flex-col"> {/* Individual Card */}
                    <CardHeader className="pb-2">
                      <CardDescription>Reported: {formatDateForDisplay(entry.reported_at)}</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow space-y-2">
                      <p className="font-medium">Description:</p>
                      <p className="text-sm text-muted-foreground break-words">{entry.description}</p>
                      <div className="flex items-center justify-between pt-2">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Severity</p>
                          <div className="flex items-center gap-1.5 mt-1">
                            {getSeverityIcon(entry.severity)}
                            <span className="capitalize">{entry.severity}</span>
                          </div>
                        </div>
                        <div>
                           <p className="text-sm font-medium text-muted-foreground">Status</p>
                           <p className="text-sm capitalize font-semibold mt-1">{entry.status}</p> 
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SideEffectPage;
