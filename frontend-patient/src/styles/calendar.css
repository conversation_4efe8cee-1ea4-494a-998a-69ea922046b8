/* Calendar appointment status indicators */
.rdp-day_hasConfirmedAppointment {
  position: relative;
}

.rdp-day_hasConfirmedAppointment::after {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: hsl(142, 76%, 36%); /* Green color */
  border-radius: 50%;
}

.rdp-day_hasRequestedAppointment {
  position: relative;
}

.rdp-day_hasRequestedAppointment::after {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: hsl(38, 92%, 50%); /* Amber color */
  border-radius: 50%;
}