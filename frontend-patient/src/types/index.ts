// src/types/index.ts

export interface AppointmentResponse {
  id: string;
  appointment_datetime: string;
  duration_minutes: number;
  appointment_type: string;
  reason?: string;
  patient_notes?: string;
  status: string; // e.g., 'scheduled', 'completed', 'cancelled'
  created_at: string;
  updated_at: string;
  patient_id: string;
  clinician_id: string;
  patient: { first_name: string; last_name: string; email: string };
  clinician: { first_name: string; last_name: string; email: string };
  cancelled_at?: string;
  cancelled_by_id?: string;
  cancellation_reason?: string;
}

export interface PaginatedAppointments {
  items: AppointmentResponse[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
