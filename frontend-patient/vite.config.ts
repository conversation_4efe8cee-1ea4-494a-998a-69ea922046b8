import path from "path"
import tailwindcss from "@tailwindcss/vite"
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      '@pulsetrack/shared-frontend': path.resolve(__dirname, '../packages/shared-frontend/src'),
    },
  },
  server: {
    host: true,
    port: 5174,
    watch: {
      usePolling: true,
      interval: 100,
    },
  },
})
