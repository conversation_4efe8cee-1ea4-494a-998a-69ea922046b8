docker exec -it pulsetrack-db-1 psql -U pulsetrack_user -d pulsetrack_dev

-- List all databases
\l

-- Connect to a specific database
\c pulsetrack_dev

-- List all tables
\dt

-- List all tables with more details
\dt+

-- Describe a specific table
\d table_name

-- Show running queries
SELECT pid, age(clock_timestamp(), query_start), usename, query 
FROM pg_stat_activity 
WHERE query != '<IDLE>' AND query NOT ILIKE '%pg_stat_activity%' 
ORDER BY query_start desc;

-- Kill a specific query
SELECT pg_terminate_backend(pid);

-- Show table sizes
SELECT 
    table_name, 
    pg_size_pretty(pg_total_relation_size(table_name)) as total_size
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY pg_total_relation_size(table_name) DESC;

-- Exit psql
\q