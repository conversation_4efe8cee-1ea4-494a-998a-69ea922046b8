# MPI Memory Knowledge Graph: MCP/LLM User Guide

## Overview

The MPI Memory Knowledge Graph is designed to be accessed primarily through AI assistants (like <PERSON>, <PERSON>T-4, etc.) via the Model Context Protocol (MCP). This guide explains how to effectively use the Knowledge Graph through natural language interactions with your AI assistant.

## How It Works

Instead of writing Cypher queries directly, you'll ask your AI assistant to perform operations on the Knowledge Graph. The AI uses three MCP tools to interact with the graph:

1. **`get_neo4j_schema`** - Understands the graph structure
2. **`read_neo4j_cypher`** - Retrieves information
3. **`write_neo4j_cypher`** - Creates or updates data

## Getting Started

### First-Time Setup

Simply tell your AI assistant:

> "I'm starting a new project called [ProjectName]. Please set up the Knowledge Graph with my developer profile and initial project structure."

The AI will:
- Create your developer profile
- Initialize the project
- Set up basic architecture components
- Create your first development session

### Example Setup Conversation

**You**: "I'm starting a new e-commerce project called ShopFlow. I'll be using Python with FastAPI for the backend and React for the frontend. Please set up the Knowledge Graph."

**AI Assistant**: "I'll set up your Knowledge Graph for the ShopFlow project. This will include:
- Creating your developer profile
- Setting up the ShopFlow project
- Defining the basic architecture (FastAPI backend, React frontend, database)
- Starting your first development session

[The AI then executes the necessary MCP commands to create all the nodes and relationships]

Done! Your Knowledge Graph is now tracking the ShopFlow project. What would you like to work on first?"

## Common Use Cases

### 1. Starting a Work Session

**Natural Language Request**:
> "I'm starting work on user authentication. Please create a new session focused on implementing JWT tokens."

**What the AI Does**:
- Creates a new DevelopmentSession node
- Links it to your developer profile
- Sets the focus and context
- Prepares the session for tracking your work

### 2. Creating and Managing Tasks

**Natural Language Requests**:
> "Create a task to implement user login endpoint with high priority"

> "What tasks do I have pending for authentication?"

> "Mark the JWT implementation task as complete. It took 4 hours."

**AI Capabilities**:
- Creates tasks with appropriate metadata
- Queries existing tasks by topic or status
- Updates task progress and completion
- Tracks time spent

### 3. Recording Technical Decisions

**Natural Language Request**:
> "Record a decision: We're using refresh tokens stored in httpOnly cookies for security. The alternative was localStorage but that's vulnerable to XSS."

**AI Actions**:
- Creates a Decision node with full context
- Links it to the current session
- Captures alternatives and trade-offs
- Maintains decision history

### 4. Context Switching

**Natural Language Requests**:
> "I need to pause and work on a bug fix. Save my current context."

> "Resume my work on authentication from yesterday."

**AI Handles**:
- Preserving mental model and next steps
- Creating context snapshots
- Retrieving previous context
- Seamless session transitions

### 5. Progress Tracking

**Natural Language Requests**:
> "Show me what I accomplished this week"

> "What's the status of the authentication feature?"

> "Which tasks are blocking other work?"

**AI Provides**:
- Aggregated progress reports
- Feature completion status
- Dependency analysis
- Blocker identification

## Advanced Workflows

### Comprehensive Project Status

**You**: "Give me a complete status update on the ShopFlow project including progress, blockers, and what needs attention."

**AI**: Queries the Knowledge Graph to provide:
- Overall completion percentage
- Active tasks and their status
- Recent accomplishments
- Current blockers
- Upcoming priorities
- Team velocity trends

### Intelligent Task Prioritization

**You**: "What should I work on next? Consider dependencies and business impact."

**AI**: Uses the Priority Engine to:
- Analyze task dependencies
- Evaluate business impact
- Consider your current context
- Recommend optimal next task
- Explain the reasoning

### Knowledge Retrieval

**You**: "Have we dealt with CORS issues before? What was the solution?"

**AI**: Searches the Knowledge Graph for:
- Similar past problems
- Previous solutions
- Technical decisions made
- Implementation details
- Lessons learned

### Architecture Impact Analysis

**You**: "If I change the authentication service, what else might be affected?"

**AI**: Performs impact analysis:
- Identifies dependent components
- Shows affected functions
- Lists related tasks
- Highlights potential risks

## Best Practices for AI Interaction

### 1. Be Specific About Context

**Good**: "I'm working on the payment processing module and need to integrate Stripe webhooks"

**Better**: "I'm in the payment module working on Stripe webhook integration for order confirmation. This is part of the checkout epic."

### 2. Update Progress Regularly

Throughout your work session, inform the AI:
- "I just completed the webhook endpoint"
- "I'm stuck on webhook signature validation"
- "Found a bug in the payment calculation"

### 3. Document Decisions in Real-Time

When you make choices:
- "Decided to use Stripe's webhook CLI for local testing"
- "Choosing async processing for webhooks to avoid timeouts"

### 4. Use Natural Language for Complex Queries

Instead of trying to formulate complex queries, just ask:
- "Show me all the security-related decisions we've made"
- "What authentication patterns have we used successfully?"
- "Which components have the most technical debt?"

## Session Management Patterns

### Daily Workflow

**Morning Start**:
> "Good morning! Please start a new session. First, show me what I was working on yesterday and any overnight issues."

**Context Switches**:
> "I need to switch to reviewing pull requests. Save my current debugging context."

**End of Day**:
> "I'm done for today. Please summarize what I accomplished and set up tomorrow's priorities."

### Weekly Planning

**Monday Morning**:
> "Start a new week. Show me last week's progress and help me plan this week's priorities based on our sprint goals."

### Crisis Management

**Urgent Issue**:
> "Critical bug in production! Start an emergency session focused on the payment processing error. Show me recent changes to payment-related components."

## AI Assistant Capabilities

Your AI assistant can:

### 1. Intelligent Querying
- Find patterns in your development history
- Identify similar problems and solutions
- Track decision rationale over time
- Analyze productivity patterns

### 2. Proactive Assistance
- Suggest when to take breaks
- Remind about incomplete tasks
- Warn about potential conflicts
- Recommend relevant past solutions

### 3. Context-Aware Responses
- Understand your current focus
- Maintain conversation continuity
- Reference recent work appropriately
- Provide relevant suggestions

### 4. Report Generation
- Daily standup summaries
- Weekly progress reports
- Architecture documentation
- Decision logs

## Example Conversations

### Starting a New Feature

**You**: "I need to implement user profiles. Create an epic and initial tasks."

**AI**: "I'll create a User Profiles epic with initial tasks. Based on your architecture, this will need:

1. Created Epic: 'User Profiles Implementation'
2. Created Tasks:
   - Design profile database schema (backend, high priority)
   - Create profile API endpoints (backend, high priority)
   - Build profile React components (frontend, medium priority)
   - Add profile image upload (full-stack, medium priority)
   - Implement profile privacy settings (full-stack, low priority)

I've set up dependencies: API endpoints must be done before frontend components. Would you like to start with the database schema?"

### Debugging Assistance

**You**: "I'm getting a 'JWT token expired' error but the token should be valid for 24 hours."

**AI**: "Let me check your authentication implementation history. I found:

1. Previous Issue: Similar token expiration problem 2 weeks ago
2. Root Cause: Timezone mismatch between server and client
3. Solution Applied: Standardized on UTC for all token timestamps

Checking your current context... You're working in the user service. Have you verified:
- The server is using UTC for token generation?
- The token expiration claim is correctly set?
- The client isn't modifying the token?

Would you like me to record this debugging session and link it to the previous issue?"

### Architecture Planning

**You**: "We need to add real-time notifications. How should this integrate with our current architecture?"

**AI**: "Based on your current architecture, here's how notifications could integrate:

Current Components I found:
- FastAPI backend (REST API)
- React frontend
- PostgreSQL database
- Redis cache

For real-time notifications, I recommend:
1. Add WebSocket support to FastAPI
2. Create a NotificationService component
3. Use Redis for pub/sub messaging

This would affect:
- Backend: New WebSocket endpoints
- Frontend: WebSocket client integration
- Database: Notification preferences table

Should I create an epic with detailed tasks for implementing this?"

## Tips for Effective Use

### 1. Treat Your AI as a Pair Programmer
Share your thoughts, problems, and decisions as you would with a human colleague.

### 2. Use Conversational Commands
- "Let's work on..." instead of "Create task for..."
- "I'm thinking about..." instead of "Record decision..."
- "What happened with..." instead of "Query history of..."

### 3. Leverage AI Memory
The AI can maintain context across a conversation, so you can:
- Reference "that bug from earlier"
- Say "add that to the current task"
- Ask "what was that solution we discussed?"

### 4. Request Proactive Help
- "Keep track of my progress on this feature"
- "Remind me about best practices for this type of work"
- "Let me know if I'm creating technical debt"

## Troubleshooting

### AI Doesn't Understand Context

**Solution**: Provide more specific information:
- Current project name
- What you're working on
- Recent actions taken

### Queries Return Unexpected Results

**Solution**: Ask the AI to:
- Explain what it's searching for
- Show the actual query it's using
- Refine the search parameters

### Session Management Issues

**Solution**: Natural language commands:
- "Show me all active sessions"
- "Clean up abandoned sessions"
- "Merge duplicate tasks"

## Summary

The MPI Memory Knowledge Graph transforms AI assistants into intelligent development partners that:
- Remember your complete development history
- Understand your project's context
- Provide relevant, timely assistance
- Learn from your patterns and preferences

By interacting naturally with your AI assistant, you build a rich knowledge base that becomes more valuable over time, creating a truly intelligent development environment.