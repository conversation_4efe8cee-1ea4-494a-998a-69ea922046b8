# MPI Memory Knowledge Graph: Technical Overview

## Architecture Overview

The MPI Memory Knowledge Graph is a Neo4j-based graph database system designed to capture, store, and query the complete context of software development projects. It provides persistent memory and intelligent context retrieval for AI-assisted development workflows.

## Core Technologies

- **Database**: Neo4j 5.x (Graph Database)
- **Query Language**: Cypher
- **Integration**: Model Context Protocol (MCP) for AI tool integration
- **Schema**: Dynamic node/relationship model with typed properties
- **Performance**: Optimized for complex traversals and pattern matching

## Graph Schema

### Node Types (Labels)

The system currently supports 90+ distinct node types, organized into these primary categories:

#### 1. **Project Management Nodes**
```cypher
// Core project entities
- Project: {name, status, created_date, architecture, primary_language}
- Task: {task_id, title, status, priority, estimated_hours, completion_percentage}
- Epic: {epic_id, title, status, description}
- Milestone: {milestone_id, name, target_date, completion_percentage}
- Issue: {number, title, status, severity, business_impact}
```

#### 2. **Development Session Nodes**
```cypher
- DevelopmentSession: {session_id, start_time, status, primary_focus}
- Session_State: {flow_state, mental_model, current_context}
- AI_Context: {conversation_thread_active, context_continuity_level}
- Focus_Context: {focus_level, flow_state, primary_objective}
- Tool_State: {active_files[], terminal_sessions, browser_tabs[]}
```

#### 3. **Architecture & Code Nodes**
```cypher
- Component: {name, file_path, type, status, complexity}
- Function: {name, parameters[], return_type, file_path, line_numbers}
- Service: {name, path, key_functions[], implementation_notes}
- Database_Model: {name, key_attributes[], relationships[]}
- API_Schema: {name, authentication, description}
```

#### 4. **AI Integration Nodes**
```cypher
- AI_Conversation_Context: {conversation_topic, ai_assistance_level}
- Priority_Engine: {algorithm_version, focus_protection_active}
- Context_Generator: {version, query_patterns[], context_types[]}
- AI_Query_Template: {name, cypher_template, use_cases[]}
```

#### 5. **Business Domain Nodes**
```cypher
- Business_Domain_Concept: {name, description}
- Business_Rule: {name, implementation}
- Business_Workflow: {name, stages[]}
- External_Integration: {name, provider, api_version}
```

### Relationship Types

The graph uses 100+ relationship types to model complex interactions:

#### Hierarchical Relationships
- `CONTAINS`, `PART_OF`, `BELONGS_TO`, `INCLUDES`
- `HAS_TASK`, `HAS_SESSION`, `HAS_COMPONENT`

#### Dependency Relationships
- `DEPENDS_ON`, `PREREQUISITE_FOR`, `BLOCKS`
- `REQUIRES`, `USES`, `CALLS`, `IMPLEMENTS`

#### Workflow Relationships
- `WORKING_ON`, `COMPLETED_TASK`, `RESOLVED_ISSUE`
- `CREATED_COMPONENT`, `MODIFIED_FILE`, `FIXED_BUG`

#### AI-Specific Relationships
- `USES_AI_CONTEXT`, `HAS_AI_CONVERSATION`
- `USES_PRIORITY_ENGINE`, `DOCUMENTED_BY`

## Key Features

### 1. **Session Management**
```cypher
// Create new development session
CREATE (s:DevelopmentSession {
  session_id: randomUUID(),
  start_time: datetime(),
  status: 'active',
  primary_focus: $focus
})

// Link session to current work
MATCH (t:Task {task_id: $taskId})
CREATE (s)-[:WORKING_ON]->(t)
```

### 2. **Context Preservation**
```cypher
// Save session context for resumption
CREATE (cp:Context_Preservation {
  mental_model_snapshot: $mentalModel,
  next_actions: $nextActions,
  resume_instructions: $instructions
})
CREATE (session)-[:HAS_CONTEXT_PRESERVATION]->(cp)
```

### 3. **Intelligent Querying**
```cypher
// Get comprehensive project context
MATCH (p:Project {name: $projectName})
OPTIONAL MATCH (p)-[:HAS_TASK]->(t:Task {status: 'in_progress'})
OPTIONAL MATCH (p)-[:HAS_SESSION]->(s:DevelopmentSession {status: 'active'})
OPTIONAL MATCH (p)-[:HAS_ISSUE]->(i:Issue {status: 'open'})
RETURN p, collect(DISTINCT t) as tasks, 
       collect(DISTINCT s) as sessions, 
       collect(DISTINCT i) as issues
```

### 4. **Dependency Tracking**
```cypher
// Find all dependencies of a component
MATCH (c:Component {name: $componentName})
CALL apoc.path.subgraphAll(c, {
  relationshipFilter: "DEPENDS_ON>|USES>|CALLS>",
  maxLevel: 5
})
YIELD nodes, relationships
RETURN nodes, relationships
```

## Advanced Capabilities

### 1. **Pattern Detection**
The system can identify common development patterns:
```cypher
// Find recurring bug patterns
MATCH (b1:Bug)-[:AFFECTS_COMPONENT]->(c:Component)<-[:AFFECTS_COMPONENT]-(b2:Bug)
WHERE b1.root_cause = b2.root_cause AND b1 <> b2
RETURN c.name as component, b1.root_cause as pattern, count(*) as occurrences
ORDER BY occurrences DESC
```

### 2. **Impact Analysis**
```cypher
// Analyze impact of changes
MATCH (c:Component {name: $componentName})
MATCH (c)-[:USED_BY*1..3]->(dependent:Component)
MATCH (dependent)<-[:IMPLEMENTS]-(f:Function)
RETURN dependent.name, collect(f.name) as affected_functions
```

### 3. **AI Context Generation**
```cypher
// Generate context for AI assistance
MATCH (s:DevelopmentSession {status: 'active'})
MATCH (s)-[:WORKING_ON]->(current:Task)
MATCH (s)-[:HAS_STATE]->(state:Session_State)
OPTIONAL MATCH (current)-[:DEPENDS_ON]->(dep:Task)
OPTIONAL MATCH (s)-[:MADE_DECISION]->(d:Decision)
RETURN {
  current_task: current,
  session_state: state,
  dependencies: collect(DISTINCT dep),
  recent_decisions: collect(DISTINCT d)
} as context
```

### 4. **Performance Optimization**

#### Indexes
```cypher
CREATE INDEX task_status FOR (t:Task) ON (t.status);
CREATE INDEX session_active FOR (s:DevelopmentSession) ON (s.status);
CREATE INDEX component_name FOR (c:Component) ON (c.name);
CREATE INDEX issue_priority FOR (i:Issue) ON (i.priority, i.status);
```

#### Constraints
```cypher
CREATE CONSTRAINT unique_task_id FOR (t:Task) REQUIRE t.task_id IS UNIQUE;
CREATE CONSTRAINT unique_session_id FOR (s:DevelopmentSession) REQUIRE s.session_id IS UNIQUE;
CREATE CONSTRAINT unique_component_path FOR (c:Component) REQUIRE c.file_path IS UNIQUE;
```

## Integration Points

### 1. **MCP Tool Interface**
```python
# Tool functions exposed via MCP
- get_neo4j_schema(): Returns complete schema metadata
- read_neo4j_cypher(query, params): Execute read queries
- write_neo4j_cypher(query, params): Execute write operations
```

### 2. **AI Assistant Integration**
The system provides structured queries for AI context:
```cypher
// Priority-based task recommendation
MATCH (t:Task {status: 'pending'})
OPTIONAL MATCH (t)-[:BLOCKS]->(blocked:Task)
RETURN t.title, t.priority, 
       count(blocked) as blocks_count,
       t.business_impact
ORDER BY t.priority DESC, blocks_count DESC
```

### 3. **Event Sourcing**
All changes are tracked as events:
```cypher
CREATE (e:Event {
  type: $eventType,
  timestamp: datetime(),
  entity_id: $entityId,
  changes: $changes
})
```

## Query Patterns

### 1. **Session Initialization**
```cypher
// Start new session with context
MATCH (dev:Developer {id: $developerId})
CREATE (s:DevelopmentSession {
  session_id: randomUUID(),
  start_time: datetime(),
  status: 'active'
})
CREATE (dev)-[:STARTED_SESSION]->(s)
WITH s
MATCH (t:Task {status: 'in_progress', assigned_to: $developerId})
CREATE (s)-[:CONTINUES_WORK_ON]->(t)
```

### 2. **Context Switching**
```cypher
// Save context before switching
MATCH (s:DevelopmentSession {session_id: $sessionId})
SET s.status = 'paused'
CREATE (s)-[:HAS_CONTEXT_PRESERVATION]->(:Context_Preservation {
  saved_at: datetime(),
  mental_model: $mentalModel,
  next_actions: $nextActions
})
```

### 3. **Knowledge Retrieval**
```cypher
// Find similar past solutions
MATCH (p:Problem)-[:SOLVED_BY]->(s:Solution)
WHERE p.category = $problemCategory
MATCH (s)-[:IMPLEMENTED_IN]->(c:Component)
RETURN s.description, s.implementation, c.file_path
ORDER BY s.effectiveness_score DESC
LIMIT 5
```

## Performance Characteristics

- **Node Creation**: O(1) with proper indexes
- **Relationship Creation**: O(1) for direct relationships
- **Pattern Matching**: O(n*m) where n=nodes, m=relationships in pattern
- **Traversal**: Optimized with relationship indexes
- **Full-text Search**: Supported via Neo4j full-text indexes

## Best Practices

### 1. **Query Optimization**
- Use parameters instead of string concatenation
- Leverage indexes for node lookups
- Limit traversal depth with maxLevel
- Use OPTIONAL MATCH for nullable relationships

### 2. **Data Modeling**
- Keep node properties focused and relevant
- Use relationships to model connections, not properties
- Implement proper constraints for data integrity
- Version schema changes with migration scripts

### 3. **Transaction Management**
```cypher
// Use transactions for complex operations
CALL apoc.periodic.iterate(
  "MATCH (t:Task {status: 'completed'}) RETURN t",
  "SET t.archived = true",
  {batchSize: 1000, parallel: true}
)
```

## Monitoring & Maintenance

### 1. **Performance Monitoring**
```cypher
// Query performance analysis
CALL db.stats.retrieve('QUERIES')
YIELD data
RETURN data.query, data.executionTime, data.planning
ORDER BY data.executionTime DESC
```

### 2. **Database Health**
```cypher
// Check database statistics
CALL apoc.meta.stats()
YIELD nodeCount, relCount, labels, relTypes
```

### 3. **Cleanup Operations**
```cypher
// Archive old sessions
MATCH (s:DevelopmentSession)
WHERE s.last_updated < datetime() - duration('P30D')
SET s.archived = true
```

## Future Enhancements

1. **GraphQL API Layer**: Direct GraphQL interface for complex queries
2. **Real-time Subscriptions**: WebSocket support for live updates
3. **ML Integration**: Graph embeddings for similarity search
4. **Distributed Scaling**: Neo4j cluster configuration
5. **Advanced Analytics**: Graph algorithms for pattern discovery

## Security Considerations

- Role-based access control on node/relationship types
- Query parameter sanitization
- Audit logging for all write operations
- Encryption at rest and in transit
- Regular backup and recovery procedures