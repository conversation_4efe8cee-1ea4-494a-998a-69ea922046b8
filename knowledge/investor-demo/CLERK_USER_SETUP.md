# Clerk User Setup for PulseTrack Demo

## Overview
PulseTrack uses <PERSON> for authentication. All patients and clinicians must have valid Clerk user accounts. This guide explains how to set up demo users for the investor demonstration.

## Current Setup
The system currently has:
- 1 real patient user (<PERSON>)
- 1 real clinician user (<PERSON>, <PERSON>)
- Test data with invalid user IDs that won't work with authentication

## Options for Demo Users

### Option 1: Use Existing Users (Recommended for Quick Demo)
Use the existing authenticated users and create comprehensive demo data around them.

**Pros:**
- No Clerk setup required
- Can start immediately
- Authentication works out of the box

**Cons:**
- Limited to showing one patient/clinician perspective
- Can't demonstrate multi-patient scenarios live

### Option 2: Create Demo Users in Clerk Dashboard
Create dedicated demo accounts through the Clerk dashboard.

**Steps:**
1. Log into Clerk Dashboard (https://dashboard.clerk.com)
2. Navigate to Users section
3. Click "Create User"
4. For each demo user, provide:
   - Email (e.g., <EMAIL>)
   - Password (document these securely)
   - First/Last name
5. Copy the user ID (starts with `user_`)
6. Update the demo script with these IDs

**Demo Users to Create:**

#### Patients:
| Name | Email | Role | Demo <PERSON> |
|------|-------|------|---------------|
| Emma Thompson | <EMAIL> | Patient | Successful weight loss, mild side effects |
| Marcus Johnson | <EMAIL> | Patient | High-risk, medication adherence issues |
| Sarah Kim | <EMAIL> | Patient | New patient, exploring options |
| Robert Chen | <EMAIL> | Patient | Weight loss plateau, needs adjustment |

#### Clinicians:
| Name | Email | Role | Specialty |
|------|-------|------|-----------|
| Dr. Sarah Chen | <EMAIL> | Clinician | Weight Management |
| Dr. James Wilson | <EMAIL> | Clinician | Endocrinology |

### Option 3: Use Clerk API (Programmatic)
Create users programmatically using Clerk's backend API.

```python
import requests

CLERK_SECRET_KEY = "sk_test_..."  # Your Clerk secret key
CLERK_API_URL = "https://api.clerk.com/v1/users"

def create_clerk_user(email, first_name, last_name, password):
    headers = {
        "Authorization": f"Bearer {CLERK_SECRET_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "email_address": [email],
        "password": password,
        "first_name": first_name,
        "last_name": last_name,
        "skip_password_checks": True,
        "skip_password_requirement": True
    }
    
    response = requests.post(CLERK_API_URL, json=data, headers=headers)
    if response.status_code == 200:
        user = response.json()
        return user["id"]
    else:
        raise Exception(f"Failed to create user: {response.text}")
```

### Option 4: Demo Mode (Future Enhancement)
Implement a demo mode that bypasses Clerk authentication for demonstration purposes.

**Implementation Ideas:**
- Environment variable `DEMO_MODE=true`
- Special demo tokens that bypass verification
- Separate demo database with pre-authenticated sessions

## Running the Demo Seeder

Once users are set up:

1. Update the configuration in `seed_demo_data.py`:
```python
DEMO_CONFIG = {
    "use_existing_users": False,  # Set to False for new users
    "existing_patient_id": "user_xxx",  # Update with actual Clerk IDs
    "existing_clinician_id": "user_yyy",
}
```

2. Run the seeder:
```bash
cd backend
python scripts/seed_demo_data.py
```

## Demo User Credentials

Store demo credentials securely and share only with authorized demo presenters:

```
# Demo Credentials (CONFIDENTIAL)
# Store in password manager, not in code

Emma Thompson
Email: <EMAIL>
Password: [secure password]
Clerk ID: user_[generated]

Dr. Sarah Chen
Email: <EMAIL>
Password: [secure password]
Clerk ID: user_[generated]
```

## Quick Setup Script

For quick demo setup with existing users:

```bash
#!/bin/bash
# Quick demo setup

echo "Setting up PulseTrack demo data..."

# Ensure services are running
docker-compose up -d

# Wait for services
sleep 10

# Run the seeder with existing users
cd backend
python scripts/seed_demo_data.py

echo "Demo setup complete!"
```

## Troubleshooting

### Issue: "User not found" errors
**Solution**: Ensure Clerk user IDs in the database match actual Clerk users

### Issue: Authentication fails for demo users
**Solution**: Check that:
1. Users are created in the correct Clerk application
2. Environment variables match (development vs production)
3. User emails are verified (if required by Clerk settings)

### Issue: Demo data not showing up
**Solution**: 
1. Check database connection
2. Verify user IDs are correct
3. Look for errors in seeding script output

## Best Practices

1. **Security**: Never commit real user passwords to code
2. **Isolation**: Use a separate Clerk development instance for demos
3. **Cleanup**: Have a script to remove demo data after presentations
4. **Documentation**: Keep a list of demo user credentials in a secure location
5. **Testing**: Always test the full authentication flow before demos