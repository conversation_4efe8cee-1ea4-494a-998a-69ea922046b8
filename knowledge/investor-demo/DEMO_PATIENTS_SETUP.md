# Demo Patients Setup Guide

## Current Status

We have successfully created a compelling demo scenario with **<PERSON>** showing:
- Dangerous weight loss pattern (37 lbs in 30 days)
- Concerning symptoms mentioned to AI but not to clinician
- AI-generated critical alert for the clinician
- Complete medical history (appointments, medications, etc.)

## The Challenge with Additional Demo Patients

PulseTrack uses <PERSON> for authentication, which means every patient must have a valid <PERSON> user account. We cannot create "fake" patients with made-up IDs.

## Options for Additional Demo Patients

### Option 1: Create Real Clerk Users (Recommended for Full Demo)
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Create 3-4 additional test users:
   - <PERSON> (Type 2 Diabetes, normal progression)
   - <PERSON> (Weight management, good results)
   - <PERSON> (PCOS, steady progress)
   - <PERSON> (Pre-diabetes, excellent adherence)
3. Note their Clerk user IDs (format: `user_XXXXXXXXXXXXXXXXXXXXXXXXXXXX`)
4. Run a modified seed script with these real IDs

### Option 2: Use Only <PERSON> (Simplest)
Focus the demo entirely on <PERSON>'s critical case:
- Show how AI detected the dangerous pattern
- Emphasize the value of AI monitoring
- Mention that the system handles multiple patients but focus on this one compelling case

### Option 3: Create Demo Mode (Future Enhancement)
Implement a demo mode that bypasses Clerk authentication for investor presentations.

## Quick Setup Script for New Clerk Users

If you create new Clerk users, here's a template script:

```python
# Add to scripts/seed_real_demo_patients.py
REAL_DEMO_PATIENTS = [
    {
        "id": "user_XXXXXXXXXXXXXXXXXXXXXXXXXXXX",  # From Clerk
        "first_name": "Sarah",
        "last_name": "Johnson",
        "email": "<EMAIL>",
        "condition": "Type 2 Diabetes",
        "medication": "Ozempic"
    },
    # ... more patients
]
```

## Current Demo Talking Points

Even with just Michael Patient, we can demonstrate:

1. **AI Pattern Detection**: AI caught dangerous symptoms the patient didn't report to their doctor
2. **Comprehensive Monitoring**: Weight, side effects, medications, appointments all in one place
3. **Proactive Alerts**: Critical alert generated before the patient's next appointment
4. **Clinical Value**: Prevents potential cardiac events, dehydration, or other serious complications

## Recommendation

For the investor demo, focus on Michael's compelling case. Mention that the system handles multiple patients but demonstrate the value with this one powerful example of AI preventing a potential medical crisis.