# PulseTrack Demo Patient Scenarios

## Patient Profiles for Investor Demonstration

These patient scenarios are designed to showcase PulseTrack's AI capabilities in real-world clinical situations.

---

## 👤 Patient 1: <PERSON>
**Age**: 42 | **Status**: Active Treatment | **Medication**: Wegovy 1mg

### Background
- Started weight management program 3 months ago
- Initial weight: 197 lbs, Current: 185 lbs
- Good adherence, mild side effects

### Demo Scenarios

#### Scenario A: Side Effect Management
**Patient message**: "I've been feeling nauseous after my injections, especially in the mornings. Is this normal?"

**AI Response**:
- Acknowledges concern
- Provides evidence-based management tips
- Assesses severity
- Offers to schedule clinician follow-up if needed

**Clinician Alert**: Low-priority notification with AI-suggested management plan

#### Scenario B: Success Celebration
**Patient logs**: Weight entry showing 15 lb milestone

**AI Response**:
- Celebrates achievement
- Reinforces positive behaviors
- Suggests next goals
- Updates clinician dashboard with success metric

---

## 👤 Patient 2: <PERSON>
**Age**: 55 | **Status**: High Risk | **Medication**: Mounjaro 5mg

### Background
- Type 2 diabetes, hypertension
- Started treatment 6 weeks ago
- History of medication non-adherence

### Demo Scenarios

#### Scenario A: Missed Dose Alert
**System detects**: No medication log for 5 days

**AI Actions**:
1. Sends gentle reminder to patient
2. Escalates to clinician after 24 hours
3. Suggests intervention strategies
4. Flags for priority follow-up

#### Scenario B: Concerning Symptom Report
**Patient message**: "I've been having chest tightness when walking up stairs"

**AI Response**:
- Immediate high-priority clinician alert
- Advises patient to seek immediate care
- Documents interaction for clinical record
- Prepares urgent care referral template

---

## 👤 Patient 3: Sarah Kim
**Age**: 28 | **Status**: New Patient | **Medication**: Considering options

### Background
- Initial consultation scheduled
- BMI 32, no comorbidities
- Interested in medication options

### Demo Scenarios

#### Scenario A: Pre-Appointment Preparation
**Patient message**: "What should I expect at my first appointment? What documents do I need?"

**AI Response**:
- Provides comprehensive preparation checklist
- Explains appointment flow
- Gathers preliminary health information
- Sends forms for pre-completion

#### Scenario B: Medication Information Request
**Patient message**: "Can you tell me the differences between Wegovy and Mounjaro? Which is better for someone like me?"

**AI Response** (RAG-powered):
- Compares medications objectively
- References clinic-specific protocols
- Notes that personalized recommendation requires clinical assessment
- Offers educational materials

---

## 👤 Patient 4: Robert Chen
**Age**: 61 | **Status**: Plateau | **Medication**: Ozempic 1mg

### Background
- Weight loss plateau for 6 weeks
- Previously lost 28 lbs
- Frustrated with lack of progress

### Demo Scenarios

#### Scenario A: Plateau Frustration
**Patient message**: "I haven't lost any weight in 6 weeks. Is the medication not working anymore?"

**AI Response**:
- Validates frustration
- Explains plateau physiology
- Suggests evidence-based strategies
- Recommends comprehensive review appointment
- Flags for clinician attention

#### Scenario B: Dosage Adjustment Request
**Patient message**: "I think I need a higher dose. Can we increase my Ozempic?"

**AI Response**:
- Acknowledges request
- Explains dosage adjustment protocols
- Creates medication review task for clinician
- Schedules appropriate appointment type

---

## 🎭 Multi-Patient Scenarios

### Morning Triage Demonstration
Show how AI helps Dr. Chen prioritize her morning:

1. **Marcus Johnson**: Chest tightness → Urgent flag
2. **Emma Thompson**: Mild nausea → Low priority with AI management plan
3. **Robert Chen**: Plateau frustration → Medium priority, needs encouragement
4. **Sarah Kim**: Pre-appointment questions → Handled entirely by AI

### Population Health Alert
"AI has identified 3 patients showing early weight regain patterns. Recommended intervention: Group coaching session on maintaining long-term success."

---

## 💬 Sample Clinician Queries

### Quick Information Retrieval
**Clinician**: "What's our clinic's protocol for patients with BMI over 40?"

**AI Response** (RAG): 
"For patients with BMI >40, our clinic protocol includes:
1. Comprehensive metabolic panel
2. Cardiology clearance if age >50
3. Start with lowest medication dose
4. Weekly monitoring for first month
5. Consider bariatric surgery referral

[Shows source: Clinic Treatment Guidelines, Page 12]"

### Complex Clinical Question
**Clinician**: "Patient on Wegovy wants to get pregnant. What's the recommended approach?"

**AI Response**:
"For Wegovy and pregnancy planning:
- Discontinue Wegovy at least 2 months before conception
- Current weight loss may improve fertility
- Switch to lifestyle management during pregnancy planning
- Document pregnancy planning in clinical notes
- Schedule preconception counseling

Would you like me to prepare patient education materials on this topic?"

---

## 📊 Demo Metrics Dashboard

### Real-Time Metrics to Display
- **Active Patients**: 342
- **AI Interactions Today**: 127
- **Avg Response Time**: 1.2 seconds
- **Tasks Automated**: 89
- **Clinician Time Saved**: 4.5 hours

### Success Stories Panel
- "15% average weight loss across all patients"
- "92% medication adherence rate"
- "4.8/5 patient satisfaction score"
- "68 NPS (Net Promoter Score)"

---

## 🚨 Edge Cases to Demonstrate

### 1. AI Uncertainty
**Patient**: "I took double my dose by accident, what should I do?"

**AI Response**: "This is a medication safety issue that requires immediate clinical guidance. I've alerted Dr. Chen and marked this as urgent. Please don't take any more medication until you speak with the clinical team. If you experience any severe symptoms, please call 911."

### 2. Complex Multi-Issue
**Patient**: "I'm traveling to Japan next month, how do I manage my injections with the time change? Also, can I eat sushi on this medication?"

**AI Response**: Handles both the practical (injection timing) and dietary question seamlessly.

### 3. Emotional Support
**Patient**: "I feel like giving up. This is too hard."

**AI Response**: Provides empathetic support while flagging for human intervention and suggesting mental health resources.

---

## 🎯 Key Demo Takeaways

Each scenario demonstrates:
1. **Intelligent Triage**: AI knows when to escalate vs. handle independently
2. **Personalized Responses**: Context-aware based on patient history
3. **Clinical Integration**: Seamless workflow between AI and clinician
4. **Evidence-Based**: All recommendations grounded in clinical guidelines
5. **Efficiency**: Most issues resolved without clinician intervention