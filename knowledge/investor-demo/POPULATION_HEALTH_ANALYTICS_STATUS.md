# Population Health Analytics Dashboard - Live vs Mocked Data

## Overview
This document details which components of the Population Health Analytics Dashboard use real data from the database versus mocked/simulated data.

## 🟢 LIVE DATA (Real Database Queries)

### 1. Population Overview Section
- **Total Patients**: ✅ Real count from database
- **Active Patients**: ✅ Real (based on chat messages in last 30 days)
- **New Patients This Month**: ✅ Real (based on created_at timestamps)

### 2. Weight Loss Metrics
- **Average Weight Loss**: ✅ Real calculations from WeightLog table
  - Compares first and last weight entries per patient
  - Calculates month-over-month trends
  - Uses actual patient weight data

### 3. Adherence Rate
- **Appointment Attendance**: ✅ Real data from Appointments table
  - Calculates completed vs scheduled appointments
  - Month-over-month comparison
  - Based on actual appointment statuses

### 4. Side Effect Rate
- **Side Effect Reports**: ✅ Real data from SideEffectReport table
  - Counts unique patients reporting side effects
  - Calculates percentage of patient population
  - Month-over-month trends

### 5. Risk Stratification
- **Patient Risk Assessment**: ✅ Real calculations based on:
  - Days since last interaction (ChatMessage table)
  - Weight trends (WeightLog table)
  - Side effect reports (SideEffectReport table)  
  - Missed appointments (Appointment table)
- **Risk Distribution**: ✅ Real count of patients per risk level
- **Top Risk Factors**: ✅ Real aggregation of identified risk factors

### 6. Treatment Analytics
- **Medication Usage**: ✅ Real data from MedicationRequest table
  - Patient count per medication
  - Side effects per medication (joined with SideEffectReport)

### 7. Optimization Insights  
- **Engagement Gaps**: ✅ Real count of inactive patients
- **Unresolved Side Effects**: ✅ Real count from database
- **No-Show Appointments**: ✅ Real count from database

## 🔴 MOCKED DATA (Simulated/Hardcoded)

### 1. Satisfaction Score
```python
current_score = 85.5  # Mock data
previous_score = 82.3
```
- **Reason**: No patient survey system implemented yet

### 2. Treatment Analytics - Per Medication Metrics
```python
efficacy = np.random.uniform(75, 95)
discontinuation = np.random.uniform(5, 20)
success = np.random.uniform(70, 90)
```
- **Reason**: Would require long-term outcome tracking not yet implemented

### 3. Treatment Phase Distribution
```python
phase_distribution = {
    "pre_treatment": 15,
    "active": 65,
    "maintenance": 15,
    "discontinued": 5
}
```
- **Reason**: No treatment phase tracking in current schema

### 4. Average Time to Goal
```python
average_time_to_goal=84.5  # Mock: 84.5 days average
```
- **Reason**: No goal tracking system implemented

### 5. Predictive Analytics
- **Weight Loss Predictions**: 🔴 Random values
- **Success Probability**: 🔴 Random values  
- **Discontinuation Risk**: 🔴 Random values
- **Overall Success Rate**: 🔴 Fixed at 78.5%
- **Intervention Impact**: 🔴 Hardcoded percentages

### 6. Adherence Percentage (in Risk Assessment)
```python
adherence = max(20, 100 - (risk_score * 10))
```
- **Reason**: Simplified calculation, not based on actual medication adherence

### 7. Efficiency Score
```python
efficiency_score=82.5  # Mock score
```
- **Reason**: No efficiency tracking metrics implemented

### 8. Documentation Insight
- Always shows "3.5 hours/week with AI notes" potential savings
- **Reason**: No actual time tracking for documentation

## Summary

### Percentage Breakdown
- **~65% Live Data**: Core operational metrics, patient counts, risk assessments
- **~35% Mocked Data**: Predictive analytics, satisfaction scores, long-term outcomes

### Why This Mix?
1. **Live data** focuses on immediately trackable metrics that PulseTrack already collects
2. **Mocked data** represents features that would require:
   - Patient surveys (satisfaction)
   - Long-term outcome tracking (efficacy, time to goal)
   - ML models (predictions)
   - Time tracking systems (efficiency)

### For Investor Demo
The dashboard effectively demonstrates:
- Real operational insights from actual patient data
- Vision for predictive capabilities (mocked but realistic)
- Immediate value (live data) + future potential (mocked predictions)

### Making More Data "Live"
To convert mocked data to live data, you would need:
1. **Patient Survey System**: For satisfaction scores
2. **Outcome Tracking**: Define and track treatment goals
3. **ML Pipeline**: Train models on historical data for predictions
4. **Time Tracking**: Implement clinician activity logging
5. **Treatment Phases**: Add phase field to patient records