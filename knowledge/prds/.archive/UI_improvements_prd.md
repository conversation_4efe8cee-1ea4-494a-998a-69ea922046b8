### 📘 `UI_improvements_prd.md`
**Project Name:** Clinician Dashboard UI Modernization
**Owner:** <PERSON><PERSON><PERSON>
**Last Updated:** 2025-04-03

---

## 🧠 Purpose

To modernize the Clinician Dashboard UI/UX, focusing on aesthetics and component structure that evoke intelligence, clarity, calm control, and clinical trust. The goal is an AI-first interface that reduces cognitive load, improves usability, and feels like a supportive assistant for clinicians, based on the design principles outlined in `.xgen/.seed/UI_notes.txt`.

---

## 🛡️ Core Features

| Feature | Description |
|--------|-------------|
| **Header Bar** | Top navigation bar containing: SynapseDx logo (compact), App name, Breadcrumb navigation or universal search, Profile dropdown (with role switch, logout), Notification bell, Dark mode toggle. |
| **Dashboard Title Section** | Prominent section below the header displaying a personalized welcome message (e.g., `👩‍⚕️ Welcome back, Dr. [Name]`) and a brief summary prompt (e.g., “Here's what needs your attention today”). |
| **AI Summary Strip** | A distinct banner/strip near the top of the page displaying concise, high-level AI-driven insights or alerts (e.g., "🧠 AI Insights: 2 patients show early signs of medication resistance."). Designed as a `highlight pill` or `info banner`. |
| **Main Grid Layout** | The primary content area organized into a responsive 3-4 column grid structure. This grid will contain various "Smart Cards". |
| **Smart Cards** | Modular, visually distinct cards within the main grid for key information categories: Pending Tasks (e.g., Medication Requests), Side Effect Reports (with flags/avatars), Today's Appointments, Patient Alerts (summarized insights), and Quick Actions (buttons). Cards feature rounded corners (2xl), soft drop shadows, compact titles, badges/tags, and optional icons. |
| **Recent Activity Feed** | A section displaying a vertical timeline of recent system events or user actions (e.g., "Dr. Kim updated meds for John Doe", "AI flagged duplicate prescription") with timestamps and potentially avatars. Styled as a subtle card with dividers. |
| **Footer / Status Bar (Optional)** | A minimal bar at the bottom displaying information like EHR sync status, last AI model update time, and a support/help link. |

---

## ✅ Acceptance Criteria

- ✅ Header bar is implemented at the top of the dashboard containing all specified elements: logo, app name, search/breadcrumbs, profile dropdown, notification icon, and dark mode toggle.
- ✅ A personalized welcome message and introductory subheading are displayed below the header.
- ✅ The AI Summary Strip is present, styled distinctively (e.g., highlight pill/banner), and positioned prominently near the top.
- ✅ The main content area utilizes a responsive grid layout (adapting between 3-4 columns based on screen size).
- ✅ Smart Cards for Pending Tasks, Side Effect Reports, Appointments, Alerts, and Quick Actions are implemented within the grid.
- ✅ Smart Cards adhere to the specified visual style: rounded corners (2xl), soft drop shadow, compact title, visual indicators (badges/tags), and optional top-left icon.
- ✅ A Recent Activity Feed section is present, displaying items in a vertical timeline format with timestamps.
- ✅ The overall UI adheres to the "Calm Clarity" principle using soft whites, pale neutrals, light accent colors (#1A73E8, #4CAF50), and a clean sans-serif font (Inter or SF Pro).
- ✅ UI components (Buttons, Cards, Icons) match the specified styles: rounded/pill buttons, subtle card hover effects, minimalistic outline icons (Lucide or Heroicons).
- ✅ The specified color theme (Primary: #1A73E8, Secondary: #4CAF50, Background: #F8FAFC or #FFFFFF) is consistently applied.

---

## 🚫 Non-Goals

- Implementation of the backend logic required to populate the AI Summary Strip, Smart Cards, or Activity Feed with real data.
- Detailed implementation of the Profile page accessible via the header dropdown (only the dropdown itself is in scope).
- Implementation of the actual dark mode theme switching logic (only the UI toggle element is required).
- Implementation of specific EHR sync logic (only displaying status if the optional footer is included).
- Building the optional design enhancements (Sidebar, patient hover cards, mini-stats, AI-enhanced search) unless covered by separate, subsequent PRDs.
- Defining specific user roles or permissions related to the profile dropdown.

---

## 👥 Stakeholders

- **Clinicians** (Primary End Users)
- **Development Team** (Implementers)
- **Product Owner/Manager** (Requirements Definition)

---

## 🧹 Constraints

- Adherence to Design Principles: Calm Clarity, Cognitive Load Reduction, Trust & Control, Modularity.
- Must use specified font families: Inter or SF Pro.
- Must use specified icon sets: Lucide or Heroicons.
- Must use the specified color palette: Primary #1A73E8, Secondary #4CAF50, Background #F8FAFC/#FFFFFF.
- The core layout must be a responsive grid containing modular cards.
- UI component styling must follow the guidelines in the `UI_notes.txt` (rounded corners, shadows, button styles, etc.).

---

## 🔼 Priority

| Area | Priority |
|------|----------|
| Header Bar | High |
| Main Grid Layout | High |
| Smart Cards (Core Set: Tasks, Reports, Alerts, Actions) | High |
| AI Summary Strip | High |
| Dashboard Title Section | Medium |
| Recent Activity Feed | Medium |
| Acceptance Criteria related to Core Styling (Colors, Fonts, Icons, Card Style) | High |
| Footer / Status Bar (Optional) | Low |