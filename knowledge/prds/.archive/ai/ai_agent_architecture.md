### 📝 `atomic_prd.md`
**Feature:** `AI Chat Agent Internal Architecture`
**Parent PRD:** master_prd.md, api/chat_agent.md
**Last Updated:** 2025-03-31

---

## 🎯 Purpose

Define the high-level internal architecture, components, and processing flow for the AI Chat Agent responsible for patient interactions (triage, Q&A) within Codename Pulsetrack. This complements the `api/chat_agent.md` PRD which defines the external API interface.

---

## 🏗️ Core Components & Flow

```mermaid
graph TD
    A[Patient Message via API] --> B(Input Processing & Validation);
    B --> C{Context Retrieval};
    C --> D[Patient Data (DB)];
    C --> E[Clinic-Specific Info (Vector DB / Files)];
    C --> F[Conversation History (DB)];
    G[System Prompt Template] --> H(Prompt Generation);
    C --> H;
    H --> I{LLM Interaction};
    I --> J[External LLM API (e.g., OpenAI)];
    J --> K(Response Processing & Guardrails);
    K --> L{Output Formatting};
    L --> M(Update Conversation History);
    M --> N[Chat History DB];
    L --> O[Response via API];

    subgraph Context Sources
        D; E; F;
    end

    subgraph Core Agent Logic
        B; C; H; K; L; M;
    end
```

1.  **Input Processing & Validation (B):** Receives message from `chat_agent` API. Performs basic validation (length, format). Identifies user ID.
2.  **Context Retrieval (C):** Gathers relevant information to inform the LLM response:
    *   **Patient Data (D):** Retrieves relevant, *minimal necessary* patient profile details (e.g., name, maybe recent weight trend - *carefully consider PHI exposure*). Access via internal API call or direct DB query.
    *   **Clinic-Specific Info (E):** Retrieves relevant clinic operational details, FAQs, medication info, etc. Likely implemented using **Retrieval-Augmented Generation (RAG)** with a vector database (e.g., pgvector in PostgreSQL, dedicated vector DB) containing embedded clinic documents.
    *   **Conversation History (F):** Fetches recent turns of the current conversation for context.
3.  **Prompt Generation (H):** Constructs the final prompt for the LLM using a predefined **System Prompt Template (G)**, the retrieved context (patient, clinic, history), and the user's latest message. The system prompt must heavily emphasize *not* providing medical advice or diagnosis.
4.  **LLM Interaction (I):** Sends the generated prompt to the chosen external **LLM API (J)** (e.g., OpenAI GPT-4/3.5, Anthropic Claude). Handles API call, authentication, and potential errors.
5.  **Response Processing & Guardrails (K):** Receives the raw response from the LLM. Applies safety guardrails:
    *   Checks for harmful content.
    *   **Crucially: Checks for and attempts to remove or rephrase any statements resembling medical advice or diagnosis.** This might involve secondary checks or specific prompting techniques.
    *   Logs any guardrail triggers.
6.  **Output Formatting (L):** Formats the processed response for display to the user (e.g., plain text, basic markdown).
7.  **Update Conversation History (M):** Stores the user message and the final agent response in the **Chat History DB (N)**.
8.  **Response via API (O):** Returns the formatted, safe response via the `chat_agent` API.

---

## ✅ Acceptance Criteria

- ✅ The agent follows the defined processing flow for each incoming message.
- ✅ Context retrieval mechanisms (Patient Data, RAG for Clinic Info, History) are functional.
- ✅ Prompts are generated dynamically based on context and user input, incorporating safety instructions.
- ✅ Interaction with the external LLM API is successful.
- ✅ Response guardrails effectively identify and mitigate prohibited content (especially medical advice).
- ✅ Conversation history is accurately stored.
- ✅ Final response is returned via the API.

---

## ⛓️ Constraints

- **LLM Choice:** Specific external LLM provider/model to be determined.
- **RAG Implementation:** Requires setting up document embedding, vector storage (e.g., pgvector), and retrieval logic.
- **Context Security:** Strict controls needed to ensure only necessary and authorized patient data is retrieved and included in prompts. Avoid leaking unnecessary PHI to the LLM.
- **Guardrail Effectiveness:** Preventing medical advice is critical and challenging. Requires careful prompt engineering and potentially multiple layers of checks. May not be 100% foolproof.
- **State Management:** Conversation history provides state; the agent core logic itself aims to be stateless per request.
- **Latency:** Multiple steps (context retrieval, RAG, LLM call, processing) contribute to latency. Needs to meet performance expectations outlined in `constraints/performance.md`.
- **Cost:** LLM API calls incur costs. Monitor usage.

---

## 🔗 Dependencies

- Relies on `api/chat_agent.md` for the external interface.
- Relies on PostgreSQL database for Patient Data, Conversation History, and potentially Vector Storage (pgvector).
- Relies on external LLM Provider API.
- Relies on internal mechanisms or APIs for accessing patient/clinic data.
- Requires libraries for LLM interaction, potentially vector DB interaction (e.g., LangChain, LlamaIndex - optional but helpful).