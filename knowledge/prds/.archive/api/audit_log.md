### 📝 `atomic_prd.md`
**Feature:** `Audit Logging API & Mechanism`
**Parent PRD:** master_prd.md
**Last Updated:** 2025-03-31

---

## 🎯 Purpose

Define the requirements for a comprehensive audit logging system within Codename Pulsetrack. This system is critical for security monitoring, compliance (HIPAA/GDPR audit trails), debugging, and maintaining traceability of significant actions within the application.

---

## ✨ Features / Logged Events

| Event Category | Example Events | Key Data Points |
|----------------|----------------|-----------------|
| **Authentication** | Patient Login (Magic Code Validated), Clinician Login (Firebase Verified), Login Failure, Logout | Timestamp, User ID (Patient/Clinician), Source IP (optional), Success/Failure, Failure Reason |
| **Data Access (PHI/PII)** | Patient Record Viewed (by Clinician), Weight History Viewed, Med Requests Viewed, Side Effects Viewed, Profile Viewed | Timestamp, Actor User ID (Clinician), Target User ID (Patient), Resource Type, Resource ID |
| **Data Modification (PHI/PII)** | Patient Profile Updated, Weight Logged, Med Request Submitted, Side Effect Submitted | Timestamp, Actor User ID (Patient/Clinician), Target User ID (Patient), Resource Type, Resource ID, Old Value (optional), New Value (or summary) |
| **Clinician Actions** | Magic Code Generated, Med Request Status Updated (Future), Side Effect Status Updated (Future) | Timestamp, Actor User ID (Clinician), Action Details (e.g., generated code ID, request ID + new status) |
| **AI Interaction** | Chat Message Sent (Patient), Chat Response Received (Agent), Prompt details used for generation (if feasible) | Timestamp, User ID (Patient), Conversation ID, Message Content (or hash/summary), Agent Response (or hash/summary), Prompt Metadata (for traceability) |
| **Admin Actions (Future)** | User Onboarded, Clinic Config Changed | Timestamp, Admin User ID, Action Details |

---

## ✅ Acceptance Criteria

- ✅ A standardized logging mechanism (e.g., dedicated logging function/service within FastAPI) is implemented.
- ✅ All critical events listed above (and others identified during development) trigger an audit log entry.
- ✅ Audit log entries contain a consistent set of core fields: timestamp (UTC), actor user ID/type, action performed, target resource type/ID (if applicable), source IP address (optional, consider privacy), outcome (success/failure).
- ✅ Log entries are stored securely and reliably in a dedicated table within the PostgreSQL database (e.g., `audit_logs` table).
- ✅ Log storage mechanism prevents tampering (e.g., append-only logic if possible, restricted DB permissions).
- ✅ Log format facilitates querying and analysis (e.g., structured JSON or well-defined columns).
- ✅ Sensitive data within log messages (e.g., full chat content) should be considered carefully (hash, summarize, or store reference ID only if full content isn't needed for audit).

---

## ⛓️ Constraints

- **Storage:** Audit logs can grow significantly. Plan for storage capacity and potential archiving/rotation strategies (Post-MVP). PostgreSQL table partitioning might be useful later.
- **Performance:** Audit logging should have minimal performance impact on primary application requests. Consider asynchronous logging if necessary.
- **Security:** Access to raw audit logs must be strictly controlled.
- **Compliance:** Log content and retention period must meet HIPAA/GDPR requirements.
- **Queryability:** The log structure must allow efficient querying for specific users, actions, or time ranges.

---

## 🔗 Dependencies

- Relies on the PostgreSQL database for log storage.
- Integrated into various API endpoints and service logic throughout the backend (FastAPI).
- May require specific logging libraries (e.g., `loguru`, standard `logging` configured for structure).
- Consumed indirectly by security monitoring tools or compliance auditors.