### 📝 `atomic_prd.md`
**Feature:** `Authentication API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for authenticating both patients (via magic codes) and clinicians (via Firebase Authentication). These endpoints are critical for securing access to the application's features and data.

---

## ✨ Features

| Feature | Description | Endpoint Idea |
|---------|-------------|---------------|
| Patient Magic Code Validation | Validates a magic code provided by a patient during initial access. Returns a session token upon success. | `POST /api/auth/validate-code` |
| Clinician Firebase Login | Accepts a Firebase ID token from an authenticated clinician (obtained via Firebase client-side SDK). Validates the token and returns a session token. | `POST /api/auth/clinician-login` |
| Session Management | (Implicit) Manage session validity/tokens after successful authentication. | N/A (Handled via tokens/middleware) |

---

## ✅ Acceptance Criteria

**Patient Magic Code Validation:**
- ✅ API accepts a magic code.
- ✅ API successfully validates a correct, active magic code against stored codes.
- ✅ API returns a session token (e.g., JWT) upon successful validation.
- ✅ API returns an appropriate error (e.g., 401/403) for invalid, expired, or already used codes.
- ✅ Validated codes are marked as used or linked to the patient record appropriately.

**Clinician Firebase Login:**
- ✅ API accepts a Firebase ID token in the Authorization header (Bearer token) or request body.
- ✅ API successfully validates the Firebase ID token using the Firebase Admin SDK.
- ✅ API verifies the clinician's email (from the validated Firebase token) belongs to a pre-approved clinic domain (or checks against a clinician table in PostgreSQL).
- ✅ API returns a session token (e.g., JWT) upon successful validation and verification.
- ✅ API returns an appropriate error (e.g., 401/403) for invalid tokens or unauthorized email domains.

---

## ⛓️ Constraints

- **Dependency:** Relies heavily on Firebase Authentication for clinician identity verification.
- **Security:** Magic codes must be securely generated, stored, and invalidated. Session tokens must be secure (e.g., JWT with proper signing, short expiry). Standard security practices (rate limiting, input validation) must be applied.
- **Compliance:** Authentication mechanisms must align with HIPAA/GDPR requirements regarding user identification and access control.
- **Clinician Onboarding:** Assumes clinicians are pre-onboarded within Firebase Authentication by an administrator.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (for magic code validation).
- Consumed by Clinician Web Portal Frontend (for Firebase login).
- Relies on Firebase Admin SDK for token validation.
- Relies on PostgreSQL database for storing/validating magic codes and potentially linking them to patient profiles.