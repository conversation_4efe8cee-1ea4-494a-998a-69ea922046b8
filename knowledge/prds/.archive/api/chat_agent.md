### 📝 `atomic_prd.md`
**Feature:** `Chat Agent API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for facilitating interaction between authenticated patients and a clinic-specific chat agent. The agent handles tasks like triage, answering questions based on clinic data, etc.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Send Message to Agent | Allows an authenticated patient to send a message to the chat agent. | `POST /api/patients/me/chat` | Patient |
| Get Chat History | Allows an authenticated patient to retrieve their chat history with the agent. | `GET /api/patients/me/chat` | Patient |
| Get Chat History (Clinician) | (Potential Future/MVP+) Allows an authenticated clinician to view a patient's chat history. | `GET /api/clinicians/patients/{patientId}/chat` | Clinician |

---

## ✅ Acceptance Criteria

**Send Message to Agent:**
- ✅ API accepts a message content from an authenticated patient.
- ✅ API forwards the message to the underlying chat agent/service.
- ✅ API receives a response from the chat agent/service.
- ✅ API returns the agent's response to the patient.
- ✅ API stores the conversation turn (patient message + agent response) in the database.
- ✅ API returns 401/403 if the user is not an authenticated patient.

**Get Chat History:**
- ✅ API returns the historical chat messages for the authenticated patient.
- ✅ API allows pagination for long histories.
- ✅ API returns 401/403 if the user is not an authenticated patient.

**(MVP+ Get Chat History - Clinician):**
- ✅ API accepts a patient ID.
- ✅ API returns the chat history for the specified patient.
- ✅ API returns 401/403 if the user is not an authenticated clinician.
- ✅ API returns 404 if the patient ID does not exist or is not associated with the clinician.

---

## ⛓️ Constraints

- **External Dependency:** Relies on an external chat agent service or model (e.g., OpenAI API, custom model). The specific integration needs definition.
- **Clinic-Specific Data:** The chat agent requires access to and training on clinic-specific data for relevant responses. The mechanism for providing this data needs definition.
- **No Clinical Advice:** The chat agent must be strictly programmed *not* to provide medical diagnoses or clinical guidance. Clear disclaimers are essential in the chat interface.
- **Conversation State:** Decide if the API needs to manage conversation state or if the underlying chat service handles it.
- **Cost/Rate Limiting:** Interactions with external AI services may incur costs and have rate limits that need management.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (Send/Display Messages).
- (Potentially) Consumed by Clinician Web Portal Frontend (View History).
- Relies on an external Chat Agent Service/Model.
- Relies on a data source for clinic-specific information.
- Relies on the database for storing chat history.
- Relies on Authentication API/middleware for user authorization.