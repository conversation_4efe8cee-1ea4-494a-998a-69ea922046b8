### 🏢 `atomic_prd.md`
**Feature:** `Chat Multi-Tenancy & Access Control API`
**Parent PRD:** master_chat_prd.md
**Last Updated:** 2025-04-05

---

## 🎯 Purpose

To define the backend data models, relationships, and API logic required to support the multi-tenant architecture for the chatbot feature. This includes managing relationships between users (Patients, Clinicians, Admins), Clinics, and ensuring appropriate role-based access control (RBAC) for different frontends and API endpoints.

---

## ✨ Features

| Feature                          | Description                                                                                                                                                           |
| -------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| User Roles                       | Define distinct user roles (e.g., Patient, Clinician, Clinic Administrator, System Administrator).                                                                    |
| Clinic Data Model                | Data model to represent a Clinic entity, including its associated knowledge base, chatbot instance, etc.                                                              |
| Clinician-Clinic Relationship    | Data model and logic to support a many-to-many or one-to-many relationship where a Clinician can be associated with one or more Clinics.                               |
| Patient-Clinic Relationship      | Data model and logic to support a relationship where a Patient is associated with one or more Clinics (likely via their Clinician relationship).                        |
| Patient-Clinician Relationship   | Data model and logic to support a many-to-many relationship where a Patient can be seen by one or more Clinicians.                                                    |
| RBAC for Patient Frontend        | Logic to ensure Patients logging in are directed to the correct clinic-specific frontend and can only access data/chatbot for their associated clinic(s).               |
| RBAC for Clinician Frontend      | Logic to allow Clinicians to view/manage data for all clinics they are associated with.                                                                               |
| RBAC for Admin Frontend          | Logic to allow Clinic Administrators to manage context/settings for their specific clinic(s).                                                                         |
| RBAC for API Endpoints           | Middleware or dependency logic to enforce role-based access control on relevant API endpoints (e.g., chat, data ingestion, patient management).                       |
| User/Clinic Management API (Admin) | Potential API endpoints for System Administrators to manage users, clinics, and their relationships (if not handled by existing auth provider like Clerk/Firebase). |

---

## ✅ Acceptance Criteria

- ✅ Database models accurately represent Users, Clinics, and their relationships (Patient-Clinician, Clinician-Clinic).
- ✅ API logic correctly enforces that Patients can only access information (including chatbot) related to their associated clinic(s).
- ✅ API logic correctly allows Clinicians to access information for all clinics they are associated with.
- ✅ API logic correctly allows Clinic Administrators to manage settings for their specific clinic(s).
- ✅ Access control mechanisms (e.g., API dependencies, middleware) effectively restrict access to endpoints based on user role and clinic associations.
- ✅ Patients logging in are correctly identified and associated with their clinic(s) for frontend routing.
- ✅ Clinicians logging in can retrieve a list of clinics they are associated with.

---

## ⛓️ Constraints

- The chosen relationship model (e.g., many-to-many association tables) must be efficient for querying access rights.
- RBAC logic must be robust and consistently applied across all relevant API endpoints and potentially frontend routing.
- Integration with the existing authentication system (e.g., Clerk, Firebase Auth, internal JWT) is crucial for identifying users and their roles/metadata.
- Data models must be designed with privacy regulations in mind (e.g., minimizing data exposure).
- Scalability needs to be considered as the number of users, clinics, and relationships grows.

---

## 🔗 Dependencies

- **Authentication Service:** (e.g., Clerk, Firebase Auth, Internal JWT) Provides user identity and potentially roles/metadata.
- **Database:** Stores the user, clinic, and relationship data.
- **Backend API Framework:** Provides middleware/dependency injection for implementing RBAC.
- **All Frontend Applications:** Rely on the backend to enforce access control and provide necessary context (e.g., associated clinic ID for patient).
- **All other Chat-related API PRDs:** Rely on this service for authorization checks (e.g., Can this patient access this clinic's chatbot? Can this admin ingest data for this clinic?).