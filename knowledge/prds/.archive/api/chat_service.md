### 🤖 `atomic_prd.md`
**Feature:** `Clinic-Specific Chatbot Service API`
**Parent PRD:** master_chat_prd.md
**Last Updated:** 2025-04-05

---

## 🎯 Purpose

To define the backend service responsible for creating, managing, and serving clinic-specific chatbot instances. This service utilizes the knowledge base embeddings to provide contextually relevant responses to patient queries via an API endpoint.

---

## ✨ Features

| Feature                       | Description                                                                                                                               |
| ----------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| Chatbot Instance Management   | Ability to spin up and manage dedicated chatbot instances, one per clinic.                                                                |
| Knowledge Base Integration    | Each chatbot instance is configured to access and query the specific embeddings for its associated clinic stored in the vector database.    |
| Chat API Endpoint             | An API endpoint (e.g., POST /api/v1/chat/{clinicId}) for frontends to send user messages and receive chatbot responses.                     |
| Contextual Response Generation | Utilizes Retrieval-Augmented Generation (RAG) or similar techniques with the clinic's knowledge base to generate informed, relevant answers. |
| Disclaimer Handling           | Mechanism to ensure chatbot responses include or are preceded by a disclaimer stating it does not provide clinical advice.                |

---

## ✅ Acceptance Criteria

- ✅ A dedicated chatbot instance can be created and associated with a specific clinic ID.
- ✅ The chatbot instance correctly retrieves and utilizes embeddings specific to its associated clinic from the vector database.
- ✅ The Chat API endpoint successfully receives messages from authenticated users (patients associated with the clinic).
- ✅ The Chat API endpoint returns responses generated by the chatbot, based on the clinic's knowledge base.
- ✅ Chatbot responses accurately reflect the information contained within the scraped and supplemental context for that clinic.
- ✅ Chatbot responses consistently include a disclaimer about not providing clinical advice.
- ✅ The API endpoint handles errors gracefully (e.g., invalid clinic ID, chatbot instance unavailable, knowledge base query failure).
- ✅ Access to the chat endpoint is restricted to authenticated patients associated with the specified clinic ID.

---

## ⛓️ Constraints

- Chatbot responses must not provide clinical advice.
- Response generation latency should be minimized for a good user experience.
- Scalability needs to be considered to handle multiple concurrent chatbot instances and user interactions.
- Requires robust integration with the vector database for efficient similarity searches.
- LLM choice for response generation impacts quality, cost, and latency.
- Must adhere to data privacy regulations when handling user queries and potentially sensitive information retrieved from the knowledge base.

---

## 🔗 Dependencies

- **Chatbot Knowledge Base Management API (`api/chat_knowledge_base.md`):** Provides the embeddings used by this service.
- **Patient Frontend (`frontend/patient/chat_integration.md`):** Consumes the Chat API endpoint provided by this service.
- **Multi-Tenancy & Access Control Service (`api/chat_multi_tenancy.md`):** Provides information on patient-clinic associations for authorization.
- **Vector Database:** Used to retrieve relevant context based on user queries.
- **LLM Service:** Used for generating the final chatbot response based on retrieved context.
- **Authentication/Authorization Service:** Required to verify patient identity and clinic association.