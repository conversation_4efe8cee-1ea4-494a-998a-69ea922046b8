### 📝 `atomic_prd.md`
**Feature:** `Clinician Portal API Endpoints`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define specific API endpoints tailored to the needs of the Clinician Web Portal. These endpoints facilitate clinician-specific workflows like viewing patient lists, generating access codes, and potentially accessing aggregated dashboard views.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Get Patient List | Allows an authenticated clinician to retrieve a list of patients associated with them or their clinic. | `GET /api/clinicians/patients` | Clinician |
| Generate Patient Access Code | Allows an authenticated clinician to generate a new, unique magic access code for onboarding a patient. | `POST /api/clinicians/access-codes` | Clinician |
| Get Dashboard Summary | (Potential) Allows an authenticated clinician to retrieve summary data (e.g., count of pending med requests, count of major side effect reports). | `GET /api/clinicians/dashboard` | Clinician |

*Note: Viewing specific patient details, med requests, side effects, etc., are covered by endpoints defined in other API PRDs (e.g., `patient_data.md`, `medication.md`, `side_effects.md`) but consumed by the Clinician Portal.*

---

## ✅ Acceptance Criteria

**Get Patient List:**
- ✅ API returns a list of patients associated with the authenticated clinician.
- ✅ Each patient entry includes minimal necessary info for a list view (e.g., ID, name, maybe last activity date).
- ✅ API allows pagination for large lists.
- ✅ API returns 401/403 if the user is not an authenticated clinician.

**Generate Patient Access Code:**
- ✅ API generates a unique, secure magic access code.
- ✅ API stores the code securely with an expiry or single-use flag.
- ✅ API returns the generated code to the clinician.
- ✅ API returns 401/403 if the user is not an authenticated clinician.
- ✅ (Optional) API might accept basic patient info (name/email) to pre-associate the code.

**(Potential Get Dashboard Summary):**
- ✅ API returns aggregated counts (e.g., pending med requests, major side effects) relevant to the clinician.
- ✅ API returns 401/403 if the user is not an authenticated clinician.

---

## ⛓️ Constraints

- **Authorization:** All endpoints must be strictly limited to authenticated clinicians. Further authorization might be needed based on clinic roles (if applicable later).
- **Code Security:** Generated magic codes must be cryptographically secure, unique, have a limited lifespan or be single-use, and stored securely.
- **Data Aggregation:** Dashboard endpoints require efficient querying across potentially multiple data types.
- **Clinician Association:** Logic must correctly associate patients, codes, requests, etc., with the requesting clinician/clinic.

---

## 🔗 Dependencies

- Consumed by Clinician Web Portal Frontend.
- Relies on the PostgreSQL database for patient lists, storing access codes, and aggregating data.
- Relies on Authentication API/middleware for user authorization.
- May implicitly rely on data managed by Patient Data, Medication, and Side Effect APIs for dashboard summaries.

- Relies on Firebase Authentication for clinician identity.

## Clinician Profile and Associations

Clinician details have been updated to include:
 • clerk_id (string, optional) for external integrations (e.g., Clerk)
 • A note explaining the conversion of clinician IDs from UUID to string
 • Documentation of the clinician_clinic_association mechanism, allowing a clinician to be linked to multiple clinics