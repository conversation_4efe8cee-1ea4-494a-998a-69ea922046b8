### 📝 `atomic_prd.md`
**Feature:** `Educational Content API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoint(s) responsible for serving static educational content to patients. This content typically includes information about clinic operations, medications, emergency contacts, etc., as provided by the clinic.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Get Educational Content | Allows an authenticated patient or clinician to retrieve the available educational content. | `GET /api/content/educational` | Patient, Clinician |
| Manage Educational Content (Clinician/Admin) | (Potential Future/MVP+) Allows authorized users to upload/update educational content. | `POST /api/admin/content/educational` <br> `PUT /api/admin/content/educational/{contentId}` | Clinician/Admin |

---

## ✅ Acceptance Criteria

**Get Educational Content:**
- ✅ API returns the configured educational content for the relevant clinic (assuming multi-tenancy later, or global for MVP).
- ✅ Content is returned in a usable format (e.g., Markdown, HTML snippets within JSON).
- ✅ API returns 401/403 if the user is not authenticated (if required, content might be public post-auth).

**(MVP+ Manage Educational Content):**
- ✅ API allows authorized users (Admin/Clinician) to upload new content.
- ✅ API allows authorized users to update existing content.
- ✅ API handles storage of the content.
- ✅ API returns appropriate success/error messages.
- ✅ API enforces authorization.

---

## ⛓️ Constraints

- **Content Format:** Define the expected format for educational content (e.g., Markdown, plain HTML). Markdown is often flexible.
- **Content Storage:** Determine where the static content resides (e.g., files deployed with the API container, database records, separate CMS). File-based is simplest for MVP if content changes infrequently.
- **Content Management (MVP):** For MVP, content updates likely require code deployment. A management interface is a future enhancement.
- **Multi-Clinic:** For MVP (single/few clinics), content might be global. Future multi-tenancy requires associating content with specific clinics.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (to display content).
- Consumed by Clinician Web Portal Frontend (potentially to preview content).
- Relies on the chosen content storage mechanism (filesystem, database, etc.).
- Relies on Authentication API/middleware for user authorization (if content is not public post-auth).