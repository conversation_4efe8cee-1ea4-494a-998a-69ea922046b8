### 📝 `atomic_prd.md`
**Feature:** `Internal Event Bus / Asynchronous Processing`
**Parent PRD:** master_prd.md
**Last Updated:** 2025-03-31

---

## 🎯 Purpose

Define a mechanism for handling asynchronous tasks and decoupling actions within the backend system. This allows certain operations (like sending notifications or triggering complex follow-up logic) to occur without blocking the primary user request flow, improving responsiveness and enabling more complex workflows.

---

## ✨ Features / Use Cases (MVP)

| Use Case | Triggering Event | Action / Subscriber | Notes |
|----------|------------------|---------------------|-------|
| **Weight Log Reminder Check** | Potentially time-based (e.g., daily check) or triggered by `WeightLogged` event. | Notification Service | Checks if patient is due for reminder based on last log date. |
| **Side Effect Alert (Major)** | `SideEffectReported` event (with severity='major') | Notification Service / Alerting Mechanism | Triggers an alert to clinicians (e.g., via email, push notification - mechanism TBD). |
| **Post-Registration Tasks** | `PatientRegistered` event (after magic code validation) | Background Task Runner | Perform any necessary background setup for a new patient. |

---

## ✅ Acceptance Criteria

- ✅ A mechanism exists to publish events from within the FastAPI application (e.g., after a successful weight log save, side effect submission).
- ✅ A mechanism exists to subscribe to and handle these events asynchronously.
- ✅ For MVP, event handling guarantees "at-least-once" delivery (retries might be needed for critical tasks).
- ✅ Event publishing adds minimal overhead to the originating API request.
- ✅ Basic error handling and logging are implemented for event handlers.

---

## ⛓️ Constraints

- **MVP Simplicity:** Avoid overly complex distributed systems (like Kafka, RabbitMQ) for the initial MVP unless absolutely necessary.
- **Potential MVP Approaches:**
    *   **Database Triggers/Functions (PostgreSQL):** Use database features to trigger actions (e.g., insert into a notification queue table). Simple, transactional.
    *   **Background Tasks (FastAPI):** Utilize FastAPI's built-in `BackgroundTasks` for simple, fire-and-forget tasks within the same process. Not suitable for heavy or critical tasks.
    *   **Simple Queue:** Implement a basic queue using PostgreSQL (e.g., `SKIP LOCKED`) or Redis (if already needed for caching) and a separate worker process.
- **Scalability:** The chosen MVP approach should ideally allow for migration to a more robust queuing system later if needed.
- **Reliability:** Critical tasks (like major side effect alerts) need reliable handling.

---

## 🔗 Dependencies

- Integrated into various API service logic where events need to be published (e.g., Weight Tracking API, Side Effect API).
- Relies on the chosen implementation mechanism (PostgreSQL, FastAPI BackgroundTasks, Redis, etc.).
- Consumed by background workers or subscribers (e.g., a Notification Service module/process).
- Relies on PostgreSQL database if used for queuing or triggering.