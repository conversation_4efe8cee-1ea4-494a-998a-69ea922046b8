### 📝 `atomic_prd.md`
**Feature:** `Medication Management API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for handling patient medication refill requests. This allows patients to request refills for specific medications (e.g., Mounjaro pens) and makes these requests visible to clinicians in their portal.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Request Medication Refill | Allows an authenticated patient to submit a request for a medication refill. This endpoint now handles the updated medications model with the following fields: name (string, unique), description (text, optional), dosage_guidelines (text, optional), common_side_effects (text, optional), category (string, optional). | `POST /api/patients/me/medication-requests` | Patient |
| Get Medication Requests (Clinician) | Allows an authenticated clinician to retrieve a list of pending (and potentially historical) medication refill requests for their patients. Medication requests have been extended to include: dosage (string, optional), frequency (string, optional), duration (string, optional). Indexes have been added on these fields to support fast lookup. | `GET /api/clinicians/medication-requests` | Clinician |
| Update Medication Request Status (Clinician) | (Potential Future/MVP+) Allows a clinician to update the status of a request (e.g., Approved, Denied, Contact Patient). | `PUT /api/clinicians/medication-requests/{requestId}` | Clinician |

---

## ✅ Acceptance Criteria

**Request Medication Refill:**
- ✅ API accepts medication details (e.g., name, dosage - needs definition) from an authenticated patient.
- ✅ API successfully stores the refill request with patient identifier, timestamp, and initial status (e.g., 'Pending').
- ✅ API returns a success confirmation.
- ✅ API returns 401/403 if the user is not an authenticated patient.

**Get Medication Requests (Clinician):**
- ✅ API returns a list of medication refill requests for patients associated with the authenticated clinician.
- ✅ API allows filtering by status (e.g., 'Pending').
- ✅ Each request in the list includes patient identifier, medication details, request timestamp, and status.
- ✅ API returns 401/403 if the user is not an authenticated clinician.

**(MVP+ Update Medication Request Status):**
- ✅ API accepts a request ID and a new status from an authenticated clinician.
- ✅ API updates the status of the specified request in the database.
- ✅ API returns a success confirmation.
- ✅ API returns 401/403 if the user is not an authenticated clinician or not authorized for the specific request.
- ✅ API returns 404 if the request ID does not exist.

---

## ⛓️ Constraints

- **Medication Data:** Need to define how medications are identified (e.g., free text entry by patient, pre-defined list). Free text is simpler for MVP but less structured.
- **Request Status:** Define the lifecycle/statuses for a request (e.g., Pending, Approved, Denied). MVP might only need 'Pending' visibility.
- **Clinician Association:** The API must correctly associate requests with the appropriate clinicians/clinics.
- **No Dispensing/Prescribing:** The API only handles *requests*; it does not integrate with pharmacies or formal prescribing systems.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (Submit Request).
- Consumed by Clinician Web Portal Frontend (View Requests, potentially Update Status).
- Relies on the database for storing and retrieving medication request data.
- Relies on Authentication API/middleware for user authorization.