### 📝 `atomic_prd.md`
**Feature:** `Patient Data API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for managing patient profile information. This includes retrieving patient data and allowing patients to update specific fields like contact information and profile photo. It also includes endpoints for clinicians to view patient profiles.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Get Patient Profile (Self) | Allows an authenticated patient to retrieve their own profile data. | `GET /api/patients/me` | Patient |
| Update Patient Profile (Self) | Allows an authenticated patient to update their phone, email, and profile photo. May include height for BMI. | `PUT /api/patients/me` | Patient |
| Get Patient Profile (Clinician) | Allows an authenticated clinician to retrieve a specific patient's profile data. | `GET /api/clinicians/patients/{patientId}` | Clinician |

---

## ✅ Acceptance Criteria

**Get Patient Profile (Self):**
- ✅ API returns the authenticated patient's profile data (phone, email, photo, height, etc.).
- ✅ API returns 401/403 if the user is not an authenticated patient.

**Update Patient Profile (Self):**
- ✅ API accepts updated phone number, email address, and profile photo (and potentially height).
- ✅ API successfully updates the patient's record in the database.
- ✅ API validates input data format (e.g., valid email format, phone number format).
- ✅ API returns the updated profile data or a success confirmation.
- ✅ API returns 401/403 if the user is not an authenticated patient.
- ✅ API handles profile photo upload/storage appropriately.

**Get Patient Profile (Clinician):**
- ✅ API accepts a patient ID.
- ✅ API returns the specified patient's profile data.
- ✅ API returns 401/403 if the user is not an authenticated clinician.
- ✅ API returns 404 if the patient ID does not exist or is not associated with the clinician's clinic (if applicable).

---

## ⛓️ Constraints

- **Authorization:** Patients can only access/modify their own data. Clinicians can only access data for patients associated with them/their clinic.
- **Data Privacy:** All handling of Personally Identifiable Information (PII) must strictly adhere to HIPAA, GDPR, and UK regulations. Sensitive data should be encrypted at rest and in transit.
- **Validation:** Implement robust validation for all updatable fields (email format, phone number format/region).
- **Photo Storage:** Profile photo storage mechanism needs definition (e.g., S3 bucket, local storage within container - consider persistence). Access control for photos is required.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (Get/Update Self).
- Consumed by Clinician Web Portal Frontend (Get Patient).
- Relies on the database for storing and retrieving patient profile data.
- Relies on Authentication API/middleware for user authorization.
- May rely on a separate file storage service for profile photos.

---

## 📈 Patient Profile Enhancements

Patient profiles now also include:
 • photo_url (string, optional) for profile pictures
 • invited_by_clinician_id (string, optional) to track invitation source
 • associated_clinic_id (UUID, optional) linking patients to clinics