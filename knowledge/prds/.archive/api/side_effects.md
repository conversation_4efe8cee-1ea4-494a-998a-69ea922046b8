### 📝 `atomic_prd.md`
**Feature:** `Side Effect Reporting API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for allowing patients to report side effects experienced (categorized as minor or major) and for clinicians to view and triage these reports.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Submit Side Effect Report | Allows an authenticated patient to submit a report detailing a side effect, including severity (minor/major) and description. | `POST /api/patients/me/side-effects` | Patient |
| Get Side Effect Reports (Clinician) | Allows an authenticated clinician to retrieve a list of side effect reports for their patients, potentially filterable by severity or status. | `GET /api/clinicians/side-effects` | Clinician |
| Update Side Effect Report Status (Clinician) | (Potential Future/MVP+) Allows a clinician to update the status of a report (e.g., Reviewed, Action Taken, Needs Follow-up). | `PUT /api/clinicians/side-effects/{reportId}` | Clinician |

---

## ✅ Acceptance Criteria

**Submit Side Effect Report:**
- ✅ API accepts side effect details (description, severity level - minor/major) from an authenticated patient.
- ✅ API successfully stores the report with patient identifier, timestamp, severity, description, and initial status (e.g., 'Submitted').
- ✅ API returns a success confirmation.
- ✅ API returns 401/403 if the user is not an authenticated patient.

**Get Side Effect Reports (Clinician):**
- ✅ API returns a list of side effect reports for patients associated with the authenticated clinician.
- ✅ API allows filtering by severity (minor/major) and potentially status.
- ✅ API allows sorting (e.g., by submission date, by severity).
- ✅ Each report includes patient identifier, description, severity, timestamp, and status.
- ✅ API returns 401/403 if the user is not an authenticated clinician.

**(MVP+ Update Side Effect Report Status):**
- ✅ API accepts a report ID and a new status from an authenticated clinician.
- ✅ API updates the status of the specified report in the database.
- ✅ API returns a success confirmation.
- ✅ API returns 401/403 if the user is not an authenticated clinician or not authorized for the specific report.
- ✅ API returns 404 if the report ID does not exist.

---

## ⛓️ Constraints

- **Severity Definition:** Clear criteria for 'minor' vs. 'major' needed for consistent reporting and triage (this might be defined more on the frontend guidance, but API needs to store the category).
- **Triage Logic:** The API provides the data; the clinician portal frontend will likely implement the visual flagging/color-coding based on severity/status.
- **Data Structure:** Define the fields required for a side effect report (e.g., description, date occurred, severity).
- **No Clinical Advice:** The API and reporting mechanism must not provide diagnosis or treatment advice. Disclaimers are necessary.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (Submit Report).
- Consumed by Clinician Web Portal Frontend (View/Triage Reports, potentially Update Status).
- Relies on the database for storing and retrieving side effect report data.
- Relies on Authentication API/middleware for user authorization.

---

## 📈 Side Effect Reporting Enhancements

Side effect reports have been updated to include:
 • clinician_id (string, optional) linking the report to a reviewer
 • reported_at (datetime, mandatory) indicating when the report was filed
 • resolved_at (datetime, optional) to record resolution timing
 • resolution_notes (text, optional) for additional context
Index changes now support optimized queries by severity and status.