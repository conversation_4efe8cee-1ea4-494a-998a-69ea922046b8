### 📝 `atomic_prd.md`
**Feature:** `Weight Tracking API`  
**Parent PRD:** master_prd.md, api_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the API endpoints responsible for managing patient weight tracking data. This includes allowing patients to log their weight manually on a weekly basis, retrieving weight history, and potentially calculating BMI.

---

## ✨ Features

| Feature | Description | Endpoint Idea | User Role |
|---------|-------------|---------------|-----------|
| Log Weight Entry | Allows an authenticated patient to manually log their weight. Enforces weekly interval. | `POST /api/patients/me/weight-log` | Patient |
| Get Weight History | Allows an authenticated patient or clinician to retrieve the patient's weight history data. | `GET /api/patients/me/weight-log` (Patient) <br> `GET /api/clinicians/patients/{patientId}/weight-log` (Clinician) | Patient, Clinician |
| Calculate BMI | (Implicit/Derived) BMI is calculated based on logged weight and patient's height from profile. May be returned with history or calculated client-side. | N/A (Derived Data) | N/A |

---

## ✅ Acceptance Criteria

**Log Weight Entry:**
- ✅ API accepts a weight value from an authenticated patient.
- ✅ API validates the input weight value (e.g., positive number).
- ✅ API validates that the logging interval is respected (e.g., no more than one entry per week, TBD exact logic). Returns error if violated.
- ✅ API successfully stores the weight entry with a timestamp in the database.
- ✅ API returns a success confirmation or the created entry.
- ✅ API returns 401/403 if the user is not an authenticated patient.

**Get Weight History:**
- ✅ API returns a list of the patient's historical weight entries (value and timestamp).
- ✅ API allows filtering/pagination if needed for large histories (consider for future, not MVP).
- ✅ (Patient) API returns 401/403 if the user is not the authenticated patient.
- ✅ (Clinician) API accepts a patient ID and returns 401/403 if the user is not an authenticated clinician.
- ✅ (Clinician) API returns 404 if the patient ID does not exist or is not associated with the clinician.
- ✅ API response includes calculated BMI for each entry if patient height is available.

---

## ⛓️ Constraints

- **Weekly Interval:** The API or underlying logic must enforce the weekly logging rule. The exact definition of "weekly" needs clarification (e.g., 7 days since last log, specific day of week).
- **BMI Calculation:** Requires patient's height to be stored and accessible (likely via Patient Data API). Decide if BMI is calculated/stored on write, or calculated on read.
- **Data Storage:** Weight logs need to be stored efficiently with timestamps for historical graphing.
- **Reminder Trigger:** The API itself doesn't send reminders; it provides the data. A separate mechanism (e.g., scheduled task, event listener) will be needed to trigger push notifications based on logging activity/inactivity.

---

## 🔗 Dependencies

- Consumed by Patient Web App Frontend (Log Weight, View History/Graph).
- Consumed by Clinician Web Portal Frontend (View History/Graph).
- Relies on the database for storing and retrieving weight log data.
- Relies on Authentication API/middleware for user authorization.
- Relies on Patient Data API (or direct DB access) for patient height for BMI calculation.
- Indirect dependency for a separate Notification Service/Scheduler for reminders.