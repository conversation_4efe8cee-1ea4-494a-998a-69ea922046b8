## Feature: Appointment Management

### 1. Overview

This document defines the requirements for the development and integration of appointment management within the PulseTrack platform. The feature provides role-specific capabilities for patients and clinicians. The objective is to seamlessly integrate appointment requests and views into the patient-facing mobile/desktop app and to offer full appointment creation, scheduling, and management tools through the clinician portal.

### 2. Background and Rationale

**Current State:**  
The existing codebase includes an appointment model and basic CRUD operations (see backend files such as `app/models/appointment.py`, `app/crud/crud_appointment.py`, and associated schema definitions). However, there is no dedicated user interface or API endpoints that clearly differentiate between the patient and clinician roles in the management of appointments.  

**Why This Feature?**  
- **For Patients:** To enhance the user experience by allowing them to seamlessly request appointments, track their status, view details such as date, time, and any notes from the clinician, and review past consultations.
- **For Clinicians:** To offer an effective management tool for scheduling patient appointments, manually creating appointments when needed, and overseeing the appointment lifecycle—from request and confirmation to rescheduling or cancellation.  
- **Compliance and Workflow:** Ensuring that appointment data is handled in a HIPAA-compliant manner is essential. Role-based access controls must be enforced so that patients and clinicians only see data pertinent to their interactions.

### 3. Goals and Objectives

- **Primary Goals:**
  - Enable patients to request an appointment through a dedicated UI.
  - Allow patients to view a list of both pending and confirmed appointments.
  - Provide clinicians with a dashboard to view incoming appointment requests, create appointments manually, and manage existing ones.
  
- **Secondary Goals:**
  - Integrate appointment notifications and reminders within the existing notification system.
  - Maintain secure data handling and role-based access as defined in the architecture and compliance constraints.
  - Provide clear, consistent UI/UX that conforms to overall project design principles.

### 4. User Roles and Use Cases

#### 4.1 Patient Use Cases

- **UC-P1 – Request Appointment:**
  - **As a patient,** I want to fill out a simple appointment request form including my preferred date, time, appointment type (e.g., consultation, follow-up), and any relevant comments or concerns.
  - **Acceptance Criteria:** Upon submission, a request is recorded with a “pending” status, and the patient receives a confirmation notification.

- **UC-P2 – View Appointments:**
  - **As a patient,** I want to view a list of my appointment requests and confirmed appointments.
  - **Acceptance Criteria:** Appointments are displayed in a timeline or list format, showing key details (date, time, status, clinician name, and any notes). Past, present, and future appointments should be clearly indicated.

#### 4.2 Clinician Use Cases

- **UC-C1 – Create Appointment:**
  - **As a clinician,** I want to directly create an appointment for a patient if needed (for instance, to schedule follow-ups).
  - **Acceptance Criteria:** The clinician fills out an appointment form (patient identifier, appointment date/time, location, and notes). The system saves the appointment and associates it with both the patient and the clinician.

- **UC-C2 – View and Manage Appointments:**
  - **As a clinician,** I want to view all appointment requests submitted by my patients as well as appointments I have created.
  - **Acceptance Criteria:** A dashboard provides a complete list of appointments with filtering options (by status, date, patient, etc.). Clinicians can update appointment details (e.g., reschedule, add notes, change status, or cancel an appointment).

### 5. Functional Requirements

#### 5.1 API and Backend Enhancements
- **Data Model Integration:**
  - Use the existing appointment model (and associated migration files) as the baseline.
  - Ensure that new endpoints correctly associate appointments with both patient and clinician IDs for role-based access.
- **Endpoints for Patients:**
  - **GET /api/v1/appointments:** Return a list of all appointments linked to the logged-in patient.
  - **POST /api/v1/appointments/request:** Allow the patient to submit an appointment request with required fields.
- **Endpoints for Clinicians:**
  - **GET /api/v1/appointments:** Return a list of all appointments (both those requested by patients and those manually created) for clinicians based on associations.
  - **POST /api/v1/appointments:** Allow clinicians to create a new appointment.
  - **PUT /api/v1/appointments/{appointment_id}:** Enable updating, rescheduling, or canceling an appointment.
- **Security and Access Control:**
  - Integrate role-based access controls to ensure patients only see their own appointments and clinicians only see/manage appointments associated with their patient roster.
  - Audit logs must capture appointment-related actions (creation, update, cancellation) as part of compliance efforts.

#### 5.2 Frontend Requirements

##### Patient Interface:
- **Appointment Request Form:**
  - Fields: Preferred Date and Time (with date/time picker), Appointment Type (dropdown or radio buttons), Additional Comments (optional text area).
  - Validation: Ensure dates/times are in the future, required fields are completed.
- **Appointment List View:**
  - Display key appointment details: status (Pending, Confirmed, Cancelled), date, time, clinician assigned, and patient notes.
  - Sorting/filtering options: upcoming vs. past appointments.
  - Notification of status changes (e.g., appointment approved/rescheduled).

##### Clinician Interface:
- **Appointment Dashboard:**
  - **List View:** A clear, sortable list or calendar view of all appointments.
  - **Appointment Detail Modal/Page:** Allow editing or cancelling an appointment, with update forms for date, time, and additional notes.
- **Appointment Creation Form:**
  - Similar structure to the patient request form, with additional fields if needed (e.g., clinic location, appointment confirmation status).
- **Integration:**
  - Ensure that appointment changes update in real time or via polling as per existing front-end update strategies.

#### 5.3 Notifications and Reminders
- **Automated Notifications:**  
  - Send confirmation notifications to patients when their appointment is requested, approved, or rescheduled.
  - Clinicians receive alerts for new appointment requests.
- **Reminders:**  
  - Integration with the notification system to push reminders 24 hours before an appointment.

### 6. Non-Functional Requirements

- **Performance:**
  - API endpoints for appointments should return results in under 500ms under normal load.
  - The frontend appointment interface should be responsive on both mobile and desktop devices.
- **Security:**
  - All data exchanged must use HTTPS, and sensitive appointment data must be accessible only to authenticated users with proper roles.
  - Role-based access must be enforced server-side.
- **Compliance:**
  - Ensure that all appointment data handling complies with HIPAA, GDPR, and any other relevant healthcare regulations.
- **Scalability:**
  - The design should allow for future enhancements (e.g., integration with calendar systems, third-party schedulers) as the user base grows.

### 7. Dependencies and Assumptions

- **Dependencies:**
  - The existing appointment model, CRUD operations, and related schemas (available in the repository as part of the merged codebase).
  - Existing authentication and role-based routing mechanisms are in place for differentiating patient and clinician sessions.
  - Notification system and audit logging are already part of the platform or will be extended.
- **Assumptions:**
  - The appointment request and management features will be integrated into the current backend (FastAPI) and frontend (React) architecture.
  - Existing data migration scripts will be utilized to support new fields or relationships if modifications are required.

### 8. Milestones and Timeline

- **Design and Specification (1 week):**
  - Finalize detailed flows, UI/UX mockups, and API specifications.
- **Backend API Development (2–3 weeks):**
  - Implement new appointment endpoints and update CRUD functions.
  - Enhance the appointment model and run necessary database migrations.
- **Frontend Implementation (2–3 weeks):**
  - Develop patient appointment request and viewing interfaces.
  - Build clinician appointment creation and management dashboard.
- **Integration and Testing (1–2 weeks):**
  - End-to-end testing of appointment flows for both patients and clinicians.
  - QA, load testing, and compliance review (including audit logging verification).
- **Deployment and Monitoring (1 week):**
  - Deploy to staging/production.
  - Monitor system performance and user feedback.

### 9. Acceptance Criteria

- **Patients:**
  - Successful submission of appointment requests with required validations.
  - Ability to view a chronological list of appointments with clear status indicators.
  - Notification is received for any status updates related to appointments.
- **Clinicians:**
  - Ability to view all appointments linked to their roster, including both requests and manually created appointments.
  - Functionality to create, update, or cancel appointments directly from their portal.
  - Audit logs accurately record actions taken on appointment records.
- **System:**
  - API endpoints respond within performance targets.
  - Role-based access correctly restricts the views and operations.
  - The interface is responsive and user friendly across devices.
  - Compliance requirements (data security, audit logging) are met.

### 10. Future Considerations

- Integration with third-party calendar systems (e.g., Google Calendar, Outlook).
- Enhanced reminder capabilities (SMS or email confirmations).
- AI-powered scheduling suggestions based on clinician availability and patient preferences.
- Dynamic rescheduling capabilities triggered by cancellation events.
- Further UI enhancements for appointment calendars (e.g., drag-and-drop scheduling in clinician portal).

### Appointment Model Changes

The appointment model now includes additional fields:
 • duration_minutes (integer)
 • reason (text, optional)
 • cancelled_at (datetime, optional)
 • cancelled_by_id (string, optional)
 • cancellation_reason (text, optional)
These fields support enhanced scheduling, cancellation tracking, and record keeping.

---