# Atomic PRD: Centralize Authentication Logic

**Feature:** Shared Authentication Context and Components
**Source PRD:** `REF:.xgen/.seed/custom-auth.txt` (Section 1)
**Date:** 2025-04-07
**Status:** Proposed

---

## 🎯 Outcome

Implement a shared React Context Provider across all frontend applications (<PERSON>ient, Clinician, Admin) to centralize Clerk SDK initialization, session management, token handling, and core authentication actions (`signIn`, `signOut`, `acceptInvitation`). Promote UI consistency by utilizing shared/reusable components for authentication forms and elements.

---

## 🔑 Key Features & Requirements

1.  **Unified Auth Context Provider:**
    *   Create a React Context Provider (`AuthProvider`) that wraps the root of each frontend application (<PERSON><PERSON>, Clinician, Admin).
    *   The provider must initialize the Clerk SDK using the appropriate Frontend API Key from environment variables.
    *   It must manage the user's authentication state (e.g., logged in/out, user object, session details).
    *   It must expose core authentication functions:
        *   `signIn(...)`: Method to initiate the Clerk sign-in flow.
        *   `signOut()`: Method to initiate the Clerk sign-out flow.
        *   `acceptInvitation(...)`: Method to handle the invitation acceptance flow.
        *   `getToken()`: Method to retrieve the current Clerk session token.
    *   It should provide access to the current user's session and profile information managed by Clerk.

2.  **Shared UI Components:**
    *   Identify common UI elements used in authentication flows (login forms, invitation acceptance forms).
    *   Develop or utilize existing shared/reusable components (e.g., from `shadcn/ui` or a common component library) for elements like:
        *   Input fields (Email, Password, Magic Code)
        *   Buttons (Login, Submit, Accept Invitation, Logout)
        *   Error message displays
        *   Loading indicators
    *   Ensure these components adhere to the project's `style_guide.md`.

---

## 🔗 Dependencies & Interactions

*   **Depends On:** Clerk SDK, React Context API, Frontend project setup, Environment variable configuration.
*   **Impacts:** All three frontend applications (Patient, Clinician, Admin), Authentication flows, UI consistency.

---

## 🧠 Design & Implementation Notes

*   Consider creating a shared package or workspace (if using a monorepo) for the `AuthProvider` and reusable UI components to avoid code duplication.
*   The `AuthProvider` should handle token refresh logic implicitly via the Clerk SDK.
*   Ensure proper error handling and state management within the provider for API calls to Clerk.
*   Memory Bank Context: Leverages existing frontend architecture (React, TypeScript) and aligns with the API-First pattern by handling auth state client-side (`productContext.md`, `systemPatterns.md`). Addresses the need for consistent UX across portals (`productContext.md`).

---

## 🏷️ Slash Command Tags

/auth /frontend /react /context /ui /refactor

---

## ⚖️ Applicable Rules & Constraints

*   **[P-AUTH-UI-001]** Shared Auth Context
*   **[P-AUTH-UI-002]** Reusable Components
*   **[P-AUTH-INT-001]** SDK Initialization
*   Ref: `.xgen/constraints/auth_constraints.md`
*   Ref: `.xgen/style_guide.md`

---

## ✅ Acceptance Criteria

*   An `AuthProvider` component exists and wraps each frontend application.
*   Clerk SDK is initialized correctly within the provider using environment variables.
*   `signIn`, `signOut`, `acceptInvitation`, and `getToken` functions are available via the context.
*   Authentication state (user, session) is accessible via the context.
*   Common UI elements for authentication use shared/reusable components.
*   Basic authentication flows (login/logout) function correctly using the context provider.