# Atomic PRD: Custom Login & Invitation Pages

**Feature:** Implement Custom UI for Authentication Flows
**Source PRD:** `REF:.xgen/.seed/custom-auth.txt` (Section 2)
**Date:** 2025-04-07
**Status:** Proposed

---

## 🎯 Outcome

Develop custom, visually distinct login and invitation acceptance pages for each frontend application (<PERSON>ient, Clinician, Admin), replacing default Clerk UI where necessary. These pages will utilize the shared `AuthProvider` and components to interact with the Clerk SDK for authentication and invitation handling.

---

## 🔑 Key Features & Requirements

1.  **Custom Login Page(s):**
    *   Design and implement a dedicated login page/route for each frontend application (or a single page dynamically styled).
    *   The page must include form fields for necessary credentials (e.g., Email, Password for Admin/Clinician, Email/Magic Code for Patient - TBC based on final auth strategy).
    *   Utilize shared input and button components.
    *   On submission, call the `signIn` method exposed by the `AuthProvider`.
    *   Handle loading states during the sign-in process.
    *   Display clear error messages on failed login attempts using shared error display components.
    *   On successful login (handled by `AuthProvider` updating state), the user should be automatically redirected to their respective authenticated dashboard/landing page (managed by routing logic).

2.  **Custom Invitation Acceptance Page(s):**
    *   Design and implement a dedicated page/route to handle user invitations (primarily for Clinicians/Admins invited via Clerk).
    *   The page should be accessed via the unique link sent in the Clerk invitation email.
    *   It must capture the invitation token from the URL or Clerk's session.
    *   Call a method (e.g., `acceptInvitation` or similar logic using Clerk SDK functions exposed via `AuthProvider`) to verify the invitation token.
    *   Allow the user to set required information (e.g., set a password, confirm details).
    *   Handle loading states and display clear error messages for invalid/expired tokens or other issues.
    *   On successful acceptance, log the user in and redirect them to the appropriate application dashboard.

3.  **Logout Mechanism:**
    *   Implement a visible logout button or link within the authenticated sections of each application.
    *   Clicking logout must call the `signOut` method exposed by the `AuthProvider`.
    *   The `AuthProvider` (or routing logic reacting to auth state change) should handle clearing the session and redirecting the user to the public landing page or the custom login page.

---

## 🔗 Dependencies & Interactions

*   **Depends On:** `AuthProvider` (from `auth_centralize.md`), Shared UI components, Clerk SDK, Frontend routing setup.
*   **Impacts:** User login experience, Invitation onboarding flow, User logout flow across all frontends.

---

## 🧠 Design & Implementation Notes

*   Determine if separate login routes (`/login/patient`, `/login/clinician`, etc.) are needed or if conditional logic/styling on a single `/login` route is sufficient.
*   Ensure the invitation acceptance page clearly indicates the purpose and guides the user through setup.
*   Leverage Clerk's capabilities for handling different authentication factors (Password, Magic Link, etc.) via the `signIn` method.
*   Memory Bank Context: Replaces default Clerk UI with custom pages, aligning with the need for tailored UX per user type (`productContext.md`, `systemPatterns.md`). Builds upon the existing frontend structure (`decisionLog.md`).

---

## 🏷️ Slash Command Tags

/auth /frontend /react /ui /login /invitation /logout

---

## ⚖️ Applicable Rules & Constraints

*   **[P-AUTH-UI-003]** Tailored Experience
*   **[P-AUTH-UI-004]** Responsive Design
*   **[P-AUTH-UI-005]** Clear Feedback
*   **[P-AUTH-INT-003]** Logout Flow
*   **[P-AUTH-INT-004]** Invitation Acceptance Flow
*   Ref: `.xgen/constraints/auth_constraints.md`
*   Ref: `.xgen/style_guide.md`

---

## ✅ Acceptance Criteria

*   Custom login pages exist and successfully authenticate users via the `AuthProvider`'s `signIn` method.
*   Custom invitation acceptance pages exist, verify Clerk invitation tokens, allow user setup, and log the user in.
*   Logout functionality exists in authenticated areas, calls `AuthProvider`'s `signOut`, and redirects correctly.
*   Appropriate loading states and error messages are displayed on login and invitation pages.
*   Pages are responsive and styled according to the `style_guide.md` and tailored UX requirements.
*   Users are correctly redirected after login, invitation acceptance, and logout.