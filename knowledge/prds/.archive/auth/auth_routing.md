# Atomic PRD: Authentication Routing and Navigation

**Feature:** Implement Protected Routes and Authentication Navigation Logic
**Source PRD:** `REF:.xgen/.seed/custom-auth.txt` (Section 5)
**Date:** 2025-04-07
**Status:** Proposed

---

## 🎯 Outcome

Establish clear routing rules within each frontend application to protect authenticated sections, redirect users based on their authentication status, and potentially handle distinct entry points for different user types if necessary.

---

## 🔑 Key Features & Requirements

1.  **Protected Routes:**
    *   Implement a mechanism (e.g., a wrapper component, higher-order component, or logic within the router setup) to protect routes/pages that require authentication.
    *   This mechanism must check the user's authentication status using the shared `AuthProvider` (from `auth_centralize.md`).
    *   If the user is authenticated, allow access to the requested protected route.
    *   If the user is not authenticated, redirect them to the appropriate custom login page (from `auth_custom_pages.md`).

2.  **Public Routes:**
    *   Define routes that are publicly accessible (e.g., login page, invitation acceptance page, potentially a landing page).
    *   Ensure authenticated users accessing public-only routes (like the login page) are redirected to their authenticated dashboard/home page.

3.  **Redirection Logic:**
    *   Implement clear redirection logic upon successful:
        *   **Login:** Redirect from the login page to the user's default authenticated view (e.g., Patient Dashboard, Clinician Dashboard, Admin Dashboard).
        *   **Invitation Acceptance:** Redirect from the invitation page to the user's default authenticated view.
        *   **Logout:** Redirect from any authenticated page to the login page or a public landing page.

4.  **Distinct Entry Points (Consideration):**
    *   Evaluate the need for distinct login URLs (e.g., `/login/patient`, `/login/clinician`, `/login/admin`) versus a single `/login` route.
    *   If distinct URLs are used, ensure they route to the correctly styled login component/page tailored for that user type.
    *   If a single `/login` route is used, implement logic to potentially adapt styling or messaging based on context (if feasible and necessary). *Decision: Default to shared `/login` unless strong justification for separation arises.*

---

## 🔗 Dependencies & Interactions

*   **Depends On:** Frontend routing library (e.g., React Router), `AuthProvider`, Custom authentication pages.
*   **Impacts:** Application navigation flow, Access control to different sections of the applications.

---

## 🧠 Design & Implementation Notes

*   Leverage the capabilities of the chosen routing library (e.g., `react-router-dom`) for creating protected route components and handling redirects.
*   The `AuthProvider`'s state (e.g., `isAuthenticated`, `isLoading`) will be crucial for the routing logic. Handle the loading state gracefully to avoid flickering or incorrect redirects during initial auth checks.
*   Consider storing the intended destination URL before redirecting to login, so the user can be sent there after successful authentication.
*   Memory Bank Context: Implements standard web application security patterns (`systemPatterns.md`) within the React frontend context (`productContext.md`).

---

## 🏷️ Slash Command Tags

/auth /frontend /react /routing /navigation /security

---

## ⚖️ Applicable Rules & Constraints

*   **[P-AUTH-INT-002]** Protected Routes
*   Ref: `.xgen/constraints/auth_constraints.md`
*   Ref: `.xgen/constraints/security.md`

---

## ✅ Acceptance Criteria

*   Unauthenticated users attempting to access protected routes are redirected to the login page.
*   Authenticated users attempting to access the login page are redirected to their dashboard.
*   Users are correctly redirected to their respective dashboards after successful login or invitation acceptance.
*   Users are correctly redirected to the login/landing page after logging out.
*   Routing logic correctly utilizes the authentication state from the `AuthProvider`.
*   Navigation between public and private routes behaves as expected based on authentication status.