# Atomic PRD: Clerk SDK Integration & Token Handling

**Feature:** Integrate <PERSON>K and Manage Authentication Tokens Securely
**Source PRD:** `REF:.xgen/.seed/custom-auth.txt` (Section 3)
**Date:** 2025-04-07
**Status:** Proposed

---

## 🎯 Outcome

Ensure the Clerk JavaScript SDK is correctly initialized in each frontend application and that authentication tokens generated by <PERSON> are securely handled, stored, and validated by the backend API for protected endpoints.

---

## 🔑 Key Features & Requirements

1.  **SDK Initialization:**
    *   The Clerk SDK (`@clerk/clerk-react` or similar) must be installed as a dependency in each frontend project (<PERSON>ient, Clinician, Admin).
    *   The SDK must be initialized at the application's root level (likely within the `AuthProvider` from `auth_centralize.md`).
    *   Initialization must use the correct Clerk Frontend API Key (`VITE_CLERK_PUBLISHABLE_KEY` or equivalent) loaded securely from environment variables.

2.  **Token Handling (Frontend):**
    *   The `AuthProvider` should utilize the Clerk SDK to manage the lifecycle of the session token.
    *   The `getToken()` method exposed by the `AuthProvider` should retrieve the current, valid session token from the Clerk SDK.
    *   Tokens should ideally be managed internally by the Clerk SDK, leveraging its secure storage mechanisms (e.g., HttpOnly cookies if configured, or other secure browser storage). Direct manual storage of tokens in `localStorage` should be avoided unless absolutely necessary and properly justified.

3.  **Token Handling (Backend):**
    *   Backend API endpoints requiring authentication must implement a mechanism to validate the Clerk session token sent from the frontend.
    *   This typically involves:
        *   Frontend sending the token in the `Authorization: Bearer <token>` header.
        *   Backend using the Clerk Backend SDK (e.g., `@clerk/clerk-sdk-node` if Node.js, or equivalent verification method for Python/FastAPI using Clerk's Backend API Key/JWT Verification Key) to verify the token's signature and expiration.
        *   Extracting user identity (e.g., Clerk User ID, roles, email) from the validated token payload.
    *   Alternatively, the backend might exchange the Clerk token for an internal session mechanism (e.g., a separate JWT issued by the backend), but direct Clerk token validation is often preferred for simplicity if the backend trusts Clerk as the IdP.
    *   The specific Clerk JWT template (`CodenamePulsetrack` as identified in `activeContext.md` and `decisionLog.md`) must be requested by the frontend and validated by the backend to ensure necessary metadata (like roles, clinic ID) is present.

4.  **Backend Synchronization (Optional but Recommended):**
    *   Consider using Clerk webhooks to notify the backend of significant authentication events (e.g., user creation, deletion, updates).
    *   This allows the backend database (e.g., Clinician or Patient records) to be kept in sync with Clerk's user directory if necessary, rather than relying solely on JWT claims.

---

## 🔗 Dependencies & Interactions

*   **Depends On:** Clerk Account & API Keys, Clerk SDKs (Frontend/Backend), `AuthProvider`, Backend API framework (FastAPI), Environment variable setup.
*   **Impacts:** Frontend-Backend communication security, Authentication state management, Protected API endpoint access.

---

## 🧠 Design & Implementation Notes

*   Refer to Clerk documentation for the recommended way to integrate with FastAPI backends for token validation. This might involve fetching Clerk's JWKS (JSON Web Key Set) or using a Clerk library.
*   Ensure the backend validation correctly handles the specific Clerk JWT template (`CodenamePulsetrack`) used by the frontends.
*   Error handling is crucial for token validation failures (expired, invalid signature, wrong template).
*   Memory Bank Context: Builds upon previous decisions to use Clerk for Admin/Clinician auth (`productContext.md`, `decisionLog.md`). Addresses the specific need for validating the `CodenamePulsetrack` JWT template identified during recent debugging (`activeContext.md`, `decisionLog.md`). Aligns with security constraints (`systemPatterns.md`).

---

## 🏷️ Slash Command Tags

/auth /frontend /backend /api /security /clerk /sdk /jwt /token

---

## ⚖️ Applicable Rules & Constraints

*   **[P-AUTH-SEC-001]** Token Storage
*   **[P-AUTH-SEC-002]** Backend Token Validation
*   **[P-AUTH-SEC-003]** Environment Variables
*   **[P-AUTH-INT-001]** SDK Initialization
*   Ref: `.xgen/constraints/auth_constraints.md`
*   Ref: `.xgen/constraints/security.md`

---

## ✅ Acceptance Criteria

*   Clerk SDK is initialized in each frontend using the correct environment variable key.
*   Frontend retrieves tokens via the `AuthProvider` which uses the Clerk SDK.
*   Frontend sends the Clerk token in the `Authorization` header for protected API calls, requesting the `CodenamePulsetrack` template.
*   Backend API has a dependency or middleware that successfully validates the incoming Clerk token (signature, expiration, template).
*   Backend can extract user identity and relevant metadata from the validated token.
*   Protected API endpoints return 401/403 errors for invalid or missing tokens.
*   (Optional) Backend webhook handler is implemented to sync with Clerk user events if deemed necessary.