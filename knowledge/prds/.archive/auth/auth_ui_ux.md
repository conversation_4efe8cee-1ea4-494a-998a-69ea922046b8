# Atomic PRD: Authentication UI/UX Considerations

**Feature:** Tailor User Experience for Authentication Flows
**Source PRD:** `REF:.xgen/.seed/custom-auth.txt` (Section 4)
**Date:** 2025-04-07
**Status:** Proposed

---

## 🎯 Outcome

Ensure the custom authentication pages (login, invitation acceptance) provide a user experience that is tailored to the specific user type (Patient, Clinician, Admin), is fully responsive, and provides clear feedback and error handling, adhering to the project's overall style guide.

---

## 🔑 Key Features & Requirements

1.  **Tailored Experience per Application:**
    *   **Patient App:** The authentication UI (login, magic code entry, potentially invitation if applicable) must prioritize simplicity, ease of use, and a friendly, reassuring tone. Minimal jargon. Clear instructions.
    *   **Clinician & Admin Portals:** The authentication UI (login, invitation acceptance) should adopt a more formal, professional, and secure appearance. Branding should be consistent with the respective portals. Instructions should be clear regarding roles and security.

2.  **Responsive Design:**
    *   All custom authentication pages and components must be fully responsive and function correctly across various screen sizes (desktop, tablet, mobile).
    *   Mobile experience is particularly critical for the Patient App. Layouts should adapt gracefully, ensuring readability and usability on small screens.

3.  **Error Handling and Feedback:**
    *   Implement clear, user-friendly error messages for common authentication issues:
        *   Invalid email/password/magic code.
        *   Incorrect credentials.
        *   Expired or invalid invitation token.
        *   Account locked or disabled (if applicable).
        *   Network errors during API calls to Clerk or the backend.
    *   Error messages should be displayed inline near the relevant input field where possible.
    *   Provide visual feedback during asynchronous operations (e.g., submitting login form, verifying invitation) using loading indicators (spinners, disabled buttons).
    *   Success feedback (e.g., "Invitation accepted, redirecting...") can briefly be shown before redirection.

4.  **Consistency with Style Guide:**
    *   All UI elements (buttons, inputs, typography, colors, spacing) used in the authentication flows must adhere to the definitions in `.xgen/style_guide.md`.
    *   Utilize the shared component library established in `auth_centralize.md`.

---

## 🔗 Dependencies & Interactions

*   **Depends On:** Custom authentication pages (`auth_custom_pages.md`), Shared UI components (`auth_centralize.md`), `style_guide.md`, Frontend framework capabilities (React).
*   **Impacts:** User satisfaction, Ease of login/onboarding, Brand perception, Accessibility.

---

## 🧠 Design & Implementation Notes

*   Work closely with UI/UX design resources if available, or establish clear design mockups/principles if not.
*   Test responsiveness thoroughly using browser developer tools and real devices if possible.
*   Write error messages from the user's perspective – avoid technical jargon.
*   Ensure accessibility guidelines (WCAG) are followed for form elements, error messages, and focus indicators.
*   Memory Bank Context: Directly addresses the need for a "drop-down simple" and intuitive UI/UX, especially for patients (`productContext.md`). Reinforces the use of Shadcn/ui and Tailwind for consistency (`productContext.md`, `systemPatterns.md`).

---

## 🏷️ Slash Command Tags

/auth /frontend /ui /ux /design /responsive /error-handling /accessibility

---

## ⚖️ Applicable Rules & Constraints

*   **[P-AUTH-UI-003]** Tailored Experience
*   **[P-AUTH-UI-004]** Responsive Design
*   **[P-AUTH-UI-005]** Clear Feedback
*   Ref: `.xgen/constraints/auth_constraints.md`
*   Ref: `.xgen/style_guide.md`
*   Ref: `.xgen/constraints/security.md` (regarding accessible error messages - avoid revealing too much info)

---

## ✅ Acceptance Criteria

*   Patient authentication pages have a distinct, user-friendly look and feel compared to Clinician/Admin pages.
*   Clinician/Admin authentication pages have a professional and secure appearance.
*   All authentication pages render correctly and are usable on mobile, tablet, and desktop screen sizes.
*   Specific, helpful error messages are displayed for invalid inputs or failed authentication attempts.
*   Loading indicators are present during asynchronous operations.
*   All UI elements conform to the project's `style_guide.md`.
*   Accessibility standards (e.g., keyboard navigation, focus states, contrast) are met.