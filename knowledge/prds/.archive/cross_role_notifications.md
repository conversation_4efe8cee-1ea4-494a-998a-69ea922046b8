# PRD: Notification System (Clinician, Patient, Admin)

## Title
Cross-Role Notification System

## Status
Draft

## Version
v0.1 - 2025-04-11

## Owner
<PERSON>

## Overview
PulseTrack requires a unified notification system that supports real-time and persistent alerts across all user roles: **Clinicians**, **Patients**, and **Admins**. This system will serve as the foundation for all time-sensitive and event-driven communications (e.g., medication requests, side effect submissions, appointment changes, and admin alerts).

## Goals
- Deliver relevant notifications to appropriate user roles based on events.
- Provide UI components for notification display and management.
- Ensure secure, multi-tenant-safe delivery of notifications.
- Build an extensible backend service to trigger and route notifications.

## Non-Goals
- External notification delivery (e.g., SMS/email/push)
- Rich formatting or attachments

## Stakeholders
- Clinicians (primary consumers)
- Patients (receive reminders, confirmations)
- Admins (monitor triage events, system changes)
- Developers (build event integrations)

---

## Functional Requirements

### Notification Model
Each notification must include:
- `id` (UUID)
- `title` (string)
- `message` (optional text)
- `notification_type` (enum)
- `recipient_role` (enum: CLIN<PERSON>IAN, PATIENT, ADMIN)
- `recipient_id` (FK to user table based on role)
- `related_entity_type` (optional: APPOINTMENT, MEDICATION_REQUEST, etc)
- `related_entity_id` (optional UUID)
- `is_read` (bool)
- `read_at` (datetime)
- `created_at`, `updated_at`

### Core Use Cases
1. **Clinician** receives a notification when:
   - A patient submits a medication request
   - A side effect report is logged
   - An appointment is booked, rescheduled, or canceled

2. **Patient** receives a notification when:
   - A clinician approves/rejects a medication request
   - An appointment reminder is due
   - A clinician logs a new note or lab result

3. **Admin** receives a notification when:
   - A patient signs up or accepts an invitation
   - A new clinician is created
   - A patient alert or content upload occurs

### Trigger Mechanism
Events triggering notifications must call a centralized service:
```python
send_notification(
    title: str,
    message: Optional[str],
    notification_type: NotificationType,
    recipient_role: RoleType,
    recipient_id: str,
    related_entity_type: Optional[str],
    related_entity_id: Optional[str]
)
```

This service should be modular, easily mockable, and auditable.

### Notification Types (enum)
- `APPOINTMENT_CREATED`
- `APPOINTMENT_UPDATED`
- `MEDICATION_REQUESTED`
- `MEDICATION_APPROVED`
- `SIDE_EFFECT_LOGGED`
- `LAB_RESULT_UPLOADED`
- `NOTE_ADDED`
- `PATIENT_INVITED`
- `SYSTEM_ALERT`

### Read/Unread Management
- `GET /notifications/` (with filters by role, read status)
- `POST /notifications/{id}/read`
- `POST /notifications/read_all`

---

## UI Requirements
### Common Elements
- Notification bell with unread badge
- Dropdown with preview of recent notifications
- “View All” page for full history

### Clinician
- Highlight critical events (e.g., patient-reported side effects)
- Ability to filter by patient, type, or severity

### Patient
- Clear upcoming appointments and reminders
- Medication request status updates

### Admin
- System-wide monitoring dashboard with important actions (e.g., triage queue, new user alerts)

---

## Future Considerations
- Role-based notification delivery preferences
- Push notifications to mobile
- WebSocket-based real-time support
- Notification digest (daily/weekly summaries)

---

## Dependencies
- Notification model (exists)
- Clerk-based auth integration (exists)
- Role detection middleware (exists)
- Event bus module (exists)
- UI layout components (in place)

---

## Milestones
1. **v1**: Basic notification triggering, read tracking, clinician UI
2. **v2**: Patient & admin support, filters, routing improvements
3. **v3**: WebSocket support and notification preference settings

