### 📝 `atomic_prd.md`
**Feature:** `Patient Dashboard Header`
**Parent PRD:** patient_ui_prd.md
**Last Updated:** 2025-04-04

---

## 🎯 Purpose

To display a personalized and welcoming greeting to the patient upon accessing the dashboard, setting a friendly and encouraging tone for their interaction with the application.

---

## ✨ Features

| Feature                 | Description                                                               |
|-------------------------|---------------------------------------------------------------------------|
| **Personalized Greeting** | Display "👋 Hello, [First Name]!" using the logged-in patient's first name. |
| **Motivational Message**  | Optionally display a brief, encouraging message below the greeting.       |

---

## ✅ Acceptance Criteria

- ✅ The header prominently displays "👋 Hello, [First Name]!".
- ✅ The patient's actual first name is correctly substituted.
- ✅ An optional motivational message (e.g., "You’re doing great. Let’s check in today.") can be displayed below the greeting.
- ✅ The header adheres to the overall visual style guidelines (font, color, spacing) defined in the parent PRD.
- ✅ The heading "Patient App Home" is *not* used.

---

## ⛓️ Constraints

- Must fetch and use the patient's actual first name.
- Must adhere to the Visual Style Guidelines (Font, Color Palette, Spacing) from `patient_ui_prd.md`.
- Motivational message content may need to be configurable or dynamically generated (TBD based on implementation).

---

## 🔗 Dependencies

- **Authentication System:** Needs access to the logged-in patient's profile data (specifically first name).
- **UI Layout Component:** Will be integrated into the main dashboard layout.