### 📝 `atomic_prd.md`
**Feature:** `Patient Weight Progress Card`
**Parent PRD:** patient_ui_prd.md
**Last Updated:** 2025-04-04

---

## 🎯 Purpose

To provide patients with a quick, visual summary of their weight tracking progress and an easy way to log their current weight, encouraging consistent engagement with this key health metric.

---

## ✨ Features

| Feature             | Description                                                                      |
|---------------------|----------------------------------------------------------------------------------|
| **Visual Trend**    | Display a simple visual representation (e.g., mini-chart, indicator) of weight trend. |
| **Last Logged Date**| Optionally display the date of the last weight entry.                            |
| **Log Weight CTA**  | Provide a clear Call to Action button/link labeled "Log Weight".                   |
| **Summary Snippet** | Include a brief summary line if applicable (e.g., "Last logged: YYYY-MM-DD").     |

---

## ✅ Acceptance Criteria

- ✅ The card displays a visual indicator of weight progress (specific visualization TBD).
- ✅ A prominent "Log Weight" CTA is present and functional (links to the weight logging view/modal).
- ✅ The card adheres to the visual style guidelines (calm colors, spacing, friendly icons/emoji).
- ✅ The card fits within the modular dashboard grid layout.
- ✅ Displays a summary snippet like "Last logged: [Date]" or similar.

---

## ⛓️ Constraints

- Visual trend representation must be simple and easily understandable, avoiding complex charts.
- Must adhere to the Visual Style Guidelines (Component Style, Spacing, Emotion) from `patient_ui_prd.md`.
- Requires access to patient's weight log history data.

---

## 🔗 Dependencies

- **Weight Logging Feature:** The "Log Weight" CTA must link to the corresponding page/modal.
- **API Endpoint (Weight History):** Needs data from the backend to display trends/last logged date.
- **UI Layout Component:** Will be integrated into the main dashboard grid.
- **Charting Library (Optional):** May require a lightweight charting library if a mini-chart is chosen for visualization.