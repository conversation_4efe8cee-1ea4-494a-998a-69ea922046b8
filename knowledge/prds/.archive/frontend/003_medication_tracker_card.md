### 📝 `atomic_prd.md`
**Feature:** `Patient Medication Tracker Card`
**Parent PRD:** patient_ui_prd.md
**Last Updated:** 2025-04-04

---

## 🎯 Purpose

To provide patients with a clear overview of their medication status, including recent requests and current prescriptions (if applicable), and offer a simple way to request refills.

---

## ✨ Features

| Feature               | Description                                                                   |
|-----------------------|-------------------------------------------------------------------------------|
| **Last Request Status** | Display the status of the most recent medication refill request (e.g., Pending, Approved). |
| **Current Meds (Opt.)** | Optionally list currently prescribed medications (if data is available).      |
| **Request Refill CTA**| Provide a clear Call to Action button/link labeled "Request Refill".          |
| **Summary Snippet**   | Include a brief summary line (e.g., "Last request: Pending").                 |

---

## ✅ Acceptance Criteria

- ✅ The card displays the status of the last medication request.
- ✅ A prominent "Request Refill" CTA is present and functional (links to the medication request view/modal).
- ✅ The card adheres to the visual style guidelines (calm colors, spacing, friendly icons/emoji).
- ✅ The card fits within the modular dashboard grid layout.
- ✅ Displays a summary snippet indicating the last request status.
- ✅ Optionally displays current medications if available and deemed appropriate for the summary view.

---

## ⛓️ Constraints

- Display of current medications might depend on data availability and UI simplicity goals.
- Must adhere to the Visual Style Guidelines (Component Style, Spacing, Emotion) from `patient_ui_prd.md`.
- Requires access to patient's medication request history and potentially current prescription data.

---

## 🔗 Dependencies

- **Medication Request Feature:** The "Request Refill" CTA must link to the corresponding page/modal.
- **API Endpoint (Medication Requests):** Needs data from the backend to display status and potentially current meds.
- **API Endpoint (Patient Profile/Meds):** May need access to current prescriptions if displayed.
- **UI Layout Component:** Will be integrated into the main dashboard grid.