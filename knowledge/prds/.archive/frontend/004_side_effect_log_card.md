### 📝 `atomic_prd.md`
**Feature:** `Patient Side Effect Log Card`
**Parent PRD:** patient_ui_prd.md
**Last Updated:** 2025-04-04

---

## 🎯 Purpose

To give patients a quick reminder of their last reported side effect (if any) and provide an immediate pathway to report new symptoms, facilitating timely communication with their care team.

---

## ✨ Features

| Feature                 | Description                                                                    |
|-------------------------|--------------------------------------------------------------------------------|
| **Last Entry Summary**  | Display a brief summary or date of the last reported side effect (e.g., "Last reported: Headache on YYYY-MM-DD"). |
| **Report New Symptom CTA**| Provide a clear Call to Action button/link labeled "Report New Symptom".       |
| **Summary Snippet**     | Include a brief summary line (e.g., "Last reported: [Date]").                  |

---

## ✅ Acceptance Criteria

- ✅ The card displays a summary of the last reported side effect or indicates if none have been reported recently.
- ✅ A prominent "Report New Symptom" CTA is present and functional (links to the side effect reporting view/modal).
- ✅ The card adheres to the visual style guidelines (calm colors, spacing, friendly icons/emoji).
- ✅ The card fits within the modular dashboard grid layout.
- ✅ Displays a summary snippet like "Last reported: [Date]" or "No recent reports".

---

## ⛓️ Constraints

- The summary of the last entry should be concise.
- Must adhere to the Visual Style Guidelines (Component Style, Spacing, Emotion) from `patient_ui_prd.md`.
- Requires access to the patient's side effect report history.

---

## 🔗 Dependencies

- **Side Effect Reporting Feature:** The "Report New Symptom" CTA must link to the corresponding page/modal.
- **API Endpoint (Side Effect Reports):** Needs data from the backend to display the last entry summary.
- **UI Layout Component:** Will be integrated into the main dashboard grid.