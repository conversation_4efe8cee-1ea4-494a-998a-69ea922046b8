### 📝 `atomic_prd.md`
**Feature:** `Patient Next Appointment Card`
**Parent PRD:** patient_ui_prd.md
**Last Updated:** 2025-04-04

---

## 🎯 Purpose

To clearly inform patients about their upcoming appointment details and provide easy access to view more information or manage the appointment if necessary.

---

## ✨ Features

| Feature                | Description                                                                          |
|------------------------|--------------------------------------------------------------------------------------|
| **Appointment Details**| Display key details like date, time, and potentially clinician name/type of appointment. |
| **View/Manage Options**| Provide clear options (buttons/links) like "View Details" or "Reschedule" (if applicable). |
| **Summary Snippet**    | Include a brief summary line (e.g., "Next appointment: [Date] at [Time]").           |

---

## ✅ Acceptance Criteria

- ✅ The card displays the date and time of the next scheduled appointment.
- ✅ Options to "View Details" and potentially "Reschedule" are present and functional (linking to appropriate views/actions).
- ✅ The card adheres to the visual style guidelines (calm colors, spacing, friendly icons/emoji).
- ✅ The card fits within the modular dashboard grid layout.
- ✅ Displays a summary snippet with the appointment date and time.
- ✅ Clearly indicates if no upcoming appointments are scheduled.

---

## ⛓️ Constraints

- Reschedule functionality might be complex and depend on backend/scheduling system capabilities (may be deferred post-MVP).
- Must adhere to the Visual Style Guidelines (Component Style, Spacing, Emotion) from `patient_ui_prd.md`.
- Requires access to the patient's appointment schedule data.

---

## 🔗 Dependencies

- **Appointment Scheduling System/Feature:** Needs integration to display data and potentially trigger actions like rescheduling.
- **API Endpoint (Appointments):** Needs data from the backend to display appointment details.
- **UI Layout Component:** Will be integrated into the main dashboard grid.
- **Appointment Detail View:** The "View Details" CTA must link to a relevant page/modal.