### 🩺 `atomic_prd.md`
**Feature:** `Clinician Frontend - Multi-Clinic View & Management`
**Parent PRD:** master_chat_prd.md
**Last Updated:** 2025-04-05

---

## 🎯 Purpose

To define the user interface components and logic within the Clinician Frontend application that allow authenticated clinicians to view and manage patient data across all the clinics they are associated with, adapting to the multi-tenant structure.

---

## ✨ Features

| Feature                     | Description                                                                                                                                                              |
| --------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Clinic Context Switching    | A mechanism (e.g., dropdown, sidebar) for the clinician to select which clinic's data they are currently viewing/managing, if they are associated with multiple clinics. |
| Multi-Clinic Data Display | Views (e.g., patient lists, dashboards) should be capable of displaying aggregated data or data filtered by the selected clinic context.                                   |
| Patient List (Contextual)   | Display a list of patients associated with the currently selected clinic context.                                                                                          |
| Patient Detail (Contextual) | Display detailed information for a specific patient, ensuring only data relevant to the clinician's relationship with the patient within the selected clinic is shown.      |
| API Integration             | Logic to fetch data from backend APIs, passing the appropriate clinic context (if needed) and handling data associated with multiple clinics.                               |
| Authorization Awareness     | The frontend should rely on the backend API (`api/chat_multi_tenancy.md`) to enforce that clinicians only see data for clinics/patients they are authorized to access.    |

---

## ✅ Acceptance Criteria

- ✅ An authenticated clinician can log in to the clinician frontend.
- ✅ If associated with multiple clinics, the clinician can see a list of their associated clinics and select one as the active context.
- ✅ Patient lists and other data displays correctly filter based on the selected clinic context.
- ✅ The clinician can view detailed information for patients associated with the selected clinic.
- ✅ API calls made from the clinician frontend correctly include context (like clinic ID if required by the endpoint) or rely on backend RBAC based on the authenticated clinician's associations.
- ✅ The clinician cannot view data for clinics or patients they are not associated with (enforced by the backend).
- ✅ The user interface for switching clinic contexts and viewing data is user-friendly and clear.

---

## ⛓️ Constraints

- The UI must clearly indicate the current clinic context being viewed.
- Performance needs to be considered when fetching and displaying data potentially aggregated across multiple clinics or filtered for a specific one.
- Requires robust integration with the frontend's authentication state to get the clinician's token and list of associated clinics.
- UI design must accommodate clinicians associated with potentially many clinics.

---

## 🔗 Dependencies

- **Multi-Tenancy & Access Control API (`api/chat_multi_tenancy.md`):** Provides the list of associated clinics for a logged-in clinician and enforces backend access control.
- **Various Backend Data APIs:** (e.g., Patient data, Weight data, etc.) Endpoints that the clinician frontend consumes, which must respect multi-tenancy rules.
- **Authentication Service / Frontend Auth State:** Provides the clinician's authentication token.
- **Clinician Frontend Application:** The parent application hosting these views.
- **Frontend Framework & UI Library:** (e.g., React, Vite, Shadcn/ui)