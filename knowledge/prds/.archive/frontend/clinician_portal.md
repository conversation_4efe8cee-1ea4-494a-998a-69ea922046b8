### 📝 `atomic_prd.md`
**Feature:** `Clinician Web Portal Frontend`  
**Parent PRD:** master_prd.md, frontend_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the user interface and user experience requirements for the Clinician Web Portal. This web application provides clinicians with tools to manage patients, review submitted data (medication requests, side effects), and generate access codes for new patients. The focus is on efficient workflows for clinicians using standard desktop browsers.

---

## ✨ Features / Views

| Feature/View | Description | Key UI Elements | Consumes API(s) |
|--------------|-------------|-----------------|-----------------|
| **Authentication** | Handles login via Firebase Authentication. | Login button (triggers Firebase client-side auth flow), Session handling. | `Authentication API` (clinician-login) |
| **Dashboard** | Overview page showing key metrics and pending items. | Summary cards (pending requests, major side effects), Quick navigation links. | `Clinician Portal API` (Get Dashboard Summary - if implemented), `Medication API`, `Side Effect API` (for counts) |
| **Patient List** | Displays a searchable/sortable list of patients associated with the clinician. | Table/list of patients (Name, ID, etc.), Search bar, Sorting controls, Pagination controls. | `Clinician Portal API` (Get Patient List) |
| **Patient Detail View** | Displays comprehensive information for a selected patient. | Tabs/sections for Profile, Weight History, Med Requests, Side Effects, Chat History (MVP+). | `Patient Data API` (Get Patient), `Weight Tracking API` (Get History), `Medication API` (Get Requests - filtered by patient), `Side Effect API` (Get Reports - filtered by patient), `Chat Agent API` (Get History - MVP+) |
| **Medication Request Triage** | Dedicated view to list and potentially manage pending medication requests. | Table/list of pending requests, Patient name link, Request details, Status indicators/filters. | `Medication API` (Get Requests) |
| **Side Effect Triage** | Dedicated view to list and potentially manage side effect reports. | Table/list of reports, Patient name link, Severity indicators (color-coding), Description preview, Status indicators/filters. | `Side Effect API` (Get Reports) |
| **Access Code Generation** | Interface to generate a magic code for a new patient. | Generate button, Display area for the generated code, Copy button. | `Clinician Portal API` (Generate Code) |
| **General** | Consistent navigation, session handling (logout). | Header/Sidebar navigation, Logout button. | `Authentication API` (implicitly for session) |

---

## ✅ Acceptance Criteria

- ✅ Clinician can successfully log in using their pre-approved clinic email via the Firebase Authentication UI flow.
- ✅ Clinician is presented with a dashboard summarizing key information upon login.
- ✅ Clinician can view a list of their associated patients.
- ✅ Clinician can search/sort the patient list.
- ✅ Clinician can select a patient and view their detailed information across different sections (Profile, Weight, Meds, Side Effects).
- ✅ Clinician can view pending medication requests, clearly identifying the patient and request details.
- ✅ Clinician can view submitted side effect reports, with clear visual indication of severity (minor/major).
- ✅ Clinician can successfully generate a new magic access code for a patient.
- ✅ UI elements are clear, functional, and suitable for desktop use.
- ✅ Loading indicators are shown during API calls.
- ✅ User-friendly error messages are displayed for API errors.
- ✅ Clinician can log out of the application.

---

## ⛓️ Constraints

- **Platform:** Primarily designed for desktop web browsers. Mobile responsiveness is a lower priority for MVP.
- **Workflow Efficiency:** UI should support efficient clinician workflows (e.g., quick triage, easy navigation between patient data).
- **API Driven:** All dynamic data and actions rely on backend APIs.
- **Data Density:** May need to display more dense information compared to the patient app; clarity is key.
- **Security:** Role-based access control might be needed in the future, but MVP assumes a single 'clinician' role.

---

## 🔗 Dependencies

- Consumes various backend APIs as listed in the Features table.
- Requires a defined UI/UX design system/style guide (can be shared/adapted from patient app).
- Requires React framework, Shadcn/ui component library, and Tailwind CSS.
- Requires frontend state management library.
- Relies on Firebase Authentication client library (`firebase/auth`) for login flow.