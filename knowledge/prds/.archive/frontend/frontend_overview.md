### 📝 `atomic_prd.md`
**Feature:** `Frontend Applications Overview`  
**Parent PRD:** master_prd.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the scope, purpose, and high-level requirements for the frontend applications of Codename Pulsetrack: the Patient Web Application and the Clinician Web Portal. Both applications will be web-based initially, consuming the backend APIs, and must be responsive for use on mobile devices. A high standard of UI/UX, simplicity, and usability is paramount.

---

## ✨ Features

**Patient Web Application (Responsive):**
| Feature Area | Description |
|--------------|-------------|
| Authentication | Handles magic code input and session management. |
| Dashboard/Home | Main view after login (content TBD). |
| Weight Tracking UI | Interface for logging weight and viewing history graph. |
| Medication Request UI | Interface for submitting refill requests. |
| Side Effect Reporting UI | Interface for submitting minor/major side effect reports. |
| Profile Management UI | Interface for viewing/updating contact info and photo. |
| Educational Content UI | Displays static educational materials. |
| Chat Agent UI | Interface for interacting with the chat agent. |

**Clinician Web Portal (Web-based):**
| Feature Area | Description |
|--------------|-------------|
| Authentication | Handles Firebase login and session management. |
| Dashboard | Overview of pending items (med requests, side effects). |
| Patient List/Management | View list of patients, navigate to individual patient views. |
| Patient Profile View | Display detailed patient profile information. |
| Medication Request Triage | View and manage pending medication requests. |
| Side Effect Triage | View and manage side effect reports (with urgency flags). |
| Access Code Generation | Interface to generate magic codes for new patients. |

---

## ✅ Acceptance Criteria

- ✅ Both Patient App and Clinician Portal are web applications accessible via a browser.
- ✅ Patient App is fully responsive and usable on common mobile device screen sizes.
- ✅ Clinician Portal is functional on standard desktop browser sizes (mobile responsiveness is lower priority but desirable).
- ✅ All frontend interactions requiring data or actions utilize the defined backend APIs.
- ✅ User interfaces are intuitive, simple to navigate, and visually appealing ("gorgeous").
- ✅ Application correctly handles user sessions (login, logout, session expiry).
- ✅ Appropriate loading states and error handling are implemented for API interactions.
- ✅ Necessary disclaimers (e.g., "not for diagnosis") are displayed prominently where required.

---

## ⛓️ Constraints

- **Platform:** Web-based only for MVP. No native apps.
- **Responsiveness:** Patient app *must* be responsive. Clinician portal *should* be usable on desktop, responsive is nice-to-have.
- **Technology:** Frontend framework is **React**. UI components built using **Shadcn/ui** on **Tailwind CSS**. Potentially leverage **Supabase UI** components where applicable.
- **UI/UX:** High priority on simplicity, usability, and aesthetics.
- **API Dependency:** Functionality is entirely dependent on the backend APIs.
- **State Management:** Appropriate frontend state management solution required.
- **Security:** Implement standard frontend security practices (e.g., handling tokens securely, input sanitization where applicable though most validation is backend).

---

## 🔗 Dependencies

- Consumes all defined backend APIs (`Authentication`, `Patient Data`, `Weight Tracking`, `Medication`, `Side Effects`, `Educational Content`, `Chat Agent`, `Clinician Portal`).
- Requires a defined UI/UX design system or style guide.
- Requires React development environment (Node.js, npm/yarn, potentially Vite or Create React App) and build tools.