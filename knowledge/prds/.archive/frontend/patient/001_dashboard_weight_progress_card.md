# Dashboard Weight Progress Card

## Source
docs/.xgen/prds/patient_ui_prd.md

## Status
Draft

## Owner
<PERSON>

## Area
frontend

## Version
v1

## Last Updated
2025-04-11

---

## Purpose
Implement a specialized dashboard card that displays the patient's weight progress, including a visual trend (chart) and quick access to the full Weight Tracking page.

## Requirements
- Show most recent weight entry and date
- Display a mini trend chart (last 7–30 days)
- Provide CTA to WeightTrackingPage
- Responsive and accessible design

## Constraints
- Data must be scoped to authenticated patient and clinic
- Use REST API `/api/v1/patients/self/weight-log`
- Follow accessibility and performance guidelines

## Dependencies
- Weight log API and data model must exist
- Clerk authentication and RBAC enforced

## Acceptance Criteria
- Card appears on dashboard for all patients
- Shows latest weight and trend
- Clicking CTA navigates to WeightTrackingPage
- Passes accessibility checks