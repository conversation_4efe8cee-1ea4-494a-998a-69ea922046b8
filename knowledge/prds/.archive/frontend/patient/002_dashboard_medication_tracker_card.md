# Dashboard Medication Tracker Card

## Source
docs/.xgen/prds/patient_ui_prd.md

## Status
Draft

## Owner
<PERSON>

## Area
frontend

## Version
v1

## Last Updated
2025-04-11

---

## Purpose
Implement a dashboard card that summarizes current medications and provides quick access to request refills.

## Requirements
- Display current medications (names, dosages)
- Show pending/approved/rejected refill requests
- CTA to MedicationRequestPage

## Constraints
- Data scoped to authenticated patient and clinic
- Use `/api/v1/patients/self/medication-requests`
- Follow UI/UX and accessibility standards

## Dependencies
- Medication request API and data model
- Clerk authentication

## Acceptance Criteria
- Card displays medication summary and request statuses
- CTA navigates to MedicationRequestPage
- Passes accessibility checks