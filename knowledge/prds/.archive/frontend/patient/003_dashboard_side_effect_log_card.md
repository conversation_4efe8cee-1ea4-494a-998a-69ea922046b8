# Dashboard Side Effect Log Card

## Source
docs/.xgen/prds/patient_ui_prd.md

## Status
Draft

## Owner
<PERSON>

## Area
frontend

## Version
v1

## Last Updated
2025-04-11

---

## Purpose
Implement a dashboard card that shows the patient's most recent side effect log and provides a CTA to report a new symptom.

## Requirements
- Display last side effect entry (description, date, status)
- CTA to SideEffectPage for new report

## Constraints
- Data scoped to authenticated patient and clinic
- Use `/api/v1/patients/self/side-effects`
- Accessibility and compliance disclaimers

## Dependencies
- Side effect log API and data model
- Clerk authentication

## Acceptance Criteria
- Card displays latest side effect log
- CT<PERSON> navigates to SideEffectPage
- Passes accessibility and compliance checks