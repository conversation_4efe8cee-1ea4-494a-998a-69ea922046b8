# Side Effect Photo Upload

## Source
docs/.xgen/prds/patient_ui_prd.md

## Status
Draft

## Owner
<PERSON>

## Area
frontend

## Version
v1

## Last Updated
2025-04-11

---

## Purpose
Allow patients to optionally upload a photo when reporting a new side effect.

## Requirements
- Add photo upload field to side effect report form
- Support image preview and removal before submission
- Upload to secure storage (e.g., Azure Blob Storage)
- Display uploaded photo in history

## Constraints
- File size/type restrictions
- Data privacy and security (HIPAA/GDPR)
- Use secure upload API

## Dependencies
- Backend support for photo upload and retrieval
- Clerk authentication

## Acceptance Criteria
- Patients can upload, preview, and remove photos
- Photos are securely stored and displayed in history
- Passes accessibility and compliance checks