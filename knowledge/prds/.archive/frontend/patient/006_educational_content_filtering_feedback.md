# Educational Content Filtering and Feedback

## Source
docs/.xgen/prds/patient_ui_prd.md

## Status
Draft

## Owner
<PERSON>

## Area
frontend

## Version
v1

## Last Updated
2025-04-11

---

## Purpose
Enhance the educational content page with filtering by type and a feedback ("Helpful?") button.

## Requirements
- Filter content by type (article, video, FAQ)
- Add "Helpful?" feedback button for each item
- Track feedback for analytics

## Constraints
- Data scoped to patient’s clinic
- Use `/api/v1/content/educational`
- Accessibility and performance standards

## Dependencies
- Content API must support content_type
- Clerk authentication

## Acceptance Criteria
- Patients can filter content by type
- Feedback button is present and functional
- Passes accessibility checks