### 💬 `atomic_prd.md`
**Feature:** `Patient Frontend - Chatbot Integration`
**Parent PRD:** master_chat_prd.md
**Last Updated:** 2025-04-05

---

## 🎯 Purpose

To define the user interface components and logic within the Patient Frontend application that allow authenticated patients to interact with the chatbot specific to their associated clinic.

---

## ✨ Features

| Feature                  | Description                                                                                                                                 |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- |
| Chat Interface           | A UI component displaying the conversation history (user messages and chatbot responses).                                                     |
| Message Input            | A text input field and send button for the patient to type and submit their messages/questions to the chatbot.                                |
| API Integration          | Logic to send the user's message to the backend Chat API endpoint (`api/chat_service.md`) associated with the patient's clinic.               |
| Response Display         | Logic to receive the chatbot's response from the API and display it in the chat interface.                                                    |
| Disclaimer Display       | Ensure the chatbot disclaimer (e.g., "I cannot provide clinical advice") is clearly visible within or near the chat interface.                |
| Loading/Error States     | Visual indicators for when the chatbot is processing a response and clear error messages if the API call fails.                               |
| Clinic Context Awareness | The chat interface must implicitly use the patient's associated clinic ID when communicating with the backend Chat API.                       |

---

## ✅ Acceptance Criteria

- ✅ An authenticated patient, associated with a specific clinic, can access the chat interface within the patient frontend.
- ✅ The patient can type a message into the input field and click 'Send'.
- ✅ The message is successfully sent to the correct backend Chat API endpoint for the patient's clinic.
- ✅ A loading indicator is displayed while waiting for the chatbot's response.
- ✅ The chatbot's response is received from the API and displayed correctly in the chat history.
- ✅ The disclaimer stating the chatbot does not provide clinical advice is clearly visible.
- ✅ Appropriate error messages are displayed if sending the message or receiving the response fails.
- ✅ The chat interface is user-friendly and accessible.
- ✅ Patients cannot access or interact with chatbots from clinics they are not associated with.

---

## ⛓️ Constraints

- The UI must clearly distinguish between user messages and chatbot responses.
- Must handle potentially long responses or messages gracefully (e.g., scrolling).
- Requires integration with the frontend's authentication state to get the user's token and associated clinic ID.
- UI performance should remain acceptable even with a long chat history.

---

## 🔗 Dependencies

- **Clinic-Specific Chatbot Service API (`api/chat_service.md`):** The backend endpoint this frontend view interacts with.
- **Authentication Service / Frontend Auth State:** Provides the patient's authentication token and associated clinic ID.
- **Multi-Tenancy & Access Control Service (`api/chat_multi_tenancy.md`):** Backend logic ensures the patient is authorized for the clinic's chat.
- **Patient Frontend Application:** The parent application hosting this chat interface.
- **Frontend Framework & UI Library:** (e.g., React, Vite, Shadcn/ui)