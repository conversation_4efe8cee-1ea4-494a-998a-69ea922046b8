### 📝 `atomic_prd.md`
**Feature:** `Patient Web Application Frontend`  
**Parent PRD:** master_prd.md, frontend_overview.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the user interface and user experience requirements for the Patient Web Application. This responsive web app allows patients to interact with clinic services, track their health data, communicate via chat, and access educational resources, all through a simple and intuitive interface optimized for mobile devices.

---

## ✨ Features / Views

| Feature/View | Description | Key UI Elements | Consumes API(s) |
|--------------|-------------|-----------------|-----------------|
| **Authentication** | Handles initial access using a magic code. | Input field for magic code, Submit button, Error message display. | `Authentication API` (validate-code) |
| **Dashboard/Home** | Landing page after successful login. Content TBD, potentially summary info or navigation. | Navigation menu/tabs, Summary cards (optional). | (Potentially multiple APIs for summary data) |
| **Weight Tracking** | Allows logging current weight and viewing historical data. | Input field for weight, Log button, Date display, History graph/chart, BMI display. | `Weight Tracking API` (Log, Get History), `Patient Data API` (for height) |
| **Medication Request** | Allows submitting requests for medication refills. | Input field/selector for medication, Submit button, Confirmation message. | `Medication API` (Request Refill) |
| **Side Effect Reporting** | Allows submitting reports of side effects. | Input fields/selectors for description and severity (minor/major), Submit button, Confirmation message. | `Side Effect API` (Submit Report) |
| **Profile Management** | Allows viewing and updating contact info (phone, email) and profile photo. | Display fields for current info, Input fields for editing, Photo upload component, Save button. | `Patient Data API` (Get Self, Update Self) |
| **Educational Content** | Displays static informational content provided by the clinic. | Content display area (rendering Markdown/HTML). | `Educational Content API` (Get Content) |
| **Chat Agent** | Provides an interface for real-time chat with the clinic's AI agent. | Chat message display area (history), Message input field, Send button. | `Chat Agent API` (Send Message, Get History) |
| **General** | Consistent navigation, session handling (logout), display of disclaimers. | Header/Footer, Menu, Logout button, Disclaimer text areas. | `Authentication API` (implicitly for session) |

---

## ✅ Acceptance Criteria

- ✅ User can successfully enter a valid magic code and gain access to the application.
- ✅ User is presented with an intuitive dashboard or home screen upon login.
- ✅ User can navigate easily between different sections (Weight, Meds, Profile, etc.).
- ✅ User can successfully log their weight, respecting the weekly interval enforced by the backend.
- ✅ User can view their weight history presented graphically.
- ✅ User can successfully submit a medication refill request.
- ✅ User can successfully submit a side effect report, specifying severity.
- ✅ User can view and update their profile information (phone, email, photo).
- ✅ User can view the educational content provided.
- ✅ User can send messages to and receive responses from the chat agent, viewing history.
- ✅ All views are fully responsive and display correctly on common mobile screen sizes (iOS/Android).
- ✅ UI elements match the defined design system/style guide for simplicity and aesthetics.
- ✅ Loading indicators are shown during API calls.
- ✅ User-friendly error messages are displayed for API errors or validation issues.
- ✅ Required disclaimers ("not for diagnosis") are clearly visible in relevant sections (e.g., Chat, Side Effects).
- ✅ User can log out of the application.

---

## ⛓️ Constraints

- **Responsiveness:** Must provide an excellent experience on mobile browsers. Desktop view is secondary.
- **Simplicity:** UI must be extremely easy to understand and use ("drop-down simple"). Minimize clutter and complex interactions.
- **API Driven:** All dynamic data and actions rely on backend APIs. Frontend does not contain business logic beyond presentation.
- **Performance:** Application should load quickly and feel responsive, especially on mobile connections (consider bundle sizes, efficient rendering).
- **Accessibility:** Adhere to basic web accessibility standards (WCAG AA where feasible).

---

## 🔗 Dependencies

- Consumes various backend APIs as listed in the Features table.
- Requires a defined UI/UX design system/style guide.
- Requires a chosen frontend framework (React, Vue, etc.).
- Requires frontend state management library.
- Requires a charting library for the weight history graph.

- Requires React framework, Shadcn/ui component library, and Tailwind CSS.