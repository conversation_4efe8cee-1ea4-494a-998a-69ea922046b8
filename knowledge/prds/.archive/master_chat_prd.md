# PRD: Context-Aware, Clinic-Specific Chatbot System

## Title
Clinic-Aware Conversational Agent with Contextual Memory and Role-Based Alignment

## Status
Draft

## Version
v0.2 – 2025-04-11

## Owner
<PERSON>

## Overview
PulseTrack requires a sophisticated, role-sensitive chatbot system that adapts dynamically to the clinic, page context, and individual user history. The system must provide separate chatbot instances per clinic, leveraging scraped public data, admin-injected context, and user interaction history (both clinician and patient).

The chatbot's purpose is **not only to answer questions**, but to function as a proactive **advocate** for both patients and clinicians, guiding them, surfacing risks, reminding, explaining, and reducing confusion.

## Purpose
This PRD extends the original `master_chat_prd.md` by defining technical and contextual enhancements that elevate the chatbot to an intelligent, clinic-specific, role-aware agent.

## Goals
- Support one chatbot per clinic, populated from scraped + injected content.
- Maintain persistent, contextual memory across sessions per patient and clinician.
- Dynamically adjust chatbot behavior and tone based on:
  - User role (patient, clinician)
  - Page-level context (e.g. "Side Effects")
  - Current session state/history
- Support multi-modal context injection (scraped HTML, admin markdown, structured FAQ, patient EHR data)
- Build on the vectorized PG search base, layered with retrieval + message memory
- Ensure secure, multi-tenant, role-specific chatbot access

## Non-Goals
- Real-time voice/chat hybrid
- Multilingual support
- Federated RAG models across clinics
- Replacing human clinical judgment or direct care

## Stakeholders
- Patients: asking for clarifications, help, reminders, side effect explanations
- Clinicians: seeking summaries, guidance, and triage support
- Admins: configuring chatbot behavior/content
- Development team: implement and maintain chatbot logic and integration
- Compliance officer: ensure regulatory alignment

---

## Core Features (Merged)
| Feature | Description |
|--------|-------------|
| Clinic Website Scraping | Backend service automatically scrapes a clinic's website content to form a primary knowledge base. |
| Admin Context Supplementation | Admins can provide additional context (pricing, guidelines, care plans) via input form. |
| Chatbot Knowledge Base | Combined scraped + admin content embedded into PGVector. |
| Clinic-Specific Chatbot Instance | Each clinic has a dedicated RAG chatbot aligned to its knowledge base. |
| Role + Page + History-Aware Context | Chatbot adjusts responses based on user role, current app location, and session history. |
| Multi-Tenant Access Control | Patients and clinicians only access chatbots for associated clinics. |
| Patient Frontend Integration | Patients see only their clinic's chatbot. Context passed automatically via Clerk and route state. |
| Clinician Multi-Clinic Support | Clinicians can access chatbots across associated clinics with scoped memory and permissions. |
| Admin Chat Configuration | Admins can inject content, preview chatbot, and debug context. |
| Disclaimer | Each chatbot displays a prominent disclaimer ("not clinical advice"). |

---

## Contextual Intelligence
### Context Types:
- `PAGE_CONTEXT` – Inferred from route (e.g. `/side-effects`)
- `ROLE_CONTEXT` – Extracted from Clerk token/user role
- `HISTORY_CONTEXT` – From chat + domain activity logs (e.g., medication requests, alerts)
- `CLINIC_CONTEXT` – From vector DB (scraped + injected)

### Prompt Enrichment Strategy:
- Enrich prompts with:
  - Top 3 vector search chunks
  - Role-aware guidance
  - Page intent hints
  - Dynamic inserts: recent side effect logs, appointment proximity, unresolved alerts

### Example Prompt Behavior
- **Patient on Side Effects Page:**
  > "User is on side effects page. They've submitted nausea symptoms and are awaiting clinician review. Inject clinic-provided self-care advice + calming reassurance."

- **Clinician on Medication Request Page:**
  > "Clinician is reviewing semaglutide request. Show past requests, patient alerts, and clinic refill policy. Be concise and clinical."

---

## API Endpoints
- `POST /chat/{clinic_id}/message` – Unified message handling for patient/clinician
- `GET /chat/{clinic_id}/context-preview` – Debug context view
- `GET /chat/{clinic_id}/history/{user_id}` – Message memory per user
- `POST /chat/{clinic_id}/inject` – Admin doc injection

---

## UI Requirements
### Patient / Clinician Portals
- Slide-in or anchored chatbot panel
- Context-anchored dynamic suggestions ("Ask about side effects" on side effects page)
- Visual feedback (typing indicator, references shown if used)
- Support markdown + error recovery

### Admin Portal
- Upload content form (Markdown, HTML, text)
- Context preview (see embedded chunks)
- Live chatbot tester (impersonate patient/clinician)

---

## Technical Constraints
- Must comply with HIPAA/GDPR
- Must respect patient-clinician-clinic relationships (Clerk roles + metadata)
- Chatbot cannot offer clinical guidance or diagnosis
- Must fallback gracefully if vector DB fails

---

## Future Phases
1. Daily proactive messages ("Hey, don’t forget to log weight today!")
2. Patient-aware summarization view ("What your clinic chatbot knows about you")
3. Admin chatbot override tool (editable scripted answers)
4. Push-to-notification tie-in
5. Adaptive tone based on sentiment analysis

---

## Milestones
1. **v1**: Clinic-specific RAG chatbot scoped to role + screen
2. **v2**: Add history and memory persistence
3. **v3**: Admin override panel, context preview
4. **v4**: Real-time nudge/chatbot-prompted reminders

---

## Acceptance Criteria (Blended)
- ✅ Admin submits URL and supplemental context
- ✅ Backend scrapes + embeds clinic website
- ✅ Admin content is injected and searchable
- ✅ Chatbot answers based on merged context
- ✅ Patients/Clinicians access only their clinic chatbots
- ✅ Role/page/history awareness is reflected in prompts
- ✅ Admin can preview/chatbot and review current context
- ✅ System shows disclaimer: "not clinical advice"
- ✅ Access control enforced across all chat endpoints

