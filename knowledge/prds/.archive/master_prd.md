# Product Requirements Summary

This document has been updated to include:
 • Detailed changes to the medications API (new table schema and extended fields in medication requests)
 • Extended appointment model requirements (including duration, cancellation workflows)
 • Enhanced side effect reporting (with clinician linkage and additional timestamps)
 • Patient and clinician profile updates (including invitation fields and clerk_id conversion)
 • Integration of RAG pipeline features (scraped_pages, content_chunks, and vector search)
