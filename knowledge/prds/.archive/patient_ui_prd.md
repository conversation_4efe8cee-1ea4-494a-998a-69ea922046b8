# PRD: PulseTrack Patient Portal MVP

## Title
PulseTrack Patient Portal – Phase 1 (Core Health Tools + Educational UX)

## Owner
<PERSON>  
## Status
Draft  
## Version
v1.0 – 2025-04-11

---

## 🎯 Purpose
To empower patients with a calming, intuitive, mobile-first experience where they can:
- Log and track weight, symptoms, medications
- View past and upcoming appointments
- Learn from clinician-assigned content
- Ask questions (via integrated chat – Phase 2)
- Stay informed via alerts (Notifications – Phase 3)

The patient portal aims to reduce confusion, foster self-awareness, and become the patient’s trusted interface to their clinical care journey.

---

## 👥 Stakeholders
| Role           | Interest |
|----------------|----------|
| Patients       | Primary users, track their health and communicate |
| Clinicians     | Review logs, assign content, answer questions     |
| Admins         | Associate patients with clinics, manage onboarding |
| Product/Dev Team | Implement UI, enforce secure data access        |
| Compliance Officer | Ensure HIPAA/GDPR alignment                  |

---

## ✅ Core MVP Features (Phase 1)

### 1. **Personalized Dashboard**
- “👋 Hello, [First Name]!” greeting
- Optional motivational message
- Smart Cards:
  - **Weight Progress** (log + visual trend)
  - **Medication Tracker** (current + request refills)
  - **Side Effect Log** (last entry + new report CTA)
  - **Next Appointment** (view/reschedule)
  - **Learning Content** (clinician-assigned)
- Persistent bottom navigation (Home, Log, Learn, Profile)

### 2. **Weight Tracking**
- Log weight with date
- View trends over time
- Edit/delete past entries
- UI: PatientWeightProgressCard, WeightTrackingPage

### 3. **Medication Requests**
- Request new refills
- View pending/approved/rejected requests
- Clinician notes (if applicable)
- UI: PatientMedicationTrackerCard, MedicationRequestPage

### 4. **Side Effect Logging**
- Report a new symptom (free text, severity, optional photo)
- View previous logs
- Status (submitted, reviewed, resolved)
- UI: PatientSideEffectLogCard, SideEffectPage

### 5. **Appointments**
- View upcoming and past appointments
- See details: time, clinician, type, notes
- Optionally reschedule or cancel (configurable)
- UI: PatientNextAppointmentCard, Appointments integration

### 6. **Educational Content**
- View learning resources from clinician
- Filter by type: articles, video, FAQs
- Optional feedback button (“Helpful?”)
- UI: PatientLearningContentCard, EducationPage

---

## 🔜 Phase 2 Placeholder: AI Chat (Not Yet Implemented)
> This section is reserved for intelligent, context-aware AI chat support.

**Future capabilities:**
- Ask questions about medications, symptoms, appointments
- Get explanations on logged side effects
- Receive reminders/nudges from chatbot
- Role + page-aware answers
- Slide-in UI on all key pages

---

## 🔜 Phase 3 Placeholder: Notifications System
> This will support real-time and historical messages for patients.

**Planned examples:**
- “Clinician has reviewed your side effect log”
- “Refill approved”
- “Appointment reminder – tomorrow at 2:30PM”
- Toasts + persistent inbox view

---

## 🎨 UI/UX Guidelines
- Mobile-first
- Calming color palette
- Single CTA per screen
- Spacing optimized for low cognitive load
- Large tap targets + accessibility compliance
- Bottom navigation for persistent access
- Emphasize visual confidence and simplicity over complexity

---

## 🧱 Technical Constraints
- Fully integrated with Clerk (patient role, clinic association)
- Read/write to core models: weight logs, medication requests, side effects, appointments
- All actions scoped to authenticated user and their assigned clinic
- API: Use existing endpoints (`/api/v1/patients/self/...`)
- Must comply with HIPAA/GDPR and pass security review

---

## 📅 Milestones
| Milestone                              | Target |
|----------------------------------------|--------|
| Weight Log MVP                         | ✅     |
| Medication Request MVP                 | ✅     |
| Side Effect Log MVP                    | ✅     |
| Appointment Viewer MVP                 | ✅     |
| Learning Content Viewer MVP            | ✅     |
| Chat Integration (Phase 2)             | 🔜     |
| Notifications System (Phase 3)         | 🔜     |

---

## ✅ Acceptance Criteria
- Patient can view and update their weight logs
- Patient can request medication refills and view status
- Patient can submit and view side effects
- Patient can view educational materials assigned by their clinic
- Dashboard is fully functional with card-based layout and CTAs
- UI loads quickly and works on all mobile browsers
- Patient cannot access data outside of their assigned clinic
- Clerk handles auth and routing
- Placeholder for AI chat is shown but disabled
- Notifications area exists but inactive