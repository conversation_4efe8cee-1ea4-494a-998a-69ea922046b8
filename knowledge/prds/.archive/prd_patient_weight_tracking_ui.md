### 📘 `prd_patient_weight_tracking_ui.md`
**Project Name:** Patient Weight Tracking UI
**Owner:** <PERSON><PERSON><PERSON><PERSON>
**Last Updated:** 2025-04-05

---

## 🧠 Purpose

To enhance the patient application by providing a user-friendly interface for patients to manually log their weight entries, select the date of measurement, choose units (lbs/kg), and view their historical weight data in both list and graphical formats. This supports patient engagement and provides valuable data for clinicians.

---

## 🛡️ Core Features

| Feature             | Description                                                                 |
|---------------------|-----------------------------------------------------------------------------|
| Weight Entry Form   | Allows users to input weight, select date, and choose units (lbs/kg).       |
| Weight History List | Displays a chronological list of all previously entered weight logs.        |
| Graph Integration   | Ensures the existing weight history graph updates with new entries.         |

---

## ✅ Acceptance Criteria

- ✅ User can access the weight tracking page within the patient application.
- ✅ User can select a specific date for a new weight entry using a date picker.
- ✅ User can input a numerical weight value.
- ✅ User can select either 'lbs' or 'kg' as the unit for the weight entry via radio buttons or similar selector.
- ✅ User can submit the new weight entry.
- ✅ Upon submission, the new weight entry appears in the Weight History List, typically sorted newest to oldest.
- ✅ The existing Weight History Graph dynamically updates to reflect the newly added weight entry.
- ✅ The interface is intuitive and aligns with the existing application's design system (Shadcn/ui).
- ✅ Weight entries are successfully saved via the backend API (`POST /api/patients/me/weight-log`).
- ✅ Historical weight entries are correctly fetched and displayed from the backend API (`GET /api/patients/me/weight-log`).

---

## 🚫 Non-Goals

- Automatic import of weight data from smart scales or other devices.
- Advanced weight trend analysis or prediction features beyond the basic graph.
- Setting weight goals or targets within this specific interface.
- Clinician-facing alerts triggered directly by weight entries (this would be a separate feature).
- Editing or deleting past weight entries in this initial version.

---

## 👥 Stakeholders

- **Patients** (Primary Users)
- **Clinicians** (Viewers of patient-reported data)
- **Development Team** (Implementers)
- **Product Owner** (Definer of requirements)

---

## 🧹 Constraints

- Must be implemented within the existing `frontend-patient` React application.
- Must integrate with the component located at `frontend-patient/src/pages/WeightTrackingPage.tsx`.
- Must utilize the existing backend API endpoints for weight logging (`POST /api/patients/me/weight-log`) and retrieval (`GET /api/patients/me/weight-log`).
- UI components should adhere to the established Shadcn/ui design system and project's styling guidelines.
- Date selection should be user-friendly and handle different locales/formats gracefully if possible, but default to standard formats initially.

---

## 🔼 Priority

| Area                  | Priority |
|-----------------------|----------|
| Weight Entry Form     | High     |
| History List Display  | High     |
| Graph Integration     | High     |
| Unit Selection (lbs/kg)| High     |
| Date Selection        | High     |