### 📝 `atomic_prd.md`
**Feature:** `Project Setup & Foundation`  
**Parent PRD:** master_prd.md
**Last Updated:** 2025-03-31  

---

## 🎯 Purpose

Define the requirements and tasks for establishing the initial project structure, development environment, tooling, and core service configurations for Codename Pulsetrack, based on the selected technology stack (FastAPI, React/Shadcn/Tailwind, PostgreSQL).

---

## ✨ Features / Setup Tasks

| Task | Description | Key Tools/Commands |
|------|-------------|--------------------|
| **Repository Setup** | Initialize Git repository(ies) for source code management. | `git init` |
| **Docker Environment** | Configure Docker for local development orchestration using Docker Compose. Define services for backend (FastAPI), frontend (React build/serve), and Supabase local dev. | `Dockerfile`, `docker-compose.yml`, `supabase start` |
| **Backend Initialization (FastAPI)** | Set up the Python virtual environment, install core dependencies, create initial FastAPI application structure with health check. | `python -m venv`, `pip install fastapi uvicorn[standard] python-dotenv firebase-admin psycopg2-binary` (or `asyncpg` for async), `main.py` |
| **Frontend Initialization (React)** | Initialize React project(s), install UI libraries (Tailwind, Shadcn), configure Supabase client library. | `npx create-react-app` or `npm create vite@latest`, `npm install tailwindcss firebase`, `npx tailwindcss init`, `npx shadcn-ui@latest init` |
| **Firebase Setup (Clinician Auth)** | Configure Firebase project, enable Email/Password Authentication, obtain service account key. | Firebase Console
**PostgreSQL Setup** | Configure connection details for local (Docker) and production PostgreSQL instances. Plan for schema migration tool (e.g., Alembic). | `psql`, Alembic (or other migration tool) |
| **CI Pipeline Setup** | Configure basic Continuous Integration workflows (e.g., GitHub Actions) for linting and potentially initial tests. | `.github/workflows/ci.yml` (example), `ruff`, `eslint` |
| **Secret Management (Initial)** | Establish method for managing secrets (Supabase keys, JWT secret) locally (e.g., `.env` file) and plan for secure handling in deployment. | `.env`, `.gitignore` |

---

## ✅ Acceptance Criteria

- ✅ Git repository(ies) are initialized and structured appropriately.
- ✅ Local development environment can be successfully started using `docker-compose up`.
- ✅ Basic FastAPI application runs locally and responds to health checks.
- ✅ Basic React application(s) run locally. Tailwind and Shadcn/ui are configured.
- ✅ PostgreSQL container runs locally and is accessible from the FastAPI container. Initial schema/migrations applied.
- ✅ Firebase Auth is configured. Firebase service account key and PostgreSQL connection details are securely managed locally via `.env`.
- ✅ Basic CI pipeline is configured and successfully runs linters on code pushes/PRs.
- ✅ `.env` files containing secrets are correctly added to `.gitignore`.

---

## ⛓️ Constraints

- **Technology Stack:** Must use FastAPI (Python), React (with Shadcn/ui, Tailwind), and Supabase (PostgreSQL).
- **Containerization:** All application components must run within Docker containers locally.
- **Local Development:** Supabase local development environment (`supabase start`) must be used for database and auth emulation.
- **Configuration:** Secrets must not be committed to Git; use `.env` files locally.

---

## 🔗 Dependencies

- Requires installation of: Git, Docker Desktop (with Docker Compose), Python (>=3.8 recommended for FastAPI), Node.js (LTS version recommended), `psql` client (optional), potentially a DB migration tool CLI (e.g., Alembic).
- Requires accounts for: GitHub (or other Git provider), Firebase.
- Relies on base Docker images (Python, Node).
- Relies on package managers (`pip`, `npm`/`yarn`).