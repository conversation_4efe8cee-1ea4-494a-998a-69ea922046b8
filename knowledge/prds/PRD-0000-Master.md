# HelixForge · Master Product Requirements Document (PRD)

**Version:** 0.3 · **Date:** 2025‑04‑17 · **Authors: <AUTHORS>

---
## 1 · Background & Vision
HelixForge is the next‑gen operational platform for data‑driven clinics. It unifies an OLTP “Operational Core” (real‑time patient‑centric workflows) with an OMOP‑aligned warehouse (“Nexus”) and an optional LLM assist layer—all governed by built‑in compliance (HIPAA, GDPR, ISO 13485) and zero‑drift schema controls.

> **Why now?** Pulsetrack proved the weight‑management vertical but suffered schema drift, auth‑vendor lock‑ins, and manual onboarding. HelixForge re‑architects from the data model outward, designed to scale across specialties and geographies with self‑service subscription, template‑driven setup, and automated billing.

---
## 2 · Goals & Non‑Goals
| Goals                                                                                      | Non‑Goals                                                  |
|--------------------------------------------------------------------------------------------|------------------------------------------------------------|
| • Bake in security & compliance from day 1 (TLS, AES‑256 encryption, column‑level PHI, audit logs, RBAC, RLS) | • Full EMR replacement.                                     |
| • Ship secure MVP supporting multi‑specialty care (e.g., weight, diabetes, cardiology).     | • Building native mobile apps in MVP (web responsive only). |
| • Enable self‑service clinic onboarding via Stripe subscription and templated configuration. | • Hard‑coded vendor lock‑in for auth or billing.            |
| • Provide HL7 v2 & FHIR R4 inbound/outbound interfaces for core resources (appointment export MVP). | • Marketplace for third‑party app plugins (Phase 2+).       |
| • Decouple OLTP schema and Nexus landing tables; defer full OMOP ETL to later phases.      | • Real‑time HL7/FHIR beyond core appointment export (Phase 2+). |
| • Provide clean, versioned JSON APIs with deterministic schemas.                           |                                                            |
| • ETL‑ready data aligned to OMOP v5.4 for analytics & AI (landing only in MVP).            |                                                            |
| • Provide appointment export in FHIR R4 format for EHR interoperability.                   |                                                            |

---
## 3 · Personas
1.  **Clinic Admin (Alice)** – Configures clinic via template, manages subscription and billing, audits logs.
2.  **Clinician (Dr. Ben)** – Creates medication requests, tracks vital signs, reviews dashboards across specialties.
3.  **Patient (Chris)** – Views care plan, logs weight or specialty‑specific metrics, opts into AI coaching.
4.  **Integrator (Ian)** – Pulls de‑identified data into BI tools via Nexus warehouse.
5.  **Billing Manager (Blake)** – Monitors invoices, updates payment methods, views subscription status.
6.  **Caregiver (Cam)** – Authorized to view patient data and receive alerts on behalf of patients.

---
## 4 · User Journeys (MVP focus)
1.  *Self‑Subscribe & Onboard* → Clinic Admin signs up → selects specialty template → enters payment details via Stripe → system auto‑populates clinic defaults.
2.  *Invite Clinician* → Clinic Admin invites via email → user clicks magic link → account created with proper role & specialty access.
3.  *Register Patient* → Clinician or Admin enters demographics → patient receives portal link for logging and chat.
4.  *Create Medication Request* → Clinician selects drug, dose, intent under specialty context → patient notified in portal.
5.  *Track Specialty Metrics* → Patient records weight, blood glucose, or other vital signs → dashboard auto‑updates.
6.  *Chat Coaching* (feature‑flag) → Patient engages LLM for specialty‑tailored coaching; responses stored auditably.
7.  *Alerting & Notifications* → System evaluates alert rules (e.g., high blood pressure, missed medication) in real‑time; sends notifications to clinicians, patients, and caregivers via email/SMS/in-app.
8.  *Upload Document* → Clinicians/Admins/Caregivers upload PDFs (e.g., care plans, consent forms) against entities (patient, clinic, clinician); documents stored in blob storage with metadata linked to records.
9.  *Algorithm Monitoring* → Nexus Algorithm Engine runs background algorithms (via University of St Andrews algorithm factory); matches patient data patterns in real‑time and logs `algorithm_run` results; triggers `alert_rule` and documents to `notification_event` and `document` tables.
10. *Shared Decision & Advocacy* → Clinicians and patients collaborate via low‑code rule‑builder UIs for care plan customization, drag‑and‑drop report designers for personalized insights, and educational modules to support shared decision‑making.

---
## 5 · Functional Requirements
### 5.1 Data Model (see ADR‑0003)

#### 5.1.1 Core Operational Tables
| Table                | Key fields                                                                                   |
|----------------------|----------------------------------------------------------------------------------------------|
| `clinic`             | id, name, tz, npi, template_id, subscription_status                                          |
| `actor`              | id, clinic_id, type, email, name, status                                                     |
| `patient_profile`    | actor_id, dob, sex_at_birth, mrn                                                             |
| `clinician_profile`  | actor_id, license_number, specialty_code                                                     |
| `appointment`        | id, clinic_id, patient_id, clinician_id, scheduled_start/end, status                         |
| `medication_request` | id, clinic_id, patient_id, requested_by_id, drug_code, dose_text, status                     |
| `vital_sign`         | id, clinic_id, patient_id, type_code, value, unit, measured_at                               |
| `subscription`       | id, clinic_id, stripe_subscription_id, plan_code, status, started_at, expires_at             |
| `template`           | id, name, description, default_settings JSONB                                                |
| `alert_rule`         | id, clinic_id, name, criteria JSONB, recipients JSONB (roles), enabled BOOLEAN               |
| `notification_event` | id, clinic_id, alert_rule_id, actor_id, patient_id, channel (email/SMS/in-app), payload JSONB, sent_at, status |
| `document`           | id, clinic_id, uploaded_by_id, entity_type, entity_id, file_uri, mime_type, title, description, uploaded_at, status |
| `algorithm_definition`| id, clinic_id, name, description, version, script_uri, parameters JSONB, enabled BOOLEAN |
| `algorithm_run`      | id, clinic_id, algorithm_id, actor_id, patient_id, run_at, input_payload JSONB, status       |
| `algorithm_result`   | id, clinic_id, run_id, result_payload JSONB, created_at                                      |
| `event_log`          | id, clinic_id, actor_id, event_type, payload_json, inserted_at                               |

#### 5.1.2 Reference Data & Lookup Tables
| Table                     | Purpose                                                                          | Source                                                        |
|---------------------------|----------------------------------------------------------------------------------|---------------------------------------------------------------|
| `concept`                 | Standardized concept entries across domains (LOINC, SNOMED CT, RxNorm, ICD‑10) | OMOP Vocabulary v5.4 extracts loaded via ETL                  |
| `vocabulary`              | Metadata about supported code systems                                            | OMOP Vocabulary                                               |
| `concept_relationship`    | Relationships among concepts (e.g., maps to source codes)                        | OMOP Vocabulary                                               |
| `code_system`             | Defines custom internal code systems for enums (e.g., subscription plans)        | Internal enumeration maintained in `template.default_settings` |
| `appointment_status_enum` | Lookup for valid values of `appointment.status` (booked, arrived, fulfilled, cancelled) | FHIR Appointment.status values                                |
| `measurement_unit_enum`   | Allowed units and unit codes for `vital_sign` entries                            | Derived from LOINC unit mappings                              |
| `timezone_catalog`        | Supported IANA time zones for clinics and users                                  | IANA Time Zone Database                                       |
| `specialty_catalog`       | Catalog of supported medical specialties with codes and hierarchies              | Industry standard sources (e.g., SNOMED CT, CPT categories) |
| `language_catalog`        | Supported UI and content languages (e.g., en-US, fr-FR)                          | IETF BCP 47 language tags                                     |
| `localization`            | Key-value translations for UI strings and templates                              | Internal translation management                               |

#### 5.1.3 Master Data Management (MDM) (Phase 2+)
| Table           | Purpose                                                             | Source                       |
|-----------------|---------------------------------------------------------------------|------------------------------|
| `golden_record` | Consolidated view of entities (patients, clinicians) across duplicates | Data normalization processes |
| `entity_match`  | Tracks matching and merge/purge history for records                 | MDM reconciliation workflows |

### 5.2 API Endpoints (Phase 0)
- `POST /subscriptions` · `GET /subscriptions/{id}` · `PATCH /subscriptions/{id}`
- `GET /templates` · `POST /clinics` (auto‑creates `subscription` & `clinic` via template)
- `POST /clinics/{id}/activate` (trigger TEMPLATE apply)
- Existing CRUD: patients, medication-requests, vital-signs, appointments, dashboards
- `GET /appointments/{id}/fhir` → export appointment as FHIR R4 `Appointment` resource
- `POST /alert-rules` · `GET /alert-rules` · `PATCH /alert-rules/{id}` · `DELETE /alert-rules/{id}`
- `POST /documents` → multipart/form-data upload PDF with `{entity_type, entity_id, title, description}`
- `GET /documents/{id}` → download document PDF and metadata
- `GET /entities/{entity_type}/{entity_id}/documents` → list attachments for an entity
- `POST /algorithms` → create or update `algorithm_definition`
- `GET /algorithms` → list available algorithms
- `POST /algorithms/{id}/run` → trigger an immediate algorithm execution
- `GET /algorithms/{id}/runs` → list past algorithm runs and statuses
- `GET /algorithms/runs/{run_id}/results` → fetch algorithm output

### 5.3 Core Backend Services
| Service          | Description                                                                                                                                                                                          |
|------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Document Service | Manages PDF uploads/downloads via Blob Storage; metadata in `document` table; supports tagging and access control.                                                                                   |
| Algorithm Engine | Executes background analytics algorithms from University of St Andrews; uses `algorithm_definition` and `algorithm_run` tables; triggers alerts and document outputs via `notification_event` and `document`. |
| LLM Service      | GPT‑4o chat interface via `/chat` endpoint; maps NL to API actions, enriches context, and logs interactions.                                                                                           |

### 5.4 Nexus Data Warehouse (Phase 1)
**Purpose:** Provide a landing-only analytics schema for MVP, with full OMOP ETL deferred to subsequent phases. ETL pipeline will feed downstream BI and AI workloads.

#### 5.4.1 Landing Schema
| Table                       | Source             | Description                                                              |
|-----------------------------|--------------------|--------------------------------------------------------------------------|
| `landing.raw_events`        | `event_log`        | Raw JSONB ingest of `event_log.payload_json` with `inserted_at` timestamp |
| `landing.raw_vitals`        | `vital_sign`       | Raw export of vital sign records for transformation                      |
| `landing.raw_medreqs`       | `medication_request`| Raw export of medication requests                                        |
| `landing.raw_algorithm_runs`| `algorithm_run`    | Raw export of algorithm executions and statuses for analytics/monitoring |
| `landing.raw_algorithm_results`| `algorithm_result`| Raw export of algorithm output payloads for downstream processing        |

#### 5.4.2 Staging & OMOP Alignment
| Table                   | OMOP Target       | Notes                                                                                             |
|-------------------------|-------------------|---------------------------------------------------------------------------------------------------|
| `staging.drug_exposure` | `drug_exposure`   | Transform `raw_medreqs` → OMOP fields: `drug_concept_id`, `dose_amount`, `route_concept_id`, `visit_occurrence_id` |
| `staging.measurement`   | `measurement`     | Transform `raw_vitals` → OMOP LOINC concept IDs, units, measurement_datetime                      |

#### 5.4.3 Orchestration & Tools
- **Airflow DAG**: orchestrates daily extract from OLTP → load into `landing.*` → transforms into `staging.*` → loads into Nexus schema (`omop.drug_exposure`, `omop.measurement`).
- **Schema Versioning**: use Alembic in separate `analytics/` repo; tag by data‑version (v0.1).
- **Security & Access**: read‑only roles for BI; row‑level filters on `omop` tables by `clinic_id`.

### 5.5 LLM Assist Layer (Phase 0)
**Purpose:** Provide a unified LLM‑driven chat interface that can perform and facilitate all user journeys across personas.

#### 5.5.1 Chat API
- `POST /chat` → body: `{ actor_id, clinic_id, message, context? }` returns: `{ message_id, response, actions? }`

#### 5.5.2 Core Capabilities
- **Intent Mapping:** Translates natural language into REST API calls (e.g., `create medication_request`).
- **Context Enrichment:** Retrieval over `event_log`, `patient_profile`, and `template.default_settings` for accurate prompts.
- **Multi‑Persona Flows:** Adapts phrasing, available actions, and permissions per role (Patient, Clinician, Caregiver, Billing Manager).

#### 5.5.3 Architecture
- **LLM Engine:** GPT‑4o with a system prompt auto-generated from the OpenAPI spec at build time.
- **Middleware:** Intercepts `/chat` calls; logs raw and masked prompts, responses to `event_log` and `notification_event`.
- **Plugin Modules:** Specialty‑specific logic (e.g., diabetes, cardiology) loaded dynamically via `nexus.llm_config` table.
- **Knowledge Base:** Qdrant clinical Q&A store (250k+ entries) served via a vector retrieval layer to boost LLM prompt relevance and achieve 97% USMLE accuracy.

---
## 6 · Enterprise & Global Scale Requirements (Phase 2+)
To support millions of patients and global enterprises, the platform must include:

### 6.1 Identity & Access Management
- **Enterprise SSO / Federation:** SAML 2.0, OIDC, SCIM for user provisioning and deprovisioning.
- **Fine‑Grained RBAC/ABAC:** Attribute‑based policies layered on RLS.
- **Multi‑Tenant Isolation:** Strict separation at auth and data layers.

### 6.2 Global Deployment & Resilience
- **Multi‑Region Clusters:** Active‑active or active‑passive with automated failover and failback.
- **Disaster Recovery:** Cross‑region backups, defined RPO/RTO targets, and regular DR drills.
- **Auto‑Scaling:** Horizontal scaling for peak loads (billing spikes, analytic jobs).

### 6.3 API Management & Versioning
- **API Gateway:** Rate limiting, quotas, and API key management.
- **Semantic Versioning:** Deprecation policy, canary releases, backward compatibility.
- **Developer Portal:** Interactive docs, sandbox environments, and SDK generation.

### 6.4 Infrastructure as Code & GitOps
- **IaC:** Terraform/ARM/Helm for networks, IAM, DBs, ingress.
- **GitOps Workflows:** Environment promotion (dev→staging→prod) with pull‑based deployments.
- **Immutable Artifacts:** Signed container images and artifact registries.

### 6.5 Observability & SRE Practices
- **Distributed Tracing:** End‑to‑end OpenTelemetry instrumentation.
- **SLIs/SLOs:** Error budgets, automated alerting, and burn‑rate analysis.
- **On‑Call & Runbooks:** Defined incident response processes and retrospectives.

### 6.6 Compliance & Certification
- **SOC 2 / HITRUST:** Roadmap for audits and attestations.
- **Data Residency Controls:** Per‑region data storage and processing.
- **KMS/HSM:** Managed key rotation and encryption in transit and at rest.

### 6.7 Data Governance & Lineage
- **Metadata Catalog:** Automated discovery of tables, ETL pipelines, and data models.
- **Policy‑Driven Retention:** Configurable purge workflows to satisfy ‘right to be forgotten.’
- **Lineage Auditing:** Trace BI metrics back to raw OLTP events.
- **Master Data Management (MDM):** Reconciliation workflows and golden‑record matching to maintain data quality across entities (see 5.1.3).

### 6.8 Localization & Internationalization
- **Locale Services:** Currency, date, number formatting.
- **Time‑Zone Handling:** DST-aware scheduling and per‑clinic timezone configs.
- **Translation Pipelines:** Automated UI string sync and versioned translation files.

### 6.9 Partner & EHR Integrations
- **FHIR Adapters:** Beyond appointments to Observations, MedicationRequests, Patients.
- **HL7 Interfaces:** Batch or streaming imports/exports for legacy systems.
- **Webhook Subscriptions:** Event subscriptions for external workflows.

### 6.10 Enterprise Billing & Finance
- **Multi‑Currency Pricing & Tax:** Support for VAT, GST, and custom tax rules.
- **Usage Metering:** API calls, user seats, storage, algorithm runs.
- **Invoice & Dunning:** Automated invoice generation, credit notes, and retry policies.

### 6.11 Sandbox & Developer Environments
- **Isolated Tenants:** Per‑customer sandboxes with separate data stores.
- **Anonymized Data Sets:** Synthetic or scrubbed data for testing.
- **Automated Teardown:** Cost‑controlled environment lifecycle management.

### 6.12 Support & Customer Success
- **Tiered SLAs:** Defined response times and resolution targets.
- **In‑App Support & Ticketing:** Zendesk/ServiceNow integrations.
- **Onboarding & Training:** Interactive runbooks, video tutorials, and certification.

### 6.13 Security Hardening & Vulnerability Management
- **Regular Pentests:** External and internal security assessments.
- **Dependency Scanning:** Snyk/WhiteSource/OSS scanning in CI.
- **WAF & DDoS Protection:** Cloud WAF rulesets and global edge security.

### 6.14 Performance & Cost Optimization
- **Query Profiling:** Automated index suggestions and slow‑query alerts.
- **Tiered Storage:** Hot/warm/cold storage for OLTP vs historical data.
- **Reserved & Spot Instances:** Cost‑efficient compute strategies.

---
## 7 · Refinements & Action Items (MVP+)
To address identified gaps and ensure enterprise readiness, we will implement:

- **Soft‑Delete & Audit Fields:** Add `created_at`, `updated_at`, `deleted_at`, and `deleted_by` to all PHI tables for proper retention and compliance.
- **Consent Management:** Introduce a new `consent` table to track patient agreements for LLM interactions, data exports, and algorithmic processes; integrate consent checks in each relevant service.
- **Data Erasure Workflows:** Define GDPR right‑to‑be‑forgotten APIs (`DELETE /patients/{id}/erasure`) and background purge jobs, with audit trails.
- **Error & Exception Journeys:** Document and build clear failure‑handling paths for billing failures, FHIR export errors, algorithm run exceptions, and webhook deliveries—complete with retries, alerting, and human‑in‑the‑loop fallbacks.
- **Bulk & Batch APIs:** Provide endpoints for large‑scale imports/exports (e.g., `POST /patients/batch`, `GET /notifications/batch-export`) to meet enterprise data onboarding needs.
- **Governance & ADR Roadmap:** Initiate ADRs covering:
  - Event‑Bus migration to Redis/Kafka
  - Algorithm governance policies and validation processes
  - LLM Policy Enforcement Service for PHI masking and safe‑prompting
  - Feature‑Flag Service to gate new modules dynamically
- **Platform Review Council:** Establish a governance body responsible for approval of new specialty templates, algorithm updates, and LLM policy changes, with documented review cycles.
- **Operational Runbooks:** Create runbooks for incident management (DR drills, on‑call playbooks), alert configuration, and compliance audit preparation, integrated into the CI/CD pipeline.
- **ISO 13485 Traceability Matrix:** Develop a traceability matrix linking PRD requirements, ADR decisions, test cases, and algorithm validation documentation to satisfy design‑control regulations.
- **Synthetic Data & RUM:** Build anonymized data pipelines for sandbox environments and implement Real User Monitoring (RUM) for front‑end performance insights.

---
## 8 · QMS Integration (Phase 2+)
**Purpose:** Interface HelixForge with an external Quality Management System (e.g., University of St Andrews QMS) to support clinical trial readiness, design controls, and CAPA workflows.

| Service            | Description                                                                                                                                  |
|--------------------|----------------------------------------------------------------------------------------------------------------------------------------------|
| **QMS Gateway**    | Bi‑directional API connector to external QMS: sync design documents, CAPAs, audit records; support document approvals and change‑control.      |
| **QMS Document Model** | Extend `document` table with QMS metadata: `qms_id`, `revision`, `approval_status`, `approval_date`.                                        |
| **QMS Event Sync** | Ingest QMS events (e.g., document approval, audit findings) into `event_log` for traceability and compliance.                                  |

### 8.1 User Journey
- *Submit to QMS* → Admin/patient uploads SOP or protocol → API pushes document to QMS and updates `approval_status`.
- *Fetch Approval* → System polls QMS for document sign‑off events → updates local metadata and triggers workflows (e.g., enable algorithm feature).

---
## 9 · Future Epics & Idea Backlog
These are strategic initiatives to avoid cornering the platform and enable global, compliance‑heavy expansions.
1.  **Master Data Management** (MDM) for golden records, deduplication, merge/purge.
2.  **Clinical Decision Support (CDS) Governance** with rule‑authoring, CQL editor, versioned library, clinical review sign‑off.
3.  **Pharmacovigilance & Safety Reporting**: Automated submission to regulators, case management, signal‑detection dashboards.
4.  **Real‑Time Event Streaming**: Migrate to Kafka/Redpanda with consumer lag monitoring and backpressure controls.
5.  **Deep EHR Integration**: HL7 v2 mLLP adapters for legacy systems and batch feeds.
6.  **Mobile & Offline Support**: Native apps with offline data entry, sync conflict resolution, and local encryption.
7.  **Advanced Data Privacy**: Field‑level encryption, DLP scanning, dynamic consent dashboards.
8.  **Chaos Engineering**: Regular fault‑injection and resilience testing.
9.  **Developer & Partner Ecosystem**: Partner sandboxes, app‑market governance, third‑party security review.
10. **Regulatory Submission Library**: 510(k)/CE‑Mark documentation, traceability artifacts, risk analyses.
11. **Clinical Trial Modules**: Protocol workflows, eConsent, REDCap exports.
12. **AI Model Validation & Drift Monitoring**: Bias audits, drift alerts, continuous performance tracking.

*These epics serve as a living backlog; we should track their implementation via dedicated ADRs and roadmap items to prevent refactoring pain.*
