---
id: PRD-0001
title: HelixForge MVP
authors: [<PERSON>, HelixForge Architect AI]
status: Draft
created: 2025-04-17
updated: 2025-04-17
phase: MVP
module: [voice_input, llm_pipeline, notifications, event_log, templates]
adr_links: [ADR-0001, ADR-0002, ADR-0003]
tags: [triage, RAG, Whisper, synthetic_data, facility_scope, schema_orchestration]
---

# HelixForge MVP PRD

**Version:** 0.3 · **Date:** 2025‑04‑17 · **Authors: <AUTHORS>

---

## 1 · Purpose

Deliver a demonstrable MVP of HelixForge as a **schema-driven, template-configured, voice-enabled coordination system**, operating entirely on synthetic data.  
The MVP must validate:
- Event-oriented orchestration (LLM → intent → action → log → notify)
- Multi-facility scoping via RLS (`facility_id`)
- Dynamic behavior controlled via `template.default_settings`

This serves as the foundation for compliant, multi-tenant, prompt-native healthcare operations.

---

## 2 · Core Flows

### 2.1 · Actor Input (Caregiver/Patient)
- Inputs:
  - Audio (Whisper batch)
  - Free-text observation or question
- Backend:
  - `event_log` captures `original_text`, `actor_id`, `facility_id`
  - LLM resolves `intent`, `actions[]` using schema-based resolver
- Output:
  - Executes actions (e.g., `track vital_sign`)
  - Logs outcomes
  - Triggers alert → `notification_event` if matching `alert_rule`

### 2.2 · Document-QA / RAG
- Input: Synthetic actor asks a question
- Backend: LLM over Qdrant + uploaded PDFs from `document` table
- Output: Text answer, `event_log` entry, optional notification

### 2.3 · Clinician Review + Response
- Input: Alerted via Telegram or admin dashboard
- UI shows LLM-suggested structured actions (via schema)
- Output:
  - Executes confirmed action (API)
  - `event_log` entry created
  - Optional message sent via `notification_event`

---

## 3 · Scope & Constraints

### In Scope
- Operational use of `template.default_settings`:
  - prompt schema, notification rules, visible resources
- Schema-driven orchestration:
  - LLM intent resolution → OpenAPI verb/resource execution
- Multi-facility support:
  - ≥2 synthetic `facility` rows
  - All scoped actions/data governed by `facility_id`
- Modules:
  - Whisper (batch)
  - GPT‑4o via OpenAI Dev Tier
  - Qdrant RAG engine
  - Telegram & SMS notification delivery
  - Consent simulation
  - Fully active `event_log` and `alert_rule` system

### Out of Scope
- PHI or real data
- Patient-facing authentication
- Real-time audio input or scheduling APIs
- Production billing hooks

---

## 4 · Key Tables

| Table | Notes |
|-------|-------|
| `facility` | Anchors all scoping, RLS, and template config |
| `actor` | Represents all synthetic users (clinician, caregiver, etc.) |
| `template` | Drives behavior: roles, prompts, alerts, features |
| `event_log` | Canonical audit trail; stores original + resolved context |
| `notification_event` | Channel-aware output from alert system |
| `alert_rule` | JSONB rules on structured actions or messages |
| `document` | Synthetic PDFs, policies, notes used in RAG |
| `consent` | Simulated agreements for prompts, data sharing |

---

## 5 · Success Criteria

- ≤5 min end-to-end loop: upload → resolve → notify
- ≥95% LLM actions mapped to valid `verb/resource`
- ≥90% of alerts resolve to actionable notifications in <10s
- Multi-facility scoping validates RLS: actors only access their facility
- All flows configurable via `template.default_settings`

---

## 6 · Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Schema drift | Schema versioning + `template.default_settings` config (`K-014`) |
| LLM misrouting | Structured intent resolver with schema validator |
| Alert rule overfiring | Rule cooldowns and traceability via `event_log` |
| Whisper failure | Batch retries with human fallback |
| Data leakage across facilities | Enforced `facility_id` + Postgres RLS (`K-002`) |
| MVP rigidity | Prompt schema versioning & dynamic resource flags ensure agility |

---

## 7 · Notes

This MVP codifies:
- `ADR-0001`: Facility-anchored schema
- `K-002`, `K-007`, `K-008`, `K-014` enforcement
- Schema-driven orchestration as non-negotiable
- Template config as the system’s behavioral backbone