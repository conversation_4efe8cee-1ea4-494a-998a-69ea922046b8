---
id: PRD-0002
title: PulseTrack LLM-Driven API Actions
authors: [<PERSON>, <PERSON> 3.7 Sonnet]
status: Draft
created: 2025-04-20
updated: 2025-04-20
phase: Incremental
module: [llm_pipeline, templates, voice_input, event_log]
adr_links: []
tags: [llm, voice, api_actions, templates, authorization]
---

# PulseTrack LLM-Driven API Actions PRD

**Version:** 0.1 · **Date:** 2025‑04‑20 · **Authors: <AUTHORS>

---

## 1 · Purpose

Extend PulseTrack to support **template-driven, LLM-powered action execution** that allows both clinicians and patients to perform authorized API actions through natural language conversations. The system will interpret intentions from voice or text input, validate permissions, and execute the appropriate API operations, creating a more intuitive interface while maintaining security and auditability.

This incremental enhancement serves as a foundation for more natural user interactions while preserving the existing application architecture and security model.

---

## 2 · Core Flows

### 2.1 · Clinician LLM Interaction
- **Inputs:**
  - Voice input (via Whisper)
  - Text-based chat message
- **Processing:**
  - Intent extraction via LLM using clinician-specific templates
  - Parameter identification and validation
  - Permission verification based on clinician role
- **Actions:**
  - Execute API operations (e.g., schedule appointment, add medication request)
  - Generate confirmation response
  - Log action in event_log

### 2.2 · Patient LLM Interaction
- **Inputs:**
  - Voice input (via Whisper)
  - Text-based chat message
- **Processing:**
  - Intent extraction via LLM using patient-specific templates
  - Parameter identification and validation
  - Permission verification based on patient authorization
- **Actions:**
  - Execute limited-scope API operations (e.g., log symptoms, request appointment)
  - Generate confirmation response
  - Log action in event_log

### 2.3 · Template Management
- **Inputs:**
  - Template configuration via admin interface
- **Processing:**
  - Validation of template structure and permissions
  - Version tracking of templates
- **Actions:**
  - Activate templates for specific clinics
  - Apply templates to conversations based on user role

---

## 3 · Scope & Constraints

### In Scope
- Template-driven LLM conversation framework
  - Role-specific prompt templates
  - Intent-to-API action mapping
  - Parameter extraction and validation
- Multi-role support:
  - Clinician actions (appointment scheduling, medication requests, note creation)
  - Patient actions (symptom logging, appointment requests, medication adherence)
- Authorization layer:
  - Role-based permission verification
  - Clinic-scoped action execution
  - Audit logging of all LLM-triggered actions
- Voice input processing:
  - Whisper integration for audio transcription
  - Batch audio processing
- Conversation context management:
  - Maintaining state across multi-turn interactions
  - Parameter collection through follow-up questions

### Out of Scope
- Replacement of existing UI workflows
- Direct LLM access to database (all actions must go through API)
- Complex multi-step workflows requiring approval chains
- Real-time audio processing
- Training of custom LLM models
- Third-party integration actions (outside of existing API capabilities)

---

## 4 · Key Components

| Component | Description |
|-----------|-------------|
| `template` | Stores conversation templates with prompt structures, available actions, and parameters |
| `event_log` | Records all LLM interactions and triggered actions with original input |
| `llm_intent_resolver` | Processes natural language into structured intents and parameters |
| `action_executor` | Validates permissions and executes API actions |
| `conversation_orchestrator` | Manages context and flow across multi-turn interactions |
| `whisper_service` | Handles audio transcription and processing |
| `permission_checker` | Verifies authorization for specific actions by role |

---

## 5 · Example Templates

### 5.1 · Clinician Assistant Template

```json
{
  "name": "clinician_assistant",
  "description": "Template for handling clinician requests and actions",
  "system_prompt": "You are an assistant for healthcare clinicians. Extract intents and parameters from their requests.",
  "actions": {
    "schedule_appointment": {
      "description": "Schedule a patient appointment",
      "required_role": "clinician",
      "parameters": {
        "patient_id": "string",
        "date": "date",
        "time": "time",
        "duration": "integer",
        "appointment_type": "string"
      },
      "api_endpoint": "/api/v1/appointments",
      "method": "POST"
    },
    "create_medication_request": {
      "description": "Create a medication request for a patient",
      "required_role": "clinician",
      "parameters": {
        "patient_id": "string",
        "medication_name": "string",
        "dosage": "string",
        "frequency": "string",
        "duration": "string"
      },
      "api_endpoint": "/api/v1/medication-requests",
      "method": "POST"
    },
    "add_clinical_note": {
      "description": "Add a clinical note to patient record",
      "required_role": "clinician",
      "parameters": {
        "patient_id": "string",
        "note_content": "string",
        "note_type": "string"
      },
      "api_endpoint": "/api/v1/notes",
      "method": "POST"
    }
  }
}
```

### 5.2 · Patient Assistant Template

```json
{
  "name": "patient_assistant",
  "description": "Template for handling patient requests and actions",
  "system_prompt": "You are an assistant for patients. Help them log information and make requests.",
  "actions": {
    "log_symptom": {
      "description": "Log a symptom or side effect",
      "required_role": "patient",
      "parameters": {
        "symptom_name": "string",
        "severity": "integer",
        "onset_time": "time",
        "notes": "string"
      },
      "api_endpoint": "/api/v1/side-effects",
      "method": "POST"
    },
    "log_weight": {
      "description": "Log patient weight measurement",
      "required_role": "patient",
      "parameters": {
        "weight": "number",
        "unit": "string",
        "measured_at": "datetime"
      },
      "api_endpoint": "/api/v1/weight-logs",
      "method": "POST"
    },
    "request_appointment": {
      "description": "Request an appointment with clinician",
      "required_role": "patient",
      "parameters": {
        "preferred_date": "date",
        "preferred_time": "time",
        "reason": "string",
        "clinician_preference": "string"
      },
      "api_endpoint": "/api/v1/appointment-requests",
      "method": "POST"
    }
  }
}
```

---

## 6 · Authorization & Security

### 6.1 · Permission Model
- All actions are gated by role-based permissions
- Clinicians can only perform actions on patients in their clinics
- Patients can only perform actions on their own records
- All actions must pass through existing API authorization checks
- Additional permission layer for LLM-triggered actions

### 6.2 · Audit & Compliance
- All original inputs (voice/text) are logged
- Extracted intents and parameters are recorded
- API actions triggered by LLM are linked to the original request
- Full traceability from input to action
- Review capabilities for compliance and quality assurance

---

## 7 · Implementation Approach

### 7.1 · Phase 1: Foundation
- Create template data model and storage
- Implement LLM intent resolver with basic templates
- Build action executor with permission checks
- Integrate with existing audit logging
- Support text-based input only

### 7.2 · Phase 2: Voice & Enhanced Actions
- Integrate Whisper for voice transcription
- Add conversation orchestrator for multi-turn interactions
- Expand template library for common clinical workflows
- Implement parameter validation and error handling

### 7.3 · Phase 3: Advanced Features
- Add template management interface in admin portal
- Implement version control for templates
- Add analytics for LLM action success rates
- Create customization options for clinics

---

## 8 · Success Criteria

- **Usability:** ≥90% of intended actions correctly identified from natural language
- **Efficiency:** ≥30% reduction in time for common tasks via LLM vs. traditional UI
- **Security:** 100% adherence to permission model with no unauthorized actions
- **Adoption:** ≥50% of clinicians using LLM interactions within 3 months
- **Accuracy:** ≥95% of executed actions match user intent
- **Satisfaction:** ≥4/5 user satisfaction rating for LLM interactions

---

## 9 · Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| LLM misinterpretation | Intent confirmation before action execution; parameter validation |
| Security vulnerabilities | Multi-layer permission checks; separation from direct data access |
| User confusion | Clear feedback on available actions; help guidance in conversations |
| Performance issues | Asynchronous processing; optimized templates; caching |
| Privacy concerns | Data minimization in prompts; PHI removal from logs |

---

## 10 · Integration with Existing Features

This feature enhances but does not replace existing functionality:

- **Chat Integration:** Extends current chat interface with action capabilities
- **API Layer:** Uses existing API endpoints with no structural changes
- **Auth System:** Builds on current authorization model
- **Clinic Scoping:** Maintains clinic isolation for all actions
- **Audit:** Enhances current logging with more detailed records

---

## 11 · Example Interactions

### Clinician Interaction
```
Clinician: "Schedule a follow-up appointment with John Smith next Tuesday at 2pm for 30 minutes."

System (LLM): "I'll schedule that appointment. To confirm, you want to book a 30-minute follow-up with John Smith on Tuesday, April 29th at
2:00 PM. Is that correct?"

Clinician: "Yes, that's right."

System (LLM): "Great! I've scheduled the appointment. John Smith is now booked for a 30-minute follow-up on Tuesday, April 29th at 2:00 PM. 
Would you like me to send him a notification?"
```

### Patient Interaction
```
Patient: "I need to log my weight from this morning."

System (LLM): "I can help you log your weight. What was your measurement today?"

Patient: "I weighed 185 pounds."

System (LLM): "Thanks! I'll record that you weighed 185 pounds today, April 20th. Is that correct?"

Patient: "Yes."

System (LLM): "Great! I've logged your weight as 185 pounds for today. Your weight chart has been updated. Your next scheduled weigh-in is in 
3 days."
```

---

## 12 · Next Steps

1. Review PRD with stakeholders and technical team
2. Define detailed technical specifications
3. Prioritize actions for initial implementation
4. Create template schema and validation rules
5. Begin development of core components
