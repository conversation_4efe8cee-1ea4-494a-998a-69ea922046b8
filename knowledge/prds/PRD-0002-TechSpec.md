{
  `path`: `D:\\_ProjectsLocal\\pulsetrack\\knowledge\\prds\\TechSpec-PRD-0002.md`,
  `content`: `# Technical Specification for PRD-0002: LLM-Driven API Actions

**Version:** 0.1  
**Date:** 2025-04-20  
**Author:** Claude 3.7 Sonnet  
**Status:** Draft

## 1. Overview

This technical specification outlines the implementation details for PRD-0002, which adds template-driven LLM-powered API action execution to PulseTrack. The implementation will build upon existing components while adding new capabilities to enable voice and text input processing with intent recognition and API action execution.

## 2. Architecture Overview

The enhanced architecture will introduce several new components while extending existing ones:

![Architecture Diagram]

```
User Input (Text/Voice) → Intent Resolver → Action Executor → API Operation → Response
      ↑                       ↑                  ↑                 ↓
Template System ←────────────┘                   └─→ Permission Check
      ↓                                              ↓
 Event Logging ←─────────────────────────────────────┘
```

## 3. Database Schema Changes

### 3.1 New Tables

#### `templates`

```sql
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    system_prompt TEXT NOT NULL,
    default_settings JSONB NOT NULL DEFAULT '{}'::jsonb,
    actions JSONB NOT NULL DEFAULT '[]'::jsonb,
    clinic_id UUID REFERENCES clinics(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX ix_templates_clinic_id ON templates(clinic_id);
CREATE INDEX ix_templates_name ON templates(name);
CREATE INDEX ix_templates_active ON templates(is_active);
```

#### `template_roles`

```sql
CREATE TABLE template_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL, -- 'patient', 'clinician', etc.
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(template_id, role)
);

-- Indexes
CREATE INDEX ix_template_roles_template_id ON template_roles(template_id);
```

#### `event_logs` (extends existing audit_logs)

```sql
CREATE TABLE event_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    actor_user_id VARCHAR NOT NULL,
    actor_role VARCHAR(50) NOT NULL,
    action VARCHAR(255) NOT NULL,
    target_resource_type VARCHAR(100),
    target_resource_id VARCHAR,
    outcome VARCHAR(50) NOT NULL,
    details JSONB,
    source_ip VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- New fields for LLM-driven actions
    original_input TEXT,
    input_type VARCHAR(50) NOT NULL DEFAULT 'text', -- 'text', 'voice'
    extracted_intent VARCHAR(255),
    extracted_parameters JSONB,
    executed_api_action VARCHAR(255),
    clinic_id UUID REFERENCES clinics(id)
);

-- Indexes
CREATE INDEX ix_event_logs_actor_user_id ON event_logs(actor_user_id);
CREATE INDEX ix_event_logs_action ON event_logs(action);
CREATE INDEX ix_event_logs_clinic_id ON event_logs(clinic_id);
CREATE INDEX ix_event_logs_created_at ON event_logs(created_at);
```

### 3.2 SQLAlchemy Models

#### `Template` Model

```python
class Template(BaseWithTimestamps):
    __tablename__ = \"templates\"
    
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default=\"1.0\")
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    system_prompt = Column(Text, nullable=False)
    default_settings = Column(JSONB, nullable=False, default={})
    actions = Column(JSONB, nullable=False, default=[])
    clinic_id = Column(UUID(as_uuid=True), ForeignKey(\"clinics.id\", ondelete=\"CASCADE\"), index=True)
    
    # Relationships
    clinic = relationship(\"Clinic\", back_populates=\"templates\")
    roles = relationship(\"TemplateRole\", back_populates=\"template\", cascade=\"all, delete-orphan\")
```

#### `TemplateRole` Model

```python
class TemplateRole(BaseWithTimestamps):
    __tablename__ = \"template_roles\"
    
    template_id = Column(UUID(as_uuid=True), ForeignKey(\"templates.id\", ondelete=\"CASCADE\"), nullable=False, index=True)
    role = Column(String(50), nullable=False)
    
    # Relationships
    template = relationship(\"Template\", back_populates=\"roles\")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint(\"template_id\", \"role\", name=\"uq_template_role\"),
    )
```

#### `EventLog` Model (extends AuditLog)

```python
class EventLog(BaseWithTimestamps):
    __tablename__ = \"event_logs\"
    
    actor_user_id = Column(String, index=True, nullable=False)
    actor_role = Column(String(50), index=True, nullable=False)
    action = Column(String(255), nullable=False, index=True)
    target_resource_type = Column(String(100), index=True, nullable=True)
    target_resource_id = Column(String, index=True, nullable=True)
    outcome = Column(String(50), nullable=False, index=True)
    details = Column(JSONB, nullable=True)
    source_ip = Column(String(100), nullable=True)
    
    # New fields
    original_input = Column(Text, nullable=True)
    input_type = Column(String(50), nullable=False, default=\"text\")
    extracted_intent = Column(String(255), nullable=True)
    extracted_parameters = Column(JSONB, nullable=True)
    executed_api_action = Column(String(255), nullable=True)
    clinic_id = Column(UUID(as_uuid=True), ForeignKey(\"clinics.id\"), index=True)
    
    # Relationships
    clinic = relationship(\"Clinic\")
    
    # Composite indexes
    __table_args__ = (
        Index(\"ix_event_logs_actor_created\", \"actor_user_id\", \"created_at\"),
        Index(\"ix_event_logs_clinic_created\", \"clinic_id\", \"created_at\"),
    )
```

## 4. API Endpoints

### 4.1 New Endpoints

#### Template Management

```python
@router.post(\"/templates/\", response_model=TemplateResponse)
async def create_template(
    template_in: TemplateCreate,
    current_user: Union[Clinician] = Depends(get_current_clinical_admin),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Create a new template for a clinic.\"\"\"
    
@router.get(\"/templates/\", response_model=List[TemplateResponse])
async def list_templates(
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"List templates for the current user's clinic.\"\"\"
    
@router.get(\"/templates/{template_id}\", response_model=TemplateDetailResponse)
async def get_template(
    template_id: UUID,
    current_user: Union[Clinician] = Depends(get_current_active_clinician),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Get a specific template.\"\"\"
    
@router.put(\"/templates/{template_id}\", response_model=TemplateResponse)
async def update_template(
    template_id: UUID,
    template_in: TemplateUpdate,
    current_user: Union[Clinician] = Depends(get_current_clinical_admin),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Update a template.\"\"\"
    
@router.delete(\"/templates/{template_id}\", response_model=dict)
async def delete_template(
    template_id: UUID,
    current_user: Union[Clinician] = Depends(get_current_clinical_admin),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Delete a template.\"\"\"
```

#### LLM Action Execution

```python
@router.post(\"/llm-actions/text\", response_model=LLMActionResponse)
async def execute_llm_action_from_text(
    action_request: TextActionRequest,
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Execute an action based on text input through LLM processing.\"\"\"

@router.post(\"/llm-actions/voice\", response_model=LLMActionResponse)
async def execute_llm_action_from_voice(
    voice_file: UploadFile = File(...),
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Execute an action based on voice input through Whisper and LLM processing.\"\"\"
```

#### Conversation Management

```python
@router.post(\"/conversations/\", response_model=ConversationResponse)
async def create_conversation(
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Create a new conversation.\"\"\"

@router.get(\"/conversations/\", response_model=List[ConversationListItem])
async def list_conversations(
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"List conversations for the current user.\"\"\"

@router.get(\"/conversations/{conversation_id}\", response_model=ConversationDetailResponse)
async def get_conversation(
    conversation_id: UUID,
    current_user: Union[Clinician, Patient] = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    \"\"\"Get a specific conversation with messages.\"\"\"
```

## 5. Service Implementations

### 5.1 Template Service

```python
class TemplateService:
    \"\"\"Service for managing templates\"\"\"
    
    @staticmethod
    async def get_template_for_user(user_id: str, role: str, clinic_id: UUID, db: AsyncSession) -> Optional[Template]:
        \"\"\"Get the appropriate template for a user based on role and clinic\"\"\"
        
    @staticmethod
    async def validate_template(template_data: dict) -> bool:
        \"\"\"Validate template structure and content\"\"\"
        
    @staticmethod
    async def apply_template_to_prompt(template: Template, user_message: str, context: dict) -> str:
        \"\"\"Apply template to create a formatted prompt for the LLM\"\"\"
```

### 5.2 Intent Resolver Service

```python
class IntentResolverService:
    \"\"\"Service for resolving user intents from text using LLM\"\"\"
    
    def __init__(self, template: Template, llm_provider: LLMProvider):
        self.template = template
        self.llm_provider = llm_provider
        self.system_prompt = template.system_prompt
        self.actions = template.actions
    
    async def resolve_intent(self, text: str, context: dict = None) -> dict:
        \"\"\"Resolve intent and parameters from text input\"\"\"
        
    async def validate_parameters(self, intent: str, parameters: dict) -> Tuple[bool, List[str]]:
        \"\"\"Validate parameters against the expected schema for the intent\"\"\"
```

### 5.3 Action Executor Service

```python
class ActionExecutorService:
    \"\"\"Service for executing API actions based on intents\"\"\"
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def execute_action(
        self, 
        intent: str, 
        parameters: dict, 
        user_id: str, 
        user_role: str,
        clinic_id: UUID
    ) -> dict:
        \"\"\"Execute an API action based on the resolved intent\"\"\"
        
    async def check_permission(self, user_id: str, user_role: str, action: str, clinic_id: UUID) -> bool:
        \"\"\"Check if the user has permission to execute the action\"\"\"
        
    async def log_action(
        self, 
        user_id: str, 
        user_role: str, 
        original_input: str, 
        intent: str, 
        parameters: dict, 
        executed_action: str,
        outcome: str,
        details: dict,
        clinic_id: UUID
    ) -> None:
        \"\"\"Log the action in the event_logs table\"\"\"
```

### 5.4 Whisper Service

```python
class WhisperService:
    \"\"\"Service for transcribing audio using Whisper API\"\"\"
    
    async def transcribe_audio(self, audio_file: UploadFile) -> str:
        \"\"\"Transcribe audio file to text\"\"\"
        
    async def process_audio_batch(self, audio_files: List[UploadFile]) -> List[str]:
        \"\"\"Process multiple audio files in batch\"\"\"
```

### 5.5 Conversation Orchestrator Service

```python
class ConversationOrchestratorService:
    \"\"\"Service for managing conversation flow and context\"\"\"
    
    def __init__(self, db: AsyncSession, template_service: TemplateService):
        self.db = db
        self.template_service = template_service
    
    async def process_message(
        self, 
        conversation_id: UUID, 
        user_id: str, 
        user_role: str, 
        message: str,
        clinic_id: UUID
    ) -> dict:
        \"\"\"Process a message in a conversation\"\"\"
        
    async def get_conversation_context(self, conversation_id: UUID) -> dict:
        \"\"\"Get context for a conversation including history\"\"\"
        
    async def update_conversation_context(self, conversation_id: UUID, context_updates: dict) -> None:
        \"\"\"Update the context for a conversation\"\"\"
```

## 6. Pydantic Schemas

### 6.1 Template Schemas

```python
class TemplateActionParameter(BaseModel):
    name: str
    type: str  # string, integer, number, boolean, date, time, datetime
    description: str
    required: bool = True
    default: Optional[Any] = None

class TemplateAction(BaseModel):
    name: str
    description: str
    required_role: str
    parameters: List[TemplateActionParameter]
    api_endpoint: str
    method: str  # GET, POST, PUT, DELETE

class TemplateCreate(BaseModel):
    name: str
    description: Optional[str] = None
    system_prompt: str
    default_settings: dict = {}
    actions: List[TemplateAction]
    roles: List[str]
    clinic_id: UUID

class TemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    default_settings: Optional[dict] = None
    actions: Optional[List[TemplateAction]] = None
    roles: Optional[List[str]] = None
    is_active: Optional[bool] = None

class TemplateResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    version: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    clinic_id: UUID

class TemplateDetailResponse(TemplateResponse):
    system_prompt: str
    default_settings: dict
    actions: List[TemplateAction]
    roles: List[str]
```

### 6.2 LLM Action Schemas

```python
class TextActionRequest(BaseModel):
    text: str
    template_id: Optional[UUID] = None  # If None, use default for user role

class LLMActionResponse(BaseModel):
    message_id: UUID
    intent: str
    parameters: dict
    executed_action: Optional[str] = None
    action_result: Optional[dict] = None
    confirmation_message: str
    timestamp: datetime
    requires_confirmation: bool = False
```

### 6.3 Conversation Schemas

```python
class ConversationMessage(BaseModel):
    id: UUID
    conversation_id: UUID
    sender_type: str  # 'user', 'system', 'llm'
    content: str
    intent: Optional[str] = None
    parameters: Optional[dict] = None
    executed_action: Optional[str] = None
    created_at: datetime

class ConversationListItem(BaseModel):
    id: UUID
    title: str
    last_message_at: datetime
    message_count: int

class ConversationResponse(BaseModel):
    id: UUID
    title: str
    created_at: datetime
    updated_at: datetime

class ConversationDetailResponse(ConversationResponse):
    messages: List[ConversationMessage]
```

## 7. Permission System

### 7.1 Permission Model

For actions, permissions will be controlled at three levels:

1. **Role-Based Permissions**: Templates define which roles can perform specific actions
2. **Clinic-Scoped Permissions**: Actions are scoped to the user's clinic
3. **Resource-Level Permissions**: Users can only act on resources they have access to

### 7.2 Implementation in ActionExecutorService

```python
async def check_permission(self, user_id: str, user_role: str, action: str, clinic_id: UUID) -> bool:
    \"\"\"Check if the user has permission to execute the action\"\"\"
    
    # 1. Check role-based permission
    template = await self.template_service.get_template_for_user(
        user_id=user_id, 
        role=user_role,
        clinic_id=clinic_id,
        db=self.db
    )
    
    if not template:
        return False
    
    # Check if action is allowed for this role
    action_allowed = False
    for template_action in template.actions:
        if template_action[\"name\"] == action and template_action[\"required_role\"] == user_role:
            action_allowed = True
            break
    
    if not action_allowed:
        return False
    
    # 2. Check clinic-scoped permission
    if user_role == \"clinician\":
        clinician = await crud.clinician.get(self.db, id=user_id)
        if not clinician or str(clinician.clinic_id) != str(clinic_id):
            return False
    elif user_role == \"patient\":
        patient = await crud.patient.get(self.db, id=user_id)
        if not patient or str(patient.clinic_id) != str(clinic_id):
            return False
    
    # 3. Resource-level permissions would be checked in the actual execution logic
    
    return True
```

## 8. Error Handling

### 8.1 Common Error Types

```python
class TemplateError(Exception):
    \"\"\"Base exception for template-related errors\"\"\"
    pass

class IntentResolutionError(Exception):
    \"\"\"Error resolving intent from user input\"\"\"
    pass

class ParameterValidationError(Exception):
    \"\"\"Error validating parameters for an action\"\"\"
    pass

class ActionExecutionError(Exception):
    \"\"\"Error executing an action\"\"\"
    pass

class PermissionDeniedError(Exception):
    \"\"\"User does not have permission to execute the action\"\"\"
    pass

class TranscriptionError(Exception):
    \"\"\"Error transcribing audio input\"\"\"
    pass
```

### 8.2 Error Response Format

```python
class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    code: str
```

## 9. Implementation Phases

### Phase 1: Foundation (2 weeks)

1. Create database migrations for new tables
2. Implement basic Template model and CRUD operations
3. Create IntentResolver service with OpenAI integration
4. Create ActionExecutor service with permission checks
5. Implement basic API endpoints for text-based actions
6. Add event logging

### Phase 2: Voice & Enhanced Features (2 weeks)

1. Integrate Whisper API for voice transcription
2. Implement conversation management
3. Add multi-turn dialog support
4. Enhance template system with parameter validation
5. Add more action types

### Phase 3: UI & Admin Features (2 weeks)

1. Create template management UI
2. Add conversation history interface
3. Implement voice recording in frontend
4. Add action confirmation flows
5. Create analytics dashboard for LLM actions

## 10. Testing Strategy

### 10.1 Unit Tests

```python
# Template Service Tests
def test_validate_template_valid():
    \"\"\"Test template validation with valid template\"\"\"

def test_validate_template_invalid():
    \"\"\"Test template validation with invalid template\"\"\"

# Intent Resolver Tests
def test_resolve_intent_appointment():
    \"\"\"Test resolving appointment scheduling intent\"\"\"

def test_resolve_intent_medication():
    \"\"\"Test resolving medication request intent\"\"\"

# Action Executor Tests
def test_permission_check():
    \"\"\"Test permission checking logic\"\"\"

def test_execute_action():
    \"\"\"Test action execution with valid parameters\"\"\"
```

### 10.2 Integration Tests

```python
def test_end_to_end_text_action():
    \"\"\"Test full flow from text input to action execution\"\"\"

def test_end_to_end_voice_action():
    \"\"\"Test full flow from voice input to action execution\"\"\"

def test_multi_turn_conversation():
    \"\"\"Test multi-turn conversation with parameter collection\"\"\"
```

### 10.3 User Acceptance Testing

- Test with real clinicians and patients
- Measure accuracy of intent recognition
- Measure time savings compared to UI workflows
- Collect feedback on conversation flow and confirmation messages

## 11. Security Considerations

1. **Input Validation**: All user inputs must be validated before processing
2. **Rate Limiting**: Apply rate limits to prevent abuse
3. **Action Confirmation**: Require confirmation for critical actions
4. **Audit Logging**: Log all actions with detailed context
5. **Sanitization**: Sanitize all inputs to prevent injection attacks
6. **Authorization**: Multi-level permission checks
7. **Error Handling**: Avoid leaking sensitive information in error messages

## 12. Example Implementations

### 12.1 Template Example (Appointment Scheduling)

```json
{
  \"name\": \"Clinician Assistant\",
  \"description\": \"Template for handling clinician requests\",
  \"system_prompt\": \"You are an assistant for healthcare clinicians. Extract intents and parameters from their requests. Respond ONLY with a valid JSON object matching the output schema.\",
  \"actions\": [
    {
      \"name\": \"schedule_appointment\",
      \"description\": \"Schedule a patient appointment\",
      \"required_role\": \"clinician\",
      \"parameters\": [
        {
          \"name\": \"patient_id\",
          \"type\": \"string\",
          \"description\": \"Patient ID or name (will be resolved to ID)\",
          \"required\": true
        },
        {
          \"name\": \"date\",
          \"type\": \"date\",
          \"description\": \"Appointment date (YYYY-MM-DD)\",
          \"required\": true
        },
        {
          \"name\": \"time\",
          \"type\": \"time\",
          \"description\": \"Appointment time (HH:MM)\",
          \"required\": true
        },
        {
          \"name\": \"duration\",
          \"type\": \"integer\",
          \"description\": \"Duration in minutes\",
          \"required\": false,
          \"default\": 30
        },
        {
          \"name\": \"appointment_type\",
          \"type\": \"string\",
          \"description\": \"Type of appointment\",
          \"required\": false,
          \"default\": \"follow-up\"
        }
      ],
      \"api_endpoint\": \"/api/v1/appointments\",
      \"method\": \"POST\"
    }
  ]
}
```

### 12.2 Intent Resolution Example

```python
async def resolve_intent(self, text: str, context: dict = None) -> dict:
    \"\"\"Resolve intent and parameters from text input\"\"\"
    
    context = context or {}
    
    # Format the prompt with template
    prompt = await self.template_service.apply_template_to_prompt(
        template=self.template,
        user_message=text,
        context=context
    )
    
    # Prepare output schema based on available actions
    output_schema = {
        \"type\": \"object\",
        \"properties\": {
            \"intent\": {
                \"type\": \"string\",
                \"enum\": [action[\"name\"] for action in self.template.actions]
            },
            \"parameters\": {
                \"type\": \"object\"
            },
            \"confidence\": {
                \"type\": \"number\",
                \"minimum\": 0,
                \"maximum\": 1
            }
        },
        \"required\": [\"intent\", \"parameters\", \"confidence\"]
    }
    
    # Add parameter schemas for each action
    for action in self.template.actions:
        if \"parameters\" not in output_schema[\"properties\"]:
            output_schema[\"properties\"][\"parameters\"] = {\"type\": \"object\", \"properties\": {}}
        
        if action[\"name\"] not in output_schema[\"properties\"][\"parameters\"][\"properties\"]:
            output_schema[\"properties\"][\"parameters\"][\"properties\"][action[\"name\"]] = {
                \"type\": \"object\",
                \"properties\": {}
            }
        
        for param in action[\"parameters\"]:
            output_schema[\"properties\"][\"parameters\"][\"properties\"][action[\"name\"]][\"properties\"][param[\"name\"]] = {
                \"type\": param[\"type\"]
            }
    
    # Call LLM with structured output
    try:
        result = await self.llm_provider.generate_structured(
            prompt=prompt,
            output_schema=output_schema
        )
        
        return {
            \"intent\": result.intent,
            \"parameters\": result.parameters.get(result.intent, {}),
            \"confidence\": result.confidence
        }
    except Exception as e:
        raise IntentResolutionError(f\"Failed to resolve intent: {str(e)}\")
```

### 12.3 Action Execution Example

```python
async def execute_action(
    self, 
    intent: str, 
    parameters: dict, 
    user_id: str, 
    user_role: str,
    clinic_id: UUID
) -> dict:
    \"\"\"Execute an API action based on the resolved intent\"\"\"
    
    # Check permission
    has_permission = await self.check_permission(
        user_id=user_id,
        user_role=user_role,
        action=intent,
        clinic_id=clinic_id
    )
    
    if not has_permission:
        raise PermissionDeniedError(f\"User {user_id} does not have permission to execute {intent}\")
    
    # Get the action definition from template
    template = await self.template_service.get_template_for_user(
        user_id=user_id,
        role=user_role,
        clinic_id=clinic_id,
        db=self.db
    )
    
    action_def = None
    for action in template.actions:
        if action[\"name\"] == intent:
            action_def = action
            break
    
    if not action_def:
        raise ActionExecutionError(f\"Action {intent} not found in template\")
    
    # Validate parameters
    valid, errors = await self.validate_parameters(intent, parameters, action_def)
    if not valid:
        raise ParameterValidationError(f\"Invalid parameters: {', '.join(errors)}\")
    
    # Execute the action based on the API endpoint and method
    endpoint = action_def[\"api_endpoint\"]
    method = action_def[\"method\"]
    
    # Prepare request
    request_data = {
        \"user_id\": user_id,
        \"clinic_id\": clinic_id,
        **parameters
    }
    
    # For demonstration, we're directly calling internal API functions
    # In a real implementation, you might use internal service calls instead
    try:
        if intent == \"schedule_appointment\":
            result = await crud.appointment.create(
                db=self.db, 
                obj_in=AppointmentCreate(
                    patient_id=parameters[\"patient_id\"],
                    scheduled_start=datetime.combine(
                        parameters[\"date\"], 
                        datetime.strptime(parameters[\"time\"], \"%H:%M\").time()
                    ),
                    duration_minutes=parameters.get(\"duration\", 30),
                    appointment_type=parameters.get(\"appointment_type\", \"follow-up\"),
                    clinician_id=user_id if user_role == \"clinician\" else None,
                    clinic_id=clinic_id
                )
            )
            
            # Log the action
            await self.log_action(
                user_id=user_id,
                user_role=user_role,
                original_input=\"\",  # This would be set by the caller
                intent=intent,
                parameters=parameters,
                executed_action=f\"{method} {endpoint}\",
                outcome=\"SUCCESS\",
                details={\"appointment_id\": str(result.id)},
                clinic_id=clinic_id
            )
            
            return {
                \"success\": True,
                \"result\": {
                    \"appointment_id\": str(result.id),
                    \"patient_id\": str(result.patient_id),
                    \"scheduled_start\": result.scheduled_start.isoformat(),
                    "patient_id": str(result.patient_id),
                    "scheduled_start": result.scheduled_start.isoformat(),
                    "duration_minutes": result.duration_minutes,
                    "appointment_type": result.appointment_type
                }
            }
        
        # Add more action implementations here for other intents
        # e.g., create_medication_request, log_symptom, etc.
        
        else:
            raise ActionExecutionError(f"Implementation for action {intent} not found")
            
    except Exception as e:
        # Log the failure
        await self.log_action(
            user_id=user_id,
            user_role=user_role,
            original_input="",  # This would be set by the caller
            intent=intent,
            parameters=parameters,
            executed_action=f"{method} {endpoint}",
            outcome="FAILURE",
            details={"error": str(e)},
            clinic_id=clinic_id
        )
        
        raise ActionExecutionError(f"Failed to execute action {intent}: {str(e)}")
```

## 13. Frontend Integration

### 13.1 Voice Input Component

```typescript
// VoiceInputComponent.tsx
import React, { useState, useRef } from 'react';
import { Button, Spinner } from '@chakra-ui/react';
import { FaMicrophone, FaStop } from 'react-icons/fa';
import { executeVoiceAction } from '../api/llm-actions';

const VoiceInputComponent: React.FC<{
  onActionComplete: (response: any) => void;
  templateId?: string;
}> = ({ onActionComplete, templateId }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = handleAudioSubmit;
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };
  
  const handleAudioSubmit = async () => {
    if (audioChunksRef.current.length === 0) return;
    
    setIsProcessing(true);
    
    try {
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
      const formData = new FormData();
      formData.append('voice_file', audioBlob);
      
      if (templateId) {
        formData.append('template_id', templateId);
      }
      
      const response = await executeVoiceAction(formData);
      onActionComplete(response);
    } catch (error) {
      console.error('Error processing voice input:', error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <div>
      {isProcessing ? (
        <Button isLoading colorScheme="blue" loadingText="Processing voice..." disabled>
          <Spinner />
        </Button>
      ) : isRecording ? (
        <Button 
          onClick={stopRecording} 
          colorScheme="red" 
          leftIcon={<FaStop />}
        >
          Stop Recording
        </Button>
      ) : (
        <Button 
          onClick={startRecording} 
          colorScheme="blue" 
          leftIcon={<FaMicrophone />}
        >
          Start Voice Input
        </Button>
      )}
    </div>
  );
};

export default VoiceInputComponent;
```

### 13.2 Text Action Component

```typescript
// TextActionComponent.tsx
import React, { useState } from 'react';
import { 
  Input, 
  Button, 
  HStack, 
  VStack, 
  Text, 
  Box,
  useToast
} from '@chakra-ui/react';
import { executeTextAction } from '../api/llm-actions';

const TextActionComponent: React.FC<{
  onActionComplete: (response: any) => void;
  templateId?: string;
}> = ({ onActionComplete, templateId }) => {
  const [text, setText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const toast = useToast();
  
  const handleSubmit = async () => {
    if (!text.trim()) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await executeTextAction({
        text,
        template_id: templateId
      });
      
      onActionComplete(response);
      setText('');
    } catch (error) {
      console.error('Error executing text action:', error);
      toast({
        title: 'Error',
        description: 'Failed to process your request',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <VStack spacing={3} align="stretch">
      <HStack>
        <Input
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Type your request..."
          onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
        />
        <Button
          onClick={handleSubmit}
          colorScheme="blue"
          isLoading={isSubmitting}
          loadingText="Processing"
        >
          Send
        </Button>
      </HStack>
    </VStack>
  );
};

export default TextActionComponent;
```

## 14. Deployment Strategy

### 14.1 Database Migrations

The deployment will require database migrations to create the new tables:
- `templates`
- `template_roles`
- `event_logs`

These migrations will be applied using Alembic with a rollback plan in case of issues.

### 14.2 Feature Flags

The LLM-driven API actions will be deployed behind feature flags to allow for controlled rollout:

```python
# Feature flag configuration
LLM_FEATURES = {
    "text_actions": {
        "enabled": True,
        "clinics": ["all"],  # or specific clinic IDs
        "roles": ["clinician", "patient"]
    },
    "voice_actions": {
        "enabled": True,
        "clinics": ["all"],  # or specific clinic IDs
        "roles": ["clinician", "patient"]
    },
    "template_management": {
        "enabled": True,
        "clinics": ["all"],  # or specific clinic IDs
        "roles": ["admin"]
    }
}
```

### 14.3 Rollout Phases

1. **Alpha Testing (2 weeks)**
   - Deploy to development environment
   - Internal testing with simulated data
   - Focus on accuracy of intent resolution

2. **Beta Testing (3 weeks)**
   - Limited rollout to 2-3 partner clinics
   - Monitor usage patterns and success rates
   - Collect feedback on template effectiveness

3. **General Availability**
   - Gradual rollout to all clinics
   - Continued monitoring and template refinement
   - Regular updates based on usage analytics

## 15. Monitoring & Analytics

### 15.1 Key Metrics

```python
# Metrics to track in Prometheus/Grafana
METRICS = {
    # Performance metrics
    "llm_response_time": "Histogram of LLM response times",
    "action_execution_time": "Histogram of action execution times",
    "whisper_transcription_time": "Histogram of audio transcription times",
    
    # Success metrics
    "intent_resolution_success_rate": "Success rate of intent resolution",
    "action_execution_success_rate": "Success rate of action execution",
    "transcription_success_rate": "Success rate of audio transcription",
    
    # Usage metrics
    "text_actions_count": "Count of text-based actions by type",
    "voice_actions_count": "Count of voice-based actions by type",
    "actions_by_role": "Count of actions by user role",
    "actions_by_clinic": "Count of actions by clinic"
}
```

### 15.2 Alerting

```yaml
# Alert rules
- alert: HighLLMErrorRate
  expr: rate(llm_errors_total[5m]) / rate(llm_requests_total[5m]) > 0.1
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High LLM error rate"
    description: "LLM error rate is above 10% for 5 minutes"

- alert: SlowLLMResponseTime
  expr: histogram_quantile(0.95, sum(rate(llm_response_time_bucket[5m])) by (le)) > 2
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Slow LLM response time"
    description: "95th percentile of LLM response time is above 2 seconds"
```

## 16. Maintenance & Support

### 16.1 Template Maintenance

Templates will require regular updates based on:
- New API endpoints and features
- Changes in clinical workflows
- User feedback on intent resolution accuracy
- Performance optimization

### 16.2 LLM Provider Management

The system will support multiple LLM providers with a fallback strategy:

```python
LLM_PROVIDERS = [
    {
        "name": "openai",
        "models": ["gpt-4o", "gpt-4-turbo"],
        "priority": 1
    },
    {
        "name": "anthropic",
        "models": ["claude-3-opus", "claude-3-sonnet"],
        "priority": 2
    }
]

async def get_llm_provider(preferred=None):
    """Get an available LLM provider with fallback"""
    if preferred and preferred in [p["name"] for p in LLM_PROVIDERS]:
        providers = sorted([p for p in LLM_PROVIDERS if p["name"] == preferred], key=lambda x: x["priority"])
        if providers:
            return providers[0]
    
    # Return the highest priority provider
    return sorted(LLM_PROVIDERS, key=lambda x: x["priority"])[0]
```

## 17. Conclusion

This technical specification provides a comprehensive implementation plan for adding LLM-driven API actions to PulseTrack. The design leverages existing components while introducing new capabilities for natural language processing and action execution.

The implementation follows a phased approach to minimize risk and allow for iterative improvements based on user feedback. By maintaining strict adherence to the existing security model and adding comprehensive audit logging, the system ensures that all actions are properly authorized and traceable.

Upon completion, this feature will significantly enhance the user experience for both clinicians and patients while maintaining the security and compliance standards required for healthcare applications.