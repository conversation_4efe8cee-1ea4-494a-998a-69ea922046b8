---
id: PRD-0003-Chat-Intent-Action-Flow
title: PulseTrack LLM Chat Intent Resolution and Action Flow
authors: [<PERSON>, Claude 3.7 Sonnet]
status: Complete
created: 2025-04-21
updated: 2025-04-22
version: 1.1
phase: Documentation
module: [llm_pipeline, chat_modules, action_handlers]
adr_links: [PRD-0003-LLM-Actions-Implementation.md]
tags: [llm, chat, intent_resolution, action_execution]
constraints: [../constraints/PRD-0002-Constraints.md]
---

> **Update Summary (v1.1)**
> 
> This revision incorporates findings from the clinician chat deep-dive analysis:
> - Added detailed clinician-specific execution paths and edge cases
> - Identified potential schema drift risks in F-2
> - Enhanced acceptance criteria for clinician interactions
> - Added recommendations for template versioning
> - Updated security considerations for multi-clinic deployments

# PulseTrack: LLM Chat Intent Resolution and Action Flow

## Overview

This document details the complete technical flow for how natural language user messages in PulseTrack are processed, interpreted, and converted into structured API actions. The architecture uses **LLM-based intent extraction** as its core component, allowing users to interact with the system through conversational language while the backend extracts structured intents and parameters to perform various operations.

## System Architecture

The PulseTrack chat action system is built around these key components:

1. **LLM-Based Intent Extraction**: The core intelligence of the system that transforms natural language into structured data
2. **Template-Driven Action Framework**: Defines available actions, required parameters, and validation rules
3. **Action Execution Pipeline**: Processes extracted intents into database operations
4. **Audit and Security Layer**: Ensures proper access control and traceability

```mermaid
flowchart TB
    User[User Message] --> LLMModule[LLM Action Module]
    LLMModule --> IntentService[Intent Resolver Service]
    IntentService --> LLMProvider[LLM Provider]    
    LLMProvider --> Extraction[Intent Extraction]
    Extraction --> Validation[Parameter Validation]
    Validation --> ActionExecutor[Action Executor]
    ActionExecutor --> DatabaseOps[Database Operations]
    DatabaseOps --> Response[Response Generation]
    
    subgraph "LLM-Based Intent Extraction"
        LLMProvider
        Extraction
    end
    
    subgraph "Template-Driven Framework"
        IntentService
        Validation
    end
    
    subgraph "Action Execution"
        ActionExecutor
        DatabaseOps
    end
```

## Complete Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant ChatUI as Chat Interface
    participant LLMActionModule
    participant IntentResolver as Intent Resolver Service
    participant ActionExecutor as Action Executor Service
    participant Database
    
    User->>ChatUI: Send natural language message
    ChatUI->>LLMActionModule: process_message()
    LLMActionModule->>LLMActionModule: should_handle_message()
    LLMActionModule->>IntentResolver: process_user_input()
    IntentResolver->>IntentResolver: extract_intent()
    IntentResolver->>IntentResolver: validate_parameters()
    IntentResolver-->>LLMActionModule: Return ResolvedIntent
    LLMActionModule->>ActionExecutor: execute_action()
    ActionExecutor->>ActionExecutor: _handle_[action_type]()
    ActionExecutor->>Database: Perform CRUD operation
    Database-->>ActionExecutor: Return operation result
    ActionExecutor->>Database: Log audit event
    ActionExecutor-->>LLMActionModule: Return ActionResponse
    LLMActionModule-->>ChatUI: Return confirmation message
    ChatUI-->>User: Display confirmation message
```

## Template System Implementation

### Template Model Structure
```python
class Template(BaseWithTimestamps):
    """Template model for LLM-driven API actions."""
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default="1.0")
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    system_prompt = Column(Text, nullable=False)
    default_settings = Column(JSONB, nullable=False, default={})
    actions = Column(JSONB, nullable=False, default=[])
    clinic_id = Column(UUID(as_uuid=True), ForeignKey("clinics.id", ondelete="CASCADE"), index=True)
```

### Role-Based Access Control
```python
class TemplateRole(BaseWithTimestamps):
    """Role association for templates."""
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False)
    role = Column(String(50), nullable=False)
    __table_args__ = (UniqueConstraint("template_id", "role", name="uq_template_role"),)
```

### Supported Action Types and Role Permissions
```python
SUPPORTED_ACTIONS = [
    "appointment_create",
    "appointment_cancel",
    "medication_request_create",
    "medication_request_update",
    "note_create",
    "notification_create",
    "patient_search",
    "query_history",
    "health_data_retrieve"
]

ACTION_ROLE_PERMISSIONS = {
    "appointment_create": ["patient", "clinician", "admin"],
    "appointment_cancel": ["patient", "clinician", "admin"],
    "medication_request_create": ["clinician", "admin"],
    "medication_request_update": ["clinician", "admin"],
    "note_create": ["clinician", "admin"],
    "notification_create": ["clinician", "admin"],
    "patient_search": ["clinician", "admin"],
    "query_history": ["patient", "clinician", "admin"],
    "health_data_retrieve": ["patient", "clinician", "admin"]
}
```

## Clinician Chat Implementation

### Command Detection
The LLMActionModule detects command-like messages using predefined prefixes:
```python
command_prefixes = [
    "schedule", "book", "create", "add", "make", "set up",
    "cancel", "delete", "remove", "update", "change", "modify",
    "get", "show", "list", "find", "search", "query"
]
```

### Intent Resolution Process
1. **Message Processing**:
   ```python
   async def process_message(self, message: str, context: dict[str, Any]) -> dict[str, Any]:
       # Extract context values
       user_id = context.get("user_id")
       user_role = context.get("user_role") 
       db: Session = context.get("db")
       template_id = context.get("template_id")
   ```

2. **Template Selection**:
   - If no template_id is provided, system finds appropriate templates for the user
   - Templates are filtered based on user role and clinic association
   - System uses role-based access control to determine available actions

3. **LLM Intent Extraction**:
   - Uses a sophisticated prompt template for accurate intent extraction
   - Handles timezone-aware datetime parsing
   - Extracts structured parameters with confidence scores
   - Validates parameters against template requirements

### LLM Prompt Engineering
The system uses a carefully crafted prompt template that includes:
1. **Context Setting**:
   - Current date/time
   - User's timezone offset
   - Available actions and parameters

2. **Extraction Guidelines**:
   - Parameter validation rules
   - Timezone handling instructions
   - Appointment duration parsing
   - Severity level mapping for side effects
   - Patient identification rules

3. **Response Format**:
   ```json
   {
     "action_type": "one_of_supported_actions",
     "parameters": [
       {
         "name": "param_name",
         "value": "extracted_value",
         "confidence": 0.95,
         "source": "user_input"
       }
     ],
     "confidence_score": 0.9,
     "reasoning": "Explanation of extraction logic"
   }
   ```

### Parameter Validation
The system implements multi-layer parameter validation:
1. **Schema Validation**:
   ```python
   class ActionParameter(BaseModel):
       name: str
       description: str
       required: bool = False
       type: str
       validation_pattern: Optional[str] = None
   ```

2. **Type Validation**:
   - String validation with optional regex patterns
   - Numeric value range checks
   - Date/time format validation
   - Boolean value normalization

3. **Business Rule Validation**:
   - Role-based permission checks
   - Resource availability verification
   - Conflict detection
   - Scheduling constraints

### Security Measures

1. **Template Safety**:
   ```python
   FORBIDDEN_PROMPT_PATTERNS = [
       "jailbreak",
       "ignore previous",
       "ignore all",
       "bypass",
       "credentials",
       "password",
       "admin access",
       "root access",
       "api key",
       "secret key",
       "token",
       "authentication",
   ]
   ```

2. **Access Control**:
   - Role-based template access
   - Action-level permission checks
   - Patient data access restrictions
   - Audit logging of all actions

3. **Input Sanitization**:
   - Parameter type validation
   - SQL injection prevention
   - XSS protection
   - Maximum length constraints

### Error Handling

1. **Resolution Errors**:
   ```python
   class IntentResolutionResult(str, Enum):
       SUCCESS = "success"
       FAILED_EXTRACTION = "failed_extraction"
       INVALID_ACTION = "invalid_action"
       MISSING_PARAMETERS = "missing_parameters"
       INVALID_PARAMETERS = "invalid_parameters"
       PERMISSION_DENIED = "permission_denied"
   ```

2. **Execution Errors**:
   ```python
   class ActionExecutionResult(str, Enum):
       SUCCESS = "success"
       PERMISSION_DENIED = "permission_denied"
       RESOURCE_NOT_FOUND = "resource_not_found"
       INVALID_INPUT = "invalid_input"
       CONFLICT = "conflict"
       INTERNAL_ERROR = "internal_error"
       SERVICE_UNAVAILABLE = "service_unavailable"
   ```

### Response Format

```json
{
  "response": "Human-friendly confirmation message",
  "action": "action_type",
  "metadata": {
    "module": "LLMActionModule",
    "success": true,
    "data": {
      "resource_id": "created-resource-uuid",
      "additional_metadata": "action-specific-values"
    },
    "action_type": "specific_action",
    "parameters": {
      "param1": "value1",
      "param2": "value2"
    }
  }
}
```

## Clinician-Specific Implementation Details

### Execution Path Analysis

Our deep-dive into clinician chat revealed several key implementation details:

1. **Template Resolution**
   - Clinician templates are clinic-scoped
   - Multi-clinic practitioners require template merging
   - Template versioning needed for audit compliance

2. **Action Context**
   - Patient context must persist across chat sessions
   - Clinic context determines available actions
   - Role elevation checks for admin-level actions

3. **Parameter Extraction**
   - Enhanced medical terminology recognition
   - Standardized unit conversions
   - ICD-10 code mapping support

### Schema Drift Risk (F-2)

Current implementation shows potential for schema drift between:
- Template action definitions
- Database models
- API contracts

**Mitigation Recommendations:**
1. Implement schema version tracking
2. Add automated schema compatibility checks
3. Create migration tooling for template updates

### Enhanced Acceptance Criteria

#### Template Management
- [ ] Templates must track clinic-specific overrides
- [ ] Version history must be maintained
- [ ] Schema changes must be backward compatible

#### Action Execution
- [ ] All actions must include clinic context
- [ ] Patient context must persist across sessions
- [ ] Role elevation must be explicitly logged

#### Security Requirements
- [ ] Multi-clinic data isolation
- [ ] Role-based template access
- [ ] Audit trail for template modifications

### Implementation Recommendations

1. **Template Versioning**
   ```python
   class TemplateVersion(BaseModel):
       template_id: UUID
       version: str
       changes: list[str]
       compatibility: str  # 'backward', 'forward', 'both'
       created_at: datetime
   ```

2. **Clinic Context**
   ```python
   class ClinicContext(BaseModel):
       clinic_id: UUID
       template_overrides: dict[str, Any]
       role_mappings: dict[str, list[str]]
   ```

3. **Action Validation**
   ```python
   class ActionValidator(BaseModel):
       schema_version: str
       clinic_context: ClinicContext
       role_requirements: list[str]
       elevation_rules: dict[str, list[str]]
   ```

### Security Enhancements

1. **Multi-Clinic Isolation**
   - Implement clinic-specific encryption keys
   - Add clinic context to audit logs
   - Enforce clinic-based template isolation

2. **Role Elevation**
   - Add explicit elevation tracking
   - Implement time-based elevation expiry
   - Log all elevation events

3. **Template Access**
   - Track template access patterns
   - Implement template usage analytics
   - Add template access reviews

## Technical Components

| Component | Purpose | File Path |
|-----------|---------|-----------|
| LLMActionModule | Detects actionable messages and initiates processing | `/backend/app/services/chat_modules/llm_action_module.py` |
| IntentResolverService | Extracts structured data from natural language | `/backend/app/services/intent_resolver_service.py` |
| ActionExecutorService | Routes and executes specific actions | `/backend/app/services/action_executor_service.py` |
| Template System | Defines supported actions and required parameters | `/backend/app/models/template.py` |
| CRUD Operations | Database operations for various entities | `/backend/app/crud/` |

## Integration Points

- **Chat UI**: Renders the conversation and displays confirmation messages
- **Audit System**: Records all actions for compliance and traceability
- **Notification System**: Alerts users about action results when appropriate
- **Entity Services**: Maintains data integrity and relationships

## Extension Process

To add a new action type to the system:

1. Define the action in the `ACTION_HANDLERS` and `ACTION_PERMISSIONS` mappings
2. Create a new handler method `_handle_[new_action]()` in the `ActionExecutorService`
3. Add the action to the appropriate templates for different user roles
4. Define schemas for the action parameters and responses
5. Implement any necessary CRUD operations
6. Update audit logging to include the new action type