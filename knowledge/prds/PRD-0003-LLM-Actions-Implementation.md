---
id: PRD-0003-LLM-Actions-Implementation
title: PulseTrack LLM-Driven API Actions Implementation - Patient Focus
authors: [<PERSON>, Claude 3.7 Sonnet]
status: Draft
created: 2025-04-21
updated: 2025-04-21
phase: Incremental
module: [llm_pipeline, templates, llm_action_handlers]
adr_links: [PRD-0002-Implementation-Plan.md]
tags: [llm, api_actions, templates, patient_experience]
constraints: [../constraints/PRD-0002-Constraints.md]
---

# PulseTrack LLM-Driven API Actions - Patient Focus

## 1. Overview

This document outlines the implementation plan for adding LLM-driven API actions focused on the patient experience in PulseTrack. Based on analysis of the current codebase, most of the underlying backend functionality for patient-focused features already exists, making it an ideal opportunity to add LLM interfaces without building completely new features.

## 2. Goal & Rationale

### 2.1 Primary Goal

Enhance patient engagement and self-service capabilities by enabling natural language interactions with key features:
1. Side effect reporting (COMPLETE)
2. Weight tracking (NEXT UP)
3. Health data retrieval
4. Appointment management

### 2.2 Rationale for Patient-First Approach

1. **Backend infrastructure exists**: The analysis of the codebase revealed complete implementations for patient side effect reporting, health data retrieval, and appointment management.
2. **Immediate value**: Adding LLM interfaces to existing features provides immediate value without building entirely new functionality.
3. **User adoption**: A conversational interface lowers barriers to technology use, especially important for patients.
4. **Clinical workflow impact**: Patient self-service reduces administrative burden on clinicians.

## 3. Current State Analysis

### 3.1 Side Effect Reporting (COMPLETE)

**Status:** Completed as of 2025-04-21. All backend and frontend components for LLM-driven side effect reporting (templates, intent extraction, handler, and UI) are implemented and tested.

### 3.2 Weight Tracking (IN PROGRESS)

**Planned Work:**
- Design and implement LLM action templates for weight tracking queries (e.g., "What was my weight last month?", "Show my weight trend this year.")
- Extend intent extraction logic to support weight-related queries using LLMs
- Implement action execution handler for retrieving and summarizing weight log data
- Update patient chat UI to support weight tracking queries and visualizations
- Add unit and integration tests for LLM-driven weight tracking flows

**Existing Functionality:**
- Endpoints for retrieving patient weight logs: `/patients/me/weight-log`
- CRUD operations and permission models for weight data
- Existing frontend components for weight history visualization

**Missing Components for LLM Integration:**
- LLM action template for weight queries
- Intent extraction and resolution for weight-related requests
- Action execution handler for weight data
- Enhanced UI for LLM-driven weight tracking

### 3.3 Health Data Retrieval

**Existing Functionality:**
- Endpoints for retrieving specific types of health data:
  - Weight logs: `/patients/me/weight-log`
  - Medication history: `/patients/me/medication-requests`
  - Appointment history: `/patients/me/appointments`
  - Side effect reports: `/patients/me/side-effects`
- Permission models and CRUD operations

**Missing Components for LLM Integration:**
- LLM action template for health data queries
- Intent resolution for different data types and filtering
- Action execution handler for health data retrieval
- UI for displaying LLM-driven data retrieval results

### 3.4 Appointment Management

**Existing Functionality:**
- Appointment creation via standard API
- Appointment scheduling and management endpoints
- Permissions and validation

**Missing Components for LLM Integration:**
- LLM action template for appointment scheduling
- Intent resolution for appointment requests
- Enhanced UI for LLM-driven appointment requests

## 4. Implementation Plan

### 4.1 Phase 1: Side Effect Reporting via LLM (Week 1)

#### 4.1.1 Create Side Effect Report Template

Create a template for the LLM to extract structured information from natural language about side effects:

```python
# Create a template for side effect reporting
side_effect_template = models.Template(
    name="Patient Side Effect Reporting",
    description="Template for patients to report medication side effects through natural language",
    is_active=True,
    system_prompt="""You are a medical assistant helping patients report medication side effects.
Extract the medication name, symptoms, severity, and onset time from the patient's input.
Ask follow-up questions if any critical information is missing.
Be empathetic while collecting necessary clinical information.""",
    roles=[models.TemplateRole(role="patient")],
    default_settings={
        "temperature": 0.2,  # Low temperature for more predictable extraction
        "max_tokens": 1024
    },
    actions=[
        {
            "action_type": "side_effect_report_create",
            "description": "Report a medication side effect",
            "parameters": [
                {"name": "medication_name", "type": "string", "required": True, "description": "Name of the medication causing side effects"},
                {"name": "symptoms", "type": "string", "required": True, "description": "Description of the symptoms experienced"},
                {"name": "severity", "type": "string", "required": False, "description": "Severity of side effects (minor, moderate, major)"},
                {"name": "onset_time", "type": "string", "required": False, "description": "When the side effects started (e.g., '2 hours ago', 'yesterday morning')"},
                {"name": "additional_notes", "type": "string", "required": False, "description": "Any additional information about the side effects"}
            ]
        }
    ]
)
```

#### 4.1.2 Implement Side Effect Report Action Handler

Implement a handler in the `ActionExecutorService` that leverages the existing CRUD operations for side effect reports:

```python
async def _handle_side_effect_report_create(self, user_id: str, user_role: str, params: dict[str, Any]) -> ActionResponse:
    """Handle side effect report creation via LLM."""
    try:
        # For patients, the patient_id is always the current user
        patient_id = user_id if user_role == "patient" else params.get("patient_id")
        
        # Extract parameters
        medication_name = params.get("medication_name")
        symptoms = params.get("symptoms")
        severity = params.get("severity", "moderate")  # default to moderate if not specified
        onset_time = params.get("onset_time")
        additional_notes = params.get("additional_notes", "")
        
        # Validate required parameters
        if not medication_name:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Please specify which medication is causing side effects.",
                action_type="side_effect_report_create"
            )
            
        if not symptoms:
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message="Please describe the symptoms you're experiencing.",
                action_type="side_effect_report_create"
            )
        
        # Map severity values
        severity_mapping = {
            "minor": SeverityLevel.MINOR,
            "mild": SeverityLevel.MINOR,
            "moderate": SeverityLevel.MODERATE,
            "major": SeverityLevel.MAJOR,
            "severe": SeverityLevel.MAJOR,
            "serious": SeverityLevel.MAJOR
        }
        
        normalized_severity = severity_mapping.get(
            severity.lower() if severity else "", 
            SeverityLevel.MODERATE
        )
        
        # Create description that includes onset time and additional notes
        full_description = symptoms
        if onset_time:
            full_description += f"\n\nOnset: {onset_time}"
        if additional_notes:
            full_description += f"\n\nAdditional notes: {additional_notes}"
        
        # Create side effect report using existing CRUD operations
        report_create = SideEffectReportCreate(
            description=full_description,
            severity=normalized_severity,
            clinician_id=None,  # Let the API handle this
        )
        
        # Use the existing CRUD operation
        created_report = crud.side_effect_report.create(
            db=self.db, 
            obj_in=report_create, 
            patient_id=patient_id
        )
        
        # Create a response based on severity
        if normalized_severity == SeverityLevel.MINOR:
            message = "I've recorded your report of minor side effects. Your healthcare team will review this information. If symptoms worsen, please update your report or contact your provider directly."
        elif normalized_severity == SeverityLevel.MODERATE:
            message = "I've recorded your report of moderate side effects. Your healthcare team will be notified. Monitor your symptoms and contact your healthcare provider if they worsen."
        else:  # MAJOR
            message = "I've recorded your report of serious side effects. Your healthcare team will be notified immediately. Please contact your healthcare provider right away if you haven't already done so."
        
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message=message,
            data={
                "report_id": str(created_report.id),
                "severity": str(created_report.severity.value),
                "status": created_report.status
            },
            action_type="side_effect_report_create",
            resource_id=str(created_report.id),
            resource_type="side_effect_report"
        )
        
    except Exception as e:
        logger.error(f"Error creating side effect report: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating side effect report: {str(e)}"
        )
```

#### 4.1.3 Update Frontend for LLM Side Effect Reporting

Implement UI components to handle the LLM-driven side effect reporting flow:

```javascript
// In the patient chat UI, add suggested prompts for side effect reporting
const sideEffectExamples = [
  "I'm having headaches from my blood pressure medication",
  "My arthritis medication is making me feel dizzy",
  "I've been feeling nauseous since starting my new prescription yesterday"
];

// Add structured response handling for side effect reports
const handleSideEffectResponse = (data) => {
  if (data?.metadata?.action_type === "side_effect_report_create") {
    const severity = data.data.severity;
    const severityColor = {
      "minor": "bg-blue-50 border-blue-300",
      "moderate": "bg-yellow-50 border-yellow-300",
      "major": "bg-red-50 border-red-300"
    }[severity] || "bg-gray-50 border-gray-300";
    
    return (
      <div className={`p-3 rounded-lg border mt-2 ${severityColor}`}>
        <h4 className="font-semibold">Side Effect Report Submitted</h4>
        <p>Severity: <span className="capitalize">{severity}</span></p>
        <p>Status: {data.data.status}</p>
        <div className="text-sm mt-2">
          Your report has been saved and will be reviewed by your healthcare team.
        </div>
      </div>
    );
  }
  
  return null;
};
```

#### 4.1.4 Testing Side Effect Reporting

1. Unit Tests
   - Test extraction of side effect parameters from various patient inputs
   - Test severity normalization logic
   - Test response generation based on severity

2. Integration Tests  
   - End-to-end test of side effect reporting via LLM
   - Test error handling for missing information
   - Test response formatting in UI

### 4.2 Phase 2: Health Data Retrieval via LLM (Week 2)

#### 4.2.1 Create Health Data Retrieval Template

```python
# Create a template for health data retrieval
health_data_template = models.Template(
    name="Patient Health Data Retrieval",
    description="Template for patients to retrieve their health data through natural language",
    is_active=True,
    system_prompt="""You are a medical assistant helping patients retrieve their health information.
Extract the data type, time range, and any specific measurements from the patient's query.
Be precise and helpful while maintaining privacy and security.""",
    roles=[models.TemplateRole(role="patient")],
    default_settings={
        "temperature": 0.2,
        "max_tokens": 1024
    },
    actions=[
        {
            "action_type": "health_data_retrieve",
            "description": "Retrieve patient health data",
            "parameters": [
                {"name": "data_type", "type": "string", "required": True, "description": "Type of data to retrieve (vitals, medications, labs, appointments, side_effects)"},
                {"name": "time_range", "type": "string", "required": False, "description": "Time range for the data (recent, week, month, year, all)"},
                {"name": "specific_measure", "type": "string", "required": False, "description": "Specific measure to retrieve (e.g., weight, blood pressure, specific medication)"},
                {"name": "limit", "type": "integer", "required": False, "description": "Maximum number of records to return"}
            ]
        }
    ]
)
```

#### 4.2.2 Implement Health Data Retrieval Handler

```python
async def _handle_health_data_retrieve(self, user_id: str, user_role: str, params: dict[str, Any]) -> ActionResponse:
    """Handle health data retrieval via LLM."""
    try:
        # For patients, the patient_id is always the current user
        patient_id = user_id if user_role == "patient" else params.get("patient_id")
        
        # Extract parameters
        data_type = params.get("data_type", "").lower()
        time_range = params.get("time_range", "recent").lower()
        specific_measure = params.get("specific_measure")
        limit = params.get("limit", 5)  # Default to 5 entries
        
        # Map data types to our existing API endpoints
        data_type_mapping = {
            "vitals": "weight_log",
            "vital": "weight_log",
            "weight": "weight_log",
            "medications": "medications",
            "medication": "medications",
            "meds": "medications",
            "appointments": "appointments",
            "appointment": "appointments",
            "visits": "appointments",
            "side effects": "side_effects",
            "side effect": "side_effects",
            "symptoms": "side_effects"
        }
        
        normalized_data_type = next((v for k, v in data_type_mapping.items() if k in data_type), "unknown")
        
        if normalized_data_type == "unknown":
            return ActionResponse(
                success=False,
                result=ActionExecutionResult.INVALID_INPUT,
                message=f"I don't understand what kind of health data you're looking for. Please ask about your weight, medications, appointments, or side effects.",
                action_type="health_data_retrieve"
            )
            
        # Extract time parameters
        from datetime import datetime, timedelta
        
        end_date = datetime.now()
        if "recent" in time_range or "latest" in time_range:
            start_date = end_date - timedelta(days=7)  # Last 7 days
            time_description = "from the past week"
        elif "week" in time_range:
            start_date = end_date - timedelta(days=7)
            time_description = "from the past week"
        elif "month" in time_range:
            start_date = end_date - timedelta(days=30)
            time_description = "from the past month"
        elif "year" in time_range:
            start_date = end_date - timedelta(days=365)
            time_description = "from the past year"
        elif "all" in time_range or "everything" in time_range:
            start_date = datetime(2000, 1, 1)  # Far in the past
            time_description = "from all time"
        else:
            # Default to recent
            start_date = end_date - timedelta(days=7)
            time_description = "from the past week"
        
        # Retrieve data based on the type
        result_data = []
        formatted_data = []
        
        if normalized_data_type == "weight_log":
            # Get weight logs using existing CRUD
            weight_logs = crud.weight_log.get_weight_logs_by_patient(
                db=self.db,
                patient_id=patient_id,
                skip=0,
                limit=limit
            )
            
            # Format the weight logs
            for log in weight_logs:
                formatted_data.append({
                    "date": log.log_date.strftime("%Y-%m-%d"),
                    "weight": f"{log.weight_kg} kg",
                    "bmi": None  # We'd need height to calculate this
                })
                
            message = f"Here are your {len(weight_logs)} most recent weight logs {time_description}."
            
        elif normalized_data_type == "medications":
            # Get medication requests
            medications = crud.medication_request.get_medication_requests_by_patient(
                db=self.db,
                patient_id=patient_id,
                skip=0,
                limit=limit
            )
            
            # Format the medications
            for med in medications:
                formatted_data.append({
                    "name": med.medication_name,
                    "dosage": med.dosage or "Not specified",
                    "frequency": med.frequency or "Not specified",
                    "status": med.status,
                    "requested_at": med.created_at.strftime("%Y-%m-%d")
                })
                
            message = f"Here are your {len(medications)} most recent medication requests {time_description}."
            
        elif normalized_data_type == "appointments":
            # Get appointments
            appointments = crud.appointment.get_upcoming_by_patient_id(
                db=self.db,
                patient_id=patient_id,
                limit=limit
            )
            
            # Format the appointments
            for appt in appointments:
                formatted_data.append({
                    "date": appt.appointment_datetime.strftime("%Y-%m-%d %H:%M"),
                    "type": appt.appointment_type,
                    "status": appt.status,
                    "notes": appt.notes or "No notes"
                })
                
            message = f"Here are your {len(appointments)} upcoming appointments."
            
        elif normalized_data_type == "side_effects":
            # Get side effect reports
            side_effects = crud.side_effect_report.get_by_patient(
                db=self.db,
                patient_id=patient_id,
                skip=0,
                limit=limit,
                sort_by="reported_at",
                sort_desc=True
            )
            
            # Format the side effects
            for se in side_effects:
                formatted_data.append({
                    "date": se.reported_at.strftime("%Y-%m-%d"),
                    "severity": se.severity.value,
                    "description": se.description[:100] + ("..." if len(se.description) > 100 else ""),
                    "status": se.status
                })
                
            message = f"Here are your {len(side_effects)} most recent side effect reports {time_description}."
        
        # If no data found
        if not formatted_data:
            return ActionResponse(
                success=True,
                result=ActionExecutionResult.SUCCESS,
                message=f"I couldn't find any {normalized_data_type.replace('_', ' ')} data {time_description}.",
                data={
                    "data_type": normalized_data_type,
                    "time_range": time_range,
                    "items": []
                },
                action_type="health_data_retrieve",
                resource_type=normalized_data_type
            )
        
        # Return the data
        return ActionResponse(
            success=True,
            result=ActionExecutionResult.SUCCESS,
            message=message,
            data={
                "data_type": normalized_data_type,
                "time_range": time_range,
                "specific_measure": specific_measure,
                "items": formatted_data
            },
            action_type="health_data_retrieve",
            resource_type=normalized_data_type
        )
        
    except Exception as e:
        logger.error(f"Error retrieving health data: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving health data: {str(e)}"
        )
```

#### 4.2.3 UI for Health Data Display

```javascript
// Enhanced structured response handler for health data visualization
const handleHealthDataResponse = (data) => {
  if (data?.metadata?.action_type === "health_data_retrieve") {
    const dataType = data.data.data_type;
    const items = data.data.items || [];
    
    if (items.length === 0) {
      return (
        <div className="bg-gray-50 p-3 rounded-lg border border-gray-300 mt-2">
          <p>No {dataType.replace('_', ' ')} data found for the specified criteria.</p>
        </div>
      );
    }
    
    // Weight log visualization
    if (dataType === "weight_log") {
      return (
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-300 mt-2">
          <h4 className="font-semibold">Weight Logs</h4>
          <div className="mt-2 max-h-60 overflow-y-auto">
            {items.map((item, idx) => (
              <div key={idx} className="bg-white p-2 rounded mb-2 text-sm">
                <div className="flex justify-between">
                  <span className="font-medium">{item.weight}</span>
                  <span>{item.date}</span>
                </div>
                {item.bmi && <div>BMI: {item.bmi}</div>}
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    // Medication visualization
    if (dataType === "medications") {
      return (
        <div className="bg-green-50 p-3 rounded-lg border border-green-300 mt-2">
          <h4 className="font-semibold">Medications</h4>
          <div className="mt-2 max-h-60 overflow-y-auto">
            {items.map((item, idx) => (
              <div key={idx} className="bg-white p-2 rounded mb-2 text-sm">
                <div className="font-medium">{item.name}</div>
                <div>{item.dosage}, {item.frequency}</div>
                <div className="text-xs text-gray-500">
                  Status: {item.status}, Requested: {item.requested_at}
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    // Similar components for appointments and side effects...
  }
  
  return null;
};
```

#### 4.2.4 Testing Health Data Retrieval

1. Unit Tests
   - Test type normalization for different input phrases
   - Test time range extraction and conversion
   - Test permission checks

2. Integration Tests
   - End-to-end test of data retrieval via LLM
   - Test with various data types and time ranges
   - Test UI visualization components

### 4.3 Phase 3: Enhanced Appointment Management via LLM (Week 3)

#### 4.3.1 Create Appointment Management Template

```python
# Create a template for appointment management
appointment_template = models.Template(
    name="Patient Appointment Management",
    description="Template for patients to manage appointments through natural language",
    is_active=True,
    system_prompt="""You are a medical assistant helping patients manage their appointments.
Extract appointment details including desired date, time, and reason from the patient's request.
For scheduling, identify if the patient has a preferred clinician or time.
For queries about existing appointments, extract key filters.
Be precise and helpful while respecting scheduling constraints.""",
    roles=[models.TemplateRole(role="patient")],
    default_settings={
        "temperature": 0.2,
        "max_tokens": 1024
    },
    actions=[
        {
            "action_type": "appointment_create",
            "description": "Schedule a new appointment",
            "parameters": [
                {"name": "clinician_id", "type": "string", "required": False, "description": "ID or name of the preferred clinician"},
                {"name": "appointment_date", "type": "string", "required": True, "description": "Desired date (YYYY-MM-DD)"},
                {"name": "appointment_time", "type": "string", "required": True, "description": "Desired time (HH:MM)"},
                {"name": "appointment_type", "type": "string", "required": False, "description": "Type of appointment (e.g., follow-up, consultation)"},
                {"name": "reason", "type": "string", "required": False, "description": "Reason for the appointment"},
                {"name": "notes", "type": "string", "required": False, "description": "Additional notes"}
            ]
        },
        {
            "action_type": "appointment_cancel",
            "description": "Cancel an existing appointment",
            "parameters": [
                {"name": "appointment_id", "type": "string", "required": True, "description": "ID of the appointment to cancel"},
                {"name": "cancellation_reason", "type": "string", "required": False, "description": "Reason for cancellation"}
            ]
        }
    ]
)
```

#### 4.3.2 Update the Appointment Creation Handler

The existing appointment handler already has most functionality, but can be enhanced with:

1. Better natural language date/time handling
2. Improved clinician selection
3. Patient-specific response formatting

#### 4.3.3 UI for LLM-Driven Appointment Management

Expand the existing UI components to better support LLM-driven appointment management:

```javascript
// Enhanced appointment management response handling
const handleAppointmentResponse = (data) => {
  if (data?.metadata?.action_type === "appointment_create") {
    return (
      <div className="bg-green-50 p-3 rounded-lg border border-green-300 mt-2">
        <h4 className="font-semibold">Appointment Request Submitted</h4>
        <p>Date: {formatDate(data.data.appointment_time)}</p>
        <p>Status: <span className="capitalize">{data.data.status}</span></p>
        <div className="text-sm mt-2">
          {data.data.status === "pending" ? 
            "You'll receive a notification when your clinician confirms this appointment." :
            "Your appointment has been scheduled. We look forward to seeing you."}
        </div>
      </div>
    );
  }
  
  if (data?.metadata?.action_type === "appointment_cancel") {
    return (
      <div className="bg-blue-50 p-3 rounded-lg border border-blue-300 mt-2">
        <h4 className="font-semibold">Appointment Cancelled</h4>
        <p>Your appointment has been successfully cancelled.</p>
      </div>
    );
  }
  
  return null;
};
```

## Progress

*Last Updated: 2025-04-21T11:04:49-05:00*

### Phase 1: Side Effect Reporting via LLM
- Template foundation for patient LLM-driven side effect reporting created; enables dynamic prompt generation and intent resolution in backend.
- Core backend logic for LLM-driven actions (side effect reporting) implemented, leveraging existing CRUD/service layers.
- Template registration scripts and feature flag configuration in place.
- Documentation expanded for patient-facing LLM features, including security, audit logging, and compliance.
- Fixed timezone bug in chat/LLM actions, ensuring reliable datetime handling across locales.
- Refactored prompt template logic for clarity and safe direct string replacement.
- Initial unit and integration tests for LLM-driven side effect reporting outlined.

#### Next Steps
- Continue development of LLM-driven health data retrieval and appointment management (Phases 2 & 3).
- Monitor and refine based on feedback and metrics.

## 5. Integration and Deployment Strategy

### 5.1 Template Registration

Create a script to register all the LLM action templates in the database:

```python
# In a management script
from app.db.init_db import register_templates

# Register patient-focused templates
PATIENT_TEMPLATES = [
    side_effect_template,
    health_data_template,
    appointment_template
]

def register_patient_templates(db):
    """Register all patient-focused LLM action templates."""
    for template in PATIENT_TEMPLATES:
        register_templates(db, template)
```

### 5.2 Feature Flagging

Implement feature flags to control the rollout of LLM capabilities:

```python
# In config.py
FEATURE_FLAGS = {
    "llm_side_effect_reporting": True,
    "llm_health_data_retrieval": True,
    "llm_appointment_management": True
}

# In the LLM action handler
def should_handle_message(self, message: str) -> bool:
    """Determine if this module should handle the message based on content and feature flags."""
    if not settings.FEATURE_FLAGS.get("llm_side_effect_reporting", False):
        return False
        
    # Rest of the method...
```

### 5.3 Deployment and Testing Sequence

1. **Week 1: Side Effect Reporting**
   - Deploy template and handler
   - Test with a small user group
   - Gather feedback and refine

2. **Week 2: Health Data Retrieval**
   - Deploy template and handler
   - Extend to same test group
   - Monitor performance and refine

3. **Week 3: Appointment Management**
   - Deploy enhanced appointment handling
   - Full rollout to all users
   - Continue monitoring and optimization

## 6. Success Metrics and Monitoring

### 6.1 Key Performance Indicators

1. **Engagement Metrics**
   - Number of LLM-driven patient interactions
   - Completion rate of LLM actions vs. traditional UI
   - User session time and frequency

2. **Clinical Efficiency**
   - Reduction in administrative tasks for clinicians
   - Time saved through patient self-service
   - Information quality in side effect reports

3. **Technical Performance**
   - LLM intent resolution accuracy
   - Response time for LLM actions
   - Error rates and failed interactions

### 6.2 Monitoring Implementation

Create a dedicated monitoring system for LLM actions:

```python
# In a monitoring service
class LLMActionMonitoring:
    """Service for monitoring LLM action performance."""
    
    def log_intent_resolution(self, user_id, action_type, success, duration_ms):
        """Log an intent resolution attempt."""
        # Implementation
        
    def log_action_execution(self, user_id, action_type, success, duration_ms):
        """Log an action execution attempt."""
        # Implementation
        
    def get_performance_metrics(self, start_date, end_date):
        """Get aggregated performance metrics."""
        # Implementation
```

## 7. Future Enhancements

### 7.1 Short-Term Improvements

1. Multi-turn conversation support for complex health data queries
2. Better handling of ambiguous requests with clarification questions
3. Integration with appointment availability to suggest open slots

### 7.2 Long-Term Vision

1. Proactive health insights based on patient data
2. Medication adherence reminders and support
3. Integration with external health resources for education
4. Voice interface for accessibility

## 8. Conclusion

This implementation plan focuses on enhancing the patient experience by adding LLM-driven interfaces to existing functionality. By leveraging the robust backend that's already in place, we can quickly deliver value to patients while setting the foundation for more advanced LLM capabilities in the future.

The patient-first approach will build user engagement and provide immediate benefits, while creating templates for extending similar capabilities to clinician workflows in subsequent phases.
