---
id: PRD-0004
title: He<PERSON>Forge Note Template Library
authors: [<PERSON>, ChatGPT Architect AI]
status: Draft
created: 2025-04-22
updated: 2025-04-22
phase: Incremental
module: [templates, llm_pipeline, notes, admin_ui, migrations]
adr_links: []
tags: [notes, template_library, LLM, documentation]
constraints: [../constraints/PRD-0002-Constraints.md]
---

# HelixForge Note Template Library PRD

**Version:** 0.1 · **Date:** 2025‑04‑22 · **Authors: <AUTHORS>

---

## 1 · Purpose

Provide clinics with a selectable library of standardized note templates (SOAP, CGA, SNF Daily, etc.) that integrate with the existing template‑driven LLM chat system, enabling rapid note creation and gap‑checking with minimal configuration. This builds on the current Template → Role → Action framework while adding library discovery and cloning to support clinic‑specific variations.

---

## 2 · Core Flows

### 2.1 · Template Discovery & Activation (Admin)
- Admin requests list of templates (`GET /templates?category=note`).
- UI shows global (clinic_id = NULL) templates with toggle **Enable**.
- On toggle, system clones template rows with `clinic_id` set and marks active.

### 2.2 · Note Creation (Clinician)
- Clinician opens Chat or **New Note** UI.
- Picks a template; chat agent loads `system_prompt`.
- LLM collects required parameters, validates via `template_validation_service`.
- Executes `note_create` action to `/notes`.
- Result stored; any missing fields triggers LLM follow‑ups.

### 2.3 · Template Maintenance (Global)
- Product team ships new or updated global templates via migration script.
- `version` field increments; `parent_template_id` links child copies.
- Notification banner prompts clinics to review diffs.

---

## 3 · Scope & Constraints

### In Scope
- Two new template columns: `category`, `subtype`.
- Alembic migration seeding global templates (SOAP, APSO, H&P, CGA, SNF_Daily, Discharge, Wound_Note, Fall_Incident).
- Template clone API (`POST /templates/{id}/clone`).
- Admin UI list & toggle.
- Validator map for required fields per subtype.
- Documentation & unit tests.

### Out of Scope
- Smart diff/merge UI for template updates (future).
- Free‑text custom template builder.
- Specialty‑specific validators beyond initial set.
- Mobile app UI (web‑only for admin/clinician).

---

## 4 · Goals & Non‑Goals
| Goals | Non‑Goals |
|-------|-----------|
| Reduce clinician charting time by **30 %** | Replace full EMR documentation |
| Ensure CMS compliance via rule‑based validators | Automated coding/billing |
| Allow clinics to customize note prompts safely | Real‑time voice dictation (handled separately) |

---

## 5 · Personas
1. **Clinic Admin (Alice)** – Enables and tweaks templates.
2. **Clinician (Dr. Ben)** – Uses templates in chat to draft notes.
3. **Compliance Officer (Cara)** – Audits template validators.

---

## 6 · Acceptance Criteria

- [ ] DB migration adds `category`, `subtype` columns; templates table remains backwards compatible.
- [ ] Global note templates seeded with correct prompts and required field definitions.
- [ ] `GET /templates` filters by `category` and returns global + clinic‑specific entries.
- [ ] `POST /templates/{id}/clone` copies record/roles and sets `clinic_id`.
- [ ] Admin UI lists global templates and allows enable/disable per clinic.
- [ ] Chat flow successfully creates SOAP note with follow‑up queries on missing fields.
- [ ] Validators block save if mandatory fields absent.
- [ ] Unit tests cover cloning edge cases and validator rule set.

---

## 7 · Milestones & Timeline
| Date | Milestone |
|------|-----------|
| 2025‑04‑25 | Schema migration & seed script merged |
| 2025‑04‑28 | Template clone endpoint & service |
| 2025‑05‑02 | Validator rules implemented |
| 2025‑05‑05 | Admin UI toggle MVP |
| 2025‑05‑08 | End‑to‑end QA; Release candidate |

---

## 8 · Dependencies
- Constraints document PRD‑0002 Rules (`PRD-0002-Constraints.md`).
- Existing template CRUD service.
- LLM action executor for `note_create`.

---

## 9 · Risks & Mitigations
| Risk | Impact | Mitigation |
|------|--------|------------|
| Validator over‑blocking notes | Clinician frustration | Allow override with audit flag |
| Global template updates override local customizations | Data loss | Use `parent_template_id` copy‑on‑write model |
| Schema migration conflict | Downtime | Reversible migration and feature‑flag gating |

---

## 10 · Next Steps
1. Approve PRD.
2. Generate Tech Spec.
3. Create Alembic migration.
4. Code the clone endpoint and admin UI.
5. QA and user training.


---

## 11 · Tech Implementation Steps (For AI Coding Assistant)

> **Note:** Follow constraints in `PRD-0002-Constraints.md` and keep changes incremental & feature‑flagged.

### 11.1 · Backend (FastAPI)
1. **Alembic Migration**  
   - Add nullable `category`, `subtype`, `parent_template_id` (UUID, FK `templates.id`) columns to `templates`.  
   - Add composite index `(clinic_id, category)` for fast look‑ups.  
   - Seed global note templates via Python data fixture in the same revision.
2. **ORM / CRUD**  
   - Update `models/template.py` and `crud/crud_template.py` to include new fields.  
   - Default `category` = `'note'` for seeded entries.
3. **Clone Service**  
   - `POST /templates/{id}/clone` → `TemplateCloneService.clone(template_id, clinic_id)` which:  
     - Deep‑copies template + roles; sets `clinic_id` and clears `parent_template_id`.  
     - Returns new template DTO.
4. **GET Filter Extension**  
   - Accept `category` query param; default `None` (no filter).  
   - WHERE `(clinic_id IS NULL OR clinic_id=:current)` AND (`category=:category` IF provided).
5. **Validator Map**  
   - Add `validators/note_rules.py` with dict keyed by `subtype`.  
   - `TemplateValidationService` loads rule list on first access and checks required keys.
6. **Feature Flag**  
   - Gate new endpoints under `FEATURE_NOTES_LIBRARY` env var for safe rollout.
7. **Tests** (pytest)  
   - Unit: clone service, validator edge cases.  
   - Integration: end‑to‑end note creation with SOAP template.

### 11.2 · Frontend (React + shadcn/ui)
1. **Admin UI – Template Library Page**  
   - Fetch `GET /templates?category=note&active_only=true`.  
   - List global templates with **Enable** toggle → triggers clone call.  
   - Show badge if local override exists (`parent_template_id` not null).
2. **Clinician Note Picker Modal**  
   - Call same endpoint scoped to clinician role.  
   - Display `subtype` as label chips.
3. **State Management**  
   - Use existing React Query hooks; add `useCloneTemplate` mutation.

### 11.3 · DevOps
1. **DB Migration Pipeline** – Verify reversible down migration.  
2. **Feature Flag Config** – Add to `.env.template` and Helm chart values.  
3. **Seed Job** – Ensure seeding script idempotent in staging & prod.

### 11.4 · Documentation & ADRs
1. Update `docs/architecture/template_system.md` with new fields & clone flow.  
2. Create ADR `ADR-0004-Note-Template-Library.md` capturing design rationale.
3. Add developer HOW‑TO in `docs/patterns/adding_note_template.md`.

---