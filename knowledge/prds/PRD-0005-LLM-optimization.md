### Implementation Brief · “Kill Obvious Per-Request Overhead”

Below is a **task breakdown** you can drop straight into your XGen 02 Scaffolding-Agent (or feed to Claude/<PERSON>oo Code) so the coding assistant makes the right edits without collateral damage.  
File paths match the repo you shared.  Every step is independent—land them in separate PRs or commits for easier rollback.

---

## ❶  Re-use `OpenAIProvider` (singleton)

| Step | File | Diff / Instruction |
|------|------|--------------------|
| **1.1** | `backend/app/main.py` | Inside `init_chatbot_manager()` (already called at import-time) **create once** and stick it on `app.state`:  ```python  from app.core.llm.factory import LLMFactory  from fastapi import FastAPI  ...  def create_llm_provider(app: FastAPI):      provider = LLMFactory.create_provider(          "openai", model="o4-mini", temperature=0.7, max_tokens=300      )      app.state.openai_provider = provider  ```  Then call this after FastAPI instance is created (inside lifespan or right after `FastAPI()` instantiation). |
| **1.2** | `backend/app/services/openai_chat_handler.py` | *Delete* `_get_openai_provider()` and replace its usages with:  ```python  provider = event["app"].state.openai_provider  ```  Pass the `app` object into the event payload inside `event_handlers.py` when you enqueue the chat event. |
| **1.3** | `backend/app/core/llm/providers/openai.py` | Make sure `OpenAIProvider.close()` does **not** close the shared client; guard with a “shutdown” flag so you can still close on `FastAPI` shutdown event. |

**Test**: run 10 parallel `/patients/me/chat` requests and assert only **one** TLS handshake to `api.openai.com` (visible in Wireshark or `lsof -i`).

---

## ❷  Off-load LLM call with FastAPI `BackgroundTasks`

### A. Wire background job

| Step | File | Diff / Instruction |
|------|------|--------------------|
| **2.1** | `backend/app/api/v1/endpoints/chat.py` (or wherever POST `/chat` lives) | ```python  @router.post("/patients/me/chat", status_code=202)  async def patient_chat(…, background_tasks: BackgroundTasks, …):      job_id = str(uuid4())      background_tasks.add_task( run_llm_pipeline, job_id, message, user_ctx, request.app )      return {"job_id": job_id, "status": "queued"}  ``` |
| **2.2** | `backend/app/services/background_jobs.py` (new file) | ```python  async def run_llm_pipeline(job_id: str, message: str, ctx: dict, app):      provider = app.state.openai_provider      ...   # current OpenAI logic here      # persist result to DB and maybe set Redis key f"job:{job_id}"  ``` |

### B. Poll / Push results

* Minimal: new GET `/chat/jobs/{job_id}` returns `{status, message, metadata}` from Redis.  
* Ideal: WebSocket topic `ws/jobs/{job_id}` publishes completion.

Front-end already awaits long-poll; switch to polling `job_id`.

---

## ❸  Cache conversation-history string

| Step | File | Diff / Instruction |
|------|------|--------------------|
| **3.1** | `backend/app/services/openai_chat_handler.py` | 1) At top import Redis client (you already use it elsewhere).<br>2) Before building `formatted_history`, check:  ```python  cache_key = f"conv:{patient_id}:history_str"  if r.exists(cache_key):      formatted_history = r.get(cache_key)  else:      formatted_history = build_history(conversation_history)      r.setex(cache_key, 600, formatted_history)  ```  (600 s TTL is fine.) |
| **3.2** | Ensure you `delete` or update that key whenever you insert a new chat message (`create_chat_message` already in one spot—add the cache bust). |

---

## ❹  Shutdown-cleanup hook

Add:

```python
@app.on_event("shutdown")
async def close_llm_provider():
    provider = app.state.openai_provider
    await provider.close()
```

in `main.py`.

---

## ❺  Validation checklist

1. **Locust/hey** runs: median latency per `/chat` ⇒ from ~3 500 ms ➜ ≤ 50 ms (queued) and streaming finishes async.  
2. Observe **only one** active `AsyncOpenAI` client object in memory (`len(app.state.openai_provider.client._client_pool)` if you expose it).  
3. Token usage unchanged; no duplicate requests.  
4. Background job failures bubble into `/chat/jobs/{id}` as `{status:"error", detail}` and are logged with stack trace.

---

### Copy-ready XGen Prompt Snippet  

> **XGen 02 · “LLM Overhead Refactor”**  
> Goal: implement singleton OpenAIProvider, background tasks, and history cache as described above.  
> Constraints: **no schema changes**, keep API contract (`POST /chat`) but return 202 + `job_id`. Provide a new GET `/chat/jobs/{job_id}`. All new code passes Ruff + mypy. Add unit tests for provider reuse and job polling.  

Paste the breakdown above into your Scaffolding-Agent and let Roo Code generate the diffs; review, run tests, ship.