# PRD-0007: Patient Education Material System

**Version:** 1.0  
**Date:** 2025-05-22  
**Author:** Development Team  
**Status:** Draft  

## Overview

This PRD defines a comprehensive patient education system that enables clinicians and administrators to upload, manage, and assign educational materials to patients. The system supports multiple content types (PDFs, videos, links) and integrates with the existing RAG system for AI-assisted patient education.

## Problem Statement

Currently, PulseTrack lacks a structured way for healthcare providers to share educational materials with patients. Clinicians need the ability to:
- Upload and organize educational content
- Assign specific materials to patients based on their conditions
- Track patient engagement with educational materials
- Leverage AI to reference educational content in conversations

Patients need:
- Easy access to clinician-recommended educational materials
- Progress tracking for assigned content
- Integration with their existing patient portal experience

## Goals & Success Criteria

### Primary Goals
- Enable clinicians to upload and assign educational materials to patients
- Provide patients with an intuitive interface to access assigned educational content
- Track patient engagement and completion of educational materials
- Integrate educational content with the existing RAG system for AI-enhanced conversations

### Success Criteria
- 90% of clinicians can successfully upload and assign materials within 2 minutes
- 85% patient engagement rate with assigned educational materials
- Integration with RAG system allows AI to reference educational content in 70% of relevant conversations
- Support for PDF, video (YouTube), and web link content types

## User Stories

### Admin User Stories
- **EDU-A1**: As an admin, I can upload educational materials (PDFs, videos, links) to the system library
- **EDU-A2**: As an admin, I can organize materials by category, condition, and tags for easy discovery
- **EDU-A3**: As an admin, I can view usage analytics across all clinics and materials
- **EDU-A4**: As an admin, I can manage permissions for who can upload and assign materials

### Clinician User Stories
- **EDU-C1**: As a clinician, I can browse the educational material library and search by condition/topic
- **EDU-C2**: As a clinician, I can assign specific materials to patients with personalized notes
- **EDU-C3**: As a clinician, I can track which patients have viewed/completed assigned materials
- **EDU-C4**: As a clinician, I can upload clinic-specific educational materials
- **EDU-C5**: As a clinician, I can remove or reassign materials as patient needs change

### Patient User Stories
- **EDU-P1**: As a patient, I can view all educational materials assigned to me by my clinicians
- **EDU-P2**: As a patient, I can read PDFs, watch videos, and access web links directly in the portal
- **EDU-P3**: As a patient, I can track my progress through assigned materials
- **EDU-P4**: As a patient, I can ask the AI about content from my assigned materials
- **EDU-P5**: As a patient, I can mark materials as completed and provide feedback

## Technical Requirements

### Database Schema

#### New Models

**EducationMaterial**
```python
class EducationMaterial(BaseModel):
    id: UUID
    title: str
    description: str | None
    type: MaterialType  # PDF, VIDEO, LINK, DOCUMENT
    content_url: str | None  # For links and videos
    file_path: str | None    # For uploaded files
    file_size: int | None    # In bytes
    duration_minutes: int | None  # For videos
    created_by: UUID  # clinician_id
    clinic_id: UUID
    category: str | None
    tags: list[str] = []
    is_public: bool = False  # Available to all clinics
    created_at: datetime
    updated_at: datetime
    
class MaterialType(str, Enum):
    PDF = "pdf"
    VIDEO = "video" 
    LINK = "link"
    DOCUMENT = "document"
```

**PatientEducationAssignment**
```python
class PatientEducationAssignment(BaseModel):
    id: UUID
    patient_id: UUID
    material_id: UUID
    assigned_by: UUID  # clinician_id
    assigned_at: datetime
    due_date: datetime | None
    priority: AssignmentPriority
    clinician_notes: str | None
    status: AssignmentStatus
    
class AssignmentPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    URGENT = "urgent"
    
class AssignmentStatus(str, Enum):
    ASSIGNED = "assigned"
    VIEWED = "viewed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    OVERDUE = "overdue"
```

**EducationProgress**
```python
class EducationProgress(BaseModel):
    id: UUID
    patient_id: UUID
    material_id: UUID
    assignment_id: UUID
    progress_percentage: float  # 0.0 to 100.0
    time_spent_minutes: int
    last_accessed: datetime
    completed_at: datetime | None
    patient_feedback: str | None
    patient_rating: int | None  # 1-5 stars
```

### API Endpoints

#### Material Management
```
# Admin/Clinician endpoints
POST   /api/v1/education/materials              # Upload/create material
GET    /api/v1/education/materials              # List available materials  
GET    /api/v1/education/materials/{id}         # Get material details
PUT    /api/v1/education/materials/{id}         # Update material
DELETE /api/v1/education/materials/{id}         # Delete material
POST   /api/v1/education/materials/upload       # File upload endpoint

# Assignment endpoints
POST   /api/v1/education/assignments            # Assign material to patient
GET    /api/v1/education/assignments            # List assignments (by clinician)
PUT    /api/v1/education/assignments/{id}       # Update assignment
DELETE /api/v1/education/assignments/{id}       # Remove assignment

# Analytics endpoints
GET    /api/v1/education/analytics/materials    # Material usage stats
GET    /api/v1/education/analytics/patients     # Patient engagement stats
```

#### Patient Endpoints
```
GET    /api/v1/education/assigned               # Get assigned materials
GET    /api/v1/education/progress/{material_id} # Get progress for material
POST   /api/v1/education/progress               # Update progress
POST   /api/v1/education/feedback               # Submit feedback/rating
```

### File Storage Strategy

**PDF Files:**
- Upload to Azure Blob Storage in `education-materials/pdfs/` container
- Generate signed URLs for secure access
- Store metadata in EducationMaterial model
- Index text content in RAG system for AI integration

**Video Content:**
- Support YouTube/Vimeo embed URLs
- Direct video file uploads to Azure Blob Storage
- Generate thumbnails for video previews
- Store duration and metadata

**Web Links:**
- Store URL with scraped metadata (title, description, favicon)
- Optional: Screenshot generation for preview
- Link validation and health checking

**Security Considerations:**
- Patient-specific signed URLs with expiration
- Content access logging for audit trails
- File type validation and virus scanning
- HIPAA-compliant storage and access controls

## User Experience Design

### Admin Interface

**Material Library Management:**
- Grid/list view of all materials with search and filtering
- Bulk upload interface with drag-and-drop support
- Category and tag management interface
- Usage analytics dashboard with charts

**Upload Flow:**
1. Choose material type (PDF, Video, Link)
2. Upload file or enter URL
3. Add metadata (title, description, category, tags)
4. Set visibility (clinic-specific or public)
5. Preview and confirm

### Clinician Interface

**Material Assignment Workflow:**
1. From patient detail page, click "Assign Education"
2. Browse/search material library with filters
3. Select materials and add personalized notes
4. Set priority and optional due date
5. Confirm assignment

**Patient Progress Tracking:**
- Dashboard widget showing assigned materials and completion rates
- Individual patient education progress in patient detail view
- Notifications for overdue or completed materials

### Patient Interface

**Education Portal:**
- Dedicated "Education" section in patient navigation
- Card-based layout showing assigned materials with progress
- Priority indicators and due dates
- Search functionality for assigned materials

**Content Viewing Experience:**
- Embedded PDF viewer with progress tracking
- Video player with progress saving
- Clean web link presentation with preview
- Progress indicators and completion checkmarks

**Mobile Responsiveness:**
- Touch-friendly interface for tablets and phones
- Optimized PDF viewing on mobile devices
- Responsive video player
- Offline reading indicators

## Integration Points

### RAG System Integration

**Content Indexing:**
- Automatically index PDF text content in RAG system
- Store educational material references in ContentChunk model
- Enable AI to cite specific educational materials in responses

**AI Conversation Enhancement:**
- "Based on the diabetes management guide I assigned to you..."
- "Let me refer you to section 3 of your medication guide"
- "Have you reviewed the exercise recommendations in your heart health materials?"

**Implementation:**
```python
# Add education_material_id to ContentChunk model
class ContentChunk(BaseModel):
    # ... existing fields
    education_material_id: UUID | None = None
    source_type: SourceType  # Add EDUCATION_MATERIAL enum value
```

### Chat System Integration

**Clinician Chat:**
- Quick assign education materials during conversations
- Reference materials in chat with patient
- AI suggestions for relevant materials based on conversation

**Patient Chat:**
- AI can answer questions about assigned materials
- Progress reminders and encouragement
- Links to specific sections of materials

### Notification System

**Email Notifications:**
- Patient: New material assigned
- Patient: Material due date approaching
- Clinician: Patient completed material
- Clinician: Patient overdue on material

**In-App Notifications:**
- Real-time progress updates
- Assignment confirmations
- Completion celebrations

## Implementation Phases

### Phase 1: Core Infrastructure (Sprint 1-2, ~20 hours)

**Database & Backend:**
- Create EducationMaterial, PatientEducationAssignment, EducationProgress models
- Implement CRUD operations and basic API endpoints
- Set up Azure Blob Storage for file uploads
- Basic file upload and metadata storage

**Admin Interface:**
- Material upload interface (PDF and links only)
- Basic material library with list view
- Simple category and tag management

**Deliverables:**
- Working material upload and storage
- Basic admin interface for material management
- API endpoints for material CRUD operations

### Phase 2: Assignment & Patient Interface (Sprint 3-4, ~25 hours)

**Assignment System:**
- Patient-material assignment functionality
- Progress tracking implementation
- Basic analytics and reporting

**Clinician Interface:**
- Material assignment workflow from patient pages
- Patient progress tracking dashboard
- Search and filter capabilities

**Patient Interface:**
- Education portal with assigned materials
- Basic PDF viewer and link access
- Progress tracking and completion marking

**Deliverables:**
- Full assignment workflow
- Patient education portal
- Progress tracking system

### Phase 3: Enhanced UX & Integration (Sprint 5-6, ~20 hours)

**Advanced Features:**
- Video content support (YouTube embeds + uploads)
- Enhanced PDF viewer with annotations
- Mobile optimization
- Bulk assignment capabilities

**RAG Integration:**
- Index educational content in RAG system
- AI citation of educational materials
- Chat integration for education discussions

**Analytics & Reporting:**
- Usage analytics dashboard
- Patient engagement metrics
- Material effectiveness reporting

**Deliverables:**
- Video content support
- RAG system integration
- Comprehensive analytics
- Mobile-optimized experience

### Phase 4: Advanced Features (Sprint 7-8, ~15 hours)

**Workflow Enhancements:**
- Automated assignment based on patient conditions
- Material recommendation engine
- Advanced progress analytics

**Quality of Life:**
- Notification system implementation
- Feedback and rating system
- Material versioning and updates
- Compliance and audit features

**Deliverables:**
- Automated workflows
- Enhanced user experience
- Compliance features
- Production-ready system

## Success Metrics

### Engagement Metrics
- **Material Assignment Rate:** Number of materials assigned per clinician per month
- **Patient Completion Rate:** Percentage of assigned materials completed within due date
- **Time to Complete:** Average time from assignment to completion
- **Return Rate:** Percentage of patients who revisit completed materials

### Quality Metrics
- **Patient Satisfaction:** Average rating for educational materials (target: 4.0+/5.0)
- **Clinician Adoption:** Percentage of active clinicians using education system (target: 80%+)
- **Material Utilization:** Most and least used materials for optimization

### Technical Metrics
- **Upload Success Rate:** Percentage of successful file uploads (target: 99%+)
- **Page Load Time:** Education portal load time (target: <2 seconds)
- **Mobile Usage:** Percentage of mobile vs desktop access
- **RAG Integration Accuracy:** AI citation accuracy for educational content (target: 90%+)

## Risk Assessment

### Technical Risks
- **File Storage Costs:** Large PDF/video files could increase Azure storage costs
  - *Mitigation:* Implement file size limits and compression
- **Performance Impact:** Large files could slow page loads
  - *Mitigation:* Lazy loading, CDN integration, progressive enhancement

### User Adoption Risks
- **Clinician Workflow Disruption:** Additional steps in patient care workflow
  - *Mitigation:* Streamlined assignment process, integration with existing workflows
- **Patient Engagement:** Low patient completion rates
  - *Mitigation:* Gamification, progress tracking, AI encouragement

### Compliance Risks
- **HIPAA Compliance:** Educational material access logging and security
  - *Mitigation:* Comprehensive audit trails, secure access controls
- **Content Liability:** Responsibility for medical accuracy of uploaded content
  - *Mitigation:* Clear content guidelines, admin review process

## Dependencies

### Technical Dependencies
- Azure Blob Storage configuration and scaling
- RAG system stability for content indexing
- Existing patient/clinician authentication system
- PDF.js or similar library for PDF viewing

### Product Dependencies
- Finalized patient portal navigation structure
- Clinician workflow integration points
- Mobile app development priorities (if applicable)

### External Dependencies
- Legal review of patient education content policies
- Clinical team input on material categorization
- IT security review of file upload processes

## Future Enhancements

### Version 2.0 Features
- **Interactive Content:** Quizzes and assessments
- **Personalized Learning Paths:** AI-driven material recommendations
- **Group Education:** Virtual education sessions and webinars
- **Caregiver Access:** Family member access to patient education materials

### Integration Opportunities
- **EHR Integration:** Sync with external electronic health records
- **Wearable Data:** Education based on activity/health data
- **Telehealth Integration:** Share materials during virtual appointments
- **Pharmacy Integration:** Medication-specific education materials

## Conclusion

The Patient Education Material System represents a significant enhancement to PulseTrack's value proposition, enabling personalized patient education at scale. The phased implementation approach ensures manageable development while delivering value early and often.

The integration with existing RAG and chat systems positions this feature as more than just a document repositoryit becomes an intelligent, AI-enhanced educational platform that actively supports patient care and engagement.

By focusing on clinician workflow efficiency and patient experience, this system will drive improved health outcomes through better-informed patients and more effective care delivery.