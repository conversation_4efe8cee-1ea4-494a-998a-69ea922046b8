{"private": true, "workspaces": ["frontend-admin", "frontend-clinician", "frontend-patient", "packages/*"], "dependencies": {"@clerk/clerk-react": "^5.25.6", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-table": "^8.21.2", "@types/react-calendar": "^3.9.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-calendar": "^5.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.0", "repomix": "^0.3.1", "tailwind-merge": "^3.1.0"}, "devDependencies": {"tailwindcss": "^4.1.3"}, "resolutions": {"@clerk/clerk-react": "5.25.6", "react": "19.1.0", "react-dom": "19.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}