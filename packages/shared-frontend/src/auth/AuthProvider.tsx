import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import {
  <PERSON><PERSON>rovider,
  useUser,
  useSession,
  useClerk,
  SignedIn,
  SignedOut,
} from "@clerk/clerk-react";
// Remove the import from @clerk/types and use our own simplified types
// import type { UserResource, SessionResource } from '@clerk/types';

// Define our own simplified interfaces to avoid the type conflicts
interface SimpleUserResource {
  id?: string;
  [key: string]: any;
}

interface SimpleSessionResource {
  id?: string;
  getToken?: (options?: { template?: string }) => Promise<string | null>;
  [key: string]: any;
}

interface AuthContextProps {
  isAuthenticated: boolean;
  isAuthLoading: boolean; // Renamed from isLoaded for clarity
  user: SimpleUserResource | null | undefined;
  session: SimpleSessionResource | null | undefined;
  signOut: () => Promise<void>;
  acceptInvitation: () => Promise<void>; // Placeholder, needs specific implementation based on Clerk flow
  getToken: (options?: { template?: string }) => Promise<string | null>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
  publishableKey: string; // Renamed from clerkFrontendApi
}

// Internal component to handle Clerk hooks and context provision
const AuthContextWrapper: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { isLoaded, user } = useUser();
  const { session } = useSession();
  // Renamed clerk's signIn to clerkSignIn to avoid naming conflict with our context function
  const {
    isLoaded: isClerkLoaded,
    signIn: clerkSignIn,
    signOut: clerkSignOut,
    handleRedirectCallback,
  } = useClerk();
  const [isAuthLoading, setIsAuthLoading] = useState(!isLoaded);

  useEffect(() => {
    // Update loading state based on Clerk's isLoaded status
    setIsAuthLoading(!isLoaded);
  }, [isLoaded]);

  // Handle Clerk redirect callback if necessary (e.g., after OAuth sign-in)
  // This might need adjustment based on specific routing setup in consuming apps
  useEffect(() => {
    if (isLoaded && window.location.search.includes("__clerk_callback")) {
      handleRedirectCallback({}).catch(console.error); // Pass empty options object
    }
  }, [isLoaded, handleRedirectCallback]);

  const signOut = async () => {
    // Ensure clerkSignOut is available before calling
    if (clerkSignOut) {
      await clerkSignOut();
    } else {
      console.error("Clerk signOut is not available");
      // Optionally redirect or clear local state as a fallback
    }
  };

  const acceptInvitation = async () => {
    // This is a placeholder. The actual implementation depends on how
    // invitations are handled (e.g., redirecting to a specific Clerk flow).
    // Typically, navigating to the Clerk-provided invitation link handles this.
    console.warn(
      "acceptInvitation function needs specific implementation based on Clerk flow.",
    );
    // Example: might involve redirecting or using a specific Clerk function if available
  };

  const getToken = async (options?: {
    template?: string;
  }): Promise<string | null> => {
    // Always request the specific template, merge with any other options passed in
    const mergedOptions = { ...options, template: "CodenamePulsetrack" };
    if (session?.getToken) {
      try {
        return await session.getToken(mergedOptions);
      } catch (error) {
        console.error("Error fetching Clerk token:", error);
        return null;
      }
    }
    console.warn("Clerk session or getToken method not available.");
    return null; // Fallback if session or getToken is not available
  };

  // Add this helper function to determine the correct redirect path
  const getRedirectPathByRole = (user: any) => {
    if (!user) {
      console.log("No user provided to getRedirectPathByRole");
      return null;
    }

    const role = user?.publicMetadata?.role;
    const userRole = Array.isArray(role) ? role[0] : role;

    console.log("Processing role:", userRole);

    switch (userRole) {
      case "clinician":
        return "/clinician/dashboard";
      case "patient":
        return "/patient/dashboard";
      case "admin":
        return "/";
      default:
        console.log(`No specific role found for user ${user.id}`);
        return null;
    }
  };

  // COMMENTED OUT: This effect was causing an infinite redirect loop
  // useEffect(() => {
  //   if (isLoaded && user) {
  //     const redirectPath = getRedirectPathByRole(user);
  //     // Only redirect if we have a valid path and aren't already there
  //     if (redirectPath && window.location.pathname !== redirectPath) {
  //       console.log('Redirecting to:', redirectPath);
  //       window.location.href = redirectPath;
  //     }
  //   }
  // }, [isLoaded, user]);

  const contextValue: AuthContextProps = {
    isAuthenticated: !!user && !!session, // User is authenticated if user and session exist
    isAuthLoading,
    user: user as SimpleUserResource, // Type assertion to our simplified user type
    session: session as SimpleSessionResource, // Type assertion to our simplified session type
    signOut,
    acceptInvitation,
    getToken,
  };

  // Render the provider; consuming app uses context to manage rendering
  return (
    <AuthContext.Provider value={contextValue}>
      {children}{" "}
      {/* Render children directly, without SignedIn/SignedOut wrappers */}
    </AuthContext.Provider>
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({
  children,
  publishableKey,
}) => {
  if (!publishableKey) {
    console.error("Clerk Publishable Key is missing!");
    return <div>Configuration Error: Clerk Publishable Key not provided.</div>;
  }

  // Use default values for Clerk routing rather than environment variables
  // This allows the library to be built and used without requiring specific env vars
  const signInUrl = "/signin";
  const signUpUrl = "/signup";
  const fallbackRedirectUrl = "/"; // Default to home page

  return (
    <ClerkProvider
      publishableKey={publishableKey}
      signInUrl={signInUrl}
      signUpUrl={signUpUrl}
      fallbackRedirectUrl={fallbackRedirectUrl}
    >
      <AuthContextWrapper>{children}</AuthContextWrapper>
    </ClerkProvider>
  );
};

export const useAuth = (): AuthContextProps => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
