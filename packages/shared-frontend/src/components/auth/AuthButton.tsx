import * as React from "react";
import { cn } from "@/lib/utils"; // Assuming shadcn/ui setup includes this utility
import { Button, ButtonProps } from "@/components/ui/button"; // Assuming shadcn/ui Button path

/**
 * A reusable button component for primary authentication actions, wrapping shadcn/ui Button.
 * Provides consistent styling for auth flows.
 *
 * @param {ButtonProps} props - The component props, inheriting from shadcn/ui ButtonProps.
 * @param {string} [props.className] - Optional class name to merge with default styles.
 * @param {React.ReactNode} props.children - The content of the button.
 * @param {...ButtonProps} ...rest - Other standard button attributes (type, onClick, disabled, etc.).
 * @returns {React.ReactElement} The rendered AuthButton component.
 */
const AuthButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Button
        className={cn("w-full", className)} // Default to full width, allow override
        ref={ref}
        {...props}
      >
        {children}
      </Button>
    );
  },
);
AuthButton.displayName = "AuthButton";

export { AuthButton };
