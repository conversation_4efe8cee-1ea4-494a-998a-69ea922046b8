import * as React from "react";
import { cn } from "@/lib/utils"; // Assuming shadcn/ui setup includes this utility

export interface AuthErrorMessageProps
  extends React.HTMLAttributes<HTMLParagraphElement> {
  /**
   * The error message to display. If null or undefined, the component renders nothing.
   */
  message: string | null | undefined;
}

/**
 * A reusable component to display authentication-related errors consistently.
 * Renders only when an error message is provided.
 *
 * @param {AuthErrorMessageProps} props - The component props.
 * @param {string | null | undefined} props.message - The error message text.
 * @param {string} [props.className] - Optional class name to merge with default styles.
 * @param {...React.HTMLAttributes<HTMLParagraphElement>} ...rest - Other standard paragraph attributes.
 * @returns {React.ReactElement | null} The rendered AuthErrorMessage component or null.
 */
const AuthErrorMessage = React.forwardRef<
  HTMLParagraphElement,
  AuthErrorMessageProps
>(({ message, className, ...props }, ref) => {
  if (!message) {
    return null;
  }

  return (
    <p
      ref={ref}
      className={cn("text-sm font-medium text-destructive", className)} // Using shadcn/ui's 'destructive' color variable
      {...props}
    >
      {message}
    </p>
  );
});
AuthErrorMessage.displayName = "AuthErrorMessage";

export { AuthErrorMessage };
