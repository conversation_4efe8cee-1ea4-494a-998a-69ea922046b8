import * as React from "react";
import { cn } from "@/lib/utils"; // Assuming shadcn/ui setup includes this utility
import { Input, InputProps } from "@/components/ui/input"; // Assuming shadcn/ui Input path
import { Label } from "@/components/ui/label"; // Assuming shadcn/ui Label path

export interface AuthInputProps extends InputProps {
  label: string;
  id: string;
  containerClassName?: string;
  className?: string; // Explicitly add className
  type?: string; // Explicitly add type
}

/**
 * A reusable input component for authentication forms, wrapping shadcn/ui Input and Label.
 * Provides consistent styling and label handling for auth flows.
 *
 * @param {AuthInputProps} props - The component props.
 * @param {string} props.id - Unique ID for the input, used to associate the label.
 * @param {string} props.label - The text content for the input's label.
 * @param {string} [props.containerClassName] - Optional class name for the container div.
 * @param {InputProps} ...rest - Other standard HTML input attributes (type, placeholder, value, onChange, etc.).
 * @returns {React.ReactElement} The rendered AuthInput component.
 */
const AuthInput = React.forwardRef<HTMLInputElement, AuthInputProps>(
  ({ className, label, id, containerClassName, type, ...props }, ref) => {
    return (
      <div
        className={cn(
          "grid w-full max-w-sm items-center gap-1.5",
          containerClassName,
        )}
      >
        <Label htmlFor={id}>{label}</Label>
        <Input
          type={type}
          id={id}
          className={cn(className)} // Allow overriding default input styles
          ref={ref}
          {...props}
        />
      </div>
    );
  },
);
AuthInput.displayName = "AuthInput";

export { AuthInput };
