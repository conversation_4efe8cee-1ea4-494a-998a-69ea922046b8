import * as React from "react";
import { cn } from "../../lib/utils"; // Updated import path
import { Loader2 } from "lucide-react"; // Assuming lucide-react is available

export interface AuthLoadingIndicatorProps
  extends React.SVGAttributes<SVGSVGElement> {
  /**
   * Size of the loading indicator icon. Defaults to 24 (maps to h-6 w-6 in Tailwind).
   */
  size?: number;
}

/**
 * A reusable loading indicator component for authentication operations.
 * Displays a spinning loader icon.
 *
 * @param {AuthLoadingIndicatorProps} props - The component props.
 * @param {number} [props.size=24] - The size of the icon.
 * @param {string} [props.className] - Optional class name to merge with default styles.
 * @param {...React.SVGAttributes<SVGSVGElement>} ...rest - Other standard SVG attributes.
 * @returns {React.ReactElement} The rendered AuthLoadingIndicator component.
 */
const AuthLoadingIndicator = React.forwardRef<
  SVGSVGElement,
  AuthLoadingIndicatorProps
>(({ className, size = 24, ...props }, ref) => {
  // Map size prop to Tailwind height/width classes if needed, or use style
  // For simplicity, we'll use h-6 w-6 as default via size prop mapping later if complex sizing needed
  const sizeClasses = `h-${Math.round(size / 4)} w-${Math.round(size / 4)}`; // Basic mapping assuming default Tailwind scale

  return (
    <Loader2
      ref={ref}
      className={cn("animate-spin text-primary", sizeClasses, className)} // Use primary color, spin animation
      // size={size} // lucide-react icons accept a size prop directly
      {...props}
    />
  );
});
AuthLoadingIndicator.displayName = "AuthLoadingIndicator";

export { AuthLoadingIndicator };
