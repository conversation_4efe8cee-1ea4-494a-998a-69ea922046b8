import React from "react";
import { cn } from "../../lib/utils";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { AlertCircle, CheckCircle } from "lucide-react";

interface ActionResponseProps {
  metadata?: {
    action_type: string;
    success: boolean;
    result: string;
    message: string;
    data?: {
      severity?: string;
      status?: string;
      [key: string]: any;
    };
  };
}

export const ActionResponseHandler: React.FC<ActionResponseProps> = ({
  metadata,
}) => {
  if (!metadata) return null;

  const { action_type, success, message, data } = metadata;

  // Handle side effect report responses
  if (action_type === "side_effect_report_create") {
    const severity = data?.severity?.toLowerCase() || "unknown";
    const severityMap = {
      minor: "bg-blue-50 border-blue-300 text-blue-800",
      moderate: "bg-yellow-50 border-yellow-300 text-yellow-800",
      major: "bg-red-50 border-red-300 text-red-800",
    } as const;
    const severityColor =
      severityMap[severity as keyof typeof severityMap] ||
      "bg-gray-50 border-gray-300 text-gray-800";

    return (
      <Alert variant="default" className={cn("mt-4", severityColor)}>
        {success ? (
          <CheckCircle className="h-4 w-4" />
        ) : (
          <AlertCircle className="h-4 w-4" />
        )}
        <AlertTitle>
          Side Effect Report {success ? "Submitted" : "Error"}
        </AlertTitle>
        <AlertDescription>
          {message}
          {data && (
            <div className="mt-2 text-sm">
              <p>
                Severity: <span className="capitalize">{severity}</span>
              </p>
              <p>Status: {data.status}</p>
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  // Default response handler
  return (
    <Alert
      variant={success ? "default" : "destructive"}
      className={cn(
        "mt-4",
        success ? "bg-green-50 border-green-300 text-green-800" : "",
      )}
    >
      {success ? (
        <CheckCircle className="h-4 w-4" />
      ) : (
        <AlertCircle className="h-4 w-4" />
      )}
      <AlertTitle>
        {action_type
          .split("_")
          .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
          .join(" ")}
      </AlertTitle>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};

export default ActionResponseHandler;
