// Custom declaration file to fix type issues with Clerk React components
import { ComponentType, ReactNode } from "react";

declare module "@clerk/clerk-react" {
  export interface ClerkProviderProps {
    publishableKey: string;
    children?: ReactNode;
    [key: string]: any;
  }

  export const ClerkProvider: ComponentType<ClerkProviderProps>;
  export const SignedIn: ComponentType<{ children?: ReactNode }>;
  export const SignedOut: ComponentType<{ children?: ReactNode }>;

  // Defining our own interfaces to avoid conflicts with @clerk/types
  export interface UserResource {
    id?: string;
    [key: string]: any;
  }

  export interface SessionResource {
    id?: string;
    getToken?: (options?: { template?: string }) => Promise<string | null>;
    [key: string]: any;
  }

  export function useUser(): {
    isLoaded: boolean;
    user: UserResource | null | undefined;
    [key: string]: any;
  };

  export function useSession(): {
    session: SessionResource | null | undefined;
    [key: string]: any;
  };

  export function useClerk(): {
    openSignIn: () => Promise<void>;
    signOut: () => Promise<void>;
    handleRedirectCallback: (options: any) => Promise<any>;
    [key: string]: any;
  };
}

// Override @clerk/types definitions to make them compatible
declare module "@clerk/types" {
  export interface UserResource {
    id?: string;
    [key: string]: any;
  }

  export interface SessionResource {
    id?: string;
    getToken?: (options?: { template?: string }) => Promise<string | null>;
    [key: string]: any;
  }
}
