/**
 * Shared date/time utilities for consistent timezone handling across all frontend applications
 * 
 * Convention:
 * - All dates from the backend are in UTC
 * - All dates are displayed in the user's local timezone
 * - Timezone information is always shown to avoid confusion
 * - User inputs are converted to UTC before sending to backend
 */

import { format, formatDistanceToNow, parseISO, isValid, isSameDay, isToday, isYesterday } from 'date-fns';
import { toZonedTime, fromZonedTime, formatInTimeZone } from 'date-fns-tz';

/**
 * Get user's timezone from browser
 */
export const getUserTimezone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

/**
 * Format a UTC date/time to user's local timezone with standard format
 * @param dateInput - ISO string or Date object in UTC
 * @param userTimezone - Optional timezone (defaults to browser timezone)
 * @returns Formatted date/time string with timezone
 */
export const formatDateTime = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    // Format: "Jan 15, 2024 3:30 PM EST"
    return format(zonedDate, 'MMM d, yyyy h:mm a zzz');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format a date for display (without time)
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Formatted date string
 */
export const formatDate = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    // Format: "Jan 15, 2024"
    return format(zonedDate, 'MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format time only
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Formatted time string
 */
export const formatTime = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    // Format: "3:30 PM"
    return format(zonedDate, 'h:mm a');
  } catch (error) {
    console.error('Error formatting time:', error);
    return '';
  }
};

/**
 * Format relative time (e.g., "5 minutes ago")
 * @param dateInput - ISO string or Date object
 * @returns Relative time string
 */
export const formatRelativeTime = (
  dateInput: string | Date | null | undefined
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return '';
  }
};

/**
 * Format appointment time with smart display
 * Shows relative date (Today, Tomorrow) with time and timezone
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Formatted appointment string
 */
export const formatAppointmentTime = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    let datePrefix = '';
    if (isToday(zonedDate)) {
      datePrefix = 'Today';
    } else if (isYesterday(zonedDate)) {
      datePrefix = 'Yesterday';
    } else {
      datePrefix = format(zonedDate, 'MMM d');
    }
    
    // Format: "Today at 3:30 PM EST" or "Jan 15 at 3:30 PM EST"
    return `${datePrefix} at ${format(zonedDate, 'h:mm a zzz')}`;
  } catch (error) {
    console.error('Error formatting appointment time:', error);
    return '';
  }
};

/**
 * Convert local time input to UTC for sending to backend
 * @param dateInput - Date object or string in local time
 * @param userTimezone - Optional timezone (defaults to browser timezone)
 * @returns ISO string in UTC
 */
export const convertToUTC = (
  dateInput: Date | string,
  userTimezone?: string
): string => {
  try {
    const tz = userTimezone || getUserTimezone();
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    
    // Convert from user's timezone to UTC
    const utcDate = fromZonedTime(date, tz);
    return utcDate.toISOString();
  } catch (error) {
    console.error('Error converting to UTC:', error);
    return '';
  }
};

/**
 * Format date for form inputs (YYYY-MM-DD)
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Date string for HTML date input
 */
export const formatForDateInput = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    return format(zonedDate, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error formatting for date input:', error);
    return '';
  }
};

/**
 * Format time for form inputs (HH:mm)
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Time string for HTML time input
 */
export const formatForTimeInput = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    return format(zonedDate, 'HH:mm');
  } catch (error) {
    console.error('Error formatting for time input:', error);
    return '';
  }
};

/**
 * Format chat message timestamp
 * Shows time only for today, date for older messages
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Formatted timestamp
 */
export const formatChatTimestamp = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    if (isToday(zonedDate)) {
      return format(zonedDate, 'h:mm a');
    } else if (isYesterday(zonedDate)) {
      return `Yesterday ${format(zonedDate, 'h:mm a')}`;
    } else {
      return format(zonedDate, 'MMM d, h:mm a');
    }
  } catch (error) {
    console.error('Error formatting chat timestamp:', error);
    return '';
  }
};

/**
 * Get timezone abbreviation (e.g., EST, PST)
 * @param timezone - Timezone string
 * @returns Timezone abbreviation
 */
export const getTimezoneAbbreviation = (timezone?: string): string => {
  try {
    const tz = timezone || getUserTimezone();
    const now = new Date();
    return formatInTimeZone(now, tz, 'zzz');
  } catch (error) {
    console.error('Error getting timezone abbreviation:', error);
    return '';
  }
};

/**
 * Format datetime for HTML datetime-local input (YYYY-MM-DDTHH:mm)
 * @param dateInput - ISO string or Date object
 * @param userTimezone - Optional timezone
 * @returns Datetime string for HTML datetime-local input
 */
export const formatForDateTimeInput = (
  dateInput: string | Date | null | undefined,
  userTimezone?: string
): string => {
  if (!dateInput) return '';
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return '';
    
    const tz = userTimezone || getUserTimezone();
    const zonedDate = toZonedTime(date, tz);
    
    // Format as YYYY-MM-DDTHH:mm for datetime-local input
    return format(zonedDate, "yyyy-MM-dd'T'HH:mm");
  } catch (error) {
    console.error('Error formatting for datetime input:', error);
    return '';
  }
};

/**
 * Check if a date is in the past
 * @param dateInput - ISO string or Date object
 * @returns boolean
 */
export const isPastDate = (dateInput: string | Date | null | undefined): boolean => {
  if (!dateInput) return false;
  
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    if (!isValid(date)) return false;
    
    return date < new Date();
  } catch (error) {
    console.error('Error checking if date is past:', error);
    return false;
  }
};