# LLM-Driven API Actions Testing

This directory contains scripts for testing the LLM-Driven API Actions functionality in a user-oriented manner.

## Test Script

The `test_llm_actions.py` script performs end-to-end testing of the LLM-driven API actions by:

1. Creating a test template if one doesn't exist
2. Testing natural language processing with the text endpoint
3. Testing direct action execution with the template actions endpoint
4. Providing a comprehensive test report

## Running in Docker Environment

Since PulseTrack runs in a Docker container stack, you need to execute the script within the container environment:

```powershell
# From the project root directory
docker exec -it pulsetrack-backend python scripts/test_llm_actions.py --url http://localhost:8000/api/v1
```

If you need to authenticate with credentials:

```powershell
docker exec -it pulsetrack-backend python scripts/test_llm_actions.py --url http://localhost:8000/api/v1 --email <EMAIL> --password your-password
```

Or with an existing token:

```powershell
docker exec -it pulsetrack-backend python scripts/test_llm_actions.py --url http://localhost:8000/api/v1 --token your-token
```

## Dependencies

The script requires the following Python packages:
- requests
- rich

These should already be available in the Docker container, but if you're running locally, you may need to install them:

```powershell
pip install requests rich
```

## Test Cases

The comprehensive test suite includes:

1. **Natural Language Tests**:
   - Creating an appointment using natural language
   - Creating a clinical note using natural language
   - Testing error handling with invalid actions

2. **Direct Action Tests**:
   - Direct appointment creation with explicit parameters
   - Direct note creation with explicit parameters

## Extending Tests

To add more test cases, modify the `test_cases` and `direct_test_cases` lists in the `run_comprehensive_tests` function.
