#!/usr/bin/env python
"""
ai_context_helper.py - Helper script to generate AI context for various AI tools.

This script provides different AI context formats suitable for various AI tools:
  • Standard digest (ai.md format)
  • Git-enriched digest (extended ai.md with git info)
  • Full context dump (including code snippets)
  • Focused context (filtered to specific areas of interest)

Usage:
    poetry run python scripts/ai_context_helper.py [--tool TOOL] [--focus FOCUS] [--output OUTPUT]

Options:
    --tool TOOL     Target AI tool format (default: standard)
                    Options: standard, git, full, focused
    --focus FOCUS   Focus area when using 'focused' tool (default: code)
                    Options: code, docs, knowledge, data
    --output OUTPUT Output file path (default: clipboard)
"""
from __future__ import annotations

import argparse
import os
import re
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Optional

# Try to import pyperclip for clipboard support
try:
    import pyperclip

    HAS_CLIPBOARD = True
except ImportError:
    HAS_CLIPBOARD = False

# Import functions from other scripts
sys.path.append(str(Path(__file__).resolve().parent))
try:
    # Import from render_digests.py
    from render_digests import (
        ROOT,
        build_digest as build_standard_digest,
    )

    # Import from render_git_digest.py
    from render_git_digest import (
        build_enhanced_digest as build_git_digest,
    )

    # Import from git_context.py
    from git_context import (
        get_repo_info,
        get_git_status,
    )

    IMPORTS_OK = True
except ImportError as e:
    print(f"Warning: Could not import from helper scripts: {e}")
    IMPORTS_OK = False

# File patterns for different focus areas
FOCUS_PATTERNS = {
    "code": [r"^src/.*\.py$", r"^scripts/.*\.py$", r"^tests/.*\.py$"],
    "docs": [r"^docs/", r"^README\.md$", r".*\.rst$"],
    "knowledge": [r"^knowledge/"],
    "data": [r"^data/", r".*\.csv$", r".*\.json$", r".*\.yaml$", r".*\.yml$"],
}


def get_focused_files(focus: str) -> List[str]:
    """Get files that match the focus area."""
    if focus not in FOCUS_PATTERNS:
        focus = "code"  # Default to code focus

    patterns = FOCUS_PATTERNS[focus]

    # Walk the directory and find matching files
    matched_files = []
    for root, _, files in os.walk(str(ROOT)):
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, str(ROOT))

            if any(re.match(pattern, rel_path) for pattern in patterns):
                matched_files.append(rel_path)

    # Sort files by name
    matched_files.sort()
    return matched_files


def read_file_content(rel_path: str, max_lines: int = 1000) -> str:
    """Read content of a file with a maximum number of lines."""
    file_path = Path(ROOT) / rel_path
    if not file_path.exists():
        return f"File not found: {rel_path}"

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = list(f)[:max_lines]

        if len(lines) >= max_lines:
            lines.append(f"\n... (truncated, showing {max_lines} lines)\n")

        return "".join(lines)
    except Exception as e:
        return f"Error reading file {rel_path}: {str(e)}"


def build_full_context() -> str:
    """Build a full context including standard digest, git info, and key file contents."""
    if not IMPORTS_OK:
        return "Error: Required imports not available. Please run from the scripts directory."

    # Start with git-enriched digest
    full_context = build_git_digest()

    # Add TOC for additional sections
    toc = [
        "",
        "## Additional Context Sections",
        "- [Key Project Files](#key-project-files)",
        "- [Project Structure](#project-structure)",
    ]

    # Get important Python files
    src_files = get_focused_files("code")
    if len(src_files) > 10:
        src_files = src_files[:10]  # Limit to 10 files

    # Add file contents
    file_sections = [
        "",
        "## Key Project Files",
    ]

    for file_path in src_files:
        file_sections.extend(
            [
                f"### {file_path}",
                "```python",
                read_file_content(file_path, max_lines=300),
                "```",
                "",
            ]
        )

    # Get project structure
    try:
        # Use directory_tree.py if it exists
        tree_script = Path(ROOT) / "scripts" / "directory_tree.py"
        if tree_script.exists():
            result = subprocess.run(
                [sys.executable, str(tree_script)],
                cwd=str(ROOT),
                capture_output=True,
                text=True,
                check=False,
            )
            project_structure = result.stdout
        else:
            # Use a simple ls -R alternative
            result = subprocess.run(
                [
                    "find",
                    ".",
                    "-type",
                    "f",
                    "-not",
                    "-path",
                    "*/\\.*",
                    "-o",
                    "-type",
                    "d",
                    "-not",
                    "-path",
                    "*/\\.*",
                ],
                cwd=str(ROOT),
                capture_output=True,
                text=True,
                check=False,
            )
            project_structure = result.stdout
    except Exception as e:
        project_structure = f"Error getting project structure: {str(e)}"

    structure_section = ["", "## Project Structure", "```", project_structure, "```"]

    # Combine everything
    parts = [
        full_context,
        "\n".join(toc),
        "\n".join(file_sections),
        "\n".join(structure_section),
    ]
    return "\n\n".join(parts)


def build_focused_context(focus: str) -> str:  # noqa: C901
    """Build a focused context targeting a specific area."""
    if not IMPORTS_OK:
        return "Error: Required imports not available. Please run from the scripts directory."

    # Add header with generation info
    header = [
        f"# Focused AI Context: {focus.capitalize()}",
        f"*Generated: {datetime.utcnow().isoformat()}Z*",
        "",
    ]

    # Add git status section (just current branch and changed files)
    repo_info = get_repo_info()
    status = get_git_status()

    git_section = [
        "## Git Status",
        f"- **Current Branch:** {repo_info['current_branch']}",
    ]

    # Only add changed files relevant to the focus area
    patterns = FOCUS_PATTERNS.get(focus, FOCUS_PATTERNS["code"])

    for status_type, files in status.items():
        relevant_files = []
        for file in files:
            if isinstance(file, dict):  # For renamed files
                file_path = file.get("to", "")
            else:
                file_path = file

            if any(re.match(pattern, file_path) for pattern in patterns):
                relevant_files.append(file_path)

        if relevant_files:
            git_section.append(
                f"- **{status_type.capitalize()}:** {', '.join(relevant_files[:5])}"
            )
            if len(relevant_files) > 5:
                git_section.append(
                    f"  - ... and {len(relevant_files) - 5} more {status_type} files"
                )

    # Get relevant files for the focus area
    focused_files = get_focused_files(focus)

    # Limit to a reasonable number
    if len(focused_files) > 15:
        files_to_include = focused_files[:15]
    else:
        files_to_include = focused_files

    # Add file contents
    file_sections = [
        "",
        f"## {focus.capitalize()} Files",
    ]

    if len(focused_files) > len(files_to_include):
        file_sections.append(
            f"> Showing {len(files_to_include)} of {len(focused_files)} relevant files"
        )

    for file_path in files_to_include:
        ext = os.path.splitext(file_path)[1]
        lang = "python" if ext == ".py" else "markdown" if ext == ".md" else ""

        file_sections.extend(
            [
                f"### {file_path}",
                f"```{lang}",
                read_file_content(file_path, max_lines=200),
                "```",
                "",
            ]
        )

    # Combine everything
    parts = ["\n".join(header), "\n".join(git_section), "\n".join(file_sections)]
    return "\n\n".join(parts)


def write_output(content: str, output_path: Optional[str] = None) -> None:
    """Write content to file or clipboard."""
    if output_path:
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        output_file.write_text(content, encoding="utf-8")
        print(f"Output written to {output_path}")
    elif HAS_CLIPBOARD:
        pyperclip.copy(content)
        print("Output copied to clipboard!")
    else:
        # Just print to stdout if no clipboard support
        print(content)


def main():
    """Main function to parse args and run the script."""
    parser = argparse.ArgumentParser(
        description="Generate AI context for various AI tools"
    )
    parser.add_argument(
        "--tool",
        choices=["standard", "git", "full", "focused"],
        default="standard",
        help="Target AI tool format (default: standard)",
    )
    parser.add_argument(
        "--focus",
        choices=["code", "docs", "knowledge", "data"],
        default="code",
        help="Focus area when using 'focused' tool (default: code)",
    )
    parser.add_argument("--output", help="Output file path (default: clipboard)")

    args = parser.parse_args()

    # Generate the appropriate context
    if args.tool == "standard":
        if IMPORTS_OK:
            content = build_standard_digest()
        else:
            content = "Error: Standard digest imports not available"
    elif args.tool == "git":
        if IMPORTS_OK:
            content = build_git_digest()
        else:
            content = "Error: Git digest imports not available"
    elif args.tool == "full":
        content = build_full_context()
    elif args.tool == "focused":
        content = build_focused_context(args.focus)
    else:
        content = "Invalid tool specified"

    # Write the output
    write_output(content, args.output)


if __name__ == "__main__":
    main()
