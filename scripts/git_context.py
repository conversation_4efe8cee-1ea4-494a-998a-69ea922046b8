#!/usr/bin/env python3
"""
Generate a git context report for the repository.
This script analyzes the git history and generates a report in various formats.
"""

import argparse
import datetime
import os
import subprocess
import sys
from pathlib import Path
import platform

def run_git_command(command):
    """Run a git command and return the output."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running git command: {command}")
        print(f"Error: {e.stderr}")
        return ""

def get_recent_commits(depth=10):
    """Get the most recent commits."""
    # Use double quotes for Windows compatibility
    if platform.system() == "Windows":
        command = f'git log --pretty=format:"%h|%an|%ad|%s" --date=short -n {depth}'
    else:
        command = f"git log --pretty=format:'%h|%an|%ad|%s' --date=short -n {depth}"
    
    output = run_git_command(command)
    
    commits = []
    for line in output.split("\n"):
        if line:
            parts = line.split("|", 3)
            if len(parts) == 4:
                commit_hash, author, date, message = parts
                commits.append({
                    "hash": commit_hash,
                    "author": author,
                    "date": date,
                    "message": message
                })
    
    return commits

def get_active_branches():
    """Get the active branches in the repository."""
    command = "git branch -a --sort=-committerdate"
    output = run_git_command(command)
    
    branches = []
    for line in output.split("\n"):
        line = line.strip()
        if line:
            # Remove the "* " prefix from the current branch
            if line.startswith("* "):
                line = line[2:]
            # Skip remote branches for simplicity
            if "remotes/origin/" in line:
                continue
            branches.append(line)
    
    return branches[:10]  # Return top 10 branches

def get_file_changes(days=7):
    """Get files changed in the last X days."""
    since_date = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%Y-%m-%d")
    
    # Use double quotes for Windows compatibility
    if platform.system() == "Windows":
        command = f'git log --name-only --pretty=format:"COMMIT:%h" --since="{since_date}"'
    else:
        command = f"git log --name-only --pretty=format:'COMMIT:%h' --since='{since_date}'"
    
    output = run_git_command(command)
    
    files = {}
    current_commit = None
    
    for line in output.split("\n"):
        if line.startswith("COMMIT:"):
            current_commit = line[7:]
        elif line and current_commit:
            if line not in files:
                files[line] = 0
            files[line] += 1
    
    # Sort files by number of changes (descending)
    sorted_files = sorted(files.items(), key=lambda x: x[1], reverse=True)
    return sorted_files[:20]  # Return top 20 files

def generate_markdown_report(commits, branches, files, depth):
    """Generate a markdown report."""
    report = "# Git Context Report\n\n"
    report += f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    report += "## Recent Commits\n\n"
    for commit in commits:
        report += f"- **{commit['hash']}** ({commit['date']}) by *{commit['author']}*: {commit['message']}\n"
    
    report += "\n## Active Branches\n\n"
    for branch in branches:
        report += f"- {branch}\n"
    
    report += "\n## Recently Changed Files\n\n"
    for file_path, count in files:
        report += f"- {file_path} ({count} changes)\n"
    
    if depth == "full":
        # Add more sections for a full report
        report += "\n## Contributors\n\n"
        contributors_cmd = "git shortlog -sne --all"
        contributors = run_git_command(contributors_cmd)
        for line in contributors.split("\n"):
            if line:
                report += f"- {line.strip()}\n"
    
    return report

def main():
    """Generate a git context report."""
    parser = argparse.ArgumentParser(description="Generate a git context report")
    parser.add_argument("--output", "-o", type=str, default="git-context.md",
                        help="Output file path")
    parser.add_argument("--format", "-f", type=str, choices=["md", "txt"], default="md",
                        help="Output format (md or txt)")
    parser.add_argument("--depth", "-d", type=str, choices=["minimal", "medium", "full"], default="medium",
                        help="Depth of the report")
    args = parser.parse_args()
    
    # Determine the number of commits to show based on depth
    commit_depth = {"minimal": 5, "medium": 10, "full": 20}[args.depth]
    
    # Get the git data
    commits = get_recent_commits(depth=commit_depth)
    branches = get_active_branches()
    files = get_file_changes(days=7)
    
    # Generate the report
    if args.format == "md":
        report = generate_markdown_report(commits, branches, files, args.depth)
    else:
        # Plain text format would be similar but without markdown formatting
        report = generate_markdown_report(commits, branches, files, args.depth)
    
    # Write the report to the output file
    if args.output == "-":
        print(report)
    else:
        output_path = Path(args.output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(report)
        print(f"Git context report written to: {output_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
