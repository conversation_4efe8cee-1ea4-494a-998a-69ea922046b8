#!/usr/bin/env python3
"""
Initialize the knowledge repository structure for PulseTrack.
This script creates the necessary directories and files for the knowledge management system.
"""

import os
import sys
from pathlib import Path

KNOWLEDGE_DIRS = [
    "digests",
    "snapshots",
    "progress",
    "git-context",
]

def main():
    """Initialize the knowledge repository structure."""
    # Get the project root directory
    project_root = Path(__file__).resolve().parent.parent
    knowledge_dir = project_root / "knowledge"

    # Create the knowledge directory if it doesn't exist
    if not knowledge_dir.exists():
        print(f"Creating knowledge directory: {knowledge_dir}")
        knowledge_dir.mkdir(exist_ok=True)
    else:
        print(f"Knowledge directory already exists: {knowledge_dir}")

    # Create subdirectories
    for subdir in KNOWLEDGE_DIRS:
        subdir_path = knowledge_dir / subdir
        if not subdir_path.exists():
            print(f"Creating subdirectory: {subdir_path}")
            subdir_path.mkdir(exist_ok=True)
        else:
            print(f"Subdirectory already exists: {subdir_path}")

    # Initialize ai.md if it doesn't exist
    ai_md_path = knowledge_dir / "digests" / "ai.md"
    if not ai_md_path.exists():
        print(f"Creating initial ai.md file: {ai_md_path}")
        with open(ai_md_path, "w") as f:
            f.write("# PulseTrack AI Digest\n\n")
            f.write("This file contains AI-generated insights about the PulseTrack project.\n\n")
            f.write("## Project Overview\n\n")
            f.write("PulseTrack is a healthcare platform with multiple frontends.\n\n")
            f.write("## Recent Activity\n\n")
            f.write("This section will be automatically updated by the CI pipeline.\n\n")
    else:
        print(f"ai.md file already exists: {ai_md_path}")

    # Initialize progress.md if it doesn't exist
    progress_md_path = knowledge_dir / "progress.md"
    if not progress_md_path.exists():
        print(f"Creating initial progress.md file: {progress_md_path}")
        with open(progress_md_path, "w") as f:
            f.write("# PulseTrack Project Progress\n\n")
            f.write("This file tracks the overall progress of the PulseTrack project.\n\n")
            f.write("## Project Phases\n\n")
            f.write("- [x] Initial setup\n")
            f.write("- [ ] Feature implementation\n")
            f.write("- [ ] Testing\n")
            f.write("- [ ] Deployment\n\n")
            f.write("## PRD Implementation Progress\n\n")
            f.write("This section will be automatically updated based on the PRDs in the project.\n")
    else:
        print(f"progress.md file already exists: {progress_md_path}")

    print("Knowledge repository structure initialized successfully.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
