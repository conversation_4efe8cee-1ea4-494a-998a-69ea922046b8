#!/usr/bin/env python3
"""
Migrate documentation from the old structure (.memory-bank and .xgen) to the new structure.
This script copies relevant files to their new locations while preserving the original files.
"""

import os
import shutil
import sys
from pathlib import Path
import re

def ensure_directory(directory_path):
    """Create a directory if it doesn't exist."""
    os.makedirs(directory_path, exist_ok=True)
    print(f"Ensured directory exists: {directory_path}")

def copy_file(src, dest):
    """Copy a file from source to destination."""
    shutil.copy2(src, dest)
    print(f"Copied: {src} -> {dest}")

def main():
    """Migrate documentation from the old structure to the new structure."""
    project_root = Path(__file__).resolve().parent.parent
    docs_dir = project_root / "docs"
    
    # Ensure the new directory structure exists
    for directory in ["architecture", "planning", "prds"]:
        ensure_directory(str(docs_dir / directory))
    
    # Map of source files to their new destinations
    # Format: (source_path, destination_path)
    file_mappings = [
        # Memory Bank files
        (docs_dir / ".memory-bank" / "progress.md", docs_dir / "planning" / "progress_tracking.md"),
        (docs_dir / ".memory-bank" / "systemPatterns.md", docs_dir / "architecture" / "system_patterns.md"),
        (docs_dir / ".memory-bank" / "productContext.md", docs_dir / "planning" / "product_context.md"),
        
        # XGen files
        (docs_dir / ".xgen" / "master_prd.md", docs_dir / "prds" / "master_prd.md"),
        (docs_dir / ".xgen" / "master_chat_prd.md", docs_dir / "prds" / "chat_system_prd.md"),
        (docs_dir / ".xgen" / "rag_architecture_v1.md", docs_dir / "architecture" / "rag_system.md"),
        (docs_dir / ".xgen" / "project_steps.md", docs_dir / "planning" / "implementation_steps.md"),
        (docs_dir / ".xgen" / "glossary.md", docs_dir / "planning" / "glossary.md"),
    ]
    
    # Copy the PRDs
    prds_source_dir = docs_dir / ".xgen" / "prds"
    prds_target_dir = docs_dir / "prds"
    
    if prds_source_dir.exists() and prds_source_dir.is_dir():
        prd_files = list(prds_source_dir.glob("**/*.md"))
        print(f"Found {len(prd_files)} PRD files to migrate.")
        
        for prd_file in prd_files:
            # Get the relative path from the source PRD directory
            rel_path = prd_file.relative_to(prds_source_dir)
            # Create the target directory structure
            target_dir = prds_target_dir / rel_path.parent
            ensure_directory(str(target_dir))
            # Copy the file
            copy_file(str(prd_file), str(target_dir / prd_file.name))
    
    # Copy the individual mapped files
    for src_path, dest_path in file_mappings:
        if src_path.exists():
            # Create the parent directory if it doesn't exist
            ensure_directory(str(dest_path.parent))
            # Copy the file
            copy_file(str(src_path), str(dest_path))
        else:
            print(f"Warning: Source file not found: {src_path}")
    
    print("\nMigration completed successfully.")
    print("\nImportant: The original files in .memory-bank and .xgen have been preserved.")
    print("After verifying the migration, you may add a .gitignore entry for these directories or remove them.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
