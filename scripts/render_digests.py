#!/usr/bin/env python
"""
render_digests.py - Generate knowledge digests for the PulseTrack project:
1. ai.md - AI-generated insights about the project
2. project_history.md - Chronological history of the project

Usage
-----
    python scripts/render_digests.py [output_dir]

If *output_dir* is omitted, the script writes to "knowledge/digests".

The script is idempotent and safe to run in CI.
"""
from __future__ import annotations

import hashlib
import itertools
import datetime
import re
from pathlib import Path
import os
import subprocess

ROOT = Path(__file__).resolve().parent.parent  # repo root
KNOWLEDGE = ROOT / "knowledge"
DOCS = ROOT / "docs"
DEFAULT_OUT_DIR = KNOWLEDGE / "digests"
DEFAULT_AI_DIGEST = DEFAULT_OUT_DIR / "ai.md"
DEFAULT_HISTORY_DIGEST = DEFAULT_OUT_DIR / "project_history.md"

# Updated path mapping to use the new documentation structure
PATH_MAPPING = {
    "Product Context": DOCS / "planning" / "product_context.md",
    "Progress": KNOWLEDGE / "progress.md",
    "System Patterns": DOCS / "architecture" / "system_patterns.md"
}

# Additional paths for history digest
HISTORY_PATHS = {
    "Decision Log": DOCS / ".memory-bank" / "decisionLog.md",
    "Active Context": DOCS / ".memory-bank" / "activeContext.md",
    "Progress": DOCS / ".memory-bank" / "progress.md",
    "PRDs": DOCS / "knowledge" / "prds"
}

HR = "\n\n---\n\n"


def read_first_lines(path: Path, limit: int) -> str:
    """Read the first N lines from a file."""
    try:
        with open(path, encoding="utf-8") as f:
            return "".join(itertools.islice(f, limit)).rstrip()
    except FileNotFoundError:
        print(f"Warning: File not found: {path}")
        return f"*File not found: {path}*"


def read_file(path: Path) -> str:
    """Read the entire contents of a file."""
    try:
        with open(path, encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        print(f"Warning: File not found: {path}")
        return f"*File not found: {path}*"
    except Exception as e:
        print(f"Error reading file {path}: {str(e)}")
        return f"*Error reading file: {str(e)}*"


def gather_prds() -> str:
    """Gather information about PRDs from the new structure."""
    prd_dir = DOCS / "prds"
    parts = ["## PRDs\n"]
    
    if not prd_dir.exists():
        return "## PRDs\n\n*PRD directory not found*\n"
        
    for md in sorted(prd_dir.glob("*.md")):
        try:
            with open(md, encoding="utf-8") as f:
                content = f.read()
                
            # Extract title from markdown heading
            title = md.stem
            for line in content.split("\n"):
                if line.startswith("# "):
                    title = line[2:].strip()
                    break
                    
            # Count task items
            total_tasks = content.count("- [ ]") + content.count("- [x]")
            completed_tasks = content.count("- [x]")
            status = "Not started"
            if total_tasks > 0:
                completion = int((completed_tasks / total_tasks) * 100)
                if completion == 100:
                    status = "Complete"
                elif completion > 0:
                    status = f"In progress ({completion}%)"
            
            parts.append(f"* **{title}** ({md.stem}) – {status}")
        except Exception as e:
            parts.append(f"* **{md.stem}** – Error: {str(e)}")
    
    return "\n".join(parts) + "\n"


def run_git_cmd(cmd, encoding="utf-8"):
    """Run a git command and return the output."""
    try:
        result = subprocess.run(
            ["git"] + cmd,
            cwd=str(ROOT),
            capture_output=True,
            text=True,
            encoding=encoding,  # Explicitly set encoding
            errors="replace",  # Replace problematic characters
            check=False,  # Don't fail on non-zero exit
        )
        return result.stdout.strip()
    except Exception as e:
        return f"Error running git command: {e}"


def gather_all_changes() -> str:
    """Collect all current changes (staged and unstaged), robustly excluding ai.md and other digests."""
    status_output = run_git_cmd(["status", "--short"])
    if not status_output:
        return "No changes detected in the working directory."
    changes = []
    # Always use POSIX (forward slash) relative paths from repo root for exclusion
    def to_posix_relpath(p):
        return os.path.relpath(p, ROOT).replace("\\", "/")
    EXCLUDED_PATHS = {
        to_posix_relpath(DEFAULT_AI_DIGEST),
        to_posix_relpath(DEFAULT_HISTORY_DIGEST),
    }
    for line in status_output.split("\n"):
        if not line.strip():
            continue
        status_code = line[:2]
        filename = line[3:].strip()
        rel_filename = os.path.relpath(os.path.join(ROOT, filename), ROOT).replace("\\", "/")
        if rel_filename in EXCLUDED_PATHS:
            continue
        if any(filename.endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bin', '.exe']):
            changes.append(f"### {filename}\n*Binary file - diff not shown*\n")
            continue
        if status_code.startswith('??'):
            try:
                with open(os.path.join(ROOT, filename), 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                changes.append(f"### {filename} (New file)\n```\n{content}\n```\n")
            except Exception as e:
                changes.append(f"### {filename} (New file)\n*Error reading file: {str(e)}*\n")
        else:
            diff_output = run_git_cmd(["diff", "--", filename])
            if diff_output:
                changes.append(f"### {filename}\n```diff\n{diff_output}\n```\n")
    if not changes:
        return "No substantial changes detected in the working directory."
    return "\n".join(changes)


def gather_staged_changes() -> str:
    """Get all staged (but uncommitted) diffs, robustly excluding ai.md and other digests."""
    def to_posix_relpath(p):
        return os.path.relpath(p, ROOT).replace("\\", "/")
    EXCLUDED_PATHS = {
        to_posix_relpath(DEFAULT_AI_DIGEST),
        to_posix_relpath(DEFAULT_HISTORY_DIGEST),
    }
    staged_files = run_git_cmd(["diff", "--cached", "--name-only"]).splitlines()
    filtered_files = []
    for f in staged_files:
        rel_filename = os.path.relpath(os.path.join(ROOT, f), ROOT).replace("\\", "/")
        if rel_filename not in EXCLUDED_PATHS:
            filtered_files.append(f)
    if not filtered_files:
        return "No staged (uncommitted) changes detected."
    staged_diffs = []
    for filename in filtered_files:
        diff = run_git_cmd(["diff", "--cached", "--", filename])
        if diff:
            staged_diffs.append(f"### {filename}\n```diff\n{diff}\n```\n")
    if not staged_diffs:
        return "No staged (uncommitted) changes detected."
    return "## Staged (Uncommitted) Changes\n\n" + "\n".join(staged_diffs)


def gather_git_context() -> str:
    """Collect git context info with all changes, including staged diffs."""
    # Get basic info
    git_branch = run_git_cmd(["rev-parse", "--abbrev-ref", "HEAD"])
    
    # Get recent commits with diffs
    git_log = run_git_cmd([
        "log", "-n", "10", "--pretty=format:%h | %an | %ad | %s", "--date=short"
    ])
    # For each commit, append the diff (up to N lines for brevity)
    git_log_lines = git_log.split("\n")
    git_log_with_diffs = []
    for line in git_log_lines:
        commit_hash = line.split("|")[0].strip()
        diff = run_git_cmd(["show", commit_hash, "--stat", "--oneline", "--color=never", "-U3"])
        # Limit diff output for readability
        diff_lines = diff.split("\n")[:40]
        git_log_with_diffs.append(line + "\n" + "\n".join(diff_lines) + "\n---\n")
    git_log_full = "\n".join(git_log_with_diffs)
    
    # Get current status
    git_status = run_git_cmd(["status", "--porcelain"])
    
    # Get all current changes
    changes = gather_all_changes()
    
    # Get staged (uncommitted) changes
    staged_changes = gather_staged_changes()
    
    return f"""## Git Context\n\nCurrent Branch: {git_branch}\n\nRecent Commits with Diffs:\n{git_log_full}\n\nCurrent Status:\n{git_status}\n\n## Current Changes\n{changes}\n\n{staged_changes}\n"""


def extract_decision_entries(content: str) -> list[dict]:
    """Extract decision entries from the decision log."""
    entries = []
    
    # Regular expression pattern to match table rows
    pattern = r'\|\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+\|\s+(.*?)\s+\|\s+(.*?)\s+\|\s+(.*?)\s+\|'
    matches = re.findall(pattern, content, re.DOTALL)
    
    for match in matches:
        timestamp, decision, description, details = match
        entries.append({
            "timestamp": timestamp,
            "decision": decision.strip(),
            "description": description.strip(),
            "details": details.strip()
        })
    
    # Sort by timestamp (newest first)
    entries.sort(key=lambda x: x['timestamp'], reverse=True)
    return entries


def extract_recent_activities(content: str) -> list[dict]:
    """Extract recent activities from the progress file."""
    activities = []
    
    # Find entries with timestamps
    pattern = r'\[\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\]\s+-\s+(.*?)(?:\n|$)'
    matches = re.findall(pattern, content)
    
    for match in matches:
        activities.append(match.strip())
    
    return activities[:10]  # Return the 10 most recent activities


def update_project_history() -> str:
    """Update the project history digest with new information."""
    # Read the existing project history file
    history_path = DEFAULT_HISTORY_DIGEST
    existing_history = ""
    if history_path.exists():
        existing_history = read_file(history_path)
    
    # If the file is empty or doesn't exist, we'll create a new one
    if not existing_history or "*File not found*" in existing_history:
        return build_new_project_history()
    
    # Extract and update the latest information
    
    # Read the decision log
    decision_log_path = HISTORY_PATHS["Decision Log"]
    decision_log = read_file(decision_log_path)
    
    # Read the active context
    active_context_path = HISTORY_PATHS["Active Context"]
    active_context = read_file(active_context_path)
    
    # Extract the latest decisions
    decisions = extract_decision_entries(decision_log)
    
    # Extract recent activities
    progress_path = HISTORY_PATHS["Progress"]
    progress = read_file(progress_path)
    recent_activities = extract_recent_activities(progress)
    
    # Find the Conclusion section in the existing history
    conclusion_match = re.search(r'## Conclusion', existing_history)
    if conclusion_match:
        # Split the content at the Conclusion section
        before_conclusion = existing_history[:conclusion_match.start()]
        conclusion_onwards = existing_history[conclusion_match.start():]
        
        # Add the latest updates to the Conclusion section
        updated_conclusion = "## Recent Updates\n\n"
        
        # Add recent activities
        if recent_activities:
            updated_conclusion += "### Recent Activities\n\n"
            for activity in recent_activities[:5]:  # Top 5 recent activities
                updated_conclusion += f"- {activity}\n"
            updated_conclusion += "\n"
        
        # Add recent decisions
        if decisions:
            updated_conclusion += "### Recent Decisions\n\n"
            for decision in decisions[:5]:  # Top 5 recent decisions
                updated_conclusion += f"- **{decision['timestamp']}**: {decision['decision']} - {decision['description']}\n"
            updated_conclusion += "\n"
        
        # Extract current focus areas from active context
        focus_match = re.search(r'## Current Focus(.*?)(?=##|\Z)', active_context, re.DOTALL)
        if focus_match:
            focus_content = focus_match.group(1).strip()
            updated_conclusion += "### Current Focus Areas\n\n" + focus_content + "\n\n"
        
        # Update the timestamp
        current_time = datetime.datetime.now().isoformat()
        updated_conclusion += f"\n*Last Updated: {current_time}*\n"
        
        # Combine the sections
        updated_history = before_conclusion + updated_conclusion + "\n" + conclusion_onwards
        
        # Update the timestamp at the end
        updated_history = re.sub(r'\*Generated: .*?\*', f'*Generated: {datetime.datetime.now().strftime("%B %d, %Y")}*', updated_history)
        
        return updated_history
    else:
        # If we can't find the Conclusion section, just append the new updates
        new_section = "\n\n## Recent Updates\n\n"
        
        # Add recent activities
        if recent_activities:
            new_section += "### Recent Activities\n\n"
            for activity in recent_activities[:5]:
                new_section += f"- {activity}\n"
            new_section += "\n"
        
        # Add recent decisions
        if decisions:
            new_section += "### Recent Decisions\n\n"
            for decision in decisions[:5]:
                new_section += f"- **{decision['timestamp']}**: {decision['decision']} - {decision['description']}\n"
            new_section += "\n"
        
        # Extract current focus areas from active context
        focus_match = re.search(r'## Current Focus(.*?)(?=##|\Z)', active_context, re.DOTALL)
        if focus_match:
            focus_content = focus_match.group(1).strip()
            new_section += "### Current Focus Areas\n\n" + focus_content + "\n\n"
        
        # Update the timestamp
        updated_history = existing_history + new_section
        updated_history = re.sub(r'\*Generated: .*?\*', f'*Generated: {datetime.datetime.now().strftime("%B %d, %Y")}*', updated_history)
        
        return updated_history


def build_new_project_history() -> str:
    """Build a new project history from scratch."""
    # This is a placeholder - in a real implementation, this would parse 
    # various source files to compile a comprehensive project history
    history = """# PulseTrack Project History

This document provides a chronological history of the PulseTrack project based on documentation from various sources. It captures the evolution of the system from inception through implementation.

## Project Overview

PulseTrack is a multi-tenant, AI-augmented healthcare platform focused on improving communication, decision support, and personalized care within weight management and aesthetics clinics. The system consists of:

- **Patient Application**: Mobile-first web app for tracking health metrics, medication management, and clinician communication
- **Clinician Portal**: Web dashboard for patient management and triage
- **Admin Portal**: Configuration interface for clinic management and AI content training

## Project Timeline

### Phase 1: Initial Setup (March 31, 2025)

The project began with a comprehensive setup phase establishing core infrastructure and documentation:

- Created initial PRD for "Codename PulseTrack" formalizing product requirements
- Established comprehensive project scaffold with 10 atomic PRDs and constraint files
- Developed initial executable prompt templates for project setup, API endpoints, and backend mechanisms
- Implemented initial FastAPI backend setup with health check and environment configuration
- Set up Docker environment with configuration for containerized development
- Created GitHub Actions CI pipeline for code quality enforcement
- Implemented database migrations with Alembic
- Created initial authentication endpoints for magic code validation and Firebase login

<!-- Additional phases would be automatically populated based on the available documentation -->

## Recent Updates

### Recent Activities

*This section will be populated with the most recent project activities*

### Recent Decisions

*This section will be populated with the most recent project decisions*

### Current Focus Areas

*This section will be populated with current focus areas from active context*

## Conclusion

The PulseTrack project continues to evolve with a focus on security, compliance, and user experience.

*Generated: """ + datetime.datetime.now().strftime("%B %d, %Y") + "*"
    
    return history


def build_digest() -> str:
    """Build the complete digest by collecting information from various sources."""
    chunks: list[str] = [f"*Generated: {datetime.datetime.now().isoformat()}Z*"]

    # Add sections from the path mapping
    for title, path in PATH_MAPPING.items():
        limit = 200 if "Context" in title else 50
        content = read_first_lines(path, limit)
        chunks.append(f"## {title}\n\n{content}")

    # Add git context information
    chunks.append(gather_git_context())
    
    # Add PRD information
    chunks.append(gather_prds())

    return HR.join(chunks).rstrip() + "\n"


def write_if_changed(text: str, out_path: Path) -> None:
    """Write the digest if it has changed since the last generation."""
    out_path.parent.mkdir(parents=True, exist_ok=True)
    if out_path.exists():
        try:
            old = out_path.read_text(encoding="utf-8")
            if (
                hashlib.sha256(old.encode()).digest()
                == hashlib.sha256(text.encode()).digest()
            ):
                print(f"Digest unchanged; skipping write: {out_path}")
                return
        except Exception as e:
            print(f"Error reading existing digest: {str(e)}")
    
    try:
        out_path.write_text(text, encoding="utf-8")
        print(f"Digest written to {out_path}")
    except Exception as e:
        print(f"Error writing digest to {out_path}: {str(e)}")


def main(out_dir: str | None = None) -> None:
    """Main function to generate and save the digests."""
    try:
        # Determine output directory
        out_path = Path(out_dir) if out_dir else DEFAULT_OUT_DIR
        out_path.mkdir(parents=True, exist_ok=True)
        
        # Build and write the AI digest
        ai_digest = build_digest()
        ai_digest_path = out_path / "ai.md"
        write_if_changed(ai_digest, ai_digest_path)
        
        # Update and write the project history digest
        history_digest = update_project_history()
        history_digest_path = out_path / "project_history.md"
        write_if_changed(history_digest, history_digest_path)
        
    except Exception as e:
        print(f"Error generating digests: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import sys

    main(sys.argv[1] if len(sys.argv) > 1 else None)