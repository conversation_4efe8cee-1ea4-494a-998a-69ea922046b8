#!/usr/bin/env python
"""
render_git_digest.py - Generate a knowledge digest with git status and diff information
for LLM assistants.

This script enhances the standard digest with:
  • Current git branch and status
  • Recent commits (last 5)
  • Diffs of modified files (focusing on code and knowledge files)
  • Diffs from the last n commits (configurable)

Usage:
    python scripts/render_git_digest.py [output_dir] [--commit-diffs N]

If *output_dir* is omitted, the script writes to "knowledge/digests/ai.md".
If --commit-diffs is provided, it will include diffs from the last N commits.
"""
from __future__ import annotations

import argparse
import hashlib
import itertools
import subprocess
import os
import re
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Optional

# Repository root and paths
ROOT = Path(__file__).resolve().parent.parent  # repo root
KNOWLEDGE = ROOT / "knowledge"
DEFAULT_OUT = KNOWLEDGE / "digests" / "ai.md"

# Section titles for the digest
SECTION_TOC = {
    "productContext.md": "Product Context",
    "activeContext.md": "Active Context",
    "progress.md": "Progress (Doing / Next Up)",
}

HR = "\n\n---\n\n"

# Maximum number of lines to include in diff snippets
MAX_DIFF_LINES = 100

# Files/directories to exclude from diff consideration
DIFF_EXCLUDE_PATTERNS = [
    r"\.venv/",
    r"__pycache__/",
    r"\.pytest_cache/",
    r"\.git/",
    r"poetry\.lock$",
    r"\.pyc$",
]


def read_first_lines(path: Path, limit: int) -> str:
    """Read the first 'limit' lines from a file."""
    try:
        with path.open(encoding="utf-8") as f:
            return "".join(itertools.islice(f, limit)).rstrip()
    except Exception as e:
        print(f"Error reading {path}: {e}")
        return f"Error reading file: {str(e)}"


def run_cmd(cmd: List[str], cwd: Optional[str] = None) -> str:
    """Run a shell command and return the output."""
    try:
        result = subprocess.run(
            cmd, cwd=cwd or str(ROOT), capture_output=True, text=True, check=False,
            encoding="utf-8", errors="replace"
        )
        
        if result.returncode != 0:
            print(f"Command failed: {' '.join(cmd)}")
            print(f"Error: {result.stderr}")
            return f"Error running command: {' '.join(cmd)}\nExit code: {result.returncode}\n{result.stderr}"
            
        return result.stdout.strip()
    except Exception as e:
        print(f"Exception running command: {' '.join(cmd)}")
        print(f"Error: {str(e)}")
        return f"Error: {str(e)}"


def get_current_branch() -> str:
    """Get the name of the current git branch."""
    return run_cmd(["git", "rev-parse", "--abbrev-ref", "HEAD"])


def get_git_status() -> Dict[str, List[str]]:
    """Get the current git status information."""
    status_output = run_cmd(["git", "status", "--porcelain"])

    result = {
        "modified": [],
        "added": [],
        "deleted": [],
        "untracked": [],
        "renamed": [],
    }

    if not status_output:
        return result

    for line in status_output.splitlines():
        code = line[:2]
        file_path = line[3:].strip()

        # Skip excluded files
        if any(re.search(pattern, file_path) for pattern in DIFF_EXCLUDE_PATTERNS):
            continue

        if code.startswith("M"):
            result["modified"].append(file_path)
        elif code.startswith("A"):
            result["added"].append(file_path)
        elif code.startswith("D"):
            result["deleted"].append(file_path)
        elif code.startswith("R"):
            result["renamed"].append(file_path)
        elif code.startswith("??"):
            result["untracked"].append(file_path)

    return result


def get_recent_commits(count: int = 5) -> List[Dict[str, str]]:
    """Get information about recent commits."""
    format_str = "--pretty=format:%h|%an|%ad|%s"
    commits_output = run_cmd(
        ["git", "log", "-n", str(count), format_str, "--date=short"]
    )

    commits = []
    for line in commits_output.splitlines():
        parts = line.split("|", 3)
        if len(parts) == 4:
            hash_id, author, date, message = parts
            commits.append(
                {"hash": hash_id, "author": author, "date": date, "message": message}
            )

    return commits


def get_file_diff(file_path: str, max_lines: int = MAX_DIFF_LINES) -> str:
    """Get the diff for a specific file."""
    # For deleted files
    if not Path(file_path).exists():
        return f"File deleted: {file_path}"

    # Check if file is newly added
    if (
        file_path in get_git_status()["added"]
        or file_path in get_git_status()["untracked"]
    ):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()[:max_lines]

            if len(lines) >= max_lines:
                lines.append(
                    f"\n... (truncated, showing {max_lines} of {len(lines)} lines)\n"
                )

            return f"New file: {file_path}\n\n```\n{''.join(lines)}\n```"
        except Exception as e:
            return f"Error reading file {file_path}: {str(e)}"

    # For modified files, show the diff
    diff_output = run_cmd(["git", "diff", "HEAD", "--", file_path])

    if not diff_output:
        return f"No changes detected in {file_path}"

    # Truncate large diffs
    lines = diff_output.splitlines()
    if len(lines) > max_lines:
        lines = lines[:max_lines]
        lines.append(f"... (truncated, showing {max_lines} lines of diff)")

    return f"Changes in {file_path}:\n\n```diff\n{os.linesep.join(lines)}\n```"


def get_commit_diff(commit_hash: str, max_lines: int = MAX_DIFF_LINES) -> str:
    """Get the diff for a specific commit."""
    try:
        # Get the actual diff with file content changes
        diff_output = run_cmd(["git", "show", commit_hash, "--patch", "--encoding=utf-8"])
        
        if not diff_output or diff_output.startswith("Error"):
            return f"No changes found in commit {commit_hash} or error occurred.\n{diff_output}"
        
        # Limit the output size
        lines = diff_output.splitlines()
        if len(lines) > max_lines:
            first_part = lines[:max_lines // 2]
            last_part = lines[-max_lines // 2:]
            lines = first_part + [f"... [truncated {len(lines) - max_lines} lines] ..."] + last_part
            diff_output = "\n".join(lines)
        
        # Format the output
        return f"```diff\n{diff_output}\n```\n"
    except Exception as e:
        return f"Error generating diff for commit {commit_hash}: {str(e)}"


def gather_rules() -> str:
    """Gather K-series rules."""
    lines: list[str] = []
    for rule_file in sorted((KNOWLEDGE / "rules").glob("K-*.md")):
        with rule_file.open(encoding="utf-8") as f:
            title = rule_file.stem
            must_line = next(
                (line for line in f if line.strip().startswith("must:")), None
            )
            if must_line:
                rule_text = must_line.split(":", 1)[1].strip().strip('"')
                lines.append(f"* **{title}** – {rule_text}")
    return "## Rules\n\n" + "\n".join(lines) + "\n"


def gather_adrs(limit: int = 3) -> str:
    """Gather ADR summaries."""
    adr_dir = KNOWLEDGE / "decisions"
    adrs = sorted(adr_dir.glob("ADR-*"), reverse=True)[:limit]
    parts: list[str] = ["## Recent ADRs\n"]
    for adr in adrs:
        header = adr.name.split(" ", 1)[0]
        with adr.open(encoding="utf-8") as f:
            first_line = next(f).lstrip("# ").strip()
        parts.append(f"* **{header}** – {first_line}")
    return "\n".join(parts) + "\n"


def gather_prds() -> str:
    """Gather PRD information."""
    prd_dir = KNOWLEDGE / "prds"
    parts = ["## PRDs\n"]
    for md in sorted(prd_dir.glob("PRD-*.md")):
        with md.open(encoding="utf-8") as f:
            meta = {}
            for _ in range(10):
                line = f.readline().strip()
                if line == "---":
                    continue
                if not line or ":" not in line:
                    break
                key, val = line.split(":", 1)
                meta[key.strip()] = val.strip()
        parts.append(
            f"* **{meta.get('title', '?')}** ({md.stem}) – owner: {meta.get('owner', '?')} "
            f"– {meta.get('status')}"
        )
    return "\n".join(parts) + "\n"


def build_git_section(commit_diffs: int = 0) -> str:
    """Build the git context section of the digest."""
    branch = get_current_branch()
    status = get_git_status()
    commits = get_recent_commits()

    parts = [f"## Git Context\n\n**Current Branch:** {branch}"]

    # Add status summary
    status_summary = []
    for status_type, files in status.items():
        if files:
            status_summary.append(f"{status_type}: {len(files)}")

    if status_summary:
        parts.append(f"**Status:** {', '.join(status_summary)}")
    else:
        parts.append("**Status:** Clean working directory")

    # Add recent commits
    if commits:
        parts.append("\n### Recent Commits\n")
        for commit in commits:
            parts.append(
                f"* **{commit['hash']}** ({commit['date']}) - {commit['message']} _by {commit['author']}_"
            )

    # Add diffs for modified files (focusing on important ones first)
    if any(status.values()):
        parts.append("\n### Changes\n")

        # Prioritize knowledge files and core code
        priority_patterns = [
            r"^knowledge/",
            r"^src/.*\.py$",
            r"^scripts/.*\.py$",
            r"^prompts/",
            r"^alembic/versions/",
        ]

        def is_priority_file(path):
            return any(re.match(pattern, path) for pattern in priority_patterns)

        # Process modified files first, prioritizing knowledge files
        all_modified = status["modified"] + status["added"] + status["renamed"]

        # Sort by priority
        all_modified.sort(key=lambda f: (0 if is_priority_file(f) else 1, f))

        # Limit to a reasonable number of files
        if len(all_modified) > 5:
            parts.append(
                f"> Showing diffs for {5} of {len(all_modified)} changed files (prioritizing knowledge and core code)"
            )
            all_modified = all_modified[:5]

        for file_path in all_modified:
            parts.append(get_file_diff(file_path))
    
    # Add diffs from recent commits if requested
    if commit_diffs > 0 and commits:
        parts.append(f"\n### Diffs from Last {min(commit_diffs, len(commits))} Commits\n")
        
        # Limit to the requested number of commits or available commits
        for i, commit in enumerate(commits[:commit_diffs]):
            parts.append(f"#### Commit {commit['hash']} - {commit['message']}\n")
            parts.append(get_commit_diff(commit['hash']))
            
            # Add a separator between commits
            if i < min(commit_diffs, len(commits)) - 1:
                parts.append("---\n")

    return "\n\n".join(parts)


def build_digest(commit_diffs: int = 0) -> str:
    """Build the enhanced digest with git information."""
    # First, build the standard content
    chunks: list[str] = [f"*Generated: {datetime.now(timezone.utc).isoformat()}*"]

    for filename, title in SECTION_TOC.items():
        path = KNOWLEDGE / filename
        if path.exists():
            limit = 200 if "product" in filename else 50
            content = read_first_lines(path, limit)
            chunks.append(f"## {title}\n\n{content}")

    # Add git context before the rules section
    git_section = build_git_section(commit_diffs)
    chunks.append(git_section)

    # Add the standard sections
    chunks.append(gather_rules())
    chunks.append(gather_adrs())
    chunks.append(gather_prds())

    return HR.join(chunks).rstrip() + "\n"


def write_if_changed(text: str, out_path: Path) -> None:
    """Write text to file if it has changed."""
    out_path.parent.mkdir(parents=True, exist_ok=True)
    if out_path.exists():
        old = out_path.read_text(encoding="utf-8")
        if (
            hashlib.sha256(old.encode()).digest()
            == hashlib.sha256(text.encode()).digest()
        ):
            print("Digest unchanged; skipping write.")
            return
    out_path.write_text(text, encoding="utf-8")
    print(f"Digest written to {out_path}")


def main(out: str | None = None, commit_diffs: int = 0) -> None:
    """Main function."""
    digest = build_digest(commit_diffs)
    out_path = Path(out) if out else DEFAULT_OUT
    # If output is a directory, append the default filename
    if out_path.is_dir():
        out_path = out_path / "ai.md"
    write_if_changed(digest, out_path)


if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Generate an AI digest with git information")
    parser.add_argument("output", nargs="?", default=None, help="Output file or directory")
    parser.add_argument("--commit-diffs", "-c", type=int, default=0, 
                        help="Include diffs from the last N commits")
    
    args = parser.parse_args()
    main(args.output, args.commit_diffs)
