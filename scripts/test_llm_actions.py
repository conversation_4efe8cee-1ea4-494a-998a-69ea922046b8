#!/usr/bin/env python
"""
User-oriented testing script for LLM-driven API actions.

This script performs end-to-end testing of the LLM-driven API actions functionality
by simulating real user interactions through the API endpoints.

Usage:
    python test_llm_actions.py [--url BASE_URL] [--token TOKEN]
"""

import argparse
import json
import sys
import time
import uuid
from datetime import datetime, timedelta

import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress
from rich.table import Table

# Initialize rich console
console = Console()

# Default configuration
DEFAULT_BASE_URL = "http://localhost:8000/api/v1"


def get_token_from_auth(base_url, email, password):
    """Authenticate and get a token."""
    console.print("\n[bold yellow]Authenticating...[/bold yellow]")
    try:
        response = requests.post(
            f"{base_url}/auth/login",
            json={"email": email, "password": password}
        )
        response.raise_for_status()
        token = response.json().get("accessToken")
        console.print("[green]Authentication successful![/green]")
        return token
    except Exception as e:
        console.print(f"[bold red]Authentication failed: {str(e)}[/bold red]")
        return None


def get_template_by_name(base_url, token, template_name=None):
    """Get a template by name or return the first template if name is None."""
    console.print("\n[bold]Fetching templates...[/bold]")
    try:
        response = requests.get(
            f"{base_url}/templates",
            headers={"Authorization": f"Bearer {token}"}
        )
        response.raise_for_status()
        templates = response.json()
        
        if not templates:
            console.print("[yellow]No templates found. You need to create a template first.[/yellow]")
            return None
        
        # Display available templates
        table = Table(title="Available Templates")
        table.add_column("ID", style="dim")
        table.add_column("Name", style="green")
        table.add_column("Description")
        table.add_column("Version")
        table.add_column("Is Active", justify="center")
        
        for template in templates:
            table.add_row(
                str(template.get("id")),
                template.get("name"),
                template.get("description") or "N/A",
                template.get("version"),
                "✓" if template.get("is_active") else "✗"
            )
        
        console.print(table)
        
        # Select template
        selected_template = None
        if template_name:
            for template in templates:
                if template.get("name") == template_name:
                    selected_template = template
                    break
            if not selected_template:
                console.print(f"[yellow]Template with name '{template_name}' not found. Using first template.[/yellow]")
                selected_template = templates[0]
        else:
            selected_template = templates[0]
            
        console.print(f"[green]Selected template: {selected_template['name']} (ID: {selected_template['id']})[/green]")
        return selected_template
    
    except Exception as e:
        console.print(f"[bold red]Error fetching templates: {str(e)}[/bold red]")
        return None


def create_test_template(base_url, token):
    """Create a template specifically for testing."""
    console.print("\n[bold]Creating test template...[/bold]")
    
    # Define template data
    template_data = {
        "name": "LLM Testing Template",
        "description": "Template for testing LLM-driven API actions",
        "version": "1.0",
        "system_prompt": """You are an AI assistant for the PulseTrack healthcare platform.
            Your job is to understand user requests and execute them as actions in the system.
            Always be helpful and only perform actions that the user has explicitly requested.
            If you're unsure about what the user wants, ask for clarification.""",
        "actions": [
            {
                "name": "Create Appointment",
                "description": "Schedule a new appointment for a patient with a clinician",
                "action_type": "appointment_create",
                "parameters": [
                    {
                        "name": "patient_id",
                        "description": "ID of the patient for the appointment",
                        "required": True,
                        "type": "string"
                    },
                    {
                        "name": "clinician_id",
                        "description": "ID of the clinician for the appointment",
                        "required": True,
                        "type": "string"
                    },
                    {
                        "name": "appointment_date",
                        "description": "Date for the appointment (YYYY-MM-DD)",
                        "required": True,
                        "type": "date"
                    },
                    {
                        "name": "appointment_time",
                        "description": "Time for the appointment (HH:MM)",
                        "required": False,
                        "type": "string"
                    },
                    {
                        "name": "appointment_type",
                        "description": "Type of appointment (e.g., 'checkup', 'follow-up')",
                        "required": False,
                        "type": "string"
                    },
                    {
                        "name": "notes",
                        "description": "Additional notes for the appointment",
                        "required": False,
                        "type": "string"
                    }
                ]
            },
            {
                "name": "Cancel Appointment",
                "description": "Cancel an existing appointment",
                "action_type": "appointment_cancel",
                "parameters": [
                    {
                        "name": "appointment_id",
                        "description": "ID of the appointment to cancel",
                        "required": True,
                        "type": "string"
                    },
                    {
                        "name": "reason",
                        "description": "Reason for cancellation",
                        "required": False,
                        "type": "string"
                    }
                ]
            },
            {
                "name": "Create Note",
                "description": "Create a clinical note",
                "action_type": "note_create",
                "parameters": [
                    {
                        "name": "patient_id",
                        "description": "ID of the patient the note is about",
                        "required": True,
                        "type": "string"
                    },
                    {
                        "name": "content",
                        "description": "Content of the note",
                        "required": True,
                        "type": "string"
                    },
                    {
                        "name": "note_type",
                        "description": "Type of note (e.g., 'progress', 'assessment')",
                        "required": False,
                        "type": "string"
                    }
                ]
            }
        ]
    }
    
    # Define roles that can access this template
    roles = ["admin", "clinician", "patient"]
    
    try:
        response = requests.post(
            f"{base_url}/templates",
            json={"template_in": template_data, "roles": roles},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        if response.status_code == 409:
            console.print("[yellow]Test template already exists[/yellow]")
            # Get the existing template
            return get_template_by_name(base_url, token, "LLM Testing Template")
            
        response.raise_for_status()
        template = response.json()
        console.print(f"[green]Created test template: {template['name']} (ID: {template['id']})[/green]")
        return template
    
    except Exception as e:
        console.print(f"[bold red]Error creating test template: {str(e)}[/bold red]")
        console.print("[yellow]Attempting to find existing test template instead...[/yellow]")
        return get_template_by_name(base_url, token, "LLM Testing Template")


def get_user_ids(base_url, token, user_role):
    """Get user IDs based on role for testing."""
    console.print(f"\n[bold]Fetching {user_role} IDs for testing...[/bold]")
    
    endpoint = "/clinicians" if user_role == "clinician" else "/patients"
    
    try:
        response = requests.get(
            f"{base_url}{endpoint}",
            headers={"Authorization": f"Bearer {token}"}
        )
        response.raise_for_status()
        users = response.json()
        
        if not users:
            console.print(f"[yellow]No {user_role}s found for testing.[/yellow]")
            return []
        
        # Display available users
        table = Table(title=f"Available {user_role.capitalize()}s")
        table.add_column("ID", style="dim")
        table.add_column("Name", style="green")
        
        user_ids = []
        for user in users:
            user_id = user.get(f"{user_role}_id") or user.get("id")
            name = f"{user.get('first_name')} {user.get('last_name')}"
            user_ids.append(user_id)
            table.add_row(user_id, name)
        
        console.print(table)
        return user_ids
    
    except Exception as e:
        console.print(f"[bold red]Error fetching {user_role} IDs: {str(e)}[/bold red]")
        return []


def test_text_action(base_url, token, template_id, sample_text):
    """Test the text-based LLM action endpoint."""
    console.print(Panel(f"Testing Text-Based LLM Action: '{sample_text}'", style="blue"))
    
    payload = {
        "text": sample_text,
        "template_id": template_id,
        "context": {}
    }
    
    try:
        console.print("[bold]Sending request...[/bold]")
        response = requests.post(
            f"{base_url}/llm-actions/text",
            json=payload,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        console.print(f"Status code: {response.status_code}")
        result = response.json()
        
        # Pretty print the response
        console.print("\n[bold]Response:[/bold]")
        if result.get("success"):
            console.print(f"[green]Success: {result.get('message')}[/green]")
        else:
            console.print(f"[yellow]Failure: {result.get('message')}[/yellow]")
            
        console.print("\n[bold]Action details:[/bold]")
        console.print(f"Action type: {result.get('action_type') or 'N/A'}")
        
        if result.get('data'):
            console.print("\n[bold]Data:[/bold]")
            console.print(json.dumps(result.get('data'), indent=2))
        
        return result
        
    except Exception as e:
        console.print(f"[bold red]Error testing text action: {str(e)}[/bold red]")
        return None


def test_direct_action(base_url, token, template_id, action_type, parameters):
    """Test the direct template action endpoint."""
    console.print(Panel(f"Testing Direct Template Action: '{action_type}'", style="blue"))
    
    payload = {
        "action_type": action_type,
        "parameters": parameters
    }
    
    try:
        console.print("[bold]Sending request...[/bold]")
        response = requests.post(
            f"{base_url}/templates/{template_id}/actions",
            json=payload,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        console.print(f"Status code: {response.status_code}")
        result = response.json()
        
        # Pretty print the response
        console.print("\n[bold]Response:[/bold]")
        if result.get("success"):
            console.print(f"[green]Success: {result.get('message')}[/green]")
        else:
            console.print(f"[yellow]Failure: {result.get('message')}[/yellow]")
            
        console.print("\n[bold]Action details:[/bold]")
        console.print(f"Action type: {result.get('action_type') or 'N/A'}")
        
        if result.get('data'):
            console.print("\n[bold]Data:[/bold]")
            console.print(json.dumps(result.get('data'), indent=2))
        
        return result
        
    except Exception as e:
        console.print(f"[bold red]Error testing direct action: {str(e)}[/bold red]")
        return None


def run_comprehensive_tests(base_url, token):
    """Run a comprehensive test suite covering all components."""
    console.print(Panel("[bold]Starting Comprehensive Test Suite[/bold]", style="green"))
    
    # Step 1: Create/retrieve test template
    template = create_test_template(base_url, token)
    if not template:
        console.print("[bold red]Cannot proceed without a template[/bold red]")
        return False
    
    # Step 2: Get user IDs for testing
    patient_ids = get_user_ids(base_url, token, "patient")
    clinician_ids = get_user_ids(base_url, token, "clinician")
    
    if not patient_ids or not clinician_ids:
        console.print("[bold red]Cannot proceed without test users[/bold red]")
        return False
    
    test_patient_id = patient_ids[0]
    test_clinician_id = clinician_ids[0]
    
    # Step 3: Test text-based actions
    test_cases = [
        {
            "name": "Create Appointment (Natural Language)",
            "text": f"Schedule an appointment for patient {test_patient_id} with clinician {test_clinician_id} tomorrow at 2:00 PM for a checkup",
            "expected_action": "appointment_create"
        },
        {
            "name": "Create Note (Natural Language)",
            "text": f"Create a note for patient {test_patient_id} with content 'Patient is doing well with treatment'",
            "expected_action": "note_create"
        },
        {
            "name": "Invalid Action",
            "text": "Do something that's not supported",
            "expected_action": None,
            "expected_failure": True
        }
    ]
    
    results = []
    for i, test_case in enumerate(test_cases, 1):
        console.print(f"\n[bold cyan]Test Case {i}: {test_case['name']}[/bold cyan]")
        result = test_text_action(base_url, token, template["id"], test_case["text"])
        
        if result:
            success = (
                (test_case.get("expected_failure") and not result.get("success")) or
                (not test_case.get("expected_failure") and 
                 result.get("success") and 
                 (test_case["expected_action"] is None or result.get("action_type") == test_case["expected_action"]))
            )
            
            results.append({
                "name": test_case["name"],
                "success": success,
                "details": f"Expected: {test_case['expected_action']}, Got: {result.get('action_type')}"
            })
        else:
            results.append({
                "name": test_case["name"],
                "success": False,
                "details": "Request failed"
            })
    
    # Step 4: Test direct actions
    direct_test_cases = [
        {
            "name": "Direct Appointment Creation",
            "action_type": "appointment_create",
            "parameters": {
                "patient_id": test_patient_id,
                "clinician_id": test_clinician_id,
                "appointment_date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
                "appointment_time": "14:00",
                "appointment_type": "checkup",
                "notes": "Direct API test"
            }
        },
        {
            "name": "Direct Note Creation",
            "action_type": "note_create",
            "parameters": {
                "patient_id": test_patient_id,
                "content": "Note created through direct API test",
                "note_type": "test"
            }
        }
    ]
    
    for i, test_case in enumerate(direct_test_cases, len(results) + 1):
        console.print(f"\n[bold cyan]Test Case {i}: {test_case['name']}[/bold cyan]")
        result = test_direct_action(
            base_url, token, template["id"], 
            test_case["action_type"], test_case["parameters"]
        )
        
        if result:
            success = result.get("success") and result.get("action_type") == test_case["action_type"]
            results.append({
                "name": test_case["name"],
                "success": success,
                "details": f"Expected: {test_case['action_type']}, Got: {result.get('action_type')}"
            })
        else:
            results.append({
                "name": test_case["name"],
                "success": False,
                "details": "Request failed"
            })
    
    # Step 5: Show test results summary
    console.print("\n" + "=" * 80)
    console.print("[bold]Test Results Summary:[/bold]")
    
    table = Table(title="Test Results")
    table.add_column("#", style="dim")
    table.add_column("Test Case")
    table.add_column("Result")
    table.add_column("Details")
    
    total_success = 0
    for i, result in enumerate(results, 1):
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        style = "green" if result["success"] else "red"
        table.add_row(str(i), result["name"], f"[{style}]{status}[/{style}]", result["details"])
        if result["success"]:
            total_success += 1
    
    success_rate = (total_success / len(results)) * 100 if results else 0
    console.print(table)
    console.print(f"\n[bold]Success Rate: [{'green' if success_rate >= 70 else 'yellow' if success_rate >= 50 else 'red'}]{success_rate:.1f}%[/]")
    console.print(f"Passed: {total_success}/{len(results)}")
    
    return success_rate >= 70


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test LLM-driven API actions')
    parser.add_argument('--url', default=DEFAULT_BASE_URL, help='Base URL for the API')
    parser.add_argument('--token', help='Authentication token')
    parser.add_argument('--email', help='Email for authentication')
    parser.add_argument('--password', help='Password for authentication')
    args = parser.parse_args()
    
    console.print(Panel("LLM-Driven API Actions Testing", style="bold green"))
    console.print(f"[dim]Using API base URL: {args.url}[/dim]")
    
    # Get authentication token if not provided
    token = args.token
    if not token and args.email and args.password:
        token = get_token_from_auth(args.url, args.email, args.password)
    
    if not token:
        console.print("[bold red]No authentication token provided or authentication failed.[/bold red]")
        console.print("[yellow]Please provide a token with --token or login credentials with --email and --password[/yellow]")
        return 1
    
    # Run comprehensive tests
    success = run_comprehensive_tests(args.url, token)
    
    if success:
        console.print("\n[bold green]Overall test suite: PASSED[/bold green]")
        return 0
    else:
        console.print("\n[bold red]Overall test suite: FAILED[/bold red]")
        return 1


if __name__ == "__main__":
    sys.exit(main())
