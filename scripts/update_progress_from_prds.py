#!/usr/bin/env python3
"""
Update the project progress.md file based on PRD files.
This script scans the PRD files in the project and updates the progress.md file.
"""

import os
import re
import sys
from pathlib import Path

def extract_progress_from_prd(prd_path):
    """Extract progress information from a PRD file."""
    progress_info = {
        "name": os.path.basename(prd_path).replace(".md", ""),
        "total_items": 0,
        "completed_items": 0,
        "percentage": 0,
    }

    try:
        with open(prd_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Look for task lists [ ], [x], etc.
        total_items = len(re.findall(r"- \[([ xX])\]", content))
        completed_items = len(re.findall(r"- \[[xX]\]", content))

        progress_info["total_items"] = total_items
        progress_info["completed_items"] = completed_items
        
        if total_items > 0:
            progress_info["percentage"] = round((completed_items / total_items) * 100)

        # Extract title from the PRD
        title_match = re.search(r"^# (.+)$", content, re.MULTILINE)
        if title_match:
            progress_info["name"] = title_match.group(1)

    except Exception as e:
        print(f"Error processing PRD file {prd_path}: {str(e)}")

    return progress_info

def update_progress_md(all_progress):
    """Update the progress.md file with information from PRDs."""
    project_root = Path(__file__).resolve().parent.parent
    progress_md_path = project_root / "knowledge" / "progress.md"

    try:
        # Read existing content
        existing_content = ""
        if progress_md_path.exists():
            with open(progress_md_path, "r", encoding="utf-8") as f:
                existing_content = f.read()

        # Find where to insert the PRD progress section
        prd_section_marker = "## PRD Implementation Progress"
        parts = existing_content.split(prd_section_marker)
        
        if len(parts) < 2:
            # If section doesn't exist, append it
            updated_content = existing_content + f"\n\n{prd_section_marker}\n\n"
        else:
            # Keep everything before the PRD section
            updated_content = parts[0] + prd_section_marker + "\n\n"

        # Add the PRD progress information
        if all_progress:
            updated_content += "| PRD | Progress | Status |\n"
            updated_content += "|-----|----------|--------|\n"
            
            for progress in all_progress:
                progress_bar = generate_progress_bar(progress["percentage"])
                updated_content += f"| {progress['name']} | {progress_bar} {progress['percentage']}% | {progress['completed_items']}/{progress['total_items']} |\n"
        else:
            updated_content += "No PRD files found in the project.\n"

        # Write the updated content
        with open(progress_md_path, "w", encoding="utf-8") as f:
            f.write(updated_content)

        print(f"Updated progress.md with PRD information: {progress_md_path}")

    except Exception as e:
        print(f"Error updating progress.md: {str(e)}")

def generate_progress_bar(percentage, width=10):
    """Generate a progress bar string based on percentage."""
    filled = int(width * percentage / 100)
    empty = width - filled
    return "[" + "█" * filled + "░" * empty + "]"

def main():
    """Update the project progress based on PRDs."""
    project_root = Path(__file__).resolve().parent.parent
    
    # Look for PRDs in docs/.xgen/prds and possibly other locations
    prd_paths = []
    prd_dirs = [
        project_root / "docs" / ".xgen" / "prds",
        project_root / "docs" / "prds",
    ]
    
    for prd_dir in prd_dirs:
        if prd_dir.exists() and prd_dir.is_dir():
            # Find all .md files recursively in the PRD directory
            prd_paths.extend([
                str(path) for path in prd_dir.glob("**/*.md")
            ])
    
    print(f"Found {len(prd_paths)} PRD files.")
    
    # Extract progress information from each PRD
    all_progress = []
    for prd_path in prd_paths:
        progress_info = extract_progress_from_prd(prd_path)
        if progress_info["total_items"] > 0:
            all_progress.append(progress_info)
    
    # Sort by completion percentage (descending)
    all_progress.sort(key=lambda x: x["percentage"], reverse=True)
    
    # Update the progress.md file
    update_progress_md(all_progress)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
