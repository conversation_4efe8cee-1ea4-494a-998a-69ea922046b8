{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "node", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": [], "references": [{"path": "./packages/shared-frontend"}]}